<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryParams" :inline="true" :model="queryParams" class="seache-form" label-width="" @submit.prevent>
                <el-form-item label="货主名称" prop="companyId" style="width: 250px">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in ownerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="status" style="width: 250px">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择状态" @change="handleQuery">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="订单确认功能" prop="authStatus" style="width: 270px">
                    <el-select v-model="queryParams.authStatus" clearable placeholder="请选择订单确认功能" @change="handleQuery">
                        <el-option label="已开启" value="1"></el-option>
                        <el-option label="已关闭" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10">
                <el-button type="primary" @click="() => handleOpenApplyForCooperation()">申请合作</el-button>
                <el-button type="danger" @click="clearCollection">清除收藏</el-button>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="CooperativeCargoOwners" @queryTable="getList" />
            </div>
            <column-table key="CooperativeCargoOwners" v-loading="loading" :columns="columns" :data="dataList" :max-height="600" element-loading-text="加载中...">
                <template #isKeyAccount="{ row }">
                    <span>{{ row.isKeyAccount === '1' ? '是' : '否' }}</span>
                </template>
                <template #status="{ row }">
                    <span>{{ formatStatus(row.status) }}</span>
                </template>
                <template #signCompany="{ row }">
                    <span>{{ formatDictionaryData('companyList', row.signCompany) }}</span>
                </template>
                <template #authStatus="{ row }">
                    <el-switch v-model="row.authStatus" active-color="#13ce66" active-value="1" inactive-value="0" size="small" @change="handleStatus(row)" />
                </template>
                <template #isCollect="scope">
                    <el-switch v-model="scope.row.isCollect" active-color="#1ACD7E" active-text="是" active-value="1" class="switch__label" inactive-text="否" inactive-value="0" size="small" @change="changeCollect($event, scope.row)"></el-switch>
                </template>
                <template #opt="{ row }">
                    <div class="flex align-center justify-center">
                        <el-button v-if="row.status === '1'" icon="el-icon-info-filled" link size="small" type="primary" @click="handleCooperativeReview(row)">详情</el-button>
                        <el-button icon="el-icon-edit" link size="small" type="warning" @click="handleOpenApplyForCooperation(row)">编辑</el-button>
                        <el-dropdown size="small">
                            <el-button icon="el-icon-arrow-down" link size="small">更多</el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item icon="el-icon-edit" style="color: #2a76f8" @click="handleOpenModificationRecord(row)">修改记录</el-dropdown-item>
                                    <el-dropdown-item v-if="row.status === '1'" icon="el-icon-delete" style="color: #f56c6c" @click="handleChangeCooperation(row, '3')">取消合作</el-dropdown-item>
                                    <el-dropdown-item v-if="row.status === '2' || row.status === '3'" icon="el-icon-refresh" style="color: #67c23a" @click="handleChangeCooperation(row, '1')">重新合作</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </template>
            </column-table>
            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
            </div>
        </el-card>

        <!--  / 申请合作  -->
        <el-drawer v-if="applyForCooperationVisible" v-model="applyForCooperationVisible" size="890px" title="申请合作" @close="hideApplyForCooperation">
            <div v-loading="applyForCooperationLoading" :element-loading-text="applyForCooperationLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card v-if="!editRow" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="applyForCooperationForm" :model="applyForCooperationForm" class="seache-form">
                        <el-form-item class="mb0" label="公司名称" prop="id">
                            <el-select v-model="applyForCooperationForm.id" clearable filterable placeholder="请选择公司名称" style="min-width: 300px" @change="handleSelectPartner">
                                <el-option v-for="item in companyDropDownListData" :key="item.id" :label="item.name" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </el-card>

                <el-card v-if="applyForCooperationForm.id || editRow" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <CardHeader title="公司信息" />
                    <div class="d-box-column">
                        <div v-if="companyInformationData.sysCompanyName" class="d-box-info">
                            <span>公司名称</span>
                            <span>{{ companyInformationData.sysCompanyName }}</span>
                        </div>
                        <div v-if="companyInformationData.sysCompanyAddressVo" class="d-box-info">
                            <span>公司地址</span>
                            <span>{{ companyInformationData.sysCompanyAddressVo }}</span>
                        </div>
                        <div v-if="companyInformationData.sysCompanyPhone" class="d-box-info">
                            <span>联系电话</span>
                            <span>{{ companyInformationData.sysCompanyPhone }}</span>
                        </div>
                        <div v-if="companyInformationData.sysCompanyBriefIntroduction" class="d-box-info">
                            <span>公司介绍</span>
                            <span>{{ companyInformationData.sysCompanyBriefIntroduction }}</span>
                        </div>
                        <div v-if="serviceTypeFormCarrier.length" class="d-box-info">
                            <span>服务类型</span>
                            <span>{{ serviceCategoryData }}</span>
                        </div>
                        <div v-if="serviceCategrayFormCarrier.length" class="d-box-info">
                            <span>服务种类</span>
                            <span>{{ serviceTypeData }}</span>
                        </div>
                    </div>
                    <div class="box__three__column">
                        <div v-if="sysCompanyType6.imageCode" class="box__three__column__item">
                            <span class="div1">
                                <span class="item__name">经营许可证</span>
                                <span>{{ sysCompanyType6.imageCode }}</span>
                            </span>
                            <span class="div2">
                                <span class="item__name">有效期</span>
                                <span>{{ (sysCompanyType6.imageIssuingTime || '').substring(0, 10) }} ~ {{ (sysCompanyType6.imageExpirationTime || '').substring(0, 10) }}</span>
                            </span>
                            <div class="div3">
                                <el-image :preview-src-list="[sysCompanyType6.imageUrl]" :src="sysCompanyType6.imageUrl" style="width: 60px; height: 60px"></el-image>
                                <el-image v-if="sysCompanyType7.imageUrl" :preview-src-list="[sysCompanyType7.imageUrl]" :src="sysCompanyType7.imageUrl" style="width: 60px; height: 60px"></el-image>
                            </div>
                        </div>
                        <div v-else class="d-box-info">
                            <span>经营许可证</span>
                            <span style="color: #ff2a2a">未上传</span>
                        </div>
                        <div v-if="sysCompanyType8.imageCode" class="box__three__column__item">
                            <span class="div1">
                                <span class="item__name">生产许可证</span>
                                <span>{{ sysCompanyType8.imageCode }}</span>
                            </span>
                            <span class="div2">
                                <span class="item__name">有效期</span>
                                <span>{{ (sysCompanyType8.imageIssuingTime || '').substring(0, 10) }} ~ {{ (sysCompanyType8.imageExpirationTime || '').substring(0, 10) }}</span>
                            </span>
                            <div class="div3">
                                <el-image :preview-src-list="[sysCompanyType8.imageUrl]" :src="sysCompanyType8.imageUrl" style="width: 60px; height: 60px"></el-image>
                                <el-image v-if="sysCompanyType9.imageUrl" :preview-src-list="[sysCompanyType9.imageUrl]" :src="sysCompanyType9.imageUrl" style="width: 60px; height: 60px"></el-image>
                            </div>
                        </div>
                        <div v-else class="d-box-info">
                            <span>生产许可证</span>
                            <span style="color: #ff2a2a">未上传</span>
                        </div>
                        <div v-if="sysCompanyType10.imageCode" class="box__three__column__item">
                            <span class="div1">
                                <span class="item__name">营业执照编号</span>
                                <span>{{ sysCompanyType10.imageCode }}</span>
                            </span>
                            <span class="div2">
                                <span class="item__name">有效期</span>
                                <span>{{ (sysCompanyType10.imageIssuingTime || '').substring(0, 10) }} ~ {{ (sysCompanyType10.imageExpirationTime || '').substring(0, 10) }}</span>
                            </span>
                            <div class="div3">
                                <el-image :preview-src-list="[sysCompanyType10.imageUrl]" :src="sysCompanyType10.imageUrl" style="width: 60px; height: 60px"></el-image>
                                <el-image v-if="sysCompanyType11.imageUrl" :preview-src-list="[sysCompanyType11.imageUrl]" :src="sysCompanyType11.imageUrl" style="width: 60px; height: 60px"></el-image>
                            </div>
                        </div>
                        <div v-else class="d-box-info">
                            <span>营业执照编号</span>
                            <span style="color: #ff2a2a">未上传</span>
                        </div>
                    </div>
                </el-card>
                <el-card v-if="applyForCooperationForm.id || editRow" :body-style="{ padding: '10px' }" shadow="never">
                    <CardHeader title="合作信息" />
                    <el-form ref="formParams" :model="formParams" :rules="rules" label-width="auto">
                        <el-form-item label="签约公司名称" prop="signCompany">
                            <el-select v-model="formParams.signCompany" class="w-full" clearable filterable placeholder="请选择签约公司名称" @change="companyNameChange">
                                <el-option v-for="dict in companyList" :key="dict.code" :label="dict.name" :value="dict.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="自有设备" prop="isDevice">
                            <el-radio-group v-model="formParams.isDevice">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <div v-for="(item, index) in formParams.invoiceList" :key="index" class="flex items-center">
                            <el-form-item :label="'发票类型'" :prop="'invoiceList.' + index + '.invoiceType'" :rules="[{ required: true, message: '请选择发票类型', trigger: 'change' }]" label-width="auto">
                                <el-select v-model="item.invoiceType" clearable filterable style="width: 85px">
                                    <el-option v-for="dict in invoiceTypeList" :key="dict.code" :label="dict.name" :value="dict.code"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="'项目名称'" :prop="'invoiceList.' + index + '.projectType'" :rules="[{ required: true, message: '请选择项目名称', trigger: 'change' }]" label-width="85px">
                                <el-select v-model="item.projectType" clearable filterable style="width: 128px" @change="(val) => projectIdChange(val, index)">
                                    <el-option v-for="item in projectList" :key="item.value" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                                <el-select v-if="item.projectType === '运输服务'" v-model="item.projectFeeType" clearable filterable placeholder=" " style="width: 128px">
                                    <el-option v-for="item in transportationCostsList" :key="item.label" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                                <el-select v-if="item.projectType === '物流辅助服务'" v-model="item.projectFeeType" clearable filterable placeholder=" " style="width: 128px">
                                    <el-option v-for="item in serviceChargeList" :key="item.label" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :label="'税点'" :prop="'invoiceList.' + index + '.taxPoint'" :rules="[{ required: true, message: '请选择税点', trigger: 'change' }]" label-width="55px">
                                <el-select v-model="item.taxPoint" class="tax_point_unit" clearable filterable placeholder=" " style="width: 80px">
                                    <el-option v-for="dict in taxPointList" :key="dict.code" :label="dict.name" :value="dict.code"></el-option>
                                </el-select>
                            </el-form-item>
                            <div class="ml-10" style="margin-bottom: 18px">
                                <el-button v-if="index === 0" circle icon="el-icon-plus" type="primary" @click="handleAddProjectItem"></el-button>
                                <el-button v-else circle icon="el-icon-minus" type="danger" @click="handleRemoveProjectItem(index)"></el-button>
                            </div>
                        </div>
                        <el-form-item label="合同号" prop="contractNo">
                            <el-input v-model="formParams.contractNo" clearable maxlength="30" placeholder="请输入合同号" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item class="w-min" label="合同有效期" prop="signContractTime">
                            <el-date-picker v-model="formParams.signContractTime" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD"></el-date-picker>
                        </el-form-item>
                        <div class="grid" style="grid-template-columns: 1fr 1fr; grid-gap: 5px">
                            <el-form-item :label="'对账单核对\n提醒时间'" class="custom__form__item" prop="checkRemind">
                                <el-input-number v-model="formParams.checkRemind" :max="9999" :min="1" :step="1" class="number__unit__element w-full" controls-position="right"></el-input-number>
                            </el-form-item>
                            <el-form-item :label="'付款单支付\n提醒时间'" class="custom__form__item" prop="payRemind">
                                <el-input-number v-model="formParams.payRemind" :max="9999" :min="1" :step="1" class="number__unit__element w-full" controls-position="right"></el-input-number>
                            </el-form-item>
                        </div>
                        <el-divider border-style="dashed" class="my-5" />
                        <el-form-item label="是否签订月结合同" prop="monthlySettlementFlag">
                            <el-radio-group v-model="formParams.monthlySettlementFlag">
                                <el-radio label="1">是</el-radio>
                                <el-radio label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="formParams.monthlySettlementFlag !== '0'" key="monthlySettlementCode" label="合同号" prop="monthlySettlementCode">
                            <el-input v-model="formParams.monthlySettlementCode" clearable maxlength="30" placeholder="请输入合同号" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item v-if="formParams.monthlySettlementFlag !== '0'" key="monthlyTime" label="合同有效期" prop="monthlyTime">
                            <el-date-picker v-model="formParams.monthlyTime" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="是否签订预存款合同" prop="advanceSettlementFlag">
                            <el-radio-group v-model="formParams.advanceSettlementFlag">
                                <el-radio label="1">是</el-radio>
                                <el-radio label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="formParams.advanceSettlementFlag !== '0'" key="advanceSettlementCode" label="合同号" prop="advanceSettlementCode">
                            <el-input v-model="formParams.advanceSettlementCode" clearable maxlength="30" placeholder="请输入合同号" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item v-if="formParams.advanceSettlementFlag !== '0'" key="advanceTime" label="合同有效期" prop="advanceTime">
                            <el-date-picker v-model="formParams.advanceTime" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="是否大客户" prop="isKeyAccount">
                            <el-radio-group v-model="formParams.isKeyAccount">
                                <el-radio label="1">是</el-radio>
                                <el-radio label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
            <template #footer>
                <div v-if="applyForCooperationForm.id || editRow" style="margin-top: 10px">
                    <el-button type="info" @click="hideApplyForCooperation">取消</el-button>
                    <el-button type="primary" @click="handleSubmitAnApplication">提交申请</el-button>
                </div>
            </template>
        </el-drawer>

        <!--  / 合作审核 详情 -->
        <el-drawer v-if="cooperativeReviewVisible" v-model="cooperativeReviewVisible" class="self-drawer" size="700px" title="详情" @close="hideCooperativeReview">
            <div v-loading="cooperativeReviewLoading" :element-loading-text="cooperativeReviewLoadingText">
                <el-descriptions :column="1" border title="公司基本信息">
                    <el-descriptions-item v-if="collaborateOnReviewingData.sysCompanyName" label="公司名称">
                        {{ collaborateOnReviewingData.sysCompanyName }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.sysCompanyAddressVo" label="公司地址">
                        {{ collaborateOnReviewingData.sysCompanyAddressVo }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.sysCompanyPhone" label="联系电话">
                        {{ collaborateOnReviewingData.sysCompanyPhone }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.sysCompanyBriefIntroduction" label="公司介绍">
                        {{ collaborateOnReviewingData.sysCompanyBriefIntroduction }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="serviceTypeFormCarrier.length" label="服务类型">
                        {{ serviceCategoryData }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="serviceCategrayFormCarrier.length" label="服务种类">
                        {{ serviceTypeData }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="sysCompanyType6.imageCode" label="经营许可证">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="mb-2">
                                    <span class="text-gray-500">许可证编号：</span>
                                    <span>{{ sysCompanyType6.imageCode }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">有效期：</span>
                                    <span>{{ (sysCompanyType6.imageIssuingTime || '').substring(0, 10) }} ~ {{ (sysCompanyType6.imageExpirationTime || '').substring(0, 10) }}</span>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <el-image :preview-src-list="[sysCompanyType6.imageUrl]" :src="sysCompanyType6.imageUrl" style="width: 60px; height: 60px"></el-image>
                                <el-image v-if="sysCompanyType7.imageUrl" :preview-src-list="[sysCompanyType7.imageUrl]" :src="sysCompanyType7.imageUrl" style="width: 60px; height: 60px"></el-image>
                            </div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item v-else label="经营许可证">
                        <span style="color: #ff2a2a">未上传</span>
                    </el-descriptions-item>

                    <el-descriptions-item v-if="sysCompanyType8.imageCode" label="生产许可证">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="mb-2">
                                    <span class="text-gray-500">许可证编号：</span>
                                    <span>{{ sysCompanyType8.imageCode }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">有效期：</span>
                                    <span>{{ (sysCompanyType8.imageIssuingTime || '').substring(0, 10) }} ~ {{ (sysCompanyType8.imageExpirationTime || '').substring(0, 10) }}</span>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <el-image :preview-src-list="[sysCompanyType8.imageUrl]" :src="sysCompanyType8.imageUrl" style="width: 60px; height: 60px"></el-image>
                                <el-image v-if="sysCompanyType9.imageUrl" :preview-src-list="[sysCompanyType9.imageUrl]" :src="sysCompanyType9.imageUrl" style="width: 60px; height: 60px"></el-image>
                            </div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item v-else label="生产许可证">
                        <span style="color: #ff2a2a">未上传</span>
                    </el-descriptions-item>

                    <el-descriptions-item v-if="sysCompanyType10.imageCode" label="营业执照编号">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="mb-2">
                                    <span class="text-gray-500">营业执照编号：</span>
                                    <span>{{ sysCompanyType10.imageCode }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">有效期：</span>
                                    <span>{{ (sysCompanyType10.imageIssuingTime || '').substring(0, 10) }} ~ {{ (sysCompanyType10.imageExpirationTime || '').substring(0, 10) }}</span>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <el-image :preview-src-list="[sysCompanyType10.imageUrl]" :src="sysCompanyType10.imageUrl" style="width: 60px; height: 60px"></el-image>
                                <el-image v-if="sysCompanyType11.imageUrl" :preview-src-list="[sysCompanyType11.imageUrl]" :src="sysCompanyType11.imageUrl" style="width: 60px; height: 60px"></el-image>
                            </div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item v-else label="营业执照编号">
                        <span style="color: #ff2a2a">未上传</span>
                    </el-descriptions-item>
                </el-descriptions>

                <el-descriptions :column="2" border style="margin-top: 15px" title="合作信息">
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.signCompany" label="签约公司名称" span="2">
                        {{ formatDictionaryData('companyList', collaborateOnReviewingData.partnerInfo.signCompany) }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.isDevice" label="自有设备" span="2">
                        {{ isNumberOrNumber(collaborateOnReviewingData.partnerInfo.isDevice) }}
                    </el-descriptions-item>
                </el-descriptions>

                <el-descriptions :column="3" border style="margin-top: 10px">
                    <template v-for="(item, index) in collaborateOnReviewingData.partnerInfo.invoiceList" :key="index">
                        <el-descriptions-item label="发票类型">
                            {{ formatDictionaryData('invoiceTypeList', item.invoiceType) }}
                        </el-descriptions-item>
                        <el-descriptions-item label="项目名称">
                            {{ item.projectType + (item.projectFeeType ? ' - ' + item.projectFeeType : '') }}
                        </el-descriptions-item>
                        <el-descriptions-item label="税点"> {{ item.taxPoint }}% </el-descriptions-item>
                    </template>
                </el-descriptions>

                <el-descriptions :column="2" border style="margin-top: 10px">
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.contractNo" label="合同号" span="2">
                        {{ collaborateOnReviewingData.partnerInfo.contractNo }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.signContractStartTime && collaborateOnReviewingData.partnerInfo.signContractEndTime" label="合同有效期" span="2">
                        {{ formatDate(collaborateOnReviewingData.partnerInfo.signContractStartTime) }} ~ {{ formatDate(collaborateOnReviewingData.partnerInfo.signContractEndTime) }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.checkRemind" label="对账单核对提醒时间"> {{ collaborateOnReviewingData.partnerInfo.checkRemind }}天 </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.payRemind" label="付款单支付提醒时间"> {{ collaborateOnReviewingData.partnerInfo.payRemind }}天 </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo" label="是否签订月结合同">
                        {{ isNumberOrNumber(collaborateOnReviewingData.partnerInfo.monthlySettlementFlag) }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.monthlySettlementFlag == '1'" label="合同号" span="2">
                        {{ collaborateOnReviewingData.partnerInfo.monthlySettlementCode }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.monthlySettlementFlag == '1'" label="月结合同有效期" span="2">
                        {{ formatDate(collaborateOnReviewingData.partnerInfo.monthlySettlementStartTime) }} ~ {{ formatDate(collaborateOnReviewingData.partnerInfo.monthlySettlementEndTime) }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo" label="是否签订预存款合同">
                        {{ isNumberOrNumber(collaborateOnReviewingData.partnerInfo.advanceSettlementFlag) }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.advanceSettlementFlag == '1'" label="合同号" span="2">
                        {{ collaborateOnReviewingData.partnerInfo.advanceSettlementCode }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="collaborateOnReviewingData.partnerInfo.advanceSettlementFlag == '1'" label="月结合同有效期" span="2">
                        {{ formatDate(collaborateOnReviewingData.partnerInfo.advanceSettlementStartTime) }} ~ {{ formatDate(collaborateOnReviewingData.partnerInfo.advanceSettlementEndTime) }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-drawer>

        <!-- / 修改记录 -->
        <el-drawer v-model="modifyRecordsVisible" class="self-drawer" size="800px" title="修改记录" @close="hideModifyRecords">
            <el-timeline>
                <el-timeline-item v-for="(item, index) in modifyRecordsData" :key="index" :color="'#2A76F8'" :timestamp="item.createBy + ' ' + item.createDate" placement="top">
                    <el-table :data="item.changeDetails" border>
                        <el-table-column label="修改的字段" prop="field">
                            <template #default="scope">{{ selectDictLabel(modifyRecordsDict, scope.row.field) }}</template>
                        </el-table-column>
                        <el-table-column label="修改前" prop="oldValue">
                            <template #default="scope">
                                <span v-html="formatValue(scope.row, 'oldValue')"></span>
                            </template>
                        </el-table-column>
                        <el-table-column label="修改后" prop="value">
                            <template #default="scope">
                                <span class="text-red-500" v-html="formatValue(scope.row, 'value')"></span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-timeline-item>
            </el-timeline>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import moment from 'moment';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import CardHeader from '@/components/CardHeader';
import { selectDictLabel } from '@/utils/dictLabel';

export default {
    name: 'CooperativeCargoOwners',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar,
        CardHeader
    },
    data() {
        return {
            copyParams: null,
            formParams: {
                // 签约公司名称
                signCompany: undefined,
                // 自有设备
                isDevice: undefined,
                // 发票类型
                invoiceType: undefined,
                // 项目名称
                projectType: undefined,
                // 运输服务
                projectFeeType: undefined,
                // 税点
                taxPoint: undefined,
                // 合同号
                contractNo: undefined,
                // 合同有效期
                signContractTime: [],
                monthlyTime: [],
                advanceTime: [],
                // 对账单核对提醒时间
                checkRemind: 7,
                // 付款单支付提醒时间
                payRemind: 7,
                monthlySettlementFlag: '0',
                advanceSettlementFlag: '0',
                isKeyAccount: '0',
                monthlySettlementCode: '',
                advanceSettlementCode: '',
                companyId: '',
                companyName: '',
                invoiceList: [
                    {
                        invoiceType: undefined,
                        projectType: undefined,
                        projectFeeType: undefined,
                        taxPoint: undefined
                    }
                ]
            },
            editRow: null, //点击编辑行数据
            queryParams: {
                current: 1,
                size: 10,
                status: null,
                authStatus: null,
                companyId: null
            },
            ownerList: [],
            showSearch: true,
            statusList: [],
            columns: [
                { title: '货主名称', key: 'companyName', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true, fixed: 'left' },
                { title: '公司地址', key: 'companyAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '手机号码', key: 'companyPhone', align: 'center', width: '120px', columnShow: true },
                { title: '合作时间', key: 'cooperateTime', align: 'center', width: '140px', columnShow: true },
                { title: '签约公司', key: 'signCompany', align: 'center', width: '260px', columnShow: true, showOverflowTooltip: true },
                // { title: '发票类型', key: 'invoiceType', align: 'center', width: '80px', columnShow: true },
                // { title: '项目名称', key: 'projectType', align: 'center', width: '200px', columnShow: true },
                // { title: '税点', key: 'taxPoint', align: 'center', width: '80px', columnShow: true },
                { title: '取消时间', key: 'cancelTime', align: 'center', width: '140px', columnShow: true },
                { title: '状态', key: 'status', align: 'center', width: '120px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '是否收藏', key: 'isCollect', align: 'center', width: '120px', columnShow: true },
                { title: '是否大客户', key: 'isKeyAccount', align: 'center', width: '120px', columnShow: true },
                { title: '订单确认功能', key: 'authStatus', align: 'center', width: '120px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '220px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            applyForCooperationVisible: false,
            applyForCooperationLoading: false,
            applyForCooperationLoadingText: '加载中...',
            applyForCooperationForm: {},
            companyDropDownListData: [],
            companyInformationData: {},
            cooperativeReviewVisible: false,
            SysServiceCateGray: [],
            serviceCategrayFormCarrier: [],
            serviceTypeFormCarrier: [],
            cooperativeReviewLoading: false,
            cooperativeReviewLoadingText: '加载中...',
            collaborateOnReviewingData: {},
            // 经营许可证1文件列表
            sysCompanyType6: {},
            // 经营许可证2文件列表
            sysCompanyType7: {},
            // 生产许可证1文件列表
            sysCompanyType8: {},
            // 生产许可证2文件列表
            sysCompanyType9: {},
            // 营业执照编号1文件列表
            sysCompanyType10: {},
            // 营业执照编号2文件列表
            sysCompanyType11: {},
            isOpenDetails: false,
            rules: {
                signCompany: [{ required: true, message: '请选择签约公司名称', trigger: 'change' }],
                isDevice: [{ required: true, message: '请选择是否有自有设备', trigger: 'change' }],
                invoiceType: [{ required: true, message: '请选择', trigger: 'change' }],
                projectType: [{ required: true, message: '请选择', trigger: 'change' }],
                projectFeeType: [{ required: true, message: '请选择', trigger: 'change' }],
                taxPoint: [{ required: true, message: '请选择税点', trigger: 'change' }],
                checkRemind: [{ required: true, message: '请输入对账单核对提醒时间', trigger: 'change' }],
                payRemind: [{ required: true, message: '请输入付款单支付提醒时间', trigger: 'change' }],
                monthlySettlementFlag: [{ required: true, message: '请选择是否签订月结合同', trigger: 'change' }],
                monthlySettlementCode: [{ required: true, message: '请输入合同号', trigger: 'blur' }],
                monthlyTime: [{ required: true, message: '请输入合同有效期', trigger: 'change' }],
                advanceSettlementFlag: [{ required: true, message: '请选择是否签订预存款合同', trigger: 'change' }],
                advanceSettlementCode: [{ required: true, message: '请输入合同号', trigger: 'blur' }],
                advanceTime: [{ required: true, message: '请输入合同有效期', trigger: 'change' }]
            },
            companyList: [],
            invoiceTypeList: [],
            projectList: [
                { value: '1', label: '运输服务' },
                { value: '2', label: '物流辅助服务' }
            ],
            transportationCostsList: [
                { value: '1', label: '运输服务费' },
                { value: '2', label: '国内运输费' }
            ],
            serviceChargeList: [
                { value: '1', label: '收派服务费' },
                { value: '2', label: '仓储服务费' }
            ],
            taxPointList: [],
            modifyRecordsVisible: false,
            modifyRecordsData: [],
            modifyRecordsDict: [
                { name: '签约公司名称', value: 'signCompany' },
                { name: '自有设备', value: 'isDevice' },
                { name: '发票类型', value: 'invoiceType' },
                { name: '项目名称', value: 'projectType' },
                { name: '项目费用名称', value: 'projectFeeType' },
                { name: '税点', value: 'taxPoint' },
                { name: '签约公司合同号', value: 'contractNo' },
                { name: '合同有效期开始', value: 'signContractStartTime' },
                { name: '合同有效期结束', value: 'signContractEndTime' },
                { name: '对账单核对提醒时间', value: 'checkRemind' },
                { name: '付款单支付提醒时间', value: 'payRemind' },
                { name: '是否签订月结合同', value: 'monthlySettlementFlag' },
                { name: '月结合同号', value: 'monthlySettlementCode' },
                { name: '月结合同有效期开始', value: 'monthlySettlementStartTime' },
                { name: '月结合同有效期结束', value: 'monthlySettlementEndTime' },
                { name: '是否签订预存款合同', value: 'advanceSettlementFlag' },
                { name: '预存款合同有效期开始', value: 'advanceSettlementStartTime' },
                { name: '预存款合同有效期结束', value: 'advanceSettlementEndTime' },
                { name: '预存款合同号', value: 'advanceSettlementCode' },
                { name: '预存款合同有效期', value: 'advanceTime' },
                { name: '是否大客户', value: 'isKeyAccount' },
                { name: '项目信息', value: 'invoiceList' }
            ]
        };
    },
    computed: {
        // 格式化日期
        formatDate() {
            return (value) => {
                return moment(value).format('YYYY-MM-DD');
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        // 格式化状态
        formatStatus() {
            return (value) => {
                return this.selectDictLabel(this.statusList, value);
            };
        },
        isNumberOrNumber() {
            return (value) => {
                return value == '1' ? '是' : '否';
            };
        },
        serviceCategoryData() {
            if (this.serviceTypeFormCarrier) {
                const serviceCategoryLabel = this.SysServiceCateGray.filter((item) => this.serviceTypeFormCarrier.includes(item.value));
                return serviceCategoryLabel.map((item) => item.value).join('、');
            }
            return '';
        },
        serviceTypeData() {
            if (this.serviceCategrayFormCarrier) {
                const serviceCategoryLabel = this.SysServiceCateGray.filter((item) => this.serviceCategrayFormCarrier.includes(item.value));
                return serviceCategoryLabel.map((item) => item.value).join('、');
            }
            return '';
        }
    },
    watch: {
        applyForCooperationVisible: {
            handler() {
                if (!this.applyForCooperationVisible) {
                    this.formParams = {
                        // 签约公司名称
                        signCompany: undefined,
                        // 自有设备
                        isDevice: undefined,
                        // 发票类型
                        invoiceType: undefined,
                        // 项目名称
                        projectType: undefined,
                        // 运输服务
                        projectFeeType: undefined,
                        // 税点
                        taxPoint: undefined,
                        // 合同号
                        contractNo: undefined,
                        // 合同有效期
                        signContractTime: undefined,
                        // 对账单核对提醒时间
                        checkRemind: 7,
                        // 付款单支付提醒时间
                        payRemind: 7,
                        monthlySettlementFlag: '0',
                        advanceSettlementFlag: '0',
                        isKeyAccount: '0',
                        monthlySettlementCode: '',
                        advanceSettlementCode: '',
                        companyId: '',
                        companyName: '',
                        // 添加invoiceList的初始值
                        invoiceList: [
                            {
                                invoiceType: undefined,
                                projectType: undefined,
                                projectFeeType: undefined,
                                taxPoint: undefined
                            }
                        ]
                    };
                    this.editRow = null;
                }
            },
            immediate: true
        },
        'formParams.advanceSettlementFlag': {
            handler(val) {
                if (val == 0) {
                    this.formParams = {
                        ...this.formParams,
                        advanceSettlementCode: undefined,
                        advanceSettlementStartTime: undefined,
                        advanceSettlementEndTime: undefined,
                        advanceTime: []
                    };
                } else {
                    const { monthlyTime, monthlySettlementCode } = this.formParams;
                    if (monthlyTime?.length || monthlySettlementCode) {
                        // this.formParams.advanceSettlementFlag = '0'
                        this.$confirm('切换会清空月结合同信息，确认继续操作吗？', '提示', {
                            type: 'warning',
                            confirmButtonText: '确定',
                            cancelButtonText: '取消'
                        })
                            .then(() => {
                                this.formParams = {
                                    ...this.formParams,
                                    monthlySettlementCode: undefined,
                                    monthlyTime: [],
                                    monthlySettlementStartTime: undefined,
                                    monthlySettlementEndTime: undefined,
                                    monthlySettlementFlag: '0',
                                    advanceSettlementFlag: '1'
                                };
                            })
                            .catch(() => {
                                this.$nextTick(() => {
                                    this.formParams.advanceSettlementFlag = '0';
                                });
                            });
                    } else {
                        this.formParams = { ...this.formParams, monthlySettlementFlag: '0' };
                    }
                }
            }
        },
        'formParams.monthlySettlementFlag': {
            handler(val) {
                if (val == 0) {
                    this.formParams = {
                        ...this.formParams,
                        monthlySettlementCode: undefined,
                        monthlyTime: [],
                        monthlySettlementStartTime: undefined,
                        monthlySettlementEndTime: undefined
                    };
                } else {
                    const { advanceTime, advanceSettlementCode } = this.formParams;
                    if (advanceTime?.length || advanceSettlementCode) {
                        // this.formParams.monthlySettlementFlag = '0'
                        this.$confirm('切换会清空预存款合同信息，确认继续操作吗？', '提示', {
                            type: 'warning',
                            confirmButtonText: '确定',
                            cancelButtonText: '取消'
                        })
                            .then(() => {
                                this.formParams = {
                                    ...this.formParams,
                                    advanceSettlementCode: undefined,
                                    advanceTime: [],
                                    advanceSettlementStartTime: undefined,
                                    advanceSettlementEndTime: undefined,
                                    advanceSettlementFlag: '0',
                                    monthlySettlementFlag: '1'
                                };
                            })
                            .catch(() => {
                                this.$nextTick(() => {
                                    this.formParams.monthlySettlementFlag = '0';
                                });
                            });
                    } else {
                        this.formParams = { ...this.formParams, advanceSettlementFlag: '0' };
                    }
                }
            }
        }
    },
    created() {
        this.getDict();
        this.getList();
        this.getCompanyCarrierSelect();
    },
    methods: {
        /**
         * 收藏/取消收藏
         * @param e
         * @param row
         */
        changeCollect(e, row) {
            if (row.id) {
                this.loading = true;
                enterpriseCooperation
                    .updateIsCollect({ id: row.id, isCollect: e })
                    .then((response) => {
                        if (response.code === 200) {
                            this.msgSuccess('操作成功！');
                            this.getList();
                        }
                        this.loading = false;
                    })
                    .catch(() => {
                        this.loading = false;
                    });
            }
        },

        // 检查是否存在重复的项目组合
        checkDuplicateProject(currentItem) {
            return this.formParams.invoiceList.some((item) => item !== currentItem && item.projectType === currentItem.projectType && item.projectFeeType === currentItem.projectFeeType);
        },
        /**
         * 清除收藏
         */
        clearCollection() {
            this.loading = true;
            enterpriseCooperation
                .cancelCollect()
                .then((response) => {
                    if (response.code === 200) {
                        this.msgSuccess('操作成功！');
                        this.getList();
                    }
                    this.loading = false;
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        /**
         * 选择公司名称
         */
        companyNameChange(val) {
            // val 1 甘肃天囤医药有限公司 2 甘肃天囤信达瑞医疗器械供应链有限公司
            // 选择 甘肃天囤医药有限公司 默认设置 项目名称为运输服务 运输服务费 税点 9%
            // 如果 改变了项目名称 项目名称为 物流辅助服务 收派服务费 税点 6%
            // 选择 甘肃天囤信达瑞医疗器械供应链有限公司 默认设置 项目名称为 运输服务 运输服务费 税点 1%
            // 如果 改变了项目名称 项目名称为 物流辅助服务 收派服务费 税点 1%
            if (val === '1') {
                this.formParams.projectType = '运输服务';
                this.formParams.projectFeeType = '运输服务费';
                this.formParams.taxPoint = '9';
            } else {
                this.formParams.projectType = '运输服务';
                this.formParams.projectFeeType = '运输服务费';
                this.formParams.taxPoint = '1';
            }
        },
        /**
         * 格式化修改值
         * row 当前行数据 type 修改类型
         * @return {function(any, any): any}
         */
        formatValue(row, key) {
            const value = row[key];
            switch (row.field) {
                case 'signCompany':
                    return this.formatDictionaryData('companyList', value);
                case 'isDevice':
                    return value == '1' ? '是' : '否';
                case 'invoiceType':
                    return this.selectDictLabel(this.invoiceTypeList, value);
                case 'projectType':
                    return this.selectLabel(this.projectList, value);
                case 'projectFeeType':
                    return this.selectLabel(this.transportationCostsList, value);
                case 'taxPoint':
                    return this.selectDictLabel(this.taxPointList, value) + '%';
                case 'monthlySettlementFlag':
                case 'advanceSettlementFlag':
                case 'isKeyAccount':
                    return value == '1' ? '是' : '否';
                case 'invoiceList':
                    // 如果是字符串，尝试解析为对象或数组
                    if (typeof value === 'string') {
                        try {
                            const parsedValue = JSON.parse(value);
                            // 如果解析后是数组，处理数组中的每个项目
                            if (Array.isArray(parsedValue)) {
                                return parsedValue
                                    .map((item) => {
                                        const invoiceType = this.selectDictLabel(this.invoiceTypeList, item.invoiceType) || '-';
                                        const projectType = item.projectType || '-';
                                        const projectFeeType = item.projectFeeType || '';
                                        const taxPoint = item.taxPoint || '-';
                                        return `
                                    <div class="project-item-group">
                                        <el-descriptions :column="1" border size="small">
                                            <el-descriptions-item label="发票类型">${invoiceType}</el-descriptions-item>
                                            <el-descriptions-item label="项目名称">${projectType}${projectFeeType ? ' - ' + projectFeeType : ''}</el-descriptions-item>
                                            <el-descriptions-item label="税点">${taxPoint}%</el-descriptions-item>
                                        </el-descriptions>
                                    </div>`;
                                    })
                                    .join('');
                            } else {
                                // 如果解析后是单个对象，按单个项目处理
                                const invoiceType = this.selectDictLabel(this.invoiceTypeList, parsedValue.invoiceType) || '-';
                                const projectType = parsedValue.projectType || '-';
                                const projectFeeType = parsedValue.projectFeeType || '';
                                const taxPoint = parsedValue.taxPoint || '-';
                                return `
                                <div class="project-item-group">
                                    <el-descriptions :column="1" border size="small">
                                        <el-descriptions-item label="发票类型">${invoiceType}</el-descriptions-item>
                                        <el-descriptions-item label="项目名称">${projectType}${projectFeeType ? ' - ' + projectFeeType : ''}</el-descriptions-item>
                                        <el-descriptions-item label="税点">${taxPoint}%</el-descriptions-item>
                                    </el-descriptions>
                                </div>`;
                            }
                        } catch (e) {
                            return value;
                        }
                    }
                    // 如果是数组，使用原来的处理方式
                    if (Array.isArray(value)) {
                        return value
                            .map((item) => {
                                const invoiceType = this.selectDictLabel(this.invoiceTypeList, item.invoiceType) || '-';
                                const projectType = item.projectType || '-';
                                const projectFeeType = item.projectFeeType || '';
                                const taxPoint = item.taxPoint || '-';
                                return `
                            <div class="project-item-group">
                                <el-descriptions :column="1" border size="small">
                                    <el-descriptions-item label="发票类型">${invoiceType}</el-descriptions-item>
                                    <el-descriptions-item label="项目名称">${projectType}${projectFeeType ? ' - ' + projectFeeType : ''}</el-descriptions-item>
                                    <el-descriptions-item label="税点">${taxPoint}%</el-descriptions-item>
                                </el-descriptions>
                            </div>`;
                            })
                            .join('');
                    }
                    return value;
                default:
                    return value;
            }
        },
        // 查询所有货主信息
        getCompanyCarrierSelect() {
            enterpriseCooperation.cooperateSelect().then((res) => {
                if (res.code === 200 && res.data) {
                    this.ownerList = res.data;
                }
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.statusList = await this.getDictList('fourpl_partner_management_status');
            this.SysServiceCateGray = await this.getDictList('sys_service_categray');
            this.companyList = await this.getDictList('signing_company');
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
            this.taxPointList = await this.getDictList('tax_points_for_cooperative_shippers');
        },
        getList() {
            this.loading = true;
            enterpriseCooperation
                .listOwner(this.queryParams, '2')
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 获取合作伙伴下拉列表
        getPartnerList() {
            this.applyForCooperationLoading = true;
            this.applyForCooperationLoadingText = '加载中...';
            enterpriseCooperation
                .orgSelect({
                    type: '2'
                })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.companyDropDownListData = res.data || [];
                    }
                })
                .finally(() => {
                    this.applyForCooperationLoading = false;
                });
        },
        // 添加新的项目组
        handleAddProjectItem() {
            // 检查所有现有项目是否都已填写完整
            let allFieldsFilled = this.formParams.invoiceList.every((item) => item.invoiceType && item.projectType && item.projectFeeType && item.taxPoint);

            if (!allFieldsFilled) {
                this.$message.warning('请先完善所有已有项目信息');
                return;
            }

            // 检查是否有重复的组合
            const currentItem = this.formParams.invoiceList[this.formParams.invoiceList.length - 1];
            const isDuplicate = this.checkDuplicateProject(currentItem);
            if (isDuplicate) {
                this.$message.warning('已存在相同的发票类型和项目名称组合');
                return;
            }
            // 添加新项目
            this.formParams.invoiceList.push({
                invoiceType: undefined,
                projectType: undefined,
                projectFeeType: undefined,
                taxPoint: undefined
            });
        },
        /**
         * 重新合作或者取消合作
         * @param row 当前行数据
         * @param type 1 重新 3 取消
         */
        handleChangeCooperation(row, type) {
            const { id, companyName } = row;
            let confirmText = '';
            if (type === '1') {
                confirmText = '确定要重新与 ' + companyName + ' 合作吗？';
            } else if (type === '3') {
                confirmText = '确定取消与 ' + companyName + ' 的合作吗？';
            }
            this.$confirm(confirmText, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: type === '1' ? 'success' : 'warning'
            })
                .then(() => {
                    if (type === '1' || type === '2') {
                        this.cooperativeReviewLoading = true;
                        this.cooperativeReviewLoadingText = '加载中...';
                    }
                    const params = {
                        id,
                        status: type
                    };
                    enterpriseCooperation
                        .updateStatus(params)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('操作成功');
                                this.hideCooperativeReview();
                                this.getList();
                            }
                        })
                        .finally(() => {
                            if (type === '1' || type === '2') {
                                this.cooperativeReviewLoading = false;
                            }
                        });
                })
                .catch(() => {});
        },
        // 打开 详情
        handleCooperativeReview(row) {
            const { id, companyName } = row;
            if (id) {
                this.cooperativeReviewVisible = true;
                this.cooperativeReviewLoading = true;
                this.cooperativeReviewLoadingText = '加载中...';
                enterpriseCooperation
                    .queryOwnerById({ id })
                    .then((res) => {
                        if (res.code === 200 && res.data) {
                            const { serviceCategory, companyInfo } = res.data;
                            this.collaborateOnReviewingData = {
                                ...companyInfo,
                                id,
                                companyName,
                                partnerInfo: res.data
                            };
                            this.serviceCategrayFormCarrier = [];
                            this.serviceTypeFormCarrier = [];
                            if (serviceCategory) {
                                serviceCategory.map((item) => {
                                    if (item <= 4) {
                                        this.serviceCategrayFormCarrier.push(item);
                                    } else {
                                        this.serviceTypeFormCarrier.push(item);
                                    }
                                });
                            }
                            if (companyInfo) {
                                const { sysCompanyFile } = companyInfo;
                                // sysCompanyFile 中的 imageType=6 为营业执照
                                if (sysCompanyFile) {
                                    sysCompanyFile.map((item) => {
                                        if (item.imageType === '8') {
                                            this.sysCompanyType8 = item;
                                        }
                                        if (item.imageType === '9') {
                                            this.sysCompanyType9 = item;
                                        }
                                        if (item.imageType === '10') {
                                            this.sysCompanyType10 = item;
                                        }
                                        if (item.imageType === '11') {
                                            this.sysCompanyType11 = item;
                                        }
                                        if (item.imageType === '6') {
                                            this.sysCompanyType6 = item;
                                        }
                                        if (item.imageType === '7') {
                                            this.sysCompanyType7 = item;
                                        }
                                    });
                                }
                            }
                        }
                    })
                    .finally(() => {
                        this.cooperativeReviewLoading = false;
                    });
            }
        },
        // 打开 申请合作
        handleOpenApplyForCooperation(row) {
            this.applyForCooperationVisible = true;
            if (row) {
                this.editRow = row;
                this.newPartnerDetail(this.editRow);
            } else {
                this.getPartnerList('1,3');
            }
        },
        /**
         * 查看修改记录
         * @param row
         */
        handleOpenModificationRecord(row) {
            this.modifyRecordsVisible = true;
            enterpriseCooperation
                .queryChangeLog({
                    cooperateId: row.id
                })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.modifyRecordsData = res.data;
                    }
                });
        },
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },

        // 删除项目组
        handleRemoveProjectItem(index) {
            this.formParams.invoiceList.splice(index, 1);
        },
        /**
         * 选择合作伙伴
         * @param id 合作伙伴id
         */
        handleSelectPartner(id) {
            if (id) {
                // this.applyForCooperationLoading = true;
                // this.applyForCooperationLoadingText = '加载中...';
                // getCompanyInfo(id)
                // 	.then((res) => {
                // 		if (res.code === 200 && res.data) {
                // 			this.companyInformationData = res.data;
                // 			const {serviceCategory, sysCompanyFile} = res.data;
                // 			this.serviceCategrayFormCarrier = [];
                // 			this.serviceTypeFormCarrier = [];
                // 			if (serviceCategory) {
                // 				serviceCategory.map((item) => {
                // 					if (item <= 4) {
                // 						this.serviceCategrayFormCarrier.push(item);
                // 					} else {
                // 						this.serviceTypeFormCarrier.push(item);
                // 					}
                // 				});
                // 			}
                //
                // 			this.sysCompanyType6 = {};
                // 			this.sysCompanyType7 = {};
                // 			this.sysCompanyType8 = {};
                // 			this.sysCompanyType9 = {};
                // 			this.sysCompanyType10 = {};
                // 			this.sysCompanyType11 = {};
                // 			if (sysCompanyFile) {
                // 				sysCompanyFile.map((item) => {
                // 					if (item.imageType === '8') {
                // 						this.sysCompanyType8 = item;
                // 					}
                // 					if (item.imageType === '9') {
                // 						this.sysCompanyType9 = item;
                // 					}
                // 					if (item.imageType === '10') {
                // 						this.sysCompanyType10 = item;
                // 					}
                // 					if (item.imageType === '11') {
                // 						this.sysCompanyType11 = item;
                // 					}
                // 					if (item.imageType === '6') {
                // 						this.sysCompanyType6 = item;
                // 					}
                // 					if (item.imageType === '7') {
                // 						this.sysCompanyType7 = item;
                // 					}
                // 				});
                // 			}
                // 		}
                // 	})
                // 	.finally(() => {
                // 		this.applyForCooperationLoading = false;
                // 	});
            }
        },
        /** 订单确认功能&合作关系状态*/
        handleStatus(row) {
            if (row.id) {
                enterpriseCooperation.updateAuthStatus({ id: row.id, authStatus: row.authStatus }).then(() => {
                    this.getList();
                });
            }
        },
        // 提交申请
        handleSubmitAnApplication() {
            this.$refs['formParams'].validate(async (valid) => {
                if (valid) {
                    // 检查invoiceList是否为空或包含空对象
                    if (!this.formParams.invoiceList || this.formParams.invoiceList.length === 0) {
                        this.$message.warning('请至少添加一个项目信息');
                        return;
                    }

                    // 检查所有项目组合是否有重复
                    for (let i = 0; i < this.formParams.invoiceList.length; i++) {
                        const currentItem = this.formParams.invoiceList[i];
                        const isDuplicate = this.formParams.invoiceList.some((item, index) => index !== i && item.projectType === currentItem.projectType && item.projectFeeType === currentItem.projectFeeType);
                        if (isDuplicate) {
                            this.$message.warning('同一个项目名称只能添加一次');
                            return;
                        }
                    }

                    this.applyForCooperationLoading = true;
                    this.applyForCooperationLoadingText = '提交中...';
                    const { advanceTime, monthlyTime, signContractTime } = this.formParams;
                    const params = { ...this.formParams };

                    // 处理第一个projectItem的数据
                    if (params.invoiceList && params.invoiceList.length > 0) {
                        const firstItem = params.invoiceList[0];
                        params.invoiceType = firstItem.invoiceType;
                        params.projectType = firstItem.projectType;
                        params.projectFeeType = firstItem.projectFeeType;
                        params.taxPoint = firstItem.taxPoint;
                    }

                    if (monthlyTime?.length) {
                        params.monthlySettlementStartTime = monthlyTime[0] && moment(monthlyTime[0]).format('YYYY-MM-DD HH:mm:ss');
                        params.monthlySettlementEndTime = monthlyTime[1] && moment(monthlyTime[1]).format('YYYY-MM-DD HH:mm:ss');
                    }
                    if (advanceTime?.length) {
                        params.advanceSettlementStartTime = advanceTime[0] && moment(advanceTime[0]).format('YYYY-MM-DD HH:mm:ss');
                        params.advanceSettlementEndTime = advanceTime[1] && moment(advanceTime[1]).format('YYYY-MM-DD HH:mm:ss');
                    }
                    if (signContractTime?.length) {
                        params.signContractStartTime = signContractTime[0];
                        params.signContractEndTime = signContractTime[1];
                    }
                    delete params.monthlyTime;
                    delete params.advanceTime;

                    if (this.editRow) {
                        const { id } = this.editRow;
                        enterpriseCooperation
                            .saveOwner({ ...params, id })
                            .then((res) => {
                                if (res.code === 200) {
                                    const changeData = [];

                                    // 检查发票列表的变化
                                    const oldInvoiceList = this.copyParams.invoiceList || [];
                                    const newInvoiceList = params.invoiceList || [];

                                    // 如果发票列表长度不同，说明有添加或删除
                                    if (oldInvoiceList.length !== newInvoiceList.length) {
                                        changeData.push({
                                            oldValue: JSON.stringify(oldInvoiceList),
                                            value: JSON.stringify(newInvoiceList),
                                            field: 'invoiceList'
                                        });
                                    } else {
                                        // 检查每个发票项的变化
                                        for (let i = 0; i < newInvoiceList.length; i++) {
                                            const oldItem = oldInvoiceList[i];
                                            const newItem = newInvoiceList[i];

                                            if (oldItem.invoiceType !== newItem.invoiceType || oldItem.projectType !== newItem.projectType || oldItem.projectFeeType !== newItem.projectFeeType || oldItem.taxPoint !== newItem.taxPoint) {
                                                changeData.push({
                                                    oldValue: JSON.stringify(oldItem),
                                                    value: JSON.stringify(newItem),
                                                    field: 'invoiceList'
                                                });
                                                break; // 找到一个变化就跳出循环
                                            }
                                        }
                                    }

                                    // 检查其他字段的变化
                                    for (let Key in this.copyParams) {
                                        if (Key !== 'invoiceList' && params[Key] && String(this.copyParams[Key]) !== String(params[Key])) {
                                            changeData.push({
                                                oldValue: this.copyParams[Key],
                                                value: params[Key],
                                                field: Key
                                            });
                                        }
                                    }
                                    if (changeData.length === 0) {
                                        this.applyForCooperationLoading = false;
                                        this.hideApplyForCooperation();
                                        this.getList();
                                    } else {
                                        enterpriseCooperation.saveOwnerChange({ cooperateId: params.id, changeDetails: changeData }).then(() => {
                                            this.applyForCooperationLoading = false;
                                            // 如果是预付款合同
                                            if (params.advanceSettlementFlag === '1') {
                                                this.$confirm(`${this.editRow.companyName}已成功签订预付款合同，请前往"客户预付款"维护货主的预付款账户！`, '提示', {
                                                    confirmButtonText: '确定',
                                                    cancelButtonText: '取消',
                                                    type: 'warning'
                                                })
                                                    .then(() => {
                                                        // 确认后跳转到客户预付款页面
                                                        this.$router.push({
                                                            path: '/customerPrepayment/CustomerPrepaymentBalance',
                                                            query: {
                                                                companyId: this.editRow.companyId
                                                            }
                                                        });
                                                    })
                                                    .catch(() => {
                                                        this.hideApplyForCooperation();
                                                        this.getList();
                                                    });
                                            } else {
                                                this.$message.success('提交成功');
                                                this.hideApplyForCooperation();
                                                this.getList();
                                            }
                                        });
                                    }
                                }
                            })
                            .catch(() => {
                                this.applyForCooperationLoading = false;
                            })
                            .finally(() => {
                                this.applyForCooperationLoading = false;
                            });
                    } else {
                        enterpriseCooperation
                            .saveOwner({ ...params, companyId: this.applyForCooperationForm.id })
                            .then((res) => {
                                if (res.code === 200) {
                                    if (params.advanceSettlementFlag === '1') {
                                        const companyName = this.companyDropDownListData.find((item) => item.id === this.applyForCooperationForm.id).name;
                                        this.$confirm(`${companyName}已成功签订预付款合同，请前往"客户预付款"维护货主的预付款账户！`, '提示', {
                                            confirmButtonText: '确定',
                                            cancelButtonText: '取消',
                                            type: 'warning'
                                        })
                                            .then(() => {
                                                this.$router.push({
                                                    path: '/customerPrepayment/CustomerPrepaymentBalance',
                                                    query: {
                                                        companyId: this.applyForCooperationForm.id
                                                    }
                                                });
                                            })
                                            .catch(() => {
                                                this.hideApplyForCooperation();
                                                this.getList();
                                            });
                                    } else {
                                        this.$message.success('提交成功');
                                        this.hideApplyForCooperation();
                                        this.getList();
                                    }
                                }
                                this.applyForCooperationLoading = false;
                            })
                            .catch(() => {
                                this.applyForCooperationLoading = false;
                            })
                            .finally(() => {
                                this.applyForCooperationLoading = false;
                            });
                    }
                } else {
                    return false;
                }
            });
        },
        // 关闭 申请合作
        hideApplyForCooperation() {
            this.applyForCooperationVisible = false;
            // this.$refs.applyForCooperationForm.resetFields();
            this.sysCompanyType6 = {};
            this.sysCompanyType7 = {};
            this.sysCompanyType8 = {};
            this.sysCompanyType9 = {};
            this.sysCompanyType10 = {};
            this.sysCompanyType11 = {};
        },
        hideCooperativeReview() {
            this.cooperativeReviewVisible = false;
            this.sysCompanyType6 = {};
            this.sysCompanyType7 = {};
            this.sysCompanyType8 = {};
            this.sysCompanyType9 = {};
            this.sysCompanyType10 = {};
            this.sysCompanyType11 = {};
        },
        /**
         * 隐藏修改记录
         */
        hideModifyRecords() {
            this.modifyRecordsVisible = false;
            this.modifyRecordsData = [];
        },
        // 获取编辑回显详情
        async newPartnerDetail(row) {
            this.applyForCooperationLoading = true;
            this.applyForCooperationLoadingText = '加载中...';
            const { id } = row;
            let res = await enterpriseCooperation.queryOwnerById({ id });
            if (res.code === 200) {
                const partnerInfo = res.data;
                partnerInfo.signContractStartTime = partnerInfo.signContractStartTime && this.formatDate(partnerInfo.signContractStartTime);
                partnerInfo.signContractEndTime = partnerInfo.signContractEndTime && this.formatDate(partnerInfo.signContractEndTime);

                // 确保 invoiceList 至少有一个空对象
                if (!partnerInfo.invoiceList || partnerInfo.invoiceList.length === 0) {
                    partnerInfo.invoiceList = [{
                        invoiceType: undefined,
                        projectType: undefined,
                        projectFeeType: undefined,
                        taxPoint: undefined
                    }];
                } else {
                    // 处理 partnerInfo.invoiceList 只保留 invoiceType, projectType, projectFeeType, taxPoint
                    partnerInfo.invoiceList = partnerInfo.invoiceList.map((item) => ({
                        invoiceType: item.invoiceType,
                        projectType: item.projectType,
                        projectFeeType: item.projectFeeType,
                        taxPoint: item.taxPoint
                    }));
                }

                const params = partnerInfo;
                this.copyParams = JSON.parse(JSON.stringify(params));
                const { monthlySettlementStartTime, monthlySettlementEndTime, advanceSettlementStartTime, advanceSettlementEndTime, isDevice, taxPoint, signContractStartTime, signContractEndTime } = params;

                // 如果 params.monthlySettlementFlag 等于 0 删除 params.monthlySettlementCode 和 params.monthlySettlementStartTime 和 params.monthlySettlementEndTime
                if (params.monthlySettlementFlag === '0') {
                    delete params.monthlySettlementCode;
                    delete params.monthlySettlementStartTime;
                    delete params.monthlySettlementEndTime;
                } else {
                    params.monthlyTime = monthlySettlementStartTime && monthlySettlementEndTime && [monthlySettlementStartTime, monthlySettlementEndTime];
                }
                if (params.advanceSettlementFlag === '0') {
                    delete params.advanceSettlementCode;
                    delete params.advanceSettlementStartTime;
                    delete params.advanceSettlementEndTime;
                } else {
                    params.advanceTime = advanceSettlementStartTime && advanceSettlementEndTime && [advanceSettlementStartTime, advanceSettlementEndTime];
                }
                if (params.signContractFlag === '0') {
                    delete params.signContractStartTime;
                    delete params.signContractEndTime;
                } else {
                    params.signContractTime = signContractStartTime && signContractEndTime && [signContractStartTime, signContractEndTime];
                }
                // isDevice 类型如果为 字符串 则转为数字
                if (typeof isDevice === 'string') {
                    params.isDevice = Number(isDevice);
                }
                // taxPoint 类型如果为数字 则转为字符串
                if (typeof taxPoint === 'number') {
                    params.taxPoint = taxPoint.toString();
                }
                this.formParams = params;
                console.log(this.formParams)
            }
            this.applyForCooperationLoading = false;
        },
        /**
         * 根据选择的项目名称更改表单参数
         * @param val 选择的项目名称，'1'代表运输服务，'2'代表物流辅助服务
         * @param index
         */
        projectIdChange(val, index) {
            const currentItem = this.formParams.invoiceList[index];
            if (val === '运输服务') {
                currentItem.projectFeeType = '运输服务费';
                if (this.formParams.signCompany === '1') {
                    currentItem.taxPoint = '9';
                } else {
                    currentItem.taxPoint = '1';
                }
            } else {
                currentItem.projectFeeType = '收派服务费';
                if (this.formParams.signCompany === '1') {
                    currentItem.taxPoint = '6';
                } else {
                    currentItem.taxPoint = '1';
                }
            }
        },
        resetQuery() {
            this.$refs['queryParams'].resetFields();
            this.handleQuery();
        },
        selectLabel(list, value) {
            // 假设数据字典项格式为 { label: '标签名', value: '对应值' }
            const item = list.find((item) => item.value == value);
            return item ? item.label : value;
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-timeline-item) {
    padding-bottom: 10px;
}
:deep(.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell) {
    padding-bottom: 8px;
}

.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.d-box-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    font-size: 14px;
}

.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}

.cell-content-latin {
    white-space: nowrap;
}

.box__three__column {
    display: flex;
    font-size: 14px;
    flex-direction: column;
    gap: 20px;

    .box__three__column__item {
        display: grid;
        grid-template-columns: 1fr 140px;
        grid-template-rows: repeat(2, 1fr);
        grid-gap: 8px 20px;
        flex: 1;
        align-items: center;

        .div1 {
            grid-area: 1 / 1 / 2 / 2;
            display: flex;
            justify-content: space-between;

            .item__name {
                color: #999999;
            }
        }

        .div2 {
            grid-area: 2 / 1 / 3 / 2;
            display: flex;
            justify-content: space-between;

            .item__name {
                color: #999999;
            }
        }

        .div3 {
            grid-area: 1 / 2 / 3 / 3;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
    }
}

.box__flex__empty {
    display: flex;
    justify-content: flex-end;
    height: 100%;
    padding-right: 10px;
}

.d-box-info {
    display: flex;
    justify-content: space-between;

    :nth-child(1) {
        flex-shrink: 0;
        flex-grow: 0;
        text-align: left;
        margin-right: 10px;
        color: #999999;
    }

    :nth-child(2) {
        color: #333333;
    }
}

.number__unit__element {
    position: relative;

    :deep(.el-input__inner) {
        text-align: left;
    }

    &::after {
        content: '天';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}

.tax_point_unit {
    position: relative;

    :deep(.el-input__inner) {
        text-align: left;
    }

    &::after {
        content: '%';
        position: absolute;
        right: 30px;
        top: 51%;
        transform: translateY(-50%);
    }
}

.project-item-group {
    margin-bottom: 12px;

    :deep(.el-descriptions) {
        background-color: #fff;

        .el-descriptions__body {
            padding: 8px;
        }

        .el-descriptions__label {
            width: 80px;
            color: #606266;
            font-weight: normal;
            background-color: #f5f7fa;
        }

        .el-descriptions__content {
            color: #333;
        }
    }

    &:last-child {
        margin-bottom: 0;
    }
}
</style>
