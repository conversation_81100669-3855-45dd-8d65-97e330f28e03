import request from '@/utils/request'

export default {
	// 查询XML报表管理列表
	listXmltemplate:function (params) {
		return request.get('/printer/xmlTemplate/list',params);
	},
	// 根据xml模板id获取模板
	getXmltemplate: function (params) {
		return request.get('/printer/xmlTemplate/queryById', params);
	},

	//新增/修改模板
	updateXmltemplate: function (inputForm) {
		return request.post(
			'/printer/xmlTemplate/save',
			inputForm
		)
	},
}



// 更新类型查询报表
export function listXmltemplateType(type) {
  return request({
    url: '/printer/xmlTemplate/listType/'+type,
    method: 'get',
  })
}

// 查询XM报表管理详细
export function getXmltemplate(fId) {
  return request({
    url: '/printer/xmlTemplate/' + fId,
    method: 'get'
  })
}

// 新增XM报表管理
export function addXmltemplate(data) {
  return request({
    url: '/printer/xmlTemplate',
    method: 'post',
    data: data
  })
}

// 修改XM报表管理
export function updateXmltemplate(data) {
  return request({
    url: '/printer/xmlTemplate',
    method: 'put',
    data: data
  })
}

// 删除XM报表管理
export function delXmltemplate(fId) {
  return request({
    url: '/printer/xmlTemplate/' + fId,
    method: 'delete'
  })
}
