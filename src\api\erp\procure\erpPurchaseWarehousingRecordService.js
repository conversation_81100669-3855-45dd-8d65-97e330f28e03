import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/procure/erpPurchaseWarehousingRecord/save',
      inputForm
    )
  },
  export:function (params) {
    return http.post(
      '/file/importRecordeDownloadFile',
      params, { responseType: 'blob' },true
    )
  },
  delete: function (ids) {
    return http.delete(
      '/erp/procure/erpPurchaseWarehousingRecord/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/procure/erpPurchaseWarehousingRecord/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseWarehousingRecord/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/procure/erpPurchaseWarehousingRecord/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/procure/erpPurchaseWarehousingRecord/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/procure/erpPurchaseWarehousingRecord/import',
      data
    )
  }
}
