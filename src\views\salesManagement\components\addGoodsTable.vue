<template>
  <el-table v-loading="loadingFlag2" :data="addGoods.tableData" :header-cell-style="{ 'text-align': 'center' }"
            :row-key="getRowKey" :row-style="addClass" border style="width: 100%; margin-top: 20px"
            @selection-change="addFn">
    <el-table-column :reserve-selection="true" :selectable="selectable" type="selection" width="55"/>
    <el-table-column :reserve-selection="true" fixed label="商品名称" prop="commodity.tradeName" width="150px">
      <template #default="scope">
        <p style="display:flex;align-items:center;justify-content:space-evenly">
          <el-tooltip :content="scope.row.commodity.tradeName" class="box-item" effect="dark" placement="top">
            <span class="tradeNameStyle">{{ scope.row.commodity.tradeName }}</span>
          </el-tooltip>
          <el-tooltip v-if="!scope.row.isOptional" :content="scope.row.notOptional" class="box-item" effect="dark"
                      placement="top">
            <el-icon :size="20" color="#b0b2b5">
              <QuestionFilled size="30"/>
            </el-icon>
          </el-tooltip>
        </p>
      </template>
    </el-table-column>
    <el-table-column :reserve-selection="true" label="自编码" prop="commodity.commoditySelfCode" width="120px"/>
    <el-table-column :reserve-selection="true" label="规格" prop="commodity.packageSpecification" width="120px"/>
    <el-table-column :reserve-selection="true" label="产地" prop="commodity.originPlace" width="120px"/>
    <el-table-column :reserve-selection="true" label="类型" prop="commodity.grugsType" width="120px"/>
    <el-table-column :reserve-selection="true" label="剂型" prop="commodity.dosageForm" width="120px"/>
    <el-table-column :reserve-selection="true" label="基本单位" prop="commodity.basicUnit" width="120px"/>

    <el-table-column :reserve-selection="true" label="入库数量" prop="intoQuantity" width="120px"/>
    <el-table-column :reserve-selection="true" label="库存余量" prop="inventoryBalance" width="120px"/>
    <el-table-column :reserve-selection="true" label="可开数量" prop="inventory" width="120px">
    </el-table-column>
    <el-table-column :reserve-selection="true" label="成本单价" prop="unitPrice" width="120px"/>
    <el-table-column :reserve-selection="true" label="批号" prop="batchNumber" width="120px"/>
    <el-table-column :reserve-selection="true" label="生产日期" prop="produceDate" width="150px">
      <template #default="scope">
        {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
      </template>
    </el-table-column>
    <el-table-column :reserve-selection="true" label="有效期" prop="commodity.validityTime" width="120px"/>
    <el-table-column :reserve-selection="true" label="生产厂家" prop="manufacture.enterpriseName" width="140px"/>
    <el-table-column :reserve-selection="true" label="经手人" prop="handleBy.name" width="120px"/>
    <el-table-column :reserve-selection="true" label="审核状态" prop="commodity.status" width="120px">
      <template #default="scope">
        {{ echo1(scope.row.commodity) }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, toRefs, watchEffect} from 'vue';
import {functionIndex} from "@/views/salesManagement/functionIndex";
import {Plus, UploadFilled, Search, QuestionFilled} from '@element-plus/icons-vue'
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const data = reactive({})
const emit = defineEmits(['addFn'])
const selectable = (row) => {
  return row.isOptional
}
const props = defineProps({
  loadingFlag2: {
    default: false
  },
  addGoods: {
    default: null
  }
})
const getRowKey = (row) => {
  return row.purchaseRecordId
}
const {loadingFlag2, addGoods} = toRefs(props)
const addFn = (selection) => {
  console.log(selection)
  addGoods.value.addList = selection;
}
const addClass = ({row}) => {
  if (!row.isOptional) {
    return {
      color: 'rgb(181,181,181)'
    }
  }
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})
const echo1 = (row) => {
  let n = "";
  if (row.status == "0") {
    n = "草稿";
  } else if (row.status == "1") {
    n = "待审核";
  } else if (row.status == "2") {
    n = "审核中";
  } else if (row.status == "3") {
    n = "审核通过";
  } else if (row.status == "4") {
    n = "驳回";
  } else if (row.status == "5") {
    n = "撤销";
  } else {
    n = "";
  }
  return n;
};
</script>
<style lang='scss' scoped></style>
