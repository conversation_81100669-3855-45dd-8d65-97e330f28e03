<template>
	<div class="login_bg">
		<div>
			<div class="login_main">

				<!-- 左 -->
        <div class="login_left">
          <div class="login-left-text">
            <h1>医药物流企业运输系统（TMS）</h1>
            <span>通过自动化和信息化的手段，提高物流效率，降低运营成本，满足药品行业对质量和合规性的要求。</span>
          </div>
          <div class="login-left-img">
            <img src="../../../public/img/log_left.png" alt="通过自动化和信息化的手段，提高物流效率，降低运营成本，满足药品行业对质量和合规性的要求。" />
          </div>
        </div>
        <!-- 右 -->
        <div class="login-form">
          <div v-if="formFlag">
            <div style="display: flex;justify-content: space-between;align-items: center;">
              <el-tabs v-model="activeName">
                <el-tab-pane label="登录" name="1"></el-tab-pane>
              </el-tabs>
              <div style="display: flex; gap: 20px;">
                <el-popover :width="200" placement="bottom" trigger="hover">
                  <div class="box-miniprogram">
                    <img :src="require('@/assets/home/<USER>')" style="width: 180px; height: 180px;" />
                    <div style="text-align: center; margin-top: 5px">司机小程序</div>
                  </div>
                  <template #reference>
                    <span class="miniprogram-link">
                      <img :src="require('@/assets/home/<USER>')" style="width: 16px; height: 16px; margin-right: 4px;" />
                      司机小程序
                    </span>
                  </template>
                </el-popover>
                <el-popover :width="320" placement="bottom" trigger="hover">
                  <div class="box-app-download">
                    <div>
                      <qr-code-with-logo :size="132" :value="driverAddress" logo="images/driver_end_logo.png"/>
                      <div style="text-align: center; margin-top: 5px">司机端2.0下载</div>
                    </div>
                    <div>
                      <qr-code-with-logo :size="132" :value="hubAddress" logo="images/distribution_terminal_logo.png"/>
                      <div style="text-align: center; margin-top: 5px">集散端2.0下载</div>
                    </div>
                  </div>
                  <template #reference>
                    <span slot="reference" class="app-download">下载APP</span>
                  </template>
                </el-popover>
              </div>
            </div>
            <password-form class="password-form" :handlerReset="handlerReset"></password-form>
            <div style="width: 100%; text-align: center">
              <span style="color: #808080">登录即视为同意</span>
              <span style="color: #1a5df3">《用户协议》</span>
              <span style="color: #808080">和</span>
              <span style="color: #1a5df3">《隐私政策》</span>
            </div>
          </div>
        </div>
			</div>
      <!--  底部  -->
      <div class="footer-box">
        <span>Copyright © 2022-2023 甘肃天囤医药有限公司 版权所有</span>
        <div class="footer-main">
          陇ICP备2022001113-1号
          <el-image :src="require('../../assets/images/gabh.png')"  fit="contain"></el-image>
          甘公网安备62010302001535号
        </div>
      </div>
		</div>
  <!--广告-->
<advertising-component :ad-open="adOpen" @close="adOpen=false;"></advertising-component>

	</div>
</template>

<script>
import passwordForm from "./components/passwordForm";
import phoneForm from "./components/phoneForm";
import resetPassword from "./components/resetPassword.vue";
import QrCodeWithLogo from "@/components/qrcode/QrCodeWithLogo.vue";
import AdvertisingComponent from "@/components/AdvertisingComponent/AdvertisingComponent";
import { getterFullDomainName } from '@/utils';
export default {
	components: {
    AdvertisingComponent,
		passwordForm,
		phoneForm,
		resetPassword,
    QrCodeWithLogo,
	},
	data() {
		return {
			config: {
				lang: this.$TOOL.data.get("APP_LANG") || this.$CONFIG.LANG,
				dark: this.$TOOL.data.get("APP_DARK") || false,
			},
			lang: [
				{
					name: "简体中文",
					value: "zh-cn",
				},
				{
					name: "English",
					value: "en",
				},
			],
			WechatLoginCode: "",
			showWechatLogin: false,
			isWechatLoginResult: false,
			formFlag: true,
			formFlag2: false,
      activeName:'1',
      driverAddress: getterFullDomainName()+ '/#/driverSideDownload',
      hubAddress: getterFullDomainName() + '/#/distributedDownload',
      adOpen:false,
		};
	},
	watch: {
		"config.dark"(val) {
			if (val) {
				document.documentElement.classList.add("dark");
				this.$TOOL.data.set("APP_DARK", val);
			} else {
				document.documentElement.classList.remove("dark");
				this.$TOOL.data.remove("APP_DARK");
			}
		},
		"config.lang"(val) {
			this.$i18n.locale = val;
			this.$TOOL.data.set("APP_LANG", val);
		},
	},
	created: function () {
		this.$TOOL.cookie.remove("TOKEN");
		this.$TOOL.data.remove("USER_INFO");
		this.$TOOL.data.remove("ROLE_LIST");
		this.$TOOL.data.remove("Organization");
		this.$TOOL.data.remove("MENU");
		this.$TOOL.data.remove("PERMISSIONS");
		this.$TOOL.data.remove("orgKey");
		this.$TOOL.data.remove("DASHBOARDGRID");
		this.$TOOL.data.remove("grid");
        this.$TOOL.data.remove("NETWORK");
		this.$store.commit("clearViewTags");
		this.$store.commit("clearKeepLive");
		this.$store.commit("clearIframeList");
		console.log(
			"%c SCUI %c Gitee: https://gitee.com/lolicode/scui",
			"background:#666;color:#fff;border-radius:3px;",
			""
		);
		this.adOpen = true;
	},
	methods: {
		configDark() {
			this.config.dark = this.config.dark ? false : true;
		},
		configLang(command) {
			this.config.lang = command.value;
		},
		wechatLogin() {
			this.showWechatLogin = true;
			this.WechatLoginCode =
				"SCUI-823677237287236-" + new Date().getTime();
			this.isWechatLoginResult = false;
			setTimeout(() => {
				this.isWechatLoginResult = true;
			}, 3000);
		},
		handlerPhone() {
			console.log(111);
			this.formFlag = false;
			this.formFlag2 = true;
		},
		handlerUser() {
			this.formFlag = true;
			this.formFlag2 = false;
		},
		handlerReset() {
			this.formFlag = false;
			this.formFlag2 = false;
		},
	},
};
</script>

<style scoped lang="scss">

.login_bg {
	width: 100%;
	height: 100%;
	display: flex;
  background-image: url(../../../public/img/log_bg.png);
  background-size: 100% 100%;
  align-items: center;
  flex-direction: column;
  justify-content: space-evenly;
  .login_main {
    flex: 1;
    overflow: auto;
    width: 1100px;
    height: 555px;
    position: relative;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -50%);
    background: #FFFFFF;
    box-shadow: 0px 4px 35px 0px rgba(3,3,3,0.07);
    border-radius: 8px;

    .login_left {
      .login-left-text{
        width: 500px;
        padding: 41px 53px;
        h1 {
          font-size: 24px;
          font-weight: 400;
          color: #1664FF;
          line-height: 45px;
        }

        span {
          font-size: 14px;
          font-weight: 400;
          color: #666666;
          line-height: 24px;
        }
      }
      .login-left-img{
        width: 100%;
        text-align: center;
        margin-left: -40px;
        img{
          margin: auto;
          width: 521px;
          height: 372px;
        }
      }

    }

    .login-form {
      position: absolute;
      right: 0;
      top: 0;
      width: 400px;
      height: 555px;
      background: #FFFFFF;
      box-shadow: 0px 4px 35px 0px rgba(3,3,3,0.07);
      border-radius: 0px 8px 8px 0px;
      padding: 20px;
      .password-form{
        padding: 20px;
      }
    }
  }
}




.login-header .logo {
	display: flex;
	align-items: center;
}

.login-header .logo img {
	width: 40px;
	height: 40px;
	vertical-align: bottom;
	margin-right: 10px;
}

.login-header .logo label {
	font-size: 26px;
	font-weight: bold;
}
::v-deep{
  .el-tabs__item:hover,
  .el-tabs__item.is-active {
    color: #000000;
    font-weight: 400;
  }
  .el-tabs__active-bar {
    height: 3px;
    background-color: #000000;
  }
}
.app-download{
  color: #333333;
  font-size:14px;
  :hover {
    color: #5670fe;
  }
}
.miniprogram-link {
  color: #333333;
  font-size: 14px;
  display: flex;
  align-items: center;
  cursor: pointer;
  img {
    vertical-align: middle;
    margin-right: 4px;
  }
}
.box-miniprogram {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}
.box-app-download {
  display: flex;
  align-items: center;
  justify-content: space-around;
  gap: 20px;
}
.footer-box{
  padding: 80px 10px 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 11px;
  line-height: 24px;
  font-weight: 400;
  color: #999999;
  .footer-main{
    display: flex;
    align-items: center;
  }
}
@media (max-width: 1366px) {
  .login_bg{
    .login_main{
      width: 1000px;
      height: 500px;
      top: 45%;
      .login_left {
        .login-left-img{
          margin-left: -80px;
          img{
            width: 466px;
            height: 317px;
          }
        }

      }
      .login-form {
        height: 500px;
        padding: 10px 15px;
        .password-form{
          padding: 10px 15px;
        }
      }
    }

  }
}

</style>
