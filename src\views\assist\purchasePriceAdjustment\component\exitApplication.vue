<template>
    <div class="app-container" v-if="open">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog :title="title" v-model="open" width="85%" :before-close="close">
            <div v-loading="loading">
                <div class="step">
                    <el-steps :active="active" align-center>
                        <el-step title="选择入库记录" />
                        <el-step title="填写调价信息" />
                    </el-steps>
                </div>
                <div class="step1 " v-show="active == 1">
                    <el-form :model="queryParams" ref="queryParamsRef" :inline="true" class="form_130"
                        :rules="queryParamsRules">
                        <div class="box">
                            <el-form-item label="供应商" prop="supplierId">
                                <el-select v-model="queryParams.supplierId" filterable placeholder="请选择供应商" class="form_225"
                                    clearable>
                                    <el-option :label="item.enterpriseName" :value="item.id" v-for="item in supplierList"
                                        :key="item.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="商品名称" prop="tradeName">
                                <el-input v-model="queryParams.tradeName" placeholder="请输入商品名称/自编码/拼音码" clearable
                                    class="form_225" />
                            </el-form-item>
                            <el-form-item label="经手人" prop="creatorId">
                                <el-select v-model="queryParams.creatorId" filterable placeholder="请选择经手人" clearable
                                    class="form_225" :disabled="roleList.length == 1 && roleList[0].enName == 'ywjl'">
                                    <el-option v-for="item in optionList" :key="item.id" :label="item.name"
                                        :value="item.id" />
                                </el-select>
                            </el-form-item>

                            <el-form-item label="入库日期" prop="createDate">
                                <div class="box_date">
                                    <el-date-picker v-model="queryParams.createDate" type="daterange" range-separator="至"
                                        start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" clearable />
                                </div>
                            </el-form-item>
                            <el-form-item label="单据编号" prop="orderCode">
                                <el-input v-model="queryParams.orderCode" placeholder="请输入单据编号" clearable
                                    class="form_225" />
                            </el-form-item>
                            <el-button type="primary" @click="step1_getList" class="step1_search_btn">查询</el-button>
                        </div>
                    </el-form>
                    <el-table :data="step1_list" border style="margin-top: 20px;" row-key="id"
                        ref="entrust_out_table_listRef" v-loading="loading"
                        @selection-change="handleSelectionChange_step1_list">
                        <el-table-column type="selection" min-width="55" align="center" :reserve-selection="true"
                            fixed="left" />
                        <el-table-column label="供应商" prop="suppier" :show-overflow-tooltip="true" align="center"
                            min-width="170" />
                        <el-table-column label="单据编号" prop="orderCode" :show-overflow-tooltip="true" align="center"
                            min-width="170" />
                        <el-table-column label="单据创建日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="row => row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="入库日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="row => row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />

                        <el-table-column label="自编码" prop="commodity.commoditySelfCode" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="批号" prop="intoNo" :show-overflow-tooltip="true" align="center"
                            min-width="160" />
                        <el-table-column label="仓库" prop="storages" :show-overflow-tooltip="true" align="center"
                            min-width="160" />
                        <el-table-column label="货位" prop="storages" :show-overflow-tooltip="true" align="center"
                            min-width="160" />
                        <el-table-column label="入库数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="160" />
                        <el-table-column label="单价" prop="unitPrice" :show-overflow-tooltip="true" align="center"
                            min-width="80" />
                        <el-table-column label="可调数量" prop="adjustableQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="80" :formatter="row => row.adjustableQuantity ? row.adjustableQuantity : 0" />
                    </el-table>
                    <div style="display:flex;justify-content:end;">
                        <pagination v-show="step1_list_Total > 0" :total="step1_list_Total"
                            v-model:page="queryParams.current" v-model:limit="queryParams.size"
                            @pagination="step1_getList" />
                    </div>
                </div>
                <div class="step2" v-show="active == 2">
                    <el-table :data="choose_setp1" :expand-row-keys="comeList" :row-key="row => row.id" style="width: 100%"
                        @expand-change="onchangeTable">
                        <el-table-column type="expand">
                            <template #default="props">
                                <div style="display: flex;justify-content: end">
                                    <div m="4"
                                        style="display: flex;width: 87%; justify-content:flex-end;align-items: center">
                                        <!-- <div style="color: red;font-size: 13px;line-height: 25px">
                                            <p>注意事项：</p>
                                            <p>1、调整数量总和不能大于所选出库（入库）记录当前的可调数量；</p>
                                            <p>2、调整数量不能录入小数；</p>
                                            <p>3、调整后单价不能大于所选出库（入库）记录当前的单价。</p>
                                        </div> -->
                                        <el-table :border="true" :data="props.row.adjustPriceDTOs" size="small"
                                            style="width: 55%">
                                            <el-table-column :value-on-clear="0" align="center" label="调整数量"
                                                prop="adjustQuantity">
                                                <template #default="scope">
                                                    <el-input-number :precision="0" :size="'small'" :step="1" :min="0"
                                                        @change="(val) => handleChangeINputNumber(val, props.$index, 'num')"
                                                        style="width: 70%" v-model="scope.row.adjustQuantity" />
                                                </template>


                                            </el-table-column>
                                            <el-table-column :value-on-clear="0" align="center" label="调整后单价"
                                                prop="adjustUnitPrice">
                                                <template #default="scope">
                                                    <el-input-number :precision="2" :size="'small'" :step="1" :min="0"
                                                        :max="Number(props.row.unitPrice)"
                                                        @change="(val) => handleChangeINputNumber(val, props.$index)"
                                                        style="width: 70%" v-model="scope.row.adjustUnitPrice" />
                                                </template>

                                            </el-table-column>
                                            <el-table-column align="center" label="操作" prop="" width="120px">
                                                <template #default="scope">
                                                    <el-button :size="'small'" text type="primary"
                                                        @click="handleItemOrder(props.$index, scope.row.id)">删除</el-button>
                                                </template>

                                            </el-table-column>
                                        </el-table>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="商品名称" prop="commodity.tradeName" />
                        <el-table-column align="center" label="批号" prop="intoNo" />
                        <el-table-column align="center" label="入库数量" prop="receivingQuantity" />
                        <el-table-column align="center" label="可调数量" prop="adjustableQuantity" />
                        <el-table-column align="center" label="单价" prop="unitPrice" />
                        <el-table-column align="center" label="调价金额" prop="adjustPrice" fixed="right"> <template
                                #default="scope">
                                <span style="color: red;font-weight: bold;font-size: 16px;">{{ scope.row.adjustPrice || 0.00
                                }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="操作" prop="operate" fixed="right">
                            <template #default="scope">
                                <el-button size="small" type="primary" @click="detailFn(scope.row)">查看详情</el-button>
                                <el-button size="small" type="primary" @click="newAdd(scope.row)">新增调价</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-form ref="creatform" :model="form" :rules="creatRules" label-width="90px" style="margin-top: 20px">
                        <el-form-item label="调价说明" style="width: 50%" prop="remark">
                            <el-input v-model="form.remark" :rows="3" clearable maxlength="100" placeholder="请输入调价说明"
                                show-word-limit type="textarea" />
                        </el-form-item>
                        <el-form-item label="上传附件" prop="fileList">
                            <el-upload ref="businessLicensePicturtRef" class="upload-demo" multiple
                                :before-upload="(file) => beforeFile(file)" v-model:file-list="form.fileList" drag
                                :action="uploadUrl" :headers='headers' :on-preview="handlePictureCardPreview"
                                :data="{ fileType: 60, uuid: UUID, fileGroup: 'purchasePriceAdjustmentOrder', businessType: 'purchasePriceAdjustment' }"
                                :on-success="(res, file, filList) => handleUploadSuccess(res)" list-type="picture-card">
                                <el-icon>
                                    <Plus />
                                </el-icon>
                            </el-upload>

                        </el-form-item>
                    </el-form>
                    <el-drawer v-model="detailFlag" title="商品详情" direction="rtl" size="30%" v-if="detailFlag">
                        <div style="padding: 0 20px">
                            <table border="0" cellpadding="0" cellspacing="1" class="detailTable">
                                <tr>
                                    <td>供应商</td>
                                    <td>{{ shopDetail.suppier || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>单据编号</td>
                                    <td>{{ shopDetail.orderCode || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>入库日期</td>
                                    <td>{{ shopDetail.createDate ? moment(shopDetail.createDate).format('YYYY-MM-DD') :
                                        '--'
                                    }}</td>
                                </tr>
                                <tr>
                                    <td>商品名称</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.tradeName || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>自编码</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.commoditySelfCode || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>批号</td>
                                    <td>{{ shopDetail.intoNo || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>仓库</td>
                                    <td>{{ shopDetail.storages || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>货位</td>
                                    <td>{{ shopDetail.storages || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>入库数量</td>
                                    <td>{{ shopDetail.receivingQuantity || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>单价</td>
                                    <td>{{ shopDetail.unitPrice || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>可调数量</td>
                                    <td>{{ shopDetail.adjustableQuantity || '--' }}</td>
                                </tr>
                            </table>
                        </div>
                    </el-drawer>
                    <!-- <h3 class="el-dialog__title" style="margin-bottom: 10px">合计</h3>
                    <div class="total" v-if="open">
                        <p><span>调价总金额：</span><span>{{ totalCost }}</span></p>
                    </div> -->
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button type="primary" @click="prev">{{ textBtn2 }}</el-button>
                    <el-button @click="next">{{ textBtn1 }}</el-button>
                </div>
            </template>
        </el-dialog>
        <viewImg v-if="uploadVisibleFile" :visible="uploadVisibleFile" :src="uploadViewImgUrlFile"
            :beforeClose="() => uploadVisibleFile = false" />
        <el-dialog v-model="dialogVisible" title="提示" width="50%" :before-close="() => dialogVisible = false">
            <div class="dialogTitle">
                以下入库单据已提交调价申请，您确定继续对其再次进行调价吗？
            </div>
            <div v-for="item in istjData" :key="item.id" class="dialogCnt">
                <div><span>商品名称： </span> <span>{{ item.commodity.tradeName }}</span></div>
                <div><span>批号： </span> <span>{{ item.intoNo }}</span></div>
                <div><span>单据编号： </span> <span>{{ item.orderCode }}</span></div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="prevConfirm">继续</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, watch, defineExpose, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from "element-plus";
import moment from 'moment'
import Table from "@/components/assist/massRange/Table.vue";
import inboundRecordService from '@/api/erp/procure/erpPurchaseWarehousingRecordService'
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import purchasingManagement from '@/api/erp/purchasingManagement'
import purchasePriceAdjustment from '@/api/erp/purchasePriceAdjustment'
import purchaseWithdrawalReview from '@/api/erp/purchaseWithdrawalReview'
import { Plus, UploadFilled, Search, ArrowDown } from '@element-plus/icons-vue'
import { uuid } from 'vue-uuid';
import tool from '@/utils/tool';
import { backApi } from "@/api/model/salesManagement";
const tableData = []

const Tofixed_ = (value, num) => {
    if (value) {
        return parseFloat(value)?.toFixed(num)
    } else {
        return false
    }
}
const uploadUrl = '/file/uploadd'
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const { proxy } = getCurrentInstance();
const loading = ref(false);
const active = ref(1)
const textBtn1 = ref('取消')
const textBtn2 = ref('下一步')
const step1_list = ref([])
const step1_list_Total = ref(0)
const supplierList = ref([])
const choose_setp1 = ref([])
const optionList = ref([])
const uploadVisibleFile = ref(false)
const uploadViewImgUrlFile = ref(undefined)
const roleList = ref([])
const detailFlag = ref(false)
const cargoHandling = ref([])
const paymentProcessing = ref([])
const logisticsMethods = ref([])
const thirdPartyIogistics = ref([])
const reasonForReturn = ref([])
const reviewStatus = ref([])
const comeList = ref([])
const shopDetail = ref({})
const form = ref({})
const entrust_out_table_listRef = ref(null)
const UUID = ref(null)
const fileList = ref([])
const totalCost = ref(0.00)
const dialogVisible = ref(false)
const istjData = ref([])
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
        createDate: [moment().subtract(30, "days"), moment()],
        creatorId: tool.data?.get("ROLE_LIST")?.length && tool.data?.get("ROLE_LIST")?.[0]?.enName == 'ywjl' ? tool.data?.get("USER_INFO")?.id : undefined
    },

    queryParamsRules: {
        supplierId: [{ required: true, message: "供应商不能为空", trigger: "change" }],
        creatorId: [{ required: true, message: "经手人不能为空", trigger: "change" }]
    },
    creatRules: {
        remark: [{ required: true, message: "请输入调价说明", trigger: "blur" }],
        fileList: [{ required: true, message: "请上传附件", trigger: "change" }],
    }
});
const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    beforClose: {
        type: Function,
        default: () => { }
    },
    title: {
        type: String,
        default: ''
    },
    modalType: {
        type: String,
        default: ""
    },
    getList: {
        type: Function,
        default: () => { }
    },

})
const { open, beforClose, title, modalType, getList } = toRefs(props)
const { queryParams, queryParamsRules, creatRules } = toRefs(data);
const beforeFile = (file) => {
    if (file.size > 5242880) {
        ElMessage.error("文件不能大于5M");
        return false;
    }
}
const handleChangeINputNumber = (val, index, type) => {
    let price = 0
    let costNum = 0
    let num = 0
    if (choose_setp1.value[index]?.adjustPriceDTOs?.length) {
        if (type === 'num') {
            for (let item of choose_setp1.value[index]?.adjustPriceDTOs) {
                num = num + item?.adjustQuantity
            }
            if (Number(num) > Number(choose_setp1.value[index]?.adjustableQuantity)) {
                choose_setp1.value[index].adjustPriceDTOs.at(-1).adjustQuantity = 0
                return proxy.msgError('调价数量总和不能大于可调数量')
            }
        }
        choose_setp1.value[index]?.adjustPriceDTOs?.forEach(item => {
            if (item?.adjustQuantity && item?.adjustUnitPrice) {
                price = price + ((Number(item?.adjustQuantity) * Number(item?.adjustUnitPrice)) - (Number(choose_setp1.value[index]?.unitPrice) * Number(item?.adjustQuantity)))
            }
        })
        choose_setp1.value[index].adjustPrice = price.toFixed(2)
        choose_setp1.value?.forEach(item => {
            costNum = costNum + Number(item.adjustPrice)
        })
        totalCost.value = costNum.toFixed(2)
    }


}
const onchangeTable = (row) => {
    let num = comeList.value.indexOf(row.id)
    if (num === -1) {
        comeList.value.push(row.id)
    } else {
        comeList.value.splice(num, 1)
    }
}

const handleUploadSuccess = (res) => {
    fileList.value = [...fileList.value, { url: res.data?.fileUrl, name: res.data?.fileName }]
    form.value.fileList = fileList.value
}
const handlePictureCardPreview = (uploadFile) => {
    uploadViewImgUrlFile.value = uploadFile.url
    uploadVisibleFile.value = true
}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}

const detailFn = (row) => {
    shopDetail.value = row
    detailFlag.value = true
}
const newAdd = (row) => {
    if (!comeList.value.includes(row.id)) {
        comeList.value.push(row.id)
    }
    if (!row.adjustPriceDTOs?.length) {
        row.adjustPriceDTOs = []
    }
    row.adjustPriceDTOs.push({
        id: uuid.v1()
    })
}
const handleItemOrder = (index, id) => {
    choose_setp1.value[index].adjustPriceDTOs = choose_setp1.value[index].adjustPriceDTOs?.filter(item => item.id !== id)
    console.log(choose_setp1.value[index]);
}
// 获取采购入库记录
const step1_getList = async () => {
    proxy.$refs["queryParamsRef"].validate(async valid => {
        if (valid) {
            loading.value = true
            const params = { ...queryParams.value }
            params.beginInTime = params.createDate?.length ? moment(params.createDate[0]).format('YYYY-MM-DD') : undefined
            params.endInTime = params.createDate?.length ? moment(params.createDate[1]).format('YYYY-MM-DD') : undefined
            delete params.createDate
            try {
                const res = await inboundRecordService.list({ ...params, serchType: 'purchaseAdjust' })
                if (res.code == 200) {
                    step1_list.value = res.data?.records
                    step1_list_Total.value = res.data.total
                    loading.value = false
                } else {
                    loading.value = false
                }
            } catch {
                loading.value = false
            }
        }
    })

}
const handleSelectionChange_step1_list = (key) => {
    choose_setp1.value = key
}
const luoSave = (id, uid, nums) => {
    let num = nums ? nums : 1
    backApi.saveFile({
        uuid: uid,
        busId: id
    }).then((res) => {
        if (res.code == 200) {
            UUID.value = null
        } else {
            if (num <= 5) {
                num++
                setTimeout(() => {
                    luoSave(id, uid, num)
                }, 3000)
            }
        }
    })
}
const next = () => {
    if (active.value == 1) {
        close()
        return
    }
    if (active.value == 2) {
        active.value = 1
        textBtn1.value = '取消'
        textBtn2.value = '下一步'
        entrust_out_table_listRef.value?.clearSelection()
        return
    }
}
const regData = () => {
    let index = 0
    choose_setp1.value?.forEach(item => {
        if (!(item?.adjustPriceDTOs?.length)) {
            index++
        } else {
            item?.adjustPriceDTOs?.forEach(value => {
                if (!value?.adjustQuantity || !value.adjustUnitPrice) {
                    index++
                }
            })
        }
    })
    return index
}
const prevConfirm = () => {
    active.value = 2
    textBtn1.value = '上一步'
    textBtn2.value = '提交申请'
}
const prev = () => {
    if (active.value == 1) {
        if (!choose_setp1.value.length) {
            proxy.msgError('请至少选择一条商品')
            return

        } else {
            istjData.value = []
            for (var item of choose_setp1.value) {
                if (item.isPuchaseAdjust == '1') {
                    istjData.value.push(item)
                }
            }
            if (istjData.value?.length) {
                dialogVisible.value = true
            } else {
                prevConfirm()
                return
            }
        }
    }
    if (active.value == 2) {

        proxy.$refs["creatform"].validate(async valid => {
            if (valid) {
                if (regData() === choose_setp1.value?.length) {
                    loading.value = false
                    proxy.msgError('请至少填写一条调价信息！')
                    return
                } else {
                    loading.value = true
                    const params = {
                        formType: 'submit',
                        adjustDTO: {
                            supplier: {
                                id: queryParams.value?.supplierId
                            },
                            handleBy: {
                                id: queryParams.value?.creatorId
                            },
                            adjustExplain: form.value?.remark,
                            files: form.value?.fileList?.length ? JSON.stringify(form.value.fileList) : undefined,
                            adjustPrice: totalCost.value
                        },
                    }
                    let adjustFormParams = []
                    choose_setp1.value.forEach((item, index) => {
                        adjustFormParams.push({ adjustFormDTO: { purchaseInBound: { id: item.id }, commodity: { id: item?.commodity?.id }, unitPrice: item?.unitPrice, supplierId: queryParams.value?.supplierId, creatorId: queryParams.value?.creatorId, money: item?.money }, adjustPriceDTOs: [], })
                        item.adjustPriceDTOs?.forEach(value => {
                            if (value?.adjustQuantity && value?.adjustUnitPrice) {
                                adjustFormParams[index].adjustPriceDTOs?.push({ adjustQuantity: value?.adjustQuantity, adjustUnitPrice: value?.adjustUnitPrice })
                            }
                        })
                    })
                    adjustFormParams = adjustFormParams.filter(item => item?.adjustPriceDTOs?.length)
                    params.adjustFormParams = adjustFormParams
                    purchasePriceAdjustment.save(params).then(res => {
                        if (res.code === 200) {
                            loading.value = false
                            luoSave(res?.data, UUID.value, 1)
                            getList.value()
                            proxy.msgSuccess('提交成功')
                            beforClose.value()
                        } else {
                            loading.value = false
                            proxy.msgError(res.msg)
                        }
                    }).finally(() => {
                        loading.value = false
                    })
                }
            }
        })

    }
}
const close = () => {
    if (modalType.value == 'detail') {
        beforClose.value()
    } else {
        ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
            .then(() => {
                beforClose.value()
            })
            .catch(() => {

            });
    }


}
const getUsers = () => {
    warehouseNumberManagement.getUser({ size: 9999, current: 1 }).then(res => {
        if (res.code == 200) {
            optionList.value = res.data?.records
        }

    })
}
/**
 * @description: 获取供应商
 * @return {*}
 */
const getSupplier = () => {
    purchasingManagement.getSupplierProduction({ status: 3, customLabel: 2, size: 9999, current: 1 }).then(res => {
        if (res.code == 200) {
            supplierList.value = res.data?.records
        }
    })
}

async function dict() {
    cargoHandling.value = await proxy.getDictList('cargo_handling')
    paymentProcessing.value = await proxy.getDictList('payment_processing')
    logisticsMethods.value = await proxy.getDictList('logistics_methods')
    thirdPartyIogistics.value = await proxy.getDictList('third_party_iogistics')
    reasonForReturn.value = await proxy.getDictList('reason_for_return')
    reviewStatus.value = await proxy.getDictList('erp_review_status')

}
onMounted(async () => {
    UUID.value = uuid.v1()
    loading.value = true
    await dict()
    getUsers()
    await getSupplier()
    loading.value = false
    roleList.value = tool.data.get("ROLE_LIST")
})

defineExpose({

})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.rowStyle {
    .el-col {
        margin-top: 20px;
        font-size: 15px;

        .rowTitle {
            width: 120px;
            text-align: right;
            display: inline-block;
            font-size: 15px;
            font-weight: bolder;
            color: #000;
        }

        .rowMess {
            color: #4d4d4d;
            font-weight: 600;
        }

        .rowRed {
            color: red;
        }
    }
}


.box_date {
    width: 220px;
}

.step {
    margin-bottom: 30px;
}

.step1_search_btn {
    width: 60px;
    margin-left: 50px;
}

h3 {
    color: black;
}

.detailTable {
    width: 100%;
    background-color: #eaedf3;
    font-size: 14px;
    border-radius: 5px;

    tr {
        height: 40px;

        td {
            background-color: white;
        }

        td:nth-child(1) {
            padding: 0 10px;
            font-weight: bold;
            width: 20%;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            width: 80%;
            color: #606266;
            padding: 0 10px;
        }
    }

}

.messTable {
    width: 100%;
    background-color: #eaedf3;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    padding: 1px 1px 0 1px;

    tr {
        margin-bottom: 1px;
        display: flex;

        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2
        }
    }
}

.remark,
.file {
    tr {
        margin-bottom: 1px;
        display: flex;

        // border: 1px solid #eaedf3;
        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2;
            display: flex;
            align-items: center;
        }
    }
}

.remark,
.file {
    border-bottom: 1px solid #eaedf3;
    border-left: 1px solid #eaedf3;
    border-right: 1px solid #eaedf3;
}

.file {
    th:nth-child(2) {
        line-height: 25px;
        color: #2878ff !important;
        cursor: pointer;
        font-weight: 400;
    }
}

.titleH4 {
    margin-bottom: 20px;
    color: #000;
    font-weight: bolder;
    font-size: 15px;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type="number"] {
    -moz-appearance: textfield;
    /* 此处写不写都可以 */
}

::v-deep .el-upload-dragger {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
    display: contents !important;
}

.total {
    display: flex;
    margin: 10px 20px;

    p {
        margin-right: 50px;

        & span:nth-of-type(1) {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        & span:nth-of-type(2) {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}

.dialogTitle {
    text-align: center;
    padding: 20px 0;
    color: red;
    font-size: 16px;
}

.dialogCnt {
    display: flex;
    justify-content: center;
    align-items: center;

    div {
        color: #333;
        font-size: 14px;
        padding-right: 20px;
        margin-top: 20px;

        span:nth-of-type(2) {
            color: #666;

        }
    }
}
</style>
