<template>
  <el-container v-loading="listLoading">
    <el-header>
      <div class="left-panel">
        <el-button icon="el-icon-plus" type="primary" @click="addForm(null)"></el-button>
        <el-button :disabled="selection.length == 0" icon="el-icon-delete" plain type="danger"
          @click="deleteDatas"></el-button>
        <el-tooltip class="item" content="同步组织机构数据" effect="dark" placement="top">
          <el-button icon="el-icon-download" type="primary" @click="userSync"></el-button>
        </el-tooltip>
      </div>
      <div class="right-panel">
        <div class="right-panel-search">
          <el-input v-model="searchForm.name" clearable placeholder="输入机构名称"></el-input>
          <el-button icon="el-icon-search" type="primary" @click="search"></el-button>
        </div>
      </div>
    </el-header>
    <el-main class="nopadding">
      <ytzhTable ref="dataTable" :data="dataList" :pageChangeHandle="getDataList" :refreshDataListHandle="getDataList"
        :tablePage="tablePage" :treeLoadHandle="treeLoadHandle" row-key="id" stripe @selection-change="selectionChange">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column label="机构名称" prop="name"></el-table-column>
        <el-table-column label="机构类型" prop="type"
        :formatter="(row) => (row.type == '1' || row.type == '2') ? row.type == '1' ? '承运商' : row.type == '2' ? '货主' : '--' : row.type" ></el-table-column>
        <el-table-column label="联系人" prop="contact"></el-table-column>
        <el-table-column label="联系电话" prop="phone"></el-table-column>
        <el-table-column label="是否启用" prop="enable" width="150">
          <template #default="scope">
            <el-tag v-if="scope.row.enable == true" type="success">启用</el-tag>
            <el-tag v-if="scope.row.enable == false">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="right" fixed="right" label="操作" width="300">
          <template #default="scope">
            <el-button plain size="small" @click="addForm(scope.row)">添加子级</el-button>
            <el-button plain size="small" @click="viewForm(scope.row)">查看</el-button>
            <el-button plain size="small" type="primary" @click="editForm(scope.row)">编辑
            </el-button>
            <el-popconfirm title="确定删除吗？" @confirm="deleteData(scope.row)">
              <template #reference>
                <el-button plain size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ytzhTable>
    </el-main>
  </el-container>

  <form-dialog v-if="dialog.form" ref="formDialog" :callback="formCallback" @closed="dialog.form = false"
    @success="handleSaveSuccess"></form-dialog>
</template>

<script>
import formDialog from "./form";

export default {
  name: "listCrud",
  components: {
    formDialog,
  },
  data() {
    return {
      //数据列表
      dataList: {},
      //分页参数
      tablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
      },
      //查询表单
      searchForm: {},
      //数据列选中行
      selection: [],
      //机构类型列表
      costTypeList: [],
      //列表加载
      listLoading: false,
      //弹框控制
      dialog: {
        //数据表单显示隐藏
        form: false,
      },
      sysOrgSelectDialog: false,
    };
  },
  mounted() {
    //刷新数据列表
    this.getDataList();
  },
  methods: {
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      //初始化数据列表
      this.dataList = null;
      //请求接口
      var res = await this.reqeustList();
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        //数据列表
        this.dataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
    },
    /*
     * 请求接口列表
     * @author: 路正宁
     * @date: 2023-03-30 11:20:01
     */
    async reqeustList(searchForm = {}, pageSize = this.tablePage.pageSize) {
      //页面加载
      this.listLoading = true;
      var res = await this.$API.sysOrgService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: pageSize,
        //排序查询
        orders: this.tablePage.orders,
        //查询参数
        ...searchForm,
        name:this.searchForm.name
      });
      this.listLoading = false;
      return res;
    },
    /*
     * 添加数据
     * @author: 路正宁
     * @date: 2023-03-24 14:45:27
     */
    addForm(parent) {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.addView(parent);
      });
    },
    /*
     * 编辑数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:41
     */
    editForm(row) {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.editView(row);
      });
    },
    /*
     * 查看数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:55
     */
    viewForm(row) {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.view(row);
      });
    },
    /*
     * 删除数据，行内删除
     * @author: 路正宁
     * @date: 2023-03-24 14:35:00
     */
    async deleteData(row) {
      this.listLoading = true;
      var res = await this.$API.sysOrgService.delete(row.id);
      if (res.code == 200) {
        this.removeNode(this.dataList, row.id);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      this.listLoading = false;
    },
    async userSync() {
      this.listLoading = true;
      var res = await this.$API.sysUserService.userSync();
      if (res.code == 200) {
        this.$message.success("同步成功");
        this.getDataList();
      }
      this.listLoading = false;
    },
    /*
     * 批量删除
     * @author: 路正宁
     * @date: 2023-03-31 09:42:13
     */
    async deleteDatas() {
      //确认删除弹框
      var confirmRes = await this.$confirm(
        `确定删除选中的 ${this.selection.length} 项吗？`,
        "提示",
        {
          type: "warning",
          confirmButtonText: "删除",
          confirmButtonClass: "el-button--danger",
        }
      ).catch(() => {
      });
      //确认结果处理
      if (!confirmRes) {
        return false;
      }
      //父级数据不能先删除
      for (var i = 0; i < this.selection.length; i++) {
        if (this.$ObjectUtils.isNotEmpty(this.selection[i].children)) {
          this.$message.warning("请先删除[" + this.selection[i].name + "]下的子级数据");
          return;
        }
      }
      //要删除的id数组
      var ids = this.selection.map((v) => v.id);
      //拼接的数组字符串，接口传参
      var idStr = ids.join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysOrgService.delete(idStr);
      if (res.code == 200) {
        //从列表中移除已删除的数据
        for (var j = 0; j < ids.length; j++) {
          this.removeNode(this.dataList, ids[j]);
        }
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      //释放页面加载中
      this.listLoading = false;
    },
    /*
     * 表格选择后回调事件
     * @author: 路正宁
     * @date: 2023-03-24 14:51:09
     */
    selectionChange(selection) {
      this.selection = selection;
    },
    /*
     * 数据表单回调函数，表单提交成功以后会调用此方法
     * 为了减少网络请求，直接变更表格内存数据
     * @author: 路正宁
     * @date: 2023-03-24 14:57:49
     */
    formCallback(data, mode) {
      if (mode == "add") {
        this.insertNode(this.dataList, data.parent.id, data);
      } else if (mode == "edit") {
        this.updateNode(this.dataList, data.id, data);
      }
    },
    /*
     * 头部搜索框
     * @author: 路正宁
     * @date: 2023-03-24 14:58:47
     */
    search() {
      this.getDataList();
    },
    /*
     * 树表格点击展开
     * @author: 路正宁
     * @date: 2023-03-30 11:16:33
     */
    async treeLoadHandle(tree, treeNode, resolve) {
      var form = { "parent.id": tree.id };
      //请求接口
      var res = await this.reqeustList(form, 100);
      if (res.code == 200) {
        if (res.data.records == null || res.data.records.length == 0) {
          this.$message.warning("无数据");
          return;
        }
      } else {
        this.$Response.errorNotice(res, "查询失败");
        return;
      }
      resolve(res.data.records);
      //同步本地
      tree.children = res.data.records;
      //treeNode.children = res.data.records;
    },

    /*
     * 动态删除指定节点
     * @author: 路正宁
     * @date: 2023-03-30 15:19:50
     */
    removeNode(list, removeId) {
      if (this.$ObjectUtils.isEmpty(list)) {
        return false;
      }
      for (var i = 0; i < list.length; i++) {
        if (list[i].id == removeId) {
          list.splice(i, 1);
          return true;
        }
        if (list[i].children != null && list[i].children.length > 0) {
          var res = this.removeNode(list[i].children, removeId);
          if (res == true) {
            return true;
          }
        }
      }
      return false;
    },
    /*
     * 动态添加节点
     * @author: 路正宁
     * @date: 2023-03-30 16:42:37
     */
    insertNode(list, parentId, addNode) {
      if (this.$ObjectUtils.isEmpty(list)) {
        list.push(addNode);
        return;
      }
      for (var i = 0; i < list.length; i++) {
        if (list[i].parent.id == parentId) {
          list.push(addNode);
          return true;
        }
        if (list[i].children != null && list[i].children.length > 0) {
          var res = this.insertNode(list[i].children, parentId, addNode);
          if (res == true) {
            return true;
          }
        }
      }
      return false;
    },
    /*
     * 更新数据节点
     * @author: 路正宁
     * @date: 2023-04-03 11:30:33
     */
    updateNode(list, id, addNode) {
      if (this.$ObjectUtils.isEmpty(list)) {
        return false;
      }
      for (var i = 0; i < list.length; i++) {
        if (list[i].id == id) {
          list[i] = addNode;
          return true;
        }
        if (list[i].children != null && list[i].children.length > 0) {
          var res = this.updateNode(list[i].children, id, addNode);
          if (res == true) {
            return true;
          }
        }
      }
      return false;
    },

  },
};
</script>

<style></style>
