<template>
	<h4 class="titleH4" style="margin-left: 35px;font-size: 13px">
		申请编号: <span style="font-weight: 400;color: #505050">{{ props.tableStr.salesRetreat?.applyNo }}</span>&emsp;
		申请日期: <span style="font-weight: 400;color: #505050">{{
			functionIndex.transformTimestamp(props.tableStr.salesRetreat?.applyDate)
		}}</span>&emsp;
		申请人: <span style="font-weight: 400;color: #505050">{{ props.tableStr.salesRetreat?.applyBy.name }}</span>&emsp;
	</h4>
	<h3 class="el-dialog__title" style="margin-bottom: 10px">退货明细</h3>
	<el-table :data="props.tableStr.salesRetreatForms" style="width: 100%">
    <el-table-column :show-overflow-tooltip="true" fixed label="单据编号" prop="docNum" width="180"/>
    <el-table-column :show-overflow-tooltip="true" label="商品名称" prop="commodityName" width="150"/>
    <el-table-column :show-overflow-tooltip="true" label="自编码" prop="commoditySelfCode" width="100"/>
    <el-table-column :show-overflow-tooltip="true" label="规格" prop="commodityPackageSpecification" width="90"/>
    <el-table-column :show-overflow-tooltip="true" label="生产厂家" prop="commodityManufactureName" width="150"/>
    <el-table-column :show-overflow-tooltip="true" label="产地" prop="commodityOriginPlace" width="150"/>
    <el-table-column :show-overflow-tooltip="true" label="剂型" prop="commodityDosageForm" width="100"/>
    <el-table-column :show-overflow-tooltip="true" label="基本单位" prop="commodityBasicUnit"/>
    <el-table-column :show-overflow-tooltip="true" label="库存余量" prop="currentInventory"/>
    <el-table-column :show-overflow-tooltip="true" label="可退数量" prop="returnableQuantity"/>
    <el-table-column :show-overflow-tooltip="true" label="销售单价" prop="commodityUnitPrice"/>
    <el-table-column :show-overflow-tooltip="true" label="批号" prop="batchNumber" width="150"/>
		<el-table-column :show-overflow-tooltip="true" label="生产日期" prop="commodityProduceDate" width="120">
			<template #default="scope">
				{{ functionIndex.transformTimestamp(scope.row.commodityProduceDate) }}
			</template>
		</el-table-column>
    <el-table-column :show-overflow-tooltip="true" label="有效期" prop="commodityvalidityTime"/>
    <el-table-column :show-overflow-tooltip="true" label="经手人" prop="salesHandleBy.name"/>
    <el-table-column :show-overflow-tooltip="true" align="center" fixed="right" label="申退数量"
                     prop="retreatQuantity"/>
    <el-table-column :show-overflow-tooltip="true" align="center" fixed="right" label="申退金额" prop="refundAmount">
			<template #default="scope">
				{{ scope.row.refundAmount?.toFixed(2) }}
			</template>
		</el-table-column>
	</el-table>
	<h3 class="el-dialog__title" style="margin: 10px 0">退货信息</h3>
	<table border="0" cellpadding="0" cellspacing="1" class="messTable">
		<tr>
			<td>退货原因</td>
			<td>{{
          props.tableStr.salesRetreatReason ? echoReason(props.tableStr.salesRetreatReason.returnReason) : ''
        }}
			</td>
		</tr>
		<tr>
			<td>有无实货</td>
			<td>
				{{
					props.tableStr.salesRetreatReason ? props.tableStr.salesRetreatReason.hasCommodity == 1 ? '有' : '无' : ""
				}}
			</td>
		</tr>
		<tr>
			<td>责任人</td>
			<td>{{ props.tableStr.salesRetreatReason ? props.tableStr.salesRetreatReason.responsPerson : "" }}</td>
		</tr>
		<tr>
			<td>扣款金额</td>
			<td>{{
          props.tableStr.salesRetreatReason ? props.tableStr.salesRetreatReason.deductionAmount.toFixed(2) : ""
        }}
			</td>
		</tr>
		<tr>
			<td>附件</td>
			<td>
				<div v-if="props.tableStr.salesRetreat.files">
          <p v-for="(item, index) in  JSON.parse(props.tableStr.salesRetreat.files)" :key="index"
             :style="JSON.parse(props.tableStr.salesRetreat.files).length == 1 ? 'color:#2a75f6;cursor: pointer;' : 'color:#2a75f6;cursor: pointer;line-height: 25px'"
             @click="checkImg(item)">
						{{ item.name }}</p>
				</div>
			</td>
		</tr>
		<tr>
			<td>备注</td>
			<td>{{ props.tableStr.salesRetreatReason ? props.tableStr.salesRetreatReason.remark : "" }}</td>
		</tr>
		<tr>
			<td>合计申退数量</td>
			<td>{{ props.tableStr.salesRetreatReason ? props.tableStr.salesRetreat.retreatQuantity : "" }}</td>
		</tr>
		<tr>
			<td>合计申退金额</td>
			<td>{{ props.tableStr.salesRetreatReason ? props.tableStr.salesRetreat.retreatAmount.toFixed(2) : "" }}</td>
		</tr>
	</table>
	<h3 v-if="!props.flagOut" class="el-dialog__title" style="margin: 10px 0">销退入库记录</h3>
	<el-table v-if="!props.flagOut" :data="props.inTable" style="width: 100%">
    <el-table-column label=自编码 prop="commodity.commoditySelfCode"/>
    <el-table-column label="商品名称" prop="commodityTradeName"/>
    <el-table-column label="批号" prop="batchNumber"/>
    <el-table-column label="入库时间" prop="inTime"/>
    <el-table-column label="入库数量" prop="inQuantity"/>
    <el-table-column label="入库状态" prop="salesRetreatForm.inboundStatus"/>
	</el-table>
  <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
import {functionIndex} from '../functionIndex'
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const {proxy} = getCurrentInstance();
const childRef = ref(null)
const data = reactive({
	test: "",
	tableStr: [],
	reasonType: [],
	inTable: [],
	checkFlag: false,
	imgUrl: []
})
const echoReason = (str) => {
	const newStr = data.reasonType.find(item => item.value == str)
	return newStr.name
}
const checkImg = (item) => {
	data.imgUrl = []
	data.imgUrl.push(item.url)
	data.checkFlag = true;
}
const close = () => {
	data.checkFlag = false;
	data.imgUrl = []
}
const emit = defineEmits([])
const props = defineProps({
	tableStr: {
		default: []
	},
	inTable: {
		default: []
	},
	flagOut: {
		default: false
	}
})

onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	data.reasonType = await proxy.getDictList("reason_for_order");
})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({})

</script>
<style lang='scss' scoped>
.messTable {
	width: 100%;
	background-color: #eaedf3;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	grid-template-rows: 1fr 1fr;
	padding: 1px 1px 0 1px;

	tr {
		margin-bottom: 1px;
		display: flex;

		td {
			background-color: white;
			line-height: 40px;
		}

		td:nth-child(1) {
			flex: 1;
			padding: 0 10px;
			font-weight: bold;
			color: #505050;
			background: #f7f7f7;
		}

		td:nth-child(2) {
			color: #606266;
			padding: 0 10px;
			flex: 2
		}
	}
}

h3 {
	color: black;
}

.titleH4 {
	margin-bottom: 20px;
	color: #000;
	font-weight: bolder;
	font-size: 15px;
}
</style>
