/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-11 10:08:03
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-03 11:45:01
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\manufacturerManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
  getList: function (inputForm) {
    return http.get('/erp/procure/erpPurchaseOrder/listCalculate',inputForm)
  },

  delete: function (ids) {
    return http.delete('/erp/procure/erpPurchaseOrder/delete',ids)
  },
  deleteDetail: function (ids) {
    return http.delete('/erp/procure/erpPurchaseOrderForm/delete',ids)
  },
  getIdOrder: function (inputForm) {
    return http.get('/erp/procure/erpPurchaseOrder/findPurchaseOrderById',inputForm)
  },
  
  save: function (data) {
    return http.post('/erp/procure/erpPurchaseOrder/save',data)
  },
  shopList:function (params) {
    return http.get( `/erp/product/erpCommodity/listByDelegateForCG`,params)
  },
  // 根据商品信息 获取细单信息
  getShopDetail:function (params) {
    return http.get( `/erp/product/erpCommodity/listByIdAndHandledBy`,params)
  },
   // 获取供应商
   getSupplierProduction:function (params) {
    return http.get( `/erp/supplier/erpSupplierProduction/list`,params)
  },
  // 获取委托人
  getSupplierProductionDelegate:function (params) {
    return http.get( `/erp/supplier/erpSupplierProductionDelegate/list`,params)
  },
  // 审核记录
  erpPurchaseOrderApproval:function (params) {
    return http.get( `/erp/procure/erpPurchaseOrderApproval/list`,params)
  },
  // 获取入库记录
  erpPurchaseWarehousingRecord:function (params) {
    return http.get( `/erp/procure/erpPurchaseWarehousingRecord/list`,params)
  },
  // 采购草稿批量提交
  submitAuditMore:function (params) {
    return http.get( `/erp/procure/erpPurchaseOrder/submitAudit`,params)
  },
 // 再来一单
 fastCopy:function (params) {
  return http.get( `/erp/procure/erpPurchaseOrder/fastCopy`,params)
},
  
}
