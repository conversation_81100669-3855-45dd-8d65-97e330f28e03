<template>
    <div>
        <!-- 查询表头 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item label="任务名称" prop="alarmName">
                    <el-input v-model="queryParams.alarmName" placeholder="请输入任务名称" clearable class="form_225" />
                </el-form-item>
                <el-form-item label="状态" prop="status" clearable>
                    <el-select v-model="queryParams.status" placeholder="请选择状态">
                        <el-option :label="item.name" :value="item.value" v-for=" item in statusList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="任务类型" prop="taskType" clearable>
                    <el-select v-model="queryParams.taskType" placeholder="请选择任务类型">
                        <el-option :label="item.name" :value="item.value" v-for=" item in taskTypeList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchQuery">搜索</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表单区域 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(creatForm)" class="creatSpan">新增</el-button>
            <el-table v-loading="loading" :data="configeList" @selection-change="handleSelectionChange" class="el-table"
                border>
                <!-- <el-table-column label="ID" align="left" prop="id"/> -->
                <el-table-column label="ID" align="left" prop="id">
                    <template #default="scope">
                        {{ scope.row.id }}
                    </template>
                </el-table-column>
                <el-table-column label="任务名称" align="left" prop="alarmName">
                </el-table-column>
                <el-table-column label="任务类型" align="left" prop="taskType" 
                 :formatter="(row) => formDict(taskTypeList, row.taskType)">
                </el-table-column>
                <el-table-column label="状态" align="left" prop="status">
                    <template #default="scope">
                        {{ scope.row.status == '1' ? '生效中' : '已禁用' }}
                    </template>
                </el-table-column>
                <el-table-column label="创建日期" align="left" prop="createDate"
                    :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')">
                </el-table-column>
                <el-table-column label="创建人" align="left">
                    <template #default="scope">
                        {{ createName }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="450">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row, editForm)"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                        <el-button link @click="handleDisabled(scope.row)" v-if="scope.row.status == '1'"
                            style="color:#909399"><img src="@/assets/icons/disabled.png"
                                style="margin-right:5px" />禁用</el-button>
                        <el-button link @click="handleDisabled(scope.row)" v-if="scope.row.status == '2'"
                            style="color:#909399"><img src="@/assets/icons/disabled.png"
                                style="margin-right:5px" />启用</el-button>

                        <el-button link type="primary" @click="handleCopy(scope.row)"><img src="@/assets/icons/detail.png"
                                style="margin-right:5px" />复制</el-button>
                        <el-button link type="danger" @click="handlerLog(scope.row)" style="color:#67c23a"><img
                                src="@/assets/icons/review.png" style="margin-right:5px;" />操作日志</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增弹框 -->
        <el-dialog v-model="dialogFormVisible" title="创建任务" :before-close="() => handlerClose()">
            <el-form :model="dialogform" label-width="80px" :rules="rules" ref="creatForm">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务名称" prop="alarmName">
                            <el-input v-model="dialogform.alarmName" autocomplete="off" placeholder="请输入任务名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="有效期" prop="createDate">
                            <el-date-picker v-model="dialogform.createDate" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务类型" prop="taskType">
                            <el-select v-model="dialogform.taskType" placeholder="请选择任务类型" style="width:100%;"
                                @change="(v) => taskChange(v)">
                                <el-option :label="item.name" :value="item.value" v-for=" item in tasktypeList"
                                    :key="item.id" @click.native="handlerId2(item)" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="执行时间" prop="executeTime">
                            <el-time-picker v-model="dialogform.executeTime" placeholder="请选择执行时间" />
                            <!-- <el-time-picker v-model="value1" placeholder="Arbitrary time" /> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="周期" prop="weekTerm">
                            <el-select v-model="dialogform.weekTerm" placeholder="请选择周期" style="width:100%;">
                                <el-option :label="item.name" :value="item.value" v-for=" item  in  cycleList"
                                    :key="item.id" @click="handlerCycle(item)" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="星期" prop="week" v-if="weekFlag">
                            <el-select v-model="dialogform.week" placeholder="请选择周" style="width:100%;">
                                <el-option :label="item.label" :value="item.value" v-for=" item  in  cycleOptions"
                                    :key="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="日期" prop="mounth" v-if="mounthFlag">
                            <el-select v-model="dialogform.mounth" placeholder="请选择日" style="width:100%;">
                                <el-option :label="item.label" :value="item.value" v-for=" item  in  mounthOptions"
                                    :key="item.value" />
                            </el-select>
                        </el-form-item>

                    </el-col>
                </el-row>
                <!-- (a>b?(a>c?a:c):(b>c?b:c)) -->
                <!-- :note2="item.status == 0 ? '备料中' : item.status == 1 ? '部分入库' : '全部入库'"  -->
                <el-form-item label="监控字段" prop="controlValue" class="controlValue">
                    <div v-for='(item, index) in itemcount' :key="index">
                        <el-cascader v-model="dialogform.controlValue[index]"
                            :options="taskFlag == '1' ? options : taskFlag == '2' ? options2 : options3" />
                        <el-button type="danger" style="margin:7px 0px 0px 20px;"
                            @click="deleteControl(index)">删除</el-button>
                        <el-button type="primary" style="margin:7px 0px 0px 20px;" @click="addControl">增加</el-button>
                        <span v-if="errors[index]" class="error">{{ errors[index] }}</span>
                    </div>
                </el-form-item>
                <!-- 监控规则 -->
                <el-form-item label="监控规则" prop="isStop" class="ruler-form">
                    <el-form class="rulerForm" v-for='(item, index) in itemcount2' :key="index" v-model="rulerValue[index]"
                        ref="rulerformRef">
                        <div class="rulerForm">
                            <div class="rulerDiv" v-if="rulerHidden">
                                <el-form-item>
                                    <p>条件{{ index + 1 }}</p>
                                </el-form-item>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="符号" prop="moniterName" class="rules" :rules="[
                                            {
                                                required: true,
                                                trigger: 'blur',
                                            },
                                        ]">
                                            <el-select v-model="rulerValue[index].moniterName" placeholder="请选择符号"
                                                style="margin-top: 13px;">
                                                <el-option :label="options.name" :value="options.value"
                                                    v-for=" options  in  supervisoryList" :key="options.id" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-form-item prop="currentTime" label-width="80px" v-if="quantityHidden">
                                            <el-radio-group v-model="rulerValue[index].currentTime" class="ml-4">
                                                <el-radio label="1" size="large" @change="handlerDisabled(index)">当前日期+
                                                    <el-input-number v-model="rulerValue[index].currentTimeNow"
                                                        @change="handleChange" style="width:53%;" precision="0"
                                                        :disabled="item.time == 'true'" @focus="handleFocus(index)" />
                                                    天
                                                </el-radio>
                                                <el-radio label="2" size="large" @change="handlerDisabled2(index)">指定日期
                                                    <el-date-picker v-model="rulerValue[index].sureTime" size="default"
                                                        type="date" placeholder="请选择指定日期"
                                                        style="margin-left:10px;width:53%;" value-format="YYYY-MM-DD"
                                                        :disabled="item.appoint == 'true'" @focus="handleFocus2(index)" />
                                                </el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                        <el-form-item label="监控数量" label-width="100px" v-else>
                                            <el-input v-model="rulerValue[index].moniterCount" placeholder="请输入监控数量" clearable
                                                @change="" style="margin-top:12px;width:71%;"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="操作" prop="operation" :rules="[
                                            {
                                                required: true,
                                                trigger: 'blur',
                                            },
                                        ]">
                                            <div style="display: flex; flex-direction: column; margin-top:18px;">
                                                <el-checkbox v-model="rulerValue[index].noticeCenter" label="消息中心提醒"
                                                    size="large" :true-label="2" :false-label="1" @change="handlerUser" />
                                                <el-checkbox v-model="rulerValue[index].alarmTask" label="生成异常任务"
                                                    size="large" :true-label="2" :false-label="1" />
                                                <el-checkbox v-model="rulerValue[index].statusAble" label="状态禁用"
                                                    size="large" :true-label="2" :false-label="1" v-if="statusHidden"/>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-form-item label="接收人" prop="receiverBy"
                                            v-if="rulerValue[index].noticeCenter === 2" label-width='100px'>
                                            <el-select v-model="rulerValue[index].receiverBy" placeholder="请选择接收人"
                                                style="margin-top: 13px;width:71%" multiple>
                                                <el-input v-model="rulerValue[index].receiver" clearable
                                                    placeholder="请输入接收人" @keydown.enter="handlerUser2(index)"
                                                    @clear="handlerUser" style="width:90%;margin-left:10px;">
                                                    <template v-slot:suffix>
                                                        <el-icon @click="handlerUser2(index)">
                                                            <Search />
                                                        </el-icon>
                                                    </template>
                                                </el-input>
                                                <el-option :label="options.name" :value="options.id"
                                                    v-for=" options  in  receiverList" :key="options.id" />
                                                <el-pagination v-model:current-page="current2" v-model:page-size="size2"
                                                    :total="total2" layout="->,total, prev, pager, next, jumper"
                                                    @current-change="handlerUser" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="提醒内容" prop="tipContent"
                                            v-if="rulerValue[index].noticeCenter === 2" label-width='100px' class="">
                                            <el-input v-model="rulerValue[index].tipContent" placeholder="请输入提醒内容" clearable
                                                style="margin-top: 13px;width:71%" maxlength="50"></el-input>
                                        </el-form-item>

                                    </el-col>
                                </el-row>
                            </div>
                            <div class="rulerDiv2" v-else>
                                <p class="hiddenP">请选择任务类型</p>
                            </div>
                            <el-button type="primary" @click="addDiv" class="bottomadd" v-if="rulerHidden">增加</el-button>
                            <el-button type="danger" class="bottomadd" @click="deleteRuler(item, index)"
                                v-if="rulerHidden">删除</el-button>
                        </div>
                    </el-form>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="saveAlarm(creatForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 编辑弹框 -->
        <el-dialog v-model="editdialogFormVisible" :title="type == 'copy' ? '创建任务' : '修改任务'"
            :before-close="() => handlerClose()">
            <el-form :model="editdialogform" label-width="80px" :rules="editRules" ref="editForm">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务名称" prop="alarmName">
                            <el-input v-model="editdialogform.alarmName" autocomplete="off" placeholder="请输入任务名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="有效期" prop="createDate">
                            <el-date-picker v-model="editdialogform.createDate" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="任务类型" prop="taskType">
                            <el-select v-model="editdialogform.taskType" placeholder="请选择任务类型"    @change="(v) => taskChange2(v)">
                                <el-option :label="item.name" :value="item.value" v-for=" item in tasktypeList"
                                    :key="item.id" @click.native="handlerId2(item)" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="执行时间" prop="executeTime">
                            <el-time-picker v-model="editdialogform.executeTime" placeholder="请选择执行时间"
                                value-format="HH:mm:ss" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="周期" prop="weekTerm">
                            <el-select v-model="editdialogform.weekTerm" placeholder="请选择周期">
                                <el-option :label="item.name" :value="item.value" v-for=" item  in  cycleList"
                                    :key="item.id" @click="handlerCycle(item)" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="星期" prop="week" v-if="weekFlag">
                            <el-select v-model="editdialogform.week" placeholder="请选择周">
                                <el-option :label="item.label" :value="item.value" v-for=" item  in  cycleOptions"
                                    :key="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="日期" prop="mounth" v-if="mounthFlag">
                            <el-select v-model="editdialogform.mounth" placeholder="请选择日">
                                <el-option :label="item.label" :value="item.value" v-for=" item  in  mounthOptions"
                                    :key="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="监控字段" prop="controlValue2" class="controlValue">
                    <div v-for='(item, index) in edititemcount' :key="index">
                        <el-cascader v-model="editdialogform.controlValue2[index]" :options="taskFlag == '1' ? options : taskFlag == '2' ? options2 : options3" 
                            :props="regionProps" />
                        <el-button type="danger" style="margin:7px 0px 0px 20px;"
                            @click="editdeleteControl(index)">删除</el-button>
                        <el-button type="primary" style="margin:7px 0px 0px 20px;" @click="addControl2">增加</el-button>
                    </div>
                </el-form-item>
                <!-- 编辑监控规则 -->
                <el-form-item label="监控规则" prop="isStop" class="ruler-form">
                    <el-form class="rulerForm" v-model="editRulerValue" v-for='(item, index) in edititemcount2'
                        :key="index">
                        <div class="rulerForm">
                            <div class="rulerDiv">
                                <el-form-item>
                                    <p>条件{{ index + 1 }}</p>
                                </el-form-item>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="符号" prop="moniterName" :rules="[
                                            {
                                                required: true,
                                                trigger: 'blur',
                                            },
                                        ]">
                                            <el-select v-model="editRulerValue[index].moniterName" placeholder="请选择符号"
                                                style="margin-top: 13px;">
                                                <el-option :label="options.name" :value="options.value"
                                                    v-for=" options  in  supervisoryList" :key="options.id" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-form-item prop="dateOption" label-width="80px" v-if = "dateValue">
                                            <el-radio-group v-model="editRulerValue[index].dateOption" class="ml-4">
                                                <el-radio label="1" size="large" @change="handlerDisabled3(index)">当前日期+
                                                    <el-input-number v-model="editRulerValue[index].currentTimeNow"
                                                        :disabled="item.time == 'true'" precision="0" @change="handleChange"
                                                        style="width:50%;" />
                                                    天
                                                </el-radio>
                                                <el-radio label="2" size="large" @change="handlerDisabled4(index)">指定日期
                                                    <el-date-picker v-model="editRulerValue[index].sureTime" size="default"
                                                        type="date" placeholder="请选择指定日期" :disabled="item.appoint == 'true'"
                                                        style="margin-left:10px;width:53%;" value-format="YYYY-MM-DD" />
                                                </el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                        <el-form-item label="监控数量" label-width="100px" v-else>
                                            <el-input v-model="editRulerValue[index].moniterCount" placeholder="请输入监控数量" clearable
                                                @change="" style="margin-top:12px;width:71%;"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item label="操作" prop="operation" :rules="[
                                            {
                                                required: true,
                                                trigger: 'blur',
                                            },
                                        ]">
                                            <div style="display: flex; flex-direction: column; margin-top:18px;">
                                                <el-checkbox :true-label="2" :false-label="1"
                                                    v-model="editRulerValue[index].noticeCenter" label="消息中心提醒" size="large"
                                                    @change="handlerUser" />
                                                <el-checkbox v-model="editRulerValue[index].alarmTask" label="生成异常任务"
                                                    size="large" :true-label="2" :false-label="1" />
                                                <el-checkbox v-model="editRulerValue[index].statusAble" label="状态禁用"
                                                    size="large" :true-label="2" :false-label="1" v-if="statusable"/>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-form-item label="接收人" prop="receiverBy" label-width='100px'
                                            v-if="editRulerValue[index].noticeCenter === 2" :rules="[
                                                {
                                                    required: true,
                                                    trigger: 'blur',
                                                },
                                            ]" >
                                            <el-select v-model="editRulerValue[index].receiverBy" placeholder="请选择接收人"
                                                style="margin-top: 13px;width:71%" multiple>
                                                <el-input v-model="editRulerValue[index].receiver" clearable
                                                    @clear="handlerUser" style="width:90%;margin-left:10px;"
                                                    placeholder="请输入接收人" @keydown.enter="handlerUser2(index)">
                                                    <template v-slot:suffix>
                                                        <el-icon @click="handlerUser2(index)">
                                                            <Search />
                                                        </el-icon>
                                                    </template>
                                                </el-input>
                                                <el-option :label="options.name" :value="options.id"
                                                    v-for=" options  in  receiverList" :key="options.id" />
                                                <el-pagination v-model:current-page="current2" v-model:page-size="size2"
                                                    :total="total2" layout="->,total, prev, pager, next, jumper"
                                                    @current-change="handlerUser" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="提醒内容" prop="tipContent"
                                            v-if="editRulerValue[index].tipContent? true : false" label-width='100px' class="">
                                            <el-input v-model="editRulerValue[index].tipContent" placeholder="请输入提醒内容" clearable
                                                style="margin-top: 13px;width:71%" maxlength="50"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </div>
                            <el-button type="primary" @click="addDiv2" class="bottomadd">增加</el-button>
                            <el-button type="danger" class="bottomadd" @click="deleteRuler2(item, index)">删除</el-button>
                        </div>
                    </el-form>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="editAlarm(editForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed } from 'vue';
import alarm from "@/api/erp/alarm/alarm";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import moment from 'moment';
import logList from './logList.vue'
const { proxy } = getCurrentInstance();
const statusList = ref([])
const reviewRow = ref({})
const errors = ref([])
// const dateOption = ref('1')
const rulerformRef = ref()
const supervisoryList = ref([])
const reviewVisible = ref(false)
const statusHidden = ref(false)
const statusable = ref(false)
const deleteRulerid = ref([])
const taskTypeList = ref([])
const receiverFlag = ref(false)
const receiverFlag2 = ref(false)
const dateValue = ref(false)
const showReceiver = ref(false)
const rulerHidden = ref(false)
const current = ref(false)
const quantityHidden = ref(true)
const total2 = ref(0)
const current2 = ref(1)
const size2 = ref(10)
const appoint = ref(false)
const createName = ref('')
const type = ref('')
const tasktypeList = ref([])
const cycleList = ref([])
const queryForm = ref()
const creatForm = ref()
const editForm = ref()
const total = ref(0)
const regionProps = ref({
    value: 'value',
    label: 'label',
})
const queryParams = ref({
    current: 1,
    size: 10,
})
const configeList = ref([])
const dialogform = ref({})
const editdialogform = ref({})
const weekFlag = ref(false)
const editdialogFormVisible = ref(false)
const mounthFlag = ref(false)
const itemcount = ref(1)
const edititemcount = ref(1)
const rules = reactive({
    alarmName: [{ required: true, message: '请输入任务名称', trigger: 'blur' },],
    createDate: [{ required: true, message: '请选择有效期', trigger: 'blur' },],
    taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' },],
    executeTime: [{ required: true, message: '请选择执行时间', trigger: 'blur' },],
    weekTerm: [{ required: true, message: '请选择周期', trigger: 'blur' },],
    week: [{ required: true, message: '请选择星期', trigger: 'blur' },],
    mounth: [{ required: true, message: '请选择日', trigger: 'blur' },],
    controlValue: [{ required: true, message: '请选择监控字段', trigger: 'blur' },],
    receiverBy: [{ required: true, message: '请选择接收人', trigger: 'blur' },],
    // isStop: [{required: true, message: '请填写监控规则', trigger: 'blur' },],                                                                   
})
const editRules = reactive({
    alarmName: [{ required: true, message: '请输入任务名称', trigger: 'blur' },],
    createDate: [{ required: true, message: '请选择有效期', trigger: 'blur' },],
    taskType: [{ required: true, message: '请选择任务类型', trigger: 'blur' },],
    executeTime: [{ required: true, message: '请选择执行时间', trigger: 'blur' },],
    weekTerm: [{ required: true, message: '请选择周期', trigger: 'blur' },],
    week: [{ required: true, message: '请选择星期', trigger: 'blur' },],
    mounth: [{ required: true, message: '请选择日', trigger: 'blur' },],
    controlValue2: [{ required: true, message: '请选择监控字段', trigger: 'blur' },],
})
// const itemcount2 = ref(1)
const itemcount2 = ref([{
    moniterName: '',
    dateOption: '1',
    num: '',
    sureTime: '',
    noticeCenter: '',
    alarmTask: '',
    statusAble: '',
    appoint: 'true',
    time: 'true'
}])
const edititemcount2 = ref([{
    moniterName: '',
    dateOption: "1",
    currentTimeNow: '',
    sureTime: '',
    noticeCenter: '',
    alarmTask: '',
    statusAble: '',
    appoint: 'true',
    time: 'true'
}])
// const controlValue = ref([])
const receiverList = ref([])
const controlValue2 = ref([])
const rulerValue = ref([{}])
const editRulerValue = ref([{}])
const daleteflag = ref(false)
const loading = ref(false);
const ids = ref('')
const taskFlag = ref('')
const edittask = ref('')
const dialogFormVisible = ref(false)
const options = [
    {
        id: '5',
        value: '1',
        label: '生产厂家',
        children: [
            {
                id: '9',
                value: '1',
                label: '基本信息',
                children: [
                    {
                        id: '9',
                        value: '1',
                        label: '营业执照有效期',
                    },
                ],
            },
            {
                id: '10',
                value: '2',
                label: '生产许可证',
                children: [
                    {
                        id: '10',
                        value: '2',
                        label: '有限期',
                    },
                ],
            },
        ]
    },
    {
        value: '2',
        label: '商品-药品',
        children: [
            {
                value: '3',
                label: '质量信息',
                children: [
                    {
                        value: '3',
                        label: '批准文号有限期',
                    },
                ],
            },
            {
                value: '4',
                label: '商品附件',
                children: [
                    {
                        value: '4',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '3',
        label: '商品-器械',
        children: [
            {
                value: '5',
                label: '质量信息',
                children: [
                    {
                        value: '5',
                        label: '注册证有限期',
                    },
                ],
            },
            {
                value: '6',
                label: '商品附件',
                children: [
                    {
                        value: '6',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '4',
        label: '商品-消杀',
        children: [
            {
                value: '7',
                label: '质量信息',
                children: [
                    {
                        value: '7',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '8',
                label: '商品附件',
                children: [
                    {
                        value: '8',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '5',
        label: '商品-食品',
        children: [
            {
                value: '9',
                label: '质量信息',
                children: [
                    {
                        value: '9',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '10',
                label: '商品附件',
                children: [
                    {
                        value: '10',
                        label: '有效期',
                    },
                ],
            },
        ]
    }, {
        value: '6',
        label: '供应商',
        children: [
            {
                value: '11',
                label: '基本信息',
                children: [
                    {
                        value: '11',
                        label: '营业期限',
                    },
                ],
            },
            {
                value: '12',
                label: '质量信息',
                children: [
                    {
                        value: '12',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '13',
                label: '委托书',
                children: [
                    {
                        value: '13',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '14',
                label: '质保协议',
                children: [
                    {
                        value: '14',
                        label: '有限期',
                    },
                ],
            },
            {
                value: '15',
                label: '供应商附件',
                children: [
                    {
                        value: '15',
                        label: '有限期',
                    },
                ],
            },
        ]
    }, {
        value: '7',
        label: '客户',
        children: [
            {
                value: '16',
                label: '基本信息',
                children: [
                    {
                        value: '16',
                        label: '营业期限',
                    },
                ],
            },
            {
                value: '17',
                label: '许可证',
                children: [
                    {
                        value: '17',
                        label: '有限期',
                    },
                ],
            },
            {
                value: '18',
                label: '执业许可证',
                children: [
                    {
                        value: '18',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '19',
                label: '委托书',
                children: [
                    {
                        value: '19',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '20',
                label: '质保协议',
                children: [
                    {
                        value: '20',
                        label: '有限期',
                    },
                ],
            },
            {
                value: '21',
                label: '客户附件',
                children: [
                    {
                        value: '21',
                        label: '有限期',
                    },
                ],
            },
        ]
    },

]
const options2 = [
    {
        value: '1',
        label: '效期查询',
        children: [
            {
                value: '1',
                label: '药品',
                children: [
                    {

                        value: '1',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '2',
                label: '器械',
                children: [
                    {
                        value: '2',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '3',
                label: '消杀',
                children: [
                    {
                        value: '3',
                        label: '有效期',
                    },
                ],
            },
            {
                value: '4',
                label: '食品',
                children: [
                    {
                        value: '4',
                        label: '有效期',
                    },
                ],
            },
        ]
    },
]
const options3 = [
    {
        value: '1',
        label: '库存综合查询',
        children: [
            {
                value: '1',
                label: '药品',
                children: [
                    {
                        value: '1',
                        label: '数量',
                    },
                ],
            },
            {
                value: '2',
                label: '器械',
                children: [
                    {
                        value: '2',
                        label: '数量',
                    },
                ],
            },
            {
                value: '3',
                label: '消杀',
                children: [
                    {
                        value: '3',
                        label: '数量',
                    },
                ],
            },
            {
                value: '4',
                label: '食品',
                children: [
                    {
                        value: '4',
                        label: '数量',
                    },
                ],
            },
        ]
    },
]
const cycleOptions = [
    {
        value: 'MON',
        label: '周一',
    },
    {
        value: 'TUE',
        label: '周二',
    },
    {
        value: 'WED',
        label: '周三',
    },
    {
        value: 'THU',
        label: '周四',
    },
    {
        value: 'FRI',
        label: '周五',
    },
    {
        value: 'SAT',
        label: '周六',
    },
    {
        value: 'SUN',
        label: '周日',
    },
]
const mounthOptions = [
    {
        value: '1',
        label: '1日',
    },
    {
        value: '2',
        label: '2日',
    },
    {
        value: '3',
        label: '3日',
    },
    {
        value: '4',
        label: '4日',
    },
    {
        value: '5',
        label: '5日',
    },
    {
        value: '6',
        label: '6日',
    },
    {
        value: '7',
        label: '7日',
    },
    {
        value: '8',
        label: '8日',
    },
    {
        value: '9',
        label: '9日',
    },

    {
        value: '10',
        label: '10日',
    },
    {
        value: '11',
        label: '11日',
    },
    {
        value: '12',
        label: '12日',
    },
    {
        value: '13',
        label: '13日',
    },
    {
        value: '14',
        label: '14日',
    },
    {
        value: '15',
        label: '15日',
    },
    {
        value: '16',
        label: '16日',
    },
    {
        value: '17',
        label: '17日',
    },
    {
        value: '18',
        label: '18日',
    },
    {
        value: '19',
        label: '19日',
    },
    {
        value: '20',
        label: '20日',
    },
    {
        value: '21',
        label: '21日',
    },
    {
        value: '22',
        label: '22日',
    },
    {
        value: '23',
        label: '23日',
    },
    {
        value: '24',
        label: '24日',
    },
    {
        value: '25',
        label: '25日',
    },
    {
        value: '26',
        label: '26日',
    },
    {
        value: '27',
        label: '27日',
    },
    {
        value: '28',
        label: '28日',
    },
    {
        value: '29',
        label: '29日',
    },
    {
        value: '30',
        label: '30日',
    },
    {
        value: '31',
        label: '31日',
    },


]
//计算日期差值
function DateDiff(Date_end, Date_start) {
    let aDate, oDate1, oDate2, iDays;
    Date_end = Date_end.split(" ");
    aDate = Date_end[0].split("-");
    oDate1 = new Date(aDate[0], aDate[1], aDate[2]);
    Date_start = Date_start.split(" ");
    aDate = Date_start[0].split("-");
    oDate2 = new Date(aDate[0], aDate[1], aDate[2]);
    iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24);
    return iDays;
}
//字典回显
const formDict = (data, val) => {
    return (data && val) ? proxy.selectDictLabel(data, val) : '--'
}
//任务类型切换
const taskChange = (v) => {
    // console.log(v);
    dialogform.value.controlValue = []
    // editdialogform.value.controlValue = []
    if (v == '1') {
        taskFlag.value = '1'
        rulerHidden.value = true
        quantityHidden.value = true
        statusHidden.value = true
    }
    if (v == '2') {
        taskFlag.value = '2'
        rulerHidden.value = true
        quantityHidden.value = true
        statusHidden.value = false
    }
    if (v == '3') {
        taskFlag.value = '3'
        rulerHidden.value = true
        quantityHidden.value = false
        statusHidden.value = false
    }
}
const taskChange2 = (v) => {
    editdialogform.value.controlValue2 = []
    if (v == '1') {
        taskFlag.value = '1'
        statusable.value = true
    }
    if (v == '2') {
        taskFlag.value = '2'
        statusable.value = false
    }
    if (v == '3') {
        taskFlag.value = '3'
        statusable.value = false
    }
}
const handlerUser = () => {
    // rulerValue[index].receiverFlag = !rulerValue[index].receiverFlag
    var Organization = JSON.parse(localStorage.getItem("Organization"))
    var orgKey = JSON.parse(localStorage.getItem("orgKey"))
    var num = orgKey.content
    var userList = Organization.content
    var userId = userList[num].id
    alarm.getUser({
        'sysOrg.id': userId,
        current: current2.value,
        size: size2.value,
    }).then(res => {
        if (res.code == 200) {
            receiverList.value = res.data.records
            total2.value = res.data.total
        }
    })
}
//禁用
const handlerDisabled = (index) => {
    itemcount2.value[index].appoint = 'true'
    itemcount2.value[index].time = 'false'
    rulerValue.value[index].sureTime = ''
}
const handlerDisabled2 = (index) => {
    itemcount2.value[index].appoint = 'false'
    itemcount2.value[index].time = 'true'
    rulerValue.value[index].currentTimeNow = ''
}
const handlerDisabled3 = (index) => {
    edititemcount2.value[index].appoint = 'true'
    edititemcount2.value[index].time = 'false'
    editRulerValue.value[index].sureTime = ''
}
const handlerDisabled4 = (index) => {
    edititemcount2.value[index].appoint = 'false'
    edititemcount2.value[index].time = 'true'
    editRulerValue.value[index].currentTimeNow = ''
}
// 输入框聚焦
const handleFocus = (index) => {
    rulerValue.value[index].sureTime = ''
}
const handleFocus2 = (index) => {
    rulerValue.value[index].currentTimeNow = ''
    // current.value = true
    // appoint.value = false
}
const handlerUser2 = (index) => {
    var Organization = JSON.parse(localStorage.getItem("Organization"))
    var orgKey = JSON.parse(localStorage.getItem("orgKey"))
    var num = orgKey.content
    var userList = Organization.content
    var userId = userList[num].id
    alarm.getUser({
        'sysOrg.id': userId,
        name: rulerValue.value[index].receiver || editRulerValue.value[index].receiver,
    }).then(res => {
        if (res.code == 200) {
            receiverList.value = res.data.records
        }
    })
}
// handlerUser2()
// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
// 编辑按钮
const handleEdit = (row, status) => {
    if (status == 'flag') {
        type.value = 'copy'
    } else {
        type.value = ''
    }
    handlerUser()
    deleteRulerid.value = []
    editdialogFormVisible.value = true
    weekFlag.value = false
    mounthFlag.value = false
    ids.value = row.id
    alarm.detail({ id: row.id }).then(res => {
        if (res.code == 200) {
            if (status == 'flag') {
                editdialogform.alarmName = ''
            } else {
                editdialogform.value.alarmName = res.data.alarmName
            }
            taskFlag.value =  res.data.taskType
            console.log(res.data.taskType);
            editdialogform.value.taskType = res.data.taskType
            if(res.data.taskType == '1' || res.data.taskType == '2' ){
                dateValue.value = true
                console.log(dateValue.value);
            }
            if(res.data.taskType == '3' ){
                dateValue.value = false
            }
            editdialogform.value.weekTerm = res.data.weekTerm
            if (res.data.weekTerm == '2') {
                weekFlag.value = true
                editdialogform.value.week = res.data.weekTermValue
            }
            if (res.data.weekTerm == '3') {
                mounthFlag.value = true
                editdialogform.value.mounth = res.data.weekTermValue
            }
            let tmpArr = []
            tmpArr.push(new Date(res.data.validBeginTime))
            tmpArr.push(new Date(res.data.validEndTime))
            editdialogform.value.createDate = tmpArr
            editdialogform.value.executeTime = res.data.executeTime
            const result2 = JSON.parse(res.data.moniterArr)
            edititemcount.value = result2
            editdialogform.value.controlValue2 = result2
            // editRulerValue[index].moniterName
            res.data.moniterRuleDTOS.map(item => {
                function diff() {
                    if (item.currentTimeNow) {
                        if (item.currentTimeNow == "1970-01-01T00:00:00.000+00:00") {
                            item.currentTimeNow = 0
                            item.dateOption = '1'
                        } else {
                            item.dateOption = '1'
                            var res1 = moment(item.currentTimeNow).format('YYYY-MM-DD');
                            var res2 = moment(new Date()).format('YYYY-MM-DD');
                            item.currentTimeNow = DateDiff(res1, res2)
                            if (res1 < res2) {
                                item.currentTimeNow = -item.currentTimeNow
                            }
                        }

                    }
                    if (item.sureTime) {
                        item.dateOption = '2'
                    }
                    if (item.noticeCenter == '2') {
                        item.noticeCenter = 2
                        receiverFlag2.value = true
                        item.receiverBy = item.receiverBy.split(",")
                    } else {
                        receiverFlag2.value = false
                    }
                    if (item.alarmTask == '2') {
                        item.alarmTask = 2
                    }
                    if (item.statusAble == '2') {
                        item.statusAble = 2
                    }
                    return item.currentTimeNow, item.noticeCenter, item.alarmTask, item.statusAble
                }
                return diff()
            })
            editRulerValue.value = []
            edititemcount2.value = []
            res.data.moniterRuleDTOS.forEach((item) => {
                editRulerValue.value.push(item)
                edititemcount2.value.push(item)
            })
        }
    })
}
//编辑请求
const editAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            if (deleteRulerid.value.length > 0) {
                const rulerId = deleteRulerid.value.join(',')
                alarm.deleteRuler({ ids: rulerId }).then(res => {
                    console.log(res);
                })
            }
            var validBeginTime = moment(editdialogform.value.createDate[0]).format('YYYY-MM-DD')
            var validEndTime = moment(editdialogform.value.createDate[1]).format('YYYY-MM-DD')
            const cloneeditRulerValue = JSON.parse(JSON.stringify(editRulerValue._rawValue))
            cloneeditRulerValue.map((obj) => {
                if (obj.currentTimeNow) {
                    var now = new Date(); // 获取当前时间
                    now.setDate(now.getDate() + obj.currentTimeNow); // 将用户输入的天数加到当前日期上
                    var year = now.getFullYear(); // 年份
                    var month = now.getMonth() + 1; // 月份（0-11）
                    var date = now.getDate(); // 天数（1到31）
                    obj.currentTimeNow = year + "-" + month + "-" + date
                    obj.currentTimeNow = moment(obj.currentTimeNow).format('YYYY-MM-DD')
                    // return obj.currentTimeNow; // 返回新日期字符串
                }
                if (type.value == 'copy') {
                    obj.id = ''
                }
                if (obj.sureTime) {
                    obj.sureTime = moment(obj.sureTime).format('YYYY-MM-DD')
                } else {
                    delete obj.sureTime
                }
                if (obj.noticeCenter) {
                    obj.noticeCenter = obj.noticeCenter.toString()
                }
                if (obj.alarmTask) {
                    obj.alarmTask = obj.alarmTask.toString()
                }
                if (obj.statusAble) {
                    obj.statusAble = obj.statusAble.toString()
                }
                if (obj.receiverBy) {
                    obj.receiverBy = obj.receiverBy.join(',')
                }
            });
            var params = {
                alarmName: editdialogform.value.alarmName,
                validBeginTime: validBeginTime,
                validEndTime: validEndTime,
                taskType: editdialogform.value.taskType,
                executeTime: editdialogform.value.executeTime,
                weekTerm: editdialogform.value.weekTerm,
                // weekTermValue: editdialogform.week || editdialogform.mounth,
                moniterRuleDTOS: cloneeditRulerValue
            }
            if (type.value !== 'copy') {
                params.id = ids.value
            }
            if (editdialogform.value.week) {
                params.weekTermValue = editdialogform.value.week
            }
            if (editdialogform.value.mounth) {
                params.weekTermValue = editdialogform.value.mounth
            }
            const arr2 = []
            editdialogform.value.controlValue2.forEach((item) => {
                arr2.push(item[2])
            })
            params.moniterValue = arr2.join(',')
            params.moniterArr = JSON.stringify(editdialogform.value.controlValue2)
            let flag = false
            const cloneeditRulerValue2 = JSON.parse(JSON.stringify(cloneeditRulerValue))
            cloneeditRulerValue2.forEach((item) => {
                delete item.id
            })
            console.log(cloneeditRulerValue2);
            for (let i = 0; i < cloneeditRulerValue2.length; i++) {
                for (let v = i + 1; v < cloneeditRulerValue2.length; v++) {
                    if (JSON.stringify(cloneeditRulerValue2[i]) == JSON.stringify(cloneeditRulerValue2[v])) {
                        flag = true
                        ElMessage({
                            type: "error",
                            message: `条件${i + 1}和条件${v + 1}不能完全一致，请修改后提交！`,
                        });
                        return
                    }
                }
                if (flag) {
                    return
                }
            }
            alarm.save(params).then(res => {
                if (res.code == 200) {
                    if (res.code == 200) {
                        ElMessage({
                            message: type.value == 'copy' ? '创建成功' : '修改成功',
                            type: "success",
                        });
                        editdialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "修改失败，请稍后重试",
                        });
                    }
                }
            })
        }
    });
}
//新增报警配置
const saveAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var validBeginTime = moment(dialogform.value.createDate[0]).format('YYYY-MM-DD')
            var validEndTime = moment(dialogform.value.createDate[1]).format('YYYY-MM-DD')
            function formatTime(dateString) {
                var date = new Date(dateString);
                var hour = ('0' + date.getHours()).slice(-2);
                var minute = ('0' + date.getMinutes()).slice(-2);
                var second = ('0' + date.getSeconds()).slice(-2);
                return hour + ':' + minute + ':' + second;
            }
            var executeTime = formatTime(dialogform.value.executeTime)
            const cloneRulerValue = JSON.parse(JSON.stringify(rulerValue.value))
            cloneRulerValue.map(obj => {
                if (obj.receiverBy) {
                    obj.receiverBy = obj.receiverBy.toString()
                }
                if (obj.currentTimeNow) {
                    function calculateNewDate() {
                        var now = new Date(); // 获取当前时间
                        now.setDate(now.getDate() + obj.currentTimeNow); // 将用户输入的天数加到当前日期上
                        var year = now.getFullYear(); // 年份
                        var month = now.getMonth() + 1; // 月份（0-11）
                        var date = now.getDate(); // 天数（1到31）
                        obj.currentTimeNow = year + "-" + month + "-" + date
                        obj.currentTimeNow = moment(obj.currentTimeNow).format('YYYY-MM-DD')
                        return obj.currentTimeNow; // 返回新日期字符串
                    }
                    return calculateNewDate()
                }
            });
            var params = {
                alarmName: dialogform.value.alarmName,
                validBeginTime: validBeginTime,
                validEndTime: validEndTime,
                taskType: dialogform.value.taskType,
                executeTime: executeTime,
                weekTerm: dialogform.value.weekTerm,
                weekTermValue: dialogform.value.week || dialogform.value.mounth,
                moniterRuleDTOS: cloneRulerValue
            }
            const arr = []
            dialogform.value.controlValue.forEach(v => {
                arr.push(v.toString())
            })
            const new_list = []
            const moniter = []
            arr.forEach((item) => {
                new_list.push(item.split(','))
                moniter.push(item[2])
            })
            params.moniterArr = JSON.stringify(new_list)
            params.moniterValue = moniter.join(',')
            // if(dialogform.value.moniterCount ){

            // }
            let flag = false
            for (let i = 0; i < cloneRulerValue.length; i++) {
                for (let v = i + 1; v < cloneRulerValue.length; v++) {
                    if (JSON.stringify(cloneRulerValue[i]) == JSON.stringify(cloneRulerValue[v])) {
                        flag = true
                        ElMessage({
                            type: "error",
                            message: `条件${i + 1}和条件${v + 1}不能完全一致，请修改后提交！`,
                        });
                        return
                    }
                }
                if (flag) {
                    return
                }
            }

            try {
                rulerValue.value.forEach(function (item, index) {
                    console.log(item);
                    if (item.receiverBy == '') {
                        ElMessage({
                            type: "error",
                            message: `条件${index + 1}的接收人为必填项，请修改后提交！`,
                        });
                        throw new Error();
                    }

                });
            } catch (e) {
            };
            rulerValue.value.forEach(function (item, index) {
                if (item.receiverBy !== '') {
                    return false;
                }
                ElMessage({
                    type: "error",
                    message: `条件${index + 1}的接收人为必填项，请修改后提交！`,
                });
            })
            alarm.save(params).then(res => {
                if (res.code == 200) {
                    if (res.code == 200) {
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        dialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "添加失败，请稍后重试",
                        });
                    }
                }
            })
        }
    });
}

//复制按钮
const handleCopy = (row) => {
    type.value = 'copy'
    handleEdit(row, 'flag')
}
//禁用和解禁
const handleDisabled = (row) => {
    var params = {
        id: row.id,
        status: row.status == '1' ? '2' : '1'
    }
    alarm.updateStatus(params).then(res => {
        if (res.code == 200) {
            proxy.msgSuccess(row.status == '2' ? '解禁成功' : '禁用成功')
            getList()
        } else {
            proxy.msgError(res.msg);
        }
    })
}
// 重置
function resetQuery(formEl) {
    formEl.resetFields()
    getList()
}
//新增报警配置的按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    weekFlag.value = false
    mounthFlag.value = false
    rulerHidden.value = false
    dialogform.value.controlValue = []
    itemcount.value = 1
    formEl.resetFields()
    rulerValue.value.forEach((item) => {
        item.noticeCenter = 1
    })
    rulerValue.value = [{}]
    itemcount2.value = [itemcount2._rawValue[0]]
}
// 增加监控字段
const addControl = () => {
    itemcount.value += 1;
    dialogform.value.controlValue.push('');
}

//编辑增加监控字段
const addControl2 = () => {
    editdialogform.controlValue2.push(['', '', '']);
}
const controlArr = ref([])
const controlArr2 = ref([])
// 删除监控字段
const deleteControl = index => {
    const arr = [...controlArr.value]
    arr.splice(index, 1)
    controlArr.value = [...arr]
    if (itemcount.value > 1) {
        dialogform.value.controlValue.splice(index, 1);
        itemcount.value -= 1;
    }
};
//编辑删除监控字段
const editdeleteControl = index => {
    if (editdialogform.controlValue2.length > 1) {
        editdialogform.controlValue2.splice(index, 1);
    }
};
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
// 保存监控字段
// 增加规则
const addDiv = () => {
    rulerValue.value.push({
        // supervisory: '',
        // dateOption: '1',
        // num: '',
        // specifiedDate: '',
        // reminder: '',
        // exceptionTask: '',
        // disable: ''
    })
    itemcount2.value.push(
        {
            supervisory: '',
            dateOption: '1',
            num: '',
            specifiedDate: '',
            reminder: '',
            exceptionTask: '',
            disable: '',
            appoint: 'true',
            time: 'true'
        }
    )
}
//编辑增加规则
const addDiv2 = () => {
    editRulerValue.value.push({})
    edititemcount2.value.push(
        {
            moniterName: '',
            dateOption: 1,
            currentTimeNow: '',
            sureTime: '',
            noticeCenter: '',
            alarmTask: '',
            statusAble: '',
            appoint: 'true',
            time: 'true'
        }
    )
}
//删除规则
const deleteRuler = (item, index) => {
    if (itemcount2._rawValue.length > 1) {
        itemcount2.value.splice(index, 1);
        rulerValue.value.splice(index, 1);
    }
};
// 编辑删除规则
const deleteRuler2 = (item, index) => {
    if (edititemcount2._rawValue.length > 1) {
        deleteRulerid.value.push(edititemcount2.value[index].id);
        edititemcount2.value.splice(index, 1);
        editRulerValue.value.splice(index, 1);
    }
};
//搜索
const searchQuery = () => {
    getList()
}
//报警配置列表
const getList = () => {
    loading.value = true
    const params = {
        ...queryParams.value,
    }
    if(params.taskType == '0'){
        delete params.taskType
    }
    // console.log(params);
    alarm.list(params).then(res => {
        if (res.code == 200) {
            configeList.value = res.data.records
            res.data.records.forEach(item => {
                createName.value = item.createBy.name
            });
            total.value = res.data.total
            loading.value = false
        }
    })
}
//周期
const handlerCycle = (item) => {
    if (item.value == 1) {
        weekFlag.value = false
        mounthFlag.value = false
    } else if (item.value == 2) {
        weekFlag.value = true
        mounthFlag.value = false
    } else if (item.value == 3) {
        weekFlag.value = false
        mounthFlag.value = true
    }
}
//字典请求
async function dict() {
    statusList.value = await proxy.getDictList('alarm_state')
    tasktypeList.value = await proxy.getDictList('task_type')
    cycleList.value = await proxy.getDictList('cycle')
    supervisoryList.value = await proxy.getDictList('supervisory')
    taskTypeList.value = await proxy.getDictList('taskTypeList')
}
dict()
getList()
</script>

<style scoped  lang='scss'>
::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.hiddenP {
    position: absolute;
    top: 0.6rem;
    left: 1.4rem;
    font-size: 16px;
    color: #ccc // background-color: aqua;

}
::v-deep .rules .el-form-item__label {
    // background-color: black;
    position: relative;

    .cell::after {
        content: "*";
        color: red;
        display: inline-block;
        position: absolute;
        top: 30%;
        left: 70px;
    }
}
.rulerDiv {
    border: 1px solid transparent;
    background: linear-gradient(rgb(247, 247, 247), rgb(250, 250, 250)) padding-box,
        repeating-linear-gradient(-45deg, #ccc 0, #ccc 0.5em, white 0, white 0.75em);
    padding: 10px;
    width: 3.5rem;
    height: 1.42rem;
    margin-top: 20px;
    // border: 1px dashed #000;
    // border-width: 5px;
    // background-color: #4e2222;
}

.rulerDiv2 {
    border: 1px solid transparent;
    background: linear-gradient(rgb(247, 247, 247), rgb(250, 250, 250)) padding-box,
        repeating-linear-gradient(-45deg, #ccc 0, #ccc 0.5em, white 0, white 0.75em);
    padding: 10px;
    width: 3.5rem;
    height: 1.42rem;
    margin-top: 20px;
    position: relative;
    // border: 1px dashed #000;
    // border-width: 5px;
}

::v-deep .ruler-datepicker .el-date-editor.el-input {
    margin-left: 10px;
    width: 150px;
}

::v-deep .rulerInput .el-input__wrapper {
    margin-top: 20px;
}

.rulerForm {
    display: flex;
}

.bottomadd {
    // margin:20px 0px 0px 10px
    position: relative;
    top: 20px;
    left: 10px
}

::v-deep .el-cascader .el-input {
    width: 420px;
    margin-top: 10px;
}

::v-deep .ruler-form .el-form-item__label {
    margin-top: 20px;
}

::v-deep .controlValue .el-form-item__label {
    margin-top: 10px;
}

.el-table {
    margin-top: 10px;
}
</style>