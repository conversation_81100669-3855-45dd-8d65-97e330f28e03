import request from '@/utils/request';

export default {
    /** s 车辆类型 */
    // 车辆类型列表数据
    listCarType: function (params) {
        return request.get('/tms/carType/list', params);
    },
    // 根据车辆类型id获取车辆类型
    queryCarTypeById: function (params) {
        return request.get('/tms/carType/queryById', params);
    },
    // 新增或修改车辆类型保存
    saveCarType: function (data) {
        return request.post('/tms/carType/save', data);
    },
    // 删除车辆类型
    delCarType: function (params) {
        return request.delete('/tms/carType/delete', params);
    },
    /** e 车辆类型 */
    /** s 车辆管理 */
    // 车辆信息列表
    listCarInfo: function (params) {
        return request.get('/tms/carInfo/list', params);
    },
    // 根据车辆id获取车辆信息
    queryCarInfoById: function (params) {
        return request.get('/tms/carInfo/queryById', params);
    },
    // 新增或修改车辆信息保存
    saveCarInfo: function (data) {
        return request.post('/tms/carInfo/save', data);
    },
    // 修改车辆信息保存
    editCarInfo: function (data) {
        return request.post('/tms/carInfo/edit', data);
    },
    // 删除车辆信息
    delCarInfo: function (params) {
        return request.delete('/tms/carInfo/delete', params);
    },
    // 辆绑定传感器保存
    bindDevice: function (data) {
        return request.post('/tms/carBindDevice/carBindDevice', data);
    },
    // 车辆绑定司机保存
    bindDriver: function (data) {
        return request.post('/tms/carDriver/carBindDriver', data);
    },
    // 根据车辆编号获取绑定司机
    getCarDriverList: function (params) {
        return request.get('/tms/carDriver/getCarDriverList', params);
    },
    // 获取未绑定的司机列表
    getUnbindDriverList: function (params) {
        return request.get('/tms/driverInfo/getUnbindDriverList', params);
    },
    // 查询设备列表数据
    getdeviceList: function (params) {
        return request.get('/device/terminal/list', params);
    },
    // 根据车辆查询绑定传感器
    getCarBindDevice: function (params) {
        return request.get('/tms/carBindDevice/getCarBindDevice', params);
    },
    // 获取冷链车列表
    getColdChainCarList: function (params) {
        return request.get('/tms/carInfo/getColdChainCarList', params);
    },

    /** e 车辆管理 */
    /** s 司机管理 */
    // 员工转司机保存
    addDriver: function (data) {
        return request.post('/tms/driverInfo/saveDriver', data);
    },
    // 司机信息列表
    listdDiverInfo: function (params) {
        return request.get('/tms/driverInfo/list', params);
    },
    // 根据id获取司机信息
    queryDiverInfoById: function (params) {
        return request.get('/tms/driverInfo/queryById', params);
    },
    // 司机信息修改保存
    updateDriver: function (data) {
        return request.post('/tms/driverInfo/save', data);
    },
    // 司机注销
    delDiverInfo: function (params) {
        return request.delete('/tms/driverInfo/delete', params);
    },
    // 获取承运商司机
    getDriverList: function (params) {
        return request.get('/tms/driverInfo/getDriverList', params);
    },
    // 根据网点获取干线司机
    getLineDriverList: function (params) {
        return request.get('/tms/driver/lineDriver/getLineDriverList', params);
    },
    // 网点关联干线司机
    bindLineDeiver: function (data) {
        return request.post('/tms/driver/lineDriver/bindLineDriver', data);
    },
    // 获取已绑定揽收司机
    getLanDriverList: function (params) {
        return request.get('/tms/driver/lanDriver/getLanDriverList', params);
    },
    // 获取已绑定地配司机
    getLandDriverList: function (params) {
        return request.get('/tms/driver/landDriver/getLandDriverList', params);
    },
    // 揽收区域关联揽收司机
    bindLanDeiver: function (data) {
        return request.post('/tms/driver/lanDriver/bindLanDriver', data);
    },
    // 地配区域关联地配司机
    bindLandDeiver: function (data) {
        return request.post('/tms/driver/landDriver/bindLandDriver', data);
    },
    /** e 司机管理 */
    /** s 三方司机配置 */
    // 保存三方司机配置
    saveDriverConfig: function (data) {
        return request.post('/tms/three/driverConfig/save', data);
    },
    // 三方司机配置列表
    listDriverConfig: function (params) {
        return request.get('/tms/three/driverConfig/list', params);
    },
    // 根据Id获取三方司机配置数据
    queryDriverConfigById: function (params) {
        return request.get('/tms/three/driverConfig/queryById', params);
    },
    // 删除三方司机配置
    delDriverConfig: function (params) {
        return request.delete('/tms/three/driverConfig/delete', params);
    },
    // 用户下拉
    listUser: function (params) {
        return request.get('/sys/user/list', params);
    },
    /** e 三方司机配置 */
    /** s 车辆传感器 */
    // 车辆绑定传感器-查询
    listCarBindDevice: function (params) {
        return request.get('/tms/carBindDevice/list', params);
    },
    /**
     * 解绑传感器
     * @param data
     * @returns {Promise | Promise<unknown>}
     */
    batchUnBindCarAndDevice: function (data) {
        return request.post('/tms/carBindDevice/batch-unBind', data);
    },
    /** e 车辆传感器 */
};
