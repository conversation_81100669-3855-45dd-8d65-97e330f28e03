<template>
    <div class="app-container">
        <!-- 搜索 -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form">
                <el-form-item label="传感器" prop="deviceNo" style="width: 250px">
                    <el-input v-model="queryParams.deviceNo" clearable placeholder="请输入传感器编号" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="车牌号码" prop="carCode" style="width: 250px">
                    <el-input v-model="queryParams.carCode" clearable placeholder="请输入车牌号码" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10 flex justify-between">
                <el-button :disabled="multiple" icon="el-icon-magic-stick" type="danger" @click="handleBatchUnbind">批量解绑</el-button>
                <right-toolbar v-model:showSearch="showSearch" table-i-d="CarSensor" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="CarSensor" ref="CarSensor" v-loading="loading" v-model:columns="columns" :data="dataList" show-index :show-check-box="true" border element-loading-text="加载中..." max-height="600" @selection-change="handleSelectionChange">
                <template #createBy="scope">
                    {{scope.row?.createBy?.name}}
                </template>
                <template #opt="scope">
                    <el-button icon="el-icon-magic-stick" link size="small" type="danger" @click="handleUnbind(scope.row)">解除绑定</el-button>
                </template>
            </column-table>
            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>
<script>
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js';
import { ElMessage,ElMessageBox  } from 'element-plus';

export default {
    name: 'CarSensor',
    components: { SearchButton, ColumnTable, RightToolbar },
    data() {
        return {
            showSearch: true,
            queryParams: {
                current: 1,
                size: 10,
                deviceNo: undefined,
                carCode: undefined
            },
            loading: false,
            multiple: true,
            selectRows: [], // 选中的行
            dataList: [],
            total: 0,
            columns: [
                { title: '传感器', key: 'deviceNo', align: 'center', minWidth: 200, columnShow: true, sortable: true },
                { title: '车牌号码', key: 'carCode', align: 'center', minWidth: 200, columnShow: true, sortable: true },
                { title: '绑定时间', key: 'createDate', align: 'center', minWidth: 200, columnShow: true, sortable: true },
                { title: '操作人', key: 'createBy', align: 'center', minWidth: 200, sortable: true, columnShow: true },
                { title: '操作', key: 'opt', width: '100px', align: 'center', columnShow: true, hideFilter: true, fixed: 'right' }
            ]
        };
    },
    created() {
        this.queryParams.carCode = this.$route.query?.carCode || undefined;
        this.handleQuery();
    },
    activated() {
        // 从地址栏获取carCode
        this.queryParams.carCode = this.$route.query?.carCode || undefined;
        if (this.queryParams.carCode) {
            this.handleQuery();
        }
    },
    methods: {
        getList() {
            this.loading = true;
            this.dataList = [];
            operationConfiguration.listCarBindDevice(this.queryParams).then((response) => {
                if (response.code === 200 && response.data) {
                    this.dataList = response.data.records || [];
                    this.total = response.data.total || 0;
                }
                this.loading = false;
            });
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.queryParams = {
                current: 1,
                size: 10,
                deviceNo: undefined,
                carCode: undefined
            };
            this.handleQuery();
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },
        /**
         * 解除绑定
         * @param row
         */
        handleUnbind(row) {
            const _this = this;
            ElMessageBox.confirm(
                '<span style="color:red;">确认删除【' + row.carCode + '】绑定的【'+row.deviceNo+'】传感器吗？</span>',
                '警告',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    draggable: true,
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                _this.unbindSubmit([{ carCode: row.carCode, deviceNo: row.deviceNo }]);
            }).catch(() => {
                ElMessage({
                    type: 'info',
                    message: '操作已取消',
                })
            });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.selectRows = [];
            this.selectRows = selection;
            this.multiple = !this.selectRows.length;
        },
        /**
         * 批量解除绑定
         * @returns {*}
         */
        handleBatchUnbind() {
            // 移除非同一车辆的数据
            const carCode = this.selectRows[0].carCode;
            const selectNum = this.selectRows.length;
            let data = [];
            this.selectRows.forEach((item, index) => {
                if (item.carCode !== carCode) {
                    this.$refs.CarSensor.$refs.ColumnTable.toggleRowSelection(item, false);
                } else {
                    data.push({ carCode: item.carCode, deviceNo: item.deviceNo });
                }
            });
            if (selectNum > data.length) {
                ElMessage({
                    message: '已取消选中非同一车辆的数据',
                    type: 'warning'
                });
            }
            const _this = this;
            ElMessageBox.confirm(
                '<span style="color:red;">确认删除已绑定【' + carCode + '】的所有传感器吗？</span>',
                '警告',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    draggable: true,
                    dangerouslyUseHTMLString: true,
                }
            ).then(() => {
                _this.unbindSubmit(data);
            }).catch(() => {
                ElMessage({
                    type: 'info',
                    message: '操作已取消',
                })
            });
        },
        /**
         * 解除绑定提交
         * @param data
         */
        unbindSubmit(data) {
            operationConfiguration.batchUnBindCarAndDevice(data).then((response) => {
                if (response.code === 200) {
                    this.msgSuccess('解绑成功');
                    this.getList();
                }
            });
        }
    }
};
</script>

<style scoped lang="scss"></style>
