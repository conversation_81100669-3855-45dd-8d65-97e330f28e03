<template>
    <div class="p10" style="background-color: #f2f2f2">
        <el-card class="mb10" shadow="never">
            <card-header title="订单信息" />
            <div class="box-item">
                <div>
                    <span>订单号：</span>
                    <span>{{ orderInfo.orderNo || orderInfo.orderNo }}</span>
                </div>
                <div>
                    <span>运单号：</span>
                    <span>{{ orderInfo.transOrderNo }}</span>
                </div>
                <div>
                    <span>件数：</span>
                    <span>{{ dataList.length }}件</span>
                </div>
            </div>
        </el-card>
        <el-card class="mb10" shadow="never" style="margin-top: 10px">
            <card-header title="打印信息" />
            <div class="mb10">
                <el-button :loading="loading" :disabled="multiple" type="primary" @click="isASinglePrintJudgmentType">批量打印</el-button>
                <el-button :loading="loading" type="primary" @click="isPayMentMethodAllPrinted">全部打印</el-button>
            </div>
            <el-table ref="list" v-loading="loading" :data="dataList" :element-loading-text="loadText" @selection-change="handleSelectionChange">
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="箱号" prop="code" width="250" />
                <el-table-column align="center" label="打印次数" prop="printNum" />
                <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                    <template #="scope">
                        <el-button icon="el-icon-printer" style="color: #ffc21c; font-size: 15px" type="text" @click="isASinglePrintJudgmentType(scope.row)"> </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mt10 mb10" @pagination="getCodePageByOrderId" />
        </el-card>
    </div>
</template>

<script>
import CardHeader from '@/components/CardHeader';
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
export default {
    name: 'PrintBoxTag',
    components: {
        CardHeader
    },
    props: {
        orderInfo: {
            required: true,
            type: Object
        },
        type: {
            type: String,
            default: '1'
        }
    },
    data() {
        return {
            tabPosition: 'top',
            loading: false,
            dataList: [],
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            selectionRows: [],
            loadText: '',
            total: 0,
            queryParams: {
                current: 1,
                size: 50
            },
            boxlabelTemplateId: null
        };
    },
    created() {
        this.getCodePageByOrderId();
        this.boxlabelTemplateId = '********************************';
    },
    methods: {
        cancel() {
            this.$emit('callbackMethod');
        },
        getCodePageByOrderId() {
            this.loading = true; // 加载开始
            let params = {
                transOrderNo: this.orderInfo.transOrderNo,
                current: this.queryParams.current,
                size: this.queryParams.size
            };
            orderManagement.listTransBoxCode(params).then((response) => {
                if (response.code === 200) {
                    if (response.data && response.data.records) {
                        this.dataList = response.data.records;
                        this.total = response.data.total;
                    }
                }
            }).finally(() => {
                this.loading = false; // 加载结束
            });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
            this.selectionRows = selection;
        },
        /**
         * 判断订单 付款方式 类型
         * @returns {boolean}
         */
        isASinglePrintJudgmentType(row) {
            const { paymentMethod } = this.orderInfo;
            if (paymentMethod && paymentMethod == 2) {
                this.printLabelPaymentMethod(row);
                // this.printLabel(row)
            } else {
                this.printLabel(row);
            }
        },
        /**
         * 判断订单 付款方式 类型 全部打印
         */
        isPayMentMethodAllPrinted() {
            if (this.total > 50) {
                this.msgError('当前订单件数大于50件，请您使用批量打印');
                return;
            }
            const { paymentMethod } = this.orderInfo;
            if (paymentMethod && paymentMethod == 2) {
                this.printBoxCode();
                // this.printAll()
            } else {
                this.printAll();
            }
        },
        /**
         * 打印全部箱签
         * @param that.orderInfo 订单信息
         * @param paymentMethod 2 付款方式为到付
         */
        printAll() {
            orderManagement.getBoxCodeList({ transOrderNo: this.orderInfo.transOrderNo }).then((response) => {
                if (response.code === 200 && response?.data) {
                    const params = {
                        boxCodeList: response.data
                    };
                    const ids = response.data.map((item) => item.id);
                    let param = { ids };
                    if (typeof ids !== 'string') {
                        param.ids = ids.join(',');
                    }
                    orderManagement.changePrintNum(param).then((res) => {
                        new Promise((resolve, reject) => {
                            this.pdfLabelView(this.boxlabelTemplateId, ids);
                            setTimeout(() => {
                                resolve();
                            }, 500);
                        })
                            .then((data) => {
                                this.getCodePageByOrderId();
                                this.msgSuccess('打印成功');
                                this.loading = false;
                            })
                            .catch((err) => {
                                this.msgError('打印失败');
                                this.loading = false;
                            });
                    });
                }
            });
        },

        //全部打印
        printBoxCode() {
            let paramData = [];
            let that = this;
            let ids = [];
            let params = {};
            orderManagement.getBoxCodeList({ transOrderNo: this.orderInfo.transOrderNo }).then((response) => {
                if (response.code === 200 && response?.data) {
                    response.data.forEach((item) => {
                        paramData.push({
                            codeTemplateId: this.boxlabelTemplateId,
                            'payTemplateId': '075139aa5ac843de80561d78f5b06b53',
                            'orderNo': this.orderInfo.orderNo,
                            codeId: item.id
                        });
                        ids.push(item.id);
                    });
                    // const params = {
                    //   boxCodeList: response.data
                    // };
                    params = { ids };
                    if (typeof ids !== 'string') {
                        params.ids = ids.join(',');
                    }
                    orderManagement.changePrintNum(params).then((response) => {
                        new Promise((resolve, reject) => {
                            that.printPdfLabelViewNew(paramData);
                            setTimeout(() => {
                                resolve();
                            }, paramData.length * 200);
                        })
                            .then((data) => {
                                this.getCodePageByOrderId();
                                this.msgSuccess('打印成功');
                                this.loading = false;
                            })
                            .catch((err) => {
                                this.msgError('打印失败');
                                this.loading = false;
                            });
                    });
                }
            });
        },
        /**
         * 打印箱签
         * @param row 箱码信息
         * @param that.orderInfo 订单信息
         * @param paymentMethod 2 付款方式为到付
         */
        printLabel(row) {
            let ids = row.id || this.ids;
            let that = this;
            // var params = {};
            var num = 1;
            this.loading = true;
            this.loadText = '打印中';
            // if (row.id) {
            //   params.boxCodeList = [row];
            // } else {
            //   params.boxCodeList = this.selectionRows;
            //   num = this.selectionRows.length;
            //   this.$refs.list.clearSelection();
            // }
            let param = { ids };
            if (typeof ids !== 'string') {
                param.ids = ids.join(',');
            }
            orderManagement.changePrintNum(param).then((response) => {
                new Promise((resolve, reject) => {
                    that.pdfLabelView(this.boxlabelTemplateId, ids);
                    setTimeout(() => {
                        resolve();
                    }, num * 200);
                })
                    .then((data) => {
                        that.getCodePageByOrderId();
                        that.msgSuccess('打印成功');
                        that.loading = false;
                    })
                    .catch((err) => {
                        that.msgError('打印失败');
                        that.loading = false;
                    });
            });
        },
        /**
         * 打印箱签-单行/批量打印
         * @param row 箱码信息
         * @param that.orderInfo 订单信息
         * @param paymentMethod 2 付款方式为到付
         */
        printLabelPaymentMethod(row) {
            let paramData = [];
            var params = {};

            let ids = row.id || this.ids;
            let that = this;
            var num = 1;
            this.loading = true;
            this.loadText = '打印中';
            if (row.id) {
                paramData.push({
                    codeTemplateId: this.boxlabelTemplateId,
                    'payTemplateId': '075139aa5ac843de80561d78f5b06b53',
                    'orderNo': this.orderInfo.orderNo,
                    codeId: row.id
                });
            } else {
                num = this.ids.length;
                if (ids.length > 0) {
                    ids.forEach((o) => {
                        paramData.push({
                            codeTemplateId: this.boxlabelTemplateId,
                            'payTemplateId': '075139aa5ac843de80561d78f5b06b53',
                            'orderNo': this.orderInfo.orderNo,
                            codeId: o
                        });
                    });
                }
                this.$refs.list.clearSelection();
            }
            params = { ids };
            if (typeof ids !== 'string') {
                params.ids = ids.join(',');
            }
            orderManagement.changePrintNum(params).then((response) => {
                new Promise((resolve, reject) => {
                    that.printPdfLabelViewNew(paramData);
                    setTimeout(() => {
                        resolve();
                    }, num * 200);
                })
                    .then((data) => {
                        that.getCodePageByOrderId();
                        that.msgSuccess('打印成功');
                        that.loading = false;
                    })
                    .catch((err) => {
                        that.msgError('打印失败');
                        that.loading = false;
                    });
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.box-item {
    display: flex;
    justify-content: space-between;
}
</style>
