<template>
    <div v-loading="loading" class="app-container" element-loading-text="加载中...">
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="发票客户" prop="invoiceCustomer" style="width: 200px">
                    <el-input v-model="queryForm.invoiceCustomer" clearable placeholder="请输入发票客户" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="蓝字发票号码" prop="blueInvoiceNo" style="width: 230px">
                    <el-input v-model="queryForm.blueInvoiceNo" clearable placeholder="请输入蓝字发票号码" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="蓝字发票开票日期" prop="blueInvoiceDate" style="width: 360px">
                    <el-date-picker v-model="queryForm.blueInvoiceDate" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" style="width: 300px" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery" />
                </el-form-item>
                <template v-if="isShowAll">
                    <el-form-item label="审核状态" prop="auditStatus">
                        <el-select v-model="queryForm.auditStatus" clearable filterable placeholder="请选择审核状态" @change="handleQuery">
                            <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开票状态" prop="redInvoiceFlag">
                        <el-select v-model="queryForm.redInvoiceFlag" clearable filterable placeholder="请选择开票状态" @change="handleQuery">
                            <el-option v-for="item in redInvoiceFlagList" :key="item.value" :label="item.name" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="红冲原因" prop="reason">
                        <el-select v-model="queryForm.reason" clearable filterable placeholder="请选择红冲原因" @change="handleQuery">
                            <el-option v-for="item in reasonList" :key="item.value" :label="item.name" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="红字发票申请编号" prop="applyNo">
                        <el-input v-model="queryForm.applyNo" clearable placeholder="请输入红字发票申请编号" @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="红字发票申请日期" prop="redApplyDate">
                        <el-date-picker v-model="queryForm.redApplyDate" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" style="width: 300px" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery" />
                    </el-form-item>
                    <el-form-item label="红字发票开票日期" prop="redInvoiceDate">
                        <el-date-picker v-model="queryForm.redInvoiceDate" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" style="width: 300px" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery" />
                    </el-form-item>
                    <el-form-item label="申请人" prop="applyUser">
                        <el-input v-model="queryForm.applyUser" clearable placeholder="请输入申请人" style="width: 200px" @keyup.enter="handleQuery" />
                    </el-form-item>
                </template>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb-40">
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="RedLetterInvoiceApplication" @queryTable="getList"></right-toolbar>
            </div>
            <column-table ref="RedLetterInvoiceApplication" :columns="columns" :data="dataList" :maxHeight="tableHeight">
                <template #company="{ row }">
                    {{ row.company?.name }}
                </template>
                <template #reason="{ row }">
                    <span class="whitespace-nowrap">{{ formatDictionaryData('reasonList', row.reason) }}</span>
                </template>
                <template #businessType="{ row }">
                    <span :style="setBusinessTypeColor(row.businessType)">{{ formatDictionaryData('businessTypeList', row.businessType) }}</span>
                </template>
                <template #auditStatus="{ row }">
                    <span :style="setStatusColor(row.auditStatus)">{{ formatDictionaryData('statusList', row.auditStatus) }}</span>
                </template>
                <template #redInvoiceFlag="{ row }">
                    <span :style="setRedInvoiceFlagColor(row.redInvoiceFlag)">{{ formatDictionaryData('redInvoiceFlagList', row.redInvoiceFlag) }}</span>
                </template>
                <template #redFailReason="{ row }">
                    <div v-if="row.auditStatus === '1' && row.redInvoiceFlag === '0'">
                        <el-button icon="el-icon-refresh" link size="small" type="warning" @click="onRetryRedInvoice(row)">重试</el-button>
                        <span class="ml-2">{{ row.redFailReason }}</span>
                    </div>
                </template>
                <template #opt="{ row }">
                    <div>
                        <!-- 审核状态 auditStatus 审批中0 审批通过1 审批驳回2 作废3 -->
                        <el-button v-if="row.auditStatus === '2'" icon="el-icon-edit" link size="small" type="warning" @click="onOpenRedInvoice(row, 'edit')">编辑</el-button>
                        <el-button v-if="row.auditStatus === '2'" icon="el-icon-delete" link size="small" type="danger" @click="onCancelRedInvoice(row)">撤销</el-button>
                        <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="onOpenRedInvoice(row, 'detail')">详情</el-button>
                        <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="onCheckApproval(row)">查看审批</el-button>
                    </div>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- 红冲 -->
        <invoice-red-invoicing-detail
            v-if="invoiceRedInvoicingVisible"
            v-model:invoice-red-invoicing-visible="invoiceRedInvoicingVisible"
            :invoice-red-invoicing-id="invoiceRedInvoicingId"
            :invoice-red-invoicing-source="invoiceRedInvoicingSource"
            :invoice-red-invoicing-title="invoiceRedInvoicingTitle"
            :invoice-red-invoicing-type="invoiceRedInvoicingType"
            :operation-type="operationType"
            @close="closeRedInvoice"
            @submit="submitRedInvoice"
        />

        <!-- 审批记录弹窗 -->
        <approval-record v-if="approvalVisible" v-model:approval-visible="approvalVisible" :record-list="approvalRecords" @close="closeApprovalRecord" />
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import RedLetterInvoiceApplication from '@/api/carrierEnd/redLetterInvoiceApplication';
import InvoiceRedInvoicingDetail from '@/views/carrierFunction/InvoiceRedInvoicingDetail.vue';
import ApprovalRecord from '@/views/carrierFunction/ApprovalRecord';
import tool from '@/utils/tool';
import InvoiceApproval from '@/api/carrierEnd/InvoiceApproval';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'RedLetterInvoiceApplication',
    components: {
        ColumnTable,
        RightToolbar,
        SearchButton,
        InvoiceRedInvoicingDetail,
        ApprovalRecord
    },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                invoiceCustomer: undefined,
                blueInvoiceNo: undefined,
                blueInvoiceDate: [],
                auditStatus: undefined,
                redInvoiceFlag: undefined,
                reason: undefined,
                applyNo: undefined,
                redApplyDate: [],
                redInvoiceDate: [],
                applyUser: undefined
            },
            columns: [
                { title: '红字发票申请编号', key: 'applyNo', align: 'center', minWidth: '210px', columnShow: true, showOverflowTooltip: true },
                { title: '发票客户', key: 'companyName', align: 'center', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '纳税人识别号', key: 'taxNo', align: 'center', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '红冲原因', key: 'reason', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '详情说明', key: 'detailDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '红票金额（元）', key: 'redInvoiceAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '蓝票金额（元）', key: 'blueInvoiceAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '红字发票费（元）', key: 'invoiceMoney', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '申请人', key: 'applyUser', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '申请日期', key: 'applyTime', align: 'center', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'businessType', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '审核状态', key: 'auditStatus', align: 'center', minWidth: '120px', fixed: 'right', columnShow: true, showOverflowTooltip: true },
                { title: '开票状态', key: 'redInvoiceFlag', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '失败原因', key: 'redFailReason', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '265px', fixed: 'right', columnShow: true, showOverflowTooltip: true }
            ],
            isShowAll: false,
            loading: false,
            dataList: [],
            total: 0,
            businessTypeList: [],
            statusList: [],
            invoiceTypeList: [],
            tableHeight: 400,
            invoiceRedInvoicingVisible: false,
            invoiceRedInvoicingId: undefined,
            invoiceRedInvoicingTitle: undefined,
            invoiceRedInvoicingType: undefined,
            redInvoiceFlagList: [],
            reasonList: [],
            isShowMore: false,
            operationType: 'edit',
            approvalVisible: false,
            approvalRecords: [],
            isCarrier: false,
            invoiceRedInvoicingSource: undefined,
            shortcuts: setDatePickerShortcuts(),
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置业务类型颜色
		 * 1:预存款充值 2:付款单支付 3:收款单收款
         */
         setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': { color: '#5cb85c' },
                        '3': { color: '#2196f3' }
                    }[businessType] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (auditStatus) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' },
                        '3': { color: '#999' }
                    }[auditStatus] || { color: '#999' }
                );
            };
        },
        /**
         * 设置红冲状态颜色
         */
        setRedInvoiceFlagColor() {
            return (redInvoiceFlag) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' }
                    }[redInvoiceFlag] || { color: '#999' }
                );
            };
        }
    },
    created() {
        // 根据 Organization 判断是否为承运端
        const Organization = tool.data?.get('Organization');
        if (!Organization || !Organization.length || !Organization[0]?.type) {
            this.isCarrier = false;
        } else {
            const roleType = Organization[0].type;
            this.isCarrier = roleType === '1';
        }

        this.getDict();
        this.handleQuery();
    },
    methods: {
        /**
         * 重试红冲
         * @param row
         */
        onRetryRedInvoice(row) {
            RedLetterInvoiceApplication.manualInvoice({ applyId: row.id }).then((res) => {
                if (res.code === 200) {
                    this.$message.success(res.data?.msg);
                    this.getList();
                } else {
                    this.$message.error(res.msg || '开票失败');
                }
            });
        },
        /**
         * 关闭审批记录弹窗

         */
        closeApprovalRecord() {
            this.approvalVisible = false;
            this.approvalRecords = [];
        },
        /**
         * 关闭红冲
         */
        closeRedInvoice() {
            this.invoiceRedInvoicingVisible = false;
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.businessTypeList = await this.getDictList('payment_type');
            this.statusList = await this.getDictList('payment_approval_type');
            this.redInvoiceFlagList = await this.getDictList('red_invoice_result');
            this.reasonList = await this.getDictList('red_reason');
        },
        /**
         * 获取列表
         */
        async getList() {
            this.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { blueInvoiceDate, redApplyDate, redInvoiceDate, ...params } = this.queryForm;
            const res = await (this.isCarrier ? RedLetterInvoiceApplication.getListCarrierList(params) : RedLetterInvoiceApplication.getListCompanyList(params));

            if (res.code === 200) {
                this.tableHeight = window.innerHeight - this.$refs.searchCard.$el.offsetHeight - 240;
                this.dataList = res.data.records;
                this.total = res.data.total;
            } else {
                this.dataList = [];
                this.total = 0;
            }
            this.loading = false;
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { blueInvoiceDate, redApplyDate, redInvoiceDate } = this.queryForm;
            if (blueInvoiceDate && blueInvoiceDate.length) {
                this.queryForm.startBlueInvoiceDate = blueInvoiceDate[0] ? blueInvoiceDate[0] + ' 00:00:00' : '';
                this.queryForm.endBlueInvoiceDate = blueInvoiceDate[1] ? blueInvoiceDate[1] + ' 23:59:59' : '';
            } else {
                this.queryForm.startBlueInvoiceDate = undefined;
                this.queryForm.endBlueInvoiceDate = undefined;
            }
            if (redApplyDate && redApplyDate.length) {
                this.queryForm.startApplyTime = redApplyDate[0] ? redApplyDate[0] + ' 00:00:00' : '';
                this.queryForm.endApplyTime = redApplyDate[1] ? redApplyDate[1] + ' 23:59:59' : '';
            } else {
                this.queryForm.startApplyTime = undefined;
                this.queryForm.endApplyTime = undefined;
            }
            if (redInvoiceDate && redInvoiceDate.length) {
                this.queryForm.startRedInvoiceDate = redInvoiceDate[0] ? redInvoiceDate[0] + ' 00:00:00' : '';
                this.queryForm.endRedInvoiceDate = redInvoiceDate[1] ? redInvoiceDate[1] + ' 23:59:59' : '';
            } else {
                this.queryForm.startRedInvoiceDate = undefined;
                this.queryForm.endRedInvoiceDate = undefined;
            }
            this.getList();
        },
        /**
         * 撤销
         * @param row
         */
        onCancelRedInvoice(row) {
            this.$confirm(`<div style="padding: 20px 0"><div style="color: red">确认撤销？</div><div>撤销后需要重新发起!</div></div>`, '', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '确 认',
                cancelButtonText: '取 消',
                showClose: false,
                center: true,
                customClass: 'dialog-confirm',
                confirmButtonClass: 'el-button--large',
                cancelButtonClass: 'el-button--large'
            })
                .then(async () => {
                    try {
                        const res = await RedLetterInvoiceApplication.cancel({ ids: row.id });
                        if (res.code === 200) {
                            this.$message.success('撤销成功');
                            await this.getList();
                        } else {
                            this.$message.error(res.msg || '撤销失败');
                        }
                    } catch (err) {
                        this.$message.error('撤销失败');
                        console.error(err);
                    }
                })
                .catch(() => {});
        },
        /**
         * 查看审批记录
         * @param row
         */
        async onCheckApproval(row) {
            this.approvalVisible = true;
            try {
                const res = await RedLetterInvoiceApplication.getRecordList({ applyId: row.id });
                if (res.code === 200) {
                    this.approvalRecords = res.data || [];
                    this.approvalVisible = true;
                } else {
                    this.$message.error(res.msg || '获取审批记录失败');
                }
            } catch (err) {
                console.error(err);
                this.$message.error('获取审批记录失败');
            }
        },
        /**
         * 打开红字发票详情
         * @param {Object} row - 行数据
         * @param {string} type - 操作类型：'edit' 或 'detail'
         */
        onOpenRedInvoice(row, type = 'edit') {
            this.invoiceRedInvoicingVisible = true;
            this.invoiceRedInvoicingId = row.id;
            this.invoiceRedInvoicingTitle = '红字发票申请';
            this.invoiceRedInvoicingType = row.businessType;
            this.operationType = type;
            this.invoiceRedInvoicingSource = this.isCarrier ? 'carrier' : 'owner';
        },
        /**
         * 重置表单
         * @param formName
         */
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
        },
        /**
         * 重置查询条件
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 提交红冲
         */
        submitRedInvoice(formData) {
            const params = {
                id: formData.id,
                applyNo: formData.applyNo,
                source: this.invoiceRedInvoicingSource === 'carrier' ? '2' : '1', // 1-货主发起 2-承运商发起
                companyId: this.invoiceRedInvoicingSource === 'carrier' ? formData.companyId : undefined, // 货主id 承运端传递的参数
                companyName: this.invoiceRedInvoicingSource === 'carrier' ? formData.companyName : undefined, // 货主名称 承运端传递的参数
                carrierId: this.invoiceRedInvoicingSource === 'owner' ? formData.carrierId : undefined, // 承运商id 货主端传递的参数
                invoiceApplyId: formData.invoiceApplyId, // 蓝字发票申请id
                reason: formData.reason, // 红冲原因
                detailDesc: formData.detailDesc, // 详情说明
                invoiceMoney: formData.invoiceMoney, // 红字发票费
                redInvoiceAmount: formData.redInvoiceAmount // 红票金额
            };
            console.log('%c 1 --> Line: 395||RedLetterInvoiceApplication.vue\n params: ', 'color:#f0f;', params);
            InvoiceApproval.submitInvoiceRedInvoicing(params).then((res) => {
                if (res.code === 200) {
                    this.$message.success('红冲申请成功');
                    this.closeRedInvoice();
                    this.getList();
                } else {
                    this.$message.error(res.msg || '红冲申请失败');
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
}
.number__unit__element {
    position: relative;
    ::v-deep .el-input__inner {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 10px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
