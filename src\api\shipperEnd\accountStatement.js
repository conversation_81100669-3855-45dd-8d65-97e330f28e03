import request from '@/utils/request'
export default {
    // 列表
    statementListOwner: function(params) {
        return request.get('/cost/statement/company/list', params);
    },
    // 货主端-对账单列表费用统计
    statementListOwnerCost: function(params) {
        return request.get('/cost/statement/company/cost/stats', params);
    },
    // 货主端批量核对
    batchCheck: function(params) {
        return request.post('/cost/statement/company/check', params);
    },
    // 导出
    exportStatementOwner: function(params, config, resDetail, responseType) {
        return request.get('/cost/statement/company/export', params, config, resDetail, responseType);
    },
	// 获取承运商列表
	getCarrierList: function(params) {
		return request.post('/cost/statement/queryCarrierSelect', params);
	}
}
