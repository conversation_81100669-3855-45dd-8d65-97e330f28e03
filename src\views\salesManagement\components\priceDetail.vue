<template>
  <el-dialog v-model="dialogVisible" :before-close="cancalFn" title="销售调价详情" width="80%">
		<div v-loading="loadingFlag">
			<h4 class="titleH4" style="font-size: 13px;margin-left: 35px;">
				申请编号: <span style="font-weight: 400;color: #505050"> {{ listForm?.applyNo }}</span>&emsp;
        申请日期: <span style="font-weight: 400;color: #505050">{{
					listForm.applyDate ? functionIndex.transformTimestamp(listForm.applyDate) : null
				}}</span>&emsp;
				申请人: <span style="font-weight: 400;color: #505050">{{
					listForm.applyBy?.name
				}} </span>&emsp;审核状态:
        <span style="font-weight: 400;color: #505050"> {{
            listForm.auditStatus ? echo(listForm.auditStatus) : null
          }} </span>&emsp;
      </h4>
			<h4 class="el-dialog__title" style="margin-bottom: 10px">调整明细</h4>
			<el-table :data="listTable" :expand-row-keys="comeList" :row-key="renderKey" style="width: 100%"
                @expand-change="onchangeTable">
				<el-table-column type="expand">
					<template #default="props">
						<div style="display: flex;justify-content: end">
              <div m="4" style="display: flex;width: 87%; justify-content:space-between;align-items: center">
								<div style="color: red;font-size: 13px;line-height: 25px">
									<!--									<p>注意事项：</p>-->
									<!--									<p>1、调整数量总和不能大于所选出库（入库）记录当前的可调数量；</p>-->
									<!--									<p>2、调整数量不能录入小数；</p>-->
									<!--									<p>3、调整后单价不能大于所选出库（入库）记录当前的单价。</p>-->
								</div>
								<el-table :border="true" :data="props.row.salesAdjustPriceList" size="small"
                          style="width: 55%">
									<el-table-column :value-on-clear="0" align="center" label="调整数量"
                                   prop="adjustQuantity"/>
									<el-table-column :value-on-clear="0" align="center" label="调整后单价"
                                   prop="adjustUnitPrice">
                    <template #default="scope">
                      {{ Number(scope.row.adjustUnitPrice).toFixed(2) }}
                    </template>
                  </el-table-column>
                  <el-table-column :value-on-clear="0" align="center" label="调价金额" prop="adjustAmount">
                    <template #default="scope">
                      {{ Number(scope.row.adjustAmount).toFixed(2) }}
                    </template>
                  </el-table-column>
								</el-table>
							</div>
						</div>
					</template>
				</el-table-column>
				<el-table-column align="center" label="序号">
					<template #default="scope">
						{{ scope.$index + 1 }}
					</template>
				</el-table-column>
				<el-table-column align="center" label="客户" prop="" width="150">
					{{ listForm?.customer.enterpriseName }}
				</el-table-column>
        <el-table-column align="center" label="单据编号" min-width="180" prop="salesOutBound.orderCode"/>
				<el-table-column align="center" label="单据创建日期" min-width="150" prop="salesOutBound.billDate">
					<template #default="scope">
						{{ functionIndex.transformTimestamp(scope.row.createDate) }}
					</template>
				</el-table-column>
				<el-table-column align="center" label="出库日期" prop="salesOutBound.outTime" width="100">
					<template #default="scope">
						{{ functionIndex.transformTimestamp(scope.row.salesOutBound.outTime) }}
					</template>
				</el-table-column>
        <el-table-column align="center" label="商品名称" min-width="180" prop="commodity.tradeName"/>
        <el-table-column align="center" label="批号" prop="salesOutBound.batchNumber"/>
        <el-table-column align="center" label="仓库" prop="storages"/>
        <el-table-column align="center" label="货位" prop="goodsPosition.goodsShelvesId"/>
        <el-table-column align="center" label="出库数量" prop="salesOutBound.outQuantity"/>
        <el-table-column align="center" label="可调数量" prop="allowAdjustQuantity"/>
				<el-table-column align="center" label="单价" prop="uintPrice">
					<template #default="scope">
						{{ scope.row.uintPrice.toFixed(2) }}
					</template>
				</el-table-column>
        <el-table-column align="center" label="金额" prop="outBoundAmount"/>
			</el-table>
			<table border="0" cellpadding="0" cellspacing="1" class="messTable">
				<tr>
					<td>调价说明</td>
					<td>{{ listForm?.adjustExplain }}</td>
				</tr>
				<tr>
					<td>附件</td>
					<td>
						<div v-if="listForm.files"
                 style="padding: 0 0 10px 0;display: grid;grid-template-columns: 1fr 1fr 1fr;">
              <img v-for="(item, index) in  JSON.parse(listForm.files)" :key="index" :src="item.resFileUrl"
                   style="height: 100px;margin-right: 10px;margin-top: 10px" @click="checkImg(item)"/>
						</div>
					</td>
				</tr>
			</table>
			<h4 v-if="data.logFlag" class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px">操作记录</h4>
      <logQuery v-if="data.logFlag" ref="childLog"/>
      <h4 v-if="data.logFlag && !data.auditFlag" class="el-dialog__title" style="margin-bottom: 10px">审批意见</h4>
      <auditForms v-if="data.logFlag && !data.auditFlag" ref="auditRef" @refresh="refresh"/>
      <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="cancalFn()">取消</el-button>
				<el-button v-if="data.logFlag && !data.auditFlag" type="primary" @click="rightForm()">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
import {manageApi, priceApi} from "@/api/model/salesManagement";
import {functionIndex} from "../functionIndex";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const data = reactive({
	checkFlag: false,
	imgUrl: []
})
const auditRef = ref(null)
const tableList = ref([])
const emit = defineEmits(['getList'])
const props = defineProps({})
const dialogVisible = ref(false)
const loadingFlag = ref(false)
const childLog = ref(null)
const listTable = ref([])
const listForm = ref({})
const statusType = ref(JSON.parse(localStorage.getItem('salesType')) || [])
const comeList = ref([])
const dialogVisible3 = ref(false)
const close = () => {
	data.checkFlag = false
}
const checkImg = (item) => {
	data.imgUrl = []
	data.imgUrl.push(item.resFileUrl)
	data.checkFlag = true;
}
const echo = (status) => {
	let searchValue = statusType.value.find(item => item.value == status).name
	return searchValue ? searchValue : '未知'
}
const rightForm = () => {
	auditRef.value.formSub(data.backId);
}
const refresh = () => {
	emit('getList')
	dialogVisible.value = false
}
const cancalFn = () => {
	listTable.value = []
	comeList.value = []
	listForm.value = {}
	dialogVisible.value = false
}
const renderKey = (row) => {
	return row.id
}
const logFn = async (row) => {
	if (childLog.value) {
		childLog.value.data.list = [];
	}
  const auditList = await priceApi.auditLists({"salesAdjust.id": row.id})
  const logList = await manageApi.logList({masterId: row.id})
	if (auditList.code == 200 && logList.code == 200) {
		childLog.value.timeFns(auditList.data.records, logList.data.records);
	}
}
const getList = (id, flag, flag2) => {
	loadingFlag.value = true
	data.backId = flag
	if (flag) {
		data.logFlag = true
	} else {
		data.logFlag = false
	}
	if (flag2) {
		data.auditFlag = true
	} else {
		data.auditFlag = false
	}
	priceApi.getDetail({
		adjustId: id
	}).then(res => {
		if (res.code == 200) {
			if (flag) {
				logFn(res.data.salesAdjust)
			}
			listForm.value = res.data.salesAdjust
			listTable.value = res.data.salesAdjustForms
			comeList.value.push(listTable.value[0]?.id)
		}
		loadingFlag.value = false
	})
}
const onchangeTable = (row) => {
	let num = comeList.value.indexOf(row.id)
	if (num === -1) {
		comeList.value.push(row.id)
	} else {
		comeList.value.splice(num, 1)
	}
}
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	dialogVisible,
	getList
})

</script>
<style lang='scss' scoped>
.titleH4 {
	margin-bottom: 20px;
	color: #000;
	font-weight: bolder;
	font-size: 15px;
}

.messTable {
	width: 100%;
	background-color: #eaedf3;
	display: grid;
	grid-template-columns: 1fr 1fr;
	padding: 1px 1px 0 1px;
	margin-top: 20px;

	tr {
		margin-bottom: 1px;
		display: flex;
		background: #fff;

		td {
			background-color: white;
			line-height: 40px;
		}

		td:nth-child(1) {
			flex: 1;
			padding: 0 10px;
			font-weight: bold;
			color: #505050;
			background: #f7f7f7;
		}

		td:nth-child(2) {
			color: #606266;
			padding: 0 10px;
			flex: 3
		}
	}
}
</style>
