<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form">
                <el-form-item label="保温箱编号" prop="incubatorNo" style="width: 230px">
                    <el-input v-model="queryParams.incubatorNo" clearable placeholder="请输入保温箱编号" @keyup.enter.native="getList" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="传感器编号" prop="deviceNo">
                    <el-input v-model="queryParams.deviceNo" clearable placeholder="请输入传感器编号" @keyup.enter.native="getList" />
                </el-form-item>
                <el-form-item label="保温箱绑定状态" prop="status" style="width: 240px">
                    <el-select v-model="queryParams.status" clearable filterable placeholder="请选择保温箱绑定状态" @change="getList">
                        <el-option v-for="dict in incubatorRecordStatusOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item class="date-screening" label="装箱时间" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="getList"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="getList" @resetQuery="resetQuery('queryForm')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 16px">
                <el-button icon="el-icon-printer" size="mini" type="primary" @click="customPrintClick">自定义传感器数据打印</el-button>
                <el-button icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                <el-button icon="el-icon-plus" size="mini" type="primary" @click="generateBindingCodeVisible = true">生成绑定码</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :tableID="'cabineBoxRecord'" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="cabineBoxRecord" ref="ColumnTable" v-loading="loading" :columns="columns" :data="dataList">
                <template #createDate="{ row }">
                    <span>{{ timeFormatting(row.createDate) }}</span>
                </template>
                <template #createBy="{ row }">
                    <span>{{ row.createBy.name }}</span>
                </template>
                <template #status="{ row }">
                    <span>{{ formDict(incubatorRecordStatusOptions, row.status) }}</span>
                </template>
                <!--      <template #incubatorOperateStatus="{ row }">-->
                <!--        <span>{{ formDict(incubatorUsageStatusOptions, row.incubatorOperateStatus) }}</span>-->
                <!--      </template>-->

                <template #opt="{ row }">
                    <el-button icon="el-icon-printer" link size="small" type="warning" @click="printBoxCode({ incubatorRecordId: row.id })">打印箱码</el-button>
                    <el-button v-if="row.status == '2'" icon="el-icon-close" link size="small" type="danger" @click="forcedUnbinding(row)">强制解绑</el-button>
                    <el-button v-if="row.status == '3'" icon="el-icon-info-filled" link size="small" type="danger" @click="unbindRecord(row)">解绑记录</el-button>
                    <el-button icon="el-icon-folder-opened" link size="small" type="primary" @click="packingDetailsClick(row)">装箱详情</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
        <!-- 自定义设备数据打印 -->
        <el-dialog v-model="customPrintDialog" title="自定义设备数据打印" width="40%">
            <el-form ref="customPrintForm" :model="customPrintParams" :rules="customPrintRules" label-width="auto" size="mini">
                <el-divider>设备参数</el-divider>
				<el-form-item label="车载设备" prop="isCarDevice">
					<el-switch v-model="customPrintParams.isCarDevice" active-text="是" inactive-text="否" active-value="1" inactive-value="0"/>
				</el-form-item>
				<el-form-item label="运输车辆" prop="carCode" v-if="customPrintParams.isCarDevice == '1'">
					<el-select v-model="customPrintParams.carCode" clearable filterable placeholder="请选择运输车辆" style="width: 100%" @change="getCarBindDevice">
						<el-option v-for="dict in carList" :key="dict.carCode" :label="dict.carCode" :value="dict.carCode"></el-option>
					</el-select>
					<div style="display: flex;gap: 5px;flex-wrap: wrap;">
						<el-tag type="primary" v-for="(item,index) in customPrintParams.deviceNoList" :key="index">{{item}}</el-tag>
					</div>

				</el-form-item>
                <el-form-item label="传感器" prop="deviceNo" v-else>
                    <el-input v-model="customPrintParams.deviceNo" placeholder="请输入设备编号"></el-input>
                </el-form-item>
                <el-form-item label="起止时间" prop="time">
                    <el-date-picker v-model="customPrintParams.time" align="right" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" style="width: 100%" type="datetimerange" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
                </el-form-item>
                <el-divider>打印参数</el-divider>
                <el-form-item label="运输单位" prop="transportCompany">
                    <el-select v-model="customPrintParams.transportCompany" clearable filterable placeholder="请选择运输单位" style="width: 100%">
                        <el-option v-for="dict in trafficUnitOptions" :key="dict.value" :label="dict.name" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="运单编号" prop="transOrderNo">
                    <el-select v-model="customPrintParams.transOrderNo" :loading="transOrderNoLoading" :remote-method="getOrderSimpleInfo" filterable placeholder="请输入运单号搜索" remote reserve-keyword style="width: 100%" @change="orderSelect">
                        <el-option v-for="(item, index) in orderOptions" :key="index" :label="item.transOrderNo" :value="item.transOrderNo"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="单据编号" prop="externalOrderNo">
                    <el-input v-model="customPrintParams.externalOrderNo" placeholder="请输入单据编号" />
                </el-form-item>
                <el-form-item label="发件公司" prop="sendCompany">
                    <el-input v-model="customPrintParams.sendCompany" placeholder="请输入发件公司" />
                </el-form-item>
                <el-form-item label="收件公司" prop="receiverCompany">
                    <el-input v-model="customPrintParams.receiverCompany" placeholder="请输入收件公司" />
                </el-form-item>
            </el-form>
            <template #footer class="dialog-footer">
                <el-button @click="customPrintDialog = false">取 消</el-button>
                <el-button :loading="customPrintLoading" type="primary" @click="customPrintDataPrint">确定</el-button>
            </template>
        </el-dialog>
        <!--    保温箱生成绑定码-->
        <el-dialog v-model="generateBindingCodeVisible" title="生成绑定码" width="650px">
            <el-form ref="generateBindingCodeForm" :model="generateBindingCodeForm" :rules="generateBindingCodeRules" label-width="auto">
                <el-form-item label="保温箱" prop="incubatorNoList">
                    <el-select v-model="generateBindingCodeForm.incubatorNoList" :filter-method="searchBoxNumberAndCode" clearable filterable multiple placeholder="请选择保温箱" style="width: 100%">
                        <el-option v-for="(item, idx) in incubatorNoList" :key="idx" :label="item.name" :value="item.serialNumber">
                            <span style="float: left">{{ item.name }}</span>
                            <span class="ml10" style="float: left; color: #8492a6">{{ formDict(incubatorUsageStatusOptions, item.operateStatus) }}</span>
                            <span style="float: right">{{ item.serialNumber }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer class="dialog-footer">
                <el-button @click="generateBindingCodeVisible = false">取 消</el-button>
                <el-button :disabled="generateBindingDisabled" type="primary" @click="generateBindingCodeClick(1)">确定生成 </el-button>
                <el-button :disabled="generateBindingDisabled" type="primary" @click="generateBindingCodeClick(2)">生成并打印 </el-button>
            </template>
        </el-dialog>
        <!--    装箱详情-->
        <el-drawer v-model="packingDetailsOpen" :title="packingDetailsTitle" direction="rtl" size="50%">
            <packingDetails v-if="packingDetailsOpen" :recordInfo="recordInfo" :type="packingDetailsType" @closeSlider="packingDetailsOpen = false"> </packingDetails>
        </el-drawer>
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import packingDetails from './packingDetails.vue';
import cabineBox from '@/api/cabineBox/cabineBox.js';
import orderManagement from '@/api/logisticsManagement/orderManagement';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration';
import printLabelView from '@/api/print/printLabelView.js';
import { ElMessage } from 'element-plus';

import moment from 'moment';
import response from "@/utils/response";

export default {
    name: 'cabineBoxRecord',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar,
        packingDetails
    },
    data() {
        return {
            showSearch: true,
            isShowAll: false,
            customPrintDialog: false,
            recordInfo: null, // 保温箱记录
            packingDetailsOpen: false,
            generateBindingCodeVisible: false,
            loading: false,
            incubatorRecordStatusOptions: [],
            queryParams: {
                current: 1,
                size: 10
            },
            customPrintParams: {
				isCarDevice:'0',
                sendCompany: '',
                receiverCompany: ''
            },
            generateBindingCodeForm: {
                incubatorNoList: []
            },
            dataList: [],
            columns: [
                { title: '保温箱编号', width: '110px', align: 'center', key: 'incubatorNo', columnShow: true },
                { title: '传感器编号', width: '110px', align: 'center', key: 'deviceNo', columnShow: true },
                // {title: '保温箱使用状态', width: '140px', align: 'center', key: 'incubatorOperateStatus', columnShow: true},
                { title: '保温箱绑定状态', width: '140px', align: 'center', key: 'status', columnShow: true },
                { title: '高温阈值', width: '140px', align: 'center', key: 'temperatureCeiling', columnShow: true },
                { title: '低温阈值', width: '140px', align: 'center', key: 'temperatureLower', columnShow: true },
                { title: '保温箱绑定时间', width: '170px', align: 'center', key: 'bindIncubatorTime', columnShow: true },
                { title: '保温箱解绑时间', width: '170px', align: 'center', key: 'finishIncubatorTime', columnShow: true },
                { title: '传感器绑定时间', width: '170px', align: 'center', key: 'bindDeviceTime', columnShow: true },
                { title: '传感器解绑时间', width: '170px', align: 'center', key: 'finishDeviceTime', columnShow: true },
                { title: '创建时间', width: '170px', align: 'center', key: 'createDate', columnShow: true },
                { title: '创建人', width: '170px', align: 'center', key: 'createBy', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '280px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            customPrintRules: {
				isCarDevice:[{ required: true, message: '请选择车载设备', trigger: ['change', 'blur'] }],
				carCode:[{ required: true, message: '请选择运输车辆', trigger: ['change', 'blur'] }],
                deviceNo: [
                    { required: true, message: '请输入设备编号', trigger: ['change', 'blur'] },
                    { pattern: /^[a-zA-Z0-9]+$/, message: '请输入正确的设备编号', trigger: ['change', 'blur'] }
                ],
                time: [{ required: true, message: '请选择起止时间', trigger: ['change', 'blur'] }],
                transportCompany: [{ required: true, message: '请选择运输单位', trigger: ['change', 'blur'] }],
                transOrderNo: [
                    { required: true, message: '请输入运单编号', trigger: ['change', 'blur'] },
                    { pattern: /^[a-zA-Z0-9]+$/, message: '请输入正确的运单编号', trigger: ['change', 'blur'] }
                ],
                externalOrderNo: [{ pattern: /^[a-zA-Z0-9]+$/, message: '请输入正确的单据编号', trigger: ['change', 'blur'] }],
                sendCompany: [{ required: true, message: '请输入发件公司', trigger: ['change', 'blur'] }],
                receiverCompany: [{ required: true, message: '请输入收件公司', trigger: ['change', 'blur'] }]
            },
            incubatorUsageStatusOptions: [], // 保温箱使用状态
            incubatorNoList: [], //保温箱 列表
            trafficUnitOptions: [], // 运输单位
            orderOptions: [], // 订单列表
            generateBindingDisabled: false,
            generateBindingCodeRules: {
                incubatorNoList: [{ required: true, message: '请选择保温箱', trigger: 'blur' }]
            }, // 生成绑定码表单验证
            customPrintLoading: false,
            transOrderNoLoading: false, // 运单搜索
            packingDetailsTitle: '', // 详情标题
            packingDetailsType: null, // 详情类型
            incubatorNoListAll: [],
			shortcuts: [
				{
					text: '无',
					value: (e) => {
						return [null, null];
					}
				},
				{
					text: '当天',
					value: (e) => {
						let now = moment(new Date()).format('YYYY-MM-DD');
						return [now, now];
					}
				},
				{
					text: '7天',
					value: () => {
						let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
						let end = moment(new Date()).format('YYYY-MM-DD');
						return [start, end];
					}
				},
				{
					text: '30天',
					value: () => {
						let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
						let end = moment(new Date()).format('YYYY-MM-DD');
						return [start, end];
					}
				}
			],
			carList:[], // 冷链车列表
        };
    },
    watch: {},
    created() {
		// 默认设置当天
		let now = moment(new Date()).format('YYYY-MM-DD');
		this.queryParams.queryTime = [now, now];
        this.getList();
        this.getDict();
        this.getIncubatorNoList();
    },
    computed: {
        timeFormatting(val) {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },
    methods: {
        /**
         * 解绑记录
         * @param row
         */
        unbindRecord(row) {
            this.packingDetailsOpen = true;
            this.packingDetailsTitle = '解绑记录';
            this.packingDetailsType = '2';
            this.recordInfo = row;
        },
        /*生成绑定码接口*/
        generateBindingCodeClick(type) {
            this.generateBindingDisabled = true;
            let incubatorList = this.generateBindingCodeForm.incubatorNoList.map((item) => {
                return { incubatorNo: item };
            });
            this.$refs['generateBindingCodeForm'].validate((valid) => {
                if (valid) {
                    cabineBox.generateIncubatorRecord({ incubatorNos: this.generateBindingCodeForm.incubatorNoList.toString() }).then((res) => {
                        if (res.code == 200) {
                            this.msgSuccess('生成绑定码成功');
                            this.$refs['generateBindingCodeForm'].resetFields();
                            if (type == 2) {
                                this.batchPrintBoxCode(res.data);
                            }
                            this.getList();
                            this.getIncubatorNoList();
                            this.generateBindingCodeVisible = false;
                        }
                        this.generateBindingDisabled = false;
                    });
                } else {
                    this.generateBindingDisabled = false;
                }
            });
        },
        /*打印多选项*/
        batchPrintBoxCode(data) {
            this.loading = true;
            let params = {
                templateId: '0b686154274a4a029ec5a2dfddd570b9',
                dataList: data
            };
            new Promise((resolve, reject) => {
                printLabelView.printLabel(params, data).then((res) => {
                    const binaryData = [];
                    binaryData.push(res);
                    //获取blob链接
                    let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
                    window.open(pdfUrl);
                });
                setTimeout(() => {
                    resolve();
                }, 20000);
            })
                .then((data) => {
                    this.msgSuccess('打印成功');
                    this.loading = false;
                })
                .catch((err) => {
                    this.msgError('打印失败');
                    this.loading = false;
                });
        },
        /**
         * 传感器数据打印查询订单信息
         */
        getOrderSimpleInfo(e) {
            this.transOrderNoLoading = true;
            orderManagement
                .listOrderDrug({ queryType: '1', transOrderNo: e })
                .then((res) => {
                    if (res.code && res.data.records) {
                        this.orderOptions = res.data.records;
                    } else {
                        this.msgError('数据获取失败');
                    }
                    this.transOrderNoLoading = false;
                })
                .catch((e) => {
                    this.transOrderNoLoading = false;
                });
        },
        /**
         * 弹出自定义打印弹出
         */
        customPrintClick() {
            this.customPrintParams = {
            	isCarDevice:'0',
			};
            this.getColdChainCarList();
            this.customPrintDialog = true;
        },
		/**
		 * 获取冷链车列表
		 */
		getColdChainCarList(){
			operationConfiguration.getColdChainCarList().then(res=>{
				if(res.code == 200){
					this.carList =  res.data;
				}
			})
		},
		/**
		 *  获取冷链车绑定设备列表
		 */
		getCarBindDevice(carCode){
			this.customPrintParams.deviceNoList = [];
			if(carCode){
				operationConfiguration.getCarBindDevice({carCode}).then(res=>{
					if(res.code == 200 && res.data && res.data.records && res.data.records.length > 0){
						this.customPrintParams.deviceNoList =  res.data.records.map(item=>item.deviceNo);
					}else{
						this.msgError('未查询到设备信息');
					}
				})
			}
		},
        /**
         * 选择运单
         * @param e
         */
        orderSelect(e) {
            let item = this.orderOptions.find((order) => order.transOrderNo === e);
            if (item) {
                this.customPrintParams.sendCompany = item.sendCompany;
                this.customPrintParams.receiverCompany = item.receiverCompany;
            }
        },
        /**
         * 自定义传感器数据打印
         */
        customPrintDataPrint() {
            this.$refs.customPrintForm.validate((valid) => {
                if (valid) {
                	if(this.customPrintParams.isCarDevice == '1' &&  this.customPrintParams.deviceNoList.length == 0){
						this.msgError('请选择已绑定传感器车辆');
					}
                	let params = {
							deviceNo: this.customPrintParams.isCarDevice == '1'?this.customPrintParams.deviceNoList.toString():this.customPrintParams.deviceNo,
							transOrderNo: this.customPrintParams.transOrderNo, // 运单号
							carCode: this.customPrintParams.carCode, // 车牌号
							startTime: this.customPrintParams.time[0],
							endTime: this.customPrintParams.time[1],
							transportCompany: this.customPrintParams.transportCompany, // 发货单位
							externalOrderNo: this.customPrintParams.externalOrderNo, // 外部订单号
							sendCompany: this.customPrintParams.sendCompany, // 发货单位
							receiverCompany: this.customPrintParams.receiverCompany, // 收货单位
							isCarDevice: this.customPrintParams.isCarDevice == '1'?true:false, // 是否为车辆传感器数据
						};
					this.customPrintLoading = true;
					orderManagement.getTemplateData(params).then(res=>{
						if(res.code == 200 && res.data){
							let printParams = {
								...res.data,
								startTime:res.data.tempDataList[0].dateTime,
								endTime:res.data.tempDataList[res.data.tempDataList.length-1].dateTime,
								source:'3'
							};
							orderManagement.devicePrint(printParams).then(response=>{
								this.customPrintLoading = false;
								this.customPrintDialog = false;
								this.customPrintParams = {
									isCarDevice:'0',
								};
								const binaryData = [];
								binaryData.push(response);
								//获取blob链接
								let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
								window.open(pdfUrl)

							}).catch(e=>{
								this.customPrintLoading = false;
							})
						} else {
							this.customPrintLoading = false;
							// this.msgError('未查到设备信息');
						}
					}).catch(e=>{
						this.customPrintLoading = false;
					})
                }
            });
        },
        /**
         * 搜索箱号与箱码
         */
        searchBoxNumberAndCode(e) {
            if (e) {
                // 从 this.incubatorNoListCopy 中搜索 deviceName deviceNumber 包含 e 的数据 忽略大小写 结果放入 this.incubatorNoList
                this.incubatorNoList = this.incubatorNoList.filter((item) => {
                    return item.name.toLowerCase().indexOf(e.toLowerCase()) > -1 || item.serialNumber.toLowerCase().indexOf(e.toLowerCase()) > -1;
                });
            } else {
                this.incubatorNoList = JSON.parse(JSON.stringify(this.incubatorNoListAll));
            }
        },
        /**
         * 获取保温箱列表
         */
        getIncubatorNoList() {
            cabineBox.findIncubator({ operateStatus: '0', size: 10000 }).then((res) => {
                if (res.code === 200) {
                    this.incubatorNoList = res.data.records || [];
                    this.incubatorNoListAll = res.data.records || [];
                } else {
                    this.msgError(res.msg);
                }
            });
        },
        /*强制解绑*/
        forcedUnbinding(row) {
            this.$confirm('是否确认强制解绑保温箱"' + row.incubatorNo + '"?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then((action) => {
                    if (action != 'confirm') {
                        this.msgError('解绑取消');
                        return false;
                    }
                    // 确认解绑操作
                    cabineBox
                        .updateIncubatorRecordStatus({
                            incubatorRecordId: row.id,
                            status: '3'
                        })
                        .then((res) => {
                            if (res.code == 200) {
                                this.getList();
                                this.msgSuccess('解绑成功');
                            } else {
                                this.msgError(res.msg);
                            }
                        });
                })
                .catch((action) => {
                    if (action == 'cancel') {
                        this.msgError('解绑取消');
                        return false;
                    }
                });
        },
        // 保温箱记录列表
        getList() {
            this.loading = true;
            let params = { ...this.queryParams };
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                params.beginBindIncubatorTime = this.queryParams.queryTime[0] + ' 00:00:00';
                params.endBindIncubatorTime = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                params.beginBindIncubatorTime = null;
                params.endBindIncubatorTime = null;
            }
            delete params.queryTime;
            cabineBox.cabineList(params).then((res) => {
                if (res.code === 200) {
                    this.loading = false;
                    this.dataList = res.data.records || [];
                    this.total = res.data.total || 0;
                } else {
                    this.loading = false;
                    this.msgError(res.msg);
                }
            });
        },
        resetQuery(formName) {
            this.queryParams = {
                current: 1,
                size: 10,
                queryTime: [moment(new Date()).format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')]
            };
            this.getList();
        },
        handleExport() {
            let params = { ...this.queryParams };
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                params.beginBindIncubatorTime = this.queryParams.queryTime[0] + ' 00:00:00';
                params.endBindIncubatorTime = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                params.beginBindIncubatorTime = null;
                params.endBindIncubatorTime = null;
            }
            delete params.queryTime;
            cabineBox.exportIncubatorRecord({ filename: '保温箱记录列表.xls', ...params }, '', '', 'blob').then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '保温箱记录列表.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                    this.msgSuccess('保温箱记录列表导出任务已生成！');
                } else {
                    this.msgError('导出异常请联系管理员');
                }
            });
        },
        // // 标准时间格式化
        // formatDate(cellValue) {
        //   return formatDate(cellValue);
        // },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /*打印箱码*/
        printBoxCode(row) {
            this.loading = true;
            let params = {
                templateId: '0b686154274a4a029ec5a2dfddd570b9',
                data: {}
            };
            // let that = this;
            cabineBox
                .printIncubatorRecord(row)
                .then((res) => {
                    if (res.code == 200) {
                        params.data = res.data;
                        new Promise((resolve, reject) => {
                            printLabelView.printPdfWithData(params).then((res) => {
                                const binaryData = [];
                                binaryData.push(res);
                                //获取blob链接
                                let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
                                window.open(pdfUrl);
                            });
                            resolve();
                        })
                            .then((data) => {
                                this.msgSuccess('打印成功');
                                this.loading = false;
                            })
                            .catch((err) => {
                                this.msgError('打印失败');
                                this.loading = false;
                            });
                    } else {
                        this.msgError('打印失败');
                        this.loading = false;
                    }
                })
                .catch((err) => {
                    this.loading = false;
                });
        },

        formDict(typeList, value) {
            return this.selectDictLabel(typeList, value);
        },

        /*打开装箱详情*/
        packingDetailsClick(row) {
            this.packingDetailsOpen = true;
            this.packingDetailsTitle = '装箱详情';
            this.packingDetailsType = '1';
            this.recordInfo = row;
        },
        async getDict() {
            // 保温箱绑定状态
            this.incubatorRecordStatusOptions = await this.getDictList('fourpl_incubator_record_status');
            // 保温箱使用状态
            this.incubatorUsageStatusOptions = await this.getDictList('incubator_use_status');
            // 运输单位
            this.trafficUnitOptions = await this.getDictList('inc_traffic_unit');
        }
    }
};
</script>
<style lang="scss" scoped>
.app-container {
    padding: 10px;
}
@media screen and (max-width: 1366px) {
    .seache-form .el-form-item:not(.last-form-item) {
        width: 257px;
    }
}
</style>