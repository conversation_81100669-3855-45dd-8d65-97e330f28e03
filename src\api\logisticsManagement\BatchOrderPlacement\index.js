import http from "@/utils/request"

export default {
    // 查询已合作货主下拉框
    cooperateSelect: function (params) {
        return http.get('/tms/cooperate/company/cooperateSelect', params)
    },
    // 根据id获取地址薄地址信息
    addressBookList: function (params) {
        return http.get('/tms/config/addressBook/list', params)
    },
    // 批量下单-立即下单(导入数据)
    batchImportOrder: function (data) {
        return http.post("/tms/orderDrug/batchImportOrder", data);
    },
    // 附件下载
    downloadTemplate: function (params, config, resDetail, responseType) {
        return http.get("/tms/orderDrug/downloadTemplate", params, config, resDetail, responseType);
    },
    //网点查询
    branchList: function (params) {
        return http.get('/tms/branch/getBranchList', params)
    },


    // 货主
    carrierSelect: function (params) {
        return http.get('/tms/cooperate/company/carrierSelect', params)
    },
    // 查询网点，货主端
    // getBranchList: function (params) {
    //     return http.get('/tms/branch/getBranchList', params)
    // },
    
}