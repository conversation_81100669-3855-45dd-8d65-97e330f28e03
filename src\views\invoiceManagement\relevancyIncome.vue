<template>
	<div class="ApplicationSearch">
		<relevancySearch ref="searchRef" @handleQuery="handleQuery"/>
		<el-card body-style="padding-top:0;padding-bottom:10;" class="box-card last-card" style="margin-top: 10px">
			<template #header>
				<div class="card-header">
          <span>
            <el-button class="button" type="primary" @click="newAddTable()">创建申请（采购入库）</el-button>
            <el-button class="button" type="primary" @click="newAddTable2()">创建申请（采退出库）</el-button>
          </span>
				</div>
			</template>
			<div class="item" style="margin-top:10px">
				<DragTableColumn v-if="statusList.length" v-loading="loadingFlag"
								 v-model:queryParams="searchRef.searchForm"
								 :columns="columns" :getList="handleQuery" :row-style="tableRowStyle"
								 :tableData="tableList"
								 className="invoiceManagement_relevancyIncome">
					<template v-slot:operate="{ scopeData }">
						<el-button :disabled="scopeData.row.auditStatus != 0 && scopeData.row.auditStatus != 7" link
								   type="primary"
								   @click="() => {
									scopeData.row.type == '1' ? editTable(scopeData.row, '编辑') : editTable2(scopeData.row, '编辑')
								  }"><img src="@/assets/icons/update.png" style="margin: 0px 5px 0 0"/>编辑
						</el-button>
						<el-button :disabled="scopeData.row.auditStatus != 0 && scopeData.row.auditStatus != 7" link
								   type="danger"
								   @click="deltable(scopeData.row)"><img src="@/assets/icons/delete.png"
																		 style="margin: 0px 5px 0 0"/>删除
						</el-button>
						<el-button link type="primary" @click="detailTable(scopeData.row, scopeData.row.type)"><img
							src="@/assets/icons/detail.png" style="margin: 2px 5px 0 0"/>详情
						</el-button>
						<el-button link type="success" @click="logFn(scopeData.row)"><img
							src="@/assets/icons/review.png"
							style="margin: 0px 2px 0 0"/>操作日志
						</el-button>
					</template>
				</DragTableColumn>
				<el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
							   :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
							   layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
							   @size-change="handleQuery"
							   @current-change="handleQuery"/>
			</div>
		</el-card>

		<el-dialog v-model="dialogVisible" :before-close="handleClose" :title="data.title" width="80%">
			<div v-loading="editFlag">
				<relevancyDialog ref="formMess"/>
			</div>
			<template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitFn()">
            提交申请
          </el-button>
        </span>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisibleOut" :before-close="handleClose2" :title="data.title" width="80%">
			<div v-loading="editFlag2">
				<relevancyDialog2 ref="formMess2"/>
			</div>
			<template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose2()">取消</el-button>
          <el-button type="primary" @click="submitFn2()">
            提交申请
          </el-button>
        </span>
			</template>
		</el-dialog>

		<el-dialog v-model="dialogVisible2" title="查看开票详情" width="80%">
			<h4 slot="title" class="stateTitle">
				审核状态: {{ echo(data.row.auditStatus) }}
			</h4>
			<div v-loading="checkFlag">
				<relevancyDetails :detailType="data.detailType" :price="data.price" :strs="data.row"
								  :table1="data.detailTable1"
								  :table2="data.detailTable2"/>
			</div>
			<template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible2 = false">取消</el-button>
        </span>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisible3" title="操作记录" width="30%">
			<div v-loading="logFlag">
				<logQuery ref="childLog"/>
			</div>
			<template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible3 = false">取消</el-button>
        </span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import relevancySearch from './components/relevancySearch.vue'
import {ElMessage, ElMessageBox} from "element-plus";
import relevancyDialog from './components/relevancyDialog.vue'
import relevancyDialog2 from './components/relevancyDialog2.vue'
import relevancyDetails from './components/relevancyDetails.vue'
import {relevanceInput} from '@/api/model/invoice'
import {functionIndex} from "@/views/salesManagement/functionIndex";

const {proxy} = getCurrentInstance();
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)

const data = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
	title: "",
	detailTable1: [],
	detailTable2: [],
	price: 0,
	row: {}
})
const childLog = ref(null)
const logFlag = ref(false)
const checkFlag = ref(false)
const editFlag = ref(false)
const editFlag2 = ref(false)
const dialogVisible = ref(false)
const formMess = ref(null)
const formMess2 = ref(null)
const dialogVisibleOut = ref(false)
const handleClose = (done) => {
	ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			dialogVisible.value = false
			emptyFn()
		})
		.catch(() => {
		});
}
const handleClose2 = (done) => {
	ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			dialogVisibleOut.value = false
			emptyFn2()
		})
		.catch(() => {
		});
}
const emit = defineEmits([])
const props = defineProps({})
const searchRef = ref(null)
const loadingFlag = ref(false)
const dialogVisible2 = ref(false)
const dialogVisible3 = ref(false)
const tableList = ref()
const editTable = (row) => {
	editFlag.value = true
	dialogVisible.value = true
	data.title = '编辑申请'
	relevanceInput.detailList({
		id: row.id
	}).then(res => {
		formMess.value.chooseData = []
		if (res.code == 200) {
			res.data.erpPurchaseInvoiceFormDtos.forEach((item) => {
				formMess.value.chooseData.push(
					{
						oldId: item.id,
						orderCode: item.purchaseNo,
						commodity: {
							tradeName: item.commodityName,
							commoditySelfCode: item.commoditySelfCode,
							packageSpecification: item.commodityPackageSpecification,
							manufactureName: item.commodityManufactureName,
							producingArea: item.commodityProducingArea,
							commodityCode: item.commodityCode,
						},
						intoTime: item.intoDate,
						purchaseCreatedDate: item.purchaseCreateDate,
						invoicingInfo: item.invoicingInfo?.split(','),
						unitPrice: item.commodityUnitPrice,
						receivingQuantity: item.inboundQuantity,
						allPrice: item.invoicingAmount,
						basicUnit: item.commodityBasicUnit,
						validate: item.commodityValidityTime,
						batchNumber: item.batchNo,
						produceDate: item.commodityProduceDate,
						suppier: item.supplierName,
					}
				)
			})
			formMess.value.formData.n1 = ''
			formMess.value.formData.n2 = 0
			formMess.value.formData.n3 = 0
			formMess.value.formData.n4 = ''
			formMess.value.formData.n5 = ''
			formMess.value.formData.n6 = ''
			formMess.value.formData.n1 = res.data.erpPurchaseInvoiceDto.remark
			formMess.value.chooseData?.forEach(item => {
				formMess.value.formData.n2 += item.unitPrice * item.receivingQuantity
			})
			formMess.value.formData.n3 = res.data.erpPurchaseInvoiceDto.invoiceAmount
			formMess.value.formData.n4 = res.data.erpPurchaseInvoiceDto.supplier.id
			formMess.value.formData.n5 = '1'//TODO
			formMess.value.formData.n6 = res.data.erpPurchaseInvoiceDto.id
			formMess.value.invoiceList = []
			res.data.erpPurchaseInvoiceUnionDtos?.forEach((item, index) => {
				formMess.value.invoiceList.push({
					oldId: item.id,
					invoiceNo: item.purchaseInvoiceReceipt.invoiceNo,
					invoiceCode: item.purchaseInvoiceReceipt.invoiceCode,
					invoiceSupplier: item.purchaseInvoiceReceipt.invoiceSupplier,
					taxpayerNo: item.purchaseInvoiceReceipt.taxpayerNo,
					invoicingDate: item.purchaseInvoiceReceipt.invoicingDate,
					invoicingAmount: item.purchaseInvoiceReceipt.invoicingAmount,
					excludingTax: item.purchaseInvoiceReceipt.excludingTax,
					totalTax: item.purchaseInvoiceReceipt.totalTax,
					effectiveTax: item.purchaseInvoiceReceipt.effectiveTax,
					fileDtos: []
				})
				item.purchaseInvoiceReceipt.fileDtos?.forEach(item => {
					formMess.value.invoiceList[index].fileDtos.push({
						id: item.id,
						name: item.fileName,
						url: item.fileUrl
					})
				})
			})
		}
		editFlag.value = false
	})
}
const editTable2 = (row) => {
	editFlag2.value = true
	dialogVisibleOut.value = true
	data.title = '编辑申请'
	relevanceInput.detailList({
		id: row.id
	}).then(res => {
		formMess2.value.chooseData = []
		if (res.code == 200) {
			res.data.erpPurchaseInvoiceFormDtos.forEach((item) => {
				formMess2.value.chooseData.push(
					{
						oldId: item.id,
						purchaseOrder: item.purchaseNo,
						commodity: {
							tradeName: item.commodityName,
							commoditySelfCode: item.commoditySelfCode,
							packageSpecification: item.commodityPackageSpecification,
							manufactureName: item.commodityManufactureName,
							producingArea: item.commodityProducingArea,
							commodityCode: item.commodityCode,
						},
						num: item.invoicingQuantity,
						outTime: item.intoDate,
						orderTime: item.purchaseCreateDate,
						unitPrice: item.commodityUnitPrice,
						outQuantity: item.inboundQuantity,
						allPrice: item.invoicingAmount,
						basicUnit: item.commodityBasicUnit,
						expirevalidate: item.commodityValidityTime,
						batchNumber: item.batchNo,
						produceDate: item.commodityProduceDate,
						suppier: item.supplierName,
					}
				)
			})
			formMess2.value.formData.n1 = ''
			formMess2.value.formData.n2 = 0
			formMess2.value.formData.n3 = 0
			formMess2.value.formData.n4 = ''
			formMess2.value.formData.n5 = ''
			formMess2.value.formData.n6 = ''
			formMess2.value.formData.n1 = res.data.erpPurchaseInvoiceDto.remark
			formMess2.value.chooseData?.forEach(item => {
				formMess2.value.formData.n2 += item.unitPrice * item.receivingQuantity
			})
			formMess2.value.formData.n3 = res.data.erpPurchaseInvoiceDto.invoiceAmount
			formMess2.value.formData.n4 = res.data.erpPurchaseInvoiceDto.supplier.id
			formMess2.value.formData.n5 = '1'//TODO
			formMess2.value.formData.n6 = res.data.erpPurchaseInvoiceDto.id
			formMess2.value.invoiceList = []
			res.data.erpPurchaseInvoiceUnionDtos?.forEach((item, index) => {
				formMess2.value.invoiceList.push({
					oldId: item.id,
					invoiceNo: item.purchaseInvoiceReceipt.invoiceNo,
					invoiceCode: item.purchaseInvoiceReceipt.invoiceCode,
					invoiceSupplier: item.purchaseInvoiceReceipt.invoiceSupplier,
					taxpayerNo: item.purchaseInvoiceReceipt.taxpayerNo,
					invoicingDate: item.purchaseInvoiceReceipt.invoicingDate,
					invoicingAmount: item.purchaseInvoiceReceipt.invoicingAmount,
					excludingTax: item.purchaseInvoiceReceipt.excludingTax,
					totalTax: item.purchaseInvoiceReceipt.totalTax,
					effectiveTax: item.purchaseInvoiceReceipt.effectiveTax,
					fileDtos: []
				})
				item.purchaseInvoiceReceipt.fileDtos?.forEach(item => {
					formMess2.value.invoiceList[index].fileDtos.push({
						id: item.id,
						name: item.fileName,
						url: item.fileUrl
					})
				})
			})
		}
		editFlag2.value = false
	})
}
const detailTable = (row, num) => {
	checkFlag.value = true
	dialogVisible2.value = true
	data.detailTable1 = []
	data.price = 0
	data.row = row
	console.log(row)
	data.detailType = num
	data.detailTable2 = []
	relevanceInput.detailList({
		id: row.id
	}).then(res => {
		if (res.code == 200) {
			res.data.erpPurchaseInvoiceFormDtos.forEach((item) => {
				data.detailTable2.push(
					{
						id: item.id,
						orderCode: item.purchaseNo,
						commodity: {
							tradeName: item.commodityName,
							commoditySelfCode: item.commoditySelfCode,
							packageSpecification: item.commodityPackageSpecification,
							manufactureName: item.commodityManufactureName,
							producingArea: item.commodityProducingArea,
							commodityCode: item.commodityCode,
							produceDate: item.commodityProduceDate
						},
						checkFlag: false,
						invoicingInfo: item.invoicingInfo?.split(','),
						unitPrice: item.commodityUnitPrice.toFixed(2),
						receivingQuantity: item.inboundQuantity,
						allPrice: item.invoicingAmount.toFixed(2),
						basicUnit: item.commodityBasicUnit,
						validate: item.commodityValidityTime,
						invoicingQuantity: item.invoicingQuantity,
						purchaseCreatedDate: item.purchaseCreatedDate,
						batchNumber: item.batchNo,
						produceDate: item.produceDate,
						purchaseCreateDate: item.purchaseCreateDate,
						intoDate: item.intoDate,
						suppier: item.supplierName,
					}
				)
			})
			data.price = res.data.erpPurchaseInvoiceDto.invoiceAmount
			res.data.erpPurchaseInvoiceUnionDtos.forEach((item, index) => {
				data.detailTable1.push({
					id: item.id,
					invoiceNo: item.purchaseInvoiceReceipt.invoiceNo,
					invoiceCode: item.purchaseInvoiceReceipt.invoiceCode,
					invoiceSupplier: item.purchaseInvoiceReceipt.invoiceSupplier,
					taxpayerNo: item.purchaseInvoiceReceipt.taxpayerNo,
					invoicingDate: item.purchaseInvoiceReceipt.invoicingDate,
					invoicingAmount: item.purchaseInvoiceReceipt.invoicingAmount,
					excludingTax: item.purchaseInvoiceReceipt.excludingTax,
					totalTax: item.purchaseInvoiceReceipt.totalTax,
					effectiveTax: item.purchaseInvoiceReceipt.effectiveTax,
          type: item.purchaseInvoiceReceipt.type,
					fileDtos: []
				})
				item.purchaseInvoiceReceipt.fileDtos.forEach(item => {
					data.detailTable1[index].fileDtos.push({
						id: item.id,
						name: item.fileName,
						url: item.fileUrl
					})
				})
			})
		}
		checkFlag.value = false
	})
}
const deltable = (row) => {
	ElMessageBox.confirm("确认删除此项吗?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			relevanceInput.delList({ids: row.id}).then(res => {
				if (res.code == 200) {
					ElMessage.success('删除成功')
					handleQuery()
				} else {
					ElMessage.error(res.msg)
				}
			})
		})
		.catch(() => {
		});
}
const logFn = async (row) => {
	logFlag.value = true
	dialogVisible3.value = true
	if (childLog.value) {
		childLog.value.data.list = [];
	}
	const auditList = await relevanceInput.auditLists({"purchaseInvoice.id": row.id})
	const logList = await relevanceInput.logList({masterId: row.id})
	if (auditList.code == 200 && logList.code == 200) {
		childLog.value.timeFns(auditList.data.records, logList.data.records);
	} else {
		ElMessage.error('加载失败')
	}
	logFlag.value = false
}
const echo = (value) => {
	return statusList.value.find((item) => item.value === value).name
}
const emptyFn = () => {
	setTimeout(() => {
		formMess.value.chooseData = []
		formMess.value.invoiceList = []
		formMess.value.receiptsList = []
		for (let key in formMess.value.formData) {
			if (key == 'n2' || key == 'n3') {
				formMess.value.formData[key] = 0
			} else {
				formMess.value.formData[key] = ''
			}
		}
		for (let key in formMess.value.searchForm) {
			formMess.value.searchForm[key] = ''
		}
	})
}
const emptyFn2 = () => {
	setTimeout(() => {
		formMess2.value.chooseData = []
		formMess2.value.invoiceList = []
		formMess2.value.receiptsList = []
		for (let key in formMess2.value.formData) {
			if (key == 'n2' || key == 'n3') {
				formMess2.value.formData[key] = 0
			} else {
				formMess2.value.formData[key] = ''
			}
		}
		for (let key in formMess2.value.searchForm) {
			formMess2.value.searchForm[key] = ''
		}
	})
}
const cancalFn = () => {
	emptyFn()
	dialogVisible.value = false
}
const submitFn = () => {
	if (formMess.value.chooseData.length <= 0) {
		ElMessage.error('请选择入库单据')
	} else if (formMess.value.invoiceList.length <= 0) {
		ElMessage.error('请添加发票信息')
	} else if (formMess.value.formData.n2 < formMess.value.formData.n3) {
		ElMessage.error('发票金额请勿大于待开票金额')
	} else {
		console.log(formMess.value.chooseData);
		let newArr = []
		let newId = []
		formMess.value.chooseData.forEach((item) => {
			let newInvoices = []
			item.purchaseAdjustPrices.forEach(items => {
				newInvoices.push({
					invoiceQuantity: items.num,
					invoicePrice: items.num * items.adjustUnitPrice,
					commodityId: item.commodity.id,
					purchaseAdjustPrice: {
						id: items.id
					}
				})
			})
			newArr.push({
				"id": item.oldId ? item.oldId : null,
				"purchaseNo": item.orderCode,
				'purchaseRecordId': item.id,
				"commodity": item.commodity,
				"commodityName": item.commodity.tradeName,
				"commoditySelfCode": item.commodity.commoditySelfCode,
				"commodityPackageSpecification": item.commodity.packageSpecification,
				"commodityManufactureName": item.commodity.manufactureName,
				"commodityProducingArea": item.commodity.producingArea,
				"supplier": {
					id: formMess.value.formData.n4
				},
				'handledBy': {
					id: formMess.value.formData.n5
				},
				'formPriceDtos': newInvoices,
				'invoicingInfo': item.invoicingInfo.join(','),
				"supplierName": item.suppier,
				"commodityProduceDate": item.produceDate,
				"batchNo": item.batchNumber,
				'purchaseCreateDate': item.purchaseCreatedDate,
				'intoDate': item.intoTime,
				"commodityValidityTime": item.validate,
				"commodityBasicUnit": item.basicUnit,
				"commodityCode": item.commodity.commodityCode,
				"commodityUnitPrice": item.unitPrice,
				"inboundQuantity": item.receivingQuantity,
				"invoicingAmount": item.allPrice,
				"remark": item.remark
			})
		})
		formMess.value.invoiceList.forEach((item) => {
			newId.push(
				{
					"id": item.oldId ? item.oldId : null,
					"purchaseInvoiceReceipt": {
						"id": item.id,
						'invoiceNo': item.invoiceNo,
						'taxpayerNo': item.taxpayerNo,
						'invoiceSupplier': item.invoiceSupplier,
						'invoicingAmount': item.invoicingAmount,
						'invoicingDate': item.invoicingDate,
						'invoiceCode': item.invoiceCode
					}
				}
			)
		})
		console.log(newArr, newId);
		relevanceInput.saveInput({
			erpPurchaseInvoiceDto: {
				'type': 1,
				"id": formMess.value.formData.n6 ? formMess.value.formData.n6 : null,
				"applyNo": null,
				"applyDate": null,
				"applyBy": null,
				"invoiceNo": null,
				"invoiceSupplier": null,
				"taxpayerNo": null,
				"invoicingDate": null,
				"invoiceAmount": formMess.value.formData.n3,
				"supplier": {
					id: formMess.value.formData.n4
				},
				'handledBy': {
					id: formMess.value.formData.n5
				},
				"supplierName": null,
				"invoiceOrigin": null,
				"remark": formMess.value.formData.n1
			},
			erpPurchaseInvoiceFormDtos: newArr,
			erpPurchaseInvoiceUnionDtos: newId,
			"formType": "submit"
		}).then((res) => {
			if (res.code == 200) {
				dialogVisible.value = false
				emptyFn()
				handleQuery()
				ElMessage.success('提交审核成功')
			} else {
				ElMessage.error(res.msg)
			}
		})
	}
}
const submitFn2 = () => {
	if (formMess2.value.chooseData.length <= 0) {
		ElMessage.error('请选择入库单据')
	} else if (formMess2.value.invoiceList.length <= 0) {
		ElMessage.error('请添加发票信息')
	} else if (formMess2.value.formData.n2 < formMess2.value.formData.n3) {
		ElMessage.error('发票金额请勿大于待开票金额')
	} else {
		console.log(formMess2.value.chooseData);
		let newArr = []
		let newId = []
		formMess2.value.chooseData.forEach((item) => {
			newArr.push({
				"id": item.oldId ? item.oldId : null,
				"purchaseNo": item.purchaseOrder,
				'purchaseRecordId': item.id,
				"commodity": item.commodity,
				"commodityName": item.commodity.tradeName,
				"commoditySelfCode": item.commodity.commoditySelfCode,
				"commodityPackageSpecification": item.commodity.packageSpecification,
				"commodityManufactureName": item.commodity.manufactureName,
				"commodityProducingArea": item.commodity.producingArea,
				"supplier": {
					id: formMess2.value.formData.n4
				},
				'handledBy': {
					id: formMess2.value.formData.n5
				},
				"supplierName": item.suppier,
				"commodityProduceDate": item.produceDate,
				"batchNo": item.batchNumber,
				'purchaseCreateDate': item.orderTime,
				'intoDate': item.outTime,
				"commodityValidityTime": item.expirevalidate,
				"commodityBasicUnit": item.basicUnit,
				"commodityCode": item.commodity.commodityCode,
				"commodityUnitPrice": item.unitPrice,
				"inboundQuantity": item.outQuantity,
				"invoicingAmount": item.allPrice,
				'invoicingQuantity': item.num,
				"remark": item.remark
			})
		})
		formMess2.value.invoiceList.forEach((item) => {
			newId.push(
				{
					"id": item.oldId ? item.oldId : null,
					"purchaseInvoiceReceipt": {
						"id": item.id,
						'invoiceNo': item.invoiceNo,
						'taxpayerNo': item.taxpayerNo,
						'invoiceSupplier': item.invoiceSupplier,
						'invoicingAmount': item.invoicingAmount,
						'invoicingDate': item.invoicingDate,
						'invoiceCode': item.invoiceCode
					}
				}
			)
		})
		console.log(newArr, newId);
		relevanceInput.saveInput({
			erpPurchaseInvoiceDto: {
				'type': 2,
				"id": formMess2.value.formData.n6 ? formMess2.value.formData.n6 : null,
				"applyNo": null,
				"applyDate": null,
				"applyBy": null,
				"invoiceNo": null,
				"invoiceSupplier": null,
				"taxpayerNo": null,
				"invoicingDate": null,
				"invoiceAmount": formMess2.value.formData.n3,
				"supplier": {
					id: formMess2.value.formData.n4
				},
				'handledBy': {
					id: formMess2.value.formData.n5
				},
				"supplierName": null,
				"invoiceOrigin": null,
				"remark": formMess2.value.formData.n1
			},
			erpPurchaseInvoiceFormDtos: newArr,
			erpPurchaseInvoiceUnionDtos: newId,
			"formType": "submit"
		}).then((res) => {
			if (res.code == 200) {
				dialogVisibleOut.value = false
				emptyFn2()
				handleQuery()
				ElMessage.success('提交审核成功')
			} else {
				ElMessage.error(res.msg)
			}
		})
	}
}
const newAddTable = () => {
	data.title = '创建申请'
	emptyFn()
	dialogVisible.value = true
}
const newAddTable2 = () => {
	data.title = '创建申请'
	emptyFn2()
	dialogVisibleOut.value = true
}
const changeTime = (time) => {
	if (time) {
		let newTime = new Date(time)
		newTime = newTime.setDate(newTime.getDate() + 1);
		newTime = functionIndex.transformTimestampSearch(newTime)
		return newTime
	} else {
		return null
	}
}
const handleQuery = () => {
	loadingFlag.value = true
	relevanceInput.getList({
		size: data.pageSize,
		current: data.pageNum,
		supplierName: searchRef.value.searchForm.n1,
		applyNo: searchRef.value.searchForm.n2,
		invoiceNo: searchRef.value.searchForm.n3,
		invoiceCode: searchRef.value.searchForm.n4,
		applyByName: searchRef.value.searchForm.n5,
		beginCreateDate: searchRef.value.searchForm?.n6[0],
		endCreateDate: changeTime(searchRef.value.searchForm?.n6[1]),
		auditStatus: searchRef.value.searchForm.n7,
		type: searchRef.value.searchForm.n8
	}).then(res => {
		if (res.code == 200) {
			data.total = res.data.total
			tableList.value = res.data.records
		}
		loadingFlag.value = false
	})
}
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	setTimeout(() => {
		handleQuery()
	})

})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	...toRefs(data)
})
const statusList = ref()
const dick = async () => {
	statusList.value = await proxy.getDictList("erp_review_status");
}
dick()
const columns = ref([
	{label: '申请单据编号', prop: 'applyNo', minWidth: '170'},
	{label: '申请日期', prop: 'createDate', type: "date"},
	{label: '申请人', prop: 'createBy.name'},
	// {label: '发票号码', prop: 'invoiceNo'},
	{label: '发票供应商', prop: 'invoiceSupplier'},
	{label: '纳税人识别号', prop: 'taxpayerNo'},
	{label: '发票日期', prop: 'invoicingDate', type: "date"},
	{label: '发票金额', prop: 'invoiceAmount'},
	{label: '供应商', prop: 'supplier.enterpriseName'},
	{label: '审核状态', prop: 'auditStatus', type: 'status', filters: statusList, searchKey: 'n7'},
	{
		label: '类型', prop: 'type', searchKey: 'n8', type: "status", filters: [
			{
				name: '采购入库',
				value: '1'
			},
			{
				name: '采退出库',
				value: '2'
			}
		],
	},
	{label: '操作', prop: 'operate', minWidth: "300px", type: 'operate', fixed: 'right'},
])
const tableRowStyle = ({row}) => {
	if (row.auditStatus == "1") {
		return {
			color: '#409eff'
		}
	} else if (row.auditStatus == "2") {
		return {
			color: '#67c23a'
		}
	} else if (row.auditStatus == "4") {
		return {
			color: '#ff4800'
		}
	} else if (row.auditStatus == "6") {
		return {
			color: '#e6a23c'
		}
	} else if (row.auditStatus == "7") {
		return {
			color: '#ff4800'
		}
	} else if (row.auditStatus == "0") {
		return {
			color: '#f56c6c'
		}
	}
}
</script>
<style lang='scss' scoped>
.ApplicationSearch {
	padding: 10px;
}

.stateTitle {
	position: absolute;
	font-size: 15px;
	top: 21px;
	right: 53px;
}
</style>
