<template>
    <div>
        <el-drawer v-model="visible" title="申请开票" @close="onClose" size="600px">
            <div v-loading="loading" element-loading-text="加载中..." style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="invoiceForm" :model="form" label-width="auto">
                        <el-form-item :rules="[{ required: true, message: '请输入开票金额', trigger: 'change' }]" label="开票金额" prop="invoiceAmount">
                            <el-input v-model="form.invoiceAmount" class="input__unit__element w-full" disabled></el-input>
                        </el-form-item>
                        <el-form-item v-if="form.advanceAccount" label="预付款账号" prop="advanceAccount">
                            <span>{{ form.advanceAccount }}</span>
                        </el-form-item>
                        <el-form-item :rules="[{ required: true, message: '请输入申请公司', trigger: 'change' }]" label="申请公司" prop="companyName">
                            <div class="flex items-center w-full">
                                <el-input v-model="form.companyName" :disabled="form.paymentDocType !== '4'" class="w-full" clearable maxlength="50" placeholder="请输入申请公司" />
                                <el-button v-if="form.paymentDocType === '4'" class="ml-10" type="primary" @click="handleCompanySearch">
                                    <el-icon><Search /></el-icon>
                                    <span>公司查询</span>
                                </el-button>
                            </div>
                        </el-form-item>
                        <el-form-item :rules="[{ required: true, message: '请选择发票抬头', trigger: 'change' }]" class="w-full" label="发票抬头" prop="invoiceData">
                            <el-select v-if="!isCollectPayment" v-model="form.invoiceData" class="w-full" clearable filterable placeholder="请选择发票抬头" value-key="id">
                                <el-option v-for="item in invoiceList" :key="item.id" :label="item.invoiceHead" :value="item">
                                    <div class="flex justify-between">
                                        <span>{{ item.invoiceHead }}</span>
                                        <span style="color: #8492a6; font-size: 12px">{{ item.taxNo }}</span>
                                    </div>
                                </el-option>
                            </el-select>
                            <el-input v-else v-model="form.invoiceData.invoiceHead" class="w-full" clearable maxlength="50" placeholder="请输入发票抬头" />
                        </el-form-item>
                        <div v-if="form.invoiceData">
                            <el-form-item :rules="[{ required: isCollectPayment && form.paymentDocType !== '4', message: '请输入税号', trigger: 'blur' }]" label="税号" prop="invoiceData.taxNo">
                                <span v-if="!isCollectPayment">{{ form.invoiceData.taxNo }}</span>
                                <el-input v-else v-model="form.invoiceData.taxNo" class="w-full" clearable maxlength="50" placeholder="请输入税号" />
                            </el-form-item>
                            <el-form-item label="地址" prop="invoiceData.address">
                                <span v-if="!isCollectPayment">{{ form.invoiceData.address }}</span>
                                <el-input v-else v-model="form.invoiceData.address" class="w-full" clearable maxlength="100" placeholder="请输入地址" />
                            </el-form-item>
                            <el-form-item label="电话" prop="invoiceData.phone">
                                <span v-if="!isCollectPayment">{{ form.invoiceData.phone }}</span>
                                <el-input v-else v-model="form.invoiceData.phone" class="w-full" clearable maxlength="20" placeholder="请输入电话" />
                            </el-form-item>
                            <el-form-item label="开户银行" prop="invoiceData.openBank">
                                <span v-if="!isCollectPayment">{{ form.invoiceData.openBank }}</span>
                                <el-input v-else v-model="form.invoiceData.openBank" class="w-full" clearable maxlength="50" placeholder="请输入开户银行" />
                            </el-form-item>
                            <el-form-item label="银行账号" prop="invoiceData.bankAccount">
                                <span v-if="!isCollectPayment">{{ form.invoiceData.bankAccount }}</span>
                                <el-input v-else v-model="form.invoiceData.bankAccount" class="w-full" clearable maxlength="30" placeholder="请输入银行账号" />
                            </el-form-item>
                            <el-form-item :rules="[{ required: true, message: '请选择签约公司名称', trigger: 'change' }]" label="签约公司名称" prop="signCompany">
                                <el-select v-model="form.signCompany" :disabled="!isCollectPayment" class="w-full" clearable filterable placeholder="请选择签约公司名称" @change="companyNameChange">
                                    <el-option v-for="dict in companyList" :key="dict.code" :label="dict.name" :value="dict.code" />
                                </el-select>
                            </el-form-item>
                            <el-form-item :rules="[{ required: true, message: '请选择发票类型', trigger: 'change' }]" label="发票类型" prop="invoiceType">
                                <el-select v-model="form.invoiceType" :disabled="!isCollectPayment" clearable filterable style="width: 90px">
                                    <el-option v-for="dict in invoiceTypeList" :key="dict.code" :label="dict.name" :value="dict.code"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :rules="[{ required: true, message: '请选择项目名称', trigger: 'change' }]" label="项目名称" prop="projectType">
                                <el-select v-model="form.projectType" :disabled="!isCollectPayment && form.invoiceList.length === 1" clearable filterable style="width: 130px" @change="projectIdChange">
                                    <el-option v-for="item in availableProjectList" :key="item.value" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                                <el-select v-if="form.projectType === '运输服务'" v-model="form.projectFeeType" :disabled="!isCollectPayment && form.invoiceList.length === 1" clearable filterable placeholder=" " style="width: 130px" @change="projectFeeTypeChange">
                                    <el-option v-for="item in availableTransportationCostsList" :key="item.label" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                                <el-select v-if="form.projectType === '物流辅助服务'" v-model="form.projectFeeType" :disabled="!isCollectPayment && form.invoiceList.length === 1" clearable filterable placeholder=" " style="width: 130px" @change="projectFeeTypeChange">
                                    <el-option v-for="item in availableServiceChargeList" :key="item.label" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :rules="[{ required: true, message: '请选择税点', trigger: 'change' }]" label="税点" prop="taxPoint">
                                <el-select v-model="form.taxPoint" :disabled="!isCollectPayment" class="tax_point_unit" clearable filterable placeholder=" " style="width: 90px">
                                    <el-option v-for="dict in taxPointList" :key="dict.code" :label="dict.name" :value="dict.code"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="开票备注" prop="remark">
                                <el-input v-model="form.remark" class="w-full" clearable maxlength="150" placeholder="请输入开票备注" show-word-limit type="textarea" />
                            </el-form-item>
                        </div>
                    </el-form>
                </el-card>
            </div>
            <template #footer>
                <div style="margin-top: 10px">
                    <el-button @click="onClose">取 消</el-button>
                    <el-button type="primary" @click="onSubmit">确认开票</el-button>
                </div>
            </template>
        </el-drawer>

        <!-- 公司查询 -->
        <application-company-inquiry v-if="dialogVisible" v-model="dialogVisible" :base-form="form" @submit="setInvoiceInformation" />
    </div>
</template>

<script>
import { Search } from '@element-plus/icons-vue';
import ApplicationCompanyInquiry from '@/components/ApplicationCompanyInquiry.vue';

export default {
    name: 'InvoiceDrawer',
    components: {
        ApplicationCompanyInquiry,
        Search
    },
    props: {
        initialFormData: {
            type: Object,
            default: () => ({})
        },
        invoiceList: {
            type: Array,
            default: () => []
        },
        isCollectPayment: {
            type: Boolean,
            default: false
        },
        modelValue: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            form: {
                invoiceAmount: undefined,
                companyName: undefined,
                invoiceData: null,
                remark: '',
                signCompany: undefined,
                invoiceList: [],
                invoiceType: undefined,
                projectType: undefined,
                projectFeeType: undefined,
                taxPoint: undefined
            },
            invoiceTypeList: [],
            companyList: [],
            projectList: [
                { value: '1', label: '运输服务' },
                { value: '2', label: '物流辅助服务' }
            ],
            transportationCostsList: [
                { value: '1', label: '运输服务费' },
                { value: '2', label: '国内运输费' }
            ],
            serviceChargeList: [
                { value: '1', label: '收派服务费' },
                { value: '2', label: '仓储服务费' }
            ],
            taxPointList: [],
            dialogVisible: false
        };
    },
    computed: {
        availableProjectList() {
            // 从invoiceList中获取可用的项目类型
            const projectTypes = [...new Set(this.form.invoiceList.map((item) => item.projectType))];
            return this.projectList.filter((item) => projectTypes.includes(item.label));
        },
        availableServiceChargeList() {
            // 从invoiceList中获取可用的物流辅助服务费用类型
            const feeTypes = [...new Set(this.form.invoiceList.filter((item) => item.projectType === '物流辅助服务').map((item) => item.projectFeeType))];
            return this.serviceChargeList.filter((item) => feeTypes.includes(item.label));
        },
        availableTransportationCostsList() {
            // 从invoiceList中获取可用的运输服务费用类型
            const feeTypes = [...new Set(this.form.invoiceList.filter((item) => item.projectType === '运输服务').map((item) => item.projectFeeType))];
            return this.transportationCostsList.filter((item) => feeTypes.includes(item.label));
        },
        visible: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    watch: {
        initialFormData: {
            handler(val) {
                this.form = { ...val };
                if (this.form.invoiceType) {
                    this.form.invoiceType = this.form.invoiceType;
                    this.form.projectType = this.form.projectType;
                    this.form.projectFeeType = this.form.projectFeeType;
                    this.form.taxPoint = this.form.taxPoint;
                } else {
                    if (this.form.invoiceList && this.form.invoiceList.length > 0) {
                        const firstItem = this.form.invoiceList[0];
                        this.form.invoiceType = firstItem.invoiceType;
                        this.form.projectType = firstItem.projectType;
                        this.form.projectFeeType = firstItem.projectFeeType;
                        this.form.taxPoint = firstItem.taxPoint;
                    } else {
                        // 如果没有invoiceList，构造一个包含当前表单值的数组
                        this.form.invoiceList = [
                            {
                                invoiceType: this.form.invoiceType,
                                projectType: this.form.projectType,
                                projectFeeType: this.form.projectFeeType,
                                taxPoint: this.form.taxPoint
                            }
                        ];
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    created() {
        this.getDict();
    },
    methods: {
        companyNameChange(val) {
            if (!this.initialFormData.signCompany) {
                if (val === '1') {
                    this.form.projectType = '运输服务';
                    this.form.projectFeeType = '运输服务费';
                    this.form.taxPoint = '9';
                } else {
                    this.form.projectType = '运输服务';
                    this.form.projectFeeType = '运输服务费';
                    this.form.taxPoint = '1';
                }
            }
        },
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
            this.taxPointList = await this.getDictList('tax_points_for_cooperative_shippers');
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
        },
        handleCompanySearch() {
            this.dialogVisible = true;
        },
        onClose() {
            this.$refs.invoiceForm?.resetFields();
            this.$emit('close');
        },
        async onSubmit() {
            this.$refs.invoiceForm.validate(async (valid) => {
                if (valid) {
                    this.loading = true;
                    try {
                        await this.$emit('submit', this.form);
                    } finally {
                        this.loading = false;
                    }
                }
            });
        },
        projectFeeTypeChange(val) {
            if (this.form.invoiceList.length > 1) {
                // 找到匹配的项目配置
                const matchedItem = this.form.invoiceList.find((item) => item.projectType === this.form.projectType && item.projectFeeType === val);
                if (matchedItem) {
                    this.form.invoiceType = matchedItem.invoiceType;
                    this.form.taxPoint = matchedItem.taxPoint;
                }
            }
        },
        projectIdChange(val) {
            if (this.form.invoiceList.length > 1) {
                // 找到匹配的项目配置
                const matchedItems = this.form.invoiceList.filter((item) => item.projectType === val);
                if (matchedItems.length > 0) {
                    // 自动选择第一个匹配的费用类型
                    this.form.projectFeeType = matchedItems[0].projectFeeType;
                    // 更新发票类型和税点
                    this.form.invoiceType = matchedItems[0].invoiceType;
                    this.form.taxPoint = matchedItems[0].taxPoint;
                }
            } else {
                // 单个项目时的默认设置
                if (val === '运输服务') {
                    this.form.projectFeeType = '运输服务费';
                    if (this.form.signCompany === '1') {
                        this.form.taxPoint = '9';
                    } else {
                        this.form.taxPoint = '1';
                    }
                } else if (val === '物流辅助服务') {
                    this.form.projectFeeType = '收派服务费';
                    if (this.form.signCompany === '1') {
                        this.form.taxPoint = '6';
                    } else {
                        this.form.taxPoint = '1';
                    }
                }
            }
        },
        /**
         * 设置发票信息
         * @param val
         */
        setInvoiceInformation(val) {
            this.form.companyName = val.companyName;
            this.form.invoiceData = {
                invoiceHead: val.invoiceHead,
                taxNo: val.taxNo,
                address: val.address,
                phone: val.phone,
                openBank: val.openBank,
                bankAccount: val.bankAccount
            };
            this.form.invoiceType = val.invoiceType;
            this.form.signCompany = val.signCompany;
            this.form.projectType = ['运输服务费', '国内运输费'].includes(val.projectName) ? '运输服务' : val.projectName === '收派服务费' || val.projectName === '仓储服务费' ? '物流辅助服务' : val.projectName;
            this.form.projectFeeType = val.projectName;
            this.form.taxPoint = Number(val.taxPoint).toFixed(0);
        }
    },
    emits: ['update:modelValue', 'close', 'submit']
};
</script>

<style lang="scss" scoped>
.input__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 10px;
        top: 47%;
        transform: translateY(-50%);
    }
}

.tax_point_unit {
    position: relative;

    :deep(.el-input__inner) {
        text-align: left;
    }

    &::after {
        content: '%';
        position: absolute;
        right: 30px;
        top: 51%;
        transform: translateY(-50%);
    }
}
.ml-10 {
    margin-left: 10px;
}
</style>
