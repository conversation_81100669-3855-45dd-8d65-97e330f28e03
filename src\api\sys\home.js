import http from "@/utils/request"

export default {
  getReviewTotal: function (inputForm) {
    return http.get('/erp/audit/currentAuditNode/countNowData',inputForm)
  },
  countPurchaseOrderYM: function (inputForm) {
    return http.get('/erp/home/<USER>/countPurchaseOrderYM',inputForm)
  },
  countMoneyPurchaseYM: function (inputForm) {
    return http.get('/erp/home/<USER>/countMoneyPurchaseYM',inputForm)
  },
  countSaleOrderYM: function (inputForm) {
    return http.get('/erp/home/<USER>/countSaleOrderYM',inputForm)
  },
  countMoneySaleYM: function (inputForm) {
    return http.get('/erp/home/<USER>/countMoneySaleYM',inputForm)
  },
  hotGoodsYM: function (inputForm) {
    return http.get('/erp/home/<USER>/hotGoodsYM',inputForm)
  },
  hotCustomerByYM: function (inputForm) {
    return http.get('/erp/home/<USER>/hotCustomerByYM',inputForm)
  },
  countDataByMD: function (inputForm) {
    return http.get('/erp/home/<USER>/countDataByMD',inputForm)
  },
  codeLogin: function (inputForm) {
    return http.get('/auth/usercenter/codeLogin',inputForm)
  },
  
}
