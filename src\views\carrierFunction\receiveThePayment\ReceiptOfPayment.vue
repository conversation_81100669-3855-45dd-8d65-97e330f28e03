<template>
    <div v-loading="fullLoading" :element-loading-text="fullLoadingText" class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" :label-width="isShow ? 'auto' : ''" class="seache-form" @submit.native.prevent>
                <el-form-item label="认款申请编号" prop="applyNo" style="width: 230px">
                    <el-input v-model="queryParams.applyNo" clearable placeholder="请输入认款申请编号" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="审核状态" prop="auditStatus" style="width: 230px">
                    <el-select v-model="queryParams.auditStatus" clearable placeholder="请选择审核状态" @change="handleQuery">
                        <el-option v-for="item in auditStatusOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="认款日期" prop="queryTime" style="width: 310px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShow" label="付款公司" prop="payCompany">
                    <el-input v-model="queryParams.payCompany" clearable placeholder="请输入付款公司" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShow" label="结算公司" prop="settleCompany">
                    <el-input v-model="queryParams.settleCompany" clearable placeholder="请输入结算公司" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all="isShow" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <div class="flex justify-around">
                <el-statistic :precision="2" :value="statisticalData.totalAmount" :value-style="{ color: '#3cccca', fontWeight: 'bold' }" group-separator="," title="认款金额合计："></el-statistic>
                <el-statistic :precision="2" :value="statisticalData.auditAmount" :value-style="{ color: '#ac86ff', fontWeight: 'bold' }" group-separator="," title="待审核金额："></el-statistic>
                <el-statistic :precision="2" :value="statisticalData.confirmAmount" :value-style="{ color: '#feb119', fontWeight: 'bold' }" group-separator="," title="已确认金额："></el-statistic>
            </div>
        </el-card>
        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 16px;">
                <el-button icon="el-icon-plus" size="mini" type="primary" @click="handleAdd('add')">创建认款申请</el-button>
                <el-button :disabled="dataList.length == 0" icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :tableID="'ReceiptOfPayment'" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="ReceiptOfPayment" :max-height="600" ref="ColumnTable" v-loading="loading" v-model:columns="columns" :data="dataList" :row-style="setRowStyle" rowKey="id" @filter-click="handleFilterClick">
                <template #auditStatus="{ row }">
                    <span>{{ row.auditStatus ? selectDictLabel(auditStatusOptions, row.auditStatus) : '--' }}</span>
                </template>
                <template #claimDate="{ row }">
                    <span>{{ row.claimDate ? formatDate(row.claimDate) : '--' }}</span>
                </template>

                <template #opt="{ row }">
                    <el-button v-if="row.auditStatus == '2'" icon="el-icon-edit" link size="small" type="warning" @click="handleAdd('edit', row)">编辑</el-button>
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleAdd('view', row)">详情</el-button>
                    <el-button v-if="row.auditStatus != '0' && row.auditStatus != '1'" icon="el-icon-delete" link size="small" type="danger" @click="deleteHandle(row)">删除</el-button>
                    <el-dropdown size="small" style="height: 23px">
                        <el-button icon="el-icon-arrow-down" link size="small">更多</el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item icon="el-icon-stamp" style="color: #67c23a" @click.native="approvalProgress(row)">审批进展</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
        <!--申请弹窗-->
        <subscription-apply
            v-if="applicationData.visible"
            v-model:visible="applicationData.visible"
            :applyId="applicationData.id"
            :title="applicationData.title"
            :type="applicationData.type"
            @success="
                getList();
                getTotalAmount();
            "
        ></subscription-apply>
        <!--审批进展-->
        <approval-progress v-if="aplData.visible" v-model:visible="aplData.visible" :current-index="aplData.currentIndex" :data-list="aplData.data" :title="aplData.title"></approval-progress>
    </div>
</template>

<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SubscriptionApply from '@/views/carrierFunction/receiveThePayment/components/SubscriptionApply';
import receiptOfPayment from '@/api/carrierEnd/receiveThePayment/receiptOfPayment';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadNoData } from '@/utils';
import moment from 'moment';
import ApprovalProgress from '@/components/approvalProgress/ApprovalProgress';
import { setDatePickerShortcuts } from '@/utils/config-store';
export default {
    name: 'ReceiptOfPayment',
    components: {
        ApprovalProgress,
        SubscriptionApply,
        SearchButton,
        ColumnTable,
        RightToolbar
    },
    data() {
        return {
            fullLoading: false, // 全屏加载
            fullLoadingText: '正在导出数据，请稍等...',
            showSearch: true, // 显示搜索
            isShowAll: false, // 显示全部
            queryParams: {
                current: 1,
                size: 10,
                applyNo: undefined, // 认款申请编号
                queryTime: [], // 认款日期
                auditStatus: undefined, // 审核状态
                payCompany: undefined, // 付款公司
                settleCompany: undefined // 结算公司
            },
            shortcuts: setDatePickerShortcuts(),
            dataList: [], // 列表数据
            loading: false, // 加载状态
            columns: [
                { title: '认款申请编号', key: 'applyNo', align: 'center', width: '160px', columnShow: true },
                { title: '认款日期', key: 'claimDate', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '认款金额', key: 'claimAmount', align: 'center', width: '80px', columnShow: true },
                { title: '审核状态', key: 'auditStatus', align: 'center', width: '100px', columnShow: true, filters: [], isFilter: true, isActive: false },
                { title: '付款公司', key: 'payCompany', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settleCompany', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '申请人', key: 'applyUser', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', fixed: 'right', width: '280px', hideFilter: true, columnShow: true, showOverflowTooltip: true }
            ], // 表格列
            total: 0, // 总条数
            auditStatusOptions: [], // 审核状态
            applicationData: {
                title: '',
                visible: false,
                type: 'add',
                id: null
            }, // 申请弹窗数据
            aplData: {
                title: '',
                visible: false,
                currentIndex: null,
                data: null
            }, // 审批进度弹窗数据
            isShow: false, // 展开或者合上
            statisticalData: {
                totalAmount: 0,
                auditAmount: 0,
                confirmAmount: 0,
            } // 合计金额
        };
    },
    computed: {
        /**
         * 格式化日期
         * @returns {function(*=): *}
         */
        formatDate() {
            return function (date) {
                return moment(date).format('YYYY-MM-DD');
            };
        }
    },
    created() {
        this.getDict();
        this.handleQuery();
    },
    methods: {
        // 展开或者合上
        showAllClick() {
            this.isShow = !this.isShow;
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            // 审核状态
            this.auditStatusOptions = await this.getDictList('audit_status');

            this.setFilters();
        },
        /**
         *  重置筛选条件
         */
        setFilters() {
            //  任务来源
            if (this.columns.find((column) => column.key === 'auditStatus')) {
                this.columns.find((column) => column.key === 'auditStatus').filters = this.auditStatusOptions;
            }
        },
        /**
         * 设置行样式
         * @param row
         * @returns {{color: string}}
         */
        setRowStyle({ row }) {
            switch (row.auditStatus) {
                case '0':
                    return { color: 'rgb(64, 158, 255)' };
                case '1':
                    return { color: '#039D12' };
                case '2':
                case '3':
                    return { color: 'rgb(255, 72, 0)' };
            }
        },
        /**
         *  筛选点击
         * @param value
         * @param key
         */
        handleFilterClick(value, key) {
            this.queryParams[key] = value;
            this.handleQuery();
        },
        /**
         * 获取统计数据
         * @returns {Promise<void>}
         */
        async getTotalAmount() {
            try {
                // 重置统计数据
                this.statisticalData = {
                    totalAmount: 0,
                    auditAmount: 0,
                    confirmAmount: 0
                };

                // 解构查询参数，排除不需要的字段
                const { queryTime, size, current, ...params } = this.queryParams;

                // 调用接口获取数据
                const res = await receiptOfPayment.getTotalAmount(params);

                if (res.code === 200 && res.data) {
                    this.updateStatisticalData(res.data);
                }
            } catch (error) {
                console.error('获取统计数据失败:', error);
                this.msgError('获取统计数据失败');
            }
        },
        /**
         * 更新统计数据
         * @param {Object} data - 接口返回的数据
         */
        updateStatisticalData(data) {
            this.statisticalData = {
                totalAmount: this.parseAmount(data.totalAmount),
                auditAmount: this.parseAmount(data.auditAmount),
                confirmAmount: this.parseAmount(data.confirmAmount)
            };
        },
        /**
         * 解析金额
         * @param {string|number} amount - 金额
         * @returns {number} 解析后的金额
         */
        parseAmount(amount) {
            const value = Number(amount);
            return isNaN(value) ? 0 : Number(value.toFixed(2));
        },
        /**
         * 获取列表数据
         */
        getList() {
            this.loading = true;
            this.dataList = [];

            const { queryTime, ...params } = this.queryParams;
            receiptOfPayment
                .getClaimApplyList(params)
                .then((res) => {
                    if (res.code == 200) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {
                    this.total = 0;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 上传到款记录 -弹出
         */
        handleAdd(type, row = null) {
            console.log(type, row);
            if (type == 'add') {
                this.applicationData.title = '创建认款申请';
            } else if (type == 'edit') {
                this.applicationData.title = '编辑认款申请';
            } else if (type == 'view') {
                this.applicationData.title = '查看认款申请';
            }
            this.applicationData.type = type;
            this.applicationData.id = row?.id ? row.id : null;
            this.applicationData.visible = true;
        },
        /**
         * 重置查询
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryParams = {
                current: 1,
                size: 10,
                applyNo: undefined, // 认款申请编号
                queryTime: [], // 认款日期
                auditStatus: undefined, // 审核状态
                payCompany: undefined, // 付款公司
                settleCompany: undefined // 结算公司
            };
            this.handleQuery();
        },
        /**
         * 查询
         */
        handleQuery() {
            const { queryTime } = this.queryParams;
            this.queryParams.startClaimDate = null;
            this.queryParams.endClaimDate = null;
            if (queryTime != undefined && queryTime.length != 0 && queryTime[0] != 'Invalid Date') {
                this.queryParams.startClaimDate = queryTime[0] + ' 00:00:00';
                this.queryParams.endClaimDate = queryTime[1] + ' 23:59:59';
            }
            if (this.$refs.ColumnTable) {
                this.$refs.ColumnTable.setFilterActive(this.queryParams.auditStatus, 'auditStatus');
            }
            this.queryParams.current = 1;
            this.getList();
            this.getTotalAmount();
        },
        /**
         * 查看审批进度
         * @param row
         */
        approvalProgress(row) {
            receiptOfPayment
                .getAuditRecord({ claimId: row.id })
                .then((res) => {
                    if (res.code === 200) {
                        this.aplData.title = '审批进度';
                        this.aplData.visible = true;
                        this.aplData.data = res.data;
                        this.aplData.currentIndex = res.data.length - 1;
                    }
                })
                .catch(() => {
                    this.msgError('获取审批进度失败！');
                });
        },
        /**
         * 删除到款记录
         * @param row
         */
        deleteHandle(row) {
            ElMessageBox.confirm('确认删除此项吗?', '提示', {
                type: 'warning',
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            })
                .then(() => {
                    receiptOfPayment
                        .delClaimApply({ ids: row.id })
                        .then((res) => {
                            if (res.code === 200) {
                                this.msgSuccess('删除成功！');
                                this.getList();
                                this.getTotalAmount();
                            }
                        })
                        .catch(() => {
                            this.msgError('删除失败！');
                        });
                })
                .catch(() => {
                    ElMessage({
                        message: '删除取消!',
                        type: 'info'
                    });
                });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.fullLoading = true;
            const { queryTime, current, size, ...params } = this.queryParams;
            receiptOfPayment
                .exportClaimApply({ filename: '三方到款查询.xls', ...params }, { responseType: 'blob' }, '')
                .then((res) => {
                    downloadNoData(res, 'application/vnd.ms-excel', '三方到款查询.xlsx');
                })
                .catch(() => {})
                .finally(() => {
                    this.fullLoading = false;
                });
        }
    }
};
</script>

<style scoped>
.el-statistic {
    display: flex;
    align-items: baseline;
    margin-right: 20px;
    gap: 5px;
}
</style>
