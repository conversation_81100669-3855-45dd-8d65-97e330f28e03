<template>
  <el-container>
    <el-main style="padding: 0 20px">
      <ytzhTable
        ref="dataTable"
        :data="dataList"
        row-key="id"
        @selection-change="selectionChange"
        stripe
        :tablePage="tablePage"
        :pageChangeHandle="getDataList"
        :refreshDataListHandle="getDataList"
      >
        <el-table-column
          label="执行时间"
          prop="taskExcuteTime"
          width="200"
        ></el-table-column>
        <el-table-column label="执行结果" prop="taskExcuteState" width="100">
          <template #default="scope">
            <span v-if="scope.row.taskExcuteState == 200" style="color: #67c23a"
              ><el-icon><el-icon-success-filled /></el-icon
            ></span>
            <span v-else style="color: #f56c6c"
              ><el-icon><el-icon-circle-close-filled /></el-icon
            ></span>
          </template>
        </el-table-column>
        <el-table-column label="执行日志" prop="logs" width="100" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="show(scope.row)" type="text">日志</el-button>
          </template>
        </el-table-column>
      </ytzhTable>
    </el-main>
  </el-container>

  <el-drawer
    title="日志"
    v-model="logsVisible"
    :size="500"
    direction="rtl"
    destroy-on-close
  >
    <el-main style="padding: 0 20px 20px 20px">
      <pre
        style="
          font-size: 12px;
          color: #999;
          padding: 20px;
          background: #333;
          font-family: consolas;
          line-height: 1.5;
          overflow: auto;
        "
        >{{ demoLog }}</pre
      >
    </el-main>
  </el-drawer>
</template>

<script>
export default {
  data() {
    return {
      logsVisible: false,
      demoLog: `2021-07-07 12:35:00 [com.xxl.job.core.thread.JobThread#run]-[124]-[Thread-308]
----------- xxl-job job execute start -----------
----------- Param:
2021-07-07 12:35:00 [com.heronshn.reservation.jobhandler.AqshMasterDataSendHandler#execute]-[31]-[Thread-308] aqshMasterDataSendHandler start
2021-07-07 12:35:00 [com.heronshn.reservation.data.service.impl.AqshVehicleServiceImpl#send]-[42]-[Thread-308] send 45
2021-07-07 12:35:00 [com.heronshn.reservation.data.service.impl.AqshVehicleServiceImpl#send]-[45]-[Thread-308] webapi http://127.0.0.1:48080
2021-07-07 12:35:00 [com.heronshn.reservation.jobhandler.AqshMasterDataSendHandler#execute]-[33]-[Thread-308] aqshMasterDataSendHandler vehicle end
2021-07-07 12:35:00 [com.heronshn.reservation.jobhandler.AqshMasterDataSendHandler#execute]-[35]-[Thread-308] aqshMasterDataSendHandler stop
2021-07-07 12:35:00 [com.xxl.job.core.thread.JobThread#run]-[158]-[Thread-308]
----------- xxl-job job execute end(finish) -----------
----------- ReturnT:ReturnT [code=200, msg=null, content=null]
2021-07-07 12:35:00 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[176]-[Thread-10]
----------- xxl-job job callback finish.

[Load Log Finish]`,
      //数据列表
      dataList: {},
      //分页参数
      tablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
        //排序
        //orders: [{ column: "createDate", asc: false }],
      },
      //查询表单
      searchForm: {
        name: "",
        url: "",
      },
      //数据列选中行
      selection: [],
      //列表加载
      listLoading: false,
    };
  },
  mounted() {
    //刷新数据列表
    this.getDataList();
  },
  methods: {
    show(log) {
      console.log(log);
      this.logsVisible = true;
    },
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      //页面加载
      this.listLoading = true;
      this.dataList = null;
      var res = await this.$API.sysTaskLogService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: this.tablePage.pageSize,
        //排序查询
        orders: this.tablePage.orders,
        ...this.searchForm,
      });
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        //数据列表
        this.dataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      this.listLoading = false;
    },
    /*
     * 编辑数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:41
     */
    editForm(row) {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.editView(row);
      });
    },
    /*
     * 查看数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:55
     */
    viewForm(row) {
      this.dialog.form = true;
      this.$nextTick(() => {
        this.$refs.formDialog.view(row);
      });
    },
    /*
     * 删除数据，行内删除
     * @author: 路正宁
     * @date: 2023-03-24 14:35:00
     */
    async deleteData(row, index) {
      this.listLoading = true;
      var res = await this.$API.sysTaskLogService.delete(row.id);
      if (res.code == 200) {
        this.$refs.dataTable.removeIndex(index);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      this.listLoading = false;
    },
    /*
     * 批量删除
     * @author: 路正宁
     * @date: 2023-03-24 14:36:11
     */
    async deleteDatas() {
      //确认删除弹框
      var confirmRes = await this.$confirm(
        `确定删除选中的 ${this.selection.length} 项吗？`,
        "提示",
        {
          type: "warning",
          confirmButtonText: "删除",
          confirmButtonClass: "el-button--danger",
        }
      ).catch(() => {});
      //确认结果处理
      if (!confirmRes) {
        return false;
      }
      //要删除的id数组
      var ids = this.selection.map((v) => v.id);
      //拼接的数组字符串，接口传参
      var idStr = this.selection.map((v) => v.id).join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysTaskLogService.delete(idStr);
      if (res.code == 200) {
        //从列表中移除已删除的数据
        this.$refs.dataTable.removeKeys(ids);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      //释放页面加载中
      this.listLoading = false;
    },
    /*
     * 表格选择后回调事件
     * @author: 路正宁
     * @date: 2023-03-24 14:51:09
     */
    selectionChange(selection) {
      this.selection = selection;
    },
  },
};
</script>

<style></style>
