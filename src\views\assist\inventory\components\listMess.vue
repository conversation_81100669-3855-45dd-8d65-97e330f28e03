<template>
	<div v-loading="loadingFlag">
		<table v-if="props.fileArr.length>0" border="0" cellpadding="0" cellspacing="1" class="detailTable">
			<tr v-for="(item,index) in props.fileArr" :key="index">
				<td>
					文件{{
						index + 1
					}}
				</td>

				<td @click="checkimg(item.fileUrl)">
					{{
						item.fileName
					}}
				</td>
			</tr>
		</table>
		<el-empty v-if="props.fileArr.length==0" description="无数据"/>
		<el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const loadingFlag = ref(false)
const data = reactive({
	checkFlag: false,
	imgUrl: []
})
const close = () => {
	data.checkFlag = false;
	data.imgUrl = []
}
const emit = defineEmits([])
const props = defineProps({
	fileArr: {
		default: []
	}
})
const checkimg = (url) => {
	data.imgUrl = []
	data.imgUrl.push(url)
	data.checkFlag = true
}
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	loadingFlag
})

</script>
<style lang='scss' scoped>
.detailTable {
	width: 100%;
	background-color: #eaedf3;
	font-size: 14px;
	border-radius: 5px;
	table-layout: fixed;

	tr {
		height: 40px;

		td {
			background-color: white;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
			cursor: pointer;
		}

		td:nth-child(1) {
			padding: 0 10px;
			font-weight: bold;
			width: 20%;
			color: #505050;
			background: #f7f7f7;
		}

		td:nth-child(2) {
			width: 80%;
			color: #2877fd;
			padding: 0 10px;
		}
	}
}
</style>
