import request from '@/utils/request'
export default {
    // 保温箱记录列表
    cabineList: function (params) {
        return request.get('/tms/receipt/incubatorRecord/list', params);
    },
    // 保温箱记录详情
    getRecordDetail: function (id) {
        return request.get('/tms/receipt/incubatorRecord/findIncubatorDetail', id);
    },
    // 打印箱码
    printIncubatorRecord: function (params) {
        return request.get('/tms/receipt/incubatorRecord/getIncubatorQrCodeInfo', params);
    },
    // 导出
    exportIncubatorRecord: function (params,config,resDetail,responseType) {
        return request.get('/tms/receipt/incubatorRecord/export', params,config,resDetail,responseType);
    },
    //运单详情
    getTransDetail: function (params) {
        return request.get('/tms/orderDrug/queryById', params);
    },
    // 修改保温箱记录状态
    updateIncubatorRecordStatus: function (params) {
        return request.get('/tms/receipt/incubatorRecord/updateIncubatorRecordStatus', params);
    },
    // 查询保温箱
    findIncubator: function (params) {
        return request.get('/staple/cargosort/coldchainpacking/findIncubator', params);
    },
    // 生成保温箱绑定记录
    generateIncubatorRecord: function (params) {
        return request.get('/tms/receipt/incubatorRecord/generateIncubatorRecord', params);
    },

}
