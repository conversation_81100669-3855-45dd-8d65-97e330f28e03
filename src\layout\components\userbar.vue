<template>
    <div class="user-bar">
        <div class="panel-item hidden-sm-and-down" @click="search">
            <el-icon><el-icon-search /></el-icon>
        </div>

        <!--客服-->
        <template v-if="Organization[current].type === '2'">
            <el-popover :width="160" placement="bottom" trigger="hover">
                <div class="box-app-download">
                    <div>
                        <qr-code-with-logo :size="132" logo="images/weixin.png" value="https://work.weixin.qq.com/kfid/kfc848d3e9947022b57" />
                        <div style="text-align: center; margin-top: 5px">微信扫码咨询客服</div>
                    </div>
                </div>
                <template #reference>
                    <img alt="微信客服" src="../../assets/home/<USER>" style="margin-right: 2px" width="25" />
                </template>
            </el-popover>
        </template>
        <!--司机小程序-->
        <el-popover :width="160" placement="bottom" trigger="hover">
            <div class="box-app-download">
                <div>
                    <img src="../../assets/home/<USER>" style="width: 132px; height: 132px;" />
                    <div style="text-align: center; margin-top: 5px">微信扫码使用司机小程序</div>
                </div>
            </div>
            <template #reference>
                <div class="app-download">
                    <img src="../../assets/home/<USER>" style="width: 16px; margin-right: 4px; vertical-align: middle;" />
                    <span>司机小程序</span>
                </div>
            </template>
        </el-popover>
        <!--下载APP-->
        <el-popover :width="320" placement="bottom" trigger="hover">
            <div class="box-app-download">
                <div>
                    <qr-code-with-logo :size="132" :value="driverAddress" logo="images/driver_end_logo.png" />
                    <div style="text-align: center; margin-top: 5px">司机端2.0下载</div>
                </div>
                <div>
                    <qr-code-with-logo :size="132" :value="hubAddress" logo="images/distribution_terminal_logo.png" />
                    <div style="text-align: center; margin-top: 5px">集散端2.0下载</div>
                </div>
            </div>
            <template #reference>
                <el-link slot="reference" class="app-download" link>APP 下载</el-link>
            </template>
        </el-popover>
        <div style="margin-right: 30px; color: #fff; cursor: pointer; display: flex; align-items: center">
            <!-- 右上角消息提醒 -->
            <div @click="handlerDrawer">
                <span id="icon_cnt">
                    <svg id="notice_icon" height="25" p-id="2304" t="1687834321766" version="1.1" viewBox="0 0 1024 1024" width="25" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M558.1 251.7c40 0 77.7 15.6 106 43.9 28.3 28.3 44 65.9 44 106l0.2 171.4c0 11.1 2.6 22.2 7.6 32.2L793.4 760c0.5 1 2 4-0.3 7.8s-5.7 3.8-6.8 3.8H671l-99.3 0.1-119.5 0.1H388l-149.6 0.1h-0.1c-1.1 0-4.5 0-6.8-3.8s-0.8-6.8-0.3-7.8l77.4-154.6c5-10 7.6-21.2 7.6-32.3L316 402c0-40.1 15.5-77.8 43.8-106.1 28.3-28.4 66-44 106-44l92.1-0.1h0.2m0-60.1h-0.2l-92.1 0.1C349.8 191.9 255.9 286 256 402l0.2 171.4c0 1.9-0.4 3.7-1.3 5.4l-77.4 154.6c-22.6 45.2 10.2 98.4 60.8 98.4h0.1l149.7-0.1c14 55.4 64.2 96.4 123.9 96.4 59.8 0 110-41.1 124-96.6h-21.7H636l150.4-0.1c50.5 0 83.4-53.3 60.8-98.5l-77.5-154.8c-0.8-1.7-1.3-3.5-1.3-5.4l-0.2-171.4C768 285.6 674 191.7 558.1 191.7zM452.2 831.8l119.5-0.1c-11.3 21.5-33.8 36.2-59.8 36.2-25.9 0-48.4-14.7-59.7-36.1H426h26.2z"
                            fill="#ffffff"
                            p-id="2305"
                        ></path>
                        <path d="M512.9 98.4c-55.5 0-100.5 44.9-100.7 100.4 17.1-4.5 35-6.9 53.5-7h6.1c4.3-18.1 20.6-31.6 40.1-31.6 19.4 0 35.7 13.5 40.1 31.6h6c19.2 0 37.8 2.6 55.5 7.4v-0.1c0-55.6-45-100.7-100.6-100.7z" fill="#ffffff" p-id="2306"></path>
                    </svg>
                </span>
            </div>
            <el-badge v-if="listLength !== 0" id="el_badge" :max="999999999" :value="listLength" class="item" @click="showList" />
            <div v-if="this.badgeElement" style="width: 15px"></div>
            <el-dropdown v-if="Organization?.length > 1" trigger="click">
                <div>
                    <span style="color: #fff; cursor: pointer; font-size: 14px; position: relative">{{ Organization[current].name }}</span>
                    <el-icon class="el-icon--right" color="white" style="position: absolute">
                        <arrow-down />
                    </el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item
                            v-for="(item, index) in Organization"
                            :key="index"
                            :style="[
                                {
                                    color: index == current ? '#409eff !important' : ''
                                },
                                {
                                    fontWeight: index == current ? 'bold !important' : 'normal'
                                }
                            ]"
                            @click="handleTab(item, index)"
                            >{{ item.name }}</el-dropdown-item
                        >
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <span v-if="Organization?.length == 1" style="color: #fff; cursor: pointer">{{ Organization[0].name }}</span>

            <div v-if="BranchList?.length > 0" style="width: 30px"></div>
            <el-dropdown v-if="BranchList?.length > 1" trigger="click">
                <div>
                    <span style="color: #fff; cursor: pointer; font-size: 14px; position: relative">{{ BranchList[current1].branchName }}</span>
                    <el-icon class="el-icon--right" color="white" style="position: absolute">
                        <arrow-down />
                    </el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item
                            v-for="(item, index) in BranchList"
                            :key="index"
                            :style="[
                                {
                                    color: index == current1 ? '#409eff !important' : ''
                                },
                                {
                                    fontWeight: index == current1 ? 'bold !important' : 'normal'
                                }
                            ]"
                            @click="network(item, index)"
                            >{{ item.branchName }}</el-dropdown-item
                        >
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <span v-if="BranchList?.length == 1" style="color: #fff; cursor: pointer">{{ BranchList[0].branchName }}</span>
        </div>
        <div class="screen panel-item hidden-sm-and-down" @click="screen">
            <el-icon><el-icon-full-screen /></el-icon>
        </div>

        <el-dropdown class="user panel-item" trigger="click" @command="handleUser">
            <div class="user-avatar">
                <el-avatar :size="30">{{ nameImg() }}</el-avatar>
                <label>{{ userName }}</label>
                <el-icon class="el-icon--right" style="color: #fff"><el-icon-arrow-down /></el-icon>
            </div>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item command="uc">帐号信息</el-dropdown-item>
                    <el-dropdown-item command="clearCache">清除缓存</el-dropdown-item>
                    <el-dropdown-item command="outLogin" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        <div id="el-drawer" ref="container" class="msg panel-item" @click="showMsg">
            <el-drawer id="dropdown-item" ref="drawerRef" v-model="drawer" :append-to-body="false" :show-close="false" :size="400" destroy-on-close style="position: fixed; top: 60px">
                <template #header>
                    <div style="display: flex; align-items: center; margin-bottom: 5px; margin-top: -5px">
                        <!-- 消息提醒 -->
                        <svg class="icon" height="30" p-id="2316" t="1688006537068" version="1.1" viewBox="0 0 1024 1024" width="30" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M892.1 805L781.9 621.3V458c0-71.8-28.1-139.5-79.3-190.6-36.2-36.2-80.7-60.8-129-72.2v-22.7c0-34-27.6-61.6-61.6-61.6s-61.6 27.6-61.6 61.6v22.7c-48.4 11.3-92.9 36-129 72.2-51.1 51.1-79.3 118.8-79.3 190.6v163.3L131.9 805h257.7c7.6 60.8 59.5 108 122.3 108s114.8-47.2 122.3-108h257.9zM496.4 172.6c0-8.6 7-15.6 15.6-15.6s15.6 7 15.6 15.6v16c-5.2-0.3-10.4-0.5-15.6-0.5s-10.4 0.2-15.6 0.5v-16zM288.1 634.1V458c0-123.4 100.4-223.9 223.9-223.9 123.4 0 223.9 100.4 223.9 223.9v176.1L810.8 759H213.2l74.9-124.9zM512 867c-37.4 0-68.6-26.7-75.8-62h151.5c-7.1 35.3-38.3 62-75.7 62z"
                                p-id="2317"
                            ></path>
                        </svg>
                        <el-badge v-if="listLength !== 0" :max="999999999" :value="listLength" class="item2" @click="drawer = true" />
                        <el-dropdown trigger="click" @command="handlerMsg">
                            <div style="display: flex; align-items: center">
                                <h3>{{ title }}</h3>
                                <el-icon>
                                    <arrow-down />
                                </el-icon>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item id="dropdown-item" command="all" style="padding: 5px">
                                        <h4 id="dropdown-item">全部通知</h4>
                                        <el-icon v-if="allMsg" style="margin-left: 10px">
                                            <el-icon-check id="dropdown-item" />
                                        </el-icon>
                                    </el-dropdown-item>
                                    <el-dropdown-item id="dropdown-item" command="read" style="padding: 5px">
                                        <h4 id="dropdown-item">未读通知</h4>
                                        <el-icon v-if="readMsg" style="margin-left: 10px">
                                            <el-icon-check id="dropdown-item" />
                                        </el-icon>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <!--                        <el-dropdown trigger="click" @command="handler">-->
                        <!--                            <el-icon style="margin-left: 200px"> <el-icon-MoreFilled /></el-icon>-->
                        <!--                            <template #dropdown>-->
                        <!--                                <el-dropdown-menu>-->
                        <!--                                    <el-dropdown-item id="dropdown-item" command="uc">-->
                        <!--                                        <h4 id="dropdown-item">标记所有为已读</h4>-->
                        <!--                                    </el-dropdown-item>-->
                        <!--                                </el-dropdown-menu>-->
                        <!--                            </template>-->
                        <!--                        </el-dropdown>-->
                    </div>
                </template>
                <el-container ref="listContainer">
                    <el-main class="nopadding">
                        <el-scrollbar>
                            <ul class="msg-list">
                                <li v-for="item in msgList" v-bind:key="item.id" @click="handlerRead(item)">
                                    <a :href="item.link" target="_blank">
                                        <div v-if="item.type == '1' || item.type == '2' || item.type == '9'" class="msg-list__main">
                                            <h2 :style="{ color: item.color }">【{{ item.title }}】</h2>
                                            <p v-if="item.content?.company" :style="{ color: item.color }">付款公司：{{ item.content?.company }}</p>
                                            <p v-if="item.content?.money" :style="{ color: item.color }">{{ item.type == '1' ? '充值金额' : '汇款金额' }}：{{ item.content?.money }}元</p>
                                            <p v-if="item.content?.timer" :style="{ color: item.color }">{{ item.type == '1' ? '充值时间' : '汇款时间' }}：{{ item.content?.timer }}</p>
                                        </div>
                                        <div v-if="item.type == '3' || item.type == '4' || item.type == '7' || item.type == '8'" class="msg-list__main">
                                            <h2 :style="{ color: item.color }">【{{ item.title }}】</h2>
                                            <p v-if="item.content?.message" :style="{ color: item.color }">{{ item.content?.message }}</p>
                                        </div>
                                        <div v-if="item.type == '5' || item.type == '6'" class="msg-list__main">
                                            <h2 :style="{ color: item.color }">【{{ item.title }}】</h2>
                                            <p v-if="item.content?.companyName" :style="{ color: item.color }">货主公司：{{ item.content?.companyName }}</p>
                                            <p v-if="item.content?.money" :style="{ color: item.color }">开票金额：{{ item.content?.money }}元</p>
                                            <p v-if="item.content?.timer" :style="{ color: item.color }">申请时间：{{ item.content?.timer }}</p>
                                        </div>
                                        <!-- 对号 -->
                                        <div class="msg-list__time" @click.stop="handlerSvg(item)">
                                            <svg v-if="item.color !== '#3333336b'" class="icon" height="15" p-id="2879" t="1688607623880" version="1.1" viewBox="0 0 1024 1024" width="15" xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M348.**********.0368l-223.232-223.232a25.6 25.6 0 0 0-36.181334 36.181333l241.**********.322667a25.6 25.6 0 0 0 36.181333 0L921.6 273.066667a25.6 25.6 0 1 0-36.181333-36.181334L348.603733 761.002667z"
                                                    fill="#8a8a8a"
                                                    p-id="2880"
                                                ></path>
                                            </svg>
                                        </div>
                                    </a>
                                </li>
                                <div v-if="loading">Loading...</div>
                                <el-empty v-if="msgList.length == 0" :image-size="100" description="暂无新消息"></el-empty>
                            </ul>
                        </el-scrollbar>
                    </el-main>
                </el-container>
            </el-drawer>
        </div>

        <el-dialog v-model="searchVisible" :width="700" center destroy-on-close title="搜索">
            <search @success="searchVisible = false"></search>
        </el-dialog>
    </div>
</template>

<script>
import search from './search.vue';
import tasks from './tasks.vue';
import {ArrowDown} from '@element-plus/icons-vue';
import {ElMessageBox} from 'element-plus';

import QrCodeWithLogo from '@/components/qrcode/QrCodeWithLogo.vue';
import { getterFullDomainName } from '@/utils';

export default {
    components: {
        QrCodeWithLogo,
        search,
        tasks,
        ArrowDown
    },
    data() {
        return {
            currentOrgId: null,
            userName: '',
            userNameF: '',
            searchVisible: false,
            tasksVisible: false,
            drawer: false,
            icon_svg: true,
            msg: false,
            allMsg: true,
            readMsg: false,
            Organization: [],
            BranchList: [],
            current: 0,
            current1: 0,
            msgList: [],
            listLength: 0,
            title: '全部通知',
            readList: [],
            ids: [],
            idsList: [],
            typeList: [],
            currentPage: 1, // 当前页码
            pageSize: 10, // 每页数量
            loading: false,
            unread: '',
            threshold: 100, // 距离底部多少像素时触发加载
            scrollTop: 0,
            pages: 0,
            driverAddress: getterFullDomainName() + '/#/driverSideDownload',
            hubAddress: getterFullDomainName() + '/#/distributedDownload',

            //websocket
            websocket: null,
            websocketUrl: 'null',
            //承运商id
            orgId: null,
            //当前登录用户id
            userId: null,
            websocketDictList: [],
            websocketState: false,
            websocketTimer: 3000
        };
    },
    created() {
        // const proxy = this.$options.proxy;
        this.current = this.$TOOL.data.get('orgKey');
        this.current1 = this.$TOOL.data.get('braKey');
        this.Organization = this.$TOOL.data.get('Organization');
        this.currentOrgId = this.Organization[0].id;
        this.BranchList = this.$TOOL.data.get('BranchList');
        var userInfo = this.$TOOL.data.get('USER_INFO');
        this.userName = userInfo.userName;
        this.userNameF = this.userName?.substring(0, 1);
        this.spanElement = document.getElementById('icon_cnt');
        this.badgeElement = document.getElementsByClassName('item');
        //this.getDict();
        if (this.currentOrgId && this.Organization[0].type == '2') {
            this.getCompanyMesssage();
        } else {
            this.getList(); // 首次加载数据
        }
        if (!this.$TOOL.data.get('NETWORK')) {
            this.getmenuTreeByOrg(this.BranchList[0]);
            this.$TOOL.data.set('NETWORK', this.BranchList[0]);
        }
    },
    async mounted() {
        //获取当前登录用户所属组织机构
        await this.getCurrentOrg();
        await this.getDict();

        this.getCurrentUserInfo();
    },
    methods: {
        async getCompanyMesssage() {
            let res = await this.$API.system.getCompanyMesssage();
            if (res.code == 200 && res.data) {
                this.msgList = res.data.map((item) => {
                    if (item.status == '1') {
                        item.color = '#3333336b';
                    }
                    if (item.content && typeof item.content == 'string') {
                        item.content = JSON.parse(item.content);
                    }
                    return item;
                });
            }
            this.listLength = this.msgList.length;
            if (this.msgList.length > 0) {
                this.handlerDrawer();
            }
        },
        async handleTab(row, key) {
            const _this = this;
            if (this.$TOOL.data.get('orgKey') == key) return;
            ElMessageBox.confirm(`是否确认切换到${row.name}?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(async () => {
                    let res = await this.$API.system.menuTreeByOrg({
                        orgId: row.id
                    });
                    if (res.code == 200) {
                        _this.$TOOL.data.set('orgKey', key);
                        var menuTree = _this.sysMenuToUiTree(res.data);
                        var permissions = _this.getPermissions(res.data);
                        //菜单数据写入本地
                        _this.$TOOL.data.set('MENU', menuTree);
                        //权限标识写入本地
                        _this.$TOOL.data.set('PERMISSIONS', permissions);
                        // _this.$router.replace({
                        // 	path: "/home",
                        // });
                        location.href = '#/';
                        _this.$router.go(0);
                    }
                })
                .catch(() => {});
        },

        async network(row, key) {
            const _this = this;
            if (this.$TOOL.data.get('braKey') == key) return;
            ElMessageBox.confirm(`是否确认切换到${row.branchName}网点?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(async () => {
                    let res = await this.$API.system.switchBranch({
                        branchId: row.id
                    });
                    if (res.code == 200) {
                        _this.$TOOL.data.set('braKey', key);
                        // var menuTree = _this.sysMenuToUiTree(res.data);
                        // var permissions = _this.getPermissions(res.data);
                        // //菜单数据写入本地
                        // _this.$TOOL.data.set('MENU', menuTree);
                        // //权限标识写入本地
                        _this.$TOOL.data.set('NETWORK', res.data);
                        // _this.$router.replace({
                        // 	path: "/home",
                        // });
                        location.href = '#/';
                        _this.$router.go(0);
                    }
                })
                .catch(() => {});
        },
        getmenuTreeByOrg(row) {
            let res = this.$API.system.switchBranch({
                branchId: row.id
            });
            if (res.code == 200) {
                // this.$TOOL.data.set('braKey', key);
                // //菜单数据写入本地
                // _this.$TOOL.data.set('MENU', menuTree);
                // //权限标识写入本地
                this.$TOOL.data.set('NETWORK', res.data);
                // _this.$router.replace({
                // 	path: "/home",
                // });
                location.href = '#/';
                this.$router.go(0);
            }
        },
        handleOutsideClick(e) {
            if (e.target.id === 'notice_icon' || e.target.id === 'icon_cnt' || e.target.id === 'dropdown-item') return;
            this.drawer = false;
        },
        //消息列表数据
        async getList(type) {
            this.loading = true;
            let res = await this.$API.system.getMessageList({
                current: type == 'all' ? 1 : this.currentPage,
                size: type == 'all' ? 10 : this.pageSize
            });
            if (res.code == '200') {
                // this.pages = res.data?.pages;
                // if (type === 'all') {
                // 	this.currentPage = 1;
                // 	this.msgList = [...res.data];
                // }
                // if (type === 'pager') {
                // 	this.msgList = [...this.msgList, ...res.data];
                // } else {
                // 	this.msgList = [...res.data];
                // }
                this.pages = res.data?.length;
                this.msgList = res.data;
                this.loading = false;
                this.msgList.forEach((item) => {
                    if (item.status == '1') {
                        item.color = '#3333336b';
                    }
                    if (item.content && typeof item.content == 'string') {
                        item.content = JSON.parse(item.content);
                    }
                });
                this.readList = [];
                this.listLength = 0;
                res.data.forEach((item) => {
                    this.listLength = item.status == '0' ? this.listLength + 1 : this.listLength;
                });
                if (this.msgList.length > 0) {
                    this.handlerDrawer();
                }
            } else {
                // 提示错误
                this.$message.error(res.msg);
            }
        },

        scrollChange() {
            if (this.msgList.length != 0) {
                // if(this.unread == 'unread')return
                //获取网页的总高度
                var htmlHeight = document.documentElement.scrollHeight;
                //clientHeight网页在浏览器中的可视高度
                var clientHeight = document.body.clientHeight || document.documentElement.clientHeight;
                //scrollTop浏览器滚动条的top位置
                var scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
                if (scrollTop + clientHeight > htmlHeight - 50) {
                    if (this.currentPage < this.pages) {
                        this.currentPage += 1;
                        this.getList('pager');
                    }
                }
            }
        },

        showList() {
            this.drawer = true;
            this.unread = 'read';
        },
        close() {
            this.drawer = false;
        },
        handlerDrawer() {
            this.drawer = true;
            this.unread = 'read';
        },
        handlerBadge() {
            this.drawer = true;
        },
        nameImg() {
            return JSON.parse(window.localStorage.getItem('USER_INFO')).content.name.substr(0, 1);
        },
        sysMenuToUiTree(sysMenu) {
            var menuTree = [];
            for (var i = 0; i < sysMenu.length; i++) {
                menuTree[i] = {
                    name: sysMenu[i].alias,
                    path: sysMenu[i].path,
                    meta: {
                        title: sysMenu[i].name,
                        icon: sysMenu[i].logo,
                        type: sysMenu[i].type,
                        hidden: sysMenu[i].hide,
                        color: sysMenu[i].affix,
                        fullpage: sysMenu[i].wholePageRoute
                    },
                    component: sysMenu[i].view
                };
                if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
                    menuTree[i].children = this.sysMenuToUiTree(sysMenu[i].children);
                }
            }
            return menuTree;
        },
        getPermissions(sysMenu) {
            var permissions = [];
            for (var i = 0; i < sysMenu.length; i++) {
                if (this.$ObjectUtils.isNotEmpty(sysMenu[i].permission)) {
                    permissions.push(sysMenu[i].permission);
                }
                if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
                    var pe = this.getPermissions(sysMenu[i].children);
                    for (var j = 0; j < pe.length; j++) {
                        if (this.$ObjectUtils.isNotEmpty(pe[j])) {
                            permissions.push(pe[j]);
                        }
                    }
                }
            }
            return permissions;
        },
        //消息点击事件
        async handlerRead(item) {
            this.drawer = false;
            if (item.color != '#3333336b' && item.type != '1' && item.type != '2' && item.type != '5' && item.type != '6' && item.type != '7') {
                let res = await this.$API.system.updateStatus({ id: item.id });
                if (res.code == '200') {
                    if (this.listLength > 0) {
                        this.listLength -= 1;
                        item.color = '#3333336b';
                    }
                }
            }
            if (item.type == '1' || item.type == '2' ||  item.type == '9') {
                // 1-预存款充值审批 2-付款单支付审批
                this.$router.push({ name: 'PaymentApproval' });
            } else if (item.type == '3') {
                // 对账单
                this.$router.push({ name: 'AccountStatement' }); // 货主-对账单页面
            } else if (item.type == '4' || item.type == '8') {
                // 付款单
                this.$router.push({ name: 'PaymentDoc' }); // 货主-跳转付款单页面
            } else if (item.type == '5' || item.type == '6') {
                // 开票审批
                this.$router.push({ name: 'InvoiceApproval' });
            } else if (item.type == '7') {
                this.$router.push({ name: 'CustomerStatement' }); // 承运商-跳转客户对账单
            }
        },
        //对号点击事件
        async handlerSvg(item) {
            if (item.color != '#3333336b' && item.type != '1' && item.type != '2') {
                let res = await this.$API.system.updateStatus({ id: item.id });
                if (res.code == '200') {
                    if (this.listLength > 0) {
                        this.listLength -= 1;
                        item.color = '#3333336b';
                    }
                }
            }
        },
        //标记所有为已读
        async handler(command) {
            if (command == 'uc') {
                let res = await this.$API.system.updateStatus();
                if (res.code == '200') {
                    this.listLength = 0;
                    if (this.unread == 'unread') {
                        this.msgList = [];
                    } else {
                        this.getList('all');
                    }
                }
            }
        },
        //全部通知和未读通知
        handlerMsg(command) {
            if (command == 'read') {
                this.title = '未读通知';
                this.allMsg = false;
                this.readMsg = true;
                this.unread = 'unread';
                let filteredOptions = this.msgList.filter((item) => item.color !== '#3333336b');
                this.msgList = filteredOptions;
            }
            if (command == 'all') {
                this.title = '全部通知';
                this.unread = 'read';
                this.allMsg = true;
                this.readMsg = false;
                if (this.currentOrgId && this.Organization[0].type == '2') {
                    this.getCompanyMesssage();
                } else {
                    this.getList('all');
                }
            }
        },
        // 字典请求
        getDict() {
            this.getDictList('alarm_message_type').then((res) => {
                this.typeList = res;
            });

            this.getDictList('fourpl_message_websocket').then((res) => {
                this.websocketDictList = res;
                this.websocketUrl = this.websocketDictList[0].code;
                console.log('---------this.websocketUrl:', this.websocketUrl);
                this.initWebSocket();
            });
        },
        formDict(typeList, value) {
            return this.selectDictLabel(typeList, value);
        },
        //个人信息
        handleUser(command) {
            if (command == 'uc') {
                this.$router.push({ path: '/system/usercenter' });
            }
            if (command == 'cmd') {
                this.$router.push({ path: '/cmd' });
            }
            if (command == 'clearCache') {
                this.$confirm('清除缓存会将系统初始化，包括登录状态、主题、语言设置等，是否继续？', '提示', {
                    type: 'info',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                })
                    .then(() => {
                        const loading = this.$loading();
                        this.$TOOL.data.clear();
                        this.$router.replace({ path: '/login' });
                        setTimeout(() => {
                            loading.close();
                            location.reload();
                        }, 1000);
                    })
                    .catch(() => {
                        //取消
                    });
            }
            if (command == 'outLogin') {
                this.$confirm('确认是否退出当前用户？', '提示', {
                    type: 'warning',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    cancelButtonClass: 'el-button--danger'
                })
                    .then(() => {
                        localStorage.removeItem('NETWORK');
                        this.$router.replace({ path: '/login' });
                    })
                    .catch(() => {
                        //取消退出
                    });
            }
        },
        //全屏
        screen() {
            var element = document.documentElement;
            this.$TOOL.screen(element);
        },
        //显示短消息
        showMsg() {
            this.msg = true;
        },
        //标记已读
        markRead() {
            this.msgList = [];
        },
        //搜索
        search() {
            this.searchVisible = true;
        },
        //任务
        tasks() {
            this.tasksVisible = true;
        },

        getCurrentOrg() {
            // 获取付款公司
            const Organization = JSON.parse(localStorage.getItem('Organization'));

            // 检查Organization是否存在且content数组中有元素
            if (Organization && Array.isArray(Organization.content) && Organization.content.length > 0) {
                this.orgId = Organization.content[0].id;
            } else {
                this.$message.error('无法获取组织信息，请重新登录或联系管理员');
            }
        },

        getCurrentUserInfo() {
            // 获取当前登录用户
            const userInfo = JSON.parse(localStorage.getItem('USER_INFO'));

            if (userInfo) {
                this.userId = userInfo.content.id;
            } else {
                this.$message.error('无法获取当前登录用户信息，请重新登录或联系管理员');
            }
        },
        initWebSocket() {
            //初始化weosocket
            const wsuri = this.websocketUrl;
            this.websocket = new WebSocket(wsuri + '/' + this.userId);
            this.websocket.onopen = this.websocketonopen;
            //this.websocket.onerror = this.websocketonerror;
            this.websocket.onmessage = this.websocketonmessage;
            this.websocket.onclose = this.websocketclose;
            console.log('-------------- 初始化websocket成功 --------------');
        },
        websocketonopen() {
            //连接建立之后执行send方法发送数据
            this.websocketState = true;
            console.log('-------------- websocket连接成功 --------------');
        },
        websocketonerror() {
            console.log('-------------- websocket连接异常 --------------');
            this.websocketState = false;
            //连接建立失败重连
            this.reconnectWebsocket();
        },
        websocketonmessage(e) {
            //数据接收
            var recevieData = JSON.parse(e.data);
            if (this.userId == recevieData.currentUserId) {
                console.log('-------------- 开始接收数据：{}', e.data);
                // this.openMessage();
                if (this.currentOrgId && this.Organization[0].type == '2') {
                    this.getCompanyMesssage();
                } else {
                    this.getList('all');
                }
                this.handlerDrawer();
                //this.getMessage();
            }
        },
        websocketsend(Data) {
            //数据发送
            console.log('-------------- 开始发送数据 --------------');
            this.websocket.send(Data);
        },
        websocketclose(e) {
            //关闭
            console.log('-------------- 连接已断开 --------------');
            console.log('断开连接', e);
            this.websocketState = false;
            //连接建立失败重连
            this.reconnectWebsocket();
        },
        /**
         * 重连
         */
        reconnectWebsocket() {
            const timer = setTimeout(() => {
                if (!this.websocket) {
                    this.initWebSocket();
                } else {
                    if (!this.websocketState) {
                        clearTimeout(timer);
                        // 断开重连
                        console.log('-------------- 连接已断开 --------------');
                        this.initWebSocket();
                    }
                }
            }, this.websocketTimer);
        },
        closeDrawer() {
            this.drawer = false;
        },

        /**
         * 消息提示弹框
         */
        getMessage() {
            this.$notify({
                title: '成功',
                message: '您有新的待审批任务',
                type: 'success'
            });
        }
    },
    beforeDestroy() {
        // document.removeEventListener('click', this.handleOutsideClick);
        // 获取指定元素
        const scrollview = this.$refs['drawerRef'];
        // 移除监听
        scrollview.removeEventListener('scroll', this.scrollChange, true);
    },
    destroy() {
        this.websocket.close();
    }
};
</script>

<style scoped>
.item {
    /* width: 25px; */
    margin-right: 0;
    position: relative;
    right: 13px;
    top: -3px;
}

::v-deep .el-overlay {
    background-color: transparent;
}

::v-deep #el-id-5241-17 {
    margin-left: 215px;
}

.item2 {
    width: 25px;
    position: relative;
    right: 12px;
}

::v-deep .el-drawer__header {
    margin-bottom: 10px;
}

.user-bar {
    display: flex;
    align-items: center;
    height: 100%;
}

.user-bar .panel-item {
    padding: 0 10px;
    cursor: pointer;
    height: 100%;
    display: flex;
    align-items: center;
}

.user-bar .panel-item i {
    font-size: 16px;
}

.user-bar .panel-item:hover {
    background: rgba(0, 0, 0, 0.1);
}
.user-bar .panel-item ::v-deep .el-drawer__body {
    margin-bottom: 60px;
}

.user-bar .user-avatar {
    height: 49px;
    display: flex;
    align-items: center;
}

.user-bar .user-avatar label {
    display: inline-block;
    margin-left: 5px;
    font-size: 12px;
    cursor: pointer;
}

.msg-list li {
    border-top: 1px solid #eee;
}

.msg-list li a {
    display: flex;
    padding: 20px;
}

.msg-list li a:hover {
    background: #ecf5ff;
}

.msg-list__icon {
    width: 40px;
    margin-right: 15px;
}

.msg-list__main {
    flex: 1;
}

.msg-list__main h2 {
    font-size: 15px;
    font-weight: bold;
    color: #333;
}

.msg-list__main p {
    font-size: 12px;
    color: #333;
    line-height: 1.8;
    margin-top: 5px;
    margin-left: 10px;
}

.msg-list__time {
    width: 50px;
    height: 50px;
    /* background-color: aqua; */
    text-align: right;
    /* color: #d41313; */
}

.dark .msg-list__main h2 {
    color: #d0d0d0;
}

.dark .msg-list li {
    border-top: 1px solid #363636;
}

.dark .msg-list li a:hover {
    background: #383838;
}

#icon_cnt {
    padding: 0;
    /* width:30px;
	height:30px; */
    /* margin-right: 15px; */
}
.app-download {
    color: #ffffff;
    margin-right: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    cursor: pointer;
}
.box-app-download {
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 20px;
}
</style>
