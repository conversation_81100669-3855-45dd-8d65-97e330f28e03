<template>
    <div class="app-container" v-if="visible">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog title="调拨申请详情" v-model="visible" width="85%" :before-close="beforeClose">
            <div v-loading="loading">
                <div class="step1 ">
                    <h4 class="titleH4">
                        申请编号:<span style="color: #505050;font-size: 13px; font-weight: 500;margin-right:10px;margin-left:10px"> {{
                            form.applyNo ||
                            '--' }}</span>
                        申请日期: <span style="color: #505050;font-size: 13px; font-weight: 500;margin-right:10px;margin-left:10px"> {{
                            form.applyDate ?
                            moment(form.applyDate).format('YYYY-MM-DD') : '--' }}</span>
                        调拨类型:<span style="color: #505050;font-size: 13px; font-weight: 500;margin-right:10px;margin-left:10px"> {{
                            formDict(allocationType, form.transferType) || '--' }}</span>
                        <!-- 制单人:<span style="color: #505050;font-size: 13px; font-weight: 500;margin-right:10px;margin-left:10px"> {{
                            form.createBy &&
                            form.createBy.name || '--' }}</span> -->
                        审核状态: <span style="color: #505050;font-size: 13px; font-weight: 500;margin-right:10px;margin-left:10px">{{
                            formDict(reviewStatus,
                            form.auditStatus) }}</span> </h4>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px">调拨明细</h3>
                    <el-table :data="step1_list" border style="margin-top: 20px;" row-key="id"
                        ref="entrust_out_table_listRef" @selection-change="handleSelectionChange_step1_list">
                        <el-table-column label="序号">
                            <template #default="scope">
                                {{ scope.$index + 1 }}
                            </template>
                        </el-table-column>

                        <el-table-column label="单据编号" prop="purchaseOrderCode" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="商品名称" prop="commodityName" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="自编码" prop="commoditySelfCode" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="规格" prop="commodityPackageSpecification" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="生产厂家" prop="commodityManufactureName" align="center" min-width="120" :show-overflow-tooltip="true"/>
                        <el-table-column label="产地" prop="commodityOriginPlace" align="center" min-width="120" />
                        <el-table-column label="供应商" prop="supplier.enterpriseName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="生产日期" prop="commodityProduceDate" :show-overflow-tooltip="true"
                            align="center" min-width="120"
                            :formatter="row => row['commodityProduceDate'] ? moment(row['commodityProduceDate']).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="批号" prop="batchNumber" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="有效期" prop="commodityValidityTime" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="基本单位" prop="commodityBasicUnit" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="商品编号" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="库存数量" prop="availableInventory" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="调拨数量" prop="transferQuantity" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="单价" prop="commodityUnitPrice" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="金额" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="row => (Number(row.commodityUnitPrice || 0) * Number(row.transferQuantity || 0)).toFixed(2)" />
                    </el-table>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;">
                        调拨信息
                    </h3>
                    <table border="0" cellpadding="0" cellspacing="1" class="messTable">
                        <tr>
                            <td>调拨类型</td>
                            <td>{{ formDict(allocationType, form.transferType) || '--' }}</td>
                        </tr>

                        <tr>
                            <td>合计调拨数量</td>
                            <td>{{ form.transferAmount || '--' }}</td>
                        </tr>
                        <tr>
                            <td>合计调拨差价</td>
                            <td>{{ form.transferQuantity || '--' }}</td>
                        </tr>
                        <tr>
                            <td>出货经手人</td>
                            <td>{{ form.shipmentBy && form.shipmentBy.name || '--' }}</td>
                        </tr>
                        <tr>
                            <td>出货库号</td>
                            <td>{{ form.shipmentWarehouseNo && form.shipmentWarehouseNo.warehouseNumber || '--' }}</td>
                        </tr>
                        <tr>
                            <td>入货经手人</td>
                            <td>{{ form.incomingBy && form.incomingBy.name || '--' }}</td>
                        </tr>
                        <tr>
                            <td>入货库号</td>
                            <td>{{ form.incomingWarehouseNo && form.incomingWarehouseNo.warehouseNumber || '--' }}</td>
                        </tr>
                        <tr>
                            <td>备注</td>
                            <td>{{ form.remark || '--' }}</td>
                        </tr>
                    </table>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;">
                        操作日志
                    </h3>
                    <LogQuery ref="childRef" />
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;" v-if="type != 'record'">
                        审批意见
                    </h3>
                    <Audit ref="auditRef" v-if="type != 'record'"  @refresh="getList"/>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="next">取消</el-button>
                    <el-button type="primary" @click="prev" v-if="type != 'record'">确认</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, watch, defineExpose, onMounted } from 'vue'
import moment from 'moment'
import Table from "@/components/assist/massRange/Table.vue";
import transferApplication from '@/api/erp/transferApplication'
import Audit from '@/components/detailsForm/audit.vue'
import LogQuery from '@/components/detailsForm/logQuery.vue'
import { drugApi } from "@/api/model/commodity/drug/index";

const { proxy } = getCurrentInstance();
const loading = ref(false);
const step1_list = ref([])
const form = ref({})
const allocationType = ref([])
const reviewStatus = ref([])
const childRef = ref(null)
const auditRef = ref(null)
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    beforeClose: {
        type: Function,
        default: () => { }
    },
    data: {
        type: Object,
        default: () => { }
    },
    getList: {
        type: Function,
        default: () => { }
    },
    type: {
        type: String,
        default: ''
    },
})
const detailFlag = ref(false)
const { visible, beforeClose, type, getList, data } = toRefs(props)
const { queryParams, rules, formRules } = toRefs(data);
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}

const getDetail = () => {
    loading.value = true
    transferApplication.getIdOrder({ id: data.value.documentId }).then(res => {
        if (res.code == 200) {
            loading.value = false
            step1_list.value = res.data?.transferFormDtos
            form.value = res?.data.transferDto
        } else {
            proxy.msgError(res.msg)
            loading.value = false
        }
    })
}
const next = () => {
    beforeClose.value()
}
const prev = async () => {
    if(!auditRef.value.form?.status) return proxy.msgError('请先选择审批结果！')
    await auditRef.value.formSub(data.value?.id);
    await beforeClose.value()
}
async function dict() {
    allocationType.value = await proxy.getDictList('allocation_type')
    reviewStatus.value = await proxy.getDictList('erp_review_status')
}
onMounted(async () => {
    loading.value = true
    await dict()
    getDetail()
    const reviewRes = await transferApplication.getReview({ 'transfer.id': data.value.documentId })
    let resq = await drugApi.drugLog({ masterId: data.value.documentId })
    if ((reviewRes.code == 200 && reviewRes.data) || (resq.code == 200 && resq.data)) {
        childRef.value?.timeFns(reviewRes?.data?.records, resq?.data?.records)
    }
    loading.value = false
})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.rowStyle {
    .el-col {
        margin-top: 20px;
        font-size: 15px;

        .rowTitle {
            width: 120px;
            text-align: right;
            display: inline-block;
            font-size: 15px;
            font-weight: bolder;
            color: #000;
        }

        .rowMess {
            color: #4d4d4d;
            font-weight: 600;
        }

        .rowRed {
            color: red;
        }
    }
}

.total {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    margin: 0px 20px;

    p {
        margin-right: 50px;
        margin-top: 20px;

        & span:nth-of-type(1) {
            font-size: 15px;
            font-weight: bold;
            color: #333;
            display: inline-block;
            width: 130px;
            text-align: right;
        }

        .red {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}

.box_date {
    width: 220px;
}

.step {
    margin-bottom: 30px;
}

.step1_search_btn {
    width: 60px;
    margin-left: 50px;
}

h3 {
    color: black;
}

.detailTable {
    width: 100%;
    background-color: #eaedf3;
    font-size: 14px;
    border-radius: 5px;

    tr {
        height: 40px;

        td {
            background-color: white;
        }

        td:nth-child(1) {
            padding: 0 10px;
            font-weight: bold;
            width: 20%;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            width: 80%;
            color: #606266;
            padding: 0 10px;
        }
    }

}

.messTable {
    width: 100%;
    background-color: #eaedf3;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    padding: 1px 1px 0 1px;

    tr {
        margin-bottom: 1px;
        display: flex;

        td {
            background-color: white;
            line-height: 40px;
        }

        td:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2
        }
    }
}

.titleH4 {
    margin-bottom: 20px;
    color: #000;
    font-weight: bolder;
    font-size: 15px;
}

</style>
