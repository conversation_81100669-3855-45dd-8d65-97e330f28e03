<template>
  <el-form-item class="last-form-item">
    <el-button icon="el-icon-search" type="primary" @click="handleQuery" :loading="loading">搜索</el-button>
    <el-button icon="el-icon-refresh" type="info" @click="resetQuery" :loading="loading">重置</el-button>
    <div v-if="isShowAllSwitch">
      <el-link v-show="isShowAll" style="margin: 0 5px 0" type="primary" @click="showAllClick">
        <el-icon>
          <el-icon-arrow-up-bold />
        </el-icon>
        折叠
      </el-link>
      <el-link v-show="!isShowAll" style="margin: 0 0 0 10px" type="primary" @click="showAllClick">展开
        <el-icon>
          <el-icon-arrow-down-bold />
        </el-icon>
      </el-link>
    </div>
  </el-form-item>
</template>
<script>
export default {
  name: 'SearchButton',
  props: {
    isShowAll: {},
    // 是否开启展开收起
    isShowAllSwitch: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    showAllClick() {
      this.$emit('showAllClick');
    },
    handleQuery() {
      this.$emit('handleQuery');
    },
    resetQuery() {
      this.$emit('resetQuery', 'queryForm');
    }
  }
};
</script>
<style lang="scss" scoped></style>
