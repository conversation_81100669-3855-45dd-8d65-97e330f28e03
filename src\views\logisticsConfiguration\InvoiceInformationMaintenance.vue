<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="抬头名称" prop="invoiceHead" style="width: 250px">
                    <el-input v-model="queryForm.invoiceHead" clearable placeholder="请输入抬头名称" @clear="handleQuery" @keyup.enter="handleQuery" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10">
                <el-button icon="el-icon-plus" type="primary" @click="onAddInvoiceInformation">添加</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="InvoiceInformationMaintenanceList" @queryTable="getList"></right-toolbar>
            </div>
            <column-table ref="InvoiceInformationMaintenanceList" v-loading="loading" :columns="columns" :data="dataList" :max-height="600">
                <template #createBy="{ row }">
                    {{ row.createBy.name }}
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-edit" link size="small" type="warning" @click="onEditInvoiceInformation(row)">修改</el-button>
                    <el-button icon="el-icon-delete" link size="small" type="danger" @click="onDeleteInvoiceInformation(row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- 新增、修改开票信息	-->
        <el-drawer v-model="invoiceInformationVisible" title="开票信息" @close="onCloseInvoiceInformation">
            <div class="p-10" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <el-form ref="invoiceInformationForm" :model="invoiceInformationForm" :rules="invoiceInformationFormRules" label-width="auto">
                        <el-form-item label="抬头名称" prop="invoiceHead">
                            <el-input v-model="invoiceInformationForm.invoiceHead" clearable placeholder="请输入抬头名称"></el-input>
                        </el-form-item>
                        <el-form-item label="单位税号" prop="taxNo">
                            <el-input v-model="invoiceInformationForm.taxNo" clearable placeholder="请输入单位税号" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item label="注册地址" prop="address">
                            <el-input v-model="invoiceInformationForm.address" clearable placeholder="请输入注册地址"></el-input>
                        </el-form-item>
                        <el-form-item label="注册电话" prop="phone">
                            <el-input v-model="invoiceInformationForm.phone" clearable placeholder="请输入注册电话"></el-input>
                        </el-form-item>
                        <el-form-item label="开户银行" prop="openBank">
                            <el-input v-model="invoiceInformationForm.openBank" clearable placeholder="请输入开户银行"></el-input>
                        </el-form-item>
                        <el-form-item label="银行账号" prop="bankAccount">
                            <el-input v-model="invoiceInformationForm.bankAccount" clearable placeholder="请输入银行账号" show-word-limit></el-input>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div class="flex justify-end mt-10">
                    <el-button @click="onCloseInvoiceInformation">取消</el-button>
                    <el-button type="primary" @click="onSaveInvoicingInformation">保存</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import invoiceInformationMaintenance from '@/api/shipperEnd/invoiceInformationMaintenance';

export default {
    name: 'InvoiceInformationMaintenance',
    components: { ColumnTable, RightToolbar, SearchButton },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                invoiceHead: undefined
            },
            columns: [
                { title: '抬头名称', key: 'invoiceHead', align: 'center', width: '200px', fixed: 'left', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '单位税号', key: 'taxNo', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '注册地址', key: 'address', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '注册电话', key: 'phone', align: 'center', width: '120px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '开户银行', key: 'openBank', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '银行账号', key: 'bankAccount', align: 'center', width: '250px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createBy', align: 'center', width: '220px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '130px', fixed: 'right', hideFilter: true, columnShow: true, showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            // 新增、修改开票信息 抽屉状态
            invoiceInformationVisible: false,
            // 新增、修改开票信息 表单数据
            invoiceInformationForm: {
                invoiceHead: undefined,
                taxNo: undefined,
                address: undefined,
                phone: undefined,
                openBank: undefined,
                bankAccount: undefined
            },
            invoiceInformationFormRules: {
                invoiceHead: [
                    { required: true, message: '请输入抬头名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
                ],
                taxNo: [
                    { required: true, message: '请输入单位税号', trigger: 'blur' },
                    { pattern: /^[A-Za-z0-9]{15,20}$/, message: '请输入正确的单位税号', trigger: 'blur' }
                ],
                address: [
                    { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
                ],
                phone: [
                    { pattern: /^(1[3456789]\d{9})$|^(0\d{2,3}-?\d{7,8})$/, message: '请输入正确的手机号码或固定电话号码', trigger: 'blur' }
                ],
                openBank: [
                    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
                ],
                bankAccount: [
                    { pattern: /^[0-9]{16,19}$/, message: '请输入16-19位数字的银行账号', trigger: 'blur' }
                ]
            },
            // 承运商列表
            signAdvancePaymentContract: []
        };
    },
    created() {
        this.handleQuery();
    },
    methods: {
        /**
         * 获取列表
         */
        async getList() {
            try {
                this.loading = true;
                const { ...params } = this.queryForm;
                const res = await invoiceInformationMaintenance.getList(params);
                if (res.code === 200) {
                    this.dataList = res.data.records;
                    this.total = res.data.total;
                } else {
                    this.$message.error(res.msg || '获取数据失败');
                    this.dataList = [];
                    this.total = 0;
                }
            } catch (error) {
                this.$message.error('系统异常，请稍后重试');
                this.dataList = [];
                this.total = 0;
            } finally {
                this.loading = false;
            }
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            this.getList();
        },
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
        },
        /**
         * 重置查询条件
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /**
         * 关闭开票信息抽屉
         */
        onCloseInvoiceInformation() {
            this.resetForm('invoiceInformationForm');
            this.invoiceInformationForm = {
                invoiceHead: undefined,
                taxNo: undefined,
                address: undefined,
                phone: undefined,
                openBank: undefined,
                bankAccount: undefined
            };
            this.invoiceInformationVisible = false;
        },
        /**
         * 保存开票信息
         */
        onSaveInvoicingInformation() {
            this.$refs.invoiceInformationForm.validate(async (valid) => {
                if (valid) {
                    try {
                        const params = {
                            ...this.invoiceInformationForm,
                            source: '1',
                            carrier: {
                                id: this.$TOOL.data.get('Organization')[0].id
                            }
                        };
                        const res = await invoiceInformationMaintenance.save(params);
                        if (res.code === 200) {
                            this.$message.success('保存成功');
                            this.onCloseInvoiceInformation();
                            await this.getList();
                        } else {
                            this.$message.error(res.msg || '保存失败');
                        }
                    } catch (error) {
                        this.$message.error('系统异常，请稍后重试');
                    }
                }
            });
        },
        /**
         * 添加开票信息
         */
        onAddInvoiceInformation() {
            this.invoiceInformationVisible = true;
        },
        /**
         * 修改开票信息
         * @param row
         */
        onEditInvoiceInformation(row) {
            this.invoiceInformationVisible = true;
            this.$nextTick(() => {
                this.invoiceInformationForm = { ...row };
            });
        },
        /**
         * 删除开票信息
         * @param row
         */
        onDeleteInvoiceInformation(row) {
            this.$confirm('是否删除该开票信息？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(async () => {
                    try {
                        const res = await invoiceInformationMaintenance.delete({
                            ids: row.id
                        });
                        if (res.code === 200) {
                            this.$message.success('删除成功');
                            await this.getList();
                        } else {
                            this.$message.error(res.msg || '删除失败');
                        }
                    } catch (error) {
                        this.$message.error('系统异常，请稍后重试');
                    }
                })
                .catch(() => {
                    this.$message.info('已取消删除');
                });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
}
</style>
