/* 全局 */
#app,
body,
html {
    width: 100%;
    height: 100%;
    background-color: #f6f8f9;
    font-size: 12px;
    font-family: 'Microsoft YaHei';
}
a {
    color: #333;
    text-decoration: none;
}
a:hover,
a:focus {
    color: #000;
    text-decoration: none;
}
a:link {
    text-decoration: none;
}
a:-webkit-any-link {
    text-decoration: none;
}
a,
button,
input,
textarea {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    box-sizing: border-box;
    outline: none !important;
    -webkit-appearance: none;
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    outline: none;
}

/* 大布局样式 */
.aminui {
    display: flex;
    flex-flow: column;
}
.aminui-wrapper {
    display: flex;
    flex: 1;
    overflow: auto;
}

/* 全局滚动条样式 */
.scrollable {
    -webkit-overflow-scrolling: touch;
}
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}
::-webkit-scrollbar-thumb {
    background-color: rgba(50, 50, 50, 0.3);
}
::-webkit-scrollbar-thumb:hover {
    background-color: rgba(50, 50, 50, 0.6);
}
::-webkit-scrollbar-track {
    background-color: rgba(50, 50, 50, 0.1);
}
::-webkit-scrollbar-track:hover {
    background-color: rgba(50, 50, 50, 0.2);
}

/*布局设置*/
.layout-setting {
    position: fixed;
    width: 40px;
    height: 40px;
    border-radius: 3px 0 0 3px;
    bottom: 100px;
    right: 0px;
    z-index: 100;
    background: #409eff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.layout-setting i {
    font-size: 18px;
    color: #fff;
}

/* 头部 */
.adminui-header {
    height: 60px;
    background: #2878ff;
    color: #fff;
    display: flex;
    justify-content: space-between;
}
.adminui-header-left {
    display: flex;
    align-items: center;
    padding-left: 20px;
}
.adminui-header-right {
    display: flex;
    align-items: center;
}
.adminui-header .logo-bar {
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
}
.adminui-header .logo-bar .logo {
    margin-right: 10px;
}
.adminui-header .nav {
    display: flex;
    height: 100%;
    margin-left: 40px;
}
.adminui-header .nav li {
    padding: 0 10px;
    margin: 0 10px 0 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    list-style: none;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
}
.adminui-header .nav li i {
    margin-right: 5px;
}
.adminui-header .nav li:hover {
    color: #fff;
}
.adminui-header .nav li.active {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}
.adminui-header .user-bar .panel-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}
.adminui-header .user-bar .user label {
    color: #fff;
}

/* 左侧菜单 */
.aminui-side-split {
    width: 65px;
    flex-shrink: 0;
    background: #222b45;
    display: flex;
    flex-flow: column;
}
.aminui-side-split-top {
    height: 49px;
}
.aminui-side-split-top a {
    display: inline-block;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.aminui-side-split-top .logo {
    height: 30px;
    vertical-align: bottom;
}
.adminui-side-split-scroll {
    overflow: auto;
    overflow-x: hidden;
    height: 100%;
    flex: 1;
}
.aminui-side-split li {
    cursor: pointer;
    width: 65px;
    height: 65px;
    color: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.aminui-side-split li i {
    font-size: 18px;
}
.aminui-side-split li p {
    margin-top: 5px;
}
.aminui-side-split li:hover {
    background: rgba(255, 255, 255, 0.1);
}
.aminui-side-split li.active {
    background: #409eff;
}

.adminui-side-split-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 5px;
}
.adminui-side-split-scroll::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.5);
}
.adminui-side-split-scroll::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0);
}
.adminui-side-split-scroll::-webkit-scrollbar-track:hover {
    background-color: rgba(255, 255, 255, 0);
}

.aminui-side {
    display: flex;
    flex-flow: column;
    flex-shrink: 0;
    width: 210px;
    background: #fff;
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
    border-right: 1px solid #e6e6e6;
    transition: width 0.3s;
}
.adminui-side-top {
    border-bottom: 1px solid #ebeef5;
    height: 50px;
    line-height: 50px;
}
.adminui-side-top h2 {
    padding: 0 20px;
    font-size: 17px;
    color: #3c4a54;
}
.adminui-side-scroll {
    overflow: auto;
    overflow-x: hidden;
    flex: 1;
}
.adminui-side-bottom {
    border-top: 1px solid #ebeef5;
    height: 51px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}
.adminui-side-bottom i {
    font-size: 16px;
}
.adminui-side-bottom:hover {
    color: var(--el-color-primary);
}
.aminui-side.isCollapse {
    width: 65px !important;
}
.el-menu .menu-tag {
    position: absolute;
    height: 18px;
    line-height: 18px;
    background: var(--el-color-danger);
    font-size: 12px;
    color: #fff;
    right: 20px;
    border-radius: 18px;
    padding: 0 6px;
}
.el-menu .el-sub-menu__title .menu-tag {
    right: 40px;
}
.el-menu--horizontal > li .menu-tag {
    display: none;
}

/* 右侧内容 */
.aminui-body {
    flex: 1;
    display: flex;
    flex-flow: column;
}

.adminui-topbar {
    height: 50px;
    border-bottom: 1px solid #ebeef5;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    justify-content: space-between;
}
.adminui-topbar .left-panel {
    display: flex;
    align-items: center;
}
.adminui-topbar .right-panel {
    display: flex;
    align-items: center;
}

.right-panel-search {
    display: flex;
    align-items: center;
}
.right-panel-search > * + * {
    margin-left: 10px;
}

.adminui-tags {
    height: 40px;
    padding-top: 4px;
    margin-top: -9px;
    background: #fff;
    border-bottom: 1px solid #e6e6e6;
}
.adminui-tags ul {
    display: flex;
    overflow: hidden;
}
.adminui-tags li {
    cursor: pointer;
    display: inline-block;
    float: left;
    height: 30px;
    line-height: 34px;
    position: relative;
    flex-shrink: 0;
}
.adminui-tags li::after {
    content: ' ';
    width: 1px;
    height: 100%;
    position: absolute;
    right: 0px;
    background-image: linear-gradient(#fff, #e6e6e6);
}
.adminui-tags li a {
    display: inline-block;
    padding: 0 10px;
    width: 100%;
    height: 100%;
    color: #999;
    text-decoration: none;
    display: flex;
    align-items: center;
}
.adminui-tags li i {
    margin-left: 10px;
    border-radius: 3px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.adminui-tags li i:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #fff;
}
.adminui-tags li:hover {
    background: #ecf5ff;
}
.adminui-tags li.active {
    background: #409eff;
}
.adminui-tags li.active a {
    color: #fff;
}
.adminui-tags li.sortable-ghost {
    opacity: 0;
}

.adminui-main {
    overflow: auto;
    background-color: #f6f8f9;
    flex: 1;
}

/*页面最大化*/
.aminui.main-maximize {
    .main-maximize-exit {
        display: block;
    }
    .aminui-side-split,
    .aminui-side,
    .adminui-header,
    .adminui-topbar,
    .adminui-tags {
        display: none;
    }
}
.aminui-side {
    width: 270px !important;
}
.main-maximize-exit {
    display: none;
    position: fixed;
    z-index: 3000;
    top: -20px;
    left: 50%;
    margin-left: -20px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.2);
    text-align: center;
}
.main-maximize-exit i {
    font-size: 14px;
    margin-top: 22px;
    color: #fff;
}
.main-maximize-exit:hover {
    background: rgba(0, 0, 0, 0.4);
}

/*定宽页面*/
.sc-page {
    width: 1230px;
    margin: 0 auto;
}
.el-table__header th {
    color: #333 !important;
    background-color: #f9f9f9 !important;
}

::v-deep .form_225 {
    width: 225px;
}
::v-deep .form_130 {
    .el-form-item__label {
        width: 130px;
    }
}
.el-message-box__title {
    span {
        font-size: 15px;
    }
}
.mt10 {
    margin-top: 10px;
}
.mb10 {
    margin-bottom: 10px;
}
.ml10 {
    margin-left: 10px;
}
.box-m-10 {
    margin: 10px;
}
.box-flex-right {
    display: flex;
    justify-content: flex-end;
}
.border-bottom-1 {
    height: 1px;
    background-color: #e6ebf5;
    margin-bottom: 15px;
    width: calc(100% + 40px);
    margin-left: -20px;
}
.p10 {
    padding: 10px;
}
.el-upload-list__item-actions {
    .el-icon {
        height: 60px;
        width: 60px;
        svg {
            width: 30px;
            height: 30px;
        }
    }
}

.seache-form {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    .el-form-item {
        margin-bottom: 3px;
        margin-top: 3px;
        display: flex;
        align-items: center;
        margin-right: 10px;
    }
    .date-screening {
        width: 460px;
        .el-form-item__label {
            flex-shrink: 0;
        }
    }
    .time-screening {
        width: 540px;
        .el-form-item__label {
            flex-shrink: 0;
        }
    }
    .last-form-item {
        margin-left: auto;
        margin-right: 0;
        // margin-bottom: 0;
        .el-form-item__content {
            margin-left: 0 !important;
        }
    }
}
@media screen and (max-width: 1366px) {
    .seache-form {
        .el-form-item {
            margin-right: 5px;
        }
        .el-form-item:not(.last-form-item) {
            width: 270px;
        }
        .el-form-item.date-screening,
        .el-form-item.time-screening {
            width: 267px;
            .el-range-editor {
                width: 100%;
            }
        }
    }
}
.app-container {
    padding: 10px;
}
.el-table {
    .el-table__body {
        tr.hover-row {
            td.el-table__cell {
                background-color: #e0f0ff !important;
            }
        }
    }
}
.w-full {
    width: 100% !important;
}
.w-50 {
    width: 50% !important;
}
.mt-10 {
    margin-top: 10px;
}
.ml-10 {
    margin-left: 10px;
}
.mr-5 {
    margin-right: 5px;
}
.flex {
    display: flex;
}
.grid {
    display: grid;
}
.flex-col {
    flex-direction: column;
}
.justify-items-start {
    justify-items: start;
}
.justify-items-end {
    justify-items: end;
}
.justify-items-center {
    justify-items: center;
}
.justify-items-stretch {
    justify-items: stretch;
}
.justify-normal {
    justify-content: normal;
}
.justify-start {
    justify-content: flex-start;
}
.justify-end {
    justify-content: flex-end;
}
.justify-center {
    justify-content: center;
}
.justify-between {
    justify-content: space-between;
}
.justify-around {
    justify-content: space-around;
}
.justify-evenly {
    justify-content: space-evenly;
}
.justify-stretch {
    justify-content: stretch;
}
.items-start {
    align-items: flex-start;
}
.items-end {
    align-items: flex-end;
}
.items-center {
    align-items: center;
}
.items-baseline {
    align-items: baseline;
}
.items-stretch {
    align-items: stretch;
}
.m-0 {
    margin: 0;
}
.mx-0 {
    margin-left: 0;
    margin-right: 0;
}
.mx-5 {
    margin-left: 5px;
    margin-right: 5px;
}
.my-0 {
    margin-top: 0;
    margin-bottom: 0;
}
.mr-0 {
    margin-right: 0;
}
.ml-0 {
    margin-left: 0;
}
.mt-0 {
    margin-top: 0;
}
.mb-0 {
    margin-bottom: 0;
}
.mb-5 {
    margin-bottom: 5px;
}
.mb-10 {
    margin-bottom: 10px;
}
.mb-40 {
    margin-bottom: 40px;
}
.my-5 {
    margin-top: 5px;
    margin-bottom: 5px;
}
.p-10 {
    padding: 10px;
}
.w-min {
    width: min-content;
}
.w-max {
    width: max-content;
}
.custom__form__item .el-form-item__label {
    white-space: pre-line;
    text-align-last: end;
    margin-top: -4px;
    line-height: 20px;
}
.el-upload-list--picture-card .el-upload-list__item-actions span + span {
    margin-left: 1em;
}
.text-red-500 {
    color: rgb(239 68 68);
}
.text-amber-500 {
    color: rgb(245 158 11);
}
.text-blue-500 {
    color: rgb(59 130 246);
}
.text-green-500 {
    color: #039d12;
}
.text-main-500 {
    color: rgb(51, 51, 51);
}
.text-secondary-500 {
    color: rgb(88, 88, 88);
}
.whitespace-nowrap {
    white-space: nowrap;
}
.word-break-break-word {
    word-break: break-word;
}
.text-base {
    font-size: 14px;
}
.px-10 {
    padding-left: 10px;
    padding-right: 10px;
}
.hidden {
    display: none;
}
.font-bold {
    font-weight: bold;
}
.text-black {
    color: #000;
}
.el-dropdown-menu.table-dropdown-menu .el-dropdown-menu__item.is-active {
    background-color: #e8f4ff;
    color: #46a6ff;
}
.text-center {
    text-align: center;
}
.text-left {
    text-align: left;
}
.text-right {
    text-align: right;
}
.w-auto {
    width: auto;
}
.font-color-main {
    color: #72767b;
}
.h-100 {
    height: 100%;
}
.el-drawer__header {
    margin-bottom: 20px;
}
.self-drawer .el-drawer__header {
    margin-bottom: 10px;
}
.self-drawer .el-drawer__body {
    padding: 10px 15px;
}
.customer-auto-height-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 104px);
    .customer-auto-height-card {
        flex: 0 0 auto;
    }
    .customer-auto-height-table {
        overflow: auto;

        .el-table {
            height: 100%;
        }
    }
}
