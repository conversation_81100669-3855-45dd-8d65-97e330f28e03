<template>
    <div class="app-container customer-auto-height-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="账单类型" prop="billType" style="width: 250px">
                    <el-select v-model="queryForm.billType" clearable placeholder="请选择账单类型" style="width: 100%" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in billTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="收款单号" prop="paymentOrderNo" style="width: 250px">
                    <el-input v-model="queryForm.paymentOrderNo" clearable placeholder="请输入收款单号" @clear="handleChangesTheOrderType('queryForm')" @keyup.enter="handleChangesTheOrderType('queryForm')"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单类型" prop="type" style="width: 250px">
                    <el-select v-model="queryForm.type" clearable disabled placeholder="请选择订单类型" style="width: 100%" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in settlementManagementOrderTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="queryForm.type === '2'" label="货主公司" prop="companyId" style="width: 250px">
                    <el-select v-model="queryForm.companyId" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="结算公司" prop="settlementCompanyId">
                    <el-select v-model="queryForm.settlementCompanyId" clearable filterable placeholder="请选择结算公司" style="width: 100%" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in settlementCompanyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="到账公司" prop="billArriveCompany">
                    <el-select v-model="queryForm.billArriveCompany" clearable placeholder="请选择到账公司" style="width: 100%" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in receivingCompanyList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="是否发起付款申请" prop="applyStatus">
                    <el-select v-model="queryForm.applyStatus" clearable placeholder="请选择是否发起付款申请" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in applyStatusList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收款单状态" prop="status">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择收款单状态" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in statusList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收款单类型" prop="paymentDocType">
                    <el-select v-model="queryForm.paymentDocType" clearable placeholder="请选择收款单类型" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in paymentReceiptType" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="是否发起开票申请" prop="invoicingStatus">
                    <el-select v-model="queryForm.invoicingStatus" clearable placeholder="请选择是否发起开票申请" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in invoiceStatusList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="核销人" prop="reversedName" style="width: 250px">
                    <el-input v-model="queryForm.reversedName" clearable placeholder="请输入核销人" @keyup.enter="handleChangesTheOrderType('queryForm')"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收款单创建时间" prop="queryTime" style="width: 350px">
                    <el-date-picker
                        v-model="queryForm.queryTime"
                        :shortcuts="shortcuts"
                        end-placeholder="结束日期"
                        range-separator="至"
                        start-placeholder="开始日期"
                        style="width: 260px"
                        type="daterange"
                        unlink-panels
                        value-format="YYYY-MM-DD"
                        @change="handleChangesTheOrderType('queryForm')"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="账单月度" prop="billDate" style="width: 200px">
                    <el-date-picker v-model="queryForm.billDate" class="w-full" placeholder="选择账单月度" type="month" value-format="YYYY-MM" @change="handleChangesTheOrderType('queryForm')"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleChangesTheOrderType('queryForm')" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <div class="flex justify-around">
                <el-statistic :precision="2" :value="statistics.totalReceivableCost" :value-style="{ color: '#5670FE' }" group-separator="," title="收款单合计金额"></el-statistic>
                <el-statistic :precision="2" :value="statistics.totalPaidCost" :value-style="{ color: '#1ACD7E' }" group-separator="," title="已付款金额"></el-statistic>
                <el-statistic :precision="2" :value="statistics.totalUnpaidCost" :value-style="{ color: '#F4AC00' }" group-separator="," title="未付款金额"></el-statistic>
                <el-statistic :precision="2" :value="statistics.totalInvoicingCost" :value-style="{ color: '#32CD32' }" group-separator="," title="已开票金额"></el-statistic>
                <el-statistic :precision="2" :value="statistics.totalUninvoicingCost" :value-style="{ color: '#DC143C' }" group-separator="," title="未开票金额"></el-statistic>
            </div>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div class="mb10">
                <el-button :disabled="multiple" type="primary" @click="handleClickSubmit">付款信息提交</el-button>
                <el-button :disabled="multiple" type="primary" @click="onOpenInvoice">申请开票</el-button>
                <el-button :disabled="multiple" type="primary" @click="handleClickQuash">撤销</el-button>
                <!--<el-button :disabled="multiple" type="primary" @click="handleClickBulkWriteOff">批量核销</el-button>-->
                <el-button :disabled="!orderList || orderList.length === 0" icon="el-icon-download" type="warning" @click="handleExportAll">全部导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="customerPaymentDocument" @queryTable="getList" />
            </div>
            <column-table ref="customerPaymentDocument" v-loading="loading" :columns="columns" :data="orderList" :show-summary="true" :showCheckBox="true" class="customer-auto-height-table" max-height="null" @selection-change="handleSelectionChange">
                <template #billType="{ row }">
                    <span>{{ formatDictionaryData('billTypeList', row.billType) }}</span>
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ formatDictionaryData('statusList', row.status) }}</span>
                </template>
                <template #discountType="{ row }">
                    <span>{{ formatDictionaryData('discountTypeList', row.discountType) }}</span>
                </template>
                <template #applyStatus="{ row }">
                    <span :style="setApplyStatusColor(row.applyStatus)">{{ formatDictionaryData('applyStatusList', row.applyStatus) }}</span>
                </template>
                <template #billArriveCompany="{ row }">
                    <span>{{ formatDictionaryData('receivingCompanyList', row.billArriveCompany) }}</span>
                </template>
                <template #paymentDocType="{ row }">
                    <span>{{ formatDictionaryData('paymentReceiptType', row.paymentDocType) }}</span>
                </template>
                <template #billArriveTime="{ row }">
                    <span>{{ row.billArriveTime ? row.billArriveTime.substring(0, 10) : '' }}</span>
                </template>
                <template #invoicingStatus="{ row }">
                    <span :style="setInvoiceStatusColor(row.invoicingStatus)">{{ formatDictionaryData('invoiceStatusList', row.invoicingStatus) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickBillingDetails(row)">账单明细</el-button>
                    <el-button v-if="row.status == '1'" icon="el-icon-edit" link size="small" type="warning" @click="handleBadDebtRegistration(row)">坏账登记</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>

        <!--  /账单明细   -->
        <el-drawer v-if="billDetailsVisible && billDetailsType === 'normal'" v-model="billDetailsVisible" size="90%" title="账单明细" @close="hideBillDetails">
            <div class="p16" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="billDetailsForm" :inline="true" :model="billDetailsForm" class="seache-form">
                        <el-form-item label="订单号" prop="orderNo">
                            <el-input v-model="billDetailsForm.orderNo" clearable placeholder="请输入订单号" @clear="handleChangesTheOrderType('billDetailsForm')" @keyup.enter="handleChangesTheOrderType('billDetailsForm')"></el-input>
                        </el-form-item>
                        <el-form-item label="货主公司" prop="companyName">
                            <span style="font-weight: bold">{{ billDetailsRow.companyName }}</span>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="billDetailsForm.transType" clearable placeholder="请选择产品类型" style="width: 100%" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in productTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productType">
                            <el-select v-model="billDetailsForm.productType" clearable placeholder="请选择货物类型" style="width: 100%" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温区类型" prop="temperatureType">
                            <el-select v-model="billDetailsForm.temperatureType" clearable placeholder="请选择温区类型" style="width: 100%" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in temperatureTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="对账单状态" prop="status">
                            <el-select v-model="billDetailsForm.status" clearable placeholder="请选择对账单状态" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in orderStatusList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单月度" prop="billDate">
                            <el-date-picker v-model="billDetailsForm.billDate" class="w-full" placeholder="选择账单月度" type="month" value-format="YYYY-MM" @change="handleChangesTheOrderType('billDetailsForm')"></el-date-picker>
                        </el-form-item>
                        <el-form-item style="margin-left: auto; margin-right: 0; margin-bottom: 0">
                            <el-button icon="el-icon-search" type="primary" @click="handleChangesTheOrderType('billDetailsForm')">搜索</el-button>
                            <el-button icon="el-icon-refresh" type="info" @click="resetBillDetailsForm">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <column-table :columns="billDetailsColumns" :data="billDetailsList" :element-loading-text="billDetailsLoadingText" :loading="billDetailsLoading">
                        <template #orderNo="{ row }">
                            <el-link type="primary" @click="handleClickOrderDetail(row)">{{ row.orderNo }}</el-link>
                        </template>
                        <template #discountType="{ row }">
                            <span>{{ formatDictionaryData('discountTypeList', row.discountType) }}</span>
                        </template>
                        <template #temperatureType="{ row }">
                            <span>{{ formatDictionaryData('temperatureTypeList', row.temperatureType) }}</span>
                        </template>
                        <template #settlementMethod="{ row }">
                            <span>{{ formatDictionaryData('settlementMethodList', row.settlementMethod) }}</span>
                        </template>
                        <template #type="{ row }">
                            <span>{{ formatDictionaryData('settlementManagementOrderTypeList', row.type) }}</span>
                        </template>
                        <template #productType="{ row }">
                            <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                        </template>
                        <template #totalReceivedCost="{ row }">
                            <div>{{ row.status == '4' ? row.totalReceivedCost : '--' }}</div>
                        </template>
                        <template #status="{ row }">
                            <div v-html="formatStatus(row.status)"></div>
                        </template>
                        <template #collectFormula="{ row }">
                            <el-popover placement="top" width="400">
                                <pre>{{ row.collectFormula }}</pre>
                                <template #reference>
                                    <el-link size="small">查看</el-link>
                                </template>
                            </el-popover>
                        </template>
                        <template #transportFormula="{ row }">
                            <el-popover placement="top" width="400">
                                <pre>{{ row.transportFormula }}</pre>
                                <template #reference>
                                    <el-link size="small">查看</el-link>
                                </template>
                            </el-popover>
                        </template>
                        <template #deliveryFormula="{ row }">
                            <el-popover placement="top" width="400">
                                <pre>{{ row.deliveryFormula }}</pre>
                                <template #reference>
                                    <el-link size="small">查看</el-link>
                                </template>
                            </el-popover>
                        </template>
                        <template #opt="{ row }">
                            <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickFeeDetails(row)">费用详情</el-button>
                        </template>
                    </column-table>
                </el-card>
                <el-card v-if="badDebtRegistrationList.length > 0" :body-style="{ padding: '10px' }" class="mt-10" shadow="never">
                    <h3 class="mb10">坏账登记信息</h3>
                    <column-table :columns="badDebtRegistrationColumns" :data="badDebtRegistrationList" :element-loading-text="badDebtRegistrationText" :loading="badDebtRegistrationLoading">
                        <template #type="{ row }">
                            <span>{{ formatDictionaryData('badDebtTypeList', row.type) }}</span>
                        </template>
                        <template #createDate="{ row }">
                            <span>{{ row.createDate ? formatDate(row.createDate) : '' }}</span>
                        </template>
                        <template #opt="{ row }">
                            <el-button link size="small" type="primary" @click="handleClickBadDebtRegistration(row)">查看</el-button>
                        </template>
                    </column-table>
                </el-card>
            </div>
        </el-drawer>

        <!-- 仓储服务费账单明细 -->
        <el-drawer v-if="billDetailsVisible && billDetailsType === 'storage'" v-model="billDetailsVisible" size="1180px" title="账单明细" @close="hideBillDetails">
            <div v-loading="billDetailsLoading" style="padding: 0 10px">
                <column-table :columns="storageBillDetailsColumns" :data="storageBillDetails" :element-loading-text="billDetailsLoadingText" max-height="calc(100vh - 120px)"> </column-table>
            </div>
        </el-drawer>

        <!-- 仓储年费账单明细 -->
        <el-drawer v-if="billDetailsVisible && billDetailsType === 'yearly'" v-model="billDetailsVisible" size="500px" title="账单明细" @close="hideBillDetails">
            <div style="padding: 0 10px">
                <el-descriptions :column="1" border>
                    <el-descriptions-item label="货主公司">{{ yearlyBillDetail.ownerName || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="年费收费方式">{{ formatDictionaryData('chargeWayList', yearlyBillDetail.chargeWay) }}</el-descriptions-item>
                    <el-descriptions-item label="计费开始时间">{{ yearlyBillDetail.startDate || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="计费结束时间">{{ yearlyBillDetail.endDate || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="缴费金额">{{ yearlyBillDetail.payAmount || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="缴费时长(天)">{{ yearlyBillDetail.payDay || '--' }}</el-descriptions-item>
                </el-descriptions>
            </div>
        </el-drawer>

        <!-- 仓储保费账单明细 -->
        <el-drawer v-if="billDetailsVisible && billDetailsType === 'insurance'" v-model="billDetailsVisible" size="500px" title="账单明细" @close="hideBillDetails">
            <div style="padding: 0 10px">
                <el-descriptions :column="1" border>
                    <el-descriptions-item label="货主公司">{{ insuranceBillDetail.ownerName || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="计费开始时间">{{ insuranceBillDetail.startDate || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="计费结束时间">{{ insuranceBillDetail.endDate || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="缴费时长(天)">{{ insuranceBillDetail.payDay || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="保险金额">{{ insuranceBillDetail.amount || '--' }}</el-descriptions-item>
                    <el-descriptions-item label="保险比例">{{ insuranceBillDetail.ratio ? insuranceBillDetail.ratio * 100 + '%' : '--' }}</el-descriptions-item>
                    <el-descriptions-item label="保费">{{ insuranceBillDetail.premium || '--' }}</el-descriptions-item>
                </el-descriptions>
            </div>
        </el-drawer>

        <!--  /坏账登记   -->
        <el-drawer v-if="badDebtRegistrationVisible" v-model="badDebtRegistrationVisible" size="500px" title="坏账登记" @close="hideBadDebtRegistration">
            <div style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="badDebtRegistrationForm" :model="badDebtRegistrationForm" :rules="badDebtRegistrationFormRules" label-width="auto">
                        <el-form-item label="收款单号" prop="paymentOrderNo">
                            <el-input v-model="badDebtRegistrationForm.paymentOrderNo" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="应收合计" prop="receivableCost">
                            <el-input v-model="badDebtRegistrationForm.receivableCost" class="number__unit__input__element" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="实收合计" prop="paidCost">
                            <el-input v-model="badDebtRegistrationForm.paidCost" class="number__unit__input__element" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="未收合计" prop="totalUnpaidCost">
                            <el-input v-model="badDebtRegistrationForm.totalUnpaidCost" class="number__unit__input__element" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="坏账合计" prop="badDebtCost">
                            <el-input v-model="badDebtRegistrationForm.badDebtCost" class="number__unit__input__element" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="坏账金额" prop="amount">
                            <el-input-number v-model="badDebtRegistrationForm.amount" :max="badDebtRegistrationForm.totalUnpaidCost" :min="0" :precision="2" class="number__unit__element" controls-position="right" placeholder="请输入坏账金额" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="坏账类型" prop="type">
                            <el-select v-model="badDebtRegistrationForm.type" clearable placeholder="请选择坏账类型" style="width: 100%">
                                <el-option v-for="item in badDebtTypeList" :key="item.code" :label="item.name" :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="备注" prop="reason">
                            <el-input v-model="badDebtRegistrationForm.reason" :rows="2" placeholder="请输入备注" type="textarea"></el-input>
                        </el-form-item>
                        <el-form-item label="坏账资料" prop="fileUrl">
                            <el-upload :action="uploadFileUrl" :file-list="badDebtRegistrationForm.fileUrl" :headers="headers" :limit="9" :on-preview="handlePicturePreview" :on-success="handleBadDebtFileUploadSuccess" accept="image/*" list-type="picture-card">
                                <el-icon class="el-icon--upload"><el-icon-plus /></el-icon>
                                <template #tip>
                                    <div class="el-upload__tip">最多可上传9张图片，支持.jpg/.jpeg/.png格式文件</div>
                                </template>
                            </el-upload>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
            <template #footer>
                <el-button @click="hideBadDebtRegistration">取消</el-button>
                <el-button type="primary" @click="handleClickConfirmBadDebtRegistration">确定</el-button>
            </template>
        </el-drawer>

        <!-- /订单费用详情  -->
        <order-fee-details-with-details v-if="orderCostVisible" v-model="orderCostVisible" :detail-data="detailData" :fee-breakdown-data="feeBreakdownData" />

        <!-- /图片预览 -->
        <el-image-viewer v-if="dialogVisible" :initial-index="0" :url-list="dialogImageUrl" :z-index="9999" @close="imgClose" />

        <!-- 提交付款信息 -->
        <payment-submission v-if="applyPrepaymentVisible" v-model="applyPrepaymentVisible" :initial-form-data="applyPrepaymentForm" @close="onCloseApplyPrepayment" @submit="onSubmitApplyPrepayment" />

        <!-- 开票 -->
        <invoice-drawer v-if="invoiceVisible" v-model="invoiceVisible" :initial-form-data="invoiceForm" :invoice-list="invoiceList" :is-collect-payment="isCollectPayment" @close="closeInvoice" @submit="submitInvoice" />

        <!-- 汇款金额与收款单金额不匹配 弹窗组件 -->
        <mismatch-dialog v-if="mismatchDialogVisible" v-model="mismatchDialogVisible" :initial-form-data="mismatchFormData" @close="closeMismatchDialog" @submit="submitMismatchDialog" />

        <!-- 选择收款单 -->
        <select-receipt v-if="selectReceiptVisible" v-model="selectReceiptVisible" :available-receipts="availableReceipts" :unavailable-receipts="unavailableReceipts" @submit="handleReceiptSubmit" />

        <!-- 订单详情滑块 -->
        <el-drawer v-model="orderDetailVisible" direction="rtl" size="50%" title="订单详情">
            <order-detail-trans v-if="orderDetailVisible" :orderInfo="orderDetailInfo" :parent-data="transportationData"></order-detail-trans>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import moment from 'moment';
import OrderFeeDetailsWithDetails from '@/views/carrierFunction/OrderFeeDetailsWithDetails.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import customerPaymentDocument from '@/api/carrierEnd/customerPaymentDocument';
import RightToolbar from '@/components/RightToolbar/index.vue';
import { downloadNoData } from '@/utils';
import { selectDictLabel } from '@/utils/dictLabel';
import settlementCompany from '@/api/carrierEnd/settlementCompany';
import PaymentSubmission from '@/components/PaymentSubmission.vue';
import invoiceInformationMaintenance from '@/api/shipperEnd/invoiceInformationMaintenance';
import customerPrepaymentBalance from '@/api/shipperEnd/customerPrepaymentBalance';
import paymentDoc from '@/api/shipperEnd/paymentDoc';
import InvoiceDrawer from '@/components/InvoiceDrawer.vue';
import MismatchDialog from '@/components/MismatchDialog.vue';
import SelectReceipt from '@/components/SelectReceipt.vue';
import OrderDetailTrans from '@/views/orderComponents/OrderDetailTrans.vue';
import orderManagement from '@/api/logisticsManagement/orderManagement';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'CustomerPaymentDocument',
    components: {
        RightToolbar,
        SearchButton,
        ColumnTable,
        OrderFeeDetailsWithDetails,
        PaymentSubmission,
        InvoiceDrawer,
        MismatchDialog,
        SelectReceipt,
        OrderDetailTrans
    },
    data() {
        return {
            billTypeList: [],
            queryForm: {
                current: 1,
                size: 10,
                paymentOrderNo: null,
                type: '2',
                companyId: undefined,
                status: null,
                invoicingStatus: null,
                reversedName: null,
                queryTime: [],
                billDate: undefined,
                billArriveCompany: undefined,
                applyStatus: undefined,
                paymentDocType: undefined,
                settlementCompanyId: undefined
            },
            settlementMethodList: [],
            orderStatusList: [],
            columns: [
                { title: '账单类型', key: 'billType', align: 'center', width: '120px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', width: '120px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '220px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', width: '220px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'billDate', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '折扣合计', key: 'discountCost', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '折扣方式', key: 'discountType', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '其他服务费', key: 'warehouseFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true },
                { title: '纸箱费', key: 'cartonFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '垫付费', key: 'advanceFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '租箱费', key: 'rentalBoxFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '其他费用', key: 'otherFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '费用说明', key: 'feeDesc', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单实收合计', key: 'paidCost', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '坏账总金额', key: 'badDebtCost', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '调整费用合计', key: 'adjustCost', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '核销人', key: 'reversedName', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '到账公司', key: 'billArriveCompany', align: 'center', width: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '到账时间', key: 'billArriveTime', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '是否发起付款申请', key: 'applyStatus', align: 'center', width: '125px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '收款单状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '收款单类型', key: 'paymentDocType', align: 'center', width: '90px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '是否发起开票申请', key: 'invoicingStatus', align: 'center', width: '125px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '180px', fixed: 'right', hideFilter: true, columnShow: true, showOverflowTooltip: true }
            ],
            showSearch: true,
            loading: false,
            orderList: [],
            total: 0,
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            invoicingVisible: false,
            invoiceForm: {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined
            },
            columnsInvoicing: [
                { title: '收款单号', key: 'billNo', align: 'center', width: '191px', columnShow: true, fixed: 'left' },
                { title: '应收金额（元）', key: 'receivableAmount', labelClassName: 'isShowSummary', align: 'center', width: '150px', columnShow: true },
                { title: '实收金额（元）', key: 'actualAmount', labelClassName: 'isShowSummary', align: 'center', width: '150px', columnShow: true },
                { title: '开票金额（元）', key: 'billAmount', labelClassName: 'isShowSummary', align: 'center', width: '150px', columnShow: true },
                { title: '账单日期', key: 'billDate', align: 'center', width: '150px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', width: '150px', columnShow: true }
            ],
            customerList: [],
            customerBillDetailsList: [],
            statusList: [],
            applyStatusList: [],
            invoiceStatusList: [],
            billDetailsVisible: false,
            billDetailsForm: {
                orderNo: null,
                type: null,
                companyName: null,
                productType: null,
                transType: null,
                temperatureType: null,
                status: null,
                billDate: undefined
            },
            billDetailsList: [],
            billDetailsColumns: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '130px', columnShow: true, fixed: 'left' },
                { title: '货主公司', key: 'companyName', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '下单时间', key: 'orderDate', align: 'center', width: '120px', columnShow: true },
                { title: '温层', key: 'temperatureType', align: 'center', width: '120px', columnShow: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '80px', columnShow: true },
                { title: '结算方式', key: 'settlementMethod', align: 'center', width: '100px', columnShow: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收费用', key: 'collectCost', align: 'center', width: '120px', columnShow: true },
                { title: '揽收公式', key: 'collectFormula', align: 'center', width: '130px', columnShow: true },
                { title: '干线费用', key: 'transportCost', align: 'center', width: '120px', columnShow: true },
                { title: '干线公式', key: 'transportFormula', align: 'center', width: '130px', columnShow: true },
                { title: '配送费用', key: 'deliveryCost', align: 'center', width: '120px', columnShow: true },
                { title: '配送公式', key: 'deliveryFormula', align: 'center', width: '130px', columnShow: true },
                { title: '超区费用', key: 'exceedCountyCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true },
                { title: '增值服务总费用', key: 'addedServicesCost', align: 'center', width: '120px', columnShow: true },
                { title: '预估费用', key: 'estimateCost', align: 'center', width: '120px', columnShow: true },
                { title: '调整金额', key: 'adjustCost', align: 'center', width: '120px', columnShow: true },
                { title: '合同价合计', key: 'totalContractPrice', align: 'center', width: '160px', columnShow: true },
                { title: '折扣方式', key: 'discountType', align: 'center', width: '120px', columnShow: true },
                { title: '折扣金额', key: 'discountCost', align: 'center', width: '120px', columnShow: true },
                { title: '应收合计', key: 'totalReceivableCost', align: 'center', width: '120px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '150px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            billDetailsLoading: false,
            invoicingLoading: false,
            invoicingLoadingText: '加载中...',
            dataInvoicing: [],
            billDetailsLoadingText: '加载中...',
            settlementManagementOrderTypeList: [],
            goodsTypeList: [],
            productTypeList: [],
            detailData: {},
            feeBreakdownData: [],
            orderCostVisible: false,
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            tempFileList: [],
            headers: {
                Authorization: 'Bearer ' + this.$TOOL.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            dialogImageUrl: [],
            dialogVisible: false,
            temperatureTypeList: [],
            // 订单费用调整 弹窗状态
            orderExpenseChangeVisible: false,
            billDetailsRow: null,
            isShowAll: false,
            shortcuts: setDatePickerShortcuts(),
            receivingCompanyList: [],
            badDebtRegistrationVisible: false,
            badDebtRegistrationForm: {
                paymentOrderNo: undefined,
                receivableCost: 0,
                paidCost: 0,
                totalUnpaidCost: 0,
                badDebtCost: 0,
                amount: 0,
                type: undefined,
                reason: undefined,
                fileUrl: []
            },
            badDebtRegistrationFormRules: {
                amount: [{ required: true, message: '请输入坏账金额', trigger: 'blur' }],
                type: [{ required: true, message: '请选择坏账类型', trigger: 'blur' }]
            },
            badDebtTypeList: [],
            badDebtRegistrationColumns: [
                { title: '登记时间', key: 'createDate', align: 'center', width: '110px', columnShow: true },
                { title: '登记人', key: 'createName', align: 'center', width: '180px', columnShow: true },
                { title: '坏账金额', key: 'amount', align: 'center', width: '200px', columnShow: true },
                { title: '坏账类型', key: 'type', align: 'center', width: '120px', columnShow: true },
                { title: '备注', key: 'reason', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '坏账资料', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            badDebtRegistrationList: [],
            badDebtRegistrationText: '加载中...',
            badDebtRegistrationLoading: false,
            settlementCompanyList: [],
            applyPrepaymentVisible: false,
            applyPrepaymentForm: {
                companyName: '',
                receiveCompany: '',
                remitAmount: 0,
                remitTime: '',
                remitFile: []
            },
            paymentReceiptType: [],
            invoiceList: [],
            invoiceVisible: false,
            mismatchDialogVisible: false,
            mismatchFormData: {
                selectedData: [],
                receivableCost: 0,
                unpaidCost: 0,
                remitAmount: 0
            },
            selectReceiptVisible: false,
            selectReceiptForm: {
                selectedData: []
            },
            availableReceipts: [],
            unavailableReceipts: [],
            billDetailsType: 'normal', // 账单明细类型：normal-普通账单，storage-仓储服务费，yearly-仓储年费，insurance-仓储保费
            storageBillDetail: {},
            yearlyBillDetail: {},
            insuranceBillDetail: {},
            storageBillDetails: [],
            storageBillDetailsColumns: [
                { title: '账单时间', key: 'month', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'ownerName', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '仓储服务费', key: 'storageAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '扫码上传费', key: 'scanAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '不合格品存放费', key: 'depositAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '不合格品销毁费', key: 'destroyAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '暂存保管费', key: 'custodyAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '应收合计', key: 'receAmount', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true }
            ],
            chargeWayList: [],
            statistics: {
                totalInvoicingCost: 0,
                totalPaidCost: 0,
                totalReceivableCost: 0,
                totalUninvoicingCost: 0,
                totalUnpaidCost: 0
            },
            // 订单详情相关
            orderDetailVisible: false,
            orderDetailInfo: {},
            transportationData: {
                isEdit: false,
                orderNo: '',
                transOrderNo: '',
                isDownloadAllImages: true
            }
        };
    },
    computed: {
        /**
         * 格式化时间
         */
        formatDate() {
            return (value) => {
                if (!value) {
                    return '-';
                }
                return moment(value).format('YYYY-MM-DD');
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        // 格式化订单状态
        formatStatus() {
            return (value) => {
                const statusText = selectDictLabel(this.orderStatusList, value);
                if (value === '0') {
                    return `<span style="color: #B1B1B1">${statusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #F4AC00">${statusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #5670FE">${statusText}</span>`;
                } else if (value === '3') {
                    return `<span style="color: #1ACD7E">${statusText}</span>`;
                } else {
                    return statusText;
                }
            };
        },
        // 判断是否为到付类型
        isCollectPayment() {
            return this.selectData[0]?.paymentDocType === '4';
        },
        /**
         * 设置申请状态颜色
         */
        setApplyStatusColor() {
            return (status) => {
                return (
                    {
                        '1': { color: '#606266' }, // 未申请
                        '2': { color: '#1ACD7E' } // 已申请
                    }[status] || { color: '#999' }
                );
            };
        },
        /**
         * 设置是否发起开票申请颜色
         */
        setInvoiceStatusColor() {
            return (status) => {
                return (
                    {
                        '1': { color: '#606266' }, // 未发起
                        '2': { color: '#1ACD7E' } // 已发起
                    }[status] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#606266' }, // 未付款
                        '1': { color: '#F4AC00' }, // 部分付款
                        '2': { color: '#1ACD7E' } // 已付款
                    }[status] || { color: '#999' }
                );
            };
        }
    },
    created() {
        this.getDict();
        this.getSettlementCompanyList();
        // 将默认设置调整为7天
        this.queryForm.startCreateDate = moment().subtract(7, 'days').format('YYYY-MM-DD');
        this.queryForm.endCreateDate = moment().format('YYYY-MM-DD');
        this.queryForm.queryTime = [this.queryForm.startCreateDate, this.queryForm.endCreateDate];
        this.handleChangesTheOrderType('queryForm');
    },
    methods: {
        /**
         * 关闭开票
         */
        closeInvoice() {
            this.invoiceVisible = false;
            this.invoiceForm = {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined
            };
        },
        /**
         * 关闭汇款金额与收款单金额不匹配 弹窗
         */
        closeMismatchDialog() {
            this.mismatchDialogVisible = false;
            this.mismatchFormData = {
                selectedData: [],
                receivableCost: 0,
                remitAmount: 0
            };
        },
        /**
         * 获取坏账记录
         */
        getBadDebtRegistrationList(paymentDocId) {
            this.badDebtRegistrationLoading = true;
            this.badDebtRegistrationText = '加载中...';
            customerPaymentDocument
                .getBadDebtRegistrationList({ paymentDocId })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.badDebtRegistrationList = res.data || [];
                    } else {
                        this.badDebtRegistrationList = [];
                    }
                })
                .catch(() => {
                    this.badDebtRegistrationList = [];
                })
                .finally(() => {
                    this.badDebtRegistrationLoading = false;
                });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.billTypeList = await this.getDictList('fourpl_bill_type');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
            this.settlementManagementOrderTypeList = await this.getDictList('cost_settlement_management_order_type');
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.orderStatusList = await this.getDictList('expense_order_status');
            this.statusList = await this.getDictList('receipt_status_status');
            this.paymentReceiptType = await this.getDictList('cost_payment_doc_type');
            this.productTypeList = await this.getDictList('fourpl_product_type');
            this.temperatureTypeList = await this.getDictList('fourpl_temperature_type');
            this.invoiceStatusList = await this.getDictList('cost_invoicing_status_receipt');
            this.receivingCompanyList = await this.getDictList('signing_company');
            this.discountTypeList = await this.getDictList('discount_type');
            this.badDebtTypeList = await this.getDictList('bad_debt_type');
            this.applyStatusList = await this.getDictList('apply_status');
            this.chargeWayList = await this.getDictList('fourpl_charge_way');
        },
        /**
         * 获取发票抬头列表
         */
        async getInvoiceHeadList(ownerId) {
            const res = (await invoiceInformationMaintenance.getInvoiceHeadList({ ownerId })) || [];
            if (res.code === 200) {
                this.invoiceList = res.data || [];
                if (this.invoiceList.length === 0) {
                    this.$message.warning('未查询到发票抬头信息,请先前往【发票信息维护】新增发票抬头');
                }
            } else {
                this.invoiceList = [];
            }
        },
        getList() {
            this.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { queryTime, date, sendAddress, receiverAddress, ...params } = this.queryForm;
            customerPaymentDocument
                .paymentList(params)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.orderList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 获取结算公司列表
         */
        async getSettlementCompanyList() {
            const res = await settlementCompany.getSettlementCompanySelectList();
            if (res.code === 200) {
                this.settlementCompanyList = res.data || [];
            } else {
                this.settlementCompanyList = [];
            }
        },
        /**
         * 上传坏账凭证成功后的处理函数
         * @param {Object} response - 服务器返回的数据对象，包含文件URL和文件名
         */
        handleBadDebtFileUploadSuccess(response) {
            if (!response || !response.data || !('fileUrl' in response.data) || !('fileName' in response.data)) {
                this.$message.error('上传失败，请稍后重试');
                return;
            }

            const newFileData = {
                url: response.data.fileUrl,
                name: response.data.fileName
            };

            this.badDebtRegistrationForm.fileUrl = [...this.badDebtRegistrationForm.fileUrl, newFileData];
        },
        /**
         * 打开坏账登记模态框并初始化数据
         * @param {Object} row - 行数据对象
         */
        handleBadDebtRegistration(row) {
            // 检查receivableCost和paidCost是否存在
            if ('receivableCost' in row && 'paidCost' in row && 'badDebtCost' in row && 'id' in row) {
                const receivableCost = Number(row.receivableCost);
                const paidCost = Number(row.paidCost);

                // 计算坏账金额，增加错误处理和边界条件检查
                const availableAmount = receivableCost - paidCost;

                this.badDebtRegistrationVisible = true;

                // 深拷贝基础数据对象以避免修改原始数据
                const baseFormData = { ...this.badDebtRegistrationForm };

                // 合并新行数据，同时确保 paymentDocId 属性被正确更新
                this.badDebtRegistrationForm = { ...baseFormData, ...row, paymentDocId: row.id };

                if (typeof availableAmount !== 'number' || isNaN(availableAmount) || availableAmount < 0) {
                    this.badDebtRegistrationForm.amount = 0;
                } else {
                    this.badDebtRegistrationForm.totalUnpaidCost = Number(availableAmount.toFixed(2)); // 保留两位小数
                    // 计算坏账金额，减去坏账合计
                    this.badDebtRegistrationForm.amount = Math.max(0, Number((availableAmount - row.badDebtCost).toFixed(2)));
                }
            } else {
                this.$message.error('未获取到应收费用或已收费用或坏账金额');
            }
        },
        /**
         * 账单明细时间转化
         */
        handleBillDetailsQuery() {
            this.billDetailsForm.current = 1;
            this.statementList();
        },
        /**
         * 改变订单类型
         */
        handleChangesTheOrderType(formName) {
            if (formName === 'queryForm') {
                this.handleQuery();
                // eslint-disable-next-line no-unused-vars
                const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;
                customerPaymentDocument
                    .getCustomerList({ ...params })
                    .then((res) => {
                        if (res.code === 200 && res.data?.length > 0) {
                            this.customerList = res.data;
                        } else {
                            this.customerList = [];
                            this.queryForm.companyId = undefined;
                        }
                    })
                    .catch(() => {
                        this.customerList = [];
                        this.queryForm.companyId = undefined;
                    })
                    .finally(() => {
                        this.getList();
                        this.getStatistics(); // 添加获取统计数据的调用
                    });
            } else if (formName === 'billDetailsForm') {
                this.handleBillDetailsQuery();
            }
        },
        /**
         * 查看坏账登记图片
         * @param {Object} row - 行数据对象
         */
        handleClickBadDebtRegistration(row) {
            if ('fileUrl' in row && row.fileUrl.trim()) {
                // 将逗号分隔的字符串转换为数组
                const fileUrls = row.fileUrl.split(',').map((url) => url.trim());

                // 如果所有项都不是空字符串，则展示图片
                if (fileUrls.every((url) => url !== '')) {
                    this.dialogImageUrl = fileUrls;
                    this.dialogVisible = true;
                } else {
                    this.$message.error('未获取到有效的坏账资料链接');
                }
            } else {
                this.$message.error('未获取到坏账资料');
            }
        },
        // 打开账单明细
        handleClickBillingDetails(row) {
            this.billDetailsVisible = true;
            this.billDetailsLoading = true;
            this.billDetailsLoadingText = '加载中...';
            // 临时存储row
            this.billDetailsRow = row;

            // 根据收款单类型设置账单明细类型
            switch (row.paymentDocType) {
                case '6': // 仓储服务费
                    this.billDetailsType = 'storage';
                    break;
                case '7': // 仓储年费
                    this.billDetailsType = 'yearly';
                    break;
                case '8': // 仓储保费
                    this.billDetailsType = 'insurance';
                    break;
                default:
                    this.billDetailsType = 'normal';
                    break;
            }

            // 根据不同类型调用不同的接口获取数据
            if (this.billDetailsType === 'normal') {
                this.handleChangesTheOrderType('billDetailsForm');
                if ('id' in row) {
                    this.getBadDebtRegistrationList(row.id);
                }
            } else {
                this.getStorageBillDetails(row);
            }
        },

        /**
         * 获取仓储费用账单明细
         * @param {Object} row - 行数据对象
         */
        getStorageBillDetails(row) {
            this.billDetailsLoading = true;
            const paymentOrderNo = row.paymentOrderNo;

            let requestPromise;
            switch (this.billDetailsType) {
                case 'storage':
                    requestPromise = customerPaymentDocument.getStorageBillDetails(paymentOrderNo);
                    break;
                case 'yearly':
                    requestPromise = customerPaymentDocument.getStorageYearFee(paymentOrderNo);
                    break;
                case 'insurance':
                    requestPromise = customerPaymentDocument.getStorageInsuranceFee(paymentOrderNo);
                    break;
                default:
                    this.billDetailsLoading = false;
                    return;
            }

            requestPromise
                .then((res) => {
                    if (res.code === 200) {
                        switch (this.billDetailsType) {
                            case 'storage':
                                // 如果数据是数组则直接使用，否则将对象放入数组中
                                this.storageBillDetails = Array.isArray(res.data) ? res.data : [res.data || {}];
                                this.storageBillDetail = res.data || {};
                                break;
                            case 'yearly':
                                this.yearlyBillDetail = res.data || {};
                                break;
                            case 'insurance':
                                this.insuranceBillDetail = res.data || {};
                                break;
                        }
                    } else {
                        this.$message.error(res.message || '获取账单明细失败');
                        switch (this.billDetailsType) {
                            case 'storage':
                                this.storageBillDetails = [];
                                this.storageBillDetail = {};
                                break;
                            case 'yearly':
                                this.yearlyBillDetail = {};
                                break;
                            case 'insurance':
                                this.insuranceBillDetail = {};
                                break;
                        }
                    }
                })
                .catch(() => {
                    this.$message.error('获取账单明细失败');
                    switch (this.billDetailsType) {
                        case 'storage':
                            this.storageBillDetails = [];
                            this.storageBillDetail = {};
                            break;
                        case 'yearly':
                            this.yearlyBillDetail = {};
                            break;
                        case 'insurance':
                            this.insuranceBillDetail = {};
                            break;
                    }
                })
                .finally(() => {
                    this.billDetailsLoading = false;
                });
        },

        /**
         * 隐藏订单明细
         */
        hideBillDetails() {
            this.billDetailsVisible = false;
            this.billDetailsType = 'normal';
            this.$refs.billDetailsForm?.resetFields();
            this.billDetailsRow = {};
        },
        // 批量核销
        handleClickBulkWriteOff() {
            this.$confirm('确认核销选中的数据吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    // 遍历 this.selectData id 赋值为 ids
                    let ids = [];
                    this.selectData.forEach((item) => {
                        ids.push(item.id);
                    });
                    const loading = this.$loading({
                        lock: true,
                        text: '核销中...',
                        spinner: 'el-icon-loading'
                    });
                    // 批量核销
                    customerPaymentDocument
                        .carrierCheck(ids)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message({
                                    type: 'success',
                                    message: '核销成功!'
                                });
                                this.handleChangesTheOrderType('queryForm');
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: res.data
                                });
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            loading.close();
                        });
                })
                .catch(() => {});
        },
        /**
         * 确认坏账登记
         */
        handleClickConfirmBadDebtRegistration() {
            this.$refs.badDebtRegistrationForm.validate((valid) => {
                if (valid) {
                    const { paymentDocId, amount, type, reason, fileUrl } = this.badDebtRegistrationForm;
                    const params = {
                        paymentDocId,
                        amount,
                        type,
                        reason,
                        // fileUrl 数组使用,分隔
                        fileUrl: fileUrl.map((file) => file.url).join(',') || undefined
                    };
                    customerPaymentDocument.badDebtRegister(params).then((res) => {
                        if (res.code === 200) {
                            this.$message({
                                type: 'success',
                                message: '坏账登记成功'
                            });
                            this.hideBadDebtRegistration();
                            this.handleChangesTheOrderType('queryForm');
                        }
                    });
                }
            });
        },
        // 查看费用详情
        handleClickFeeDetails(data) {
            this.orderCostVisible = true;
            // this.detailData 初始为空
            this.detailData = {};
            this.detailData = { ...data };
            const { addedServiceList, costList } = data;
            let addedServicesFilter = [];
            if (addedServiceList) {
                // addedServiceList addedServicesContractPrice 变为 costContractPrice, addedServicesCost 变为 costData,addedServicesName 变为 costType 赋值给 addedServiceList
                // 使用 for of 循环遍历 addedServiceList
                for (const item of addedServiceList) {
                    item.costContractPrice = item.addedServicesContractPrice;
                    item.costData = item.addedServicesCost;
                    item.costType = item.addedServicesName;
                }
                // 去除 addedServiceList 中 costData 和 costContractPrice 都为 '0' 的数据 其中一个为 '0' 的数据不去除
                // 赋值给 addedServicesFilter
                addedServicesFilter = addedServiceList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
            }
            // 筛选 costList 中 costData 和 costContractPrice 都不等于0的数据
            const costListFilter = costList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
            costListFilter.sort((a, b) => {
                return a.costType - b.costType;
            });

            // addedServiceList 为 null 解构赋值给 this.feeBreakdownData []
            this.feeBreakdownData = [...(addedServicesFilter || []), ...(costListFilter || [])];
        },
        /**
         * 点击订单号查看订单详情
         */
        handleClickOrderDetail(row) {
            // 检查是否有订单号
            if (!row.orderNo) {
                this.$message.warning('订单号不存在，无法查看订单详情');
                return;
            }

            // 如果账单明细数据已经包含完整的订单信息，直接使用
            if (row.orderId || row.id) {
                this.showOrderDetail(row);
                return;
            }

            // 否则通过订单号查询完整的订单信息
            this.getOrderInfoByOrderNo(row.orderNo, row);
        },
        /**
         * 通过订单号查询完整的订单信息
         */
        getOrderInfoByOrderNo(orderNo, billDetailRow) {
            // 通过订单号查询订单列表，获取完整的订单信息
            orderManagement
                .listOrderDrug({
                    orderNo: orderNo,
                    current: 1,
                    size: 1
                })
                .then((response) => {
                    if (response.code === 200 && response.data && response.data.records && response.data.records.length > 0) {
                        const orderInfo = response.data.records[0];
                        // 合并账单明细数据和订单数据
                        const mergedData = {
                            ...orderInfo,
                            ...billDetailRow, // 保留账单明细的特有字段
                            orderId: orderInfo.id,
                            id: orderInfo.id
                        };
                        this.showOrderDetail(mergedData);
                    } else {
                        this.$message.warning('未找到对应的订单信息');
                    }
                })
                .catch((error) => {
                    console.error('查询订单信息失败:', error);
                    this.$message.error('查询订单信息失败，请稍后重试');
                });
        },
        /**
         * 显示订单详情
         */
        showOrderDetail(orderData) {
            // 设置运输数据
            this.transportationData.isEdit = false;
            this.transportationData.orderNo = orderData.orderNo;
            this.transportationData.transOrderNo = orderData.transOrderNo || '';
            this.transportationData.isDownloadAllImages = true;

            // 设置订单信息，参照OrderList.vue的实现
            this.orderDetailInfo = {
                ...orderData
            };

            // 显示订单详情滑块
            this.orderDetailVisible = true;
        },
        /**
         * 撤销
         */
        handleClickQuash() {
            // 遍历 this.selectData id 赋值为 ids
            let ids = [];
            let storageTypeReceipts = [];

            this.selectData.forEach((item) => {
                // 仓储类型的收款单（paymentDocType为6、7、8）不允许撤销
                if (['6', '7', '8'].includes(item.paymentDocType)) {
                    this.$refs.customerPaymentDocument.$refs.ColumnTable.toggleRowSelection(item, false);
                    storageTypeReceipts.push(item.paymentOrderNo);
                }
                // invoicingStatus 2 已发起 1 未发起
                // status 2 已付款 1 部分付款 0 未付款
                // 已开票+未付款，未发起+已付款，已开票+已付款 的取消选中
                else if ((item.invoicingStatus == '2' && item.status == '0') || (item.invoicingStatus == '0' && item.status == '2') || (item.invoicingStatus == '2' && item.status == '2')) {
                    this.$refs.customerPaymentDocument.$refs.ColumnTable.toggleRowSelection(item, false);
                } else {
                    ids.push(item.id);
                }
            });

            if (ids.length === 0) {
                if (storageTypeReceipts.length > 0) {
                    this.$message({
                        message: '仓储费类型的收款单不允许撤销',
                        type: 'warning'
                    });
                } else {
                    this.$message({
                        message: '请选择【未发起+未付款】或【未发起+部分付款】的收款单',
                        type: 'warning'
                    });
                }
                return;
            }

            this.$confirm('确认撤销选中的数据吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const loading = this.$loading({
                        lock: true,
                        text: '撤销中...',
                        spinner: 'el-icon-loading'
                    });
                    customerPaymentDocument
                        .revokePaymentDoc(ids)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message({
                                    type: 'success',
                                    message: '撤销成功!'
                                });
                                this.handleChangesTheOrderType('queryForm');
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            loading.close();
                        });
                })
                .catch(() => {});
        },
        /*
         * 打开提交弹窗
         */
        async handleClickSubmit() {
            // paymentDocType 收款单类型(1-预付款 2-其他 3-月结 4-到付 5-现付) 取消选中 预付款(1) 和 其他(2) 的行
            // status 付款状态(2-已付款 1-部分付款 0-未付款) 取消选中已付款(2) 的行
            // applyStatus 是否发起付款申请(1-未申请 2-已申请) 取消选中已申请(2) 的行
            this.selectData.forEach((item) => {
                if (item.paymentDocType === '1' || item.paymentDocType === '2' || item.status === '2') {
                    // 取消选中不符合条件的行
                    this.$refs.customerPaymentDocument.$refs.ColumnTable.toggleRowSelection(item, false);
                }
            });

            // 检查是否有选中数据
            if (this.selectData.length === 0) {
                this.$message({
                    message: '请选择【收款单类型：月结、到付、现付】，【付款状态：未付款、部分付款】的收款单',
                    type: 'warning'
                });
                return;
            }

            // 检查货主公司是否一致
            const firstCompanyId = this.selectData[0].companyId;
            const hasInconsistentCompany = this.selectData.some((item) => item.companyId !== firstCompanyId);

            // 获取第一条数据的收款单类型
            const firstPaymentType = this.selectData[0].paymentDocType;

            // 判断是否是仓储类型的收款单(6-仓储服务费、7-仓储年费、8-仓储保费)
            const isStorageType = (type) => ['6', '7', '8'].includes(type);

            // 检查是否所有选中的收款单都是仓储类型
            const allStorageTypes = this.selectData.every((item) => isStorageType(item.paymentDocType));

            // 如果不是全部都是仓储类型，则检查非仓储类型是否一致
            if (!allStorageTypes) {
                // 检查是否有仓储类型和非仓储类型混合的情况
                const hasStorageType = this.selectData.some((item) => isStorageType(item.paymentDocType));
                const hasNonStorageType = this.selectData.some((item) => !isStorageType(item.paymentDocType));

                if (hasStorageType && hasNonStorageType) {
                    this.$message({
                        message: '仓储类型收款单不能与其他类型收款单一起提交',
                        type: 'warning'
                    });
                    return;
                }

                // 检查非仓储类型是否一致
                const hasInconsistentType = this.selectData.some((item) => item.paymentDocType !== firstPaymentType);

                if (hasInconsistentType) {
                    this.$message({
                        message: '请选择相同的收款单类型',
                        type: 'warning'
                    });
                    return;
                }
            }

            // 月结或现付时检查货主是否一致
            if ((firstPaymentType === '3' || firstPaymentType === '5') && hasInconsistentCompany) {
                this.$message({
                    message: `${firstPaymentType === '3' ? '月结' : '现付'}收款单必须选择相同货主公司`,
                    type: 'warning'
                });
                return;
            }

            // 通过 支付申请-前置校验
            const applyPreCheck = await customerPaymentDocument.paymentApplyPreCheck({
                payDocId: this.selectData.map((item) => item.id).join(',')
            });
            if (applyPreCheck.code === 200) {
                // If there are no items in noAllowList, directly open payment submission dialog
                if (!applyPreCheck.data.noAllowList?.length) {
                    // 计算选中数据的未付金额总和
                    this.applyPrepaymentForm.remitAmount = this.selectData.reduce((total, item) => {
                        const unpaidCost = Number(item.unpaidCost) || 0;
                        return Number((total + unpaidCost).toFixed(2));
                    }, 0);
                    // 付款公司id
                    // 设置付款公司ID,从选中的第一条数据中获取
                    this.applyPrepaymentForm.companyId = this.selectData[0]?.companyId || '';
                    // 付款公司
                    this.applyPrepaymentForm.companyName = this.selectData[0].paymentDocType === '4' ? undefined : (this.selectData[0].companyName || '').trim();
                    // 收款单类型 付款类型(1-预付款 2-其他 3-月结 4-到付 5-现付)
                    this.applyPrepaymentForm.paymentDocType = this.selectData[0]?.paymentDocType ?? '';
                    // 默认收款公司
                    const Organization = this.$TOOL.data.get('Organization');

                    // 获取付款类型
                    const paymentDocType = this.selectData[0].paymentDocType;
                    // 如果是到付(4)，则设置为空
                    if (paymentDocType === '4') {
                        this.applyPrepaymentForm.receiveCompany = undefined;
                    } else {
                        // 月结(3)和现付(5)保持默认收款公司
                        const res = await customerPrepaymentBalance.getCompanyInfo(this.selectData[0].companyId, Organization[0].id);
                        if (res.code === 200 && res.data?.signCompany) {
                            this.applyPrepaymentForm.receiveCompany = res.data.signCompany;
                        } else {
                            this.$message.warning('未获取到签约公司信息');
                            this.applyPrepaymentForm.receiveCompany = undefined;
                        }
                    }
                    // 汇款时间
                    this.applyPrepaymentForm.remitTime = moment().format('YYYY-MM-DD HH:mm:ss');
                    this.applyPrepaymentVisible = true;
                } else {
                    // If there are items in noAllowList, show the receipt selection dialog
                    this.availableReceipts = this.selectData
                        .filter((item) => applyPreCheck.data.allowList.includes(item.id))
                        .map((item) => ({
                            id: item.id,
                            number: item.paymentOrderNo
                        }));

                    this.unavailableReceipts = this.selectData
                        .filter((item) => applyPreCheck.data.noAllowList.includes(item.id))
                        .map((item) => ({
                            id: item.id,
                            number: item.paymentOrderNo
                        }));

                    this.selectReceiptVisible = true;
                }
            } else {
                this.$message({
                    message: applyPreCheck.data || '操作失败',
                    type: 'warning'
                });
            }
        },
        /**
         * 全部导出
         */
        handleExportAll() {
            // 提示框 询问是否全部导出
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    // eslint-disable-next-line no-unused-vars
                    const { queryTime, ...params } = this.queryForm;
                    customerPaymentDocument
                        .paymentExport({ filename: '客户收款单.xls', ...params }, '', '', 'blob')
                        .then((res) => {
                            downloadNoData(res, 'application/vnd.ms-excel', '客户收款单.xlsx');
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        // 预览上传图片
        handlePicturePreview(file) {
            this.dialogImageUrl = [];
            this.dialogImageUrl.push(file.url);
            this.dialogVisible = true;
        },
        /**
         * 列表的时间处理
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { queryTime } = this.queryForm;
            if (queryTime && queryTime.length) {
                this.queryForm.startCreateDate = queryTime[0] + ' 00:00:00';
                this.queryForm.endCreateDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startCreateDate = null;
                this.queryForm.endCreateDate = null;
            }
        },
        /**
         * 处理收款单提交
         * selectedIds 选中数据的id
         */
        async handleReceiptSubmit(selectedIds) {
            // 计算选中数据的未付金额总和
            this.applyPrepaymentForm.remitAmount = this.selectData.reduce((total, item) => {
                // 只计算可以提交的收款单的未付金额
                if (selectedIds.includes(item.id)) {
                    const unpaidCost = Number(item.unpaidCost) || 0;
                    return Number((total + unpaidCost).toFixed(2));
                }
                return total;
            }, 0);

            // 设置付款公司ID和名称
            const selectedData = this.selectData.find((item) => selectedIds.includes(item.id));
            if (selectedData) {
                this.applyPrepaymentForm.companyId = selectedData.companyId;
                this.applyPrepaymentForm.companyName = selectedData.paymentDocType === '4' ? undefined : (selectedData.companyName || '').trim();
                this.applyPrepaymentForm.paymentDocType = selectedData.paymentDocType;

                // 设置默认收款公司
                const Organization = this.$TOOL.data.get('Organization');
                if (selectedData.paymentDocType === '4') {
                    this.applyPrepaymentForm.receiveCompany = undefined;
                } else {
                    try {
                        const res = await customerPrepaymentBalance.getCompanyInfo(selectedData.companyId, Organization[0].id);
                        if (res.code === 200 && res.data?.signCompany) {
                            this.applyPrepaymentForm.receiveCompany = res.data.signCompany;
                        } else {
                            this.$message.warning('未获取到签约公司信息');
                            this.applyPrepaymentForm.receiveCompany = undefined;
                        }
                    } catch (error) {
                        this.$message.warning('获取签约公司信息失败');
                        this.applyPrepaymentForm.receiveCompany = undefined;
                    }
                }
            }

            // 设置汇款时间
            this.applyPrepaymentForm.remitTime = moment().format('YYYY-MM-DD HH:mm:ss');

            this.selectReceiptVisible = false;
            this.applyPrepaymentVisible = true;
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            // 遍历 selection中的 id status 赋值给 this.selectData
            this.selectData = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /**
         * 隐藏坏账登记
         */
        hideBadDebtRegistration() {
            this.badDebtRegistrationVisible = false;
            this.$refs.badDebtRegistrationForm.resetFields();
        },
        imgClose() {
            this.dialogVisible = false;
        },
        /**
         * 关闭提交付款信息
         */
        onCloseApplyPrepayment() {
            this.applyPrepaymentVisible = false;
            this.applyPrepaymentForm = {
                companyName: '',
                receiveCompany: '',
                remitAmount: 0,
                remitTime: '',
                remitFile: []
            };
        },
        /**
         * 打开开票
         */
        async onOpenInvoice() {
            // 初始化一个数组来收集需要提示的收款单号
            const paymentDocTypeIsOne = [];
            this.selectData.forEach((item) => {
                // paymentDocType 付款类型(1-预付款 2-其他 3-月结 4-到付 5-现付)
                // invoicingStatus 是否发起开票申请(2-已发起 1-未发起) 取消已开票和部分开票的行
                if (item.paymentDocType === '2' || item.invoicingStatus === '2') {
                    this.$refs.customerPaymentDocument.$refs.ColumnTable.toggleRowSelection(item, false);
                    // 收集预付款类型的收款单号
                    paymentDocTypeIsOne.push(item.paymentOrderNo);
                }
            });

            // 如果没有选中任何数据，则显示警告信息并终止后续操作
            if (this.selectData.length > 0) {
                // 校验是否为同一货主
                const ownerId = this.selectData[0].ownerId;
                const isSameOwner = this.selectData.every((item) => item.ownerId === ownerId);
                if (!isSameOwner) {
                    this.$message.warning('请选择同一货主的付款单');
                    this.$refs.customerPaymentDocument.$refs.ColumnTable.clearSelection();
                    return;
                }

                // 校验付款类型是否符合规则
                const paymentTypes = [...new Set(this.selectData.map((item) => item.paymentDocType))];

                // 检查是否包含预付款(1)或到付(4)
                const hasPrePay = paymentTypes.includes('1');
                const hasCollectPay = paymentTypes.includes('4');

                // 检查是否包含月结(3)或现付(5)
                const hasMonthlyPay = paymentTypes.includes('3');
                const hasCashPay = paymentTypes.includes('5');

                // 检查是否包含仓储类型(6-仓储服务费, 7-仓储年费, 8-仓储保费)
                const hasStorageType = paymentTypes.some((item) => ['6', '7', '8'].includes(item));

                // 验证付款类型组合
                if (hasPrePay || hasCollectPay) {
                    // 如果包含预付款或到付，不能与其他类型混合
                    if (paymentTypes.length > 1) {
                        this.$message.warning('【预付款】或【到付】类型的付款单不能与其他类型混合开票');
                        return;
                    }
                } else if (hasStorageType) {
                    // 对于仓储类型，验证是否只选择了一种类型
                    if (paymentTypes.length > 1) {
                        this.$message.warning('仓储服务费、仓储年费、仓储保费的收款单类型不能与其他类型混合开票');
                        return;
                    }
                } else if (!(hasMonthlyPay || hasCashPay)) {
                    // 如果既不是预付款/到付，也不是月结/现付的组合，也不是仓储类型
                    this.$message.warning('选择的付款类型不支持开票');
                    return;
                }

                // 校验 this.selectData 中的 companyId 是否一致
                const companyId = this.selectData[0].companyId;
                const isSameCompany = this.selectData.every((item) => item.companyId === companyId);
                if (!isSameCompany) {
                    this.$message.warning('请选择同一承运商的付款单');
                    return;
                }

                // 计算选中数据的未付金额总和
                this.invoiceForm.invoiceAmount = this.selectData.reduce((total, item) => {
                    // 尝试将字符串转换为数字，并处理NaN情况
                    const receivableCost = Number(item.receivableCost) || 0;
                    // 累加有效数字并保留2位小数
                    return Number((total + receivableCost).toFixed(2));
                }, 0);

                this.invoiceVisible = true;
                await this.getInvoiceHeadList(this.selectData[0]?.companyId);

                // 初始化发票表单数据
                this.invoiceForm = {
                    invoiceAmount: this.invoiceForm.invoiceAmount,
                    carrierId: this.selectData[0].carrierId,
                    companyId: this.selectData[0]?.companyId || '',
                    companyName: this.selectData[0].paymentDocType === '4' ? undefined : (this.selectData[0].companyName || '').trim(),
                    paymentDocType: this.selectData[0]?.paymentDocType || '',
                    payMethod: this.selectData[0]?.paymentDocType === '4' ? '4' : '3', // 4-到付, 3-月结&现付
                    // 根据付款类型组合设置开票业务类型
                    openInvoiceType: (() => {
                        // 如果只有一种付款类型
                        if (paymentTypes.length === 1) {
                            switch (paymentTypes[0]) {
                                case '1':
                                    return '1'; // 预付款开票
                                case '3':
                                    return '3'; // 月结开票
                                case '4':
                                    return '4'; // 到付开票
                                case '5':
                                    return '5'; // 现付开票
                                case '6':
                                    return '6'; // 仓储服务费开票
                                case '7':
                                    return '7'; // 仓储年费开票
                                case '8':
                                    return '8'; // 仓储保费开票
                            }
                        }
                        // 如果包含月结和现付的组合
                        if (paymentTypes.includes('3') || paymentTypes.includes('5')) {
                            return '2'; // 月结&现付开票
                        }
                        return '2'; // 默认月结&现付开票
                    })(),
                    // 如果是到付类型，初始化空的发票信息对象
                    invoiceData: this.isCollectPayment
                        ? {
                              invoiceHead: '',
                              taxNo: '',
                              address: '',
                              phone: '',
                              openBank: '',
                              bankAccount: ''
                          }
                        : null,
                    remark: ''
                };

                // 默认收款公司
                const Organization = this.$TOOL.data.get('Organization');
                // 获取付款类型
                const res = await customerPrepaymentBalance.getCompanyInfo(this.selectData[0].companyId, Organization[0].id);
                if (res.code === 200 && res.data?.signCompany) {
                    this.invoiceForm.signCompany = res.data.signCompany;
                    this.invoiceForm.invoiceList = res.data.invoiceList;
                } else {
                    this.$message.warning('未获取到签约公司信息');
                    this.invoiceForm.signCompany = undefined;
                    this.invoiceForm.invoiceList = [];
                }
            } else {
                if (paymentDocTypeIsOne.length > 0) {
                    this.$message.warning('请选择【是否发起开票申请：未发起】数据');
                }
            }
        },
        /**
         * 提交预付款充值申请
         */
        onSubmitApplyPrepayment(formData) {
            // 判断是否经过选择收款单弹窗
            const hasSelectedReceipts = this.availableReceipts && this.availableReceipts.length > 0;

            // 根据是否经过选择收款单弹窗来决定使用哪个数据源
            const selectedData = hasSelectedReceipts ? this.selectData.filter((item) => this.availableReceipts.map((r) => r.id).includes(item.id)) : this.selectData;

            // 收款单应收合计金额
            this.mismatchFormData.receivableCost = selectedData.reduce((total, item) => {
                const receivableCost = Number(item.unpaidCost) || 0;
                return Number((total + receivableCost).toFixed(2));
            }, 0);

            // 收款单未收合计金额
            this.mismatchFormData.unpaidCost = selectedData.reduce((total, item) => {
                const unpaidCost = Number(item.unpaidCost) || 0;
                return Number((total + unpaidCost).toFixed(2));
            }, 0);

            // 汇款合计金额 - 确保是数字类型并保留两位小数
            this.mismatchFormData.remitAmount = Number(formData.remitAmount);
            // 存储选中的数据
            this.mismatchFormData.selectedData = selectedData;
            // 存储 formData
            this.mismatchFormData.formData = formData;
            this.mismatchDialogVisible = true;
        },
        resetBillDetailsForm() {
            this.$refs.billDetailsForm.resetFields();
            this.handleChangesTheOrderType('billDetailsForm');
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleChangesTheOrderType('queryForm');
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 查询账单明细
         * clientType 1=承运端 2-货主端
         */
        statementList() {
            this.billDetailsLoading = true;
            customerPaymentDocument
                .statementDetail({ ...this.billDetailsForm, paymentId: this.billDetailsRow.id, clientType: '1' })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.billDetailsList = res.data || [];
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.billDetailsLoading = false;
                });
        },
        /**
         * 提交开票
         */
        submitInvoice(formData) {
            const params = {
                applyWay: '2', // 1-货主 2-承运商 (预付款默认货主发起)， 不传默认货主发起
                businessType: '2', // 1-预存款开票 2-付款单开票
                carrierId: formData.carrierId,
                companyId: formData.isCollectPayment ? undefined : formData.companyId,
                companyName: formData.companyName,
                invoiceId: formData.isCollectPayment ? undefined : formData.invoiceData.id,
                invoiceHead: formData.invoiceData.invoiceHead,
                taxNo: formData.invoiceData.taxNo,
                address: formData.invoiceData.address,
                phone: formData.invoiceData.phone,
                openBank: formData.invoiceData.openBank,
                bankAccount: formData.invoiceData.bankAccount,
                invoiceAmount: formData.invoiceAmount,
                payIdList: this.selectData.map((item) => item.id),
                remark: formData.remark,
                payMethod: formData.payMethod,
                openInvoiceType: formData.openInvoiceType,
                invoiceType: formData.invoiceType,
                projectName: formData.projectFeeType,
                signCompany: formData.signCompany,
                taxPoint: formData.taxPoint
            };

            paymentDoc.applyInvoice(params).then((res) => {
                if (res.code === 200) {
                    this.$message.success('开票申请成功');
                    this.closeInvoice();
                    this.getList();
                }
            });
        },
        /**
         * 确认汇款金额与收款单金额不匹配
         */
        submitMismatchDialog(mismatchFormData) {
            const loading = this.$loading({
                lock: true,
                text: '提交中...',
                spinner: 'el-icon-loading'
            });

            const payDocList = mismatchFormData.selectedData.map((item) => ({
                payId: item.id,
                paidCost: item.paidCost
            }));
            const payMethod = this.selectData[0].paymentDocType; // 支付方式

            // 转换附件信息为JSON字符串
            const params = {
                ...mismatchFormData.formData,
                applyWay: '2', // 申请方式 1-货主发起 2-承运商发起
                businessType: '3', // 业务类型 1-预存款充值 2-付款单支付 3-收款单收款
                payDocList,
                remitFile: JSON.stringify(mismatchFormData.formData.remitFile),
                payMethod
            };

            paymentDoc
                .applyPayment(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.$message.success('提交成功');
                        this.closeMismatchDialog();
                        this.onCloseApplyPrepayment();
                        this.getList();
                    }
                })
                .finally(() => {
                    loading.close();
                });
        },
        /**
         * 获取统计数据
         */
        getStatistics() {
            // eslint-disable-next-line no-unused-vars
            const { queryTime, ...params } = this.queryForm;
            customerPaymentDocument.getCostStatistics(params).then((res) => {
                if (res.code === 200 && res.data) {
                    this.statistics = res.data;
                } else {
                    this.statistics = {};
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-upload-list--picture-card .el-upload-list__item-actions:hover span) {
    display: contents !important;
}
:deep(.el-input.is-disabled .el-input__inner) {
    color: #666666;
    background-color: #f5f7fa;
}

:deep(thead th) {
    border-right: none !important;
}

:deep(.el-drawer__header) {
    margin-bottom: 20px;
}

:deep(label.el-radio) {
    margin-right: 8px;
}

:deep(.box-footer) {
    padding-left: 8px;
    display: flex;
    justify-content: space-between;

    .el-form-item__content {
        margin-left: 0 !important;
    }
}

:deep(.el-button-group > .el-button:not(:last-child)) {
    margin-right: 8px;
}

:deep(.el-tabs__header.is-top) {
    margin-bottom: 0;
}

:deep(.el-tabs__nav-scroll) {
    padding-left: 32px;
}

:deep(.card-pb-10 .el-card__body) {
    padding-bottom: 10px;
}

:deep(.el-input.is-disabled .el-input__inner) {
    color: #606266;
    -webkit-text-fill-color: #606266;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.box__PromptText {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #999999;

    > span {
        font-size: 14px;
    }
}

.expenseAdjustment__top {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 30px;

    .top__title {
        color: #666666;
    }
}

.dialog__billDetails {
    :deep(.el-dialog__header) {
        padding-bottom: 20px;
    }

    :deep(.el-result) {
        padding: 0;
    }

    .dialog__footer {
        display: flex;
        justify-content: center;
    }
}

.generatePaymentDocument__card {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card__Describe {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-row-gap: 20px;
    }
}

.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}

.number__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}

.number__unit__input__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
    }
}
</style>
