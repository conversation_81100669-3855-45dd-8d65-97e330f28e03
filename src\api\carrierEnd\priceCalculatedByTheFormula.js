import request from '@/utils/request'
export default {
	// 翻译公式
	translatePriceBookFormula: function (params) {
		return request.post('/cost/priceBook/formula/translateFormulas', params);
	},
	// 新增价格计算公式
	addPriceBookFormula: function (params) {
		return request.post('/cost/priceBook/formula/save', params);
	},
	// 获取价格计算公式列表
	getPriceBookFormulaList: function (params) {
		return request.get('/cost/priceBook/formula/list', params);
	},
	// 发布价格计算公式
	releaseFormulaPriceBook: function (params) {
		return request.get('/cost/priceBook/formula/published', params);
	},
	// 注销价格计算公式
	logoutFormulaPriceBook: function (params) {
		return request.get('/cost/priceBook/formula/loggedout', params);
	},
	// 修改公式状态
	updateFormulaStatus: function (params) {
		return request.get('/cost/priceBook/formula/updateStatus', params);
	},
	// 获取价格计算公式详情
	getPriceBookFormulaDetail: function (params) {
		return request.get('/cost/priceBook/formula/queryById', params);
	}
}
