<template>
	<div class="demo-collapse">
		<el-collapse v-model="activeNames" @change="handleChange">
			<el-collapse-item name="1">
				<template #title>
					<h3 class="col_title">表头信息</h3>
				</template>
        <el-form ref="creatform1" :disabled="data.title == '查看详情'" :model="formInline1" :rules="creatRules1"
                 class="demo-form-inline form_130" label-position="right" style="overflow-x:auto">
					<div class="formBox">
						<el-form-item label="客户" prop="n1">
              <el-select v-model="formInline1.n1" placeholder="请选择客户" style="width: 100%;min-width:100px">
								<template #empty>
									<p style="
												text-align: center;
												color: #635f5e;
												margin: 15px 0;
											">
										无数据
									</p>
									<p style="text-align: center">
										<el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
											data.clientType.value =
												'';
											clientList();
										}
											">
											返回
										</el-button>
									</p>
								</template>
                <el-input v-model="data.clientType.value" placeholder="请输入客户名称Enter键搜索"
                          @keydown.enter="clientList"/>
                <el-option v-for="(item, index) in data.clientType
									.type" :key="index" :label="item.enterpriseName" :value="item.id"/>

							</el-select>
						</el-form-item>
						<el-form-item label="客户代表" prop="n2">
              <el-select v-model="formInline1.n2" :disabled="formInline1.n1 == ''"
                         :placeholder="formInline1.n1 == '' ? '请先选择客户' : '请选择客户代表'" class="m-2"
                         style="width: 100%;min-width:100px;height: auto;">
                <el-option v-for="(item, index) in data.powerType" :key="index" :label="item.delegateName"
                           :value="item.id"/>
							</el-select>
						</el-form-item>
						<el-form-item label="结算方式">
              <el-select v-model="formInline1.n3" class="m-2" placeholder="请选择结算方式"
                         style="width: 100%;min-width:100px">
                <el-option v-for="(
											item, index
										) in data.clearingFormType" :key="index" :label="item.name" :value="item.value"/>
							</el-select>
						</el-form-item>
						<el-form-item label="收款期限">
              <el-date-picker v-model="formInline1.n4"
                              :disabled-date="(time) => time.getTime() < Date.now() - 8.64e7" format="YYYY-MM-DD"
                              placeholder="请选择收款期限" style="width: 100%;min-width:100px" type="date"
                              value-format="YYYY-MM-DD"/>
						</el-form-item>
						<!--						<el-form-item label="发票类型" prop="n5">-->
						<!--							<el-select-->
						<!--								v-model="formInline1.n5"-->
						<!--								class="m-2"-->
						<!--								placeholder="请选择发票类型"-->
						<!--								style="width: 100%;min-width:100px"-->
						<!--							>-->
						<!--								<el-option-->
						<!--									v-for="(item, index) in data.invoice"-->
						<!--									:key="index"-->
						<!--									:label="item.name"-->
						<!--									:value="item.value"-->
						<!--								/>-->
						<!--							</el-select>-->
						<!--						</el-form-item>-->
						<!--						<el-form-item label="业务类型" prop="n6">-->
						<!--							<el-select-->
						<!--								v-model="formInline1.n6"-->
						<!--								class="m-2"-->
						<!--								placeholder="请选择业务类型"-->
						<!--								style="width: 100%;min-width:100px"-->
						<!--							>-->
						<!--								<el-option-->
						<!--									v-for="(item, index) in data.business"-->
						<!--									:key="index"-->
						<!--									:label="item.name"-->
						<!--									:value="item.value"-->
						<!--								/>-->
						<!--							</el-select>-->
						<!--						</el-form-item>-->
						<el-form-item label="物流方式" prop="n7">
              <el-select v-model="formInline1.n7" class="m-2" placeholder="请选择物流方式"
                         style="width: 100%;min-width:100px">
                <el-option v-for="(item, index) in data.logistics" :key="index" :label="item.name"
                           :value="item.value"/>
							</el-select>
						</el-form-item>
            <el-form-item :prop="formInline1.n7 == '1' || formInline1.n7 == '2' ? 'n8' : null" label="三方物流">
              <el-select v-model="formInline1.n8"
                         :disabled="formInline1.n7 == '0' || formInline1.n7 == '3' || formInline1.n7 === '' ? true : false"
                         :placeholder="threeFn(formInline1.n7)" class="m-2" style="width: 100%;min-width:100px">
                <el-option v-for="(
											item, index
										) in data.TripartiteLogistics" :key="index" :label="item.name" :value="item.value"/>
							</el-select>
						</el-form-item>
						<el-form-item label="收货地址" prop="n9">
              <el-cascader v-model="formInline1.n9" :options="regionDatas" placeholder="请选择收货地址"
                           size="default" style="width: 100%;min-width:100px">
							</el-cascader>
						</el-form-item>
						<el-form-item label="详细地址" prop="n10">
              <el-input v-model="formInline1.n10" placeholder="请输入详细地址" style="width: 100%;min-width:100px"/>
						</el-form-item>
						<el-form-item label="收货人" prop="n11">
              <el-input v-model="formInline1.n11" placeholder="请输入收货人" style="width: 100%;min-width:100px"/>
						</el-form-item>
						<el-form-item label="收货人电话" prop="n12">
              <el-input v-model="formInline1.n12" placeholder="请输入收货人电话"
                        style="width: 100%;min-width:100px"/>
						</el-form-item>
						<el-form-item label="库号" prop="n13">
              <el-select v-model="formInline1.n13" placeholder="请选择库号" style="width: 100%;min-width:100px">
								<template #empty>
                  <p style="
												text-align: center;
												color: #635f5e;
												margin: 15px 0;
											">
										无数据
									</p>
									<p style="text-align: center">
                    <el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
												data.serial.value = '';
												serialList();
											}
											">
											返回
										</el-button>
									</p>
								</template>
                <el-input v-model="data.serial.value" placeholder="请输入库号Enter键搜索"
                          @keydown.enter="serialList"/>
                <el-option v-for="(item, index) in data.serial
									.type" :key="index" :label="item.warehouseNumber" :value="item.id"/>

							</el-select>
						</el-form-item>
						<el-form-item label="经手人" prop="n14">
              <el-select v-model="formInline1.n14" :disabled="formInline1.n13 == ''"
                         :placeholder="formInline1.n13 == '' ? '请先选择库号' : '请选择经手人'"
                         style="width: 100%;min-width:100px;height: auto;">
                <el-option v-for="(item, index) in data.handle" :key="index" :label="item.handledBy.name"
                           :value="item.handledBy.id"/>
							</el-select>
						</el-form-item>
            <el-form-item label="自营扣率">
              <el-select v-model="formInline1.n15" class="m-2" placeholder="请选择自营扣率"
                         style="width: 100%;min-width:100px">
                <el-option v-for="(item, index) in data.discount" :key="index" :label="item.name"
                           :value="item.value"/>
							</el-select>
						</el-form-item>
						<el-form-item label="制单人" prop="n16">
              <el-input v-model="formInline1.n16.name" disabled placeholder="请输入制单人"
                        style="width: 100%;min-width:100px"/>
						</el-form-item>


						<el-form-item label="折扣金额">
              <el-input-number v-model="formInline1.n19" :min="0" :precision="2" :step="0.1"
                               :value-on-clear="0" class="mx-4" placeholder="请输入折扣金额"
                               style="width: 100%;min-width:100px;height:100%" @change="() => {
										allPrice();
									}
									"/>
						</el-form-item>
            <br/>
						<el-form-item label=" ">
              <el-checkbox v-model="formInline1.n22" label="是否生成合同"/>
						</el-form-item>
            <el-form-item :prop="formInline1.n22 ? 'n21' : null" label="合同模板">
              <el-select v-model="formInline1.n21" :disabled="!formInline1.n22 ? true : false"
                         :placeholder="formInline1.n22 ? '请选择合同模板' : ' '" style="width: 100%;min-width:100px">
                <el-option v-for="(item, index) in data.contract" :key="index" :label="item.name"
                           :value="item.value"/>
							</el-select>
						</el-form-item>

						<!--						<el-form-item label=" ">-->
						<!--							<el-checkbox-->
						<!--								v-model="formInline1.n20"-->
						<!--								label="是否开具发票"-->
						<!--							/>-->
						<!--						</el-form-item>-->
					</div>

					<el-form-item label="备注">
            <el-input v-model="formInline1.n17" :rows="3" maxlength="1000" placeholder="请输入备注" show-word-limit
                      style="width: 90%;min-width:100px" type="textarea"/>
          </el-form-item>
				</el-form>
			</el-collapse-item>
			<el-collapse-item name="2">
				<template #title>
					<h3 class="col_title">细单信息</h3>
				</template>
				<div style="margin-bottom: 18px">
          <el-button v-if="data.title != '查看详情'" style="margin-left: 12px" type="primary" @click="
						GoodsRule()
						">添加
          </el-button>
          <el-button v-if="data.title != '查看详情'" :disabled="addGoods.delList.length == 0" type="danger"
                     @click="delGoods">删除
          </el-button>
				</div>
        <el-table :cell-style="{ textAlign: 'center' }" :data="addGoods.allGoods"
                  :header-cell-style="{ 'text-align': 'center' }" border style="width: 100%" @selection-change="delFn">
          <el-table-column v-if="data.title != '查看详情'" type="selection" width="55"/>
          <el-table-column fixed label="序号" prop="tag" width="80px">
						<template #default="scope">
							{{ scope.$index + 1 }}
						</template>
					</el-table-column>
          <el-table-column label="自编码" prop="commodity.commoditySelfCode" width="120px"/>
          <el-table-column label="商品名称" prop="commodity.tradeName" width="120px"/>
          <el-table-column label="规格" prop="commodity.packageSpecification" width="120px"/>
          <el-table-column label="产地" prop="commodity.originPlace" width="120px"/>
          <el-table-column label="整件单位" prop="commodity.completeUnit" width="120px"/>
          <el-table-column label="基本单位" prop="commodity.basicUnit" width="120px"/>
          <el-table-column label="有效期(月)" prop="commodity.validityTime" width="120px"/>
          <el-table-column fixed="right" label="数量" prop="date" width="150px">
						<template #default="scope">
              <p v-show="!scope.row.num.flag" style="
											width: 100%;
											color: red;
											font-size: 16px;
										" @click="
											addGoods.allGoods[
												scope.$index
											].num.flag = true
											">
                <span v-if="scope.row.num.str.length == 0" style="color: red">点击输入</span>
								<span v-else>{{
                    scope.row.num.str
                  }}</span>
							</p>
              <el-input-number v-show="scope.row.num.flag" v-model="addGoods.allGoods[scope.$index].num
										.str
									" v-clickOutside="() => {
				addGoods.allGoods[
					scope.$index
				].num.flag = false;
			}
			" :disabled="data.title == '查看详情'" :min="0" :precision="0" :step="1" size="small" style="height: 30px"
                               @change="handPrice(scope)"/>
						</template>
					</el-table-column>
          <el-table-column fixed="right" label="单价" prop="date" width="150px">
						<template #default="scope">
              <p v-show="!scope.row.price.flag" style="
											width: 100%;
											color: red;
											font-size: 16px;
										" @click="
											addGoods.allGoods[
												scope.$index
											].price.flag = true
											">
								<span v-if="scope.row.price.str.length == 0
									" style="color: red">点击输入</span>
								<span v-else>
									{{ scope.row.price.str.toFixed(2) }}
								</span>
							</p>
              <el-input-number v-show="scope.row.price.flag" v-model="addGoods.allGoods[scope.$index]
									.price.str
								" v-clickOutside="() => {
			addGoods.allGoods[
				scope.$index
			].price.flag = false;
		}
		" :disabled="data.title == '查看详情'" :min="0" :precision="2" :step="1" size="small" style="height: 30px"
                               @change="handPrice(scope)"/>
						</template>
					</el-table-column>
          <el-table-column fixed="right" label="单价合计金额" prop="date" width="100px">
						<template #default="scope">
							<span style="color: red; font-size: 16px">{{
                  scope.row.total.toFixed(2)
                }}</span>
						</template>
					</el-table-column>
          <el-table-column label="入库数量" prop="intoQuantity" width="120px"/>
          <el-table-column label="库存余量" prop="inventoryBalance" width="120px"/>
          <el-table-column label="可开数量" prop="openableQuantity" width="120px"/>
          <el-table-column label="件装量" prop="commodity.ratio" width="120px"/>
          <el-table-column label="件数" prop="pieceNumber" width="120px"/>
          <el-table-column label="最后一次销售价格" prop="lastSalesPrice" width="140px"/>
          <el-table-column label="类型" prop="commodity.grugsType" width="120px"/>
          <el-table-column label="生产厂家" prop="manufacture.enterpriseName" width="120px"/>
          <el-table-column label="供应商" prop="supplier.enterpriseName" width="120px"/>
          <el-table-column label="批准文号" prop="approvalNumber" width="120px"/>
          <el-table-column label="税率" prop="commodity.taxRate" width="120px"/>
          <el-table-column label="剂型" prop="commodity.dosageForm" width="120px"/>
          <el-table-column label="入库时间" prop="intoTime" width="150px">
						<template #default="scope">
							{{ functionIndex.transformTimestamp(scope.row.intoTime) }}
						</template>
					</el-table-column>

          <el-table-column label="成本单价" prop="unitPrice" width="120px"/>

          <el-table-column label="贮藏温区" prop="commodity.storageTemperature" width="120px"/>
          <el-table-column label="批号" prop="batchNumber" width="120px"/>
          <el-table-column label="生产日期" prop="produceDate" width="150px">
						<template #default="scope">
							{{
								functionIndex.transformTimestamp(
									scope.row.produceDate
								)
							}}
						</template>
					</el-table-column>
          <el-table-column label="过期时间" prop="validityTime" width="120px">
						<template #default="scope">
							{{
								functionIndex.transformTimestamp(
									scope.row.validityTime
								)
							}}
						</template>
					</el-table-column>
				</el-table>
        <div v-if="data.title != '创建销售订单' &&
						data.editStrs.orderHeader.status == 3
						">
					<h3 style="margin: 12px 0">出库记录</h3>
          <el-table :cell-style="{ textAlign: 'center' }" :data="data.warehouseList"
                    :header-cell-style="{ 'text-align': 'center' }" style="width: 100%">
            <el-table-column label="自编码" prop="commodity.commoditySelfCode"/>
            <el-table-column label="商品名称" prop="commodity.tradeName"/>
            <el-table-column label="出库单编号" prop="outNo" width="170px"/>
						<el-table-column label="出库时间" prop="outTime">
							<template #default="scope">
								{{ functionIndex.transformTimestamp(scope.row.outTime) }}
							</template>
						</el-table-column>
            <el-table-column label="出库数量" prop="outQuantity"/>
            <el-table-column label="批号" prop="batchNumber"/>
            <el-table-column label="单据状态" prop="outStatus"/>
					</el-table>
				</div>
			</el-collapse-item>
			<el-collapse-item name="3">
				<template #title>
					<h3 class="col_title">合计信息</h3>
				</template>
        <span style="margin-left: 50px; font-size: 15px">合计数量：<span style="color: red; font-size: 19px">{{
            footForm.num
          }}</span></span>
        <span style="margin-left: 50px; font-size: 15px">合计金额：<span style="color: red; font-size: 19px">{{
            footForm.price
          }}</span></span>
        <span style="margin-left: 50px; font-size: 15px">折后总金额：<span style="color: red; font-size: 19px">{{
            footForm.discount
          }}</span></span>
			</el-collapse-item>
			<el-collapse-item name="4">
				<template #title>
					<h3 class="col_title">订单配置</h3>
				</template>
        <el-form ref="creatform2" :disabled="data.title == '查看详情'" :inline="true" :model="formInline2"
                 :rules="creatRules2" class="demo-form-inline" label-position="right" label-width="110px">
          <el-form-item label="三方章选项" label-width="110px" prop="n1">
            <el-select v-model="formInline2.n1" class="m-2" placeholder="请选择三方章选项" style="width: 199px">
              <el-option v-for="(item, index) in data.stamp" :key="index" :label="item.name"
                         :value="item.value"/>
						</el-select>
					</el-form-item>

          <el-form-item label="货主章选项" label-width="95px" prop="n2">
            <el-select v-model="formInline2.n2" class="m-2" placeholder="请选择货主章选项" style="width: 200px">
              <el-option v-for="(item, index) in data.owner" :key="index" :label="item.name"
                         :value="item.value"/>
						</el-select>
					</el-form-item>
          <el-form-item label="装货选项" label-width="85px" prop="n3">
            <el-select v-model="formInline2.n3" class="m-2" placeholder="请选择装货选项" style="width: 200px">
              <el-option v-for="(item, index) in data.encasement" :key="index" :label="item.name"
                         :value="item.value"/>
						</el-select>
          </el-form-item>
          <br/>
					<el-form-item label="是否拼箱" prop="n4">
						<el-radio-group v-model="formInline2.n4">
							<el-radio :label="false">否</el-radio>
							<el-radio :label="true">是</el-radio>
						</el-radio-group>
          </el-form-item>
          <br/>
					<el-form-item label="单据返回选项" prop="n5">
						<el-checkbox-group v-model="formInline2.n5">
              <el-checkbox v-for="(item, index) in data.receipts" :key="index" :label="item.value">{{
                  item.name
                }}
              </el-checkbox>
						</el-checkbox-group>
          </el-form-item>
          <br/>
					<el-form-item label="留存资料选项" prop="n6">
						<el-radio-group v-model="formInline2.n6">
              <el-radio label="0">非装箱联全部保留
              </el-radio>
							<el-radio label="1">不保留</el-radio>
						</el-radio-group>
          </el-form-item>
          <br/>
					<el-form-item label="质检报告" prop="n7">
						<el-checkbox-group v-model="formInline2.n7">
              <el-checkbox v-for="(item, index) in data.testing" :key="index" :label="item.value">{{
                  item.name
                }}
              </el-checkbox>
						</el-checkbox-group>
          </el-form-item>
          <br/>

					<el-form-item label="质量单据返回" prop="n8">
						<el-checkbox-group v-model="formInline2.n8">
              <el-checkbox v-for="(item, index) in data.return" :key="index" :label="item.value">{{
                  item.name
                }}
              </el-checkbox>
						</el-checkbox-group>
          </el-form-item>
          <br/>
					<el-form-item label="收集随货资料" prop="n9">
						<el-checkbox-group v-model="formInline2.n9">
              <el-checkbox v-for="(item, index) in data.gather" :key="index" :label="item.value">{{
                  item.name
                }}
              </el-checkbox>
						</el-checkbox-group>
          </el-form-item>
          <br/>
					<el-form-item label="备注">
            <el-input v-model="formInline2.n10" :rows="4" maxlength="1000" show-word-limit style="width: 40vw"
                      type="textarea"/>
					</el-form-item>
				</el-form>
			</el-collapse-item>
		</el-collapse>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import {functionIndex} from "@/views/salesManagement/functionIndex";
import {creatRules} from "@/views/salesManagement/orderList";
import {ClickOutside as vClickOutside} from 'element-plus'
// import { useStore } from 'vuex';
const LIU = vClickOutside
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const regionDatas = ref(JSON.parse(localStorage.getItem('cityData')) || [])
const emit = defineEmits(['delFn', 'delGoods', 'GoodsRule', 'clientList', 'serialList', 'handPrice', 'allPrice'])
const props = defineProps({
	formInline1: {
		default: {}
	},
	formInline2: {
		default: {}
	},
	data: {
		default: {}
	},
	addGoods: {
		default: {}
	},
	footForm: {
		default: {}
	},
})
const {formInline1, formInline2, data, addGoods, footForm} = toRefs(props)
const activeNames = ref(["1"]);
const handleChange = (val) => {
	console.log(val);
};
const creatform1 = ref();
const creatRules1 = reactive(creatRules(1));
const creatform2 = ref();
const creatRules2 = reactive(creatRules(2));
const clientList = () => {
	emit('clientList')
}
const serialList = () => {
	emit('serialList')
}
const handPrice = (scope) => {
	emit('handPrice', scope)
}
const threeFn = (flag) => {
	if (flag == '1' || flag == '2') {
		return '请选择三方物流'
	} else if (flag == '0' || flag == '3') {
		return '无需填写'
	} else {
		return ' '
	}
}
const allPrice = () => {
	emit('allPrice')
}
const GoodsRule = () => {
	emit('GoodsRule')
}
const delGoods = () => {
	emit('delGoods')
}
const delFn = (selection) => {
	emit('delFn', selection)
}
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')

})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	creatform1,
  creatform2,
  activeNames
})

</script>
<style lang='scss' scoped>
@import '../style/order';
</style>
