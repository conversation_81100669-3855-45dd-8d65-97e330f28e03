<!--  1. 保温箱管理 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form :inline="true" :model="queryParams" class="seache-form">
                <el-form-item label="设备编号" prop="serialNumber">
                    <el-input v-model="queryParams.serialNumber" clearable placeholder="请输入设备编号" />
                </el-form-item>
                <el-form-item label="查询时间" style="width: 305px">
                    <el-date-picker v-model="queryParams.daterangeValidityTerm" end-placeholder="结束日期" range-separator="-" start-placeholder="开始日期" type="datetimerange" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
                </el-form-item>
                <el-form-item label="错误类型" prop="errorType">
                    <el-select v-model="queryParams.errorType" clearable placeholder="请选择错误类型">
                        <el-option v-for="dict in errorTypeList" :key="dict.dictValue" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="getList" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:export']" icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                    </el-col>
                    <!-- <right-toolbar :showSearch="showSearch" @queryTable="getList"></right-toolbar> -->
                </el-row>
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table v-loading="loading" :data="abnormalprintList" border style="margin-top: 15px" @selection-change="handleSelectionChange">
<!--                <el-table-column align="center" fixed="left" type="selection" width="55" />-->
                <el-table-column align="center" fixed="left" label="设备编号" prop="deviceTerminal.serialNumber" />
                <el-table-column align="center" label="开始时间" prop="startPrintDate" width="180">
                    <template #default="scope">
                        <span>{{ moment(scope.row.startPrintDate).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="结束时间" prop="endPrintDate" width="180">
                    <template #default="scope">
                        <span>{{ moment(scope.row.endPrintDate).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="第一条数据时间" prop="firstDataTime" width="180">
                    <template #default="scope">
                        <span>{{ moment(scope.row.firstDataTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="最后一条数据时间" prop="endDataTime" width="180">
                    <template #default="scope">
                        <span>{{ moment(scope.row.endDataTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>
                <el-table-column :formatter="(row) => formDict(printErrorIsCheck, row.isCheck)" align="center" label="已校验" prop="isCheck"> </el-table-column>
                <el-table-column :formatter="(row) => formDict(printErrorCheckResult, row.checkResult)" align="center" label="校验结果" prop="checkResult" />
                <el-table-column :formatter="(row) => formDict(errorTypeList, row.errorType)" align="center" label="错误类型" prop="errorType" />
                <el-table-column align="center" label="错误信息" prop="errorInfo" />
                <el-table-column align="center" label="原数据" prop="data" />
                <el-table-column align="center" fixed="right" label="接口调用时间" prop="requestDate" width="180">
                    <template #default="scope">
                        <span>{{ moment(scope.row.requestDate).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed="right" label="操作人" prop="operateBy.name" />
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
import abnormalprintingApi from '@/api/management/abnormalprinting';
import moment from 'moment';
import qs from 'qs';
import SearchButton from '@/components/searchModule/SearchButton.vue';
// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
});
// 显示搜索条件
const showSearch = ref(true);

// 校验结果
const printErrorCheckResult = ref([]);
// 错误类型
const errorTypeList = ref([]);
// 是否检验
const printErrorIsCheck = ref([]);
// 字典请求
//字典回显
const formDict = (data, val) => {
    return data && val ? proxy.selectDictLabel(data, val) : '--';
};
const getDict = async () => {
    printErrorCheckResult.value = await proxy.getDictList('print_error_check_result');
    errorTypeList.value = await proxy.getDictList('print_exception_error_type ');
    printErrorIsCheck.value = await proxy.getDictList('print_error_is_check');
};
getDict();

// 查询设备列表数据
const abnormalprintList = ref([]);
const timeList = ref({});
function getList() {
    if (queryParams.value.daterangeValidityTerm) {
        timeList.value = {
            beginStartPrintDate: queryParams.value.daterangeValidityTerm[0],
            endStartPrintDate: queryParams.value.daterangeValidityTerm[1]
        };
    }
    // if (queryParams.value.timeList) {
    //     timeList.value = {
    //         beginAlarmTime: queryParams.value.timeList[0],
    //         endAlarmTime: queryParams.value.timeList[1]
    //     }
    // }
    let data = {
        current: queryParams.value.current,
        size: queryParams.value.size,
        total: queryParams.value.total,
        errorType: queryParams.value.errorType,
        serialNumber: queryParams.value.serialNumber,
        beginStartPrintDate: queryParams.value.daterangeValidityTerm ? timeList.value.beginAlarmTime : null,
        endStartPrintDate: queryParams.value.daterangeValidityTerm ? timeList.value.endAlarmTime : null
    };
    abnormalprintingApi.printErrorLogList(data).then((res) => {
        if (res.code == 200) {
            abnormalprintList.value = res.data.records;
            queryParams.value.total = res.data.total;
        }
    });
}
getList();
// 重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    getList();
}

const chooseList = ref([]);
const handleSelectionChange = (key) => {
    chooseList.value = key;
};
// 导出
const newFilArr = ref([]);
function handleExport() {
    // if (chooseList.value.length > 0) {
        // let selectIds = "";
        // newFilArr.value = [];
        // chooseList.value.filter((item) => {
        //     newFilArr.value.push(item.id)
        // })
        // selectIds = newFilArr.value.join(",");
        let list = {
            // id: selectIds,
            filename: '报警信息记录',
            // exportFields: ['deviceTerminal.serialNumber', 'startPrintDate', 'endPrintDate', 'firstDataTime', 'endDataTime', 'checkResult', 'isCheck', 'errorType', 'errorInfo', 'data', 'requestDate', 'operateBy.name']
        };
        abnormalprintingApi
            .printErrorLogExport(qs.stringify(list, { arrayFormat: 'repeat' }), '', '', 'blob')
            .then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '打印异常记录.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    this.$message.error('导出异常请联系管理员');
                }
            })
            .catch((err) => {
                proxy.msgError(err.msg);
            });
    // } else {
    //     proxy.msgError('请选择需要导出的数据！');
    // }
}
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.define {
    width: 550px;
}

.drawer-footer {
    position: absolute;
    bottom: 20px;
    left: 300px;
}
</style>
