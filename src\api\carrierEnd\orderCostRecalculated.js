import request from '@/utils/request';

export default {
    // 列表
    getTheOrderFeeRecalculationList: function (params) {
        return request.get('/tms/orderDrug/calculateCost/list', params);
    },
    // 合算公式下拉框
    getFormulaList: function (params) {
        return request.get('/cost/formula/select', params);
    },
    // 价格本下拉框
    getPricesForThisList: function (params) {
        return request.post('/cost/priceBook/select', params);
    },
    // 客户承运商下拉框
    getPartnerListByOrder: function (params) {
        return request.get('/cost/order/costDetailed/orderCompanyOrCarrier', params);
    },
    // 订单费用重新计算
    orderCostRecalculate: function (params) {
        return request.post('/tms/orderDrug/calculateCost', params);
    },
    // 导出计算结果
    exportCalculateCost: function (params, config, resDetail, responseType) {
        return request.get('/cost/order/costDetailed/exportAll', params, config, resDetail, responseType);
    },
    // 修改订单费用
    updateOrderCost: function (params) {
        return request.post('/cost/order/costDetailed', params);
    },
    // 批量修改订单费用
    updateOrderCostBatch: function (params) {
        return request.post('/cost/order/costDetailed/batchUpdate', params);
    },
    // 修改揽收/干线/配送费用
    updateOrderCostByType: function (params) {
        return request.post('/cost/order/orderCost/edit', params);
    },
    // 客户下拉框
    getCustomerList: function (params) {
        return request.post('/tms/orderDrug/queryCompanySelect', params);
    },
    // 导出
    exportRecalculate: function (params) {
        return request.get('/tms/orderDrug/cost/recalculate/export', params);
    }
};
