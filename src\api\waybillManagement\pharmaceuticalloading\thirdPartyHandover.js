import config from '@/config';
import http from '@/utils/request';

export default {
    // 获取三方快递交接列表
    getThirdPartyHandoverList: (params) => {
        return http.get(`${config.API_URL}/waybill/thirdPartyHandover/list`, params);
    },

    // 获取三方快递交接详情
    getThirdPartyHandoverDetail: (id) => {
        return http.get(`${config.API_URL}/waybill/thirdPartyHandover/detail/${id}`);
    },

    // 交接三方快递
    handoverThirdParty: (params) => {
        return http.post(`${config.API_URL}/waybill/thirdPartyHandover/handover`, params);
    },

    // 批量交接三方快递
    batchHandoverThirdParty: (params) => {
        return http.post(`${config.API_URL}/waybill/thirdPartyHandover/batchHandover`, params);
    },

    // 更新三方快递交接记录
    updateThirdPartyHandover: (params) => {
        return http.put(`${config.API_URL}/waybill/thirdPartyHandover/update`, params);
    },

    // 删除三方快递交接记录
    deleteThirdPartyHandover: (id) => {
        return http.delete(`${config.API_URL}/waybill/thirdPartyHandover/delete/${id}`);
    },

    // 导出三方快递交接数据
    exportThirdPartyHandover: (params) => {
        return http.download(`${config.API_URL}/waybill/thirdPartyHandover/export`, params);
    },

    // 打印三方快递交接单
    printThirdPartyHandover: (id) => {
        return http.get(`${config.API_URL}/waybill/thirdPartyHandover/print/${id}`);
    },

    // 获取三方物流公司列表
    getThirdPartyLogisticsList: () => {
        return http.get(`${config.API_URL}/waybill/thirdPartyHandover/logisticsList`);
    },

    // 获取订单状态字典
    getOrderStatusDict: () => {
        return http.get(`${config.API_URL}/waybill/thirdPartyHandover/orderStatusDict`);
    },

    // 获取保温箱列表
    getIncubatorList: (params) => {
        return http.get(`/tms/handover/third/incubator/list`, params);
    },

    // 获取运单列表
    getWaybillList: (params) => {
        return http.get(`/tms/handover/third/collect/order/list`, params);
    },

    // 交接三方快递 - 新接口
    handoverThirdPartyNew: (params) => {
        return http.post(`/tms/handover/third/handover`, params);
    },

    // 获取三方快递记录列表
    getThirdPartyRecordList: (params) => {
        return http.get(`/tms/handover/third/record/page`, params);
    },

    // 导出三方快递记录
    exportThirdPartyRecord: (params, config, resDetail, responseType) => {
        return http.get(`/tms/handover/third/record/export`, params, config, resDetail, responseType);
    },
    // 撤销
    cancelThirdPartyHandover: (id) => {
        return http.get(`/tms/handover/third/cancel/${id}`);
    },

    // 重新下单
    reorderThirdPartyHandover: (params) => {
        return http.get(`/tms/handover/third/reorder`, params);
    },
    // 获取运单附件
    getWaybillAttachment: (id) => {
        return http.get(`/tms/handover/third/waybill/file/${id}`);
    },
    // 修改打印状态
    updatePrintStatus: (id) => {
        return http.get(`/tms/handover/third/update/print/${id}`);
    }
};
