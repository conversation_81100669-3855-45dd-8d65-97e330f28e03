<template>
  <div :class='className' :style='{ height: height, width: width }' />
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons'); // echarts theme

export default {
  name: 'CustomRingChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    height: {
      type: String,
      default: '100%'
    },
    pieData: {
      type: [Object, Array],
      default: () => []
    },
    width: {
      type: String,
      default: '100%'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    pieData: {
      handler() {
        this.initChart();
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons');
      this.chart.setOption({
        color: this.pieData.color || ['#3ba1ff', '#f0f2f5'],
        grid: {
          left: 0,
          right: 0,
          bottom: 0,
          top: 0
        },
        graphic: [
          {
            type: 'text',
            right: 'center',
            top: '36%',
            cursor: 'default',
            style: {
              text: this.pieData.graphicStyleText,
              fill: '#9a9a9a'
            }
          },
          {
            type: 'text',
            right: 'center',
            top: '60%',
            cursor: 'default',
            style: {
              text: this.pieData.dataValue.length === 0 ? '0' : this.pieData.value,
              fill: '#333333'
            }
          }
        ],
        series: [
          {
            type: 'pie',
            radius: ['65%', '100%'],
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 3
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            emphasis: {
              disabled: true,
              scale: false
            },
            data: this.pieData.dataValue.length === 0 ? [{ value: 0, name: '' }, { value: 1, name: '' }] : this.pieData.dataValue,
            legendHoverLink: false
          }
        ]
      });
    }
  }
};
</script>

<style scoped>

</style>
