<template>

  <div v-loading="loading" style="background-color: rgb(245, 247, 253); padding: 10px">
    <el-card shadow="never" style="margin-bottom: 10px;">
      <el-descriptions :column="3" border size="default" title="订单基本信息">
        <el-descriptions-item label="订单号">
          {{ orderInfo.orderNo }}
        </el-descriptions-item>
        <el-descriptions-item label="货物总件数">
          <span class="goods-packages">{{ orderInfo.goodsPackages }}件</span>
        </el-descriptions-item>
        <el-descriptions-item label="运单号" v-if="orderInfo.transOrderNo">
          {{ orderInfo.transOrderNo }}
        </el-descriptions-item>
        <el-descriptions-item label="揽收方式" v-if="orderInfo.orderTypeDesc">
          {{ orderInfo.orderTypeDesc }}
        </el-descriptions-item>
        <el-descriptions-item label="产品分类" v-if="orderInfo.productClassDesc">
          {{ orderInfo.productClassDesc }}
        </el-descriptions-item>
        <el-descriptions-item label="运输类型" v-if="orderInfo.productTypeDesc">
          {{ orderInfo.productTypeDesc }}
        </el-descriptions-item>
        <el-descriptions-item label="温层类型" v-if="orderInfo.temperatureType">
          {{ orderInfo.temperatureType.describtion }}
        </el-descriptions-item>
        <el-descriptions-item label="温层类型描述" v-if="orderInfo.temperatureTypeDesc">
          <span class="temperature-type">{{ orderInfo.temperatureTypeDesc }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card shadow="never" v-if="recordInfo.operatRecordList && recordInfo.operatRecordList.length > 0" :body-style="{ paddingBottom: '0px' }">
      <el-collapse v-model="activeName" accordion @change="collapseChange">
        <el-timeline class="custom-timeline">
          <el-timeline-item v-for="(detail, index) in recordInfo.operatRecordList" :key="index"
            class="timeline-item-custom"
            :class="{ 'is-current': index === recordInfo.operatRecordList.length - 1 }">
            <template #dot>
              <div class="timeline-dot" :class="{ 'current-dot': index === recordInfo.operatRecordList.length - 1 }"></div>
            </template>
            <el-collapse-item :name="index" class="timeline-collapse-item">
              <template #title>
                <div class="timeline-header">
                  <div class="timeline-title">{{ detail.statusDesc }}</div>
                  <div class="timeline-time">{{ formatDate(detail.createDate) }}</div>
                </div>
              </template>
              <div v-loading="detail.loading">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="操作时间" v-if="detail.createDate">
                    {{ formatDate(detail.createDate) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="经办人" v-if="detail.operatName">
                    {{ detail.operatName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="网点" v-if="detail.branchName">
                    {{ detail.branchName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="批号" v-if="detail.batchNo">
                    {{ detail.batchNo }}
                  </el-descriptions-item>
                  <el-descriptions-item label="集货区编码" v-if="detail.areaCode">
                    {{ detail.areaCode }}
                  </el-descriptions-item>
                  <el-descriptions-item label="集货区名称" v-if="detail.areaName">
                    {{ detail.areaName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="保温箱号" v-if="detail.incubatorNo">
                    {{ detail.incubatorNo }}
                  </el-descriptions-item>
                  <el-descriptions-item label="保温箱名称" v-if="detail.incubatorName">
                    {{ detail.incubatorName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="传感器号" v-if="detail.deviceNo">
                    {{ detail.deviceNo }}
                  </el-descriptions-item>
                  <el-descriptions-item label="低温阈值" v-if="detail.lowTemperature">
                    <span class="highlight">{{ detail.lowTemperature }}℃</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="高温阈值" v-if="detail.highTemperature">
                    <span class="highlight">{{ detail.highTemperature }}℃</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="车牌号" v-if="detail.carCode">
                    {{ detail.carCode }}
                  </el-descriptions-item>
                  <el-descriptions-item label="货物件数" v-if="detail.goodsNum">
                    <span class="goods-count">{{ detail.goodsNum }}件</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="装车分配司机" v-if="detail.driverName">
                    {{ detail.driverName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="三方物流" v-if="detail.flowName">
                    {{ detail.flowName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="运输方式" v-if="detail.transWay">
                    {{ mainRecordTransWayFormatting(detail) }}
                  </el-descriptions-item>
                </el-descriptions>

                <!-- 三方快递信息 -->
                <el-descriptions :column="2" border v-if="detail.thirdExpress && detail.thirdExpress.name"
                  style="margin-top: 10px;">
                  <el-descriptions-item label="三方快递">
                    {{ detail.thirdExpress.name }}
                  </el-descriptions-item>
                  <el-descriptions-item label="三方单号" v-if="detail.thirdExpress.thirdWaybillNos && detail.thirdExpress.thirdWaybillNos.length > 0">
                    <div class="code-tag-container">
                      <el-tag v-for="(waybillNo, waybillIndex) in detail.thirdExpress.thirdWaybillNos" :key="waybillIndex">
                        {{ waybillNo }}
                      </el-tag>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>

                <!-- 保温箱信息 -->
                <div v-if="detail.incubatorList && detail.incubatorList.length > 0" style="margin-top: 10px;">
                  <div v-for="(row, rowIndex) in detail.incubatorList" :key="rowIndex">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="保温箱号" v-if="row.incubatorNo">
                        {{ row.incubatorNo }}
                      </el-descriptions-item>
                      <el-descriptions-item label="保温箱名称" v-if="row.incubatorName">
                        {{ row.incubatorName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="温湿度设备号" v-if="row.deviceNo">
                        {{ row.deviceNo }}
                      </el-descriptions-item>
                      <el-descriptions-item label="温度阈值" v-if="row.maxTemp && row.minTemp">
                        <span class="highlight">{{ row.minTemp }}℃-{{ row.maxTemp }}℃</span>
                      </el-descriptions-item>
                      <el-descriptions-item label="三方物流" v-if="row.expressName">
                        {{ row.expressName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="三方单号" v-if="row.thirdWaybillNo">
                        <el-tag>{{ row.thirdWaybillNo }}</el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="箱码" v-if="row.codeList && row.codeList.length > 0">
                        <div class="code-tag-container">
                          <el-tag v-for="(code, codeIndex) in row.codeList" :key="codeIndex" class="code-tag">
                            {{ code.code }}
                          </el-tag>
                        </div>
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>

                <!-- 箱码展示 -->
                <el-descriptions :column="1" border v-if="detail.boxCodeList && detail.boxCodeList.length > 0"
                  style="margin-top: 10px;">
                  <el-descriptions-item label="箱码">
                    <div class="code-tag-container">
                      <el-tag v-for="(code, codeIndex) in detail.boxCodeList" :key="codeIndex" class="code-tag">
                        {{ code.code }}
                      </el-tag>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-collapse-item>
          </el-timeline-item>
        </el-timeline>
      </el-collapse>
    </el-card>

    <!-- 数据为空时的提示 -->
    <el-card shadow="never" v-else-if="!loading">
      <el-empty description="暂无运输记录数据" />
    </el-card>
  </div>

</template>

<script>
import waybillTracking from '@/api/logisticsManagement/waybillTracking.js'; // 运单跟踪
import moment from 'moment';
export default {
  name: "WaybillTrackingDetails",
  props: {
    orderInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      recordInfo: [],
      fourplDriverTaskSourceOptions: [], // 4PL配送任务来源字典值
      fourplMainRecordTransWayOptions: [], // 运输方式字典值
      fourplTaskTypeOptions: [], // 任务类型字典值
      fourplProductClassDicts: [], // 产品分类
      productTypeDicts: [], // 运输类型
      collectionMethod: [], // 揽收方式
      activeName: null, // 改为null，在数据加载后设置为最后一个
    }
  },
  async created() {
    /** 产品分类 */
    this.fourplProductClassDicts = await this.getDictList('fourpl_product_class');
    // 运输类型4PL
    this.productTypeDicts = await this.getDictList('fourpl_product_type');
    /** 揽收方式 */
    this.collectionMethod = await this.getDictList('fourpl_mail_service');

    this.getTransRecordDetail()
    this.orderInfo.orderTypeDesc = this.selectDictLabels(this.collectionMethod, this.orderInfo.orderType)
    this.orderInfo.productClassDesc = this.selectDictLabels(this.fourplProductClassDicts, this.orderInfo.productClass)
    this.orderInfo.productTypeDesc = this.selectDictLabels(this.productTypeDicts, this.orderInfo.productType)
  },
  methods: {
    collapseChange() {
      if (this.activeName !== null && this.activeName !== undefined &&
          this.recordInfo.operatRecordList &&
          this.recordInfo.operatRecordList[this.activeName]) {
        if (!this.recordInfo.operatRecordList[this.activeName].boxCodeList) {
          this.getOperatRecordDetail(this.recordInfo.operatRecordList[this.activeName].id)
        }
      }
    },
    /*获取轨迹跟踪明细*/
    getTransRecordDetail() {
      this.loading = true
      waybillTracking.getOperatRecordList({ transOrderNo: this.orderInfo.transOrderNo }).then((response) => {
        if (response.code === 200 && response.data) {
          this.recordInfo = response.data;
          // 检查是否有数据，设置默认展开最后一个节点
          if (this.recordInfo.operatRecordList && this.recordInfo.operatRecordList.length > 0) {
            const lastIndex = this.recordInfo.operatRecordList.length - 1;
            this.activeName = lastIndex;
            this.getOperatRecordDetail(this.recordInfo.operatRecordList[lastIndex].id);
          }
        }
        setTimeout(() => {
          this.loading = false
        }, 500)
      }).catch(() => {
        this.loading = false
      })
    },
    /*获取轨迹跟踪明细详情*/
    getOperatRecordDetail(id) {
      this.recordInfo.operatRecordList[this.activeName].loading = true;
      waybillTracking.getOperatRecordDetail({ operatId: id, orderType: this.orderInfo.orderType }).then((response) => {
        if (response.code === 200 && response.data) {
          this.recordInfo.operatRecordList[this.activeName].boxCodeList = response?.data?.codeList;
          this.recordInfo.operatRecordList[this.activeName].incubatorList = response?.data?.incubatorList;

          // 处理三方快递信息
          if (response?.data?.thirdExpress) {
            this.recordInfo.operatRecordList[this.activeName].thirdExpress = response.data.thirdExpress;
          }

          if (response?.data?.carVo) {
            this.recordInfo.operatRecordList[this.activeName].boxCodeList = response?.data?.carVo?.codeList;
            this.recordInfo.operatRecordList[this.activeName].carCode = response?.data?.carVo?.carCode;
            this.recordInfo.operatRecordList[this.activeName].flowName = response?.data?.carVo?.flowName;
            this.recordInfo.operatRecordList[this.activeName].incubatorList = response?.data?.carVo?.incubatorList;
            this.recordInfo.operatRecordList[this.activeName].driverName = response?.data?.carVo?.driverName;
          }
          if (response?.data?.areaVo) {
            this.recordInfo.operatRecordList[this.activeName].boxCodeList = response?.data?.areaVo?.codeList;
            this.recordInfo.operatRecordList[this.activeName].areaName = response?.data?.areaVo?.areaName;
            this.recordInfo.operatRecordList[this.activeName].areaCode = response?.data?.areaVo?.areaCode;
          }
        }
        this.recordInfo.operatRecordList[this.activeName].loading = false;
        console.log(this.recordInfo.operatRecordList[this.activeName].incubatorList)
      }).catch(() => {
        this.recordInfo.operatRecordList[this.activeName].loading = false;
      })
    },
    // 标准时间格式化
    formatDate(cellValue) {
      return moment(cellValue).format('YYYY-MM-DD HH:mm:ss');
    },
    // 运输方式格式化
    mainRecordTransWayFormatting(detail) {
      // 根据实际业务需求格式化运输方式
      if (detail.transWay) {
        return detail.transWay;
      }
      return '';
    },
  }
}
</script>

<style scoped>
/* 页面容器样式 */
.page-container {
  background-color: #f5f7fd;
  padding: 16px;
  min-height: 100vh;
}

/* 订单信息卡片样式 */
/* Descriptions 组件内的特殊样式 */
.goods-packages {
  color: #f56c6c;
  font-size: 18px;
  font-weight: 700;
}

.temperature-type {
  color: #f56c6c;
  font-weight: 600;
}

/* 自定义时间轴样式 */
.custom-timeline {
  padding: 0;
}

/* 时间轴项目样式 */
.timeline-item-custom {
  position: relative;
  padding-bottom: 0;
}

/* 自定义时间轴圆点 */
.timeline-dot {
  width: 12px;
  height: 12px;
  background-color: #c0c4cc;
  border-radius: 50%;
  border: 2px solid #fff;
  transition: background-color 0.3s ease;
}

/* 当前状态的圆点（最后一个） */
.timeline-dot.current-dot {
  background-color: #409eff;
}

/* 时间轴头部样式 */
.timeline-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 14px 20px 14px 0;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.timeline-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 14px;
  color: #999;
  font-weight: initial;
}

/* 高亮样式 */
.highlight {
  color: #f56c6c;
  font-weight: 600;
}

.goods-count {
  color: #f56c6c;
  font-size: 18px;
  font-weight: 700;
}

/* 箱码标签容器样式 */
.code-tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 箱码标签样式 */
.code-tag {
  margin: 0;
  background-color: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
  display: inline-flex;
  align-items: center;
  line-height: 1;
  height: auto;
  min-height: 24px;
}

/* 原有样式保持兼容 */
.el-collapse {
  border-top: 0;
}

.hy-font-24 {
  font-size: 24px;
}

.hy-weight {
  font-weight: bold;
}

.hy-c-red {
  color: red;
}

/* Element UI 深度样式覆盖 */
::v-deep .el-timeline {
  padding-left: 0;
}

::v-deep .el-timeline-item {
  padding-bottom: 0;
}

::v-deep .el-timeline-item__node {
  left: 6px;
  width: 12px;
  height: 12px;
  background-color: #c0c4cc;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #c0c4cc;
  transition: all 0.3s ease;
}

/* 当前状态的节点样式 */
::v-deep .el-timeline-item.is-current .el-timeline-item__node {
  background-color: #409eff;
  box-shadow: 0 0 0 2px #409eff;
}

::v-deep .el-timeline-item__wrapper {
  padding-left: 32px;
  top: -6px;
}

::v-deep .el-timeline-item__tail {
  left: 5px;
  border-left: 2px solid #e4e7ed;
  top: 19px;
}

::v-deep .el-timeline-item__dot {
  top: 11px;
}

::v-deep .el-timeline-item:last-child .el-timeline-item__tail {
  display: none;
}

/* 去掉最后一项的下划线 */
::v-deep .el-timeline-item:last-child .timeline-card {
  border-bottom: none;
}

::v-deep .el-timeline-item:last-child .timeline-header {
  border-bottom: none;
}

::v-deep .el-collapse {
  border: 0;
}

::v-deep .el-collapse-item {
  border: 0;
}

::v-deep .el-collapse-item__wrap {
  border: 0;
  background: transparent;
}

::v-deep .el-collapse-item__header {
  height: auto;
  line-height: initial;
  border: 0;
  background: transparent;
  padding: 0;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
}

::v-deep .el-collapse-item__header .timeline-header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

::v-deep .el-collapse-item__arrow {
  margin: 16px 8px 0 0;
  color: #409eff;
  align-self: flex-start;
}

::v-deep .el-collapse-item__content {
  padding: 0;
  background: transparent;
}

/* Descriptions 组件样式优化 */
::v-deep .el-descriptions {
  margin-top: 0;
  margin-bottom: 0;
}

/* 展开时隐藏头部下划线，避免与 descriptions 边框重合 */
::v-deep .el-collapse-item.is-active .timeline-header {
  border-bottom: none;
}

::v-deep .timeline-content .el-descriptions {
  border-top: none;
}

::v-deep .timeline-content .el-descriptions .el-descriptions__table {
  border-top: none;
}

::v-deep .el-descriptions__body .el-descriptions__table {
  border-collapse: collapse;
}

::v-deep .el-descriptions__label {
  font-weight: 600;
  color: #606266;
  background-color: #fafafa;
  width: 120px;
  text-align: left;
  padding: 8px 12px;
}

::v-deep .el-descriptions__content {
  color: #303133;
  font-weight: 500;
  padding: 8px 12px;
}

::v-deep .el-descriptions__table .el-descriptions__cell {
  border: 1px solid #ebeef5;
}

/* 垂直方向的 Descriptions 样式 */
::v-deep .el-descriptions--vertical .el-descriptions__label {
  background-color: #fafafa;
  font-weight: 600;
  color: #606266;
}

::v-deep .el-descriptions--vertical .el-descriptions__content {
  color: #303133;
  font-weight: 500;
}
</style>
