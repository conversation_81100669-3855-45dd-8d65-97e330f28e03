<template>
  <el-container>
    <el-aside width="300px" v-loading="menuloading">
      <el-container>
        <el-header>
          <el-input
            placeholder="输入关键字进行过滤"
            v-model="menuFilterText"
            clearable
          ></el-input>
        </el-header>
        <el-main class="nopadding">
          <el-tree
            ref="menu"
            class="menu"
            node-key="id"
            :data="menuList"
            :props="menuProps"
            draggable
            highlight-current
            :expand-on-click-node="false"
            check-strictly
            show-checkbox
            :filter-node-method="menuFilterNode"
            @node-click="menuClick"
            @node-drop="nodeDrop"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span class="label">
                  {{ node.label }}
                </span>
                <span class="do">
                  <el-button
                    icon="el-icon-plus"
                    size="small"
                    @click.stop="add(node, data)"
                  ></el-button>
                </span>
              </span>
            </template>
          </el-tree>
        </el-main>
        <el-footer style="height: 51px">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-plus"
            @click="add()"
          ></el-button>
          <el-button
            type="danger"
            size="small"
            plain
            icon="el-icon-delete"
            @click="delMenu"
          ></el-button>
        </el-footer>
      </el-container>
    </el-aside>
    <el-container>
      <el-main class="nopadding" style="padding: 20px" ref="main">
        <save ref="save" :menu="menuList"></save>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
let newMenuIndex = 1;
import save from "./save";

export default {
  name: "settingMenu",
  components: {
    save,
  },
  data() {
    return {
      //菜单加载中
      menuloading: false,
      //菜单列表
      menuList: [],
      menuProps: {
        label: (data) => {
          return data.name;
        },
      },
      //菜单筛选属性
      menuFilterText: "",
    };
  },
  watch: {
    //菜单过滤，顶部搜索框
    menuFilterText(val) {
      this.$refs.menu.filter(val);
    },
  },
  mounted() {
    this.getMenu();
  },
  methods: {
    /*
     * 加载菜单数据
     * @author: 路正宁
     * @date: 2023-03-23 11:22:39
     */
    async getMenu() {
      this.menuloading = true;
      var res = await this.$API.sysMenuService.treeData();
      if (res.code == 200) {
        this.menuList = res.data;
      } else {
        this.$Response.errorNotice(res, "系统菜单数据查询失败");
      }
      this.menuloading = false;
    },
    /*
     * 菜单点击
     * @author: 路正宁
     * @date: 2023-03-23 11:23:10
     */
    menuClick(data, node) {
      var pid = node.level == 1 ? undefined : node.parent.data.id;
      this.$refs.save.setData(data, pid);
      this.$refs.main.$el.scrollTop = 0;
    },
    /*
     * 菜单搜索，菜单顶部搜索框
     * @author: 路正宁
     * @date: 2023-03-23 11:27:25
     */
    menuFilterNode(value, data) {
      if (!value) return true;
      var targetText = data.name;
      return targetText.indexOf(value) !== -1;
    },
    /*
     * 菜单拖拽移动
     * @author: 路正宁
     * @date: 2023-03-23 11:11:01
     */
    async nodeDrop(draggingNode, dropNode, dropType) {
      var draggingNodeId, dropNodeId, sort;
      //拖拽对象ID
      draggingNodeId = draggingNode.data.id;
      //释放对象ID
      dropNodeId = dropNode.data.id;
      if (dropType === "before") {
        //之前
        sort = dropNode.data.sort + 1;
        draggingNode.data.sort = sort;
      } else if (dropType === "after") {
        //之后
        sort = dropNode.data.sort - 1;
        draggingNode.data.sort = sort;
      } else {
        sort = 100;
      }
      //排序接口调用
      var res = await this.$API.sysMenuService.updateSort(
        draggingNodeId,
        dropNodeId,
        dropType,
        sort
      );
      //处理结果
      if (res.code == 200) {
        draggingNode.data = res.data;
        this.$message.success("排序成功");
      } else {
        this.$Response.errorNotice(res, "排序失败");
      }
    },
    /*
     * 添加菜单
     * @author: 路正宁
     * @date: 2023-03-23 11:24:13
     */
    async add(node, data) {
      //组装基本菜单数据
      var newMenuName = "未命名" + newMenuIndex++;
      var newMenuData = {
        //父级ID
        parent: {
          id: data ? data.id : "",
        },
        //菜单名称
        name: newMenuName,
        //菜单别名
        alias: newMenuName,
        //菜单类型
        type: "menu",
        //路径
        path: "",
        //是否隐藏
        hide: false,
        //是否固定标签栏
        fixedLabelBar: "0",
        component: "",
      };
      //页面加载中
      this.menuloading = true;
      //保存菜单方法
      var res = await this.$API.sysMenuService.save(newMenuData);
      if (res.code == 200) {
        this.$message.success("添加成功");
      } else {
        this.$Response.errorNotice(res, "添加失败");
      }
      //释放页面加载
      this.menuloading = false;
      newMenuData.id = res.data.id;
      //动态追加添加后的菜单节点
      this.$refs.menu.append(newMenuData, node);
      //设置菜单列表当前节点的焦点
      this.$refs.menu.setCurrentKey(newMenuData.id);
      var pid = node ? node.data.id : "";
      //设置表单数据
      this.$refs.save.setData(newMenuData, pid);
    },
    /*
     * 删除菜单
     * @author: 路正宁
     * @date: 2023-03-23 17:46:37
     */
    async delMenu() {
      //获取选中的节点
      var CheckedNodes = this.$refs.menu.getCheckedNodes();
      if (CheckedNodes.length == 0) {
        this.$message.warning("请选择需要删除的项");
        return false;
      }
      //删除操作确认
      var confirm = await this.$confirm("确认删除已选择的菜单吗？", "提示", {
        type: "warning",
        confirmButtonText: "删除",
        confirmButtonClass: "el-button--danger",
      }).catch(() => {});
      if (confirm != "confirm") {
        return false;
      }
      //页面加载
      this.menuloading = true;
      //请求参数处理删除id参数
      var reqData = CheckedNodes.map((item) => item.id).join(",");
      //调用删除接口
      var res = await this.$API.sysMenuService.delete(reqData);
      this.menuloading = false;
      if (res.code == 200) {
        //在列表中移除已删除的菜单项
        CheckedNodes.forEach((item) => {
          var node = this.$refs.menu.getNode(item);
          //移除菜单项
          this.$refs.menu.remove(item);
          if (node.isCurrent) {
            //当前删除的是当前编辑的菜单，则清空编辑表单页面
            this.$refs.save.setData({});
          }
        });
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
    },
  },
};
</script>

<style scoped>
.menu:deep(.el-tree-node__label) {
  display: flex;
  flex: 1;
  height: 100%;
}
.custom-tree-node {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  height: 100%;
  padding-right: 24px;
}
.custom-tree-node .label {
  display: flex;
  align-items: center;
  height: 100%;
}
.custom-tree-node .label .el-tag {
  margin-left: 5px;
}
.custom-tree-node .do {
  display: none;
}
.custom-tree-node .do i {
  margin-left: 5px;
  color: #999;
}
.custom-tree-node .do i:hover {
  color: #333;
}

.custom-tree-node:hover .do {
  display: inline-block;
}
</style>
