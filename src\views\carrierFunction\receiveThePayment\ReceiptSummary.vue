<template>
    <div v-loading="fullLoading" :element-loading-text="fullLoadingText" class="app-container">
        <!-- 搜索区域 -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" ref="searchCard" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" @submit.native.prevent>
                <el-form-item label="时间" prop="billDate">
                    <el-date-picker v-model="queryParams.billDate" :shortcuts="monthShortcuts" end-placeholder="结束月份" range-separator="至" start-placeholder="开始月份" type="monthrange" unlink-panels value-format="YYYY-MM" @change="handleQuery" />
                </el-form-item>
                <el-form-item label="收款公司" prop="receiverCompany">
                    <el-select v-model="queryParams.receiverCompany" clearable placeholder="请选择收款公司" @change="handleQuery">
                        <el-option v-for="item in receiverCompanyOptions" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <!-- 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 16px">
                <el-button icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :tableID="'ReceiptSummary'" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="ReceiptSummary" :max-height="600" ref="ColumnTable" v-loading="loading" v-model:columns="columns" :data="dataList" rowKey="id" :show-summary="true" show-index :maxHeight="tableHeight" :handle-summary-method="handleSummaryMethod">
                <template #receiverCompany="{ row }">
                    <span>{{ row.receiverCompany ? selectDictLabel(receiverCompanyOptions, row.receiverCompany) : '--' }}</span>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" :page-sizes="pageSizes" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import receiptRecords from '@/api/carrierEnd/receiveThePayment/receiptRecords';
import { downloadNoData } from '@/utils';

export default {
    name: 'ReceiptSummary',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar
    },
    data() {
        return {
            fullLoading: false,
            fullLoadingText: '正在导出数据，请稍等...',
            showSearch: true,
            loading: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 50,
                billDate: [],
                receiverCompany: undefined
            },
            // 月度快
            monthShortcuts: [
                {
                    text: '当前月',
                    value: () => {
                        return [new Date(), new Date()];
                    }
                },
                {
                    text: '当前年',
                    value: () => {
                        const end = new Date();
                        const start = new Date(end.getFullYear(), 0, 1);
                        return [start, end];
                    }
                },
                {
                    text: '近半年',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 6);
                        return [start, end];
                    }
                },
                {
                    text: '近一年',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 11);
                        return [start, end];
                    }
                }
            ],
            receiverCompanyOptions: [], // 收款公司
            // 表格列配置
            columns: [
                { title: '时间', key: 'arriveMonth', align: 'center', width: '100px', columnShow: true },
                { title: '回款金额（到款金额）', key: 'arriveAmount', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '认款金额', key: 'claimAmount', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '未认款金额', key: 'stayAmount', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款公司', key: 'receiverCompany', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true }
            ],
            // 数据列表
            dataList: [],
            // 总条数
            total: 0,
            tableHeight: 200,
            pageSizes: ['50', '100', '200', '500'],
            // 统计数据
            averageCollection: 0, // 月均回款-回款金额
            averageAmount: 0, // 月均回款-认款金额
            averageUnclaimedAmount: 0, // 月均回款-未认款金额
            totalCollection: 0, // 统计-回款金额
            totalAmount: 0, // 统计-认款金额
            totalUnclaimedAmount: 0 // 统计-未认款金额
        };
    },
    created() {
        this.getDict();
        this.handleQuery();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            // 收款公司
            this.receiverCompanyOptions = await this.getDictList('signing_company');
        },
        // 获取列表数据
        getList() {
            this.loading = true;
            this.dataList = [];
            const { billDate, ...params } = this.queryParams;
            receiptRecords
                .getMonthPageList(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.tableHeight = window.innerHeight - this.$refs.searchCard.$el.offsetHeight - 320;
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                        this.calculateSummary();
                    }
                })
                .finally((e) => {
                    this.loading = false;
                });
        },
        // 计算统计数据
        calculateSummary() {
            if (this.dataList.length === 0) {
                this.averageCollection = 0;
                this.averageAmount = 0;
                this.averageUnclaimedAmount = 0;
                this.totalCollection = 0;
                this.totalAmount = 0;
                this.totalUnclaimedAmount = 0;
                return;
            }
            // 去除this.dataList中的重复arriveMonth 统计数据的个数
            let uniqueDataList = this.dataList.reduce((acc, item) => {
                if (!acc.some((i) => i.arriveMonth === item.arriveMonth)) {
                    acc.push({ arriveMonth: item.arriveMonth });
                }
                return acc;
            }, []);

            this.totalCollection = this.dataList.reduce((sum, item) => {
                if (!isNaN(item.arriveAmount)) {
                    return (Number(sum) + Number(item.arriveAmount)).toFixed(2);
                } else {
                    return sum;
                }
            }, 0);
            this.totalAmount = this.dataList.reduce((sum, item) => {
                if (!isNaN(item.claimAmount)) {
                    return (Number(sum) + Number(item.claimAmount)).toFixed(2);
                } else {
                    return sum;
                }
            }, 0);
            this.totalUnclaimedAmount = this.dataList.reduce((sum, item) => {
                if (!isNaN(item.stayAmount)) {
                    return (Number(sum) + Number(item.stayAmount)).toFixed(2);
                } else {
                    return sum;
                }
            }, 0);
            this.averageCollection = (this.totalCollection / uniqueDataList.length).toFixed(2);
            this.averageAmount = (this.totalAmount / uniqueDataList.length).toFixed(2);
            this.averageUnclaimedAmount = (this.totalUnclaimedAmount / uniqueDataList.length).toFixed(2);
        },
        /**
         *  重构统计
         * @param param
         */
        handleSummaryMethod(param) {
            const { columns } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = (
                        <p class="count_row">
                            月均
                            <br />
                            <br />
                            <strong>合计</strong>
                        </p>
                    );
                    return;
                }
                switch (column.property) {
                    case 'arriveAmount':
                        sums[index] = (
                            <p class="count_row">
                                {this.averageCollection}
                                <br />
                                <br />
                                <strong>{this.totalCollection}</strong>
                            </p>
                        );
                        break;
                    case 'claimAmount':
                        sums[index] = (
                            <p class="count_row">
                                {this.averageAmount}
                                <br />
                                <br />
                                <strong>{this.totalAmount}</strong>
                            </p>
                        );
                        break;
                    case 'stayAmount':
                        sums[index] = (
                            <p class="count_row">
                                {this.averageUnclaimedAmount}
                                <br />
                                <br />
                                <strong>{this.totalUnclaimedAmount}</strong>
                            </p>
                        );
                        break;
                    default:
                        sums[index] = '--';
                }
            });
            return sums;
        },
        // 搜索按钮操作
        handleQuery() {
            this.queryParams.current = 1;
            const { billDate } = this.queryParams;
            // 验证queryTime是否包含两个合法的日期字符串
            const isValidBillDate = billDate && billDate.length === 2 && !billDate.some((date) => date === 'Invalid Date');

            if (isValidBillDate) {
                this.queryParams.startMonth = billDate[0];
                this.queryParams.endMonth = billDate[1];
            } else {
                this.queryParams.startMonth = undefined;
                this.queryParams.endMonth = undefined;
            }
            this.getList();
        },
        // 重置按钮操作
        resetQuery() {
            this.resetForm('queryForm');
            this.queryParams = {
                current: 1,
                size: 50,
                billDate: [],
                receiverCompany: undefined
            };
            this.handleQuery();
        },
        // 导出按钮操作
        handleExport() {
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.fullLoading = true;
                    // eslint-disable-next-line no-unused-vars
                    const { billDate, size, current, ...params } = this.queryParams;
                    receiptRecords
                        .exportMonthList({ filename: '认款汇总.xls', ...params }, '', '', 'blob')
                        .then((res) => {
                            downloadNoData(res, 'application/vnd.ms-excel', '认款汇总.xlsx');
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.fullLoading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.fullLoading = false;
                });
        }
    }
};
</script>

<style scoped>
.mb10 {
    margin-bottom: 10px;
}
</style>
