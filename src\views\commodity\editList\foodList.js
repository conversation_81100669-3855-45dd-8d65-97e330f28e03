const {disappApi} = require("@/api/model/commodity/disappear");
const {foodApi} = require("@/api/model/commodity/food");

function editList(row) {
    let formInline = {
        n17: row.commodityCode,
        n3: row.pinyinCode,
        n2: row.tradeName,
        n1: row.commonName,
        n9: row.dosageForm,
        n10: row.packageSpecification,
        n8: row.validityTime,
        n13: row.originPlace,
        n15: row.producingArea,
        n11: row.listPermitHolder,
        n5: row.basicUnit,
        n14: row.businessScope,
        n16: row.remark,
        n26: row.commoditySelfCode.substr(0, 2),
        n4: row.commoditySelfCode.substr(2),
        n6: row.completeUnit,
        n7: row.ratio,
    }
    let formInline2 = {
        n26: row.importMark == "1" ? true : false,
        n16: row.grugsType,
        n1: row.gspAttribute,
        n6: row.qualityStandard,
        n13: row.specialMedicineControl == "1" ? true : false,
        n12: row.premiereVariety == "1" ? true : false,
        n7: row.curingType,
        n8: row.storageTemperature,
        n9: row.storageRange,
        n10: row.transportTemperature,
        n11: row.transportTemperatureRange,
        n14: row.specialTemperature == "1" ? true : false,
        n23: row.electronicSupervision == "1" ? true : false,
        n22: row.onlyCode == "1" ? true : false,
        n5: row.registrationNo,
        n2: row.approvalNumber,
        n3: row.approvalValidity,
        n4: row.proscriptionType,
    }
    let formInline3 = {
        n1: row.taxRate,
        n2: row.taxClassifyCode,
        n3: row.treatmentNumber,
        n4: row.medicationClassify,
        n5: row.usageDays,
        n6: row.medicalInsuranceClassify,
        n7: row.usageDosage,
        n8: row.taboo,
        n9: row.mainComponents,
        n10: row.specialStorage,
        n11: row.functionalIndications,
        n12: row.adaptiveSymptoms,
        n13: row.essentialMedicines == "1" ? true : false,
        n14: row.registeredTrademark == "1" ? true : false,
        n15: row.memberPoint == "1" ? true : false,
        n17: row.preciousMedicines == "1" ? true : false,
        n18: row.onlineSales == "1" ? true : false,
        n19: row.monopolizeGoods == "1" ? true : false,
        n20: row.isFragile == "1" ? true : false,
        n16: row.priceProtection == "1" ? true : false,
    }

    return {
        formInline, formInline2, formInline3
    }
}

const sendApi = (formInline, formInline2, formInline3, formInline4, status, editStr, state, fileArr, source) => {
    return foodApi
        .saveDrug({
            erpFoodCommodityDTO: {
                id: state ? editStr?.erpFoodCommodityDTO.id : null,
                commodity: {
                    manufacture: {
                        id: formInline.n12,
                    },
                    source: source ? '2' : null,
                    id: state
                        ? editStr?.erpFoodCommodityDTO.commodity.id
                        : null,
                    commodityCode: formInline.n17,
                    pinyinCode: formInline.n3,
                    grugsType: formInline2.n16,
                    onlyCode: formInline2.n22 ? "1" : "0",
                    chemicalName: "", //TODO
                    tradeName: formInline.n2,
                    commonName: formInline.n1,
                    dosageForm: formInline.n9,
                    packageSpecification: formInline.n10,
                    validityTime: formInline.n8,
                    originPlace: formInline.n13,
                    producingArea: formInline.n15,
                    listPermitHolder: formInline.n11,
                    basicUnit: formInline.n5,
                    gspAttribute: formInline2.n1,
                    businessScope: formInline.n14,
                    qualityStandard: formInline2.n6,
                    specialMedicineControl: formInline2.n13 ? "1" : "0",
                    premiereVariety: formInline2.n12 ? "1" : "0",
                    curingType: formInline2.n7,
                    storageTemperature: formInline2.n8,
                    storageRange: formInline2.n9,
                    transportTemperature: formInline2.n10,
                    transportTemperatureRange: formInline2.n11,
                    specialTemperature: formInline2.n14 ? "1" : "0",
                    electronicSupervision: formInline2.n23 ? "1" : "0",
                    remark: formInline.n16,
                    commoditySelfCode: formInline.n26 + formInline.n4,
                    completeUnit: formInline.n6,
                    ratio: formInline.n7,
                    taxRate: formInline3.n1,
                    taxClassifyCode: formInline3.n2,
                    treatmentNumber: formInline3.n3,
                    medicationClassify: formInline3.n4,
                    usageDays: formInline3.n5,
                    medicalInsuranceClassify: formInline3.n6,
                    usageDosage: formInline3.n7,
                    taboo: formInline3.n8,
                    mainComponents: formInline3.n9,
                    specialStorage: formInline3.n10,
                    functionalIndications: formInline3.n11,
                    adaptiveSymptoms: formInline3.n12,
                    essentialMedicines: formInline3.n13 ? "1" : "0",
                    registeredTrademark: formInline3.n14 ? "1" : "0",
                    memberPoint: formInline3.n15 ? "1" : "0",
                    preciousMedicines: formInline3.n17 ? "1" : "0",
                    onlineSales: formInline3.n18 ? "1" : "0",
                    monopolizeGoods: formInline3.n19 ? "1" : "0",
                    isFragile: formInline3.n20 ? "1" : "0",
                    priceProtection: formInline3.n16 ? "1" : "0",
                    registrationNo: formInline2.n5,
                    registrationValidity: "", //TODO
                    approvalNumber: formInline2.n2,
                    approvalValidity: formInline2.n3,
                    importMark: formInline2.n26 ? "1" : "0",
                },
                proscriptionType: formInline2.n4,
                registerLicense: formInline2.n24,
                registerLicenseDate: formInline2.n25,
            },

            annexFileDTOS: fileArr,
            operate: status,
        })
}


module.exports = {
    editList,
    sendApi
}

