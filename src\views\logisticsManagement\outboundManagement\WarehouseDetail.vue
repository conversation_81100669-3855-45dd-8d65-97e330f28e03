<template>

<div class="app-container" style="background-color: rgb(245, 247, 253); padding: 10px;" v-loading="loading">
  <el-card class="mb10" shadow="never" style="padding-top: 20px">
    <div class="d-box-column">
      <div class="d-box-info" v-if="info.company">
        <span class="box-info-l">货主公司</span>
        <span class="box-info-r">{{ info.company.name }}</span>
      </div>
      <div class="d-box-info">
        <span class="box-info-l">出库日期</span>
        <span class="box-info-r">{{ info.outDate.substr(0,10) }}</span>
      </div>
      <div class="d-box-info" v-if="info.createBy">
        <span class="box-info-l">交接人</span>
        <span class="box-info-r">{{ info.createBy.name }}</span>
      </div>
      <div class="d-box-info" v-if="info.signFile" >
        <span class="box-info-l">提货人签名</span>
        <div class="box-info-r">
          <div class="img-list">
            <div class="img" @click="handlePictureCardPreview([info.signFile])">
              <img :src="info.signFile" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
  <el-collapse v-model="activeName" v-if="detailList && detailList.length > 0">
    <el-collapse-item  :name="index" v-for="(item, index) in detailList" :key="index" class="mb10">
      <template #title>
        <card-header :title="`出库信息（${index+1}）`" size="mini" :line="false" class="ml10"/>
      </template>
      <div class="border-bottom-1"></div>
      <div class="p10">
        <div class="d-box-column">
          <div class="d-box-info" v-if="item.receiveCompany">
            <span class="box-info-l">收货客户</span>
            <span class="box-info-r">{{ item.receiveCompany.company}}</span>
          </div>
          <div class="d-box-info">
            <span class="box-info-l">件数</span>
            <span class="box-info-r">{{ item.goodsNum }}</span>
          </div>
          <div class="d-box-info">
            <span class="box-info-l">温层</span>
            <span class="box-info-r">{{ temperatureTypeFormat(item) }}</span>
          </div>
          <div class="d-box-info" v-if="item.outCodeList && item.outCodeList.length > 0">
            <span class="box-info-l">出库单号</span>
            <div class="box-info-r">
              <el-tag v-for="(code, i) in item.outCodeList" style="margin: 0 5px 5px 0;width: auto;color: #5670fe;">{{code.outCode}}</el-tag>
            </div>
          </div>
          <div class="d-box-info" v-if="item.file && item.file.length > 0">
            <span class="box-info-l">货物照片</span>
            <div class="box-info-r">
              <div class="img-list">
                <div v-for="(img, i) in item.file" class="img" @click="handlePictureCardPreview(item.file.map(f=>f.fileUrl),i)">
                  <img :src="img.fileUrl" />
                </div>
              </div>
            </div>
          </div>
          <div class="d-box-info"  v-if="item.outboundFile && item.outboundFile.length > 0">
            <span class="box-info-l">冷链交接单明细</span>
            <div class="box-info-r">
              <div class="img-list">
                <div v-for="(img, i) in item.outboundFile" class="img" @click="handlePictureCardPreview(item.outboundFile.map(f=>f.fileUrl),i)">
                  <img :src="img.fileUrl" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-collapse-item>
  </el-collapse>
<!--  图片查看-->
  <el-image-viewer v-if="dialogVisible" :initial-index="initialIndex" @close="dialogVisible = false" :url-list="dialogImageUrl"
                   :z-index="9999"/>
</div>

</template>

<script>
import CardHeader from '@/components/CardHeader';
import outboundManagement from '@/api/logisticsManagement/outboundManagement.js'; // 出库登记
export default {
  name: "WarehouseDetail",
  props: {
    recordInfo: {
      required: false,
      type: Object
    }
  },
  components: {
    CardHeader,
  },
  data(){
    return {
      activeName:0,
      info: {},
      detailList:[],
      dialogImageUrl: [], // 预览图片地址
      initialIndex:0,
      dialogVisible: false,// 预览图片显示
      temperatureTypeDicts:[], //温层
    };
  },
  async created() {
    /** 产品类型4PL */
    this.temperatureTypeDicts = await this.getDictList('fourpl_out_wh_temp')
    this.getDetail();
  },
  methods:{
    /** 状态字典转换 */
    temperatureTypeFormat(val) {
      return this.selectDictLabel(this.temperatureTypeDicts, val.tempType);
    },
    // 放大图片
    handlePictureCardPreview(imgUrl,index=0) {
      this.dialogImageUrl = imgUrl;
      this.initialIndex = index;
      this.dialogVisible = true
    },
    getDetail() {
      this.loading = true
      outboundManagement.findRecordDetail({
        recordId: this.recordInfo.id
      }).then((response) => {
        if (response.code === 200 && response.data) {
          this.info = response.data.record;
          this.detailList = response.data.detailList.map(item=>{
            item.file = JSON.parse(item.file);
            item.outboundFile = JSON.parse(item.outboundFile);
            return item;
          });
        }
        this.loading = false
      }).catch(e => {
        this.loading = false
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-collapse-item__header,.el-collapse-item__wrap,.el-collapse{
    border: 0;
  }
  .el-card__body{
    padding-top:0;
  }
  .d-box-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    font-size: 14px;

    .d-box-info {
      display: flex;
      //justify-content: space-between;

      .box-info-l:nth-child(1) {
        color: #999999;
        width: 100px;
        flex-shrink: 0;
        flex-grow: 0;
      }
      .box-info-r:nth-child(2) {
        color: #333333;
        width:calc(100% - 100px);
        .img-list {
          display: flex;
          flex-direction: row;
          flex-flow: wrap;
          width:100%;
          .img {
            width: 128px;
            height: 128px;
            border: 1px dashed #d9d9d9;
            margin-top: 0;
            margin-left: 10px;
            margin-bottom: 10px;
            img {
              width: 108px;
              height: 108px;
              margin: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
