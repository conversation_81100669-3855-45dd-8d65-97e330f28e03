import request from '@/utils/request';
export default {
    // 列表
	list: function (params) {
        return request.get('/cost/exceedCounty/priceBook/list', params);
    },
	// 添加价格本
	add: function (params) {
        return request.post('/cost/exceedCounty/priceBook/save', params);
    },
	// 修改激活状态
	updateStatus: function (params) {
        return request.get('/cost/exceedCounty/priceBook/updateStatus', params);
    },
    // 修改发布状态
    updatePublishStatus: function (params) {
        return request.get('/cost/exceedCounty/priceBook/updatePublishStatus', params);
    },
	// 删除价格本
	delete: function (params) {
        return request.delete('/cost/exceedCounty/priceBook/delete', params);
    },
    // 添加价格本明细
    addDetail: function (params) {
        return request.post('/cost/exceedCounty/priceBook/data/batchSave', params);
    },
    // 校验超区价格本是否重复
    checkRepeat: function (params) {
        return request.post('/cost/exceedCounty/priceBook/data/checkRepet', params);
    },
    // 查看价格本
    getPriceBookList: function (params) {
        return request.get('/cost/exceedCounty/priceBook/data/list', params);
    },
    // 批量修改价格本明细
    batchUpdatePriceBook: function (params) {
        return request.post('/cost/exceedCounty/priceBook/data/batchUpdate', params);
    },
	// 下载导入超区价格本内容数据模板
	downloadTemplate: function (params, config, resDetail, responseType) {
        return request.get('/cost/exceedCounty/priceBook/data/import/template', params, config, resDetail, responseType);
    },
	// 价格本明细删除
	deleteDetail: function (params) {
		return request.delete('/cost/exceedCounty/priceBook/data/delete', params);
	}
};
