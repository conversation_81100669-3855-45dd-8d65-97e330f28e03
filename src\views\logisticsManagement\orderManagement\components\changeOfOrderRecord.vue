<template>
  <div v-loading="loading" v-loading.fullscreen.lock="fullscreenLoading" class=""
    element-loading-text="加载中" style="background-color: rgb(245, 247, 253); padding: 10px">
    <el-card class="mb20 box-mb5" shadow="never" style="overflow: inherit">
      <CardHeader title="异动记录" />
      <div>
        <div class="block">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in list" :key="index" :timestamp="item.createTime" placement="top">
              <el-card shadow="never">
                <el-form ref="form" :model="form" label-width="100px">
                  <el-form-item label="修改类型">
                    <span>{{ fourplFormat(fourplOrderChangeTypeOptions, item.type) }}</span>
                  </el-form-item>
                  <el-form-item label="修改前">
                    <div v-for="(v, i) in item.changeDetails" :key="i">
                      <el-form-item v-if="v.name" :label="`${i + 1}、${v.name}：`" class="childlabel" label-width="160px">
                        <span v-if="v.properties == 'transportMethod'">{{ fourplFormat(typeOfShipping, v.oldValue)
                        }}</span>
                        <span v-else-if="v.properties == 'orderType'">{{ fourplFormat(shippingMethod, v.oldValue)
                        }}</span>
                        <span v-else-if="v.properties == 'handleMethod'">{{ fourplFormat(deliveryMethod, v.oldValue)
                        }}</span>
                        <span v-else-if="v.properties == 'paymentMethod'">{{ fourplFormat(paymentMethod, v.oldValue)
                        }}</span>
                        <span v-else-if="v.properties == 'insuredPriceRate'">{{ fourplFormat(insuranceRate, v.oldValue)
                        }}</span>
                        <span v-else-if="v.properties == 'goodPackingType'">{{ fourplFormat(pack, v.oldValue) }}</span>
                        <span v-else-if="v.properties == 'companyId'">{{ filterArray(ownerOfCargo, v.oldValue,
                          'companyId')
                        }}</span>
                        <span v-else-if="v.properties == 'receiverWebsite'">{{
                          filterArray(destinationOutlet, v.oldValue, 'value') }}</span>
                        <span v-else-if="v.properties == 'sendWebsite'">{{ filterArray(deliveryOutlet, v.oldValue,
                          'value')
                        }}</span>
                        <span v-else-if="v.properties == 'sendProvinceId'">{{ v.oldValueDesc }}</span>
                        <span v-else-if="v.properties == 'sendCityId'">{{ v.oldValueDesc }}</span>
                        <span v-else-if="v.properties == 'sendCountyId'">{{ v.oldValueDesc }}</span>
                        <span v-else-if="v.properties == 'sendTownId'">{{ v.oldValueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverProvinceId'">{{ v.oldValueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverCityId'">{{ v.oldValueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverCountyId'">{{ v.oldValueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverTownId'">{{ v.oldValueDesc }}</span>
                        <span v-else>{{ v.oldValue }}</span>
                      </el-form-item>
                    </div>
                  </el-form-item>
                  <el-form-item label="修改后">
                    <div v-for="(v, i) in item.changeDetails" :key="i">
                      <el-form-item v-if="v.name" :label="`${i + 1}、${v.name}：`" class="childlabel updated"
                        label-width="160px">
                        <span v-if="v.properties == 'transportMethod'">{{ fourplFormat(typeOfShipping, v.value) }}</span>
                        <span v-else-if="v.properties == 'orderType'">{{ fourplFormat(shippingMethod, v.value) }}</span>
                        <span v-else-if="v.properties == 'handleMethod'">{{ fourplFormat(deliveryMethod, v.value)
                        }}</span>
                        <span v-else-if="v.properties == 'paymentMethod'">{{ fourplFormat(paymentMethod, v.value)
                        }}</span>
                        <span v-else-if="v.properties == 'insuredPriceRate'">{{ fourplFormat(insuranceRate, v.value)
                        }}</span>
                        <span v-else-if="v.properties == 'goodPackingType'">{{ fourplFormat(pack, v.value) }}</span>
                        <span v-else-if="v.properties == 'companyId'">{{ fourplFormat(ownerOfCargo, v.value) }}</span>
                        <span v-else-if="v.properties == 'receiverWebsite'">{{ filterArray(destinationOutlet, v.value,
                          'value')
                        }}</span>
                        <span v-else-if="v.properties == 'sendWebsite'">{{ filterArray(deliveryOutlet, v.value,
                          'value')
                        }}</span>
                        <span v-else-if="v.properties == 'sendProvinceId'">{{ v.valueDesc }}</span>
                        <span v-else-if="v.properties == 'sendCityId'">{{ v.valueDesc }}</span>
                        <span v-else-if="v.properties == 'sendCountyId'">{{ v.valueDesc }}</span>
                        <span v-else-if="v.properties == 'sendTownId'">{{ v.valueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverProvinceId'">{{ v.valueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverCityId'">{{ v.valueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverCountyId'">{{ v.valueDesc }}</span>
                        <span v-else-if="v.properties == 'receiverTownId'">{{ v.valueDesc }}</span>
                        <span v-else>{{ v.value }}</span>
                      </el-form-item>
                    </div>
                  </el-form-item>
                  <el-form-item label="异动备注">
                    {{ item.description }}
                  </el-form-item>
                  <el-form-item label="照片">
                    <el-upload :file-list="item.imgList" :on-preview="handlePictureCardPreview" disabled
                      list-type="picture-card">
                      <i class="el-icon-plus"></i>
                    </el-upload>
                  </el-form-item>
                  <el-form-item v-if="type === 'view' && index === 0 && item.auditInfo" label="审批结果">
                    <span style="color:red">{{ fourplFormat(approveStatusList, item.auditInfo.status) || '--' }}</span>
                  </el-form-item>
                  <el-form-item v-if="type === 'view' && index === 0 && item.auditInfo" label="审批说明">
                    <span>{{ item.auditInfo.remark }}</span>
                  </el-form-item>
                  <el-form-item v-if="type === 'jobApproval' && index === 0">
                    <el-button style="width: 100px;" type="warning" @click="approvalVisible = true">审批</el-button>
                  </el-form-item>
                </el-form>

              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-card>
    <el-dialog :modal="false" :modal-append-to-body="false" :visible.sync="dialogVisible"
      @close="() => this.dialogVisible == false">
      <viewer v-if="dialogVisible" :options="options">
        <img :src="dialogImageUrl" alt="" width="100%" />
      </viewer>
    </el-dialog>
    <el-dialog v-if="type === 'jobApproval'" :modal="false" :visible.sync="approvalVisible" title="审批" width="30%"
      @close="() => this.approvalVisible = false">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="审核状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="说明" style="margin-top: 30px;">
          <el-input v-model="form.remark" placeholder="请输入" style="width: 80%;" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { getOrderChangeDetails } from '@/api/fourpl/changeOfOrder';
// import { audit } from '@/api/fourpl/terminateOrder';
import { formatDate } from '@/utils/index';
import CardHeader from '@/components/CardHeader'
// import { getCompanySelect } from '@/api/fourpl/lan';
// import { filterArray } from '@/utils/index'
// import { getOwnerInfo, getAddressByPhone, placeOrder, getCarrierWebsites, getLoginUserBranchList, getOrderCost, cost_calculation, getBranchNameByCode, boxCodeEdits, getCodePageByOrderId, getDetail } from '@/api/fourpl/departmentStoreOrder'
import moment from 'moment';
export default {
  name: 'changeOfOrderRecord',
  props: {
    orderId: {
      required: true,
      type: String
    },
    list: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  components: {
    CardHeader
  },
  data() {
    return {
      form: {},
      approvalVisible: false,
      list: [], // 异动记录数据
      productTypeDicts: [], // 产品类型
      productAttributeDictionary: [], // 商品属性
      fourplPaymentMethodOptions: [], // 付款方式
      fourplOrderChangeTypeOptions: [], // 异动类型
      temperatureTypeDicts: [], // 温层类型
      typeOptions: [], //车辆类型输
      addedServiceDicts: [], // 增值服务
      dialogImageUrl: '',
      dialogVisible: false,
      options: {
        zIndex: 1
      },
      loading: false,
      fourplProductClassDicts: [],
      typeOfShipping: [],
      shippingMethod: [],
      deliveryMethod: [],
      paymentMethod: [],
      insuranceRate: [],
      pack: [],
      fourplOrderChangeTypeOptions: [],
      ownerOfCargo: [],
      destinationOutlet: [],
      deliveryOutlet: [],
      approveStatusList: [],
      rules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ],
      },
      fullscreenLoading: false,
      // filterArray:null
    };
  },
  created() {
    // this.filterArray = filterArray
    const dictArray = {
      typeOfShipping: 'fourpl_general_order_transport_method',
      shippingMethod: 'fourpl_mail_service',
      deliveryMethod: 'fourpl_general_order_handle_method',
      paymentMethod: 'fourpl_payment_method',
      insuranceRate: 'fourpl_partner_insured_price_rate',
      pack: 'fourpl_general_order_good_packing_type',
      fourplOrderChangeTypeOptions: 'fourpl_order_change_type',
      approveStatusList: 'fourpl_approve_status'
    }
    for (let key in dictArray) {
      this.getDicts(dictArray[key]).then((res) => (this[key] = res.data));
    }
    this.getCompanySelect()
    this.getCarrierWebsites()
    this.getLoginUserBranchList()
    this.loading = true;
    getOrderChangeDetails(this.orderId)
      .then((res) => {
        if (res.code === 200) {
          this.list = res.data.map((item, index) => {
            let obj = { ...item, imgList: [] };
            obj.createTime = moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')
            if (item.imgUrls && item.imgUrls.indexOf(';') > -1) {
              obj.imgList = item.imgUrls.split(';');
              let objImg = []
              obj.imgList.forEach(v => {
                return objImg.push({ 'url': v })
              })
              obj.imgList = objImg
            } else {
              let objImg = []
              item.imgUrls && objImg.push({ 'url': item.imgUrls })
              obj.imgList = objImg
            }
            return obj;
          });
          this.loading = false;
          console.log(this.loading);
        }
      })
      .catch((e) => {
        this.loading = false;
      });
  },
  methods: {
    filterArray(orgArr, judgeValue, orgItemKey) {
      if (typeof orgArr !== 'object') return
      return orgArr.filter(item => item[orgItemKey] == judgeValue)[0]?.label || '--'
    },
    handleSubmit() {
      this.fullscreenLoading = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          audit({ ...this.form, id: this.id }).then(res => {
            if (res.code == 200) {
              this.fullscreenLoading = false
              this.approvalVisible = false
              this.cancel()
              this.msgSuccess('提交成功')
            }
          })

        } else {

          return false;
        }
      });
    },
    // 获取货主下拉
    getCompanySelect() {
      getCompanySelect({ type: '2' }).then((response) => {
        this.ownerOfCargo = response.data;
      });
    },
    getCarrierWebsites() {
      getCarrierWebsites().then((res) => {
        this.destinationOutlet = res.data
        let n = res.data.filter(v => v.value === '**********')
        console.log(n);
      })
    },
    // 根据登录信息获取发件网点
    getLoginUserBranchList() {
      getLoginUserBranchList().then((res) => {
        if (res.data?.length) {
          this.deliveryOutlet = res.data
        }
      })
    },
    // 放大图片
    handlePictureCardPreview(imgUrl) {
      this.dialogImageUrl = imgUrl.url;
      this.dialogVisible = true;
    },
    // 增值服务格式化
    fourplAddedServices(vals) {
      let arr = [];
      if (Array.isArray(vals)) {
        if (vals.length > 0) {
          vals.forEach(item => {
            let text = item.serviceName;
            if (item.content) {
              text += ':' + item.content
            }
            let index = this.addedServiceDicts.findIndex((a) => a.id == item.type);
            if (index > -1 && item.content) {
              text += this.addedServiceDicts[index].unit
            }
            arr.push(text);
          })
        }
      }
      return arr.join(',')
    },
    // 字符串转对象
    stringToObject(data) {
      return JSON.parse(data);
    },
    // 多选字典翻译
    getDictVals(data, vals, sep = ',') {
      var actions = [];
      if (vals.indexOf(sep) > -1) {
        let arr = vals.split(sep);
        arr.forEach((item) => {
          let index = data.findIndex((t) => t.dictValue == '' + item);
          if (index > -1) {
            actions.push(data[index].dictLabel);
          }
        });
      } else {
        let index = data.findIndex((t) => t.dictValue == '' + vals);
        if (index > -1) {
          actions.push(data[index].dictLabel);
        }
      }
      return actions.join(',');
    },
    /** 4PL字典转换 */
    fourplFormat(data, val) {
      return this.selectDictLabel(data, val);
    },
    // 关闭弹窗
    cancel() {
      this.$emit('callbackMethod');
    },
    // 标准时间格式化
    formatDate(cellValue) {
      return formatDate(cellValue);
    },
  }
};
</script>

<style lang="scss" scoped>
.titleLayout {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .verticalBar {
    display: inline-block;
    background-color: #5670fe;
    width: 3px;
    height: 1em;
    margin-right: 8px;
  }

  .title {
    color: #5670fe;
  }
}

.item-line {
  margin-bottom: 10px;
  display: flex;
  align-items: center;

  .item-label {
    color: #aaaaaa;
    width: 80px;
    text-align: right;
    margin-right: 15px;
    flex-shrink: 0;
    flex-wrap: 0;
  }

  .item-content {
    color: #333333;

    .img-list {
      display: flex;

      .img {
        width: 128px;
        height: 128px;
        border: 1px dashed #d9d9d9;
        margin-top: 0;
        margin-left: 10px;

        img {
          width: 108px;
          height: 108px;
          margin: 10px;
        }
      }
    }
  }
}

::v-deep .el-timeline-item__node--normal {
  border: 2px solid #1989fa;
  background-color: #fff;
}

::v-deep .el-form-item {
  margin: 0;
  text-align: left;

  .el-form-item__label {
    color: #999;
  }

  .el-form-item__content {
    font-size: 12px;
    color: #333;
    padding-left: 20px;
  }
}

::v-deep .el-upload--picture-card {
  display: none;
}

.childlabel {

  ::v-deep .el-form-item__label {
    color: #333;
    text-align: left;
  }

  ::v-deep .el-form-item__label-wrap {
    margin-left: 0;
  }

  ::v-deep .el-form-item__content {
    display: flex;
    align-items: center;
    padding-left: 0;
  }
}

.updated {
  ::v-deep .el-form-item__content {
    color: red;
  }
}</style>
