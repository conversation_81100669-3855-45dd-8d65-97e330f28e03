<template>
    <div class="the-file-upload">
        <el-upload
            ref="fileUpload"
            :accept="accept"
            :action="uploadFileUrl"
            :before-upload="handleBeforeUpload"
            :class="{ 'el-upload__display-none': hideUploadButton }"
            :disabled="disabled"
            :file-list="fileList"
            :headers="headers"
            :limit="limit"
            :list-type="listType"
            :multiple="multiple"
            :on-error="handleUploadError"
            :on-exceed="handleExceed"
            :on-success="handleUploadSuccess"
        >
            <template v-if="!disabled || fileList.length === limit">
                <el-icon v-if="(listType === 'text' && fileList.length === 0) || listType === 'picture-card'"><el-icon-plus /></el-icon>
                <el-button v-else-if="listType === 'list'" type="primary">上传文件</el-button>
            </template>
            <!-- 上传提示 -->
            <div v-if="showTip" class="el-upload__tip" name="tip">
                请上传
                <template v-if="fileSize">
                    大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
                </template>
                的文件
            </div>
            <template #file="{ file }">
                <div class="el-upload-list__item-main">
                    <img v-if="getFileType(file) === 'pdf'" class="el-upload-list__item-thumbnail" src="/img/pdf.png" />
                    <img v-else-if="getFileType(file) === 'image'" :src="file.url" class="el-upload-list__item-thumbnail" />
                    <img v-else :src="file.url" class="el-upload-list__item-thumbnail" />
                    <span v-if="actions?.length > 0" class="el-upload-list__item-actions">
                        <span v-if="actions.includes('preview')" class="el-upload-list__item-preview" @click="handlePreview(file)">
                            <el-icon><el-icon-zoom-in /></el-icon>
                        </span>
                        <span v-if="actions.includes('remove')" class="el-upload-list__item-delete" @click="handleDelete(file)">
                            <el-icon><el-icon-delete /></el-icon>
                        </span>
                        <span v-if="actions.includes('download')" class="el-upload-list__item-download" @click="handleDownload(file)">
                            <el-icon><el-icon-download /></el-icon>
                        </span>
                    </span>
                </div>
            </template>
        </el-upload>
        <component :is="initComponent" v-if="dialogVisible" :current="initialIndex" :url-list="fileUrlList" @close="dialogVisible = false"></component>
    </div>
</template>
<script>
import { ElLoading } from 'element-plus';
import transportationRecordsPage from '@/api/carrierEnd/transportationRecordsPage';
import { downloadNoData } from '@/utils';
export default {
    name: 'TheFileUpload',
    props: {
        // 值
        value: [String, Object, Array],
        // 数量限制
        limit: {
            type: Number,
            default: 9
        },
        // 大小限制(MB)，0为不限制
        fileSize: {
            type: Number,
            default: 0
        },
        // 文件类型,image/*,pdf,目前只支持这两种类型，如有需要请自行添加相关逻辑
        accept: {
            type: String,
            default: 'image/*'
        },
        // 是否显示提示
        isShowTip: {
            type: Boolean,
            default: true
        },
        fileListShow: {
            type: Boolean,
            default: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        /**
         * 是否支持多选文件
         */
        multiple: {
            type: Boolean,
            default: false
        },
        listType: {
            // picture-card-照片墙
            type: String,
            default: 'picture-card'
        },
        actions: {
            type: Array,
            default: () => ['preview', 'remove', 'download']
        },
        data: {
            type: Object,
            default: {}
        },
    },
    data() {
        return {
            number: 0,
            headers: {
                Authorization: 'Bearer ' + this.$TOOL.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            fileList: [], // 文件列表
            uploadList: [],
            loading: null,
            dialogVisible: false,
            fileUrlList: [], // 图片预览地址
            initialIndex: 0,
            initComponent:null,
            uploadData: JSON.parse(JSON.stringify(this.data))
        };
    },
    watch: {
        value: {
            handler(val) {
                if (val) {
                    // 首先将值转为数组
                    const list = Array.isArray(val) ? val : this.value.split(',');
                    // 然后将数组转为对象数组
                    this.fileList = list.map((item) => {
                        if (typeof item === 'string') {
                            item = { name: this.getFileName(item), url: item };
                        }
                        item.name = item.name ? item.name : this.getFileName(item.url);
                        return item;
                    });
                } else {
                    this.fileList = [];
                    return [];
                }
            },
            deep: true,
            immediate: true
        }
    },
    computed: {
        // 是否显示提示
        showTip() {
            return this.isShowTip && this.fileSize;
        },
        /**
         * 是否隐藏上传按钮
         * @returns {boolean}
         */
        hideUploadButton() {
            return this.fileList.length >= this.limit;
        }
    },
    methods: {
        /**
         *  上传前校检格式和大小
         * @param file
         * @returns {boolean}
         */
        handleBeforeUpload(file) {
            // 校检文件大小
            if (this.fileSize) {
                const isLt = file.size / 1024 / 1024 < this.fileSize;
                if (!isLt) {
                    this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
                    return false;
                }
            }
            this.loading = ElLoading.service({
                lock: true,
                text: '正在上传文件，请稍候...',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            this.number++;
            return true;
        },
        /**
         * 文件个数超出
         */
        handleExceed() {
            this.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
        },
        /**
         *  上传失败
         */
        handleUploadError() {
            this.msgError('上传文件失败，请重试');
            this.loading.close();
        },

        /**
         * 上传成功回调
         * @param res
         * @param file
         */
        handleUploadSuccess(res, file) {
            if (res.code === 200) {
                if(this.uploadData?.fileName){
                    this.uploadList.push({ name: this.uploadData.fileName + '_' + (this.fileList.length + 1)+`.${res.data.fileType}`, url: res.data.fileUrl, extension: res.data.fileType });
                }else{
                    this.uploadList.push({ name: res.data.fileName, url: res.data.fileUrl, extension: res.data.fileType });
                }
                this.uploadedSuccessfully();
            } else {
                this.number--;
                this.loading.close();
                this.msgError(res.msg);
                this.$refs.fileUpload.handleRemove(file);
                this.uploadedSuccessfully();
            }
        },

        /**
         * 上传结束处理
         */
        uploadedSuccessfully() {
            if (this.number > 0 && this.uploadList.length === this.number) {
                this.fileList = this.fileList.concat(this.uploadList);
                this.uploadList = [];
                this.number = 0;
                this.$emit('input', this.fileList);
                this.loading.close();
            }
        },

        /**
         * 获取文件名称
         * @param name
         * @returns {*}
         */
        getFileName(name) {
            // 如果是url那么取最后的名字 如果不是直接返回
            if (name.lastIndexOf('/') > -1) {
                return name.slice(name.lastIndexOf('/') + 1);
            } else {
                return name;
            }
        },
        /**
         * 获取文件类型
         * @param file
         * @returns {string}
         */
        getFileType(file) {
            const fileUrl = file.response?.data?.fileUrl || file.url;
            if (fileUrl && fileUrl.includes('.pdf')) {
                return 'pdf';
            }
            if (fileUrl && (fileUrl.includes('.png') || fileUrl.includes('.jpg'))) {
                return 'image';
            }
            return 'unknown';
        },
        // 删除文件
        handleDelete(file) {
            let index = this.fileList.findIndex((item) => item.url === file.url);
            this.fileList.splice(index, 1);
            this.$emit('input', this.fileList);
        },
        /**
         * 文件预览
         * @param index
         */
        handlePreview(file) {
            import('@/components/thePreviewDialog/ThePreviewDialog.vue').then((module) => {
                this.initComponent = module.default;
                this.dialogVisible = true;
                this.fileUrlList = this.fileList.map((item) => item.url);
                this.initialIndex = this.fileUrlList.findIndex((item) => item === file.url);
            });
        },
        handleDownload(file) {
            console.log(file);
            transportationRecordsPage.getImageFile({fileUrl: file.url}, '', '', 'blob').then((res) => {
                downloadNoData(res, 'application/octet-stream', file.name);
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-upload__display-none {
        .el-upload--picture-card {
            display: none;
        }
    }
}
</style>
