<!--  传感器设置 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto">
                    <el-form-item label="设备编号" prop="deviceNo">
                        <el-input v-model="queryParams.deviceNo" placeholder="请输入设备编号" clearable/>
                    </el-form-item>
                    <el-form-item label="打印时间" prop="deviceTermial" style="width: 305px">
                        <el-date-picker
                            v-model="queryParams.deviceTermial"
                            :clearable="false"
                            date-format="YYYY-MM-DD HH:mm:ss"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD HH:mm:ss"
                            range-separator="至"
                            start-placeholder="开始日期"
                            type="datetimerange"
                            value-format="YYYY-MM-DD HH:mm:ss"
							clearable />
                    </el-form-item>
                    <el-form-item label="操作人" prop="operateBy">
                        <el-input v-model="queryParams.operateBy" placeholder="请输入操作人" clearable/>
                    </el-form-item>
				<search-button :is-show-all-switch="false" @handleQuery="getList(1)" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- <el-card class="box-card Botm">
                <div class="leftCenter">
                    <div class="title">
                        <span class="txt">温度折线图</span>
                    </div>
                    <div class="line" ref="chartRef"></div>
                </div>
                            </el-card> -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: flex-end; align-items: center">
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table v-loading="loading" :data="abnormalprintList" border style="margin-top: 15px">
                <el-table-column :index="idxMethod" align="center" label="序号" type="index" width="55" />
                <el-table-column align="center" label="设备编号" prop="deviceNo" width="160" />
                <el-table-column align="center" label="打印开始时间" min-width="150" prop="printStartTime" sortable />
                <el-table-column align="center" label="打印结束时间" min-width="150" prop="printEndTime" sortable />
                <el-table-column align="center" label="第一条数据时间" min-width="150" prop="firstTimeData" sortable />
                <el-table-column align="center" label="最后一条数据时间" min-width="150" prop="lastTimeData" sortable> </el-table-column>
                <el-table-column align="center" label="接口调用时间" min-width="150" prop="requestTime" sortable> </el-table-column>
                <el-table-column align="center" label="操作人" prop="operateBy" width="150"> </el-table-column>
                <el-table-column align="center" label="操作" fixed="right" width="140" show-overflow-tooltip>
                    <template #default="scope">
                        <el-button icon="el-icon-printer" :loading="scope?.row?.printLoading"  link size="small" type="primary" @click="printRecord(scope.row)">打印</el-button>
                        <el-button v-if="scope?.row?.deviceNo.indexOf(',') < 0" :loading="scope?.row?.loading"  icon="el-icon-download" link size="small" type="warning" @click="deviceExportPdf(scope.row)">导出</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>
<script setup>
import { ref, getCurrentInstance, onUnmounted } from 'vue';
import printRecordApi from '@/api/management/printRecord';
import orderManagement from '@/api/logisticsManagement/orderManagement';
import SearchButton from "@/components/searchModule/SearchButton.vue";
const { proxy } = getCurrentInstance();

// 显示搜索条件
const showSearch = ref(true);
// 加载
const loading = ref(false);
// 查询参数
const queryParams = ref({
    current: 1,
    size: 10,
    total: 0
});

/**
 * 导出PDF
 * @param row
 */
function deviceExportPdf(row){
    row.loading = true;
    orderManagement.deviceExportPdf({logId:row.id}).then(response=>{
        row.loading = false;
        const binaryData = [];
        binaryData.push(response);
        //获取blob链接
        let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
        window.open(pdfUrl)
    }).catch(()=>{
        proxy.msgSuccess('导出失败');
        row.loading = false;
    })
};
function printRecord(val) {
    new Promise((resolve, reject) => {
		let deviceNoList = [];
		if(val.deviceNo.indexOf(',') ==-1){
			deviceNoList = [val.deviceNo];
		}else{
			deviceNoList = val.deviceNo.split(',');
		}
		let {minTemp1,maxTemp1,minTemp2,maxTemp2} = getTemp(val.tempDataList);
		let params = {
			transportCompany: val.transportUnit, // 承运单位
			deviceNoList: deviceNoList, // 设备编号
			carCode: val.carCode, // 车牌号
			transOrderNo: val.transOrderNo, // 运单号
			externalOrderNo: val.externalOrderNo, // 外部订单号
			sendCompany: val.sendingCompany, // 发货单位
			receiverCompany: val.receivingCompany, // 收货单位
			startTime: val.firstTimeData,
			endTime: val.lastTimeData,
			minTemp1,maxTemp1,minTemp2,maxTemp2,
			source:'0',
			tempDataList:val.tempDataList
		};
        val.printLoading = true;
		orderManagement.devicePrint(params).then(response=>{
            val.printLoading = false;
			const binaryData = [];
			binaryData.push(response);
			//获取blob链接
			let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
			window.open(pdfUrl)

		}).catch(()=>{
            val.printLoading = false;
		})
        setTimeout(() => {
            resolve();
        }, 20000);
    })
        .then(() => {
            proxy.msgSuccess('打印成功');
            val.printLoading = false;
        })
        .catch(() => {
            proxy.msgError('打印失败');
            val.printLoading = false;
        });
}
/**
 *  获取温度数据中的最大值和最小值
 * @param data
 * @returns {{maxTemp1: (*|{style: string, type: string}), minTemp1: (*|{style: string, type: string}), maxTemp2, minTemp2}}
 */
function getTemp(data){
	let minTemp1 = data[0].temperature;
	let maxTemp1 = data[0].temperature;
	let minTemp2,maxTemp2;
	if(data[0].temperature2){
		minTemp2 = data[0].temperature2;
		maxTemp2 = data[0].temperature2;
	}
	// 遍历数组以找到最小和最大温度
	for (let i = 1; i < data.length; i++) {
		const temp1 = data[i].temperature;

		// 如果当前值更小，则更新minTemp1
		if (temp1 < minTemp1) {
			minTemp1 = temp1;
		}

		// 如果当前值更大，则更新maxTemp1
		if (temp1 > maxTemp1) {
			maxTemp1 = temp1;
		}
		if(data[i].temperature2){
			const temp2 = data[i].temperature2;
			// 如果当前值更小，则更新minTemp1
			if (temp2 < minTemp2) {
				minTemp2 = temp2;
			}

			// 如果当前值更大，则更新maxTemp1
			if (temp2 > maxTemp2) {
				maxTemp2 = temp2;
			}
		}

	}
	return  {minTemp1,maxTemp1,minTemp2,maxTemp2};
}
const chartRef = ref(null);
let chartInstance = null;
// onMounted(() => {
//     chartInstance = echarts.init(chartRef.value, null, {
//         // width: 600,
//         height: 300
//     });
//     // 配置项
//     const option = {
//         title: {
//             text: 'Beijing AQI',
//             left: '1%'
//         },
//         tooltip: {
//             trigger: 'axis'
//         },
//         grid: {
//             left: '5%',
//             right: '15%',
//             bottom: '10%'
//         },
//         xAxis: {
//             data: data.map(function (item: string[]) {
//                 return item[0];
//             })
//         },
//         yAxis: {},
//         toolbox: {
//             right: 10,
//             feature: {
//                 dataZoom: {
//                     yAxisIndex: 'none'
//                 },
//                 restore: {},
//                 saveAsImage: {}
//             }
//         },
//         dataZoom: [
//             {
//                 startValue: '2014-06-01'
//             },
//             {
//                 type: 'inside'
//             }
//         ],
//         visualMap: {
//             top: 50,
//             right: 10,
//             pieces: [
//                 {
//                     gt: 0,
//                     lte: 50,
//                     color: '#93CE07'
//                 },
//                 {
//                     gt: 50,
//                     lte: 100,
//                     color: '#FBDB0F'
//                 },
//                 {
//                     gt: 100,
//                     lte: 150,
//                     color: '#FC7D02'
//                 },
//                 {
//                     gt: 150,
//                     lte: 200,
//                     color: '#FD0100'
//                 },
//                 {
//                     gt: 200,
//                     lte: 300,
//                     color: '#AA069F'
//                 },
//                 {
//                     gt: 300,
//                     color: '#AC3B2A'
//                 }
//             ],
//             outOfRange: {
//                 color: '#999'
//             }
//         },
//         series: {
//             name: 'Beijing AQI',
//             type: 'line',
//             data: data.map(function (item: number[]) {
//                 return item[1];
//             }),
//             markLine: {
//                 silent: true,
//                 lineStyle: {
//                     color: '#333'
//                 },
//                 data: [
//                     {
//                         yAxis: 50
//                     },
//                     {
//                         yAxis: 100
//                     },
//                     {
//                         yAxis: 150
//                     },
//                     {
//                         yAxis: 200
//                     },
//                     {
//                         yAxis: 300
//                     }
//                 ]
//             }
//         }
//     }
//     // 设置图表配置项
//     chartInstance.setOption(options)
// });

onUnmounted(() => {
    if (chartInstance) {
        chartInstance.dispose();
        chartInstance = null;
    }
});

// 查询设备列表数据
const abnormalprintList = ref([]);
const timeList = ref({});
function getList() {
    queryParams.value.deviceNo = proxy.$route.query.deviceNo ? proxy.$route.query.deviceNo : queryParams.value.deviceNo;
    if (queryParams.value.deviceTermial) {
        timeList.value = {
            beginPrintStartTime: queryParams.value.deviceTermial[0],
            endPrintStartTime: queryParams.value.deviceTermial[1]
        };
    }

    let data = {
        current: queryParams.value.current,
        size: queryParams.value.size,
        total: queryParams.value.total,
        deviceNo: queryParams.value.deviceNo,
        operateBy: queryParams.value.operateBy,
        assetType: queryParams.value.assetType,
        deviceTerminal: {
            serialNumber: queryParams.value.serialNumber
        },
        beginPrintStartTime: queryParams.value.deviceTermial ? timeList.value.beginPrintStartTime : null,
        endPrintStartTime: queryParams.value.deviceTermial ? timeList.value.endPrintStartTime : null
    };
    printRecordApi
        .printLogList(data)
        .then((res) => {
            if (res.code == 200) {
                abnormalprintList.value = res.data.records;
                queryParams.value.total = res.data.total;
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}
// 表单重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    if (proxy.$route.query.deviceNo) {
        proxy.$router.push({ path: '/printRecord' });
    }
    getList();
}
getList();
</script>

<style lang="scss" scoped>
::v-deep #zr_0 {
    height: 300px;
}

::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.leftCenterCard {
    padding: 15px 0px 0 0px;
}

.leftCenter {
    width: 100%;
    height: 30vh;

    .line {
        height: 90%;
        width: 100%;
    }

    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;

        .txt {
            font-size: 14px;
            font-weight: bold;
            color: #000000;
        }

        ::v-deep .el-input__wrapper {
            background-color: #f1f1f1 !important;
        }
    }
}

::v-deep .margin20 {
    margin-top: 10px;

    .el-card__body {
        padding: 0;
    }
}
</style>
