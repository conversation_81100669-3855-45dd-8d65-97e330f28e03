<template>
  <div class="inputSearch">
    <inputSearch ref="searchRef" @handleQuery="handleQuery"/>
    <el-card body-style="padding-top:0;padding-bottom:0;" class="box-card last-card" style="margin-top: 10px">
      <template #header>
        <div class="card-header">
          <span>
            <el-button class="button" type="primary" @click="newAddFn()">创建销售调价申请</el-button>
          </span>
        </div>
      </template>
      <div class="item" style="margin-top:10px">
        <DragTableColumn v-loading="loadingFlag" :columns="columns" :row-style="functionIndex.tableRowStyles"
                         v-model:queryParams="searchRef.searchForm" :getList="handleQuery" :tableData="tableList"
                         className="invoiceManagement_SalesPriceAdjustment">
          <template v-slot:operate="{ scopeData }">
            <el-button :disabled="scopeData.row.status != '0' &&
              scopeData.row.status != '7'
              " link type="danger" @click="delTable(scopeData.row)"><img src="@/assets/icons/delete.png"
                                                                         style="margin: 0px 5px 0 0"/>删除
            </el-button>
            <el-button link type="primary" @click="detailFn(scopeData.row)"><img src="@/assets/icons/detail.png"
                                                                                 style="margin: 2px 5px 0 0"/>详情
            </el-button>
            <el-button link type="success" @click="logFn(scopeData.row)"><img src="@/assets/icons/review.png"
                                                                              style="margin: 0px 2px 0 0"/>操作日志
            </el-button>
          </template>
        </DragTableColumn>
        <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                       :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
                       layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                       @size-change="handleQuery"
                       @current-change="handleQuery"/>
      </div>
    </el-card>
    <el-dialog v-model="dialogVisible1" :before-close="handleClose" title="创建销售调价申请" width="80%">
      <div class="step">
        <el-steps :active="active" align-center>
          <el-step title="选择出库单据"/>
          <el-step title="填写调价信息"/>
        </el-steps>
      </div>
      <priceForm ref="priceRef" :UUID="UUID" :active="active"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button v-if="active == 1" @click="handleClose()">取消</el-button>
          <el-button v-if="active == 2" @click="upTi()">上一步</el-button>
          <el-button v-if="active == 1" type="primary" @click="nextTi()">
            下一步
          </el-button>
          <el-button v-if="active == 2" type="primary" @click="submitForm()">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <priceDetail ref="detailRef"/>
    <el-dialog v-model="dialogVisible3" title="操作日志" width="30%">
      <div v-loading="logFlag">
        <logQuery ref="childLog"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible3 = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import inputSearch from '@/components/salesManagement/priceSearch.vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import priceForm from './components/priceForm.vue'
import priceDetail from './components/priceDetail.vue'
import {uuid} from "vue-uuid";
import {backApi, manageApi, priceApi} from "@/api/model/salesManagement";
import {switchPrice} from "@/views/salesManagement/switch";
import {functionIndex} from "@/views/salesManagement/functionIndex";
// import { useStore } from 'vuex';
const data = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const loadingFlag = ref(false)
const dialogVisible1 = ref(false)
const dialogVisible2 = ref(false)
const dialogVisible3 = ref(false)
const active = ref(1)
const searchRef = ref(null)
const priceRef = ref(null)
const logFlag = ref(false)
const childLog = ref(null)
const detailRef = ref(null)
const UUID = ref(null)

const luoSave = (id, uid, nums) => {
  let num = nums ? nums : 1
  backApi.saveFile({
    uuid: uid,
    busId: id
  }).then((res) => {
    if (res.code == 200) {
      UUID.value = null
    } else {
      if (num <= 5) {
        num++
        setTimeout(() => {
          luoSave(id, uid, num)
        }, 3000)
      }
    }
  })
}
const submitForm = async () => {
  let flag1 = false
  let flag2 = false
  if (!priceRef.value.creatform) return;
  await priceRef.value.creatform.validate((valid) => {
    if (valid) {
      flag2 = true
    }
  });
  priceRef.value.chooseGoods.forEach((item, index) => {
    if (item.adjustList.length) {
      if (item.adjustList[item.adjustList.length - 1].num != 0 && item.adjustList[item.adjustList.length - 1].price != 0) {
      } else {
        ElMessage.error('请完整输入第' + (index + 1) + '行单据内的调价信息')
        flag1 = true
      }
    } else {
      ElMessage.error('请添加第' + (index + 1) + '行单据的调价信息')
      flag1 = true
    }
  })
  if (!flag1 && flag2) {
    let files = priceRef.value.backForm.n2.map(item => {
      return {
        name: item.name,
        resFileUrl: item.resFileUrl
      }
    })
    priceApi.savePrice({
      adjustExplain: priceRef.value.backForm.n1,
      files: JSON.stringify(files),
      customer: {
        id: priceRef.value.backForm.n3
      },
      handleBy: {
        id: priceRef.value.backForm.n4
      }
    }).then(res1 => {
      if (res1.code == 200) {
        let newArr = switchPrice(priceRef.value.chooseGoods, res1.data.id)
        priceApi.saveGoods(newArr).then(res2 => {
          if (res2.code == 200) {
            let xArr = []
            priceRef.value.chooseGoods.forEach((item, index) => {
              item.adjustList.forEach(items => {
                xArr.push({
                  salesAdjustForm: {
                    id: res2.data[index].id
                  },
                  adjustQuantity: items.num,
                  adjustUnitPrice: items.price
                })
              })
            })
            priceApi.savePriceForm(xArr).then(res3 => {
              if (res3.code == 200) {
                priceApi.submitAll({
                  adjustIds: res1.data.id
                }).then(res4 => {
                  if (res4.code == 200) {
                    ElMessage.success('提交成功')
                    active.value = 1
                    handleQuery()
                    dialogVisible1.value = false
                    luoSave(res1.data.id, UUID.value, 1)
                    clearAll()
                  } else {
                    ElMessage.error('提交失败')
                  }
                })
              } else {
                ElMessage.error('保存价格明细失败')
              }
            })
          } else {
            ElMessage.error('信息保存失败')
          }
        })
      } else {
        ElMessage.error('信息保存失败')
      }
    })
    // ElMessage.success('提交成功')
  }
}

const nextTi = () => {
  if (priceRef.value.chooseGoods.length > 0) {
    priceRef.value.chooseGoods.forEach(item => {
      if (!item.adjustList) {
        item.adjustList = []
      }
    })
    active.value = 2
  } else {
    ElMessage.error('请先选择')
  }
}
const logFn = async (row) => {
  console.log(row)
  dialogVisible3.value = true
  logFlag.value = true
  if (childLog.value) {
    childLog.value.data.list = [];
  }
  const auditList = await priceApi.auditLists({"salesAdjust.id": row.id})
  const logList = await manageApi.logList({masterId: row.id})
  if (auditList.code == 200 && logList.code == 200) {
    childLog.value.timeFns(auditList.data.records, logList.data.records);
  } else {
    ElMessage.error('加载失败')
  }
  logFlag.value = false
}
const upTi = () => {
  active.value = 1
}


const delTable = (row) => {
  ElMessageBox.confirm("确认删除此项吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        priceApi.delList({
          adjustIds: row.id
        }).then((res) => {
          if (res.code == 200) {
            ElMessage.success('删除成功')
            handleQuery()
          } else {
            ElMessage.error('删除失败，错误信息：' + res.msg)
          }
        })
      })
      .catch(() => {
      });
}

const detailFn = (row) => {
  detailRef.value.dialogVisible = true
  detailRef.value.getList(row.id)
}
const clearAll = () => {
  priceRef.value.goodsTable = []
  priceRef.value.chooseGoods = []
  priceRef.value.backForm.n1 = ''
  priceRef.value.backForm.n2 = []
  for (let key in priceRef.value.searchForm) {
    if (key == 'n4') {
      priceRef.value.searchForm[key] = []
    } else if (key == 'size') {
      priceRef.value.searchForm[key] = 10
    } else if (key == 'current') {
      priceRef.value.searchForm[key] = 1
    } else if (key == 'total') {
      priceRef.value.searchForm[key] = 0
    } else {
      priceRef.value.searchForm[key] = ''
    }
  }
  for (let key in priceRef.value.backForm) {
    if (key == 'n2') {
      priceRef.value.backForm[key] = []
    } else {
      priceRef.value.backForm[key] = ''
    }
  }
  priceRef.value.multipleTableRef?.clearSelection()
  UUID.value = null
  active.value = 1
  priceRef.value.comeList = []
}
const handleClose = () => {
  ElMessageBox.confirm('信息未保存确认取消吗?', "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        dialogVisible1.value = false
        clearAll()
      })
      .catch(() => {
        // catch error
      })
}

const newAddFn = () => {
  UUID.value = uuid.v1()
  dialogVisible1.value = true
}
const changeTime = (time) => {
  if (time) {
    let newTime = new Date(time)
    newTime = newTime.setDate(newTime.getDate() + 1);
    newTime = functionIndex.transformTimestampSearch(newTime)
    return newTime
  } else {
    return null
  }
}
const handleQuery = () => {
  loadingFlag.value = true
  console.log(searchRef.value)
  priceApi.getList({
    size: data.pageSize,
    current: data.pageNum,
    queryCustomerName: searchRef.value.searchForm.n1 ? searchRef.value.searchForm.n1 : null,
    queryCommodityName: searchRef.value.searchForm.n2 ? searchRef.value.searchForm.n2 : null,
    queryOrderCode: searchRef.value.searchForm.n3 ? searchRef.value.searchForm.n3 : null,
    queryHandleByName: searchRef.value.searchForm.n4 ? searchRef.value.searchForm.n4 : null,
    queryApplyByName: searchRef.value.searchForm.n5 ? searchRef.value.searchForm.n5 : null,
    auditStatus: searchRef.value.searchForm.n6 ? searchRef.value.searchForm.n6 : null,
    beginApplyDate: searchRef.value.searchForm.n7 ? searchRef.value.searchForm.n7[0] : null,
    endApplyDate: searchRef.value.searchForm.n7 ? changeTime(searchRef.value.searchForm.n7[1]) : null
  }).then((res) => {
    if (res.code == 200) {
      tableList.value = res.data.records
      tableList.value.forEach(item => {
        if (Number(item.adjustPrice) === Number(item.adjustPrice)) {
          item.adjustPrice = Number(item.adjustPrice).toFixed(2)
        }
      })
      data.total = res.data.total
    }
    loadingFlag.value = false
  })
}

onBeforeMount(() => {
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  handleQuery()
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})
const columns = ref([
  {label: '申请编号', prop: 'applyNo'},
  {label: '申请日期', prop: 'applyDate', type: 'date'},
  {label: '申请人', prop: 'applyBy.name'},
  {label: '客户名称', prop: 'customer.enterpriseName'},
  {label: '经手人', prop: 'handleBy.name'},
  {label: '合计调价金额', prop: 'adjustPrice'},
  {
    label: '审核状态',
    prop: 'auditStatus',
    searchKey: "n6",
    type: "status",
    filters: JSON.parse(localStorage.getItem('salesType'))
  },
  {label: '操作', prop: 'operate', minWidth: "250px", type: 'operate', fixed: 'right'},
])
const tableList = ref([])
</script>
<style lang='scss' scoped>
.inputSearch {
  padding: 10px;
}

.item {
  margin-bottom: 18px;
  margin-top: -10px;
}
</style>
