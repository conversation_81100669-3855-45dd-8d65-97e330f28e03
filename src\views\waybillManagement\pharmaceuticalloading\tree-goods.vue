<template>
    <div style="width: 500px;">
        <el-tree ref="treeRef" :data="data" show-checkbox :default-expanded-keys="defaultExpandedKeys" node-key="id"
            highlight-current :props="defaultProps" @node-expand="expandNode" @node-click="queryDetails"
            :expand-on-click-node="false" @check="handleCheck">
            <template #default="{ node, data }">
                <span class="custom-tree-node">
                    <span style="width: 380px; display: flex;justify-content: space-between;">
                        <span v-if="data.id === 1">全选</span>
                        <span v-if="data.type == '1'">{{ data.areaName }}</span>
                        <span v-if="data.type == '1'">总计:{{ data.transTotalNum }}单 {{ data.goodsTotalNum }}件</span>
                        <span v-if="data.type == '2'">{{ data.provinceName }}{{ data.cityName }}{{ data.countyName }}</span>
                        <span v-if="data.type == '2'">{{ data.transOrderNum }}单 {{ data.goodsTotalNum }}件</span>
                        <span v-if="data.type == '3'">订单号：{{ data.orderNo }}</span>
                        <span v-if="data.type == '3' && open == false">{{ data.goodsTotalNum }}件</span>
                        <span v-if="data.type == '3' && open == true">
                            <el-tooltip class="box-item" effect="light" placement="right">
                                <template #content>
                                    <span style="width: 300px; color: #999999;">
                                        <div v-for="(item, index) in caseCode" :key="index">
                                            <span>{{ item.orderNo }}</span>
                                            <span style="margin-left: 20px;">{{ item.codeDesc }}</span>
                                        </div>
                                    </span>
                                </template>
                                <span>{{ data.goodsTotalNum }}件</span>
                            </el-tooltip>
                        </span>
                    </span>
                </span>
            </template>
        </el-tree>
    </div>
</template>

<script>
import pharmaceuticalloading from '@/api/waybillManagement/pharmaceuticalloading';
export default {
    name: "",
    props: {
        goodsList: {
            type: Array
        },
        openTree: {
            type: Boolean,
            // default: false
        },
    },
    components: {
    },
    data() {
        return {
            checkAll: false,
            isIndeterminate: true,
            checkedCities: [],
            defaultExpandedKeys: '1',
            data: [
                {
                    id: 1,
                    label: 'Level one 1',
                    children: [],
                },
            ],
            open: false,
            caseCode: [],
            dataList: [],
        };
    },
    computed: {
        defaultProps() {
            return {
                children: 'children',
                label: 'label',
            };
        },
    },
    mounted() {
        // 隐藏第一层小三角
        const tree = this.$refs.treeRef.$el;
        const firstLevelNodes = tree.querySelectorAll(".el-tree-node__content");
        firstLevelNodes.forEach(node => {
            const icon = node.querySelector(".expanded");
            if (icon) {
                icon.style.display = "none";
            }
        });

        // this.dataProcessing();
    },
    beforeUpdate() {
        this.renewGoods(1);
    },
    methods: {
        renewGoods(val) {
            if (val == 1) {
                this.dataProcessing(val);
            } else {
                this.dataProcessing();

            }

        },
        // 处理结构数据
        dataProcessing(val) {
            if (val == 1) {
                this.data[0].children = [];
            } else {
                this.data[0].children = this.goodsList;
            }
            this.data[0].children.forEach(child => {
                if (child.countyList) {
                    child.children = child.countyList;
                    child.children.forEach(order => {
                        if (order.orderList) {
                            order.children = order.orderList;
                        }
                    });
                }
            });
        },
        // 查询节点详情
        queryDetails(clickNode, node) {
            console.log(clickNode);
            console.log(node);

            if (clickNode.type === "3") {
                this.getAreaGoodsList(node.parent.parent.data.areaCode, clickNode.transOrderNo);
            }
        },
        // 查询运单详情
        getAreaGoodsList(areaCode, transOrderNo) {
            pharmaceuticalloading.getAreaGoodsList({
                areaCode: areaCode,
                transOrderNo: transOrderNo
            }).then((res) => {
                if (res.code == 200) {
                    this.caseCode = res.data;
                    this.open = true
                }
            });
        },
        handleCheck(node, infoStatus) {
            let returnList = [];
            infoStatus.checkedNodes.forEach(node => {
                if (node.type === '3') {
                    let params = returnList.filter(data => data.dataId === node.areaCode);
                    if (params.length === 0) {
                        let data = {
                            "dataId": node.areaCode,
                            "orderList": [
                                {
                                    "orderNo": node.orderNo,
                                    "transOrderNo": node.transOrderNo,
                                }
                            ]
                        }
                        returnList.push(data);
                    } else {
                        returnList.forEach(item => {
                            if (item.dataId === node.areaCode) {
                                item.orderList.push({
                                    "orderNo": node.orderNo,
                                    "transOrderNo": node.transOrderNo,
                                });
                            }
                        });
                    }
                }
            });
            this.dataList = returnList;
            this.$emit('func', this.dataList)
        },
    }
}
</script>
<style lang="scss" scoped>
::v-deep {
    .el-collapse {
        border: 0px;
    }
}
</style>