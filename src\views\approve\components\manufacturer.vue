<template>
  <div class="app-container" style="width: 100%;">
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog v-if="visible" v-model="visible" :append-to-body="ture" :before-close="beforeClose" destroy-on-close
               title="生产厂家详情" width="90%">
      <div v-loading="modalLoading">
        <div style="display: flex;justify-content: space-between;padding: 20px 20px;">
          <span style="color:#333;font-size: 18px;">{{ data.document }}</span>
          <span v-if="type != 'record'" style="color:#333;font-weight: bold;font-size: 18px;">{{ '审批中' }}</span>
          <span v-if="type == 'record'" style="color:#333;font-weight: bold;font-size: 18px;">{{ '审批完成' }}</span>
        </div>
        <!-- <el-button style="margin-top: 3px;position: absolute;right: 50px;top: 10px;"
            @click="handleFileList">附件列表</el-button> -->
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item name="1" title="基本信息">
            <template #title>
              <span class="col_title">基本信息</span>
            </template>
            <el-descriptions :column="4" border>
              <el-descriptions-item align="center" class-name="my-content" label="企业名称" label-align="left"
                                    label-class-name="my-label">{{ listStep1.enterpriseName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="统一社会信用代码" label-align="left">{{
                  listStep1.businessLicenseCode || '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="营业执照经营范围" label-align="left">{{
                  listStep1.businessScope || '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="营业执照发证日期" label-align="left">
                {{
                  listStep1.enterpriseStartTime ?
                      moment(listStep1.enterpriseStartTime).format('YYYY-MM-DD') : '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="营业执照有效期" label-align="left">
                {{
                  listStep1.businessLicenseExpiredTime ?
                      moment(listStep1.businessLicenseExpiredTime).format('YYYY-MM-DD') : '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="发证机关" label-align="left">{{
                  listStep1.enterpriseOffice || '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="备注" label-align="left">{{
                  listStep1.remark || '--'
                }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="1" border>

              <el-descriptions-item align="center" label="营业执照附件" label-align="left">
                <div class="demo-image__preview">
                  <el-image v-for="item in listStep1.businessLicensePicturt" :key="item" :preview-src-list="[item.url]"
                            :src="item.url"
                            class="imgView" fit="cover"/>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item class="step2" name="2" title="生产许可证">
            <template #title>
              <span class="col_title">生产许可证</span>
            </template>
            <div class="btn_cmt">
              <el-button
                  v-for="item in listStep2"
                  :key="item.id"
                  :type="tabKey === Object.entries(typeDict).find(([key, val]) => val === item.type)[0] ? 'primary' : 'default'"
                  @click="() => handleTabClick(Object.entries(typeDict).find(([key, val]) => val === item.type)[0])">{{
                  item.type
                }}
              </el-button>
            </div>
            <el-descriptions :column="4" border>
              <el-descriptions-item align="center" class-name="my-content" label="许可证编号"
                                    label-align="left" label-class-name="my-label">{{
                  listStep2_item.licenseNo || '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="生产地址" label-align="left">{{
                  listStep2_item.licenseAddress || '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="生产范围" label-align="left" @click="handleDetail">{{
                  Object.entries(typeDict).find(([key, val]) => val === listStep2_item.type)[0] == '1' ||
                  Object.entries(typeDict).find(([key, val]) => val === listStep2_item.type)[0] == '6' ?
                      filterArr(licenseScopeListData, 'id', listStep2_item.licenceScopes) : '查看详情'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="发证日期" label-align="left">
                {{
                  listStep2_item.licenseStartTime ?
                      moment(listStep2_item.licenseStartTime).format('YYYY-MM-DD') : '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="有效期" label-align="left">
                {{
                  listStep2_item.licenseValidity ?
                      moment(listStep2_item.licenseValidity).format('YYYY-MM-DD') : '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="发证机关" label-align="left">{{
                  listStep2_item.licenseOffice || '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="生产负责人" label-align="left">{{
                  listStep2_item.licenseDirector || '--'
                }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="质量负责人" label-align="left">{{
                  listStep2_item.qualityDirector || '--'
                }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="1" border>

              <el-descriptions-item align="center" label="许可证图片" label-align="left">
                <div class="demo-image__preview">
                  <el-image v-for="item in listStep2_item.licenseImg" :key="item" :preview-src-list="[item.url]"
                            :src="item.url" class="imgView" fit="cover"/>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="3" title="GMP证书">
            <template #title>
              <span class="col_title">GMP证书</span>
            </template>
            <el-table :data="listStep3" border>
              <el-table-column align="center" label="序号" width="80">
                <template #default="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书编号" width="200">
                <template #default="scope">
                  <span>{{ scope.row.gmpCertificateNo }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书地址" min-width="200">
                <template #default="scope">
                  <span>{{ scope.row.gmpCertificateAddress }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="证书图片" width="330">
                <template #default="scope">
                                    <span
                                        v-if="scope.row.gmpCertificatePicture"
                                        style="color:#2A76F8;cursor: pointer;white-space: nowrap;overflow:hidden;text-overflow: ellipsis;display: inline-block;width: 90%;"
                                        @click="() => handleUploadViewFile(scope.row.gmpCertificatePicture.url)">{{
                                        scope.row.gmpCertificatePicture.name
                                      }}</span>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书有效期" width="200">
                <template #default="scope">
                                    <span>{{
                                        scope.row.gmpExpiredTime ?
                                            moment(scope.row.gmpExpiredTime).format('YYYY-MM-DD') : '--'
                                      }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书授权范围" width="200">
                <template #default="scope">
                                    <span>{{
                                        filterArr(licenseScopeList, 'id', scope.row.gmpCertificateScope) ||
                                        '--'
                                      }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="4" title="附件列表">
            <template #title>
              <span class="col_title">附件列表</span>
            </template>
            <el-table ref="multipleTableRef_file" :data="listStep4" border>
              <el-table-column align="center" label="序号" width="80">
                <template #default="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="类型" width="120">
                <template #default="scope">
                  <span>{{ formDict(manufacturerType, scope.row.smallClass || scope.row.smallType) }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="文件类型" min-width="330">
                <template #default="scope">
                  <span>{{ formDict(directoryFileName, scope.row.cardName) }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="文件名称" width="330">
                <template #default="scope">
                  <div>
                    <div v-if="scope.row.fileNameUrl">
                                            <span v-for="(item, index) in scope.row.fileNameUrl" :key="item.uid"
                                                  class="fileName_t"><span
                                                @click="() => handleUploadViewFile(scope.row.fileNameUrl[index].url)">{{
                                                item.name
                                              }}</span></span>
                    </div>
                    <span v-else>--</span>
                  </div>

                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="是否必传" width="120">
                <template #default="scope">
                                    <span>{{
                                        scope.row.isUpload === '1' ? '是' : scope.row.isUpload === '0' ? '否' : '--'
                                      }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="是否多张" width="120">
                <template #default="scope">
                                    <span>{{
                                        scope.row.isPage === '1' ? '是' : scope.row.isPage === '0' ? '否' :
                                            '--'
                                      }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="备注" min-width="230">
                <template #default="scope">
                  <span>{{ scope.row.remark || '--' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item v-if="deFlag" name="7" title="操作日志" @click="handleChange_msg">
            <template #title>
              <span class="col_title">操作日志</span>
            </template>
            <div style="max-height:500px;overflow-y:scroll">
              <LogQuery ref="childRef"/>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="type != 'record'&&deFlag==true" name="8" title="审批意见" @click="handleChange_msg">
            <template #title>
              <span class="col_title">审批意见</span>
            </template>
            <Audit ref="auditRef" @refresh="getList"/>
          </el-collapse-item>
        </el-collapse>

      </div>
      <template v-if="!modalLoading" #footer>
        <div class="dialog-footer">
          <el-button v-if="type != 'record'&&deFlag==true" type="primary" @click="handleSubmit">确认</el-button>
          <el-button @click="beforeClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-drawer v-if="fileListVisible" v-model="fileListVisible" :before-close="() => fileListVisible = false"
               direction="rtl"
               size="60%" title="附件列表">
      <div style="display:flex;justify-content:end">
        <el-button :disabled="!fileListChooseList.length" style="margin-top: 10px; margin-bottom:20px;margin-right:20px"
                   type="primary" @click="handleExportFile">导出
        </el-button>
      </div>
      <el-table :data="fileListData" border @selection-change="handleDetailFileListChoose">
        <el-table-column align="center" min-width="55" type="selection"/>
        <el-table-column :formatter="(row) => row.cardName || '--'" :show-overflow-tooltip="true" align="center"
                         label="证件名称"
                         prop="cardName" show-overflow-tooltip/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="编号" prop="fileCoder"
                         show-overflow-tooltip/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="证件图片" min-width="280"
                         prop="fileName" show-overflow-tooltip>
          <template #default="scope">
            <el-button link type="primary" @click="() => handleUploadViewFile(scope.row.fileUrl)">{{
                scope.row.fileName
              }}
            </el-button>
          </template>
        </el-table-column>
        >
        <el-table-column :formatter="(row) => moment(row.fileEndDate).format('YYYY-MM-DD')"
                         :show-overflow-tooltip="true" align="center" label="有效期"
                         prop="fileEndDate" show-overflow-tooltip/>
        <el-table-column :formatter="(row) => row.range || '--'" :show-overflow-tooltip="true" align="center"
                         label="范围" prop="range"
                         show-overflow-tooltip/>
        <el-table-column
            :formatter="(row) => (row.busType == '1' || row.busType == '2') ? row.busType == '1' ? '普药' : row.busType == '2' ? '特药' : '--' : row.busType"
            :show-overflow-tooltip="true" align="center" label="类型"
            prop="busType"
            show-overflow-tooltip/>
        <el-table-column :formatter="(row) => row.remark || '--'" :show-overflow-tooltip="true" align="center"
                         label="备注" prop="remark"
                         show-overflow-tooltip/>
        <el-table-column :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')" :show-overflow-tooltip="true"
                         align="center" label="归档时间"
                         prop="createDate" show-overflow-tooltip/>
      </el-table>
      <div style="display:flex;justify-content:end;margin-top:20px">
        <pagination v-show="fileListDataTotal > 0" v-model:limit="fileListDataPage.size"
                    v-model:page="fileListDataPage.current" :total="fileListDataTotal"
                    @pagination="handleFileList"/>
      </div>

    </el-drawer>
    <el-dialog v-if="licenseScopeVisible" v-model="licenseScopeVisible"
               :before-close="() => licenseScopeVisible = false" style="padding:0 30px"
               title="生产范围" width="60%">
      <el-table v-loading="lisloading" :data="licenseScopeListData" :select-on-indeterminate="true"
                :tree-props="{ children: 'children' }"
                border row-key="id" style="width: 100%;margin-bottom: 20px">
        <el-table-column align=center label="选择" min-width="140">
          <template #default="scope">
            <el-checkbox v-model="checkedKeys" :label="scope.row.id"
                         @change="(isChoose) => handleCheckChange(isChoose, scope.row)">&nbsp;
            </el-checkbox>
          </template>
        </el-table-column>
        <!-- <el-table-column type="selection" width="55" :reserve-selection="true"
            :selectable="selectable"></el-table-column> -->
        <!-- <el-table-column type="selection" width="55" align="center"> </el-table-column> -->
        <el-table-column align="center" label="质量名称" min-width="250" prop="massName">
        </el-table-column>
        <el-table-column align="center" label="质量编号" min-width="180" prop="massNo">
        </el-table-column>
        <el-table-column align="center" label="属性分类" prop="massType">
          <template #default="scope">
            {{ scope.row.isStop == 1 ? "新范围" : "旧范围" }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="isStop">
          <template #default="scope">
            {{ scope.row.isStop == 1 ? "正常" : "停用" }}
          </template>
        </el-table-column>
        <el-table-column :formatter="row => row.remark ? row.remark : '--'" align="center" label="备注"
                         min-width="150px"
                         prop="remark"/>
      </el-table>
      <template #footer>
                <span class="dialog-footer">

                    <el-button @click="licenseScopeVisible = false">取消</el-button>
                </span>
      </template>
    </el-dialog>
    <viewImg v-if="uploadVisibleFile" :beforeClose="() => uploadVisibleFile = false" :src="uploadViewImgUrlFile"
             :visible="uploadVisibleFile"/>
  </div>
</template>

<script setup>

import {defineProps, getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import moment from 'moment'
import manufacturerManagement from '@/api/erp/manufacturerManagement'
import custom from '@/api/erp/custom'
import Audit from '@/components/detailsForm/audit.vue'
import LogQuery from '@/components/detailsForm/logQuery.vue'
import {drugApi} from "@/api/model/commodity/drug/index";

const {proxy} = getCurrentInstance();

const typeDict = {
  '1': '药品',
  '2': '三类',
  '3': '一类',
  '4': '二类',
  '5': '食品',
  '6': '消杀',
}
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  beforeClose: {
    type: Object,
    default: () => {
    }
  },
  data: {
    type: Object,
    default: () => {
    }
  },
  getList: {
    type: Object,
    default: () => {
    }
  },
  type: {
    type: String,
    default: ''
  },
  deFlag: {
    type: Boolean,
    default: true
  }
})
const {visible, beforeClose, data, getList, type, deFlag} = toRefs(props)
const childRef = ref(null)
const auditRef = ref(null)
const modalLoading = ref(false)
const activeNames = ref(['1', '2', '3', '4', '7', '8'])
const fileListVisible = ref(false)
const fileDraLoading = ref(false)
const fileListData = ref([])
const uploadViewImgUrlFile = ref(undefined)
const uploadVisibleFile = ref(false)
const fileListDataTotal = ref(0)
const licenseScopeVisible = ref(false)
const licenseScopeListData = ref([])
const lisloading = ref(false)
const checkedKeys = ref([])
const licenseScopeList = ref([])
const listStep1 = ref({})
const listStep2 = ref([])
const listStep3 = ref([])
const listStep4 = ref([])
const delFlagList = ref([])
const reviewStatus = ref([])
const manufacturerType = ref([])
const shopType = ref([])
const directoryFileName = ref([])
const tabKey = ref('1')
const listStep2_item = ref({})
const fileListChooseList = ref([])
const datas = reactive({
  fileLisstDataPage: {
    current: 1,
    size: 10,
  },
})
const {fileListDataPage} = toRefs(datas)


const formDict = (data, val) => {
  return proxy.selectDictLabel(data, val)
}
const handleSubmit = async () => {
  if (!auditRef.value.form?.status) return proxy.msgError('请先选择审批结果！')
  await auditRef.value.formSub(data.value?.id);
  await beforeClose.value()
}
const handleCheckChange = () => {

}
const handleFileList = () => {
  fileListVisible.value = true
  fileDraLoading.value = true
  manufacturerManagement.getFileList({
    commonId: listStep1.value.id,
    commonType: '10', ...fileListDataPage.value
  }).then(res => {
    if (res.code == 200) {
      fileListData.value = res.data?.records
      fileListDataTotal.value = res.data?.total

    }
    fileDraLoading.value = false
  })
}
const handleDetailFileListChoose = (key) => {
  fileListChooseList.value = key
}
const handleExportFile = () => {
  const chooseArr = []
  fileListChooseList.value.forEach(v => {
    chooseArr.push(v.fileUrl)
  })
  manufacturerManagement.exportFile({fileNames: chooseArr.toString()}).then(res => {
    proxy.download(res)
  })
}
const handleTabClick = (value) => {
  if (tabKey.value === value) retrun
  tabKey.value = value
  listStep2_item.value = {}
  const key = Object.entries(typeDict).find(([key, val]) => key === tabKey.value)
  listStep2_item.value = listStep2.value?.filter(item => item.type === key[1])?.[0]
  if (tabKey.value == '1' || tabKey.value == '6') {
    handleQualityList({'item.id': tabKey.value == '1' ? '6' : '1', isStop: '1', current: 1, size: 999})
  }
}
const handleDetail = () => {
  if (tabKey.value == '1' || tabKey.value == '6') {
    return
  }
  licenseScopeVisible.value = true
  licenseScopeListData.value = []
  const shopKey = shopType.value.filter(item => item.name === typeDict[tabKey])?.[0]?.id
  if (shopKey) {
    lisloading.value = true
    custom.getWightConfig({dictid: shopKey}).then(res => {
      if (res.code == 200) {
        lisloading.value = false
        res.data.forEach(v => {
          if (v.length) {
            licenseScopeListData.value.push(v[0])
          }

        })
        listStep2_item.value.licenceScopes?.forEach(v => {
          checkedKeys.value.push(v.massRangeSet.id)
        })

      }
    }).finally(() => {
      lisloading.value = false
    })
  } else {
    lisloading.value = false
  }
}
const handleUploadViewFile = (url) => {
  uploadVisibleFile.value = true
  uploadViewImgUrlFile.value = url

}
const filterArr = (option, key, value) => {
  if (!option?.length || !key || !value?.length) return
  const res = option.filter(v => value.includes(v[key]) === true)
  const NameArr = []
  res?.forEach(item => {
    NameArr.push(item?.valueName)
  })
  return NameArr?.toString()
}
const handleQualityList = (params) => {
  lisloading.value = true
  manufacturerManagement.erpBaseCommonValues({...params}).then(res => {
    if (res.code == 200) {
      lisloading.value = false
      licenseScopeListData.value = res.data?.records
    }
    const arr = []

    listStep2_item.value.licenceScopes?.forEach(v => {
      arr?.push(v?.massRangeSet?.id || v)
    })
    listStep2_item.value.licenceScopes = arr
  })
}

async function dict() {
  delFlagList.value = await proxy.getDictList('erp_delFlag_status')
  reviewStatus.value = await proxy.getDictList('erp_review_status')
  shopType.value = await proxy.getDictList('product_type_config')
  manufacturerType.value = await proxy.getDictList('manufacturer_type')
  directoryFileName.value = await proxy.getDictList('directory_file_name')

}

const regx = (arr) => {
  if(!arr?.length) return
  const filterArr = []
  const regKey = ["licenseNo", "licenseAddress", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg"]
  for (var item of arr) {
    for (var val of regKey) {
      if (Array.isArray(item[val]) ? item[val]?.length : item[val]) {
        if (filterArr.some(x => x.type === item.type)) break
        filterArr.push(item)
        break
      }
    }
  }
  return filterArr
}
const getDetail = () => {
  console.log(type.value);
  manufacturerManagement.detail({id: data.value.documentId}).then(res => {
    if (res.code == 200) {
      listStep1.value = res.data?.informationDTO
      listStep1.value.businessLicensePicturt = listStep1.value?.businessLicensePicturt ? JSON.parse(listStep1.value?.businessLicensePicturt) : []
      listStep2.value = regx(res.data?.licenceDTOS)
      listStep2.value?.forEach(item => {
        item.licenseImg = item.licenseImg ? JSON.parse(item.licenseImg) : []
      })
      listStep2_item.value = listStep2.value[0]
      tabKey.value = Object.entries(typeDict).find(([key, val]) => val === listStep2.value[0]?.type)[0]
      if (tabKey.value == '1' || tabKey.value == '6') {
        handleQualityList({'item.id': tabKey.value == '1' ? '6' : '1', isStop: '1', current: 1, size: 999})
      }
      listStep3.value = res.data?.gmpCertificateDTOS
      listStep3.value?.forEach(item => {
        item.gmpCertificatePicture = item.gmpCertificatePicture ? JSON.parse(item.gmpCertificatePicture) : []
      })
      listStep4.value = res.data?.fileDTOS
      listStep4.value?.forEach(item => {
        item.fileNameUrl = item.fileNameUrl ? JSON.parse(item.fileNameUrl) : []
      })
    } else {
      proxy.msgError(res.msg)
    }

  }).finally(() => {

  })
}
onMounted(async () => {
  modalLoading.value = true
  dict()
  getDetail()
  if (deFlag.value) {
    let reviewRes = await manufacturerManagement.erpManufacturerInformationApproval({'manufactureInformation.id': data.value.documentId})
    let resq = await drugApi.drugLog({masterId: data.value.documentId})
    if ((reviewRes.code == 200 && reviewRes.data) || (resq.code == 200 && resq.data)) {
      childRef.value.timeFns(reviewRes?.data?.records, resq?.data?.records)
    }
  }
  let res = await manufacturerManagement.getLicenseScopeList({'item.id': '6', isStop: '1', current: 1, size: 999})
  licenseScopeList.value = res?.data?.records
  modalLoading.value = false
})


</script>
<style lang="scss" scoped>
.col_title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 8px;

  &::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 20px;
    background-color: #2878ff;
    border-radius: 2px;
    position: absolute;
    top: 15px;
    left: 0;
  }
}

.box {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  // grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: start;
}

.el-tabs {
  display: flex;
  // align-items: center;
}

.el-tabs__nav-wrap::after {
  width: 0 !important;
}

::v-deep .rules {
  position: relative;

  .cell::after {
    content: "*";
    color: red;
    display: inline-block;
    position: absolute;
    top: 30%;
    left: 70px;
  }
}

::v-deep .Botm {
  .el-card__body {
    padding-bottom: 0px
  }
}

.box_date {
  width: 220px;
}

.box_2 {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
  display: contents !important;
}

::v-deep .el-upload-dragger {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .step2 {
  .el-button {
    // border-bottom: none;
    border-right: none;
    border-radius: 4px 0 0 4px;

  }

  .el-button + .el-button {
    margin-left: 0;
    border-radius: 0 0 0 0;
    // border-left: none;
  }
}

.btn_cmt {
  margin-bottom: 20px;
}

.last-btn {
  border: 1px solid #dcdfe6 !important;
}

.fileName_t {
  display: flex;
  width: 100%;
  color: #2a76f8;
  cursor: pointer;
  align-items: center;
  margin-top: 4px;

  span:nth-of-type(2) {
    display: none;
    margin-left: 10px;

    ::v-deep .el-icon {
      // margin-top:10px;
      font-size: 12px;
      color: red
    }
  }
}

.fileName_t:hover .fileName_t_icon {
  display: block;
}

::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
  font-weight: bold;
  color: #505050;
  font-size: 13px;
  width: 150px;
}

.imgView {
  width: 100px;
  height: 100px;
  margin-right: 20px
}

::v-deep .demo-image__preview {
  text-align: left;
}

.btn_cmt {
  margin-bottom: 20px;
}

// ::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-center {
//     width: 25%;
// }
</style>
