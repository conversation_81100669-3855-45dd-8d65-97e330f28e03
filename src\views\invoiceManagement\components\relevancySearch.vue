<template>
	<el-card body-style="padding-bottom:2px" class="box-card">
    <el-form ref="queryRef" :inline="true" :model="searchForm" class="form_130" label-width="130px">
			<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
				<el-form-item label="供应商名称">
          <el-input v-model="searchForm.n1" class="form_225" clearable placeholder="请输入供应商名称"
                    style="width: 220px"/>
				</el-form-item>
				<el-form-item label="采购单据编号">
          <el-input v-model="searchForm.n2" class="form_225" clearable placeholder="请输入采购单据编号"
                    style="width: 220px"/>
				</el-form-item>
        <el-form-item label="发票号码">
          <el-input v-model="searchForm.n3" class="form_225" clearable placeholder="请输入发票号码"
                    style="width: 220px"/>
				</el-form-item>
				<el-form-item v-show="showSearch" label="发票代码">
          <el-input v-model="searchForm.n4" class="form_225" clearable placeholder="请输入发票代码"
                    style="width: 220px"/>
				</el-form-item>
				<el-form-item v-show="showSearch" label="申请人">
          <el-input v-model="searchForm.n5" class="form_225" clearable placeholder="请输入申请人"
                    style="width: 220px"/>
				</el-form-item>
				<el-form-item v-show="showSearch" label="申请日期">
					<div class="xBox">
            <el-date-picker v-model="searchForm.n6" class="form_225" end-placeholder="结束日期" format="YYYY/MM/DD"
                            range-separator="至" size="default" start-placeholder="开始日期" style="width: 220px"
                            type="daterange" value-format="YYYY-MM-DD HH:mm:ss"/>
					</div>
				</el-form-item>
				<el-form-item v-show="showSearch" label="审核状态">
          <el-select v-model="searchForm.n7" class="form_225" placeholder="请选择审核状态" style="width: 220px">
            <el-option v-for="(item, index) in statusList" :key="index" :label="item.name" :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item v-show="showSearch" label="类型">
          <el-select v-model="searchForm.n8" class="form_225" placeholder="请选择类型" style="width: 220px">
            <el-option label="全部" value=""/>
            <el-option :value="1" label="采购入库"/>
            <el-option :value="2" label="采退出库"/>
					</el-select>
				</el-form-item>
			</TopTitle>
		</el-form>

	</el-card>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import TopTitle from "@/components/topTitle/index.vue";

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */

const {proxy} = getCurrentInstance();
const statusList = ref()
const searchForm = ref({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: "",
	n6: "",
	n7: "",
	n8: "",
});
const showSearch = ref(false);
const data = reactive({});
const emit = defineEmits(["handleQuery"]);


const handleQuery = () => {
	emit("handleQuery");
};
const resetQuery = () => {
	for (let i in searchForm.value) {
		searchForm.value[i] = "";
	}
	emit("handleQuery");
};
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	statusList.value = await proxy.getDictList("erp_review_status");

});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	searchForm
});
</script>
<style lang="scss" scoped>
.butns {
	text-align: center;
}

.xBox {
	width: 220px;
}
</style>
