<!--  1. 保温箱管理 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form">
                <el-form-item label="设备编号" prop="deviceNo" style="width: 250px">
                    <el-input v-model="queryParams.deviceNo" clearable placeholder="请输入设备编号" @keyup.enter.native="getList"/>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="设备厂家" prop="deviceManufacturer">
                    <el-select v-model="queryParams.deviceManufacturer" clearable placeholder="请选择设备厂家" @change="getList">
                        <el-option v-for="dict in manufacturerList" :key="dict.dictValue" :label="dict.name" :value="dict.name" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="资产类型" prop="assetType">
                    <el-select v-model="queryParams.assetType" clearable placeholder="请选择资产类型" @change="getList">
                        <el-option v-for="dict in assetList" :key="dict.dictValue" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="资产编号" prop="assetNo">
                    <el-input v-model="queryParams.assetNo" clearable placeholder="请输入资产编号" @keyup.enter.native="getList" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="消息类型" prop="messType">
                    <el-select v-model="queryParams.messType" clearable placeholder="请选择消息类型" @change="getList">
                        <el-option v-for="dict in messageTypeList" :key="dict.dictValue" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发送状态" prop="sendStatus" style="width: 250px">
                    <el-select v-model="queryParams.sendStatus" clearable placeholder="请选择发送状态" @change="getList">
                        <el-option v-for="dict in quotationTypeList" :key="dict.dictValue" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="报警时间" prop="timeList" style="width: 310px">
                    <el-date-picker v-model="queryParams.timeList" end-placeholder="结束日期" range-separator="-" start-placeholder="开始日期" style="width: 240px" type="daterange" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="getList" @resetQuery="resetQuery" @showAllClick="showAllClick"/>
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:export']" icon="el-icon-download" size="mini" type="warning" @click="exportList">导出 </el-button>
                    </el-col>
                    <!-- <right-toolbar :showSearch="showSearch" @queryTable="getList"></right-toolbar> -->
                </el-row>
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table v-loading="loading" :data="incubatorList" border style="margin-top: 15px" @selection-change="handleSelectionChange_file">
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="设备编号" prop="deviceTerminal.serialNumber" />
                <el-table-column align="center" label="报警名称" prop="name" />
                <el-table-column align="center" label="设备厂家" prop="deviceTerminal.manufacturer" width="250" />
                <el-table-column :formatter="(row) => formDict(assetList, row.assetType)" align="center" label="资产类型" prop="assetType" />
                <el-table-column align="center" label="资产编号" prop="assetNo" />
                <el-table-column align="center" label="报警时间" prop="alarmTime" width="180">
                    <template #default="scope">
                        <span>{{ moment(scope.row.alarmTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>
                <el-table-column :formatter="(row) => formDict(messageTypeList, row.messType)" align="center" label="消息类型" prop="messType" />
                <el-table-column :formatter="(row) => formDict(quotationTypeList, row.messType)" align="center" label="发送状态" prop="sendStatus" />
                <el-table-column align="center" label="信息内容" prop="content" width="300">
                    <template #default="scope">
                        <el-tooltip :content="scope.row.content" class="box-item" effect="dark" placement="top">
                            <span class="information">{{ scope.row.content }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance();
import ALARMApi from '@/api/management/ALARM';
import moment from 'moment';
import qs from 'qs';
import SearchButton from '@/components/searchModule/SearchButton.vue';
// 查询参数
const queryParams = ref({
    current: 1,
    size: 10,
    total: 0
});
// 显示搜索条件
const showSearch = ref(true);

const isShowAll = ref(false);

const chooseList = ref([]);
const handleSelectionChange_file = (key) => {
    chooseList.value = key;
};
// 导出
const newFilArr = ref([]);

//查询展开收缩
function showAllClick() {
	isShowAll.value = !isShowAll.value;
}

function exportList() {
    if (chooseList.value.length > 0) {
        // let selectIds = "";
        // newFilArr.value = [];
        // chooseList.value.filter((item) => {
        //     newFilArr.value.push(item.id)
        // })
        // selectIds = newFilArr.value.join(",");
        let list = {
            // id: selectIds,
            filename: '报警信息记录'
            // exportFields: ['deviceTerminal.serialNumber', 'name', 'deviceTerminal.manufacturer', 'assetType', 'assetNo', 'alarmTime', 'messType', 'sendStatus', 'content']
        };
        ALARMApi.alarmLogExport(qs.stringify(list, { arrayFormat: 'repeat' }), '', '', 'blob')
            .then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '报警信息记录.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    this.$message.error('导出异常请联系管理员');
                }
            })
            .catch((err) => {
                proxy.msgError(err.msg);
            });
    } else {
        proxy.msgError('请选择需要导出的数据！');
    }
}

// 设备状态
const deviceStatusList = ref([]);
// 使用状态
const useList = ref([]);
// 消息类型类型
const messageTypeList = ref([]);
// 资产类型
const assetList = ref([]);
// 发送状态
const quotationTypeList = ref([]);
// 设备厂家
const manufacturerList = ref([]);
// 字典请求
const getDict = async () => {
    deviceStatusList.value = await proxy.getDictList('incubator_status');
    useList.value = await proxy.getDictList('incubator_use_status');
    messageTypeList.value = await proxy.getDictList('alarm_message_type');
    assetList.value = await proxy.getDictList('asset_type');
    quotationTypeList.value = await proxy.getDictList('quotation_sending_status_type');
    manufacturerList.value = await proxy.getDictList('equipment_manufacturer');
};
getDict();

// 表单重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    getList();
}

getList();
// 查询列表
const incubatorList = ref([]);
const timeList = ref({});

//字典回显
const formDict = (data, val) => {
    return data && val ? proxy.selectDictLabel(data, val) : '--';
};

function getList() {
    if (queryParams.value.timeList) {
        timeList.value = {
            beginAlarmTime: queryParams.value.timeList[0],
            endAlarmTime: queryParams.value.timeList[1]
            // beginPrintEndTime: queryParams.value.timeList[1],
            // endPrintEndTime: queryParams.value.timeListe[1]
        };
    }
    let data = {
        current: queryParams.value.current,
        size: queryParams.value.size,
        total: queryParams.value.total,
        deviceNo: queryParams.value.deviceNo,
        assetType: queryParams.value.assetType,
        deviceManufacturer: queryParams.value.deviceManufacturer,
        assetNo: queryParams.value.assetNo,
        messType: queryParams.value.messType,
        sendStatus: queryParams.value.sendStatus,
        beginAlarmTime: queryParams.value.timeList ? timeList.value.beginAlarmTime : null,
        endAlarmTime: queryParams.value.timeList ? timeList.value.endAlarmTime : null
    };
    console.log(data);
    ALARMApi.alarmLogList(data)
        .then((res) => {
            if (res.code == 200) {
                incubatorList.value = res.data.records;
                queryParams.value.total = res.data.total;
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.define {
    width: 550px;
}

.drawer-footer {
    position: absolute;
    bottom: 20px;
    left: 300px;
}

.information {
    overflow: hidden;
    /* 隐藏超出长度的文本 */
    white-space: nowrap;
    /* 禁止文本自动换行 */
    text-overflow: ellipsis;
    /* 超出长度时省略号代替 */
    display: inline-block;
    /* 让样式作用于行内元素 */
    width: 250px;
    /* 设置元素的固定宽度 */
}
</style>
