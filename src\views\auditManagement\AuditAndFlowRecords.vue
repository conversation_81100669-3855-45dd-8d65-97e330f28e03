<template>
    <div class="logQuery">
        <el-empty v-if="dataList.length == 0" description="暂无数据" />
        <el-table v-else :data="dataList" style="width: 100%">
            <el-table-column prop="createDate" label="时间" width="150">
                <template #default="{ row }">
                    <div class="flex justify-between items-center">
                        <span class="round"></span><span>{{ row.createDate }}</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="nodeName" label="操作节点" width="100" />
            <el-table-column prop="auditUserName" label="操作人" width="180" show-overflow-tooltip />
            <el-table-column prop="status" label="审批结果" width="100">
                <template #default="{ row }">
                    <span :class="getTagType(row.status)">{{ selectDictLabel(auditStatusOptions, row.status) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="auditIdea" label="审批意见" min-width="300" />
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'AuditAndFlowRecords',
    components: {},
    data() {
        return {
            dataList: [],
            auditStatusOptions: [
                {
                    name: '已通过',
                    value: '1'
                },
                {
                    name: '已驳回',
                    value: '2'
                }
            ] // 审批状态
        };
    },
    computed: {
        /**
         * 审核状态文字颜色
         * @return string
         */
        getTagType() {
            return (status) => {
                switch (status) {
                    case '1':
                        return 'text-green-500';
                    case '2':
                        return 'text-red-500';
                    default:
                        return 'text-main-500';
                }
            };
        }
    },
    async created() {},
    methods: {
        /**
         * 数据部分
         */
        dateData(property, bol) {
            return function (a, b) {
                var value1 = a[property];
                var value2 = b[property];
                if (bol) {
                    if (value1 == value2) {
                        return Date.parse(value2) - (Date.parse(value1) + 1);
                    }
                    // 升序
                    return Date.parse(value1) - Date.parse(value2);
                } else {
                    // 降序
                    return Date.parse(value2) - Date.parse(value1);
                }
            };
        },
        timeFns(data) {
            this.dataList = data.map((item) => {
                item.createDate = this.transformTimestamps(item.createDate);
                return item;
            });
            this.dataList.sort(this.dateData('createDate', true));
        },
        transformTimestamp(timestamp) {
            let a = new Date(timestamp).getTime();
            const date = new Date(a);
            const Y = date.getFullYear() + '-';
            const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + '  ';
            const dateString = Y + M + D;
            return dateString;
        },
        transformTimestamps(timestamp) {
            let a = new Date(timestamp).getTime();
            const date = new Date(a);
            const Y = date.getFullYear() + '-';
            const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + '  ';
            const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
            const m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
            const dateString = Y + M + D + h + m;
            return dateString;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .logQuery {
        height: 200px;
        overflow-y: scroll;
        .el-timeline {
            padding-left: 10px;
            .el-timeline-item:last-child p {
                color: #0f87ff;
            }
        }
    }
}
.round {
    display: block;
    border: 2px solid #0f87ff;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}
</style>
