import request from '@/utils/request'
export default {
    // 列表
    getList(params) {
        return request.get('/tms/transport/record/list', params);
    },
    // 详情
    getDetail(params) {
        return request.get('/tms/transport/record/detail', params);
    },
    // 修改-查询数据
    getEditData(params) {
        return request.get('/tms/transport/record/queryById', params);
    },
    // 修改
	saveData(params) {
        return request.post('/tms/transport/record/edit', params);
    },
	// 附件下载
	download(params, config, resDetail, responseType) {
		return request.get('/tms/transport/record/batchDownload', params, config, resDetail, responseType);
	},
	// 导出
	exportData(params, config, resDetail, responseType) {
		return request.get('/tms/transport/record/export', params, config, resDetail, responseType);
	},
    // 获取修改记录
    getChangeRecord(params) {
        return request.get('/tms/transport/record/getChangeRecord', params);
    },
    // 获取图片文件流
    getImageFile(params, config, resDetail, responseType) {
        return request.get('/tms/transport/record/download', params, config, resDetail, responseType);
    }
};
