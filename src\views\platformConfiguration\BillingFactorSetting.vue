<template>
  <div class="app-container">
    <!--  /搜索区域  -->
    <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
      <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
        <el-form-item label="计费因子" prop="variableName">
          <el-input v-model="queryParams.variableName" clearable placeholder="请输入计费因子" @clear="handleQuery" @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item class="last-form-item">
          <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" type="info" @click="resetQuery('queryForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- / 表格内容 -->
    <el-card :body-style="{ padding: '10px' }" shadow="never">
      <div style="margin-bottom: 10px">
        <el-button type="primary" @click="openTheNewChargingFactor('add')">新增</el-button>
        <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="billingFactor" @queryTable="getList" />
      </div>
      <column-table v-loading="loading" :columns="columns" :data="orderList" :showIndex="true">
        <template #status="{ row }">
          <el-switch v-if="row.status" v-model="row.status" active-color="#13ce66" inline-prompt active-text="启用" active-value="0" inactive-text="禁用" inactive-value="1" @change="changeStatus(row)" />
          <!--          <el-button icon="el-icon-edit" plain  style="padding: 5px 10px" type="primary" @click="openTheNewChargingFactor('edit', row)">修改</el-button>
          <el-button icon="el-icon-delete" plain  style="padding: 5px 10px" type="danger" @click="openTheDeletionConfirmationBox(row)">删除</el-button>-->
        </template>
      </column-table>
      <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
    </el-card>

    <!-- /新增计费因子 抽屉  -->
    <el-drawer v-if="newBillingFactorVisible" v-model="newBillingFactorVisible" :title="newBillingFactorTitle" size="500px" @close="hideNewChargingFactors()">
      <div v-loading="newBillingFactorLoading" :element-loading-text="newBillingFactorLoadingText" style="background-color: #f2f2f2; padding: 10px">
        <el-card shadow="never">
          <el-form ref="newBillingFactorForm" :model="newBillingFactorForm" :rules="newBillingFactorRules" class="newBillingFactorForm" label-width="auto">
            <el-form-item label="映射关系" prop="mappingRelation">
              <el-select v-model="newBillingFactorForm.mappingRelation" clearable filterable placeholder="请选择映射关系" placement="top" style="width: 100%" @change="changeMappingRelation">
                <el-option v-for="item in mappingRelationList" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="变量名（中文）" prop="variableName">
              <el-input v-model="newBillingFactorForm.variableName" clearable disabled />
            </el-form-item>
            <el-form-item label="单位" prop="variableUnit">
              <el-select v-model="newBillingFactorForm.variableUnit" clearable placeholder="请选择单位" style="width: 100%">
                <el-option v-for="item in unitList" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="变量说明" prop="description">
              <el-input v-model="newBillingFactorForm.description" :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入变量说明" type="textarea"></el-input>
            </el-form-item>
          </el-form>
        </el-card>
        <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
          <el-button type="info" @click="hideNewChargingFactors()">取消</el-button>
          <el-button v-if="newBillingFactorModify" type="primary" @click="confirmToAdd()">修改</el-button>
          <el-button v-else type="primary" @click="confirmModification()">确定</el-button>
        </div>
      </div>
    </el-drawer>

    <!--  /删除计费因子 对话框  -->
    <el-dialog v-model="deleteBillingFactorVisible" append-to-body center class="dialog__deleteBillingFactor" title="删除计费因子" width="500px">
      <el-result v-loading="deleteBillingFactorLoading" :element-loading-text="deleteBillingFactorLoadingText" icon="warning" title="您确定删除吗？">
        <template slot="extra">
          <el-button @click="deleteBillingFactorVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmToDeleteChargingFactor()">确认</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import billingFactorSettings from '@/api/platformFeatures/billingFactorSettings';

export default {
  name: 'BillingFactorSetting',
  components: {
    RightToolbar,
    ColumnTable
  },
  data() {
    return {
      showSearch: true,
      queryParams: {
        current: 1,
        size: 10,
        variableName: null
      },
      columns: [
        { title: '变量名称', key: 'variableName', align: 'center', columnShow: true },
        { title: '变量说明', key: 'description', align: 'center', columnShow: true },
        { title: '单位', key: 'variableUnit', align: 'center', columnShow: true },
        { title: '状态', key: 'status', align: 'center', width: '120px', columnShow: true, hideFilter: true, fixed: 'right' }
      ],
      loading: false,
      orderList: [],
      total: 0,
      newBillingFactorVisible: false,
      newBillingFactorTitle: '新增计费因子',
      newBillingFactorLoading: false,
      newBillingFactorLoadingText: '新增中...',
      newBillingFactorForm: {
        variableName: '',
        description: '',
        mappingRelation: '',
        mappingTable: '',
        mappingColumn: '',
        variableUnit: ''
      },
      newBillingFactorRules: {
        variableName: [{ required: true, message: '请输入变量名（中文）', trigger: 'blur' }],
        mappingRelation: [{ required: true, message: '请选择映射关系', trigger: 'change' }],
        description: [{ required: true, message: '请输入变量说明', trigger: 'blur' }],
        variableUnit: [{ required: true, message: '请选择单位', trigger: 'change' }]
      },
      newBillingFactorModify: false,
      deleteBillingFactorVisible: false,
      deleteBillingFactorLoading: false,
      deleteBillingFactorLoadingText: '删除中...',
      mappingRelationList: [],
      deleteBillingFactorId: '',
      unitList: []
    };
  },
  created() {
    this.getDict();
    this.getList();
  },
  methods: {
    /**
     * 获取字典值
     * @returns {Promise<void>}
     */
    async getDict() {
      this.mappingRelationList = await this.getDictList('cost_factor_mapping');
      this.unitList = await this.getDictList('cost_param_factor_unit');
    },
    changeMappingRelation(e) {
      console.log(e)
      // this.mappingRelationList code 等于e 的 name 赋值给 this.newBillingFactorForm.variableName
      this.newBillingFactorForm.variableName = this.mappingRelationList.find((item) => item.code === e)?.name || '';
      // 根据'.'拆分e 分别赋值给mappingTable mappingColumn
      const arr = e.split('.');
      if (arr.length === 2) {
        this.newBillingFactorForm.mappingTable = arr[0];
        this.newBillingFactorForm.mappingColumn = arr[1];
      } else {
        this.msgError('映射关系格式错误');
      }
    },
    // 确定修改计费因子
    confirmModification() {
      this.$refs.newBillingFactorForm.validate((valid) => {
        if (valid) {
          this.newBillingFactorLoading = true;
          this.newBillingFactorLoadingText = '修改中...';
          billingFactorSettings.addBillingFactor(this.newBillingFactorForm)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({ title: '成功', message: '新增计费因子成功', type: 'success', duration: 2000 });
                this.getList();
                this.hideNewChargingFactors();
              } else {
                this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
              }
            })
            .finally(() => {
              this.newBillingFactorLoading = false;
            });
        }
      });
    },
    // 确定新增计费因子
    confirmToAdd() {
      this.$refs.newBillingFactorForm.validate((valid) => {
        if (valid) {
          this.newBillingFactorLoading = true;
          this.newBillingFactorLoadingText = '新增中...';
          updateBillingFactor(this.newBillingFactorForm)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({ title: '成功', message: '修改计费因子成功', type: 'success', duration: 2000 });
                this.getList();
                this.hideNewChargingFactors();
              } else {
                this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
              }
            })
            .finally(() => {
              this.newBillingFactorLoading = false;
            });
        }
      });
    },
    // 确定删除计费因子
    confirmToDeleteChargingFactor() {
      this.deleteBillingFactorLoading = true;
      deleteBillingFactor(this.deleteBillingFactorId)
        .then((res) => {
          if (res.code === 200) {
            this.$notify({ title: '成功', message: '删除计费因子成功', type: 'success', duration: 2000 });
            this.getList();
            this.deleteBillingFactorVisible = false;
          } else {
            this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
          }
        })
        .finally(() => {
          this.deleteBillingFactorLoading = false;
        });
    },
    // 获取详情
    getDetail(id) {
      this.newBillingFactorLoading = true;
      billingFactorDetail(id)
        .then((res) => {
          if (res.code === 200) {
            // res.data.mappingTable 与 res.data.mappingColumn 用'.'拼接
            Object.assign(this.newBillingFactorForm, res.data, {
              mappingRelation: res.data.mappingTable + '.' + res.data.mappingColumn
            });
          }
        })
        .finally(() => {
          this.newBillingFactorLoading = false;
        });
    },
    getList() {
      this.loading = true;
      billingFactorSettings
        .getBillingFactorList(this.queryParams)
        .then((res) => {
          if (res.code === 200 && res.data.records) {
            this.orderList = res.data.records || [];
            this.total = res.data.total || 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    // 关闭新增计费因子弹窗
    hideNewChargingFactors() {
      this.newBillingFactorVisible = false;
      this.$refs.newBillingFactorForm.resetFields();
      this.newBillingFactorForm = {
        variableName: '',
        description: '',
        mappingRelation: '',
        mappingTable: '',
        mappingColumn: ''
      };
    },
    openTheDeletionConfirmationBox(row) {
      const { id } = row;
      if (id) {
        this.deleteBillingFactorVisible = true;
        this.deleteBillingFactorId = id;
      } else {
        this.$notify({ title: '失败', message: '计费因子不存在', type: 'error', duration: 2000 });
      }
    },
    // 打开新增计费因子弹窗
    openTheNewChargingFactor(type, row) {
      this.newBillingFactorVisible = true;
      if (type === 'add') {
        this.newBillingFactorTitle = '新增计费因子';
        this.newBillingFactorLoadingText = '新增中...';
        // 关闭修改状态
        this.newBillingFactorModify = false;
      } else if (type === 'edit') {
        this.newBillingFactorTitle = '修改计费因子';
        this.newBillingFactorLoadingText = '加载中...';
        const { id } = row;
        // 查询计费因子详情
        if (id) {
          this.getDetail(id);
        } else {
          this.$notify({ title: '失败', message: '计费因子不存在', type: 'error', duration: 2000 });
        }
        // 打开修改状态
        this.newBillingFactorModify = true;
      }
    },
    resetQuery(formName) {
      this.$refs[formName].resetFields();
      this.handleQuery();
    },
    // 修改计费因子状态
    changeStatus(row) {
      const { id, status } = row;
      if (id) {
        this.newBillingFactorLoading = true;
        this.newBillingFactorLoadingText = '修改中...';
        const params = {
          id,
          status
        };
        billingFactorSettings.updateBillingFactorStatus(params)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success('修改成功');
              this.getList();
            } else {
              this.$message.error(res.msg);
            }
          })
          .finally(() => {
            this.newBillingFactorLoading = false;
          });
      } else {
        this.$message.error('计费因子不存在');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}

.dialog__deleteBillingFactor {
  :deep(.el-dialog__header) {
    padding-bottom: 20px;
  }
  :deep(.el-result) {
    padding: 0;
  }
}
</style>
