import request from '@/utils/request';
import http from '@/utils/request';

export default {
    // 医药下单
    saveOrderDrug: function (data) {
        return request.post('/tms/orderDrug/save', data);
    },
    // 订单批量提交
    batchSubmitOrderDrug: function (data) {
        return request.post('/tms/orderDrug/batchSubmit', data);
    },
    // 取消订单
    cancelOrder: function (data) {
        return request.post('/tms/orderDrug/cancelOrder', data);
    },
    // 订单明细导入模板
    importTemplate: function (params, config, resDetail, responseType) {
        return request.get('/tms/orderDetails/import/template', params, config, resDetail, responseType, 1);
    },
    // 订单详情
    queryOrderDrugById: function (params) {
        return request.get('/tms/orderDrug/queryById', params);
    },
    // 签收及批量签收订单
    batchSignOrder: function (data) {
        return request.post('/tms/orderDrug/batchSignOrder', data);
    },
    // 订单列表
    listOrderDrug: function (params) {
        return request.get('/tms/orderDrug/list', params);
    },
    // 订单列表查询箱码-分页
    listTransBoxCode: function (params) {
        return request.get('/tms/transBoxCode/list', params);
    },
    // 订单列表查询箱码-全部
    getBoxCodeList: function (params) {
        return request.get('/tms/transBoxCode/getBoxCodeList', params);
    },
    // 更新箱码打印次数
    changePrintNum: function (params) {
        return request.get('/tms/transBoxCode/changePrintNum', params);
    },
    // 确认订单
    confirmOrder: function (data) {
        return request.post('/tms/orderDrug/confirmOrder', data);
    },
    // 订单列表获取订单轨迹
    getTransRecord: function (params) {
        return request.get('/tms/transRecord/getTransRecord', params);
    },

    // 地址薄列表查询
    listAddressBook: function (params) {
        return request.get('/tms/config/addressBook/list', params);
    },

    // 下单增值服务
	getOrderAddedService:function (params) {
		return request.get('/cost/owner/added/service/list', params);
	},
    // 计算增值服务费用
    calculateAddedService: function (data) {
        return request.post('/cost/addedService/carrier/calculate', data);
    },
    // 订单异动
    orderTransaction: function (data) {
        return request.post('/tms/orderChange/changeOrder', data);
    },
    // 订单异动记录
    getTransactionRecords: function (params) {
        return request.get('/tms/orderChange/list', params);
    },
    // 根据货主查询承运商下拉框
    carrierSelectList: function (params) {
        return request.get('/tms/cooperate/company/carrierSelect', params);
    },
    // 根据区县地址id查询网点和区域信息
    getOrderCost: function (params) {
        return request.get('/tms/area/getByCountyId', params);
    },
    // 订单费用计算
    getCostCalculation: function (data) {
        return request.post('/cost/order/costDetailed/calculate', data);
    },

    // 获取订单费用明细
    orderDetailCostInfo: function (params, config, resDetail, responseType) {
        return http.get('/cost/order/costDetailed/orderDetailCostInfo', params, config, resDetail, responseType);
    },
    // 订单列表导出
    exportOrderData: function (params, config, resDetail, responseType) {
        return http.get('/tms/orderDrug/export', params, config, resDetail, responseType);
    },

    // 订单列表导出(新)
    newExportOrderData: function (params, config, resDetail, responseType) {
        return http.get('/tms/orderDrug/newExport', params, config, resDetail, responseType);
    },

    // 收藏订单或取消收藏
    collectOrder: function (data) {
        return request.post('/tms/orderDrug/collectOrder', data);
    },
    // 根据运单号打印传感器温度数据
    printTmpList: function (params) {
        return http.get('/device/tmp/printTmpList', params);
    },
    // 打印
    printPdfTmpList: function (params, progressEvent) {
        let config = {
            headers: { 'X-Requested-With': 'XMLHttpRequest' },
            onDownloadProgress: progressEvent
        };
        return http.get('/device/tmp/printPdfTmpList', params, config, false, 'blob');
    },
    // 打印数据
    tmpPrintTmpList: function (params) {
        return http.get('/device/tmp/printPdfTmpList', params);
    },
    // 修改订单-保存异动项
    saveTheTransaction: function (data) {
        return request.post('/tms/orderUpdate/add', data);
    },
    // 根据运单号打印传感器温度数据
    orderModificationDetails: function (params) {
        return http.get('/tms/orderUpdate/detail', params);
    },
    // 根据Id获取设备打印异常记录数据
    printLogQueryById: function (params) {
        return http.get('/device/printLog/queryById', params);
    },
    // 查询设备模板数据
    getTemplateData: function (params) {
        return http.get('/device/temp/getTemplateData', params);
    },
    // 温度条打印
    devicePrint: function (data, progressEvent) {
        let config = {
            headers: { 'X-Requested-With': 'XMLHttpRequest' },
            responseType: 'blob',
            onDownloadProgress: progressEvent
        };
        return request.post('/device/temp/getInputStream', data, config, false);
    },
    // 获取温湿度打印数据
    packColdChainPassOnSheet: function (params) {
        return http.get('/tms/receipt/incubatorRecord/packColdChainPassOnSheet', params);
    },
    // 打印冷链交接单-修改打印冷链交接单标识
    updatePrintDeliveryFlag: function (params) {
        return http.get('/tms/orderDrug/update-printDeliveryFlag', params);
    },

    // 仓储订单列表
    listStorageOrder: function (params) {
        return request.get('/tms/orderDrug/wms/list', params);
    },
    // 仓储订单导出
    exportStorageOrder: function (params, config, resDetail, responseType) {
        return http.get('/tms/orderDrug/wms/export', params, config, resDetail, responseType);
    },
    // 设备打印记录pdf打印
    deviceExportPdf: function (params, progressEvent) {
        let config = {
            headers: { 'X-Requested-With': 'XMLHttpRequest' },
            onDownloadProgress: progressEvent
        };
        return http.get('/tms/device-print/export-pdf', params, config, false, 'blob');
    },
    // 件数统计
    statisticsOfItems: function (params) {
        return http.get('/tms/orderDrug/count/stats', params);
    },

};
