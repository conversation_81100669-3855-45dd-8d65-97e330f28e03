<template>
  <el-dialog
    :title="titleName"
    v-model="visible"
    :width="500"
    destroy-on-close
    @closed="$emit('closed')"
  >
    <el-form
      v-loading="dialogLoading"
      :model="form"
      :rules="rules"
      :disabled="mode == 'view'"
      ref="dialogForm"
      label-width="100px"
    >
      <el-form-item label="组织机构" prop="sysOrg.name">
        <el-input
          v-model="form.sysOrg.name"
          placeholder="请选择组织机构"
          clearable
          disabled
        ></el-input>
      </el-form-item>
      <el-form-item label="归属部门" prop="sysOffice.name">
        <span
          ><el-input
            v-model="form.sysOffice.name"
            placeholder="请选择归属部门"
            clearable
            disabled
          ></el-input>
          <el-button @click="openSysOfficeDialog">选择</el-button></span
        >
      </el-form-item>
      <el-form-item
        label="姓名"
        prop="name"
        :rules="[{ required: true, message: '姓名不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.name" placeholder="请输入姓名" clearable></el-input>
      </el-form-item>
      <el-form-item
        label="登录名"
        prop="loginName"
        :rules="[{ required: true, message: '登录名不能为空', trigger: 'blur' }]"
      >
        <el-input
          v-model="form.loginName"
          placeholder="请输入登录名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item
        label="密码"
        prop="password"
        :rules="[{ required: true, message: '密码不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.password" placeholder="请输入密码" clearable></el-input>
      </el-form-item>
      <el-form-item
        label="邮箱"
        prop="email"
        :rules="[{ required: true, message: '邮箱不能为空', trigger: 'blur' }, { pattern: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/, message: '邮箱格式不正确', trigger: 'blur' }]"
      >
        <el-input v-model="form.email" placeholder="请输入邮箱" clearable></el-input>
      </el-form-item>
      <el-form-item
        label="电话"
        prop="phone"
        :rules="[{ required: true, message: '电话不能为空', trigger: 'blur' },{ pattern: /^1[3456789]\d{9}$/, message: '电话号码格式不正确', trigger: 'blur' }  ]"
      >
        <el-input v-model="form.phone" placeholder="请输入电话" clearable></el-input>
      </el-form-item>
      <el-form-item
        label="用户头像"
        prop="photo"
        :rules="[{ required: true, message: '用户头像不能为空', trigger: 'blur' }]"
      >
        <el-input v-model="form.photo" placeholder="请输入用户头像" clearable></el-input>
      </el-form-item>

      <el-form-item label="是否可登录" prop="loginFlag">
        <el-switch v-model="form.loginFlag" />
      </el-form-item>
      <el-form-item label="是否是管理员" prop="admin">
        <el-switch v-model="form.admin" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        v-if="mode != 'view'"
        type="primary"
        :loading="isSubmiting"
        @click="submit()"
        >保 存</el-button
      >
    </template>
    <sysOfficeSelect
      ref="sysOfficeSelected"
      v-if="sysOfficeSelectDialog"
      :isMultiple="false"
      draggable
      @closed="sysOfficeSelectDialog = false"
      :selectChange="officeSelectChange"
    ></sysOfficeSelect>
  </el-dialog>
</template>

<script>
export default {
  props: {
    //回调函数
    callback: { type: Function },
  },
  data() {
    return {
      //表单类型
      mode: "add",
      //表单标题
      titleName: "",
      //是否显示或隐藏表单弹框
      visible: false,
      //提交中
      isSubmiting: false,
      //弹框加载中
      dialogLoading: false,
      //表单数据
      form: {},
      sysOfficeSelectDialog: false,
    };
  },
  mounted() {},
  methods: {
    /*
     * 添加视图
     * @author: 路正宁
     * @date: 2023-03-24 13:20:15
     */
    addView(sysOrg) {
      //设置标题
      this.titleName = "添加";
      this.mode = "add";
      //显示表单
      this.visible = true;
      //释放提交按钮状态
      this.isSubmiting = false;
      //释放弹框加载
      this.dialogLoading = false;
      //初始化表单
      this.setForm({ sysOrg: sysOrg });
    },
    /*
     * 编辑视图
     * @author: 路正宁
     * @date: 2023-03-24 13:20:35
     */
    async editView(formData) {
      //设置标题
      this.titleName = "编辑";
      this.mode = "edit";
      //显示表单
      this.visible = true;
      //释放提交按钮
      this.isSubmiting = false;
      //页面加载中
      this.dialogLoading = true;
      //重新查询
      var res = await this.$API.sysUserService.queryById(formData.id);
      if (res.code == 200) {
        //设置表单数据
        this.setForm(res.data);
      } else {
        this.$Response.errorNotice(res, "查询失败");
        //锁定提交按钮
        this.isSubmiting = true;
      }
      //释放页面加载中
      this.dialogLoading = false;
    },
    /*
     * 查看视图
     * @author: 路正宁
     * @date: 2023-03-24 13:21:14
     */
    view(formData) {
      //设置标题
      this.titleName = "查看";
      this.mode = "view";
      //显示表单
      this.visible = true;
      //释放提交按钮状态
      this.isSubmiting = false;
      //释放弹框加载
      this.dialogLoading = false;
      //设置表单数据
      this.setForm(formData);
    },
    /*
     * 设置表单
     * @author: 路正宁
     * @date: 2023-04-04 16:19:17
     */
    setForm(data) {
      this.form = data;
      if (this.$ObjectUtils.isEmpty(this.form.sysOrg)) {
        this.form.sysOrg = {
          id: "",
          name: "",
        };
      }
      if (this.$ObjectUtils.isEmpty(this.form.sysOffice)) {
        this.form.sysOffice = {
          id: "",
          name: "",
        };
      }
      if (this.$ObjectUtils.isEmpty(this.form.loginFlag)) {
        this.form.loginFlag = true;
      }
      if (this.$ObjectUtils.isEmpty(this.form.admin)) {
        this.form.admin = false;
      }
	  this.form.password="";
    },
    /*
     * 表单提交
     * @author: 路正宁
     * @date: 2023-03-24 14:11:20
     */
    async submit() {
      //表单校验
      var valid = await this.$refs.dialogForm.validate().catch(() => {});
      if (!valid) {
        return false;
      }
	  // if(this.$ObjectUtils.isNotEmpty(this.form.password)){
		//   this.form.password=this.$TOOL.crypto.MD5(this.form.password);
	  // }
      //锁定提交按钮
      this.isSubmiting = true;
      var res = await this.$API.sysUserService.save({...this.form,password:this.$TOOL.crypto.MD5(this.form.password)});
      if (res.code == 200) {
        //关闭页面
        this.visible = false;
        this.$message.success("操作成功");
        //回调函数
        this.callback(res.data, this.mode);
      } else {
        this.$Response.errorNotice(res, "保存失败");
      }
      //释放提交按钮
      this.isSubmiting = false;
    },
    /*
     * 打开部门选择框
     * @author: 路正宁
     * @date: 2023-04-03 10:27:55
     */
    openSysOfficeDialog() {
      var datas = [];
      datas.push(this.form.sysOffice);
      this.sysOfficeSelectDialog = true;
      this.$nextTick(() => {
        this.$refs.sysOfficeSelected.init(this.form.sysOrg, datas);
      });
    },

    /*
     * 部门弹框回调选择事件
     * @author: 路正宁
     * @date: 2023-04-03 10:02:56
     */
    officeSelectChange(officeList) {
      if (officeList.length > 0) {
        this.form.sysOffice = officeList[0];
      } else {
        this.form.sysOffice = {
          id: "",
          name: "",
        };
      }
    },
  },
};
</script>

<style></style>
