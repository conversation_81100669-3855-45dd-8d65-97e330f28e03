<template>
  <el-dialog v-model="dialogTableVisible" :title="`查看详情 (${echo1(data.editStrs.orderHeader)})`" width="80%">
    <div class="demo-collapse">
      <el-collapse v-model="activeNames">
        <span slot="title" class="stateTitle">
          <el-button v-if="!logFlag && data.editStrs.orderHeader.status == '3'" @click="againFn">单据复制</el-button>
        </span>
        <el-collapse-item name="1">
          <template #title>
            <span class="col_title">表头信息</span>
          </template>
          <table border="0" cellpadding="0" cellspacing="1" class="messTable">
            <tr>
              <td>客户</td>
              <td>{{ formInline1.n1 }}</td>
            </tr>
            <tr>
              <td>客户代表</td>
              <td>{{ formInline1.n2 }}</td>
            </tr>
            <tr>
              <td>结算方式</td>
              <td>{{ echoType(data.clearingFormType, formInline1.n3, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>收款期限</td>
              <td>{{ functionIndex.transformTimestamp(formInline1.n4) }}</td>
            </tr>
            <!--						<tr>-->
            <!--							<td>发票类型</td>-->
            <!--							<td>{{ echoType(data.invoice, formInline1.n5, 'value', 'name') }}</td>-->
            <!--						</tr>-->
            <!--						<tr>-->
            <!--							<td>业务类型</td>-->
            <!--							<td>{{ echoType(data.business, formInline1.n6, 'value', 'name') }}</td>-->
            <!--						</tr>-->
            <tr>
              <td>物流方式</td>
              <td>{{ echoType(data.logistics, formInline1.n7, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>三方物流</td>
              <td>{{ echoType(data.TripartiteLogistics, formInline1.n8, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>收货地址</td>
              <td>{{ formInline1.n9.toString() }}</td>
            </tr>
            <tr>
              <td>详细地址</td>
              <td>{{ formInline1.n10 }}</td>
            </tr>
            <tr>
              <td>收货人</td>
              <td>{{ formInline1.n11 }}</td>
            </tr>
            <tr>
              <td>收货人电话</td>
              <td>{{ formInline1.n12 }}</td>
            </tr>
            <tr>
              <td>库号</td>
              <td>{{ formInline1.n13 }}</td>
            </tr>
            <tr>
              <td>经手人</td>
              <td>{{ formInline1.n14 }}</td>
            </tr>
            <tr>
              <td>自营扣率</td>
              <td>{{ echoType(data.discount, formInline1.n15, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>制单人</td>
              <td>{{ formInline1.n16?.name }}</td>
            </tr>
            <tr>
              <td>折扣金额</td>
              <td>￥{{ formInline1.n19.toFixed(2) }}</td>
            </tr>
            <tr>
              <td>合同模板</td>
              <td>{{ echoType(data.contract, formInline1.n21, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>备注</td>
              <td>{{ formInline1.n17 }}</td>
            </tr>
            <tr style="border-left: 1px solid #e9ecf1"></tr>
          </table>
          <div style="margin-top: 20px">
            <el-checkbox v-model="formInline1.n20" :disabled="true" label="是否开具发票"/>
            <el-checkbox v-model="formInline1.n22" :disabled="true" label="是否生成合同"/>
          </div>
        </el-collapse-item>
        <el-collapse-item name="2">
          <template #title>
            <span class="col_title">细单信息</span>
          </template>

          <el-table :cell-style="{ textAlign: 'center' }" :data="addGoods.allGoods"
                    :header-cell-style="{ 'text-align': 'center' }" border style="width: 100%">
            <el-table-column fixed label="序号" prop="tag" width="80px">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="自编码" prop="commodity.commoditySelfCode" width="120px"/>
            <el-table-column label="商品名称" prop="commodity.tradeName" width="120px"/>
            <el-table-column label="规格" prop="commodity.packageSpecification" width="120px"/>
            <el-table-column label="产地" prop="commodity.originPlace" width="120px"/>
            <el-table-column label="单位" prop="commodity.basicUnit" width="120px"/>
            <el-table-column label="有效期(月)" prop="commodity.validityTime" width="120px"/>
            <el-table-column fixed="right" label="数量" prop="date" width="150px">
              <template #default="scope">
                <p style="width: 100%">
                  <span>{{ scope.row.num.str }}</span>
                </p>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="单价" prop="date" width="150px">
              <template #default="scope">
                <p style="width: 100%">
                  <span>
                    {{ scope.row.price.str.toFixed(2) }}
                  </span>
                </p>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="单价合计金额" prop="date" width="120px">
              <template #default="scope">
                {{ scope.row.total.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="入库数量" prop="intoQuantity" width="120px"/>
            <el-table-column label="库存余量" prop="inventoryBalance" width="120px"/>
            <el-table-column label="可开数量" prop="openableQuantity" width="120px"/>
            <el-table-column label="件装量" prop="commodity.ratio" width="120px"/>
            <!--							TODO:比率/数量-->
            <el-table-column label="件数" prop="pieceNumber" width="120px"></el-table-column>
            <el-table-column label="最后一次销售价格" prop="lastSalesPrice" width="140px"/>
            <el-table-column label="类型" prop="commodity.grugsType" width="120px"/>
            <el-table-column label="生产厂家" prop="manufacture.enterpriseName" width="120px"/>
            <el-table-column label="供应商" prop="supplier.enterpriseName" width="120px"/>
            <el-table-column label="批准文号" prop="approvalNumber" width="120px"/>
            <el-table-column label="税率" prop="commodity.taxRate" width="120px"/>
            <el-table-column label="剂型" prop="commodity.dosageForm" width="120px"/>
            <el-table-column label="入库时间" prop="intoTime" width="150px">
              <template #default="scope">
                {{ transformTimestamp(scope.row.intoTime) }}
              </template>
            </el-table-column>

            <el-table-column label="成本单价" prop="unitPrice" width="120px"/>

            <el-table-column label="贮藏温区" prop="commodity.storageTemperature" width="120px"/>
            <el-table-column label="批号" prop="batchNumber" width="120px"/>
            <el-table-column label="生产日期" prop="produceDate" width="150px">
              <template #default="scope">
                {{ transformTimestamp(scope.row.produceDate) }}
              </template>
            </el-table-column>
            <el-table-column label="过期时间" prop="validityTime" width="120px">
              <template #default="scope">
                {{ transformTimestamp(scope.row.validityTime) }}
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
        <el-collapse-item name="3">
          <template #title>
            <span class="col_title">合计信息</span>
          </template>
          <span style="margin-left: 50px; font-size: 15px">合计数量：<span style="color: red; font-size: 19px">{{
              footForm.num
            }}</span></span>
          <span style="margin-left: 50px; font-size: 15px">合计金额：<span style="color: red; font-size: 19px">{{
              footForm.price.toFixed(2)
            }}</span></span>
          <span style="margin-left: 50px; font-size: 15px">折后总金额：<span style="color: red; font-size: 19px">{{
              footForm.discount.toFixed(2)
            }}</span></span>
        </el-collapse-item>
        <el-collapse-item v-if="!logFlag && data.editStrs.orderHeader.status == '3'" name="7">
          <template #title>
            <span class="col_title">出库记录</span>
          </template>
          <el-table :cell-style="{ textAlign: 'center' }" :data="data.warehouseList"
                    :header-cell-style="{ 'text-align': 'center' }" style="width: 100%">
            <el-table-column label="自编码" prop="commodity.commoditySelfCode"/>
            <el-table-column label="商品名称" prop="commodity.tradeName"/>
            <el-table-column label="出库单编号" prop="outNo" width="170px"/>
            <el-table-column label="出库时间" prop="outTime">
              <template #default="scope">
                {{ functionIndex.transformTimestamp(scope.row.outTime) }}
              </template>
            </el-table-column>
            <el-table-column label="出库数量" prop="outQuantity"/>
            <el-table-column label="批号" prop="batchNumber"/>
            <el-table-column label="单据状态" prop="outStatus"/>
          </el-table>
        </el-collapse-item>
        <el-collapse-item name="4">
          <template #title>
            <span class="col_title">订单配置</span>
          </template>
          <table border="0" cellpadding="0" cellspacing="1" class="messTable">
            <tr>
              <td>三方章选项</td>
              <td>{{ echoType(data.stamp, formInline2.n1, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>货主章选项</td>
              <td>{{ echoType(data.owner, formInline2.n2, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>装货选项</td>
              <td>{{ echoType(data.encasement, formInline2.n3, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>是否拼箱</td>
              <td>{{ formInline2.n4 ? '是' : "否" }}</td>
            </tr>
            <tr>
              <td>单据返回选项</td>
              <td>{{ checkEcho(data.receipts, formInline2.n5, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>留存资料选项</td>
              <td>{{ formInline2.n6 == 0 ? '非装箱联全部保留' : "不保留" }}</td>
            </tr>
            <tr>
              <td>质检报告</td>
              <td>{{ checkEcho(data.testing, formInline2.n7, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>质量单据返回</td>
              <td>{{ checkEcho(data.return, formInline2.n8, 'value', 'name') }}</td>
            </tr>
            <tr>
              <td>收集随货资料</td>
              <td>{{
                  checkEcho(data.gather, formInline2.n9, 'value', 'name')
                }}
              </td>
            </tr>
            <tr>
              <td>备注</td>
              <td>{{ formInline2.n10 }}</td>
            </tr>
            <tr style="border-left: 1px solid #e9ecf1"></tr>
            <tr style="border-left: 1px solid #e9ecf1"></tr>
          </table>
        </el-collapse-item>
        <el-collapse-item v-if="logFlag" name="5">
          <template #title>
            <span class="col_title">操作日志</span>
          </template>
          <LogQuery ref="logQueryRef"/>
        </el-collapse-item>
        <el-collapse-item v-if="!data.formFlag" name="6">
          <template #title>
            <span class="col_title">审批意见</span>
          </template>
          <auditForms ref="auditRef" @refresh="refresh"/>
        </el-collapse-item>
      </el-collapse>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogTableVisible = false">
          取消
        </el-button>
        <el-button v-if="!data.formFlag" type="primary" @click="allRight()"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import {defineProps, getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import {ElLoading, ElMessage} from "element-plus";
import {manageApi} from "@/api/model/salesManagement";
import LogQuery from "@/components/detailsForm/logQuery.vue";
import {functionIndex} from "../../views/commodity/functionIndex";

const dialogTableVisible = ref(false);
const activeNames = ref(["1", '2', '3', '4', '5', '6', '7']);

const logQueryRef = ref(null);
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const auditRef = ref(null);
const emit = defineEmits(["refresh", 'againFn']);

const formInline1 = reactive({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
  n5: "",
  n6: "",
  n7: "",
  n8: "",
  n9: "",
  n10: "",
  n11: "",
  n12: "",
  n13: "",
  n14: "",
  n15: "",
  n16: "",
  n17: "",
  n18: "",
  n19: 0,
  n20: false,
  n21: "",
  n22: false,
  n23: "",
  n24: "",
  n25: "",
});
const formInline2 = reactive({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
  n5: [],
  n6: "",
  n7: [],
  n8: [],
  n9: [],
  n10: "",
});
const footForm = reactive({
  num: 0,
  price: 0,
  discount: 0,
});
const refresh = () => {
  dialogTableVisible.value = false;
  emit("refresh");
};
const data = reactive({
  ids: "",
  title: "",
  tableList: [],
  warehouseList: [],
  formFlag: false,
  deliveryList: [],
  editStrs: {
    orderHeader: {
      status: 0,
    },
  },
  pageNum: 1,
  pageSize: 10,
  total: 0,
  clearingFormType: null,
  invoice: null,
  logistics: null,
  business: null,
  TripartiteLogistics: null,
  contract: null,
  clientType: {
    value: "",
    total: 0,
    pageSize: 5,
    pageNum: 1,
    type: null,
  },
  powerType: null,
  sysAreas: null,
  serial: {
    value: "",
    total: 0,
    pageSize: 5,
    pageNum: 1,
    type: null,
  },
  handle: null,
  myFlag: false,
  discount: null,
  user: null,
  stamp: null,
  owner: null,
  encasement: null,
  receipts: null,
  datum: null,
  testing: null,
  gather: null,
  return: null,
});
const allRight = () => {
  auditRef.value.formSub(data.ids);
};

function transformTimestamp(timestamp) {
  let a = new Date(timestamp).getTime();
  const date = new Date(a);
  const Y = date.getFullYear() + "-";
  const M =
      (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
  const D =
      (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
  // const h =
  // 	(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
  // const m =
  // 	date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  // const s = date.getSeconds(); // 秒
  const dateString = Y + M + D;
  // console.log('dateString', dateString); // > dateString 2021-07-06 14:23
  return dateString;
}

const addGoods = reactive({
  value: "",
  total: 0,
  pageSize: 10,
  pageNum: 1,
  addList: null,
  tableData: [],
  shops: "",
  allGoods: [],
  delList: [],
});
const {proxy} = getCurrentInstance();
const logFlag = ref(false)
const details = async (id, ids, mess, logfn) => {
  ElLoading.service();
  if (mess) {
    data.formFlag = true
  } else {
    data.formFlag = false
  }
  if (logfn) {
    logFlag.value = false
  } else {
    logFlag.value = true
    if (logQueryRef.value) {
      logQueryRef.value.data.list = [];
    }
    const auditList = await manageApi.auditList({"salesOrder.id": id})
    const logList = await manageApi.logList({masterId: id})
    setTimeout(() => {
      if (auditList.code == 200 && logList.code == 200) {
        logQueryRef.value?.timeFns(auditList.data.records, logList.data.records);
      } else {
        ElMessage.error('操作日志加载失败')
      }
    }, 500)
  }
  data.ids = ids;
  manageApi.detailManage({salesOrderId: id}).then((res) => {
    if (res.code == 200) {
      if (res.data.orderHeader.status == 3) {
        manageApi.warehouseList({docNum: res.data.orderHeader.docNum}).then((res) => {
          data.warehouseList = res.data.records
        })
      }
      addGoods.allGoods = res.data.orderFormList;
      addGoods.allGoods?.forEach((item) => {
        item.commodity.originPlace = item.originPlace;
        item.commodity.basicUnit = item.basicUnit;
        item.commodity.ratio = item.unitLoading;
        item.supplier = {
          enterpriseName: item.supplierName
        };
        item.commodity.taxRate = item.taxRate;
        item.commodity.storageTemperature = item.storageTemperature;
        item.commodity.batchNumber = item.approvalNumber;
        item.commodity.grugsType = item.grugsType;
        item.commodity.validityTime = item.validityTime;
        item.validityTime = item.expirationTime;
        item.price = {
          flag: false,
          str: item.unitPrice ? item.unitPrice : 0,
        };
        item.num = {
          flag: false,
          str: item.quantity ? item.quantity : 0,
        };
        item.manufacture = item.manufacturer;
        item.batchNumber = item.batchNum;
        item.total = item.amountMoney ? item.amountMoney : 0;
        item.unitPrice = item.costUnitPrice;
      });
      data.editStrs = res.data;
      formInline2.n1 = res.data.orderConfig?.stampOption;
      formInline2.n2 = res.data.orderConfig?.cargoOwnerOption;
      formInline2.n3 = res.data.orderConfig?.packingOption;
      formInline2.n4 = res.data.orderConfig?.isConsolidation;
      formInline2.n5 =
          res.data.orderConfig?.receiptReturnOption.split(",");
      formInline2.n6 = res.data.orderConfig?.retainedOption;
      formInline2.n7 =
          res.data.orderConfig?.inspectionReportOption.split(",");
      formInline2.n8 =
          res.data.orderConfig?.qualityReceiptReturn.split(",");
      formInline2.n9 = res.data.orderConfig?.followGenOption.split(",");
      formInline2.n10 = res.data.orderConfig?.remark;
      footForm.price = res.data.orderHeader?.totalAmount;
      footForm.discount = res.data.orderHeader?.totalAmountAfterDiscount;
      footForm.num = res.data.orderHeader?.totalQuantity;
      formInline1.n1 = res.data.orderHeader?.customer.enterpriseName;
      formInline1.n2 = res.data.orderHeader?.delegate.delegateName;
      formInline1.n3 = res.data.orderHeader?.settlementMethod;
      formInline1.n4 = res.data.orderHeader?.collectionPeriod;
      formInline1.n5 = res.data.orderHeader?.invoiceType;
      formInline1.n6 = res.data.orderHeader?.businessType;
      formInline1.n7 = res.data.orderHeader?.logisticsMode;
      formInline1.n8 = res.data.orderHeader?.thirdPartyOgistics;
      formInline1.n9 = res.data.orderHeader?.sendAddressCode.split(",");
      formInline1.n10 = res.data.orderHeader?.sendAddressDetail;
      formInline1.n11 = res.data.orderHeader?.consignee;
      formInline1.n12 = res.data.orderHeader?.consigneePhone;
      formInline1.n13 = res.data.orderHeader?.warehouseNumber.warehouseNumber;
      formInline1.n15 = res.data.orderHeader?.selfRate;
      formInline1.n16 = res.data.orderHeader?.preparedBy;
      formInline1.n17 = res.data.orderHeader?.remark;
      formInline1.n19 = Number(res.data.orderHeader?.discountAmount);
      formInline1.n20 = res.data.orderHeader?.isInvoice;
      formInline1.n21 = res.data.orderHeader?.contractTemplate;
      formInline1.n22 = res.data.orderHeader?.isCreateContract;
      footForm.price = res.data.orderHeader?.totalAmount;
      footForm.num = res.data.orderHeader?.totalQuantity;
      footForm.discount = res.data.orderHeader?.totalAmountAfterDiscount;
      formInline1.n14 = res.data.orderHeader?.handledBy.name;

      dialogTableVisible.value = true;
    } else {
      ElMessage.error('信息加载失败')
    }
    const loadingInstance = ElLoading.service();
    loadingInstance.close();
  });
};
const echo1 = (row) => {
  let n = "";
  if (row.status == "0") {
    n = "草稿";
  } else if (row.status == "1") {
    n = "待审核";
  } else if (row.status == "2") {
    n = "审核中";
  } else if (row.status == "3") {
    n = "审核通过";
  } else if (row.status == "4") {
    n = "驳回";
  } else if (row.status == "5") {
    n = "撤销";
  } else {
    n = "";
  }
  return n;
};
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')

  if (localStorage.getItem('orderType')) {
    let arrType = JSON.parse(localStorage.getItem('orderType'))
    for (let key in arrType) {
      data[key] = arrType[key];
    }
  } else {
    data.clearingFormType = await proxy.getDictList("erp_clearingForm");
    data.invoice = await proxy.getDictList("erp_invoice");
    data.logistics = await proxy.getDictList("erp_logistics");
    data.business = await proxy.getDictList("erp_business");
    data.TripartiteLogistics = await proxy.getDictList(
        "erp_TripartiteLogistics"
    );
    data.contract = await proxy.getDictList("erp_contract");
    data.discount = await proxy.getDictList("erp_discount");
    data.stamp = await proxy.getDictList("erp_stamp");
    data.owner = await proxy.getDictList("erp_owner");
    data.encasement = await proxy.getDictList("erp_encasement");
    data.receipts = await proxy.getDictList("erp_receipts");
    data.testing = await proxy.getDictList("erp_testing");
    data.gather = await proxy.getDictList("erp_gather");
    data.return = await proxy.getDictList("erp_return");
    localStorage.setItem("orderType", JSON.stringify(data))
  }
  data.user = JSON.parse(localStorage.getItem("USER_INFO")).content;
});
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
});
const props = defineProps({
  nums: {
    default: null
  }
})
const againFn = () => {
  emit('againFn', data.editStrs.orderHeader)
}
const echoType = (type, str, find, ind) => {
  if (type) {
    const newStr = type.find(item => item[find] == str)
    return newStr ? newStr[ind] : str
  } else {
    return str
  }
}
const checkEcho = (type, str, find, ind) => {
  if (type) {
    let reArr = []
    type.forEach(item => {
      str.forEach(items => {
        if (item[find] == items) {
          reArr.push(item[ind])
        }
      })
    })
    return reArr.length > 0 ? reArr.join('，') : str
  } else {
    return str
  }
}
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  details,
  dialogTableVisible
});
</script>
<style lang="scss" scoped>
::v-deep .el-input.is-disabled .el-input__wrapper {
  background: none !important;
  box-shadow: none !important;
  color: #000 !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
  -webkit-text-fill-color: #000 !important; //修改输入框文字颜色
}

::v-deep .el-input__suffix-inner {
  display: none !important;
}

.formBox {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.stateTitle {
  position: absolute;
  font-size: 15px;
  top: 17px;
  right: 53px;
}

.col_title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 8px;

  &::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 20px;
    background-color: #2878ff;
    border-radius: 2px;
    position: absolute;
    top: 15px;
    left: 0;
  }
}

.messTable {
  width: 100%;
  background-color: #eaedf3;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 1px 1px 0 1px;

  tr {
    margin-bottom: 1px;
    display: flex;
    background: #fff;

    td {
      background-color: white;
      line-height: 40px;
    }

    td:nth-child(1) {
      flex: 1;
      padding: 0 10px;
      font-weight: bold;
      color: #505050;
      background: #f7f7f7;
    }

    td:nth-child(2) {
      color: #606266;
      padding: 0 10px;
      flex: 2
    }
  }
}

::v-deep .el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: #fff;
  border-color: #dbdee5;
  cursor: not-allowed;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #2a75f6;
  border-color: #2a75f6;
}

::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
  cursor: not-allowed;
}

::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #fff;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #2a75f6 !important;
}

::v-deep .el-checkbox__inner {
  width: 20px !important;
  height: 20px !important;
}

::v-deep .el-checkbox__inner::after {
  width: 7px !important;
  height: 13px !important;
}
</style>
