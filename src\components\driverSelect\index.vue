<template>
    <el-dialog :title="values.title" v-model="open" width="850px" append-to-body @close="close">
        <div class="box-flex">
            <div class="box-flex-item">
                <el-row class="mb8">
                    <el-col :span="24">
                        <el-input v-model="searchName" placeholder="请输入司机名称" clearable @keyup.enter.native="getList" />
                    </el-col>
                </el-row>
                <div class="overflow-div">
                    <el-tree
                        :ref="config.type"
                        :props="{
                            children: config.children,
                            label: config.label,
                            disabled: config.disabled
                        }"
                        :data="dataList"
                        node-key="driverCode"
                        :check-strictly="true"
                        check-on-click-node
                        @check-change="(data, checked) => onCheckChange(data, checked)"
                        :default-checked-keys="config.checkedHandle(selectList)"
                        show-checkbox
                        default-expand-all
                    >
                    </el-tree>
                </div>
            </div>
            <div class="box-flex-item">
                <div style="display: flex; justify-content: space-between">
                    <div style="text-align: left; padding: 7px 0">
                        已选：<span style="color: #5670fe">{{ selectList.length }}人</span>
                    </div>
                    <el-link style="text-align: right" @click="clear">清空</el-link>
                </div>
                <div style="margin: 5px 0">司机列表</div>
                <div class="overflow-div">
                    <div style="display: flex; justify-content: space-between; margin: 5px 0" v-for="(item, idx) in selectList">
                        <div style="display: flex">
                            <el-icon color="#5670FE" size="14"><Avatar /></el-icon>
                            <span style="margin-left: 10px;">{{ item.driverName }}</span>
                        </div>
                        <div style="text-align: right; padding-right: 5px">
                            <span @click="delData(idx)" style="cursor: pointer">
                                <el-icon color="#5670FE"><Close /></el-icon>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
			<el-button @click="close">取 消</el-button>
			<el-button type="primary" @click="submitForm">确 定</el-button>
        </template>
    </el-dialog>
</template>

<script>
import { CONFIG_LIST } from './driver-config.js';
import { Avatar, Close } from '@element-plus/icons-vue';
export default {
    name: 'driverSelect',
    components: { Avatar, Close },
    props: {
        show: {
            required: true,
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'dep'
        },
        values: {
            required: true,
            type: Object
        },
        driverData: {
            required: true,
            type: Array
        }
    },
    watch: {
        show: {
            handler(val) {
                this.open = val;
            }
        }
    },
    data() {
        return {
            open: false,
            searchName: null,
            searchType: null,
            dataList: [],
            selectList: []
        };
    },
    created() {
        this.selectList = [...this.driverData];
        this.dataInit();
        this.getList();
    },
    methods: {
        // 搜索
        getList() {
            var that = this;
            let queryDriverParams = {};
            queryDriverParams.driverName = this.searchName;
            if (this.values.type == 2) {
                queryDriverParams.taskCode = this.values.taskCode;
            }
            this.config.getList(queryDriverParams, this.values.type).then((res) => {
                that.dataList = res;
            });
        },
        // 清空
        clear() {
            this.$refs[this.type].setCheckedKeys([]);
            this.selectList = [];
        },
        dataInit() {
            var that = this;
            this.open = this.show;
            this.config = {};
            CONFIG_LIST.find(function (cfg) {
                if (cfg.type == that.type) {
                    that.config = cfg;
                }
            });
        },
        submitForm() {
            if (this.values.minNum && this.selectList.length < this.values.minNum) {
                this.msgError('最少选择' + this.values.minNum + '个司机');
                return false;
            }
            if (this.values.maxNum && this.selectList.length > this.values.maxNum) {
                this.msgError('最多选择' + this.values.minNum + '个司机');
                return false;
            }
            this.$emit('onConfirm', { data: this.values.data, driverList: this.selectList });
        },

        delData(idx) {
          this.onCheckChange(this.selectList[idx],false)
        },
        close() {
            this.$emit('changeShow', false);
        },
        onCheckChange(data, checked) {
            if (checked) {
                if (this.values.maxNum && this.selectList.length + 1 > this.values.maxNum) {
                    this.msgError('最多选择' + this.values.maxNum + '个司机');
                    this.$refs[this.type].setChecked(data.driverCode, false);
                    return false;
                }
                if (this.selectList.length > 0) {
                    let lastIndex = this.selectList.findIndex((t) => t.driverCode === data.driverCode);
                    if (lastIndex > -1) {
                        this.selectList.splice(lastIndex, 1);
                    } else {
                        this.selectList.push({ driverCode: data.driverCode, driverName: data.driverName, driverPhone: data.driverPhone });
                    }
                } else {
                    this.selectList.push({ driverCode: data.driverCode, driverName: data.driverName, driverPhone: data.driverPhone });
                }
            } else {
                let index = this.selectList.findIndex((t) => t.driverCode === data.driverCode);
                if (index > -1) {
                    this.selectList.splice(index, 1);
                }
              this.$refs[this.type].setChecked(data.driverCode, false);
            }
        }
    }
};
</script>

<style scoped>
.box-flex {
    display: flex;
    border: 1px solid rgba(229, 236, 252, 1);
}

.box-flex-item {
    flex: 1;
    padding: 10px;
}

.box-flex-item:first-child {
    border-right: 1px solid rgba(229, 236, 252, 1);
}

/*设置滚动条 start*/
.overflow-div {
    margin-top: 10px;
    height: 500px;
    overflow-y: scroll;
}

.overflow-div::-webkit-scrollbar {
    width: 5px;
}

.overflow-div::-webkit-scrollbar {
    height: 5px;
}

.overflow-div::-webkit-scrollbar-track {
    background: #fff;
    border-radius: 2px;
}

.overflow-div::-webkit-scrollbar-thumb {
    background: #bfbdbd;
    border-radius: 10px;
}

.overflow-div::-webkit-scrollbar-thumb:hover {
    background: #999;
}

.overflow-div::-webkit-scrollbar-corner {
    background: #a7aaaa;
}

/*设置滚动条 end*/
</style>
