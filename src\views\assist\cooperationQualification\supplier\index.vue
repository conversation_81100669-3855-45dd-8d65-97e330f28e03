<template>
    <div class="app-container" style="padding: 10px;width: 100%;">
        <el-card class="box-card Botm">
          <el-form ref="queryRef" :inline="true" :model="queryParams" class="form_130">
                <TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
                    <el-form-item label="企业名称" prop="enterpriseName">
                      <el-input v-model="queryParams.enterpriseName" class="form_225" clearable
                                placeholder="请输入企业名称"/>
                    </el-form-item>
                    <el-form-item label="拼音码" prop="pinyinCode">
                      <el-input v-model="queryParams.pinyinCode" class="form_225" clearable placeholder="请输入拼音码"/>
                    </el-form-item>
                    <el-form-item label="自编码" prop="supplierCoding">
                      <el-input v-model="queryParams.supplierCoding" class="form_225" clearable
                                placeholder="请输入自编码"/>
                    </el-form-item>
                    <el-form-item label="统一社会信用代码" prop="socialCreditCode">
                      <el-input v-model="queryParams.socialCreditCode" class="form_225" clearable
                                placeholder="请输入统一社会信用代码"/>
                    </el-form-item>
                  <el-form-item label="创建来源" prop="source">
                    <el-select v-model="queryParams.source" class="form_225" placeholder="请选择创建来源">
                      <el-option label="全部" value=""/>
                      <el-option label="手工录入" value="1"/>
                      <el-option label="智能录入" value="2"/>
                    </el-select>
                  </el-form-item>
                    <el-form-item label="审核状态" prop="status">
                      <el-select v-model="queryParams.status" class="form_225" placeholder="请选择审核类型">
                        <el-option v-for="item in reviewStatus" :key="item.value" :label="item.name"
                                   :value="item.value"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="禁用状态" prop="customLabel">
                      <el-select v-model="queryParams.customLabel" class="form_225" placeholder="请选择禁用状态">
                        <el-option v-for="item in delFlagList" :key="item.value" :label="item.name"
                                   :value="item.value"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建日期" prop="createDate">
                        <div class="box_date">
                          <el-date-picker v-model="queryParams.createDate" class="form_225" end-placeholder="结束日期"
                                          range-separator="至" start-placeholder="开始日期" type="daterange"/>
                        </div>
                    </el-form-item>
                    <el-form-item label="修改日期" prop="updateDate">
                        <div class="box_date">
                          <el-date-picker v-model="queryParams.updateDate" class="form_225" end-placeholder="结束日期"
                                          range-separator="至" start-placeholder="开始日期" type="daterange"/>
                        </div>
                    </el-form-item>

                </TopTitle>

            </el-form>
        </el-card>
        <el-card class="box-card" style="margin-top: 10px;">
            <div style="display:flex;justify-content: space-between;">
              <el-button style="margin-bottom:10px" type="primary"
                         @click="() => handleAdd(undefined, 'add')">新增
              </el-button>
              <RightToptipBarV2 className="cooperationQualification_supplier" @handleRefresh="getList"/>
            </div>
            <div v-loading="loading" style="min-height:200px">
                <DragTableColumn v-if="reviewStatus.length && delFlagList.length && qualificationList.length"
                                 v-model:queryParams="queryParams" :columns="columns" :getList="getList"
                                 :row-style="tableRowStyle" :tableData="list"
                                 className="cooperationQualification_supplier">
                    <template v-slot:operate="{ scopeData }">
                        <el-button link type="primary" @click="handleDetail(scopeData.row, 'detail')"><img
                                src="@/assets/icons/detail.png" style="margin-right:5px" />详情</el-button>
                      <el-button :disabled="scopeData.row.status == '1' || scopeData.row.status == '2'" link
                                 type="primary"
                                 @click="handleEdit(scopeData.row, 'edit')"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                      <el-button
                          :disabled="isDeleteAuthority(scopeData.row.createBy.id, ['1', '4', '6'], scopeData.row.status)"
                          link type="danger"
                          @click="handleDelete(scopeData.row)"><img
                                src="@/assets/icons/delete.png" style="margin-right:5px" />删除</el-button>
                        <el-dropdown>
                          <el-button link style="margin-top: 3px;" type="primary">更多
                            <el-icon>
                                    <ArrowDown />
                                </el-icon></el-button>

                            <template #dropdown>
                                <el-dropdown-menu>
                                  <el-dropdown-item style="color:#67c23a" @click="handleLog(scopeData.row)"><img
                                            src="@/assets/icons/review.png"
                                            style="margin-right:5px" />操作记录</el-dropdown-item>
                                  <el-dropdown-item v-if="scopeData.row.customLabel == '2'"
                                                    style="color:#909399" @click="handleDisabled(scopeData.row, '1')">
                                    <img
                                            src="@/assets/icons/disabled.png"
                                            style="margin-right:5px" />禁用</el-dropdown-item>
                                  <el-dropdown-item v-if="scopeData.row.customLabel == '1'"
                                                    style="color:#909399" @click="handleDisabled(scopeData.row)"><img
                                            src="@/assets/icons/disabled.png"
                                            style="margin-right:5px" />解禁</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>

                </DragTableColumn>
            </div>
            <div style="float: right;">
              <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
                          :total="total" @pagination="getList"/>
            </div>
        </el-card>
        <!-- 添加或修改角色配置对话框 -->
      <el-dialog v-if="open" v-model="open" :append-to-body="false" :before-close="closeDialog"
                 :show-close="!modalLoding"
                 :title="title" style="position: relative;" width="90%">
            <div v-loading="modalLoding">
              <el-button v-if="modalType == 'detail'" style="margin-top: 3px;position: absolute;right: 50px;top: 10px;"
                    @click="handleFileList">附件列表</el-button>

                <el-collapse v-model="activeNames" @change="handleChange">
                  <el-collapse-item name="1" title="基本信息">
                        <template #title>
                            <span class="col_title">基本信息</span>
                        </template>
                        <el-form ref="formRef" :model="form" :rules="rules" label-width="auto"
                            style="margin-top: 0px;padding-right: 20px;">
                            <div class="box_2">
                                <el-form-item label="企业名称" prop="enterpriseName">
                                  <el-input v-model="form.enterpriseName" :disabled="modalType == 'detail'" clearable
                                            maxlength="100" placeholder="请输入企业名称" show-word-limit/>
                                </el-form-item>
                                <el-form-item label="统一社会信用代码" prop="socialCreditCode">
                                  <el-input v-model="form.socialCreditCode" :disabled="modalType == 'detail'"
                                            clearable maxlength="100" placeholder="请输入统一社会信用代码"
                                            show-word-limit
                                        @blur="handleSocialCreditCodeBlur" />
                                </el-form-item>
                            </div>

                            <div class="box">

                                <el-form-item label="资质类别" prop="credentialType">
                                  <el-select ref="credentialTypeRef" v-model="form.credentialType"
                                             :disabled="modalType == 'detail'"
                                             placeholder="请选择资质类别" style="width:100%"
                                        @change="handleCredentialType" @focus="handleSelectFcous">
                                    <el-option v-for="item in qualificationList" :key="item.value" :label="item.name"
                                               :value="item.value"/>
                                    </el-select>
                                </el-form-item>
                                <!-- <el-form-item label="拼音码" prop="pinyinCode" v-if="modalType == 'detail'">
                                    <el-input v-model="form.pinyinCode" placeholder="请输入拼音码"
                                        :disabled="modalType == 'detail'" maxlength="100" show-word-limit clearable />
                                </el-form-item> -->
                              <el-form-item :rules="{
                                    pattern: form.credentialType === '1' ? /^GPF\d{5}(-\d{2})?$/ : form.credentialType === '2' ? /^GSC\d{5}(-\d{2})?$/ : null, message: form.credentialType === '1' ? '请输入GPFxxxxx或者GPFxxxxx-xx,其中xx为纯数字' : form.credentialType === '2' ? '请输入GSCxxxxx或者GSCxxxxx-xx,其中xx为纯数字' : '', trigger: 'blur'
                                }" label="自编码" prop="supplierCoding">
                                <el-input v-model="form.supplierCoding" clearable placeholder="请输入自编码"/>
                                </el-form-item>

                                <el-form-item label="营业期限" prop="businessTerm">
                                  <el-date-picker v-model="form.businessTerm" :disabled="modalType == 'detail'"
                                                  :disabled-date="disabledDate"
                                                  placeholder="请选择营业期限" style="width: 100%;"
                                                  type="date"/>
                                </el-form-item>
                                <el-form-item label="住所" prop="residence">
                                  <el-input v-model="form.residence" :disabled="modalType == 'detail'" clearable
                                            maxlength="100" placeholder="请输入住所" show-word-limit/>
                                </el-form-item>
                                <el-form-item label="经营范围" prop="businessNature">
                                  <el-input v-model="form.businessNature" :disabled="modalType == 'detail'"
                                            clearable maxlength="500" placeholder="请输入经营范围" show-word-limit/>
                                </el-form-item>
                                <el-form-item label="发证机关" prop="issuingOffice">
                                  <el-input v-model="form.issuingOffice" :disabled="modalType == 'detail'"
                                            clearable maxlength="100" placeholder="请输入发证机关" show-word-limit/>
                                </el-form-item>
                                <el-form-item label="联系电话" prop="qaCode">
                                  <el-input v-model="form.qaCode" :disabled="modalType == 'detail'" clearable
                                            maxlength="100" placeholder="请输入联系电话" show-word-limit type="number"/>
                                </el-form-item>
                                <el-form-item label="经营类型" prop="businessType">
                                  <el-select v-model="form.businessType" :disabled="modalType == 'detail'"
                                             placeholder="请选择经营类型"
                                             style="width:100%">
                                    <el-option v-for="item in businessList" :key="item.value" :label="item.name"
                                               :value="item.value"/>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="开户银行" prop="bankOfDeposit">
                                  <el-input v-model="form.bankOfDeposit" :disabled="modalType == 'detail'"
                                            clearable maxlength="100" placeholder="请输入开户银行" show-word-limit/>
                                </el-form-item>
                                <el-form-item label="银行账号" prop="bankAccount">
                                  <el-input v-model="form.bankAccount" :disabled="modalType == 'detail'"
                                            clearable maxlength="100" placeholder="请输入银行账号" show-word-limit
                                        type="number" />
                                </el-form-item>
                                <!-- <el-form-item label="成立日期" prop="dateIncorporation">
                                    <el-date-picker v-model="form.dateIncorporation" type="date" placeholder="请选择成立日期"
                                        :disabled="modalType == 'detail'" style="width: 100%;" />
                                </el-form-item> -->
                                <el-form-item label="发证时间" prop="issuingTime">
                                  <el-date-picker v-model="form.issuingTime" :disabled="modalType == 'detail'"
                                                  placeholder="请选择发证时间"
                                                  style="width: 100%;" type="date"/>
                                </el-form-item>
                                <el-form-item label="备注" prop="remark">
                                  <el-input v-model="form.remark" :disabled="modalType == 'detail'" :rows="1"
                                            clearable maxlength="100" placeholder="请输入备注" show-word-limit
                                            type="textarea"/>
                                </el-form-item>

                            </div>
                            <div style="display:flex">
                              <el-form-item v-if="form.credentialType == '1'" label="经营范围配置"
                                            prop="apparatusFirst">
                                    <div class="checkBox"><el-checkbox v-model="form.apparatusFirst" label="1"
                                            name="type">一类器械</el-checkbox></div>
                                    <div class="checkBoxRight"> <el-input v-model="form.apparatusScopeName"
                                                                          :disabled="!form.apparatusFirst" clearable
                                                                          placeholder="请选择经营范围" readonly
                                                                          style="width:100%"
                                                                          @click="() => handleRange('isApparatus')"/>
                                    </div>
                                    <div class="checkBox"><el-checkbox v-model="form.sterilized" label="2"
                                            name="type">消杀</el-checkbox></div>
                                    <div class="checkBoxRight">
                                      <el-input v-model="form.sterilizedScopesName" :disabled="!form.sterilized"
                                                clearable
                                                placeholder="请选择经营范围" readonly style="width:100%"
                                                @click="() => handleRange('disinfectionAndSterilization')"/>
                                    </div>
                                </el-form-item>
                            </div>
                        </el-form>
                    </el-collapse-item>
                  <el-collapse-item class="step2" name="2" title="质量信息" @click="handleChange_msg">
                        <template #title>
                            <span class="col_title">质量信息</span>
                        </template>
                        <div class="btn_cmt">
                            <el-button :type="tabKey == '1' ? 'primary' : 'default'"
                                @click="() => handleTabClick('1')"><el-icon>
                                    <Plus />
                                </el-icon><span>药品</span></el-button>
                          <el-button v-if="form.credentialType == '2'" :type="tabKey == '3' ? 'primary' : 'default'"
                                     @click="() => handleTabClick('3')">
                            <el-icon>
                                    <Plus />
                                </el-icon><span>一类</span></el-button>
                            <el-button :type="tabKey == '4' ? 'primary' : 'default'"
                                @click="() => handleTabClick('4')"><el-icon>
                                    <Plus />
                                </el-icon><span>二类</span></el-button>
                            <el-button :type="tabKey == '2' ? 'primary' : 'default'"
                                @click="() => handleTabClick('2')"><el-icon>
                                    <Plus />
                                </el-icon><span>三类</span></el-button>
                          <el-button :class="form.credentialType !== '2' ? 'last-btn' : null"
                                     :type="tabKey == '5' ? 'primary' : 'default'"
                                     @click="() => handleTabClick('5')">
                            <el-icon>
                                    <Plus />
                                </el-icon><span>食品</span></el-button>
                          <el-button v-if="form.credentialType == '2'" :type="tabKey == '6' ? 'primary' : 'default'"
                                     class="last-btn" @click="() => handleTabClick('6')">
                            <el-icon>
                                    <Plus />
                                </el-icon><span>消杀</span></el-button>
                        </div>
                        <el-form ref="modalTableListRef" :model="licensedata" :rules="modalTableListRules"
                            label-width="auto" style="margin-top: 0px;padding-right: 20px;">
                            <div class="box">
                                <el-form-item label="许可证编号" prop="licenseNo">
                                  <el-input v-model="licensedata.licenseNo" :disabled="modalType == 'detail'" clearable
                                            maxlength="100" placeholder="请输入许可证编号" show-word-limit/>
                                </el-form-item>
                              <el-form-item v-if="form.credentialType === '2'"
                                            :rules="{ required: (tabKey == '1') ? true : false, message: `注册地址不能为空`, trigger: 'blur' }"
                                            label="注册地址" prop="registerAddress">
                                <el-input v-model="licensedata.registerAddress" clearable maxlength="100"
                                          placeholder="请输入注册地址" show-word-limit/>
                                </el-form-item>
                              <el-form-item :label="form.credentialType == '2' ? '生产地址' : '注册地址'"
                                            :rules="{ required: (tabKey == '1'&&form.credentialType == '1') ? true : false, message: `${form.credentialType == '2' ? '生产地址' : '注册地址'}不能为空`, trigger: 'blur' }"
                                            prop="licenseAddress">
                                    <el-input v-model=" licensedata.licenseAddress "
                                              :disabled=" modalType == 'detail' "
                                              :placeholder=" `请输入${form.credentialType == '2' ? '生产' : '注册'}地址` "
                                              clearable maxlength="100" show-word-limit/>
                                </el-form-item>
                              <el-form-item key="licenceScopesName"
                                            :label=" form.credentialType == '2' ? '生产范围' : '许可证范围' "
                                            prop="licenceScopesName">
                                    <el-select v-if=" tabKey == '6' || (tabKey == '1' && form.credentialType == '2') "
                                               key="licenceScopesName1" v-model=" licensedata.licenceScopesName "
                                        :disabled=" modalType == 'detail' || modalTableList_entrust.length "
                                               :placeholder=" `请选择${form.credentialType == '2' ? '生产范围' : '许可证范围'}` "
                                               multiple
                                               style="width:100%" @click=" () => handleLicenseScope(licensedata) ">
                                        <el-option v-for=" item  in  licenseScopeListData " :key=" item.id "
                                            :label=" item.valueName " :value=" item.id " />
                                    </el-select>
                                <el-input v-else key="licenceScopesName2``" v-model=" licensedata.licenceScopesName "
                                        :placeholder=" `请选择${form.credentialType == '2' ? '生产范围' : '许可证范围'}` "
                                          clearable maxlength="100" readonly
                                          show-word-limit @click=" () => handleLicenseScope(licensedata) "/>
                                </el-form-item>

                                <el-form-item label="发证日期" prop="licenseStartTime">
                                  <el-date-picker v-model=" licensedata.licenseStartTime "
                                                  :disabled=" modalType == 'detail' "
                                                  :disabled-date=" disabledDateAfter "
                                                  placeholder="请选择发证日期" style="width: 100%;"
                                                  type="date"/>
                                </el-form-item>

                                <el-form-item label="有效期" prop="licenseValidity">
                                  <el-date-picker v-model=" licensedata.licenseValidity "
                                                  :disabled=" modalType == 'detail' " :disabled-date=" disabledDate "
                                                  placeholder="请选择有效期" style="width: 100%;"
                                                  type="date"/>
                                </el-form-item>
                                <el-form-item label="发证机关" prop="licenseOffice">
                                  <el-input v-model=" licensedata.licenseOffice " :disabled=" modalType == 'detail' "
                                            clearable maxlength="100" placeholder="请输入发证机关" show-word-limit/>
                                </el-form-item>
                              <el-form-item
                                  :rules="{ required: (tabKey == '1'&&form.credentialType == '1') ? true : false, message: `注册地址不能为空`, trigger: 'blur' }"
                                  label="法人" prop="licenseLegalPerson">
                                <el-input v-model=" licensedata.licenseLegalPerson " :disabled=" modalType == 'detail' "
                                          clearable maxlength="100" placeholder="请输入法人" show-word-limit/>
                                </el-form-item>

                              <el-form-item
                                  :rules="{ required: (tabKey == '1'&&form.credentialType == '1') ? true : false, message: `企业负责人不能为空`, trigger: 'blur' }"
                                  label="企业负责人" prop="licenseDirector">
                                <el-input v-model=" licensedata.licenseDirector " :disabled=" modalType == 'detail' "
                                          clearable maxlength="100" placeholder="请输入企业负责人" show-word-limit/>
                                </el-form-item>
                              <el-form-item
                                  :rules="{ required: (tabKey == '1'&&form.credentialType == '1') ? true : false, message: `质量负责人不能为空`, trigger: 'blur' }"
                                  label="质量负责人" prop="qualityDirector">
                                <el-input v-model=" licensedata.qualityDirector " :disabled=" modalType == 'detail' "
                                          clearable maxlength="100" placeholder="请输质量负责人" show-word-limit/>
                                </el-form-item>
                            </div>

                            <el-form-item label="许可证图片" prop="licenseImg">
                              <el-upload ref="licenseImgRef" v-model:file-list=" licensedata.licenseImg "
                                         :action=" uploadUrl "
                                         :before-upload=" (file) => beforeFile(file) "
                                         :data=" { fileType: 'image', fjType: '质量信息', zhType: '供应商' } "
                                         :disabled=" modalType == 'detail' " :headers=' headers ' :limit=" 1 "
                                         :on-exceed=" (files) => handleExceed(files, licenseImgRef) "
                                    :on-preview=" handlePictureCardPreview "
                                         :on-success=" (res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 1) "
                                         class="upload-demo"
                                         drag
                                         list-type="picture-card">
                                    <el-icon>
                                        <Plus />
                                    </el-icon>
                                </el-upload>

                            </el-form-item>
                        </el-form>
                    </el-collapse-item>
                  <el-collapse-item name="3" title="销售授权委托书" @click=" handleChange_msg ">
                        <template #title>
                            <span class="col_title"> 销售授权委托书</span>
                        </template>
                        <div style="margin-bottom: 20px;">
                          <el-button :disabled=" modalType == 'detail' " type="primary"
                                @click=" () => handleAdd_entrust() ">新增</el-button>
                          <el-button :disabled=" modalType == 'detail' " type="danger"
                                     @click=" handleDelete_entrust ">删除
                          </el-button>
                        </div>
                        <el-table ref="multipleTableRef_entrust" :data=" modalTableList_entrust "
                                  border @selection-change=" handleSelectionChange_entrust ">
                          <el-table-column align="center" min-width="55" type="selection"/>
                          <el-table-column align="center" label="序号" min-width="80">
                                <template #default=" scope ">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="委托人姓名"
                                           min-width="100"
                                prop="delegateName">
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="身份证号"
                                           min-width="100"
                                prop="certificateCard">
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="有效期"
                                           min-width="100">
                                <template #default=" scope ">
                                    <p>{{
                                        (scope.row.effectiveTime ?
                                        moment(scope.row.effectiveTime).format('YYYY-MM-DD') :
                                        undefined) || '--' }}</p>
                                </template>
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="授权日期"
                                           min-width="100">
                                <template #default=" scope ">
                                    <p>{{
                                        (scope.row.empowerTime ?
                                        moment(scope.row.empowerTime).format('YYYY-MM-DD') :
                                        undefined) || '--' }}</p>
                                </template>
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="区域限制"
                                           min-width="100"
                                prop="erpAddressList">
                                <template #default=" scope ">
                                    <p>{{
                                        filterAddress(scope.row.erpAddressList) || '--' }}</p>
                                </template>
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="委托书"
                                           min-width="100">
                                <template #default=" scope ">
                                    <el-button link type="primary"
                                        @click=" () => handlePictureCardPreview(scope.row.remark[0].url) ">{{
                                        scope.row.remark[0].url
                                        || '--' }}</el-button>
                                </template>
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" fixed="right" label="操作"
                                           min-width="100">
                                <template #default=" scope ">
                                  <el-button :disabled=" modalType == 'detail' " icon="el-icon-edit" link type="primary"
                                             @click="handleAdd_entrust(scope.row)">修改
                                  </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                  <el-collapse-item name="4" title="质量保证协议书" @click=" handleChange_msg ">
                        <template #title>
                            <span class="col_title">质量保证协议书</span>
                        </template>
                        <div style="margin-bottom: 20px;">
                          <el-button :disabled=" modalType == 'detail' " type="primary"
                                @click=" () => handleAdd_warranty() ">新增</el-button>
                          <el-button :disabled=" modalType == 'detail' " type="danger"
                                     @click=" handleDelete_warranty ">删除
                          </el-button>
                        </div>
                        <el-table ref="multipleTableRef_warranty" :data=" modalTableList_warranty "
                                  border @selection-change=" handleSelectionChange_warranty ">
                          <el-table-column align="center" min-width="55" type="selection"/>
                          <el-table-column align="center" label="序号" min-width="80">
                                <template #default=" scope ">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <!-- <el-table-column label="协议编号" align="center" :show-overflow-tooltip="true"
                                min-width="100"></el-table-column> -->
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="有效期"
                                           min-width="100">
                                <template #default=" scope ">
                                    <p>{{
                                        (scope.row.qualityDate ?
                                        moment(scope.row.qualityDate).format('YYYY-MM-DD') :
                                        undefined) || '--' }}</p>
                                </template>
                            </el-table-column>
                            <!-- <el-table-column label="类型" align="center" min-width="100"
                                :show-overflow-tooltip="true"></el-table-column> -->
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="上传图片"
                                           min-width="100">
                                <template #default=" scope ">
                                    <el-button link type="primary"
                                        @click=" () => handlePictureCardPreview(scope.row.qualityCode[0].url) ">{{
                                        scope.row.qualityCode[0].name
                                        || '--' }}</el-button>
                                </template>
                            </el-table-column>
                          <el-table-column :show-overflow-tooltip=" true " align="center" label="操作" min-width="100">
                                <template #default=" scope ">
                                  <el-button :disabled=" modalType == 'detail' " icon="el-icon-edit" link
                                             type="primary"
                                             @click="handleAdd_warranty(scope.row)">修改
                                  </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                  <el-collapse-item name="5" title="GSP证书" @click=" handleChange_msg ">
                        <template #title>
                            <span class="col_title">{{ form.credentialType == '1' ? 'GSP证书' : 'GMP证书' }}</span>
                        </template>
                        <div>
                            <div style="margin-bottom: 20px;">
                              <el-button :disabled=" modalType == 'detail' " type="primary"
                                    @click=" handleAdd_gsp ">新增</el-button>
                              <el-button :disabled=" modalType == 'detail' || !chooseList_GMP.length " type="danger"
                                         @click=" handleDelete_gsp ">删除
                              </el-button>
                            </div>
                            <el-table ref="multipleTableRef_GMP" :data=" modalTableList_GMP "
                                      border @selection-change=" handleSelectionChange_GMP ">
                              <el-table-column align="center" min-width="55" type="selection"/>
                              <el-table-column :show-overflow-tooltip=" true " align="center" label="证书编号"
                                               min-width="100">
                                    <template #default=" scope ">
                                        <div v-clickOutside=" () => handleClickOutside(scope, 'isShowgmpCertificateNo') ">
                                            <p v-show=" !scope.row.isShowgmpCertificateNo "
                                                style="height: 42px;line-height:42px"
                                                @click="handleInputEdit(scope, 'gmpCertificateNo')">
                                                {{ scope.row.gmpCertificateNo || '请输入证书编号' }}</p>
                                          <el-input v-show=" scope.row.isShowgmpCertificateNo "
                                                    v-model=" scope.row.gmpCertificateNo "
                                                    clearable maxlength="100" placeholder="请输入证书编号"
                                                    show-word-limit/>
                                        </div>
                                    </template>
                                </el-table-column>
                              <el-table-column :show-overflow-tooltip=" true " align="center" label="证书地址"
                                               min-width="100">
                                    <template #default=" scope ">
                                        <div
                                            v-clickOutside=" () => handleClickOutside(scope, 'isShowgmpCertificateAddress') ">
                                            <p v-show=" !scope.row.isShowgmpCertificateAddress "
                                                style="height: 42px;line-height:42px"
                                                @click="handleInputEdit(scope, 'gmpCertificateAddress')">
                                                {{ scope.row.gmpCertificateAddress || '请输入证书地址' }}</p>
                                          <el-input v-show=" scope.row.isShowgmpCertificateAddress "
                                                    v-model=" scope.row.gmpCertificateAddress "
                                                    clearable maxlength="100"
                                                    placeholder="请输入证书地址" show-word-limit/>
                                        </div>
                                    </template>
                                </el-table-column>
                              <el-table-column :show-overflow-tooltip=" true " align="center" label="证书图片"
                                               min-width="100">
                                    <template #default=" scope ">
                                        <div style="display:flex;justify-content: center;">
                                            <p v-if=" scope.row.gmpCertificatePicture.length "
                                               style="color:#2A76F8;cursor: pointer;width:150px;line-height:42px;white-space: nowrap;overflow:hidden;text-overflow: ellipsis;"
                                               @click=" () => handlePictureCardPreview(scope.row.gmpCertificatePicture[0].url) ">
                                                {{ scope.row.gmpCertificatePicture[0].name
                                                }}
                                            </p>
                                          <el-upload v-if=" modalType !== 'detail' " ref="gmpCertificatePictureRef"
                                                     v-model:file-list=" scope.row.gmpCertificatePicture "
                                                     :action=" uploadUrl "
                                                     :before-upload=" (file) => beforeFile(file) "
                                                :data=" { fileType: 'image', fjType: form.credentialType == '1' ? 'GSP证书' : 'GMP证书', zhType: '供应商' } "
                                                     :headers=' headers '
                                                     :limit=" 1 "
                                                     :on-exceed=" (files) => handleExceed(files, gmpCertificatePictureRef) "
                                                     :on-success=" (res, file, filList) => handleUploadSuccess(res, file, filList, scope.$index, 5) "
                                                     :show-file-list=" false "
                                                     accept=".jpg,.jpeg,.png,.gif.JPG,.JPEG,.PNG"
                                                     class="upload-demo">
                                                <template #trigger>

                                                    <el-button type="primary"> <el-icon style="margin-right:5px;">
                                                            <UploadFilled />
                                                        </el-icon>上传</el-button>
                                                </template>
                                            </el-upload>
                                        </div>
                                    </template>
                                </el-table-column>
                              <el-table-column :show-overflow-tooltip=" true " align="center" label="证书有效期"
                                               min-width="100">
                                    <template #default=" scope ">
                                        <div v-clickOutside=" () => handleClickOutside(scope, 'isShowgmpExpiredTime') ">
                                          <p v-show=" !scope.row.isShowgmpExpiredTime "
                                             style="height: 42px;line-height:42px"
                                             @click="handleInputEdit(scope, 'gmpExpiredTime')">
                                                {{
                                                (scope.row.gmpExpiredTime ?
                                                moment(scope.row.gmpExpiredTime).format('YYYY-MM-DD') :
                                                undefined) || '请选择有效期' }}</p>
                                            <el-date-picker v-show=" scope.row.isShowgmpExpiredTime "
                                                            v-model=" scope.row.gmpExpiredTime "
                                                            :disabled-date=" disabledDate "
                                                            :style=" [{ width: '100%' }, { display: scope.row.isShowgmpExpiredTime ? 'block' : 'none' }] "
                                                            clearable
                                                            placeholder="请选择有效期" type="date"/>
                                        </div>
                                    </template>
                                </el-table-column>
                              <el-table-column align="center" label="证书授权范围" min-width="100">
                                    <template #default=" scope ">
                                        <div>
                                            <div v-if=" form.credentialType === '2' "
                                                v-clickOutside=" ($event) => handleClickOutside(scope, 'isShowgmpCertificateScope', $event) ">
                                              <p v-show=" !scope.row.isShowgmpCertificateScope "
                                                 style="height: 42px;line-height: 42px;"
                                                 @click="handleInputEdit(scope, 'gmpCertificateScope')">
                                                    {{ filterArr(licenseScopeList, 'id', scope.row.gmpCertificateScope) ||
                                                    '请选择证书授权范围' }} </p>
                                                <div v-show=" scope.row.isShowgmpCertificateScope ">
                                                  <el-select ref="gmpCertificateScopeRef"
                                                             v-model=" scope.row.gmpCertificateScope "
                                                             :popper-append-to-body=" false " multiple
                                                             placeholder="请选择证书授权范围" popper-class="selectClass"
                                                             style="width:100%">
                                                    <el-option v-for="  item   in   licenseScopeList  " :key=" item.id "
                                                               :label=" item.valueName " :value=" item.id "/>
                                                    </el-select>
                                                </div>
                                            </div>
                                          <el-button v-else link type="primary"
                                                @click=" () => handleLicenseScopeGmp(scope.row) ">{{
                                                scope.row.gmpWholesaleScope ? scope.row.gmpWholesaleScope : '请选择'
                                                }}</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-collapse-item>
                  <el-collapse-item name="6" title="附件上传" @click=" handleChange_msg ">
                        <template #title>
                            <span class="col_title">附件上传</span>
                        </template>
                        <div style="margin-bottom: 20px;">
                          <el-button :disabled=" modalType == 'detail' " type="primary"
                                @click=" handleGetFileList ">获取附件列表</el-button>
                        </div>
                    <el-table ref="multipleTableRef_file" v-loading=" file_loading " :data=" modalTableList_file "
                              border>
                            <el-table-column align="center" label="序号" width="50">
                                <template #default=" scope ">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                      <el-table-column :show-overflow-tooltip=" true " align="center" label="类型" width="150">
                                <template #default=" scope ">
                                    <span>{{ formDict(manufacturerType, scope.row.smallType) }}</span>
                                </template>
                            </el-table-column>
                      <el-table-column :show-overflow-tooltip=" true " align="center" label="文件类型" min-width="250">
                                <template #default=" scope ">
                                    <span>{{ formDict(directoryFileName, scope.row.categoryName) }}</span>
                                </template>
                            </el-table-column>
                      <el-table-column align="center" label="文件名称" width="400">
                                <template #default=" scope ">
                                    <div>
                                        <div v-if=" scope.row.fileList ">
                                            <span v-for="( item, index ) in  scope.row.fileList " :key=" item.uid "
                                                class="fileName_t"><span
                                                    @click=" () => handlePictureCardPreview(scope.row.fileList[index].url) ">{{
                                                    item.name }}</span><span class="fileName_t_icon"
                                                    @click=" () => handleDeleteFile(scope.$index, index) "><el-icon>
                                                        <Delete />
                                                    </el-icon></span></span>
                                        </div>
                                        <span v-else>请上传</span>
                                    </div>

                                </template>
                            </el-table-column>
                      <el-table-column align="center" label="文件上传" width="130">
                                <template #default=" scope ">
                                  <el-upload v-model:file-list=" scope.row.fileList " :action=" uploadUrl "
                                        :before-upload=" (file) => beforeFile(file) "
                                             :data=" { fileType: 'image', fjType: formDict(directoryFileName, scope.row.categoryName), zhType: '客户管理', smallClass: scope.row.smallType } "
                                             :headers=' headers '
                                             :limit=" scope.row.isMultiPage === '1' ? 999 : 1 "
                                             :on-success=" (res, file, filList) => handleUploadSuccess(res, file, filList, scope.$index, 4) "
                                             :show-file-list=" false " accept=".jpg,.jpeg,.png,.gif.JPG,.JPEG,.PNG"
                                             class="upload-demo">
                                        <template #trigger>
                                            <el-button type="primary"> <el-icon style="margin-right:5px;">
                                                    <UploadFilled />
                                                </el-icon>上传</el-button>
                                        </template>
                                    </el-upload>
                                </template>
                            </el-table-column>
                      <el-table-column :show-overflow-tooltip=" true " align="center" label="是否必传" width="100">
                                <template #default=" scope ">
                                    <span>{{ scope.row.isUpload === '1' ? '是' : scope.row.isUpload === '0' ? '否' : '--'
                                        }}</span>
                                </template>
                            </el-table-column>
                      <el-table-column :show-overflow-tooltip=" true " align="center" label="是否多张" width="100">
                                <template #default=" scope ">
                                    <span>{{ scope.row.isMultiPage === '1' ? '是' : scope.row.isMultiPage === '0' ? '否' :
                                        '--'
                                        }}</span>
                                </template>
                            </el-table-column>
                      <el-table-column :show-overflow-tooltip=" true " align="center" label="备注" min-width="300">
                                <template #default=" scope ">
                                    <div v-clickOutside=" () => handleClickOutside(scope, 'isShowremark') ">
                                        <p v-show=" !scope.row.isShowremark " style="height: 42px;line-height:42px"
                                            @click="handleInputEdit(scope, 'remark')">
                                            {{ scope.row.remark || '请输入备注' }}</p>
                                      <el-input v-show=" scope.row.isShowremark " v-model=" scope.row.remark "
                                                clearable maxlength="100" placeholder="请输入备注" show-word-limit/>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                </el-collapse>

            </div>
            <template #footer>
              <div v-if=" !modalLoding " class="dialog-footer">
                    <el-button @click=" closeDialog ">取 消</el-button>
                <el-button v-if=" modalType != 'detail' && modalType != 'edit' " type="primary"
                        @click="submitForm(1)">保存草稿</el-button>
                <el-button v-if=" modalType !== 'detail' " type="primary" @click="submitForm(2)">提交审核</el-button>
                </div>
            </template>
        </el-dialog>
      <el-dialog v-model=" prohibitionAndLiftingVisible " :before-close=" prohibitionAndLiftingClose "
                 :title=" prohibitionAndLiftingTitle "
                 width="30%">
            <el-form :model=" form " label-width="100px">
                <el-form-item label="企业名称：">
                    <span>{{ customLabelValue.enterpriseName }}</span>
                </el-form-item>
                <el-form-item label="自编码：">
                    <span>{{ customLabelValue.supplierCoding }}</span>
                </el-form-item>
                <el-form-item label="原因：">
                  <el-input v-model="idea" placeholder="请填写原因" type="textarea"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="prohibitionAndLiftingVisible = false">取消</el-button>
                    <el-button type="primary" @click=" prohibitionAndLiftingSubmit ">确定</el-button>
                </span>
            </template>
        </el-dialog>
      <el-dialog v-model=" reviewVisible " :before-close=" () => reviewVisible = false " title="操作记录" width="50%">
            <el-table :data=" modalReviewList " border>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="操作时间" min-width="15%"
                               prop="type"
                               show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="类型" min-width="15%" prop="type"
                               show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="操作人" min-width="15%" prop="type"
                               show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="备注" min-width="55%" prop="type"
                               show-overflow-tooltip/>
            </el-table>
            <div style="display:flex;justify-content:end">
              <pagination v-show=" modalReviewTotal > 0 " v-model:limit=" modalReview.size "
                          v-model:page=" modalReview.current "
                          :total=" modalReviewTotal " @pagination=" getReviewList "/>
            </div>
        </el-dialog>
      <el-dialog v-model=" operateLogVisible " :before-close=" () => operateLogVisible = false " title="操作日志"
                 width="50%">
            <el-table :data=" operateLogList " border>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="操作时间" prop="type"
                               show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="操作人" prop="type"
                               show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="类型" prop="type"
                               show-overflow-tooltip/>
              <el-table-column align="center" class-name="small-padding" label="操作">
                    <template #default=" scope ">
                      <el-button icon="el-icon-document" link type="primary"
                            @click="handleLogChange(scope.row)">查看详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="display:flex;justify-content:end">
              <pagination v-show=" operateLogTotal > 0 " v-model:limit=" operateLog.size "
                          v-model:page=" operateLog.current "
                          :total=" operateLogTotal " @pagination=" getOperateLogList "/>
            </div>
        </el-dialog>
      <el-drawer v-model=" operateLogDetailVisible " :before-close=" () => operateLogDetailVisible = false "
                 direction="rtl" title="操作日志详情"
                 width="30%">
            <el-table :data=" operateLogListDetail " border>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="变更字段" prop="type"
                               show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="变更内容" prop="type"
                               show-overflow-tooltip/>
            </el-table>
        </el-drawer>
      <el-dialog v-if=" warrantyVisible " v-model=" warrantyVisible " :before-close=" () => handleImgWarrantyClose() "
                 :close-on-click-modal=" false "
                 :title=" warranty_title " width="30%">
        <el-form ref="warranty_formRef" :model=" warranty " :rules=" warranty_rules " label-width="90px">

                <el-form-item label="有效期" prop="qualityDate">
                  <el-date-picker v-model=" warranty.qualityDate " :disabled=" modalType == 'detail' "
                                  :disabled-date=" disabledDate "
                                  placeholder="请选择有效期" style="width:50%;" type="date"/>
                </el-form-item>
                <!-- <el-form-item label="编号" prop="warranty_code">
                        <el-input v-model="warranty.businessLicenseCode" placeholder="请输入编号"
                            :disabled="modalType == 'detail'" maxlength="100" show-word-limit clearable />
                    </el-form-item> -->

                <el-form-item label="上传图片" prop="qualityCode">
                  <el-upload ref="qualityCodeRef" v-model:file-list=" warranty.qualityCode " :action=" uploadUrl "
                             :before-upload=" (file) => beforeFile(file) "
                             :data=" { fileType: 'image', fjType: '质保协议', zhType: '供应商' } "
                             :disabled=" modalType == 'detail' " :headers=' headers '
                             :limit=" 1 " :on-exceed=" (files) => handleExceed(files, qualityCodeRef) "
                             :on-preview=" handlePictureCardPreview "
                             :on-success=" (res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 3) "
                             class="upload-demo"
                             drag
                             list-type="picture-card">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>

                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click=" () => handleImgWarrantyClose() ">取消</el-button>
                    <el-button type="primary" @click=" warrantySubmit ">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 委托新增弹窗 -->
      <el-dialog v-model=" entrustVisible " :before-close=" () => handleImgEntrustClose() " :title=" entrust_title "
                 width="65%">
        <el-form ref="entrust_formRef" :model=" entrust " :rules=" entrust_rules " label-width="130px">
                <div class="box_2">
                    <el-form-item label="类型" prop="status">
                        <el-radio-group v-model=" entrust.status ">
                          <el-radio v-for=" item  in  drugTypeList " :key=" item.value " :label=" item.value ">{{
                              item.name
                                }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="委托人姓名" prop="delegateName">
                      <el-input v-model=" entrust.delegateName " :disabled=" modalType == 'detail' " clearable
                                maxlength="100" placeholder="请输入委托人姓名" show-word-limit/>
                    </el-form-item>
                    <el-form-item label="身份证号" prop="certificateCard">
                      <el-input v-model=" entrust.certificateCard " :disabled=" modalType == 'detail' " clearable
                                maxlength="100" placeholder="请输入身份证号" show-word-limit/>
                    </el-form-item>
                    <el-form-item label="有效期" prop="effectiveTime">
                      <el-date-picker v-model=" entrust.effectiveTime " :disabled=" modalType == 'detail' "
                                      :disabled-date=" disabledDate "
                                      placeholder="请选择有效期" style="width: 100%;" type="date"/>
                    </el-form-item>
                    <el-form-item label="授权日期" prop="empowerTime">
                      <el-date-picker v-model=" entrust.empowerTime " :disabled=" modalType == 'detail' "
                                      placeholder="请选择授权日期"
                                      style="width: 100%;" type="date"/>
                    </el-form-item>
                    <el-form-item label="区域限制" prop="erpAddressList">
                      <el-select v-model=" entrust.erpAddressList " clearable filterable multiple
                                 placeholder="请选择省份" style="width: 100%">
                        <el-option v-for=" item  in  sysAreas " :key=" item.value " :disabled=" modalType == 'detail' "
                                   :label=" item.label "
                                   :value=" item.value "/>
                        </el-select>
                    </el-form-item>
                </div>
                <el-form-item label="委托商品范围" prop="isEntrustAll">
                    <el-radio-group v-model=" entrust.isEntrustAll ">
                        <el-radio label="1">所有{{ formDict(drugTypeList, entrust.status) }}药品</el-radio>
                        <el-radio label="2">部分商品</el-radio>
                    </el-radio-group>
                </el-form-item>

                <div class="box_2">
                    <el-form-item label="委托书" prop="remark">
                      <el-upload ref="remarkRef" v-model:file-list=" entrust.remark " :action=" uploadUrl "
                                 :before-upload=" (file) => beforeFile(file) "
                                 :data=" { fileType: 'image', fjType: '委托书', zhType: '客户' } " :headers=' headers '
                                 :limit=" 1 " :on-exceed=" (files) => handleExceed(files, remarkRef) "
                                 :on-preview=" handlePictureCardPreview "
                                 :on-success=" (res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 2) "
                                 class="upload-demo"
                                 drag
                                 list-type="picture-card">
                            <el-icon>
                                <Plus />
                            </el-icon>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="委托人身份证" prop="delegateCard">
                      <el-upload ref="delegateCardRef" v-model:file-list=" entrust.delegateCard " :action=" uploadUrl "
                                 :before-upload=" (file) => beforeFile(file) "
                                 :data=" { fileType: 'image', fjType: '委托人身份证', zhType: '客户' } "
                                 :headers=' headers ' :limit=" 1 "
                                 :on-exceed=" (files) => handleExceed(files, delegateCardRef) "
                                 :on-preview=" handlePictureCardPreview "
                                 :on-success=" (res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 6) "
                                 class="upload-demo"
                                 drag
                                 list-type="picture-card">
                            <el-icon>
                                <Plus />
                            </el-icon>
                        </el-upload>


                    </el-form-item>
                </div>

            </el-form>
            <div v-show=" entrust.isEntrustAll == '2' ">
              <el-input v-model=" modalentrustOut.commonValue " :disabled=" modalType == 'detail' " clearable
                        placeholder="请输入商品名或自编码" style="width:60%"/>
              <el-button style="margin-left:10px" type="primary" @click=" handleSearchShop_entrust ">搜索商品
              </el-button>
              <el-button :disabled=" !chooseList_entrust_side_table_list.length " style="margin-left:10px" type="danger"
                         @click=" handleDelte_entrust_out ">删除
              </el-button>
            </div>
            <!-- 表格数据 -->
        <el-table v-show=" entrust.isEntrustAll == '2' " :data=" entrust_side_table_list " border
                  style="margin-top: 30px;" @selection-change=" handleSelectionChange_side_table_list ">
          <el-table-column align="center" min-width="55" type="selection"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="自编码" min-width="120"
                           prop="commoditySelfCode"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="商品名" min-width="120"
                           prop="tradeName"/>
          <el-table-column :formatter=" (row) => commodityTypeDict[row.commodityType] " :show-overflow-tooltip=" true "
                           align="center" label="分类"
                           min-width="120" prop="commodityType"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="剂型" min-width="120"
                           prop="dosageForm"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="规格" min-width="120"
                           prop="packageSpecification"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="基本单位" min-width="140"
                           prop="basicUnit"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="生产厂家" min-width="120"
                           prop="manufacture.enterpriseName"/>
          <el-table-column align="center" label="生产地址" min-width="120" prop="originPlace"/>
            </el-table>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click=" () => handleImgEntrustClose() ">取消</el-button>
                    <el-button type="primary" @click=" entrustSubmit ">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 搜索商品 -->
      <el-dialog v-model=" entrustVisible_out " :before-close=" () => entrustVisible_out = false "
                 :show-close=" !entrust_out_table_list_loading " title="请选择委托商品"
                 width="50%">
            <!-- 表格数据 -->
        <el-table v-loading=" entrust_out_table_list_loading " :data=" entrust_out_table_list " border
                  style="margin-top: 30px;" @selection-change=" handleSelectionChange_out_table_list ">
          <el-table-column align="center" min-width="55" type="selection"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="自编码" min-width="120"
                           prop="commoditySelfCode"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="商品名" min-width="120"
                           prop="tradeName"/>
          <el-table-column :formatter=" (row) => commodityTypeDict[row.commodityType] " :show-overflow-tooltip=" true "
                           align="center" label="分类"
                           min-width="120" prop="commodityType"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="剂型" min-width="120"
                           prop="dosageForm"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="规格" min-width="120"
                           prop="packageSpecification"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="基本单位" min-width="140"
                           prop="basicUnit"/>
          <el-table-column :show-overflow-tooltip=" true " align="center" label="生产厂家" min-width="120"
                           prop="manufacture.enterpriseName"/>
          <el-table-column align="center" label="生产地址" min-width="120" prop="originPlace"/>
            </el-table>
            <div style="display:flex;justify-content:end;margin-top:20px">
              <pagination v-show=" modalentrust_out_Total > 0 " v-model:limit=" modalentrustOut.size "
                          v-model:page=" modalentrustOut.current " :total=" modalentrust_out_Total "
                    @pagination=" handleSearchShop_entrust " />
            </div>
            <template #footer>
                <span v-if=" !entrust_out_table_list_loading " class="dialog-footer">
                    <el-button @click="entrustVisible_out = false">取消</el-button>
                    <el-button type="primary" @click=" entrustSubmit_out ">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
      <el-drawer v-if=" fileListVisible " v-model=" fileListVisible " v-loading=" true "
                 :before-close=" () => fileListVisible = false "
                 direction="rtl" size="60%" title="附件列表">
            <div style="display:flex;justify-content:end">
              <el-button :disabled=" !fileListChooseList.length "
                         style="margin-top: 10px; margin-bottom:20px;margin-right:20px"
                         type="primary" @click=" handleExportFile ">导出
              </el-button>
            </div>
            <el-table :data=" fileListData " border @selection-change=" handleDetailFileListChoose ">
              <el-table-column align="center" min-width="55" type="selection"/>
              <el-table-column :formatter=" (row) => row.cardName || '--' " :show-overflow-tooltip=" true "
                               align="center" label="证件名称"
                               prop="cardName" show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="编号" prop="fileCoder"
                               show-overflow-tooltip/>
              <el-table-column :show-overflow-tooltip=" true " align="center" label="证件图片" min-width="280"
                               prop="fileName" show-overflow-tooltip>
                    <template #default=" scope ">
                        <el-button link type="primary" @click=" () => handlePictureCardPreview(scope.row.fileUrl) ">{{
                            scope.row.fileName }}</el-button>
                    </template>
                </el-table-column>
              <el-table-column :formatter=" (row) => row.range || '--' " :show-overflow-tooltip=" true " align="center"
                               label="范围" prop="range"
                               show-overflow-tooltip/>
              <el-table-column
                  :formatter=" (row) => (row.busType == '1' || row.busType == '2') ? row.busType == '1' ? '普药' : row.busType == '2' ? '特药' : '--' : row.busType "
                  :show-overflow-tooltip=" true " align="center" label="类型"
                  prop="busType"
                  show-overflow-tooltip/>
              <el-table-column :formatter=" (row) => row.remark || '--' " :show-overflow-tooltip=" true " align="center"
                               label="备注" prop="remark"
                               show-overflow-tooltip/>
              <el-table-column :formatter=" (row) => moment(row.createDate).format('YYYY-MM-DD') "
                               :show-overflow-tooltip=" true " align="center" label="归档时间"
                               prop="createDate" show-overflow-tooltip/>
            </el-table>
            <div style="display:flex;justify-content:end;margin-top:20px">
              <pagination v-show=" fileListDataTotal > 0 " v-model:limit=" fileListDataPage.size "
                          v-model:page=" fileListDataPage.current " :total=" fileListDataTotal "
                    @pagination=" handleFileList " />
            </div>

        </el-drawer>
      <el-dialog v-if=" licenseScopeVisible " v-model=" licenseScopeVisible "
                 :before-close=" () => licenseScopeVisible = false " style="padding:0 30px"
                 title="许可证范围" width="60%">
        <el-table v-loading=" lisloading " :data=" licenseScopeListData " :select-on-indeterminate=" true "
                  :tree-props=" { children: 'children' } "
                  border row-key="id" style="width: 100%;margin-bottom: 20px"
                  @row-click=" handleRowClick ">
          <el-table-column align=center label="选择" min-width="140">
                    <template #default=" scope ">
                        <el-checkbox v-model=" checkedKeys " :label=" scope.row.id "
                            @change=" (isChoose) => handleCheckChange(isChoose, scope.row) ">&nbsp;</el-checkbox>
                    </template>
                </el-table-column>
                <!-- <el-table-column type="selection" width="55" :reserve-selection="true"
                    :selectable="selectable"></el-table-column> -->
                <!-- <el-table-column type="selection" width="55" align="center"> </el-table-column> -->
          <el-table-column align="center" label="质量名称" min-width="250" prop="massName">
                </el-table-column>
          <el-table-column align="center" label="质量编号" min-width="180" prop="massNo">
                </el-table-column>
          <el-table-column align="center" label="属性分类" prop="massType">
                    <template #default=" scope ">
                        {{ scope.row.isStop == 1 ? "新范围" : "旧范围" }}
                    </template>
                </el-table-column>
          <el-table-column align="center" label="状态" prop="isStop">
                    <template #default=" scope ">
                        {{ scope.row.isStop == 1 ? "正常" : "停用" }}
                    </template>
                </el-table-column>
          <el-table-column :formatter=" row => row.remark ? row.remark : '--' " align="center" label="备注"
                           min-width="150px"
                           prop="remark"/>
            </el-table>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="licenseScopeVisible = false">取消</el-button>
                    <el-button v-if=" modalType !== 'detail' " type="primary" @click=" lisSubmit ">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
      <Review v-if=" reviewVisible2 " :beforeClose=" beforeClose_review " :data=" reviewRow "
              :reviewVisible2=" reviewVisible2 "/>
      <detail v-if=" detailOpen " :beforeClose=" () => detailOpen = false " :detailData=" detailData "
              :detailOpen=" detailOpen "/>
      <viewImg v-if=" uploadVisible " :beforeClose=" () => uploadVisible = false " :src=" uploadViewImgUrl "
               :visible=" uploadVisible "/>
    </div>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs, watch} from 'vue'
import {ArrowDown, Plus, UploadFilled} from '@element-plus/icons-vue'
import moment from 'moment'
import {uuid} from 'vue-uuid';
import supplier from '@/api/erp/supplier';
import Review from './review.vue'
import custom from '@/api/erp/custom'
import {ElMessage, ElMessageBox, genFileId} from 'element-plus'
import manufacturerManagement from '@/api/erp/manufacturerManagement'
import tool from '@/utils/tool';
import TopTitle from '@/components/topTitle'
import isDeleteAuthority from '@/utils/isDeleteAuthority'
import detail from './detail.vue'

const { proxy } = getCurrentInstance();
const typeDict = {
    '1': '药品',
    '2': '三类',
    '3': '一类',
    '4': '二类',
    '5': '食品',
    '6': '消杀',
    '7': '其他'
}
const titleDict = {
    add: "新增",
    edit: "编辑",
    detail: "详情",
    draft: "草稿"
}
const commodityTypeDict = {
    '1': '药品',
    '2': '器械',
    '3': '消杀',
    '4': '食品'
}
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const delegateCardRef = ref(null)
const detailOpen = ref(false)
const detailData = ref({})
const erpCustomersQualityList = ref([])
const erpCustomersDelegateList = ref([])
const erpCustomersDelegateCommodityList = ref([])
const licenseScopeList = ref([])
const operateLogDetailVisible = ref(false)
const operateLogListDetail = ref([])
const operateLogTotal = ref(0)
const operateLogVisible = ref(false)
const operateLogList = ref([])
const reviewVisible = ref(false)
const modalReviewList = ref([])
const modalReviewTotal = ref(0)
const modalType = ref("")
const prohibitionAndLiftingVisible = ref(false)
const prohibitionAndLiftingTitle = ref("")
const list = ref([]);
const reviewRow = ref({})
const open = ref(false);
const loading = ref(false);
const showSearch = ref(false)
const total = ref(0);
const title = ref("");
const activeNames = ref(['1'])
const tabKey = ref('1')
const modalTableList = ref([])
const licensedata = ref({ licenseImg: [], licenceScopes: [], type: '1' })
const modalTableList_file = ref([])
const chooseList = ref([])
const chooseList_entrust = ref([])
const modalTableList_entrust = ref([]) // 委托书列表
const modalTableList_warranty = ref([]) // 质保协议列表数据
const chooseList_warranty = ref([]) // 质保协议选中数据
const warrantyVisible = ref(false)
const warranty_title = ref("")
const warranty = ref({})
const entrustVisible = ref(false)
const entrust_title = ref("")
const entrust = ref({
    status: '1'
})
const entrust_side_table_list = ref([])
const entrust_out_table_list = ref([])
const entrustVisible_out = ref(false)
const chooseList_entrust_side_table_list = ref([])
const chooseList_entrust_out_table_list = ref([])
const sysAreas = ref([])
const delFlagList = ref([])
const reviewStatus = ref([])
const qualificationList = ref([])
const modeOfList = ref([])
const drugTypeList = ref([])
const businessList = ref([])
const uploadVisible = ref(false)
const uploadViewImgUrl = ref("")
const batchTypeList = ref([])
const retailTypeList = ref([])
const orgTypeList = ref([])
const fileSelectList = ref([])
const modalentrust_out_Total = ref(0)
const modalLoding = ref(false)
const warranty_copy = ref({})
const entrust_copy = ref({})
const customLabelValue = ref({})
const editRow = ref({})
const idea = ref("")
const fileListVisible = ref(false)
const fileListData = ref([])
const fileListDataTotal = ref(0)
const fileListChooseList = ref([])
const entrust_out_table_list_loading = ref(false)
const isChange = ref(false)
const credentialTypeRef = ref(null)
const shopType = ref([])
const busScopes = ref([])
// gsp
const modalTableList_GMP = ref([])
const gmpCertificatePictureRef = ref(null)
const chooseList_GMP = ref([])
const deleteGmpList = ref([])
const gmpCertificateScopeRef = ref(null)
const manufacturerType = ref([])
const directoryFileName = ref([])
const dosageForm = ref([])
const data = reactive({
    form: {
        credentialType: "1",
    },
    queryParams: {
        current: 1,
        size: 10,
    },
    modalentrustOut: {
        current: 1,
        size: 10,
    },
    modalReview: {
        current: 1,
        size: 10,
    },
    operateLog: {
        current: 1,
        size: 10,
    },
    modalentrust_out: {
        current: 1,
        size: 10,
        name: undefined
    },
    fileListDataPage: {
        current: 1,
        size: 10,
    },
    rules: {
        enterpriseName: [{ required: true, message: "企业名称不能为空", trigger: "blur" }],
        supplierCoding: [{ required: true, message: "自编码不能为空", trigger: "blur" },],
        credentialType: [{ required: true, message: "资质类别不能为空", trigger: "change" }],
        socialCreditCode: [{ required: true, message: "统一社会信用代码不能为空", trigger: "blur" }],
        businessTerm: [{ required: true, message: "营业期限不能为空", trigger: "change" }],
        // residence: [{ required: true, message: "住所不能为空", trigger: "blur" }],
        businessNature: [{ required: true, message: "经营范围不能为空", trigger: "blur" }],
        issuingOffice: [{ required: true, message: "发证机关不能为空", trigger: "blur" }],
    },
    modalTableListRules: {
        licenseNo: [{ required: true, message: "许可证编号不能为空", trigger: "blur" }],
        licenceScopesName: [{ required: true, message: "范围不能为空", trigger: 'blur' }],
        licenseStartTime: [{ required: true, message: "发证日期不能为空", trigger: "change" }],
        licenseValidity: [{ required: true, message: "有效期不能为空", trigger: "change" }],
        licenseOffice: [{ required: true, message: "发证机关不能为空", trigger: "blur" }],
        // licenseDirector: [{ required: true, message: "企业负责人不能为空", trigger: "blur" }],
        // qualityDirector: [{ required: true, message: "质量负责人不能为空", trigger: "blur" }],
        licenseImg: [{ required: true, message: "许可证图片不能为空", trigger: "change" }],

    },
    entrust_rules: {
        status: [{ required: true, message: "类型不能为空", trigger: "change" }],
        delegateName: [{ required: true, message: "委托人姓名不能为空", trigger: "blur" }],
        certificateCard: [{ required: true, message: "身份证号不能为空", trigger: "blur" },
        { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }],
        effectiveTime: [{ required: true, message: "有效期不能为空", trigger: "change" }],
        empowerTime: [{ required: true, message: "授权日期不能为空", trigger: "change" }],
        // erpAddressList: [{ required: true, message: "区域限制不能为空", trigger: "change" }],
        isEntrustAll: [{ required: true, message: "委托商品不能为空", trigger: "change" }],
        remark: [{ required: true, message: "委托书不能为空", trigger: "change" }],
        // delegateCard: [{ required: true, message: "委托人身份证不能为空", trigger: "change" }],
    },
    warranty_rules: {
        qualityDate: [{ required: true, message: "有效期不能为空", trigger: "change" }],
        qualityCode: [{ required: true, message: "上传图片不能为空", trigger: "change" }],
    }
});
const remarkRef = ref(null)
const qualityCodeRef = ref(null)
const fileListRef = ref(null)
const licenseImgRef = ref(null)
const zylicenseProjectOption = ref([])
const massRangeOption = ref([])
const chooseLicenseScope = ref([])
const licenseScopeRow = ref({})
const reviewVisible2 = ref(false)
const checkedKeys = ref([])
const lisloading = ref(false)
const licenseScopeVisible = ref(false)
const licenseScopeListData = ref([])
const licenseScopeListData_xx = ref([])
const rangeType = ref(undefined)
const credentialType_oldValue = ref(undefined)
const isClearFileWaring = ref(true)
const { queryParams, form, rules, modalReview, operateLog, modalentrust_out, modalentrustOut, entrust_rules, warranty_rules, fileListDataPage, modalTableListRules } = toRefs(data);
const tableRowStyle = ({ row, rowIndex }) => {
    if (row.status == '6' && row.customLabel == '2') {  // 草稿
        return {
            color: '#e6a23c'
        }
    } else if (row.status == '1' && row.customLabel == '2') {  // 待审核
        return {
            color: '#409eff'
        }
    } else if (row.status == '2' && row.customLabel == '2') {  //审核中
        return {
            color: '#67c23a'
        }
    } else if (row.status == '4' && row.customLabel == '2') {  //已驳回
        return {
            color: '#ff4800'
        }
    } else if (row.customLabel == '1') {   // 禁用
        return {
            color: '#f56c6c'
        }
    }
}
// gsp方法  start
const handleAdd_gsp = () => {
    modalTableList_GMP.value.push({ uuid: uuid.v1(), gmpCertificatePicture: [], isAdd: true })
}
const handleDelete_gsp = () => {
    proxy.$confirm('是否确认删除选中数据项?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        const ids = []
        chooseList_GMP.value.forEach(item => {
            if (!item.isAdd) {
                ids.push(item.id)
            }
        })
        if (ids?.length) {
            deleteGmpList.value = [...deleteGmpList.value, ...ids]
            modalTableList_GMP.value = modalTableList_GMP.value.filter((item) => !(chooseList_GMP.value.some((ele) => ele.uuid === item.uuid)));
            proxy.msgSuccess('删除成功')
        } else {
            modalTableList_GMP.value = modalTableList_GMP.value.filter((item) => !(chooseList_GMP.value.some((ele) => ele.uuid === item.uuid)));
            proxy.msgSuccess('删除成功')
        }

    }).catch(() => {

    })
}
const handleSelectionChange_GMP = (value) => {
    chooseList_GMP.value = value

}
// end
const handleSelectFcous = () => {
    credentialType_oldValue.value = form.value.credentialType
}
watch(() => modalTableList.value, (newValue, oldValue) => {
    console.log(open.value);
    if (!open.value || isClearFileWaring.value) return
    if (modalTableList_file.value?.length) {
        ElMessageBox.confirm("修改会清空附件列表？", '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(res => {
            modalTableList_file.value = []
        }).catch(() => {

        })
    }
}, { deep: true })
const handleCredentialType = (value) => {
    form.value.credentialType = credentialType_oldValue.value
    proxy.$confirm('切换会清空自编码、质量信息列表、委托书列表和附件列表，确认切换吗？', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        credentialTypeRef.value.blur()
        proxy.$refs["formRef"].resetFields()
        form.value.credentialType = value
        form.value.supplierCoding = undefined
        modalTableList_file.value = []
        modalTableList.value = []
        licensedata.value = { licenseImg: [], licenceScopes: [], type: '1' }
        modalTableList_entrust.value = []
        tabKey.value = '1'
        handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
        getSupplierCoding()
    }).catch(() => {

    })

}
const getSupplierCoding = async () => {
    const res = await supplier.getSelfCodeByType({ type: form.value.credentialType })
    if (res.code === 200) {
        form.value.supplierCoding = res.data
    } else {
        proxy.msgError('自编码获取失败')
    }

}
const handleLicenseScopeGmp = (row) => {
    console.log(row);
    rangeType.value = 'gmp'
    chooseLicenseScope.value = []
    licenseScopeListData.value = []
    checkedKeys.value = []
    licenseScopeVisible.value = true
    licenseScopeRow.value = row
    const shopKey = shopType.value.filter(item => item.name === '药品')?.[0]?.id
    if (shopKey) {
        lisloading.value = true
        custom.getWightConfig({ dictid: shopKey }).then(res => {
            if (res.code == 200) {
                lisloading.value = false
                res.data.forEach(v => {
                    if (v.length) {
                        licenseScopeListData.value.push(v[0])
                    }

                })
                licenseScopeRow.value?.gmpWholesaleScopes?.forEach(v => {
                    checkedKeys.value.push(v.massRangeSet?.id)
                })

            }
        }).finally(() => {
            lisloading.value = false
        })
    } else {
        lisloading.value = false
    }
}
// 获取附件列表
const file_loading = ref(false)
const handleGetFileList = async () => {
    isClearFileWaring.value = true
    file_loading.value = true
    await isaddItem()
    const ids = []
    const smallType = []
    modalTableList.value?.forEach(item => {
        ids?.push(typeDict[item.type] + form.value.credentialType)
    })
    manufacturerType.value?.forEach(item => {
        if (ids.includes(item.code)) {
            smallType.push(item.value)
        }
    })
  let smallTypes = []
  modalTableList.value?.forEach(item => {
    if (item.licenseNo) {
      if (item.type == '1' && smallTypes.indexOf(1) == -1) {
        smallTypes.push(1)
      } else if ((item.type == '2' || item.type == '3' || item.type == '4') && smallTypes.indexOf(2) == -1) {
        smallTypes.push(2)
      } else if (item.type == '5' && smallTypes.indexOf(3) == -1) {
        smallTypes.push(3)
      } else if (item.type == '6' && smallTypes.indexOf(4) == -1) {
        smallTypes.push(4)
      }
    }
  })
  manufacturerManagement.getFileList({
    bigType: '6',
    smallType: smallTypes?.length ? smallTypes.toString() : undefined,
    isFile: 1,
    size: 1000
  }).then(res => {
        if (res.code == 200) {
            modalTableList_file.value = res?.data?.records

        } else {
            proxy.msgError(res.msg)
        }
        file_loading.value = false
    }).finally(() => {
        file_loading.value = false
    })
    isClearFileWaring.value = false
    // modalTableList_file.value = []
}
const handleDeleteFile = (scopeIndex, index) => {
    modalTableList_file.value[scopeIndex].fileList.splice(index, 1)
    proxy.msgSuccess('删除成功！')
}
const handleSocialCreditCodeBlur = () => {
    if (form.value.socialCreditCode ?? false) {
        supplier.checkLicenseCode({ code: form.value.socialCreditCode, id: form.value?.id }).then(res => {
            if (res.data) {
                proxy.msgError('统一社会信用代码已经存在！请重新输入')
                form.value.socialCreditCode = undefined
            }
        })
    }
}
const lisSubmit = () => {
    checkedKeys.value = [...new Set(checkedKeys.value)]
    if (rangeType.value == 'isApparatus') {
        form.value.apparatusFirstScopes = []
        checkedKeys.value.forEach(x => {
            form.value.apparatusFirstScopes.push({
                massRangeSet: {
                    id: x
                }
            })
        })
    } else if (rangeType.value == 'disinfectionAndSterilization') {
        licenseScopeRow.value.sterilizedScopes = []
        checkedKeys.value.forEach(x => {
            licenseScopeRow.value.sterilizedScopes.push({
                massRangeSet: {
                    id: x
                }
            })
        })
    } else if (rangeType.value == 'gmp') {
        licenseScopeRow.value.gmpWholesaleScopes = []
        checkedKeys.value.forEach(x => {
            licenseScopeRow.value.gmpWholesaleScopes.push({
                massRangeSet: {
                    id: x
                }
            })
        })
    } else {
        licenseScopeRow.value.licenceScopes = []
        checkedKeys.value.forEach(x => {
            licenseScopeRow.value.licenceScopes.push({
                massRangeSet: {
                    id: x
                }
            })
        })
    }

    const arr = treeToArray(licenseScopeListData.value)
    const chooseName = []
    arr.forEach(item => {
        if (checkedKeys.value.includes(item.id)) {
            chooseName.push(item.massName)
        }
    })
    if (rangeType.value == 'isApparatus') {
        form.value.apparatusScopeName = chooseName.toString()
    } else if (rangeType.value == 'disinfectionAndSterilization') {
        form.value.sterilizedScopesName = chooseName.toString()
    } else if (rangeType.value == 'gmp') {
        licenseScopeRow.value.gmpWholesaleScope = chooseName.toString()
    }
    else {
        licenseScopeRow.value.licenceScopesName = chooseName.toString()
    }

    licenseScopeVisible.value = false
}
const viewSelection = (row, isChoose) => {
    const forTreeChoose = (arr) => {
        arr.forEach(v => {
            checkedKeys.value.push(v.id)
            checkedKeys.value = [...new Set(checkedKeys.value)]
            if (v.children?.length) {
                forTreeChoose(v.children)
            }
        })
    }
    const forTreeChoose_no = (arr) => {
        arr.forEach(v => {
            checkedKeys.value = [...checkedKeys.value].filter(x => x != v.id)
            checkedKeys.value = [...new Set(checkedKeys.value)]
            if (v.children?.length) {
                forTreeChoose_no(v.children)
            }
        })
    }
    row.forEach(x => {
        if (isChoose) {
            checkedKeys.value.push(x.id)
            checkedKeys.value = [...new Set(checkedKeys.value)]
            if (x.children?.length) {
                forTreeChoose(x.children)

            }
        } else {
            // indeterminate
            checkedKeys.value = [...checkedKeys.value].filter(s => s != x.id)
            checkedKeys.value = [...new Set(checkedKeys.value)]
            if (x.children?.length) {
                forTreeChoose_no(x.children)

            }
        }

    })
}
const handleCheckChange = (isChoose, row) => {
    viewSelection([row], isChoose)
}
function treeToArray(tree) {
    return tree.reduce((res, item) => {
        const { children, ...i } = item
        return res.concat(i, children && children.length ? treeToArray(children) : [])
    }, [])
}
const handleQualityList = (params) => {
    lisloading.value = true
    manufacturerManagement.erpBaseCommonValues({ ...params }).then(res => {
        if (res.code == 200) {
            lisloading.value = false
            if (params['item.id'] === '1' && form.value?.credentialType === '1') {
                licenseScopeListData_xx.value = res.data?.records
            } else {
                licenseScopeListData.value = res.data?.records
            }

        }
        licenseScopeRow.value.licenceScopes?.forEach(v => {
            checkedKeys.value.push(v?.massRangeSet?.id)
        })
    })
}
const handleRange = type => {
    if (modalTableList_entrust.value?.length) {
        proxy.$confirm('更新会清空委托书列表,确认操作吗?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            modalTableList_entrust.value = []

        }).catch(() => {

        })
    }
    rangeType.value = type
    chooseLicenseScope.value = []
    licenseScopeListData.value = []
    licenseScopeListData_xx.value = []
    checkedKeys.value = []
    licenseScopeRow.value = form.value

    let shopKey = undefined
    if (type === 'isApparatus') {
        licenseScopeVisible.value = true
        shopKey = shopType.value.filter(item => item.name === typeDict['3'])?.[0]?.id
    } else {
        licenseScopeVisible.value = true
        shopKey = shopType.value.filter(item => item.name === typeDict['6'])?.[0]?.id
    }
    if (shopKey) {
        lisloading.value = true
        custom.getWightConfig({ dictid: shopKey }).then(res => {
            if (res.code == 200) {
                lisloading.value = false
                res.data.forEach(v => {
                    if (v.length) {
                        licenseScopeListData.value.push(v[0])
                    }

                })
                // licenseScopeListData.value = res.data[0]
                if (type === 'isApparatus') {
                    licenseScopeRow.value.apparatusFirstScopes?.forEach(v => {
                        checkedKeys.value.push(v.massRangeSet.id)
                    })
                } else {
                    licenseScopeRow.value.sterilizedScopes?.forEach(v => {
                        checkedKeys.value.push(v.massRangeSet.id)
                    })
                }


            }
        }).finally(() => {
            lisloading.value = false
        })
    } else {
        lisloading.value = false
    }
}
const handleLicenseScope = (scope) => {
    if (modalTableList_entrust.value?.length) {
        proxy.$confirm('更新会清空委托书列表,确认操作吗?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            modalTableList_entrust.value = []

        }).catch(() => {

        })
    }
    rangeType.value = undefined
    chooseLicenseScope.value = []
    licenseScopeListData.value = []
    checkedKeys.value = []
    licenseScopeRow.value = scope
    if (scope.type == '1' && form.value.credentialType == '2') {
        handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
        return
    }
    if (scope.type == '6') {
        handleQualityList({ 'item.id': '1', isStop: '1', current: 1, size: 999 })
        return
    }


    licenseScopeVisible.value = true
    const shopKey = shopType.value.filter(item => item.name === typeDict[scope.type])?.[0]?.id
    if (shopKey) {
        lisloading.value = true
        custom.getWightConfig({ dictid: shopKey }).then(res => {
            if (res.code == 200) {
                lisloading.value = false
                res.data.forEach(v => {
                    if (v.length) {
                        licenseScopeListData.value.push(v[0])
                    }

                })
                licenseScopeRow.value.licenceScopes?.forEach(v => {
                    checkedKeys.value.push(v.massRangeSet?.id)
                })

            }
        }).finally(() => {
            lisloading.value = false
        })
    } else {
        lisloading.value = false
    }
}
const filterAddress = (erpAddressList) => {
    const addressArr = []
    sysAreas.value?.forEach(x => {
        erpAddressList.forEach(v => {
            if (x.value == v) {
                addressArr.push(x.label)
            }
        })
    })
    return addressArr.toString()
}
const handleExceed = (files, ref) => {
    ref.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    ref.handleStart(file)
    ref.submit()
}
const handleImgWarrantyClose = () => {
    if (warranty_title.value = "修改" && warranty.value.id) {
        modalTableList_warranty.value.forEach(item => {
            item = warranty_copy.value
        })
    }
    warranty_copy.value = {}
    warranty.value = {}
    warrantyVisible.value = false
}
const handleExportFile = () => {
    const chooseArr = []
    fileListChooseList.value.forEach(v => {
        chooseArr.push(v.fileUrl)
    })
    manufacturerManagement.exportFile({ fileNames: chooseArr.toString() }).then(res => {
        proxy.download(res, "application/zip")
    })
}
const handleDetailFileListChoose = (key) => {
    fileListChooseList.value = key
}

const handleFileList = () => {
    fileListVisible.value = true
    manufacturerManagement.getFileLists({ commonId: form.value.id, commonType: '11', ...fileListDataPage.value }).then(res => {
        if (res.code == 200) {
            fileListData.value = res.data?.records
            fileListDataTotal.value = res.data?.total
        }
    })
}

const handleImgEntrustClose = () => {
    if (entrust_title.value = "修改" && entrust.value.id) {
        modalTableList_entrust.value.forEach(v => {
            if (v.id == entrust.value.id) {
                v = entrust_copy.value
            }
        })
    }
    entrustVisible.value = false
}
const handlePictureCardPreview = (uploadFile) => {
    uploadViewImgUrl.value = uploadFile?.url || uploadFile
    uploadVisible.value = true
}
const handleClickOutside = (scope, key, e) => {
    if (e?.target?.parentNode?.parentNode?.parentNode?.parentNode.className.includes('selectClass') || e?.target?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode.className.includes('selectClass')) {
        return
    }
    // gmpCertificateScopeRef.value.blur()
    scope.row[key] = false
}
const filterArr = (option, key, value) => {
    if (!option?.length || !key || !value?.length) return
    const res = option.filter(v => value.includes(v[key]) === true)
    const NameArr = []
    res?.forEach(item => {
        NameArr.push(item?.valueName)
    })
    return NameArr?.toString()
}
// 获取所有经营范围
const getbusScopes = async () => {
    isClearFileWaring.value = true
    await isaddItem()
    const busScopesArr = []
    dosageForm.value = []
    if (form.value?.credentialType == '1') {

        form.value?.apparatusFirstScopes?.forEach(item => {
            busScopesArr.push(item?.massRangeSet?.id)
        })
        form.value?.sterilizedScopes?.forEach(item => {
            busScopesArr.push(item?.massRangeSet?.id)
        })

    }



    modalTableList.value?.forEach(item => {
        if ((item.type === '1' && form.value?.credentialType == '2')) {
            manufacturerManagement.erpBaseCommonValues({ 'item.id': '6', isStop: '1', current: 1, size: 999 }).then(res => {
                if (res.code == 200) {
                    item?.licenceScopesName?.forEach(value => {
                        res.data?.records?.forEach(val => {
                            console.log(val, value);
                            if (val.id === value) {
                                dosageForm.value.push(val.valueName)
                            }
                        })

                    })


                }
            })

        } else if ((item.type === '6' && form.value?.credentialType == '2')) {
            manufacturerManagement.erpBaseCommonValues({ 'item.id': '1', isStop: '1', current: 1, size: 999 }).then(res => {
                if (res.code == 200) {

                    item?.licenceScopesName?.forEach(value => {
                        res.data?.records?.forEach(val => {
                            console.log(val, value);
                            if (val.id === value) {
                                dosageForm.value.push(val.valueName)
                            }
                        })

                    })
                }
            })

        } else if (item?.licenceScopes?.length) {
            item?.licenceScopes?.forEach(value => {
                busScopesArr.push(value?.massRangeSet?.id)
                console.log(busScopesArr);
            })
        }
    })
    busScopes.value = busScopesArr
    isClearFileWaring.value = false
}
// 委托书新增
const handleAdd_entrust = (row) => {
    console.log(modalTableList.value);
    getbusScopes()
    entrust.value = {}
    entrust_side_table_list.value = []
    if (row) {
        // 修改
        entrust_title.value = "修改"
        // modalTableList_entrust.value
        entrust_copy.value = JSON.parse(JSON.stringify(row))
        entrust.value = { ...entrust_copy.value }
        entrust_side_table_list.value = entrust.value.delegateCommodityDTOS
    } else {
        // 新增
        entrust_title.value = "新增"
    }
    entrustVisible.value = true
}
// 委托书新增提交
const entrustSubmit = () => {
    proxy.$refs["entrust_formRef"].validate(valid => {
        if (valid) {
            entrust.value.effectiveTime = moment(entrust.value.effectiveTime).format("YYYY-MM-DD")
            entrust.value.empowerTime = moment(entrust.value.empowerTime).format("YYYY-MM-DD")
            if (entrust.value.isEntrustAll == '2') {
                entrust.value.delegateCommodityDTOS = entrust_side_table_list.value
            } else {
                entrust.value.delegateCommodityDTOS = []
            }
            if (entrust_title.value = "修改" && entrust.value.id) {
                modalTableList_entrust.value.forEach((v, i) => {
                    if (v.id == entrust.value.id) {
                        modalTableList_entrust.value[i] = entrust.value
                    }
                })
            } else {
                entrust.value.id = uuid.v1()
                modalTableList_entrust.value = [...modalTableList_entrust.value, entrust.value]
            }

            entrustVisible.value = false

            entrust.value = {}
            entrust_side_table_list.value = []
        }
    })


}
// 质保协议新增提交
const warrantySubmit = () => {
    proxy.$refs["warranty_formRef"].validate(valid => {
        if (valid) {
            if (warranty_title.value = "修改" && warranty.value.id) {
                modalTableList_warranty.value.forEach((item, index) => {
                    modalTableList_warranty.value[index] = warranty.value
                })
            } else {
                warranty.value.id = uuid.v1()
                modalTableList_warranty.value = [...modalTableList_warranty.value, warranty.value]
            }

            warranty.value = {}
            warrantyVisible.value = false


        }
    })
}
// 质保协议新增
const handleAdd_warranty = (row) => {
    warranty.value = {}
    if (row) {
        // 修改
        warranty_title.value = "修改"
        warranty_copy.value = JSON.parse(JSON.stringify(row))
        warranty.value = { ...warranty_copy.value }
    } else {
        // 新增
        warranty_title.value = "新增"

    }
    warrantyVisible.value = true
}
// 质保协议删除
const handleDelete_warranty = () => {
    if (chooseList_warranty.value.length > 0) {
        proxy.$confirm('是否确认删除选中数据项?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            const ids = []
            chooseList_warranty.value.forEach(item => {
                if (!item.isAdd) {
                    ids.push(item.id)
                }
            })
            if (ids?.length) {
                // 质保协议删除
                erpCustomersQualityList.value = [...erpCustomersQualityList.value, ...ids]
                modalTableList_warranty.value = modalTableList_warranty.value.filter((item) => !(chooseList_warranty.value.some((ele) => ele.id === item.id)));
                proxy.msgSuccess('删除成功')
            } else {
                modalTableList_warranty.value = modalTableList_warranty.value.filter((item) => !(chooseList_warranty.value.some((ele) => ele.id === item.id)));
                proxy.msgSuccess('删除成功')
            }

        }).catch(() => {

        })
    }

}
const handleSelectionChange_warranty = (key) => {
    chooseList_warranty.value = key
}
//委托书选择商品提交
const entrustSubmit_out = () => {
    const ids = []
    entrust_side_table_list.value.forEach(x => ids.push(x.id))

    chooseList_entrust_out_table_list.value.forEach(v => {
        if (ids.includes(v.id)) {
            proxy.msgError('检测到商品名为' + v.tradeName + '已添加，已将该条数据过滤！')
        } else {
            entrust_side_table_list.value = [...entrust_side_table_list.value, v]
        }

    })
    entrustVisible_out.value = false
}
const handleSelectionChange_side_table_list = key => {
    chooseList_entrust_side_table_list.value = key
}
const handleSelectionChange_out_table_list = key => {
    chooseList_entrust_out_table_list.value = key
}

// 委托书搜索商品
const handleSearchShop_entrust = () => {
    entrustVisible_out.value = true
    entrust_out_table_list_loading.value = true
    // entrust_out_table_list.value = []
    // modalentrust_out_Total.value = 0
  // dosageForm
    custom.shopList_({ ...modalentrustOut.value, specialMedicineControl: entrust.value.status == '1' ? '0' : entrust.value.status == '2' ? '1' : undefined, busScopes: busScopes.value, dosageForm: Array.from(new Set(dosageForm.value)).toString() }).then(res => {
        if (res.code == 200) {
            entrust_out_table_list.value = res.data?.records
            // entrust_out_table_list.value.forEach(v => {
            //     if (entrust_side_table_list.value && entrust_side_table_list.value.length) {
            //         entrust_side_table_list.value.forEach(x => {
            //             if (v.id === x.id) {
            //                 entrust_out_table_listRef.value.toggleRowSelection(v, true)

            //             }

            //         })
            //     }

            // })

            modalentrust_out_Total.value = res.data?.total
            entrust_out_table_list_loading.value = false
        }
    }).finally(() => {
        entrust_out_table_list_loading.value = false
    })


}
// 委托书删除搜索商品
const handleDelte_entrust_out = () => {
    proxy.$confirm('是否确认删除选中数据项?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        const ids = []
        chooseList_entrust_side_table_list.value.forEach(item => {
            if (!item.isAdd) {
                ids.push(item.id)
            }
        })
        if (ids?.length) {
            // 委托商品删除
            erpCustomersDelegateCommodityList.value = [...erpCustomersDelegateCommodityList.value, ...ids]
            entrust_side_table_list.value = entrust_side_table_list.value.filter((item) => !(chooseList_entrust_side_table_list.value.some((ele) => ele.id === item.id))) || [];
            proxy.msgSuccess('删除成功')
        } else {
            entrust_side_table_list.value = entrust_side_table_list.value.filter((item) => !(chooseList_entrust_side_table_list.value.some((ele) => ele.id === item.id))) || [];
            proxy.msgSuccess('删除成功')
        }

    }).catch(() => {

    })
}

// 委托书删除
const handleDelete_entrust = () => {
    if (chooseList_entrust.value.length > 0) {
        proxy.$confirm('是否确认删除选中数据项?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            const ids = []
            chooseList_entrust.value.forEach(item => {
                if (!item.isAdd) {
                    ids.push(item.id)
                }
            })
            if (ids?.length) {
                // 委托书删除
                erpCustomersDelegateList.value = [...erpCustomersDelegateList.value, ...ids]
                modalTableList_entrust.value = modalTableList_entrust.value.filter((item) => !(chooseList_entrust.value.some((ele) => ele.id === item.id)));
                proxy.msgSuccess('删除成功')

            } else {
                modalTableList_entrust.value = modalTableList_entrust.value.filter((item) => !(chooseList_entrust.value.some((ele) => ele.id === item.id)));
                proxy.msgSuccess('删除成功')
            }

        }).catch(() => {

        })
    }

}
// 委托书table多选
const handleSelectionChange_entrust = (key) => {
    chooseList_entrust.value = key
}
const disabledDate = (time) => {
    return time.getTime() < Date.now()
}
const disabledDate2 = (time) => {
    return time.getTime() > Date.now()
}
const handleLogChange = (row) => {
    operateLogDetailVisible.value = true
}
// 禁用or解禁提交
const prohibitionAndLiftingSubmit = () => {
    const params = {
        idea: idea.value,
        status: customLabelValue.value.customLabel == '1' ? '2' : '1',
        id: customLabelValue.value.id
    }
    supplier.delFlag(params).then(res => {
        if (res.code == 200) {
            proxy.msgSuccess(customLabelValue.value.customLabel == '1' ? '解禁成功' : '禁用成功')
            getList()
            customLabelValue.value = {}
        }
    }).finally(() => {
    })
    prohibitionAndLiftingVisible.value = false
}
// 关闭禁用or解禁
const prohibitionAndLiftingClose = () => {
    prohibitionAndLiftingVisible.value = false
}
// 操作记录
const handleLog = (row) => {
    reviewVisible2.value = true
    reviewRow.value = row
}
// 禁用
const handleDisabled = (row, type) => {
    prohibitionAndLiftingVisible.value = true
    idea.value = undefined
    customLabelValue.value = row
    if (type == '1') {
        prohibitionAndLiftingTitle.value = '设置禁用'
    } else {
        prohibitionAndLiftingTitle.value = '设置解禁'
    }

}
// 查看详情modal
// 查看详情modal
const handleDetail = (row, type) => {
    detailOpen.value = true
    detailData.value = row
    modalType.value = type
}
const handleEdit = (row, type) => {
    modalLoding.value = true
    open.value = true
    modalType.value = type
    title.value = titleDict[type]
    getLicenseScopeListOption()
    supplier.detail({ id: row.id }).then(res => {
        form.value = res.data?.supplierProductionDTO
        form.value.sterilized = form.value?.sterilized === '1' ? true : false
        form.value.apparatusFirst = form.value?.apparatusFirst == '1' ? true : false
        handleQualityList({ 'item.id': '1', isStop: '1', current: 1, size: 999 })
        modalTableList_entrust.value = res.data?.productionDelegateDTOList || []
        modalTableList_entrust.value?.forEach(item => {
            item.remark = item.remark ? [JSON.parse(item.remark)] : []
            item.delegateCard = item?.delegateCard ? [JSON.parse(item.delegateCard)] : []
            item.erpAddressList = item.erpAddressList?.split(",")
        })
        modalTableList_file.value = res.data?.commonFileDTOList || []
        modalTableList_file.value?.forEach(v => {
            v.fileList = [{ name: v.fileName, url: v.fileUrl }]
        })
        modalTableList.value = res.data?.productionLicenceDTOList || []
        modalTableList.value?.forEach(item => {
            item.type = Object.entries(typeDict).find(([key, val]) => val === item.type)[0]
            item.licenseImg = item.licenseImg ? JSON.parse(item.licenseImg) : []
            if (form.value.credentialType === '2') {
                if (item?.type == '1' || item?.type == '6') {
                    item.licenceScopes = item?.licenceScopesName?.split(',')
                    item.licenceScopesName = item?.licenceScopesName?.split(',')
                }
            }
            // if (form.value.credentialType === '1') {
            //     if (item?.type == '1') {
            //         item.licenceScopes = item?.licenceScopesName?.split(',')
            //         item.licenceScopesName = item?.licenceScopesName?.split(',')
            //     }
            // }
        })
      if (modalTableList.value) {
        if (modalTableList.value.length > 0) {
          licensedata.value = modalTableList.value[modalTableList.value.length - 1]
          tabKey.value = modalTableList.value[modalTableList.value.length - 1].type
        } else {
          licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
          tabKey.value = '1'
        }
      } else {
        licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
        tabKey.value = '1'
        modalTableList.value = []
      }
        if (licensedata.value?.type == '1') {
            handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
        }
        modalTableList_warranty.value = res.data?.productionQualityDTOList || []
        modalTableList_warranty.value?.forEach(item => {
            item.qualityCode = [JSON.parse(item.qualityCode)]
        })
        modalTableList_GMP.value = res.data?.gmpCertificateDTOList || []
        modalTableList_GMP.value?.forEach(item => {
            item.gmpCertificatePicture = item.gmpCertificatePicture ? [JSON.parse(item.gmpCertificatePicture)] : []
            item.gmpCertificateScope = item.gmpCertificateScope ? item.gmpCertificateScope?.split(',') : []
        })
        modalLoding.value = false
    }).finally(() => {
        modalLoding.value = false
    })
}
const handleInputEdit = (scope, type) => {
    if (modalType.value == 'detail') return
    scope.row[`isShow${type}`] = true
}
const beforeClose_review = () => {
    reviewVisible2.value = false
}
const handleUploadSuccess = (res, file, fileList, index, type) => {
    if (res.code == 200) {
        if (type == 1) {
            const fileList = [...licensedata.value.licenseImg, res.data]
            licensedata.value.licenseImg = fileList.filter(item => !item.response)
        }
        if (type == 2) {
            entrust.value.remark = [res.data]
        }
        if (type == 3) {
            warranty.value.qualityCode = [res.data]
        }
        if (type == 4) {
            const fileList = [...modalTableList_file.value[index].fileList, res.data]
            modalTableList_file.value[index].fileList = fileList.filter(item => !item.response)
        }
        if (type == 5) {
            modalTableList_GMP.value[index].gmpCertificatePicture = [res.data]
        }
        if (type == 6) {
            entrust.value.delegateCard = [res.data]
        }
    } else {
        proxy.msgError(res.msg)
    }
}
const handleSelectionChange = (key) => {
    chooseList.value = key
}
const handleChange = (value) => {
    activeNames.value = value
}
const isaddItem = async () => {
    let tem = false
    const setArr = async () => {
        modalTableList.value?.forEach((item, index) => {
            if (item.type === licensedata.value.type) {
                tem = true
                modalTableList.value[index] = licensedata.value
            }
        })
    }
    await setArr()
    if (!tem) {
        modalTableList.value = [...modalTableList.value, licensedata.value]
    }
    return modalTableList.value
}
const keys = async () => {
    let regKey = []
    if(form.value.credentialType === '1' && tabKey.value == '1'){
        regKey =   ["licenseNo", "licenseAddress", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg", "licenseLegalPerson"]
        return regKey
    }
    if(form.value.credentialType === '1' && tabKey.value !== '1'){
        regKey =   ["licenseNo",  "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseImg"]
        return regKey
    }
    if(form.value.credentialType === '2' && tabKey.value == '1'){
        regKey =   ["licenseNo",  "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseImg","registerAddress"]
        return regKey
    }
    if(form.value.credentialType === '2' && tabKey.value !== '1'){
        regKey =   ["licenseNo",  "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseImg"]
        return regKey
    }
}
// 储存数据  回显数据
const saveLicendata = async (value) => {
    modalTableList.value = await isaddItem()
    console.log(modalTableList.value);
    const data = modalTableList.value.filter(item => item.type === value)
    if (data?.length) {
        licensedata.value = data[0]

    } else {
        licensedata.value = { licenseImg: [], licenceScopes: [], type: value }
    }
    tabKey.value = value
    if (form.value.credentialType == '2') {
        if (value == '1') {
            handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
        }
        if (value == '6') {
            handleQualityList({ 'item.id': '1', isStop: '1', current: 1, size: 999 })
        }
    }
}
// 判断数组是否有该类型数据  有则回显  无则新增
const isLicenseData = (value) => {
    const cloneTabKey = JSON.parse(JSON.stringify(tabKey.value))
    if (regData()) {
        saveLicendata(value)
    } else {
        proxy.$refs["modalTableListRef"].validate((valid) => {
            if (valid) {
                saveLicendata(value)
            } else {
                tabKey.value = cloneTabKey
            }
        })
    }
}
// 检验该类型是否为空数据
const regData = async () => {
    let index = 0
    const data = licensedata.value
    const regKey = await keys()
    regKey?.forEach(item => {
        if (Array.isArray(data[item]) ? data[item]?.length : data[item]) {
            index++
        }

    })
    if (index === regKey.length || index === 0) {
        return true
    }
    return false
}

const handleTabClick = (value) => {
    isLicenseData(value)
}
/** 查询列表 */
function getList() {
    loading.value = true
    const params = {
        ...queryParams.value,
    }
    if (params?.createDate?.length) {
        params.beginCreateDate = moment(params?.createDate[0]).format('YYYY-MM-DD 00:00:00')
        params.endCreateDate = moment(params?.createDate[1]).format('YYYY-MM-DD 23:59:59')
        delete params.createDate
    }
    if (params?.updateDate?.length) {
        params.beginUpdateDate = moment(params?.updateDate[0]).format('YYYY-MM-DD 00:00:00')
        params.endUpdateDate = moment(params?.updateDate[1]).format('YYYY-MM-DD 23:59:59')
        delete params.updateDate
    }
    supplier.list(params).then(res => {
        if (res.code == 200) {
            list.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}
const formDict = (data, val) => {
    if (data.length && val) {
        return proxy.selectDictLabel(data, val)
    }

}
/** 搜索按钮操作 */
function handleQuery() {
    getList();
}
const getLicenseScopeListOption = async () => {
    let res = await manufacturerManagement.getLicenseScopeList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
    licenseScopeList.value = res?.data?.records
}
const handleAdd = async (row, type) => {
    open.value = true
    modalType.value = type
    title.value = titleDict[type]
    getLicenseScopeListOption()
    handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
    getSupplierCoding()
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
function closeDialog() {
    if (modalType.value !== 'detail') {
        ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
            .then(() => {
                open.value = false
                modalLoding.value = false
                form.value = {
                    credentialType: '1'
                }
                modalTableList.value = []
                licensedata.value = { licenseImg: [], licenceScopes: [], type: '1' }
                modalTableList_entrust.value = []
                modalTableList_warranty.value = []
                modalTableList_file.value = []
                modalTableList_GMP.value = []
                tabKey.value = '1'
                activeNames.value = '1'

                const query = proxy.$route?.query
                if (query && query.type == 'abnormalTask') {
                    proxy.$router.push('/abnormalTask')
                }


            })
            .catch(() => {
            });
    } else {
        open.value = false
        form.value = {
            credentialType: '1'
        }
        modalTableList.value = []
        licensedata.value = { licenseImg: [], licenceScopes: [], type: '1' }
        modalTableList_entrust.value = []
        modalTableList_warranty.value = []
        modalTableList_file.value = []
        modalTableList_GMP.value = []
        tabKey.value = '1'
        activeNames.value = '1'
        modalLoding.value = false

    }

}
const deleteNoModalListData = async () => {
  const modalTableListCopy = [...new Set(modalTableList.value.map(JSON.stringify))].map(JSON.parse)
    return  modalTableListCopy
}
// 新增供应商
function submitForm(type) {
    isClearFileWaring.value = true
  proxy.$refs["formRef"].validate(async (valid, fields) => {
        if (valid) {
            const deleteData = async () => {
              if (modalType.value != 'add') {

                    if (erpCustomersDelegateList.value?.length) {
                        await supplier.erpCustomersDelegate({ ids: [...erpCustomersDelegateList.value]?.toString() })
                    }
                    if (erpCustomersQualityList.value?.length) {
                        await supplier.erpCustomersQuality({ ids: [...erpCustomersQualityList.value]?.toString() })
                    }
                    if (erpCustomersDelegateCommodityList.value?.length) {
                        await supplier.erpCustomersDelegateCommodity({ ids: [...erpCustomersDelegateCommodityList.value]?.toString() })
                    }
                    // 删除gmp
                    if (deleteGmpList.value?.length) {
                        await supplier.deleteGmp({ ids: [...deleteGmpList.value]?.toString() })
                    }

                }
            }
            await deleteData()
            modalTableList.value = await deleteNoModalListData()
            const submitFormDatalist = () => {
                const supplierProductionDTO = JSON.parse(JSON.stringify(form.value))
                const productionLicenceDTOList = modalTableList.value?.length ? JSON.parse(JSON.stringify(modalTableList.value)) : []
                const productionDelegateDTOList = modalTableList_entrust.value?.length ? JSON.parse(JSON.stringify(modalTableList_entrust.value)) : []
                const productionQualityDTOList = modalTableList_warranty.value?.length ? JSON.parse(JSON.stringify(modalTableList_warranty.value)) : []
                const gmpCertificateDTOS = modalTableList_GMP.value?.length ? JSON.parse(JSON.stringify(modalTableList_GMP.value)) : []
                let commonFileDTOList = modalTableList_file.value?.length ? JSON.parse(JSON.stringify(modalTableList_file.value)) : []
                supplierProductionDTO.issuingTime = supplierProductionDTO.issuingTime ? moment(supplierProductionDTO.issuingTime).format("YYYY-MM-DD") : undefined
                supplierProductionDTO.businessTerm = supplierProductionDTO.businessTerm ? moment(supplierProductionDTO.businessTerm).format("YYYY-MM-DD") : undefined
                supplierProductionDTO.dateIncorporation = supplierProductionDTO.dateIncorporation ? moment(supplierProductionDTO.dateIncorporation).format("YYYY-MM-DD") : undefined
                supplierProductionDTO.apparatusFirst = supplierProductionDTO.apparatusFirst == true ? '1' : '0'
                supplierProductionDTO.sterilized = supplierProductionDTO.sterilized == true ? '1' : '0'
                if (!supplierProductionDTO.apparatusFirst || !supplierProductionDTO.apparatusFirstScopes?.length) {
                    delete supplierProductionDTO.apparatusFirstScopes
                    delete supplierProductionDTO.apparatusScopeName
                }
                if (!supplierProductionDTO.sterilized || !supplierProductionDTO.sterilizedScopes?.length) {
                    delete supplierProductionDTO.sterilizedScopes
                    delete supplierProductionDTO.sterilizedScopeName
                }
                const deleteKey = ['isShowbusiness', 'isShowlicenseAddress', 'isShowlicenseDirector', 'isShowlicenseLegalPerson', 'isShowlicenseNo', 'isShowlicenseOffice', 'isShowlicenseScope', 'isShowlicenseStartTime', 'isShowlicenseValidity', 'isShowqualityDirector', 'isShowzylicenseAddress', 'isShowzylicenseDirector', 'isShowzylicenseLegalPerson', 'isShowzylicenseName', 'isShowzylicenseNo', 'isShowzylicenseOffice']

                if (!productionDelegateDTOList?.length && type == 2) {
                    proxy.msgError(`请确认最少填写一条委托信息！`)
                    return
                }
                if (!productionQualityDTOList?.length && type == 2) {
                    proxy.msgError(`请确认最少填写一条质保协议！`)
                    return
                }
                productionLicenceDTOList.forEach(item => {
                    const regKey = ["licenseNo", "licenseAddress", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg", "licenseLegalPerson","licenceScopes","registerAddress"]
                    regKey.forEach(value => {
                        if (Array.isArray(item[value]) ? !item[value]?.length : !item[value]) {
                            delete item[value]
                        }
                    })
                    item.type = typeDict[item.type]
                    item.licenseStartTime = item.licenseStartTime ? moment(item.licenseStartTime).format("YYYY-MM-DD") : undefined
                    item.licenseValidity = item.licenseValidity ? moment(item.licenseValidity).format("YYYY-MM-DD") : undefined
                    item.licenseImg = JSON.stringify(item.licenseImg)
                    if ((Object.entries(typeDict)?.find(([key, val]) => val === item?.type)?.[0] == '1' && form.value.credentialType == '2') || Object.entries(typeDict)?.find(([key, val]) => val === item?.type)?.[0] == '6') {
                        const cloneliser = item.licenceScopesName ? JSON.parse(JSON.stringify(item.licenceScopesName)) : undefined
                        item.licenceScopesName = cloneliser?.toString() || undefined
                        const lisenArr = []
                        cloneliser?.forEach(v => {
                            lisenArr.push({
                                massRangeSet: {
                                    id: v
                                }
                            })
                        })
                        item.licenceScopes = lisenArr?.length ? lisenArr : undefined
                    }
                    deleteKey.forEach(x => {
                        delete item[x]
                    })
                })
                productionDelegateDTOList.forEach(item => {
                    item.remark = JSON.stringify(item.remark?.[0])
                    item.delegateCard = JSON.stringify(item.delegateCard?.[0])
                    item.erpAddressList = item.erpAddressList.toString()
                    if (item.delegateCommodityDTOS?.length) {
                        item.delegateCommodityDTOS?.forEach((x, i) => {
                            item.delegateCommodityDTOS[i] = {
                                commodityId: x.id,
                                commodityType: x.commodityType
                            }
                        })
                    }
                })
                productionQualityDTOList.forEach(item => {
                    item.qualityDate = moment(item.qualityDate).format("YYYY-MM-DD")
                    item.qualityCode = JSON.stringify(item.qualityCode?.[0])
                  if (modalType.value == 'add') {
                        delete item.id
                    }

                })
                let fileIndex = 0
                for (var item of commonFileDTOList) {
                    item.fileType =
                        item.cardName = item.categoryName
                    item.fileName = item.file
                    if (item.isUpload == '1' && !item?.fileList?.length) {
                        break
                    } else {
                        fileIndex++
                    }
                }
                const fileListArr = []
                const fileName_ = []
                const fileUrl_ = []
                commonFileDTOList.forEach(item => {
                    item.fileList?.forEach(val => {
                        fileName_.push(val?.name)
                        fileUrl_.push(val?.url)
                    })
                    fileListArr.push({
                        smallType: item.smallType,
                        categoryName: item.categoryName,
                        isMultiPage: item.isMultiPage,
                        fileType: item.smallType,
                        cardName: item.categoryName,
                        fileName: fileName_.toString(),
                        fileUrl: fileUrl_.toString(),
                        isUpload: item.isUpload,
                        isPage: item.isMultiPage,
                        fileNameUrl: JSON.stringify(item.fileList),
                        remark: item.remark
                    })
                })
                commonFileDTOList?.length && commonFileDTOList?.forEach(item => {
                    item.fileUrl = item.fileList?.length && item.fileList[0]?.url
                    item.fileName = item.fileList?.length && item.fileList[0]?.name

                })
                commonFileDTOList = fileListArr
                gmpCertificateDTOS?.forEach(item => {
                    item.gmpExpiredTime = item.gmpExpiredTime ? moment(item.gmpExpiredTime).format("YYYY-MM-DD") : undefined
                    item.gmpCertificatePicture = item.gmpCertificatePicture?.length && JSON.stringify(item.gmpCertificatePicture?.[0])
                    item.gmpCertificateScope = item.gmpCertificateScope?.length && item.gmpCertificateScope?.toString()
                })
                const params = {
                    supplierProductionDTO,
                    productionDelegateDTOList,
                    productionQualityDTOList,
                    commonFileDTOList,
                    productionLicenceDTOList,
                    gmpCertificateDTOList: gmpCertificateDTOS
                    // operate: 'submit'
                }
                if (type == 1) {
                    params.operate = 'save'
                }
                if (type == 2) {
                    params.operate = 'submit'
                }
                modalLoding.value = true
                // if (params.operate !== 'submit' && !commonFileDTOList?.length) return proxy.msgError('请填写附件信息！')
                if (fileIndex !== commonFileDTOList?.length) return proxy.msgError('请上传必传附件！')
                supplier.save(params).then(res => {
                    if (res.code == 200) {
                        open.value = false
                        ElMessage({
                            message: "操作成功",
                            type: "success",
                        });
                        form.value = {
                            credentialType: '1'
                        }
                        // modalTableList.value = []
                        licensedata.value = { licenseImg: [], licenceScopes: [], type: '1' }
                        modalTableList_entrust.value = []
                        modalTableList_warranty.value = []
                        modalTableList_file.value = []
                        modalTableList_GMP.value = []
                        tabKey.value = '1'
                        activeNames.value = '1'
                        modalLoding.value = false
                        getList()
                        const query = proxy.$route?.query
                        isClearFileWaring.value = false
                        if (query && query.type == 'abnormalTask') {
                            proxy.$router.push('/abnormalTask')
                        }
                    } else {
                        proxy.msgError(res.msg)
                        modalLoding.value = false
                    }
                }).finally(() => {
                    modalLoding.value = false
                })


            }
            if (type === 2) {
              proxy.$refs["modalTableListRef"].validate(async (valid, fields) => {
                    if (valid) {
                        await isaddItem()
                        submitFormDatalist()
                    } else {
                      Object.keys(fields).forEach((key, i) => {
                        const propName = fields[key][0].field
                        if (i == 0) {
                          proxy.$refs["modalTableListRef"].scrollToField(propName)
                        }
                      })
                      if (activeNames.value.indexOf('2') == -1) {
                        activeNames.value.push('2')
                      }
                    }
                })
            } else {
                await isaddItem()
                submitFormDatalist()
            }

        } else {
          Object.keys(fields).forEach((key, i) => {
            const propName = fields[key][0].field
            if (i == 0) {
              proxy.$refs["formRef"].scrollToField(propName)
            }
          })
          if (activeNames.value.indexOf('1') == -1) {
            activeNames.value.push('1')
          }
        }
    })
}
const beforeFile = (file) => {
    if (file.size > 2097152) {
        proxy.msgError("文件不能大于2M");
        return false;
    }
}

/** 删除按钮操作 */
function handleDelete(row) {
    proxy.$confirm('是否确认删除改数据项?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        supplier.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                proxy.msgSuccess('删除成功')
                getList()
            } else {
                proxy.msgError(res.msg)
            }
        })
    }).catch(() => { });
}

async function dict() {
    delFlagList.value = await proxy.getDictList('erp_delFlag_status')
    reviewStatus.value = await proxy.getDictList('erp_review_status')
    qualificationList.value = await proxy.getDictList('erp_supply_category')
    modeOfList.value = await proxy.getDictList("erp_mode_of_operation")
    drugTypeList.value = await proxy.getDictList("erp_drug_type")
    businessList.value = await proxy.getDictList("erp_business_type")
    batchTypeList.value = await proxy.getDictList("batch_type")  // 文件批发
    retailTypeList.value = await proxy.getDictList("retail_type")  // 文件零售
    orgTypeList.value = await proxy.getDictList("org_type")  // 文件零售
    fileSelectList.value = await proxy.getDictList("supply_batch_type")//文件类型
    shopType.value = await proxy.getDictList('product_type_config')
    manufacturerType.value = await proxy.getDictList('supplier_type')
    directoryFileName.value = await proxy.getDictList('directory_file_name')
}
const recursion = (option, key) => {
    option.forEach(v => {
        if (v[key]) {
            v.label = v[key]
            v.value = v.id
        }
        if (v.children?.length) {
            recursion(v.children, key)
        }
    })
}
const getRanges = async () => {
    let res = await custom.getRange()
    if (res.code == 200) {
        massRangeOption.value = res.data
        recursion(massRangeOption.value, 'massName')
    }
}
const getTerats = async () => {
    let res = await custom.getTerat()
    if (res.code == 200) {
        zylicenseProjectOption.value = res.data
        recursion(zylicenseProjectOption.value, 'treatmentName')
    }
}
onMounted(() => {
    const query = proxy.$route?.query
    if (query && query.id && query.type == 'abnormalTask') {
        modalLoding.value = true
        open.value = true
        modalType.value = 'edit'
        title.value = titleDict['edit']
        getLicenseScopeListOption()
        supplier.detail({ id: query.id }).then(res => {
            form.value = res.data?.supplierProductionDTO
            form.value.sterilized = form.value?.sterilized === '1' ? true : false
            form.value.apparatusFirst = form.value?.apparatusFirst == '1' ? true : false
            handleQualityList({ 'item.id': '1', isStop: '1', current: 1, size: 999 })
            modalTableList_entrust.value = res.data?.productionDelegateDTOList || []
            modalTableList_entrust.value?.forEach(item => {
                item.remark = item.remark ? [JSON.parse(item.remark)] : []
                item.delegateCard = item?.delegateCard ? [JSON.parse(item.delegateCard)] : []
                item.erpAddressList = item.erpAddressList?.split(",")
            })
            modalTableList_file.value = res.data?.commonFileDTOList || []
            modalTableList_file.value?.forEach(v => {
                v.fileList = [{ name: v.fileName, url: v.fileUrl }]
            })
            modalTableList.value = res.data?.productionLicenceDTOList || []
            modalTableList.value?.forEach(item => {
                item.type = Object.entries(typeDict).find(([key, val]) => val === item.type)[0]
                item.licenseImg = item.licenseImg ? JSON.parse(item.licenseImg) : []
                if (form.value.credentialType === '2') {
                    if (item?.type == '1' || item?.type == '6') {
                        item.licenceScopes = item?.licenceScopesName?.split(',')
                        item.licenceScopesName = item?.licenceScopesName?.split(',')
                    }
                }
            })
            licensedata.value = modalTableList.value[0]
            tabKey.value = modalTableList.value[0]?.type

            if (licensedata.value?.type == '1') {
                handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
            }
            modalTableList_warranty.value = res.data?.productionQualityDTOList || []
            modalTableList_warranty.value?.forEach(item => {
                item.qualityCode = [JSON.parse(item.qualityCode)]
            })
            modalTableList_GMP.value = res.data?.gmpCertificateDTOList || []
            modalTableList_GMP.value?.forEach(item => {
                item.gmpCertificatePicture = item.gmpCertificatePicture ? [JSON.parse(item.gmpCertificatePicture)] : []
                item.gmpCertificateScope = item.gmpCertificateScope ? item.gmpCertificateScope?.split(',') : []
            })
            modalLoding.value = false
        }).finally(() => {
            modalLoding.value = false
        })
    }
    getRanges()
    getTerats()
    dict()
    getList();
    sysAreas.value = proxy.getSysAreasThird.map(v => {
        return { value: v.value, label: v.label }
    })
    sysAreas.value = [{ label: '全国', value: '1', id: '1' }, ...sysAreas.value]
})

const columns = ref([
    {
        label: '序号',
        prop: 'commodityCommonName',
        type: "sort",
        fixed: 'left'
    },
    {
        label: '类型',
        prop: 'credentialType',
        type: 'status',
        filters: qualificationList
    },
    {
        label: '企业名称',
        prop: 'enterpriseName'
    },
    {
        label: '拼音码',
        prop: 'pinyinCode',
    },
    {
        label: '自编码',
        prop: 'supplierCoding',
    },
    {
        label: '统一社会信用代码',
        prop: 'socialCreditCode',
      minWidth: '130'
    },
  {
    label: '创建来源',
    prop: 'source',
    type: 'status',
    filters: [
      {
        name: '手工录入',
        value: '1'
      },
      {
        name: '智能录入',
        value: '2'
      },
    ],
  },
    {
        label: '审核状态',
        prop: 'status',
        type: 'status',
        filters: reviewStatus
    },
    {
        label: '禁用状态',
        prop: 'customLabel',
        type: 'status',
        filters: delFlagList
    },
    {
        label: '创建日期',
        prop: 'createDate',
        type: 'date'
    },
    {
        label: '修改日期',
        prop: 'updateDate',
        type: 'date'
    },
    {
        label: '操作',
        prop: 'operate',
        type: 'operate',
        minWidth: 300,
        fixed: 'right'
    },
])
</script>
<style lang="scss" scoped>
.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    // grid-template-rows: auto auto;
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-items: stretch;
    align-items: start;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.el-tabs {
    display: flex;
    // align-items: center;
}

.butns {
    float: right;
    margin: 18px 80px 30px 0;
}

.el-tabs__nav-wrap::after {
    width: 0 !important;
}

::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

::v-deep .rules {
    position: relative;

    .cell::after {
        content: "*";
        color: red;
        display: inline-block;
        position: absolute;
        top: 30%;
        left: 70px;
    }
}

::v-deep .rulesRemark {
    position: relative;

    .cell::after {
        content: "*";
        color: red;
        display: inline-block;
        position: absolute;
        top: 30%;
        left: calc(50% - 30px)
    }
}

.box_date {
    width: 220px;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
    display: contents !important;
}

.checkBox {
    margin-right: 30px;
    margin-left: 30px
}

.checkBoxRight {
    width: 640px
}

::v-deep .el-upload-dragger {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
}

::v-deep .step2 {
    .el-button {
        // border-bottom: none;
        border-right: none;
        border-radius: 4px 0 0 4px;

    }

    .el-button+.el-button {
        margin-left: 0;
        border-radius: 0 0 0 0;
        // border-left: none;
    }
}

.btn_cmt {
    margin-bottom: 20px;
}

.last-btn {
    border: 1px solid #dcdfe6 !important;
}

.fileName_t {
    display: flex;
    width: 100%;
    color: #2a76f8;
    cursor: pointer;
    align-items: center;
    margin-top: 4px;

    span:nth-of-type(2) {
        display: none;
        margin-left: 10px;

        ::v-deep .el-icon {
            // margin-top:10px;
            font-size: 12px;
            color: red
        }
    }
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type="number"] {
    -moz-appearance: textfield;
    /* 此处写不写都可以 */
}
</style>
