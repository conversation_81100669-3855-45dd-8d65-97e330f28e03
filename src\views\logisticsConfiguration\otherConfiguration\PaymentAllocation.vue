<template>
	<div class='app-container'>
		<el-form key="form" ref="form" :model="form" :rules="rules" label-width="auto" size="small">
			<el-card class="mb10" shadow="never">
        <card-header title="支付配置"/>
				<el-form-item :prop="form.fileUrl" class="is-required" label="支付二维码">
					<el-upload
						class="avatar-uploader"
						:action="uploadFileUrl"
						:show-file-list="false"
						:headers="headers"
						:before-upload="beforeUpload"
						:on-success="uploadSuccess"
						:on-preview="handlePictureCardPreview" :on-remove="handleRemove">
						<img v-if="dialogImageUrl" :src="dialogImageUrl" class="avatar">
						<i v-else class="el-icon-plus avatar-uploader-icon"></i>
					</el-upload>
					<el-dialog :v-model="dialogVisible">
						<img width="100%" :src="dialogImageUrl" alt="">
					</el-dialog>

				</el-form-item>
			</el-card>
		</el-form>
		<div class="foot__flex">
			<el-button type="primary" @click="submitForm">确认</el-button>
		</div>
	</div>

</template>

<script>
import CardHeader from '@/components/CardHeader';
import tool from "@/utils/tool";
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration.js'
export default {
	name: "PaymentAllocation",
  components: {
    CardHeader,
  },
	data() {
		return {
			form: {
			},
			headers: {
        Authorization: "Bearer " + tool.cookie.get("TOKEN"),
        ContentType: "multipart/form-data",
        clientType:'pc',
			},
			//文件上传定义
			uploadFileUrl: process.env.VUE_APP_API_UPLOAD, // 上传的图片服务器地址
			// 大小限制(MB)
			fileSize: 5,
			fileType: ['png', 'jpg', 'jpeg'],
			dialogImageUrl: null,
			dialogVisible: false,
			rules: {
				fileUrl: [
					{required: true, message: '支付二维码不能为空'}
				],
			},
		};
	},
	created() {
		this.getPayCode();
	},
	methods: {
		getPayCode(){
      otherConfiguration.getCarrierCodeFile().then(response => {
				if (response.code == 200 && response.data) {
          this.form.fileUrl = response.data.fileUrl
					this.dialogImageUrl = response.data.fileUrl;
				}
			});
		},
		// 提交
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
          otherConfiguration.saveCarrierCodeFile(this.form).then(response => {
						if (response.code == 200) {
							this.msgSuccess('保存成功')
							// this.cancel()
						} else {
							this.msgError('保存失败')
						}
					})
				}
			})
		},
		// 取消
		cancel() {
			this.form = {fileUrl:null};
			this.dialogImageUrl = null;

		},
		/** 上传成功 */
		uploadSuccess(response) {
			// response 为空代表首次进入页面（首次进入页面会触发此方法）
			if (!response) {
				return;
			}
			this.form.fileUrl = response.data.fileUrl;
			this.dialogImageUrl = response.data.fileUrl;
		},
		// 移除
		handleRemove(file, fileList) {
		},
		// 查看
		handlePictureCardPreview(file) {
			// this.dialogImageUrl = file.url;
			this.dialogVisible = true;
		},
		// 验证文件格式与大小
		beforeUpload(file) {
			// 校检文件类型
			if (this.fileType) {
				let fileExtension = ''
				if (file.name.lastIndexOf('.') > -1) {
					fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
				}
				const isTypeOk = this.fileType.some((type) => {
					if (file.type.indexOf(type) > -1) return true
					if (fileExtension && fileExtension.indexOf(type) > -1) return true
					return false
				})
				if (!isTypeOk) {
					this.$message.error(
						`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
					)
					return false
				}
			}
			// 校检文件大小
			if (this.fileSize) {
				const isLt = file.size / 1024 / 1024 < this.fileSize
				if (!isLt) {
					this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
					return false
				}
			}
			return true
		},
	}
}
</script>

<style scoped lang="scss">

.titleLayout {
	display: flex;
	justify-content: flex-start;
	align-items: center;

	.verticalBar {
		display: inline-block;
		background-color: #5670FE;
		width: 3px;
		height: 1em;
		margin-right: 8px;
	}

	.title {
		color: #5670FE;
	}
}
::v-deep.avatar-uploader .el-upload {
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}
.avatar-uploader .el-upload:hover {
	border-color: #409EFF;
}
.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 178px;
	height: 178px;
	line-height: 178px;
	text-align: center;
}
.avatar {
	width: 178px;
	height: 178px;
	display: block;
}
.foot__flex {
	display: flex;
	justify-content: center;
	padding-bottom: 15px;
}
</style>
