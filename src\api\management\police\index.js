import http from "@/utils/request"

export default {
// 查询列表
alarmConfigList: function (params) {
    return http.get('/device/alarmConfig/list', params)
  },
  // 新增
  alarmConfigSave: function (data) {
    return http.post("/device/alarmConfig/save", data);
  },
  // 删除
  alarmConfigDelete: function (params) {
    return http.delete("/device/alarmConfig/delete", params);
},
// 导出
alarmConfigExport: function (params, config, resDetail, responseType) {
    return http.get('/device/alarmConfig/export', params, config, resDetail, responseType, 1)
  },
   // 修改状态
   updateStatus: function (params) {
    return http.get('/device/alarmConfig/updateStatus', params)
  },
}
