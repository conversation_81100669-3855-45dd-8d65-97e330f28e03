<template>
  <div class="salesBack">
    <backSearch ref="searchRef" @handleQuery="getTableList"/>
    <el-card body-style="padding-top:10px" class="box-card last-card">
      <template #header>
        <div class="card-header">
          <span><el-button class="button" type="primary" @click="addBack()">创建销售退回申请</el-button></span>
        </div>
      </template>
      <div class="item">
        <backTable ref="tableRef" :loadingFlag="loadingFlag" :statusType="statusType" @delTable="delTable"
                   :tableList="data.tableList" @detailsTable="detailsTable" @getTableList="getTableList"
                   @logFn="logFn"/>
        <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                       :disabled="false" :page-sizes="[5, 10, 20, 50]" :small="false" :total="data.total"
                       layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                       @size-change="getTableList"
                       @current-change="getTableList"/>
      </div>

    </el-card>
    <el-dialog v-model="dialogVisible" :before-close="handleClose" title="销售退回申请" width="80%">
      <div class="step">
        <el-steps :active="active" align-center>
          <el-step title="选择出库单据"/>
          <el-step title="填写退货信息"/>
          <el-step title="提交申请"/>
        </el-steps>
      </div>
      <backForm ref="formChild" v-model:multipleTableRef="multipleTableRef" :active="active" :uuid="newUUID"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button v-if="active == 1" @click="cancalFn()">取消</el-button>
          <el-button v-else @click="upFn()">上一步</el-button>
          <el-button v-if="active == 3" type="primary" @click="rightBack()">
            确定
          </el-button>
          <el-button v-else type="primary" @click="nextTi()">
            下一步
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible2" title="查看详情" width="80%">
      <h4 slot="title" class="stateTitle">
        审核状态: {{ data.status }}
      </h4>
      <div v-loading="loadingDetailFlag" style="min-height: 200px">
        <backDetails ref="detailsChild" :inTable="data.inTable" :tableStr="data.tableStr"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible2 = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible3" title="操作记录" width="30%">
      <div v-loading="logFlag">
        <logQuery ref="childLog"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible3 = false">取消</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watch, watchEffect} from 'vue';
// import { useStore } from 'vuex';
import {useRoute, useRouter} from 'vue-router';
import backSearch from "../../components/salesManagement/backSearch.vue";
import backTable from './components/backTable.vue'
import backForm from './components/backForm.vue'
import backDetails from './components/backDetails.vue'
import {ElMessage, ElMessageBox, ElLoading} from "element-plus";
import {switchBack} from "@/views/salesManagement/switch";
import {backApi, manageApi} from "@/api/model/salesManagement";
import {functionIndex} from "@/views/commodity/functionIndex";
import {uuid} from "vue-uuid";

const tableRef = ref(null)
const {proxy} = getCurrentInstance();
/**
 * 仓库
 */
// const store = useStore();
/**
 * 路由对象
 */
watch(
    () => tableRef.value.searchForm,
    (newValue) => {
      searchRef.value.searchForm = newValue
    }
);
const logFlag = ref(false)
const route = useRoute();
/**
 * 路由实例
 */
const router = useRouter();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const searchRef = ref(null)
const loadingFlag = ref(false);
const formChild = ref(null)
const detailsChild = ref(null)
const childLog = ref(null)
const dialogVisible = ref(false)
const dialogVisible2 = ref(false)
const dialogVisible3 = ref(false)
const active = ref(1)
const data = reactive({
  tableList: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  tableStr: [],
  inTable: [],
  status: ""
})
const statusType = ref([])
const dict = async () => {
  statusType.value = await proxy.getDictList("status_sales")
  localStorage.setItem('salesType', JSON.stringify(statusType.value))
}
dict()
const emit = defineEmits([])
const props = defineProps({})
const multipleTableRef = ref(null)

const toggleSelection = () => {
  setTimeout(() => {
    formChild.value.chooseGoods.forEach((row) => {
      formChild.value.multipleTableRef?.toggleRowSelection(row, undefined)
    })
  })

}
const upFn = () => {
  active.value--
}
const echo = () => {
  if (data.tableStr.salesRetreat.auditStatus == "0") {
    data.status = "草稿";
  } else if (data.tableStr.salesRetreat.auditStatus == "1") {
    data.status = "待审核";
  } else if (data.tableStr.salesRetreat.auditStatus == "2") {
    data.status = "审核中";
  } else if (data.tableStr.salesRetreat.auditStatus == "3") {
    data.status = "审核通过";
  } else if (data.tableStr.salesRetreat.auditStatus == "4") {
    data.status = "被驳回";
  } else if (data.tableStr.salesRetreat.auditStatus == "5") {
    data.status = "撤销";
  } else if (data.tableStr.salesRetreat.auditStatus == "7") {
    data.status = "已驳回";
  } else {
    data.status = "未知";
  }
};
const delTable = (row) => {
  ElMessageBox.confirm("确认删除此项吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        ElLoading.service();
        backApi.delFn({
          ids: row.id
        }).then((res) => {
          if (res.code == 200) {
            ElMessage.success('删除成功')
            getTableList()
          } else {
            ElMessage.error('删除失败，错误信息：' + res.msg)
          }
          const loadingInstance = ElLoading.service();
          loadingInstance.close();
        })
      })
      .catch(() => {
      });
}
const loadingDetailFlag = ref(false)
const detailsTable = async (row) => {
  data.status = ''
  data.tableStr = []
  data.inTable = []
  dialogVisible2.value = true
  loadingDetailFlag.value = true
  const res1 = await backApi.getDetails({
    id: row.id
  })
  const res2 = await backApi.getPut({
    'salesRetreat.id': row.id
  })
  if (res1.code == 200 && res2.code == 200) {
    data.tableStr = res1.data
    data.inTable = res2.data.records
    echo()
  } else {
    ElMessage.error('获取失败')
  }
  loadingDetailFlag.value = false
}
const logFn = async (row) => {
  console.log(row)
  logFlag.value = true
  dialogVisible3.value = true
  if (childLog.value) {
    childLog.value.data.list = [];
  }
  const auditList = await manageApi.auditLists({"salesRetreat.id": row.id})
  const logList = await manageApi.logList({masterId: row.id})
  if (auditList.code == 200 && logList.code == 200) {
    childLog.value.timeFns(auditList.data.records, logList.data.records);
  } else {
    ElMessage.error('加载失败')
  }
  logFlag.value = false
}
const changeTime = (time) => {
  if (time) {
    let newTime = new Date(time)
    newTime = newTime.setDate(newTime.getDate() + 1);
    newTime = functionIndex.transformTimestampSearch(newTime)
    return newTime
  } else {
    return null
  }
}
const getTableList = () => {
  setTimeout(() => {
    loadingFlag.value = true;
    backApi.getTable({
      current: data.pageNum,
      size: data.pageSize,
      customerName: searchRef.value.searchForm.n1,
      queryCommodityName: searchRef.value.searchForm.n2,
      queryCommoditySelfCode: searchRef.value.searchForm.n3,
      queryDocnum: searchRef.value.searchForm.n4,
      queryHandledName: searchRef.value.searchForm.n5,
      queryPreparedName: searchRef.value.searchForm.n6,
      auditStatus: searchRef.value.searchForm.n7,
      retreatFormInboundStatus: searchRef.value.searchForm.n8,
      beginApplyDate: searchRef.value.searchForm.n9 ? searchRef.value.searchForm.n9[0] : null,
      endApplyDate: searchRef.value.searchForm.n9 ? changeTime(searchRef.value.searchForm?.n9[1]) : null
    }).then(res => {
      if (res.code == 200) {
        data.total = res.data.total
        data.tableList = res.data.records
      }
      loadingFlag.value = false;
    })
  })
};
const newUUID = ref()
const addBack = () => {
  active.value = 1
  dialogVisible.value = true
  newUUID.value = uuid.v1()
}
const nextTi = () => {
  if (active.value == 1) {
    if (formChild.value.chooseGoods.length > 0) {
      formChild.value.formPrice.allPrice = 0
      formChild.value.formPrice.allNum = 0
      formChild.value.chooseGoods.forEach(item => {
        if (!item.amount) {
          item.amount = {
            flag: false,
            num: 0
          }
          item.price = 0
        }
        formChild.value.formPrice.allNum += item.amount.num
        formChild.value.formPrice.allPrice += item.price
      })
      // for (let key in formChild.value.backForm) {
      // 	if (key == 'n4') {
      // 		formChild.value.backForm[key] = null
      // 	} else if (key == 'n6') {
      // 		formChild.value.backForm[key] = []
      // 	} else {
      // 		formChild.value.backForm[key] = ''
      // 	}
      // }
      formChild.value.backForm.n10 = formChild.value.chooseGoods[0].customer
      active.value++
      return
    }
    ElMessage.error('请先选择')
  } else if (active.value == 2) {
    verifyForm(formChild.value.creatform)
  }
}
const verifyForm = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      if (formChild.value.chooseGoods.some(item => item.amount.num == 0)) {
        ElMessage.error('申退数量不能为空或为0')
      } else {
        let allNum = 0
        let allPrice = 0
        formChild.value.chooseGoods.forEach(item => {
          allPrice = item.amount.num * item.unitPrice + allPrice
          allNum = item.amount.num + allNum
        })
        formChild.value.backForm.n7 = allNum
        formChild.value.backForm.n8 = allPrice
        active.value++
      }
    }
  });
}
const emptyFn = () => {
  for (let key in formChild.value.backForm) {
    if (key == 'n6') {
      formChild.value.backForm[key] = []
    } else if (key == 'n4') {
      formChild.value.backForm[key] = null
    } else {
      formChild.value.backForm[key] = ''
    }
  }
  for (let key in formChild.value.searchForm) {
    if (key == 'n4') {
      formChild.value.searchForm[key] = []
    } else if (key == 'size') {
      formChild.value.searchForm[key] = 10
    } else if (key == 'current') {
      formChild.value.searchForm[key] = 1
    } else if (key == 'total') {
      formChild.value.searchForm[key] = 0
    } else {
      formChild.value.searchForm[key] = ''
    }
  }
  formChild.value.formPrice.allPrice = 0
  formChild.value.formPrice.allNum = 0
  formChild.value.multipleTableRef?.clearSelection()
  formChild.value.goodsTable = []
  formChild.value.chooseGoods = []
}
const cancalFn = () => {
  ElMessageBox.confirm("信息未保存确认取消吗？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        emptyFn()
        newUUID.value = null
        dialogVisible.value = false
        active.value = 1
      })
      .catch(() => {
        // catch error
      });
}
const luoSave = (id, uuid, nums) => {
  let num = nums ? nums : 1
  backApi.saveFile({
    uuid: uuid,
    busId: id
  }).then((res) => {
    if (res.code == 200) {
      newUUID.value = null
    } else {
      if (num <= 5) {
        num++
        setTimeout(() => {
          luoSave(id, uuid, num)
        }, 3000)
      }
    }
  })
}
const rightBack = () => {
  console.log(formChild.value.backForm)
  switchBack(formChild.value.chooseGoods, formChild.value.backForm, formChild.value.formPrice)
      .then(res => {
        if (res.code == 200) {
          ElMessage.success('提交成功')
          active.value = 1
          emptyFn()
          getTableList()
          dialogVisible.value = false
          luoSave(res.data.salesRetreat.id, newUUID.value, 1)
        } else {
          ElMessage.error(res.msg)
        }
      })
}
const handleClose = (done) => {
  ElMessageBox.confirm("信息未保存确认取消吗？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        done();
        emptyFn()
        newUUID.value = null
        active.value = 1
      })
      .catch(() => {
        // catch error
      });
};
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  getTableList()
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})

</script>
<style lang='scss' scoped>
.salesBack {
  padding: 10px;
}

.last-card {
  margin-top: 10px;
}

.stateTitle {
  position: absolute;
  font-size: 15px;
  top: 21px;
  right: 53px;
}
</style>
