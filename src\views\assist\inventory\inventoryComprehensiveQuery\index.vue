<template>
	<div class="app-container">
		<el-card class="box-card Botm" style="margin: 10px;">
			<el-form ref="queryRef" :inline="true" :model="queryParams" class="form_130">
				<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
					<el-form-item label="商品名称" prop="commodityName">
						<el-input v-model="queryParams.commodityName" class="form_225" clearable placeholder="请输入商品名称" />
					</el-form-item>
					<el-form-item label="商品自编码" prop="commoditySelfCode">
						<el-input v-model="queryParams.commoditySelfCode" class="form_225" clearable
							placeholder="请输入商品自编码" />
					</el-form-item>
					<el-form-item label="商品编号" prop="commodityCode">
						<el-input v-model="queryParams.commodityCode" class="form_225" clearable placeholder="请输入商品编号" />
					</el-form-item>
					<el-form-item label="仓库" prop="storages">
						<el-input v-model="queryParams.storages" class="form_225" clearable placeholder="请输入仓库" />
					</el-form-item>
					<el-form-item label="经手人" prop="handledBy">
						<el-input v-model="queryParams.handledBy" class="form_225" clearable placeholder="请输入经手人" />
					</el-form-item>
					<el-form-item label="采购订单编号" prop="orderCode">
						<el-input v-model="queryParams.orderCode" class="form_225" clearable placeholder="请输入采购订单编号" />
					</el-form-item>
					<el-form-item label="供应商" prop="suppliers">
						<el-input v-model="queryParams.suppliers" class="form_225" clearable placeholder="请输入供应商" />
					</el-form-item>
					<!-- <el-form-item label="批号" prop="lotNo">
						<el-input v-model="queryParams.lotNo" class="form_225" clearable placeholder="请输入批号"/>
					</el-form-item> -->
					<el-form-item label="入库日期" prop="createDate">
						<div class="box_date">
							<el-date-picker v-model="queryParams.createDate" class="form_225" end-placeholder="结束日期"
								range-separator="至" start-placeholder="开始日期" type="daterange" />
						</div>

					</el-form-item>

				</TopTitle>
			</el-form>

		</el-card>

		<!-- 表格数据 -->
		<el-card class="box-card" style="margin: 10px;">
			<RightToptipBar v-model:columns="columns" @handleRefresh="getList" />
			<el-table v-loading="loading" :data="list" border style="margin-top: 10px;">
				<el-table-column align="center" label="序号" prop="sort" width="80">
					<template #default="scope">
						<span>{{ (queryParams.current - 1) * queryParams.size + scope.$index + 1 }}</span>
					</template>
				</el-table-column>
				<el-table-column align="left" label="商品自编码" min-width="120" prop="commodity.commoditySelfCode" />
				<el-table-column v-if="tableIsShow('commodityCode')" align="left" label="商品编号" min-width="120"
					prop="commodityCode" />
				<el-table-column align="left" label="商品名称" min-width="120" prop="commodity.tradeName" />
				<el-table-column v-if="tableIsShow('commodity.commonName')" align="left" label="通用名" min-width="120"
					prop="commodity.commonName" />
				<el-table-column align="left" label="商品规格" min-width="120" prop="commodity.packageSpecification" />
				<el-table-column v-if="tableIsShow('commodity.producingArea')" align="left" label="产地" min-width="120"
                         prop="commodity.producingArea"/>
				<el-table-column :show-overflow-tooltip="true" align="left" label="生产厂家" min-width="120"
                         prop="commodity.manufactureName"/>
				<el-table-column v-if="tableIsShow('commodity.nodeName')" align="left" label="批准文号" min-width="120"
					prop="approveNo" />
				<el-table-column v-if="tableIsShow('commodity.dosageForm')" align="left" label="剂型" min-width="120"
					prop="commodity.dosageForm" />
				<el-table-column v-if="tableIsShow('suppliers')" align="left" label="供应商" min-width="120"
					prop="suppliers" />
				<el-table-column v-if="tableIsShow('orderCode')" align="left" label="采购订单编号" min-width="120"
					prop="orderCode" />
				<!-- <el-table-column align="left" label="批号" min-width="120" prop="lotNo"/>
				<el-table-column align="left" label="生产日期" min-width="130">
					<template #default="scope">
						<span>{{ moment(scope.row.proDate).format("YYYY-MM-DD") }}</span>
					</template>
				</el-table-column>
				<el-table-column align="left" label="有效期" min-width="120" prop="valiDate"/> -->
				<el-table-column align="left" label="仓库" min-width="120" prop="warehouse" />
        <el-table-column align="left" label="库号" min-width="120" prop="warehouseNumber"/>
        <el-table-column align="left" label="货位" min-width="120" prop="goodsShelves"/>
				<el-table-column align="left" label="经手人" min-width="120" prop="handledBy" />
				<el-table-column align="left" label="单位" min-width="120" prop="commodity.basicUnit" />
				<el-table-column align="left" label="数量" min-width="120" prop="availableInventory" />
				<el-table-column align="left" label="单价" min-width="120" prop="unitPrice" />
				<el-table-column align="left" label="金额" min-width="120" prop="money" />
				<el-table-column align="left" label="件装量" min-width="120" prop="unitLoading" />
				<el-table-column align="left" label="件数" min-width="120" prop="pieceNumber" />
				<el-table-column v-if="tableIsShow('listPermitHolder')" align="left" label="上市许可持有人" min-width="120"
					prop="listPermitHolder" />
				<el-table-column align="left" fixed="right" label="发票号" min-width="120" prop="billNo">
					<template #default="scope">
            <p style="color: #2a76f8" @click="invoiceCheck(scope.row.invoiceId, 1, 1)">
							{{ scope.row.billNo }}
						</p>
					</template>
				</el-table-column>
				<el-table-column v-if="tableIsShow('billTime')" align="left" label="开票时间" min-width="120" prop="billTime" />
				<el-table-column align="left" label="入库日期" min-width="120" prop="intoTime"
					:formatter="row => row.intoTime ? moment(row.intoTime).format('YYYY-MM-DD') : '--'" />
				<el-table-column v-if="tableIsShow('registerNO')" align="left" label="注册证号" min-width="120"
					prop="registerNO" />
				<el-table-column v-if="tableIsShow('registerValiDate')" align="left" label="注册证号有效期" min-width="120"
					prop="registerValiDate" />
				<el-table-column v-if="tableIsShow('licenseNo')" align="left" label="生产许可证号" min-width="120"
					prop="licenseNo" />
				<el-table-column v-if="tableIsShow('licenseValiDate')" align="left" label="生产许可证号有效期" min-width="120"
					prop="licenseValiDate" />
				<el-table-column v-if="tableIsShow('retail')" align="left" label="零售价" min-width="120" prop="retail" />
			</el-table>
			<div style="float: right;">
				<pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
					:total="total" @pagination="getList" />
			</div>
		</el-card>

		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-model="open" :title="title" append-to-body width="600px">
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="() => open = false">取 消</el-button>
					<el-button type="primary">确 定</el-button>
				</div>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisibleIn" title="查看开票详情" width="80%">
			<div v-loading="checkFlag">
				<relevancyDetails :detailType="data.detailType" :price="data.price" :strs="data.row"
                          :table1="data.detailTable1" :table2="data.detailTable2"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisibleIn = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
    <el-dialog v-model="dialogVisibleOut" title="查看开票详情" width="80%">
			<div v-loading="detailFlag">
				<ApplicationDetails v-if="dialogVisibleOut" :obj="data.obj" :table1="data.table1" :table2="data.table2"
                            :types="data.types"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisibleOut = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, toRefs} from 'vue'
import RightToptipBar from '@/components/rightToptipBar'
import outboundRecordService from '@/api/erp/storage/erpInventoryService'
import moment from 'moment'
import TopTitle from '@/components/topTitle'
import {applocation, outPut, relevanceInput} from "@/api/model/invoice";
import relevancyDetails from '@/views/invoiceManagement/components/relevancyDetails.vue'
import ApplicationDetails from '@/views/invoiceManagement/components/ApplicationDetails.vue'
import {ElMessage} from "element-plus";

const { proxy } = getCurrentInstance();
const columns = ref([

	{
		label: '商品编号',
		prop: 'commodityCode',
		isShow: false
	},

	{
		label: '通用名',
		prop: 'commodity.commonName',
		isShow: false
	},

	{
		label: '产地',
		prop: 'commodity.producingArea',
		isShow: false
	},

	{
		label: '批准文号',
		prop: 'commodity.nodeName',
		isShow: false
	},
	{
		label: '剂型',
		prop: 'commodity.dosageForm',
		isShow: false
	},
	{
		label: '供应商',
		prop: 'suppliers',
		isShow: false
	},
	{
		label: '采购订单编号',
		prop: 'orderCode',
		isShow: false
	},

	{
		label: '上市许可持有人',
		prop: 'listPermitHolder',
		isShow: false
	},

	{
		label: '开票时间',
		prop: 'billTime',
		isShow: false
	},

	{
		label: '注册证号',
		prop: 'registerNO',
		isShow: false
	},
	{
		label: '注册证号有效期',
		prop: 'registerValiDate',
		isShow: false
	},
	{
		label: '生产许可证号',
		prop: 'licenseNo',
		isShow: false
	},
	{
		label: '生产许可证号有效期',
		prop: 'licenseValiDate',
		isShow: false
	},
	{
		label: '零售价',
		prop: 'retail',
		isShow: false
	},
])
const list = ref([]);
const open = ref(false);
const loading = ref(false);
const flag = ref(false)
const total = ref(0);
const title = ref("");
const statusList = ref([])
const showSearch = ref(false)
const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 10,
	}

});
const echo2 = (status) => {
	if (status == 0) {
		return '未关联'
	} else if (status == 1) {
		return '已关联'
	} else {
		return '未知'
	}
}
const checkFlag = ref(false)
const detailFlag = ref(false)
const dialogVisibleOut = ref(false)
const dialogVisibleIn = ref(false)
const invoiceCheck = (row, num, status) => {
	if (status == 1) {
		if (num == 1) {
			detailTable(row)
		} else {
			detailTableOut(row)
		}
	}
}
const detailTableOut = async (row) => {
	data.table2 = []
	data.table1 = []
	dialogVisibleOut.value = true
	detailFlag.value = true
	const res = await applocation.detailList({
		'salesInvoiceId': row
	})
	const res2 = await outPut.detaiInvoices({
		'salesInvoice.id': row
	})
	if (res.code == 200 && res2.code == 200) {
		detailFlag.value = false
		data.types = res.data.salesInvoice.billingType
		data.auditStatus = res.data.salesInvoice.auditStatus
		data.invoiceStatus = res.data.salesInvoice.status
		if (data.types == 0) {
			res2.data.records.forEach(record => {
				data.table1.push(record.salesInvoiceReceipt)
			})
			res.data.salesInvoiceForms.forEach(item => {
				if (!item.invoiceFormPriceList) {
					item.invoiceFormPriceList = []
					item.invoiceFormPriceList.push({
						invoicePrice: item.unitPrice,
						invoiceQuantity: item.invoicedQuantity,
						totalAmount: item.totalAmount
					})
				}
				data.table2.push({
					'docNum': item.salesNo,
					'erpCustomer': {
						id: item.customer.id
					},
					'editId': item.id,
					'customer': item.customerName,
					'commodity': {
						'commonName': item.commodityName,
						'commoditySelfCode': item.commoditySelfCode,
						'packageSpecification': item.commodityPackageSpecification,
						'manufactureName': item.commodityManufactureName,
						'producingArea': item.commodityProducingArea,
						'validityTime': item.commodityValidityTime,
						'basicUnit': item.commodityBasicUnit,
					},
					'checkFlag': false,
					'invoiceFormPriceList': item.invoiceFormPriceList,
					'totalAmount': item.totalAmount,
					'salesOutBound': {
						'outTime': item.salesOutBound.outTime
					},
          'totalPriceTax': item.totalPriceTax,
          'totalTax': item.totalTax,
          'taxRate': item.taxRate,
					'createDate': item.salesOrder.docDate,
					'produceDate': item.commodityProduceDate,
					'batchNumber': item.batchNo,
					'unitPrice': item.unitPrice,
					'outQuantity': item.boundQuantity,
					'supplier': null,
					'supplierName': null,
					'allPrice': (item.unitPrice * item.boundQuantity).toFixed(2)
				})
				data.obj = res.data.salesInvoice
			})
		} else {
			res2.data.records.forEach(record => {
				data.table1.push(record.salesInvoiceReceipt)
			})
			res.data.salesInvoiceForms.forEach(item => {
				data.table2.push({
					'docNum': item.salesNo,
					'editId': item.id,
					'customer': item.customerName,
					'commodity': {
						'commonName': item.commodityName,
						'commoditySelfCode': item.commoditySelfCode,
						'packageSpecification': item.commodityPackageSpecification,
						'manufactureName': item.commodityManufactureName,
						'producingArea': item.commodityProducingArea,
						'validityTime': item.commodityValidityTime,
						'basicUnit': item.commodityBasicUnit,
					},
					'totalAmount': item.totalAmount,
					'salesOutBound': {
						'outTime': item.salesRetreatInbound.inTime
					},
					'createDate': item.salesInvoice.applyDate,
					'produceDate': item.commodityProduceDate,
					'batchNumber': item.batchNo,
					'unitPrice': item.unitPrice,
					'outQuantity': item.boundQuantity,
					'num': item.invoicedQuantity,
					'supplier': null,
					'supplierName': null,
					'allPrice': item.totalAmount
				})
				data.obj = res.data.salesInvoice
			})
		}
	} else {
		ElMessage.error('获取失败')
	}
}
const detailTable = (row) => {
	checkFlag.value = true
	dialogVisibleIn.value = true
	data.detailTable1 = []
	data.price = 0
	data.row = {}
	data.detailTable2 = []
	relevanceInput.detailList({
		id: row
	}).then(res => {
		if (res.code == 200) {
			data.row = res.data.erpPurchaseInvoiceDto
			data.detailType = data.row.type
			res.data.erpPurchaseInvoiceFormDtos.forEach((item) => {
				data.detailTable2.push(
					{
						id: item.id,
						orderCode: item.purchaseNo,
						commodity: {
							tradeName: item.commodityName,
							commoditySelfCode: item.commoditySelfCode,
							packageSpecification: item.commodityPackageSpecification,
							manufactureName: item.commodityManufactureName,
							producingArea: item.commodityProducingArea,
							commodityCode: item.commodityCode,
							produceDate: item.commodityProduceDate
						},
						checkFlag: false,
						invoicingInfo: item.invoicingInfo?.split(','),
						unitPrice: item.commodityUnitPrice.toFixed(2),
						receivingQuantity: item.inboundQuantity,
						allPrice: item.invoicingAmount.toFixed(2),
						basicUnit: item.commodityBasicUnit,
						validate: item.commodityValidityTime,
						invoicingQuantity: item.invoicingQuantity,
						purchaseCreatedDate: item.purchaseCreatedDate,
						intoNo: item.batchNo,
						produceDate: item.produceDate,
						purchaseCreateDate: item.purchaseCreateDate,
						intoDate: item.intoDate,
						suppier: item.supplierName,
					}
				)
			})
			data.price = res.data.erpPurchaseInvoiceDto.invoiceAmount
			res.data.erpPurchaseInvoiceUnionDtos.forEach((item, index) => {
				data.detailTable1.push({
					id: item.id,
					invoiceNo: item.purchaseInvoiceReceipt.invoiceNo,
					invoiceCode: item.purchaseInvoiceReceipt.invoiceCode,
					invoiceSupplier: item.purchaseInvoiceReceipt.invoiceSupplier,
					taxpayerNo: item.purchaseInvoiceReceipt.taxpayerNo,
					invoicingDate: item.purchaseInvoiceReceipt.invoicingDate,
					invoicingAmount: item.purchaseInvoiceReceipt.invoicingAmount,
          excludingTax: item.purchaseInvoiceReceipt.excludingTax,
          totalTax: item.purchaseInvoiceReceipt.totalTax,
          effectiveTax: item.purchaseInvoiceReceipt.effectiveTax,
          type: item.purchaseInvoiceReceipt.type,
					fileDtos: []
				})
				item.purchaseInvoiceReceipt.fileDtos.forEach(item => {
					data.detailTable1[index].fileDtos.push({
						id: item.id,
						name: item.fileName,
						url: item.fileUrl
					})
				})
			})
		}
		checkFlag.value = false
	})
}
const {queryParams, form, rules} = toRefs(data);
const tableIsShow = key => {
	return columns.value.filter(item => item.prop == key)[0]?.isShow
}

function getList2() {
	const query = proxy.$route?.query
	if (query && query.id && query.type == 'abnormalTask') {
		loading.value = true
		flag.value = true
		outboundRecordService.list({
			id: query.id
		}).then(res => {
			if (res.code == 200) {
				list.value = res.data.records
				total.value = res.data.total
				loading.value = false
			}
		})
  } else {
    loading.value = true
    const params = {...queryParams.value}
    params.beginInTime = params.createDate?.length ? moment(params.createDate[0]).format('YYYY-MM-DD') : undefined
    params.endInTime = params.createDate?.length ? moment(params.createDate[1]).format('YYYY-MM-DD') : undefined
    delete params.createDate
    outboundRecordService.list(params).then(res => {
      if (res.code == 200) {
        console.log(res);
        list.value = res.data.records
        total.value = res.data.total
        loading.value = false
      }
    })
	}
}
getList2()
/** 查询角色列表 */
function getList() {
  if (flag.value = false) {

	}
}

const formDict = (data, val) => {
	return proxy.selectDictLabel(data, val)
}

/** 搜索按钮操作 */
function handleQuery() {
	delete proxy.$route?.query
	getList2();
}

/** 重置按钮操作 */
function resetQuery() {
	proxy.resetForm("queryRef");
	handleQuery();
}

async function dict() {
	statusList.value = await proxy.getDictList('audit_status')
}

dict()
getList();
</script>

<style lang="scss" scoped>
::v-deep .Botm {
	.el-card__body {
		padding-bottom: 0px
	}
}

.box_date {
	width: 220px;
}
</style>
