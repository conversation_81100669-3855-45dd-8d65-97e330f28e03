<template>
  <div class="inputSearch">
    <inputSearch ref="searchRef" @handleQuery="handleQuery"/>
    <el-card body-style="padding-top:0;padding-bottom:0;" class="box-card last-card" style="margin-top: 10px">
      <template #header>
        <div class="card-header">
          <span><el-button class="button" type="primary" @click="newAddTable()">
              手动录入</el-button><el-button class="button" type="primary" @click="syncInvoice">同步接口</el-button></span>
        </div>
      </template>
      <div class="item" style="margin-top:10px">
        <DragTableColumn v-if="invoiceType" v-loading="loadingFlag" v-model:queryParams="searchRef.searchForm"
                         :columns="columns" :getList="handleQuery" :tableData="tableList"
                         className="invoiceManagement_InputInvoice">
          <template v-slot:operate="{ scopeData }">
            <el-button :disabled="scopeData.row.associated != 0 && scopeData.row.source != '同步接口'" link
                       type="primary"
                       @click="editTable(scopeData.row, '编辑')"><img src="@/assets/icons/update.png"
                                                                      style="margin: 0px 5px 0 0"/>编辑
            </el-button>
            <el-button :disabled="scopeData.row.associated != 0 && scopeData.row.source != '同步接口'" link
                       type="danger"
                       @click="deltable(scopeData.row)"><img src="@/assets/icons/delete.png"
                                                             style="margin: 0px 5px 0 0"/>删除
            </el-button>

            <el-button link type="primary" @click="editTable(scopeData.row, '查看')"><img
                src="@/assets/icons/detail.png"
                style="margin: 2px 5px 0 0"/>详情
            </el-button>
            <el-button link type="success" @click="logFn(scopeData.row.id)"><img src="@/assets/icons/review.png"
                                                                                 style="margin: 0px 2px 0 0"/>操作日志
            </el-button>
          </template>
        </DragTableColumn>
        <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                       :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
                       layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                       @size-change="handleQuery"
                       @current-change="handleQuery"/>
      </div>

    </el-card>
    <el-dialog v-model="dialogVisible" :before-close="handleClose" :title="data.title" width="45%">
      <div v-loading="dialogFlag">
        <inputDialog ref="formRef"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose()">取消</el-button>
          <el-button v-if="formRef?.formFlag ? false : true" type="primary" @click="rightBtn">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible3" title="操作记录" width="30%">
      <logQuery ref="logQueryRef"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible3 = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect, getCurrentInstance} from 'vue';
import inputSearch from './components/inputSearch.vue'
import {ElLoading, ElMessage, ElMessageBox} from 'element-plus'
import inputDialog from './components/InputDialog.vue'
import {inputInvoice} from "@/api/model/invoice";
import {functionIndex} from "@/views/salesManagement/functionIndex";
import {manageApi} from "@/api/model/salesManagement";
import sysNoticeService from "@/api/model/approve/sysNoticeService";

const {proxy} = getCurrentInstance();
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const searchRef = ref(null)
const data = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  title: ""
})
const dialogFlag = ref(false)
const emit = defineEmits([])
const props = defineProps({})
const dialogVisible = ref(false)
const logQueryRef = ref(null)
const invoiceType = ref(null)
const dialogVisible3 = ref(false)
const formRef = ref(null)
const loadingFlag = ref(false)
const logFnFlag = ref(false)
const logFn = async (id) => {
  logFnFlag.value = true
  dialogVisible3.value = true;
  if (logQueryRef.value) {
    logQueryRef.value.data.list = [];
  }
  const logList = await manageApi.logList({masterId: id})
  if (logList.code == 200) {
    logQueryRef.value.timeFns([], logList.data.records);
  } else {
    ElMessage.error('加载失败')
  }
  logFnFlag.value = false
}
const handleClose = (done) => {
  if (data.title == '查看进项发票') {
    dialogVisible.value = false
    emptyFn()
  } else {
    ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    })
        .then(() => {
          dialogVisible.value = false
          emptyFn()
        })
        .catch(() => {
        });
  }
}
const changeTime = (time) => {
  if (time) {
    let newTime = new Date(time)
    newTime = newTime.setDate(newTime.getDate() + 1);
    newTime = functionIndex.transformTimestampSearch(newTime)
    return newTime
  } else {
    return null
  }
}
const handleQuery = () => {
  loadingFlag.value = true
  inputInvoice.getList({
    size: data.pageSize,
    current: data.pageNum,
    invoiceSupplier: searchRef.value.searchForm.n1 ? searchRef.value.searchForm.n1 : null,
    invoiceNo: searchRef.value.searchForm.n2 ? searchRef.value.searchForm.n2 : null,
    invoiceCode: searchRef.value.searchForm.n3 ? searchRef.value.searchForm.n3 : null,
    source: searchRef.value.searchForm.n4 ? searchRef.value.searchForm.n4 : null,
    applyByName: searchRef.value.searchForm.n5 ? searchRef.value.searchForm.n5 : null,
    beginCreateDate: searchRef.value.searchForm?.n6[0],
    endCreateDate: changeTime(searchRef.value.searchForm?.n6[1]),
    beginInvoicingDate: searchRef.value.searchForm?.n7[0],
    endInvoicingDate: changeTime(searchRef.value.searchForm?.n7[1]),
    type: searchRef.value.searchForm?.n8
  }).then(res => {
    if (res.code == 200) {
      tableList.value = res.data.records
      data.total = res.data.total
    }
    loadingFlag.value = false
  })
}
const emptyFn = () => {
  for (let key in formRef.value.backForm) {
    if (key == 'n7') {
      formRef.value.backForm[key] == []
    } else if (key == 'n6' || key == 'n10' || key == 'n11' || key == 'n12') {
      formRef.value.backForm[key] == null
    } else {
      formRef.value.backForm[key] == ''
    }
  }
  formRef.value.creatform?.resetFields()
}
const rightBtn = async () => {
  if (!formRef.value.creatform) return;
  await formRef.value.creatform.validate((valid) => {
    if (valid) {
      console.log(formRef.value.backForm.n7);
      let fileArr = []
      formRef.value.backForm.n7?.forEach(item => {
        if (item.response) {
          fileArr.push({
            fileName: item.name,
            fileUrl: item.response.data.url
          })
        }
      })
      inputInvoice.delFile({
        ids: formRef.value.delList.join(',')
      }).then((res) => {
        if (res.code != 200) {
          ElMessage.error('文件删除失败')
        }
      })
      inputInvoice.saveList({
        erpPurchaseInvoiceReceiptDto: {
          id: data.editId ? data.editId : null,
          invoiceNo: formRef.value.backForm.n1,
          invoiceCode: formRef.value.backForm.n2,
          invoiceSupplierId: formRef.value.backForm.n3,
          taxpayerNo: formRef.value.backForm.n4,
          invoicingDate: formRef.value.backForm.n5,
          invoicingAmount: formRef.value.backForm.n6,
          type: formRef.value.backForm.n8,
          invoiceSupplier: formRef.value.backForm.n9,
          excludingTax: formRef.value.backForm.n10,
          totalTax: formRef.value.backForm.n11,
          effectiveTax: formRef.value.backForm.n12
        },
        fileDtos: fileArr
      }).then(res => {
        if (res.code == 200) {
          ElMessage.success('录入成功')
          handleQuery()
          dialogVisible.value = false
          emptyFn()
        } else {
          ElMessage.error(res.msg)
        }
      })
    }
  });
}
const getAll = (row) => {
  dialogFlag.value = true
  console.log(row)
  inputInvoice.checkList({
    id: row.id
  }).then(res => {
    if (res.code == 200) {
      formRef.value.backForm.n1 = res.data.erpPurchaseInvoiceReceiptDto.invoiceNo
      formRef.value.backForm.n2 = res.data.erpPurchaseInvoiceReceiptDto.invoiceCode
      formRef.value.backForm.n3 = res.data.erpPurchaseInvoiceReceiptDto.invoiceSupplierId
      formRef.value.backForm.n4 = res.data.erpPurchaseInvoiceReceiptDto.taxpayerNo
      formRef.value.backForm.n5 = res.data.erpPurchaseInvoiceReceiptDto.invoicingDate
      formRef.value.backForm.n6 = res.data.erpPurchaseInvoiceReceiptDto.invoicingAmount
      formRef.value.backForm.n8 = res.data.erpPurchaseInvoiceReceiptDto.type
      formRef.value.backForm.n10 = res.data.erpPurchaseInvoiceReceiptDto.excludingTax
      formRef.value.backForm.n11 = res.data.erpPurchaseInvoiceReceiptDto.totalTax
      formRef.value.backForm.n12 = res.data.erpPurchaseInvoiceReceiptDto.effectiveTax
      formRef.value.backForm.n7 = res.data.fileDtos.map(item => {
        return {
          name: item.fileName,
          url: item.fileUrl,
          delId: item.id
        }
      })
    } else {
      ElMessage.error(res.msg)
    }
    dialogFlag.value = false
  })
}
const editTable = (row, title) => {
  data.editId = null
  setTimeout(() => {
    if (title == '查看') {
      data.title = '查看进项发票'
      formRef.value.formFlag = true
      getAll(row)
    } else {
      data.title = '编辑进项发票'
      data.editId = row.id
      formRef.value.delList = []
      getAll(row)
      formRef.value.formFlag = false
    }
  })
  dialogVisible.value = true
}
const deltable = (row) => {
  ElMessageBox.confirm("确认删除此项吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        ElLoading.service()
        inputInvoice.delList({
          ids: row.id
        }).then(res => {
          if (res.code == 200) {
            ElMessage.success('删除成功')
          } else {
            ElMessage.error('删除失败')
          }
          handleQuery()
          const loadingInstance = ElLoading.service()
          loadingInstance.close()
        })
      })
      .catch(() => {
      });
};
const newAddTable = () => {
  data.editId = null
  setTimeout(() => {
    formRef.value.formFlag = false
    emptyFn()
  })
  data.title = '创建进项发票'
  dialogVisible.value = true
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  setTimeout(() => {
    handleQuery()
  })

})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})
const dick = async () => {
  let typeLo = JSON.parse(window.localStorage.getItem('In_Invioce'))
  if (typeLo) {
    invoiceType.value = typeLo
  } else {
    invoiceType.value = await proxy.getDictList("In_Invioce")
    localStorage.setItem('In_Invioce', JSON.stringify(invoiceType.value))
  }
}
dick()
const columns = ref([
  {label: '发票号码', prop: 'invoiceNo'},
  {label: '发票代码', prop: 'invoiceCode'},
  {label: '发票类型', prop: 'type', type: 'status', filters: invoiceType, searchKey: "n8"},
  {label: '发票供应商', prop: 'invoiceSupplier'},
  {label: '纳税人识别号', prop: 'taxpayerNo'},
  {label: '发票日期', prop: 'invoicingDate', type: 'date'},
  {label: '发票金额', prop: 'invoicingAmount'},
  {label: '来源', prop: 'source'},
  {label: '创建人', prop: 'createBy.name'},
  {label: '创建日期', prop: 'createDate', type: 'date'},
  {
    label: '关联入库记录', prop: 'associated', type: 'status', isFilter: false,
    filters: [
      {
        name: "未关联",
        value: 0
      },
      {
        name: "已关联",
        value: 1
      },
    ],
    minWidth: "130px",
  },
  {label: '操作', prop: 'operate', minWidth: "300px", type: 'operate', fixed: 'right'},
])
const tableList = ref()


function syncInvoice() {

	inputInvoice
		.syncInvoice()
		.then((res) => {
			if (res.code == 200) {
				proxy.msgSuccess(`同步成功`+res.data);
				open.value = false;
				//getList();
			} else {
				open.value = false;
				proxy.msgError(res.msg);
				//getList();
			}
		});
}

</script>
<style lang='scss' scoped>
.inputSearch {
  padding: 10px;
}

.item {
  margin-bottom: 18px;
  margin-top: -10px;
}
</style>
