<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form">
                <el-form-item label="车牌号码" prop="carCode" style="width: 250px">
                    <el-input v-model="queryParams.carCode" clearable placeholder="请输入车牌号码" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="车辆类型" prop="carType" style="width: 250px">
                    <el-tree-select v-model="queryParams.carType" :data="typeOptions" :render-after-expand="false" check-strictly node-key="value" placeholder="请选择车辆类型" style="width: 100%" value-key="value" />
                </el-form-item>
                <el-form-item label="车辆种类" prop="carClass" style="width: 250px">
                    <el-select v-model="queryParams.carClass" clearable placeholder="请选择车辆种类" @change="handleQuery">
                        <el-option v-for="(item, idx) in fourplCarOptions" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="状态" prop="status" style="width: 250px">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择状态">
                        <el-option v-for="(item, idx) in statusOptions" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
				<el-form-item v-show="isShowAll" label="绑定状态" prop="bindStatus">
                    <el-select v-model="queryParams.bindStatus" clearable placeholder="请选择绑定状态" @change="handleQuery">
                        <el-option v-for="(item, idx) in bindStatusOptions" :key="idx" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
				<search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <div style="display: flex">
            <!-- 车辆类型 -->
            <el-card :body-style="{ padding: '10px' }" shadow="never" style="flex: 0 0 220px; height: 600px; overflow-y: scroll; margin-right: 10px">
                <el-input v-model="searchKeyword" clearable placeholder="请输入关键字进行搜索" prefix-icon="el-icon-search" size="small" style="margin-bottom: 20px" />
                <el-tree ref="tree" :data="treeTypeLists" :default-expand-all="true" :filter-node-method="filterNode" :highlight-current="true" :props="defaultProps" node-key="typeCode" @node-click="handleNodeClick"></el-tree>
            </el-card>
            <!-- 表格 -->
            <el-card :body-style="{ padding: '10px' }" shadow="never" style="flex: 1 1 75%">
                <div class="mb10" style="display: flex; justify-content: space-between">
                    <el-button v-hasPermi="['transport:car:add']" icon="el-icon-plus" type="primary" @click="handleAdd">新增 </el-button>
                    <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="CarManagement" @queryTable="getList"></right-toolbar>
                </div>

                <column-table key="CarManagement" v-loading="loading" v-model:columns="columns" :data="infoList" :default-sort="{ prop: 'regeistTime', order: 'descending' }" border element-loading-text="加载中..." max-height="600">
                    <template #typeName="scope">
                        {{ scope.row?.carType?.typeName }}
                    </template>
                    <template #carClass="scope">
                        {{ fourplCarFormat(scope.row) }}
                    </template>
                    <template #length="scope">
                        <span>{{ scope.row.length && scope.row.wide && scope.row.high ? (scope.row.length * scope.row.wide * scope.row.high).toFixed(2) : '0' }}m³</span>
                    </template>
                    <template #status="scope">
                        <el-switch v-model="scope.row.status" size="small" active-color="#13ce66" active-text="正常" active-value="0" inactive-text="停用" inactive-value="1" @change="changeStatus($event, scope.row)"> </el-switch>
                    </template>
                    <template #bindStatus="scope">
                        {{ formatDictionaryData('bindStatusOptions', scope.row.bindStatus) }}
                    </template>
                    <template #deviceFlag="scope">
                        <div class="flex justify-center items-center">
                            {{ formatDictionaryData('bindStatusOptions', scope.row.deviceFlag) }}
                            <el-link v-if="scope.row.deviceFlag ==='1'" :underline="false" @click="onviewSensor(scope.row)" class="ml-10" type="primary">查看</el-link>
                        </div>
                    </template>
                    <template #opt="scope">
                        <el-button v-if="scope.row.bindStatus !== '1'" v-hasPermi="['transport:car:add']" icon="el-icon-magic-stick" link size="small" type="warning" @click="openAssociatedDrivers(scope.row)">关联司机</el-button>
                        <el-button v-if="scope.row.carClass == '1'" icon="el-icon-edit-pen" link size="small" type="primary" @click="openTheSensorPopUpWindow(scope.row)">绑定传感器</el-button>
                        <el-button v-hasPermi="['transport:car:edit']" icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(scope.row)">修改</el-button>
                        <el-button v-if="scope.row.bindStatus !== '1'" v-hasPermi="['transport:car:remove']" icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </column-table>

                <div class="box-flex-right">
                    <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
                </div>
            </el-card>
        </div>

        <!-- 添加或修改车辆对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="650px">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="车辆类型" prop="carType">
                    <el-tree-select v-model="form.carType" :data="typeOptions" :render-after-expand="false" node-key="value" placeholder="请选择车辆类型" style="width: 100%" value-key="value" />
                </el-form-item>
                <el-form-item label="车牌号码" prop="carCode">
                    <el-input v-model="form.carCode" placeholder="请输入车牌号码" />
                </el-form-item>
                <el-form-item label="车辆种类" prop="carClass">
                    <el-select v-model="form.carClass" clearable placeholder="请选择车辆种类" size="small" style="width: 100%">
                        <el-option v-for="(item, idx) in fourplCarOptions" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="上牌时间" prop="regeistTime">
                    <el-date-picker v-model="form.regeistTime" clearable format="YYYY-MM-DD" placeholder="选择上牌时间" size="small" style="width: 100%" type="date" value-format="YYYY-MM-DD"> </el-date-picker>
                </el-form-item>
                <el-form-item label="车辆品牌" prop="brand">
                    <el-input v-model="form.brand" maxlength="20" placeholder="请输入车辆品牌" show-word-limit />
                </el-form-item>
                <el-form-item label="核载量" prop="carload">
                    <el-input v-model="form.carload" maxlength="6" placeholder="请输入核载量" show-word-limit>
                        <template #suffix>kg</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="长度" prop="length">
                    <el-input v-model="form.length" maxlength="6" placeholder="请输入长度" show-word-limit>
                        <template #suffix>M</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="宽度" prop="wide">
                    <el-input v-model="form.wide" maxlength="6" placeholder="请输入宽度" show-word-limit>
                        <template #suffix>M</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="高度" prop="high">
                    <el-input v-model="form.high" maxlength="6" placeholder="请输入高度" show-word-limit>
                        <template #suffix>M</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="状态">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.name }} </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="60" placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </template>
        </el-dialog>

        <!--  /绑定传感器弹窗  -->
        <el-dialog v-if="bindingSensorVisible" v-model="bindingSensorVisible" append-to-body title="绑定传感器" width="450px">
            <el-form ref="formSensor" :model="formSensor" :rules="rules" label-width="auto">
                <el-form-item label="传感器" prop="deviceNumbers">
                    <el-select v-model="formSensor.deviceNumbers" :filter-method="getAllDevice" filterable multiple placeholder="请选择传感器" remote size="small" style="width: 100%" @change="changeDevice" @remove-tag="removeDevice">
                        <el-option v-for="(item, idx) in sensorTypeOptions" :key="item.serialNumber" :label="item.name + ' ' + item.serialNumber" :value="item.serialNumber">
                            <span style="float: left">{{ item.name }}</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.serialNumber }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="bindingSensorVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveCarDevice">确 定</el-button>
            </template>
        </el-dialog>

        <!--关联司机-->
        <el-drawer v-model="associatedDriversVisible" size="450px" title="关联司机" @close="closeAssociatedDrivers">
            <div style="background-color: #f2f2f2; padding: 10px">
                <el-card v-loading="associatedDriversLoading" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="associatedDrivers" :inline="true" :model="associatedDrivers" :rules="associatedDriversRules" label-width="auto">
                        <el-form-item label="司机名称" prop="driver" style="width: 100%">
                            <el-select v-model="associatedDrivers.driver" clearable filterable placeholder="请输入司机名称" style="width: 100%" value-key="id">
                                <el-option v-for="(item, idx) in associatedDriversList" :key="idx" :label="item.driverName" :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div style="display: flex; justify-content: end">
                    <el-button @click="closeAssociatedDrivers">取 消</el-button>
                    <el-button type="primary" @click="saveAssociatedDriver">确 定</el-button>
                </div>
            </div>
        </el-drawer>

        <driverSelect v-if="driverOpen" v-model:show="driverOpen" :driverData="driverData" :type="'flow_driver'" :values="values" @changeShow="changeShow" @onConfirm="binding"></driverSelect>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import driverSelect from '@/components/driverSelect';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js';
import { handleTree, recursivelyQueryWhetherValueExists } from '@/utils/index.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import { ElLoading } from 'element-plus';
import tool from '@/utils/tool';
export default {
    name: 'CarManagement',
    components: { SearchButton, driverSelect, ColumnTable, RightToolbar },
    data() {
        const carValid = (rule, value, callback) => {
            const plateNumber =
                /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1})$/;
            if (plateNumber.test(value)) {
                callback();
            } else {
                callback(new Error('请输入正确的车牌号'));
            }
        };
        const regeistTimeValid = (rule, value, callback) => {
            if (value == '' || new Date(value).getTime() < new Date().getTime()) {
                callback();
            } else {
                callback(new Error('上牌时间必须小于当前时间'));
            }
        };
        return {
            // 遮罩层
            loading: true,
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 车辆表格数据
            infoList: [],
            columns: [
                { title: '车辆类型', key: 'typeName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '车牌号码', key: 'carCode', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '上牌时间', key: 'regeistTime', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '车辆种类', key: 'carClass', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '车辆品牌', key: 'brand', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '核载量', key: 'carload', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '体积', key: 'length', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '司机', key: 'driverNames', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '状态', key: 'status', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true, fixed: 'right' },
                { title: '是否关联司机', key: 'bindStatus', align: 'center', width: '100px', columnShow: true, fixed: 'right' },
                { title: '是否绑定传感器', key: 'deviceFlag', align: 'center', width: '110px', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', width: '305px', align: 'center',columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                carType: null,
                carCode: null,
                carClass: null,
                status: null,
				bindStatus:undefined
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                carType: [{ required: true, message: '请选择车辆类型', trigger: 'blur' }],
                carCode: [
                    { required: true, message: '请输入车牌号码', trigger: 'blur' },
                    { required: true, validator: carValid, trigger: 'blur' }
                ],
                regeistTime: [{ validator: regeistTimeValid, trigger: 'blur' }],
                carClass: [{ required: true, message: '请选择车辆种类', trigger: 'blur' }],
                carload: [{ pattern: /^[+-]?(0|([1-9]\d*))(\.\d+)?$/, message: '请输入正确的核载量', trigger: 'blur' }],
                length: [{ pattern: /^[+-]?(0|([1-9]\d*))(\.\d+)?$/, message: '请输入正确的长度', trigger: 'blur' }],
                wide: [{ pattern: /^[+-]?(0|([1-9]\d*))(\.\d+)?$/, message: '请输入正确的宽度', trigger: 'blur' }],
                high: [{ pattern: /^[+-]?(0|([1-9]\d*))(\.\d+)?$/, message: '请输入正确的高度', trigger: 'blur' }]
            },
            // 搜索关键字
            searchKeyword: '',
            typeOptions: [],
            treeTypeLists: [],
            defaultProps: {
                children: 'children',
                label: function (data, node) {
                    return data.label;
                },
                hasChildren: 'hasChildren'
            },
            statusOptions: [],
            values: {},
            driverOpen: false,
            fourplCarOptions: [],
            driverData: [],
            bindingSensorVisible: false,
            // 传感器列表
            sensorTypeOptions: [],
            formSensor: {
                deviceNumbers: [],
                deviceList: [],
                temperatureCeiling: '',
                temperatureFloor: ''
            },
            associatedDriversVisible: false,
            associatedDriversList: [],
            associatedDriversLoading: false,
            associatedDrivers: {},
            associatedDriversRules: {
                driver: [{ required: true, message: '请选择司机', trigger: 'blur' }]
            },
            bindStatusOptions: [],
			isShowAll: false
		};
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    watch: {
        // 根据名称筛选部门树
        searchKeyword(val) {
            console.log(val);
            this.$refs.tree.filter(val);
            console.log(val);
        }
    },
    created() {
        this.getDict();
        this.getList();
        this.getTreeSelect();
        this.getAllDevice();
    },
    methods: {
        /**
         * 查看传感器
         * @param row
         */
        onviewSensor(row) {
            // 判断是否有CarSensor的权限
            if(recursivelyQueryWhetherValueExists(tool.data.get('MENU'),'CarSensor','name')){
                this.$router.push({ name: 'CarSensor', query: { carCode: row.carCode } });
            }else{
                this.msgError('没有权限,请联系管理员');
            }

        },
        // 绑定司机
        binding(data) {
            let params = [];
            params = data.driverList.map((item) => {
                return { driverCode: item.driverCode, carCode: data.data };
            });
            console.log('params', params);
            // operationConfiguration.bindDriver(params).then((response) => {
            //     if (response.code == 200) {
            //         this.msgSuccess('绑定司机成功');
            //         this.values.open = false;
            //         this.driverOpen = false;
            //         this.getList();
            //     }
            // });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        changeDevice() {
            this.$forceUpdate();
        },
        changeShow(value) {
            this.values.open = value;
            this.values.roleType = 'admin-test';
            this.driverOpen = value;
        },
        /**
         * 关闭关联司机弹窗
         */
        closeAssociatedDrivers() {
            this.associatedDriversVisible = false;
            this.$refs['associatedDrivers'].resetFields();
        },
        // 搜索树节点
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },
        // 标准时间格式化
        formatDate(cellValue) {
            return formatDate(cellValue);
        },
        /** 车辆种类字典转换 */
        fourplCarFormat(val) {
            return this.selectDictLabel(this.fourplCarOptions, val.carClass);
        },
        //获取所有可绑定传感器列表
        getAllDevice(e) {
            operationConfiguration.getdeviceList({ current: 1, size: 10000, serialNumber: e ,operateStatus: '0', deviceStatus: '0'}).then((response) => {
                if (response.code === 200 && response.data.records) {
                    if (response.data.records) {
                        this.sensorTypeOptions = response.data.records;
                    }
                }
            });
        },
        /**
         * 获取字典值
         * @returns {Promise<void>}
         */
        async getDict() {
            /** 状态 */
            this.statusOptions = await this.getDictList('sys_normal_disable');
            /** 车辆种类 */
            this.fourplCarOptions = await this.getDictList('fourpl_car_class');
            // 车辆绑定状态
            this.bindStatusOptions = await this.getDictList('fourpl_car_bind_status');
        },
        /** 查询车辆列表 */
        getList() {
            this.loading = true;
            this.infoList = [];
            let params = { ...this.queryParams, 'carType.typeCode': this.queryParams.carType };
            delete params.carType;
            operationConfiguration.listCarInfo(params).then((response) => {
                if (response.code === 200 && response.data) {
                    this.infoList = response.data.records || [];
                    this.total = response.data.total || 0;
                }
                this.loading = false;
            });
        },
        /** 查询车辆类型树结构 */
        getTreeSelect() {
            operationConfiguration.listCarType({ status: '0', size: 1000 }).then((response) => {
                this.typeOptions = [];
                if (response.code == 200) {
                    let data = response?.data?.records.map((item) => {
                        return { label: item.typeName, value: item.typeCode, parentId: item.parentCode };
                    });
                    this.typeOptions = handleTree(data, 'value');
                    this.treeTypeLists = handleTree(data, 'value');
                }
            });
        },
        changeStatus(status, row) {
            if(row.bindStatus === '1'){
                row.status = status==='1'?'0':'1';
                return this.msgError('车辆已绑定司机，请先解绑司机');
            }
            if (row?.id) {
                operationConfiguration.queryCarInfoById({ id: row.id }).then((response) => {
                    if (response.code == 200) {
                        const carType = response.data.carType.typeCode;
                        let params = { ...response.data, carType: { typeCode: carType },status };
                        operationConfiguration.editCarInfo(params).then((res) => {
                            if(res.code === 200){
                                this.msgSuccess('修改成功');
                                this.getList();
                            }
                        });
                    }
                });
            }
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加车辆';
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            this.$confirm('是否确认删除车辆"' + row.carCode + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
					const id = row.id;
                    return operationConfiguration.delCarInfo({ id });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                '/fourPL/transport/car/export',
                {
                    ...this.queryParams
                },
                `car_info.xlsx`
            );
        },
        //点击事件
        handleNodeClick(data) {
            this.queryParams.carType = data.value;
            this.queryParams.current = 1;
            this.getList();
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id;
            operationConfiguration.queryCarInfoById({ id }).then((response) => {
                this.form = response.data;
                this.form.carType = response.data.carType.typeCode;
                this.open = true;
                this.title = '修改车辆';
            });
        },
        /** 转换车辆类型数据结构 */
        normalizer(node) {
            if (node.children && !node.children.length) {
                delete node.children;
            }
            return {
                id: node.typeCode,
                label: node.typeName,
                children: node.children
            };
        },
        /**
         * 显示关联司机
         * @param {Object} row - 行数据对象
         */
        openAssociatedDrivers(row) {
            if (row.hasOwnProperty('carCode')) {
                this.associatedDriversLoading = true;
                operationConfiguration
                    .getUnbindDriverList()
                    .then((response) => {
                        if (response.code === 200) {
                            this.associatedDriversList = response.data;
                            this.associatedDriversVisible = true;
                            // 如果需要将当前车辆编码与关联司机表单进行关联，这里设置
                            // 注意：此处根据业务需求决定是否需要将 carCode 与 associatedDrivers 合并
                            // 若 associatedDrivers 是对象，则合并
                            if (typeof this.associatedDrivers === 'object') {
                                this.associatedDrivers = { ...this.associatedDrivers, carCode: row.carCode };
                            } else {
                                this.associatedDrivers = { carCode: row.carCode };
                            }
                        } else {
                            this.$message.error('获取司机列表失败');
                        }
                    })
                    .finally(() => {
                        this.associatedDriversLoading = false;
                    });
            } else {
                this.$message.error('未获取到车辆编码');
            }
        },
        // 获取绑定传感器
        openTheSensorPopUpWindow(val) {
            const loading = ElLoading.service({
                lock: true,
                text: '获取数据中...',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            // 暂存数据
            this.formSensor = { ...val };
            operationConfiguration.getdeviceList({ current: 1, size: 10000 ,operateStatus: '0', deviceStatus: '0' }).then((response) => {
                if (response.code === 200 && response.data) {
                    if (response.data.records) {
                        this.sensorTypeOptions = response.data.records;
                    }
                    operationConfiguration.getCarBindDevice({ carCode: val.carCode }).then((response) => {
                        if (response.code === 200 && response.data) {
                            if (response.data?.records?.length > 0) {
                                this.formSensor.deviceNumbers = response.data.records.map((item) => item.deviceNo);
                            }
                            this.bindingSensorVisible = true;
                        }
                    });
                }
            }).finally(() => {
                loading.close();
            });
        },
        // 删除tag时调用
        removeDevice(e) {
            let index = this.formSensor.deviceNumbers.findIndex((item) => item == e);
            if (index > -1) {
                this.formSensor.deviceNumbers.splice(index, 1);
            }
            this.$forceUpdate();
        },
        // 表单重置
        reset() {
            this.form = {
                carType: null,
                carCode: null,
                regeistTime: null,
                brand: null,
                carload: null,
                length: null,
                wide: null,
                high: null,
                status: '0',
                remark: null
            };
            this.$refs['form'] ? this.$refs['form'].resetFields() : '';
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.handleQuery();
        },
        /**
         * 保存关联司机
         */
        saveAssociatedDriver() {
            this.$refs['associatedDrivers'].validate((valid) => {
                if (valid) {
                    let carBindDriver = {
                        carCode: this.associatedDrivers.carCode,
                        driverCode: this.associatedDrivers.driver.driverCode,
                        driverName: this.associatedDrivers.driver.driverName
                    };
                    operationConfiguration.bindDriver(carBindDriver).then((response) => {
                        if (response.code === 200) {
                            this.msgSuccess('绑定司机成功');
                            this.closeAssociatedDrivers();
                            this.getList();
                        }
                    });
                }
            });
        },
        saveCarDevice() {
            //this.formSensor.deviceNo长度为0时，提示至少选择一个传感器
            if (this.formSensor.deviceNumbers.length === 0) {
                this.$message({
                    message: '至少选择一个传感器',
                    type: 'warning'
                });
                return;
            }
            let params = this.formSensor.deviceNumbers.map((item) => {
                return { carCode: this.formSensor.carCode, deviceNo: item };
            });

            operationConfiguration.bindDevice(params).then((response) => {
                if (response.code === 200) {
                    this.$message({
                        message: '绑定成功',
                        type: 'success'
                    });
                    this.bindingSensorVisible = false;
                    this.getList();
                }
            });
        },
        /** 状态字典转换 */
        statusFormat(val) {
            return this.selectDictLabel(this.statusOptions, val.status);
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        if (this.form.carload == '') {
                            this.form.carload = 0;
                        }
                        if (this.form.length == '') {
                            this.form.length = 0;
                        }
                        if (this.form.wide == '') {
                            this.form.wide = 0;
                        }
                        if (this.form.high == '') {
                            this.form.high = 0;
                        }
                        let params = { ...this.form, carType: { typeCode: this.form.carType } };
                        operationConfiguration.editCarInfo(params).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        let params = { ...this.form, carType: { typeCode: this.form.carType } };
                        operationConfiguration.saveCarInfo(params).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
		/**
		 * 展开折叠
		 */
		showAllClick() {
			this.isShowAll = !this.isShowAll;
		}

	}
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
::v-deep {
    .el-tree {
        .el-tree-node__label {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            height: 100%;
        }
    }
    .el-drawer__header {
        margin-bottom: 20px;
    }
}
</style>
