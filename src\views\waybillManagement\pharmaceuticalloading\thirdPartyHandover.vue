<template>
    <div>
        <div class="app-container customer-auto-height-container">
            <!-- 搜索区域 -->
            <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card"
                shadow="never">
                <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" @submit.prevent>
                    <el-form-item label="三方运单号" prop="thirdWaybillNo" style="width: 250px">
                        <el-input v-model="queryParams.thirdWaybillNo" clearable placeholder="请输入三方运单号"
                            @keyup.enter="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item label="三方物流" prop="thirdType" style="width: 250px">
                        <el-select v-model="queryParams.thirdType" clearable placeholder="请选择三方物流"
                            @change="handleQuery">
                            <el-option v-for="dict in thirdPartyLogisticsList" :key="dict.value" :label="dict.name"
                                :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="下单状态" prop="status" style="width: 250px">
                        <el-select v-model="queryParams.status" clearable placeholder="请选择下单状态" @change="handleQuery">
                            <el-option v-for="dict in thirdOrderStatusList" :key="dict.value" :label="dict.name"
                                :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShowAll" label="订单号" prop="orderNo" style="width: 250px">
                        <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号"
                            @keyup.enter="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAll" label="运单号" prop="transOrderNo" style="width: 250px">
                        <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单号"
                            @keyup.enter="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAll" label="保温箱号" prop="incubatorNo" style="width: 250px">
                        <el-input v-model="queryParams.incubatorNo" clearable placeholder="请输入保温箱号"
                            @keyup.enter="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAll" label="货主名称" prop="ownerName" style="width: 250px">
                        <el-input v-model="queryParams.ownerName" clearable placeholder="请输入货主名称"
                            @keyup.enter="handleQuery"></el-input>
                    </el-form-item>
                    <search-button :is-show-all="isShowAll" @handleQuery="getList(1)"
                        @resetQuery="resetQuery('queryForm')" @showAllClick="showAllClick" />
                </el-form>
            </el-card>

            <!-- 列表区域 -->
            <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }"
                shadow="never">
                <div class="mb10" style="display: flex">
                    <el-button icon="el-icon-suitcase" type="primary"
                        @click="handleThirdPartyHandover">交接三方快递</el-button>
                    <el-button icon="el-icon-download" type="warning" @click="handleExport">导出</el-button>
                    <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" style="margin-left: auto"
                        table-i-d="ThirdPartyHandover" @queryTable="getList"></right-toolbar>
                </div>

                <column-table key="ThirdPartyHandover" ref="ColumnTable" v-loading="loading" :columns="columns"
                    :data="dataList" :defaultSort="{ prop: 'createDate', order: 'descending' }" :show-check-box="false"
                    class="customer-auto-height-table" max-height="null" show-index>
                    <template #thirdType="{ row }">
                        <span>{{ formatDictionaryData('thirdPartyLogisticsList', row.thirdType) }}</span>
                    </template>
                    <template #status="{ row }">
                        <span v-if="row.status === 1" style="color: #67C23A;">{{
                            formatDictionaryData('thirdOrderStatusList',
                            row.status) }}</span>
                        <span v-else-if="row.status === 2" style="color: #F56C6C;">
                            {{ formatDictionaryData('thirdOrderStatusList', row.status) }}
                            <el-popover :width="300" placement="bottom" trigger="click">
                                <template #reference>
                                    <el-button link size="small" type="primary">查看</el-button>
                                </template>
                                <div style="color: #F56C6C; line-height: 1.5;">
                                    {{ row.failReason || '接口调用超时，请稍后重试' }}
                                </div>
                            </el-popover>
                        </span>
                        <span v-else>{{ formatDictionaryData('thirdOrderStatusList', row.status) }}</span>
                    </template>
                    <template #isIncubator="{ row }">
                        <el-tag :type="row.isIncubator ? 'success' : 'info'">{{ row.isIncubator ? '是' : '否'
                        }}
                        </el-tag>
                    </template>
                    <template #isPrint="{ row }">
                        <el-tag :type="row.isPrint ? 'success' : 'warning'">{{ row.isPrint ? '是' : '否' }}</el-tag>
                    </template>
                    <template #operation="{ row }">
                        <!-- 下单成功：显示面单获取/打印箱签、撤销订单 -->
                        <template v-if="row.status === 1">
                            <!-- 根据 isGetWaybill 状态显示不同的按钮文本 -->
                            <el-button
                                icon="el-icon-document"
                                link
                                size="small"
                                type="primary"
                                @click="onClickFile(row)">
                                {{ row.isGetWaybill ? `打印${getThreePartyWaybill(row)}箱签` : `获取${getThreePartyWaybill(row)}面单` }}
                            </el-button>
                            <el-button icon="el-icon-close" link size="small" type="warning" @click="cancelOrder(row)">撤销订单</el-button>
                        </template>
                        <!-- 下单失败：显示重新下单 -->
                        <template v-else-if="row.status === 2">
                            <el-button icon="el-icon-refresh" link size="small" type="success"
                                @click="reOrder(row)">重新下单
                            </el-button>
                        </template>
                        <!-- 已撤销：显示操作日志 -->
                        <template v-else-if="row.status === 3">
                            <!-- <el-button icon="el-icon-document" link size="small" type="info"
                                @click="viewOperationLog(row)">操作日志
                            </el-button> -->
                        </template>
                        <!-- 初始状态：不显示按钮 -->
                    </template>
                </column-table>
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
                    :total="total" class="mb0" @pagination="handlePagination" />
            </el-card>
        </div>

        <!-- 交接三方快递抽屉 -->
        <el-drawer v-model="handoverDrawerVisible" size="80%" title="交接三方快递" @close="closeHandoverDrawer">
            <div style="height: 100%; display: flex; flex-direction: column; padding: 20px; box-sizing: border-box;">
                <!-- 三方快递选择和操作按钮 -->
                <div
                    style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px; flex-shrink: 0;">
                    <div style="display: flex; align-items: center;">
                        <span style="margin-right: 10px;font-size: 14px;">
                            <span style="color: #f56c6c; margin-right: 4px;">*</span>
                            三方快递：
                        </span>
                        <el-select v-model="selectedThirdParty" :class="{ 'is-error': thirdPartyError }"
                            :disabled="handoverLoading || incubatorLoading || waybillLoading" clearable filterable
                            placeholder="请选择三方快递" style="width: 200px;" @change="clearThirdPartyError">
                            <el-option v-for="dict in thirdPartyLogisticsList" :key="dict.value" :label="dict.name"
                                :value="dict.value" />
                        </el-select>
                        <span v-if="thirdPartyError" style="color: #f56c6c; font-size: 12px; margin-left: 8px;">{{
                            thirdPartyErrorMessage }}</span>
                    </div>
                    <div>
                        <el-button :disabled="handoverLoading || incubatorLoading || waybillLoading"
                            @click="closeHandoverDrawer">
                            取消
                        </el-button>
                        <el-button type="primary"
                            :loading="handoverLoading" @click="confirmHandover">
                            <span v-if="!handoverLoading">确定交接</span>
                            <span v-else>交接中...</span>
                        </el-button>
                    </div>
                </div>

                <!-- 选择按钮区域 -->
                <div style="margin-bottom: 10px; flex-shrink: 0;">
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <el-button type="warning" :disabled="handoverLoading || incubatorLoading || waybillLoading"
                            @click="openIncubatorSelector">
                            <i class="el-icon-box" style="margin-right: 4px;"></i>
                            选择保温箱
                        </el-button>
                        <el-button type="primary" :disabled="handoverLoading || incubatorLoading || waybillLoading"
                            @click="openWaybillSelector">
                            <i class="el-icon-document" style="margin-right: 4px;"></i>
                            选择运单
                        </el-button>
                        <span style="color: #909399; font-size: 13px; margin-left: 8px;">
                            💡 提示：可以同时选择保温箱和运单，也可以只选择其中一种
                        </span>
                    </div>
                </div>

                <!-- 表格容器区域 -->
                <div style="flex: 1; display: flex; flex-direction: column; min-height: 0; ">
                    <!-- 空状态显示 -->
                    <div v-if="!hasSelectedItems"
                        style="flex: 1; display: flex; align-items: center; justify-content: center; background-color: #ffffff; border-radius: 8px; border: 1px solid #e9ecef;">
                        <el-empty :image-size="120" description="暂未选择保温箱和运单">
                            <template #description>
                                <div style="margin-top: 16px;">
                                    <p style="color: #6c757d; font-size: 16px; font-weight: 500; margin: 0 0 8px 0;">
                                        暂未选择保温箱和运单</p>
                                    <p style="color: #9ca3af; font-size: 14px; margin: 0 0 12px 0;">请点击上方按钮选择保温箱或运单</p>
                                    <div
                                        style="display: flex; align-items: center; justify-content: center; gap: 16px; margin-top: 16px;">
                                        <div
                                            style="display: flex; align-items: center; gap: 6px; color: #909399; font-size: 13px;">
                                            <i class="el-icon-box" style="color: #e6a23c;"></i>
                                            <span>保温箱：按地区批量配送</span>
                                        </div>
                                        <div
                                            style="display: flex; align-items: center; gap: 6px; color: #909399; font-size: 13px;">
                                            <i class="el-icon-document" style="color: #67c23a;"></i>
                                            <span>运单：单独配送</span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </el-empty>
                    </div>

                    <!-- 已选保温箱列表 -->
                    <div v-if="selectedIncubators.length > 0" :class="[
                        'table-container',
                        selectedWaybills.length > 0 ? 'has-both-tables' : 'single-table',
                        selectedIncubators.length <= 5 && selectedWaybills.length > 5 ? 'small-data' : '',
                        selectedIncubators.length > 5 && selectedWaybills.length <= 5 ? 'large-data' : ''
                    ]">
                        <div class="table-header">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="el-icon-box" style="color: #409EFF; font-size: 16px;"></i>
                                    <span style="color: #303133; font-size: 16px; font-weight: 600;">已选保温箱</span>
                                    <el-tag size="small" type="primary">{{ selectedIncubators.length }} 个</el-tag>
                                </div>
                                <span style="color: #909399; font-size: 13px;">
                                    💡 可设置物流收件地址或撤销不需要的保温箱
                                </span>
                            </div>
                        </div>
                        <div class="table-content" style="padding: 0;">
                            <el-table :data="selectedIncubators" border style="width: 100%; height: 100%;">
                                <el-table-column align="center" fixed="left" label="保温箱" prop="incubatorNo"
                                    show-overflow-tooltip width="140">
                                    <template #default="scope">
                                        <span style="font-weight: 500;">{{ scope.row.incubatorNo }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="单数/件数" width="140">
                                    <template #default="scope">
                                        <div
                                            style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                                            <span>{{ scope.row.count }}</span>
                                            <el-popover :width="420" placement="bottom" trigger="click">
                                                <template #reference>
                                                    <el-button link size="small" type="primary">
                                                        <el-icon>
                                                            <View />
                                                        </el-icon>
                                                    </el-button>
                                                </template>
                                                <div style="padding-bottom: 8px;">
                                                    <h4
                                                        style="margin: 0 0 12px 0; color: #303133; font-size: 14px; font-weight: 600;">
                                                        箱贴详情
                                                    </h4>
                                                    <el-table :data="scope.row.details" size="small" stripe
                                                        style="width: 100%;">
                                                        <el-table-column align="center" label="箱贴号" prop="code"
                                                            show-overflow-tooltip />
                                                        <el-table-column align="center" label="描述" prop="codeDesc"
                                                            show-overflow-tooltip width="120" />
                                                    </el-table>
                                                </div>
                                            </el-popover>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column align="left" label="收货地址" min-width="200" prop="receiveAddress"
                                    show-overflow-tooltip>
                                    <template #default="scope">
                                        <!-- 多个地址时，如果已选择则显示选择的地址的省市区镇，否则显示提示 -->
                                        <span v-if="!hasMultipleAddresses(scope.row)">{{ scope.row.receiveAddress }}</span>
                                        <span v-else-if="scope.row.selectedLogisticsAddress">{{ getSelectedAddressRegion(scope.row) }}</span>
                                        <span v-else style="color: #c0c4cc; font-style: italic;">多地址配送</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="left" label="详细地址" width="320" prop="receiveAddressDetail"
                                    show-overflow-tooltip>
                                    <template #default="scope">
                                        <!-- 单个地址时正常显示 -->
                                        <div v-if="!hasMultipleAddresses(scope.row)" style="line-height: 1.5; max-width: 320px;">
                                            <div v-for="(address, index) in scope.row.receiveAddressDetail" :key="index"
                                                :style="{
                                                    padding: '6px 8px',
                                                    backgroundColor: scope.row.selectedLogisticsAddress === address ? '#e6f7ff' : 'transparent',
                                                    border: scope.row.selectedLogisticsAddress === address ? '1px solid #91d5ff' : '1px solid transparent',
                                                    borderRadius: '4px',
                                                    fontSize: '12px',
                                                    position: 'relative',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'space-between'
                                                }" :title="address">
                                                <span
                                                    style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                    {{ address }}
                                                </span>
                                                <el-tag v-if="scope.row.selectedLogisticsAddress === address"
                                                    size="small" type="primary"
                                                    style="margin-left: 8px; flex-shrink: 0;">
                                                    已选择
                                                </el-tag>
                                            </div>
                                        </div>
                                        <!-- 多个地址时，如果已选择则显示选择的详细地址，否则显示提示 -->
                                        <div v-else-if="scope.row.selectedLogisticsAddress" style="line-height: 1.5; max-width: 320px;">
                                            <div style="
                                                padding: 6px 8px;
                                                background-color: #e6f7ff;
                                                border: 1px solid #91d5ff;
                                                border-radius: 4px;
                                                font-size: 12px;
                                                display: flex;
                                                align-items: center;
                                                justify-content: space-between;
                                            ">
                                                <span style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                    {{ getSelectedAddressDetail(scope.row) }}
                                                </span>
                                                <el-tag size="small" type="primary" style="margin-left: 8px; flex-shrink: 0;">
                                                    已选择
                                                </el-tag>
                                            </div>
                                        </div>
                                        <!-- 多个地址且未选择时显示提示 -->
                                        <div v-else style="color: #c0c4cc; font-style: italic; text-align: center; padding: 10px;">
                                            请在物流收件地址中设置
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" fixed="right" label="物流收件地址" width="150">
                                    <template #default="scope">
                                        <!-- 多个地址且已设置 -->
                                        <div
                                            v-if="hasMultipleAddresses(scope.row) && scope.row.selectedLogisticsAddress"
                                            style="display: flex; align-items: center; gap: 8px;">
                                            <el-tag size="small" type="success">已设置</el-tag>
                                            <el-popover :width="500" placement="bottom" trigger="click"
                                                :visible="addressPopoverVisible && currentIncubatorForAddress?.incubatorRecordId === scope.row.incubatorRecordId"
                                                @hide="closeAddressPopover">
                                                <template #reference>
                                                    <el-button link size="small" type="primary"
                                                        :disabled="handoverLoading"
                                                        @click="setLogisticsAddress(scope.row)">
                                                        <el-icon>
                                                            <Setting />
                                                        </el-icon>
                                                        <span style="margin-left: 4px;">重新设置</span>
                                                    </el-button>
                                                </template>
                                                <div style="padding: 8px 0;">
                                                    <h4 style="margin: 0 0 12px 0; color: #303133; font-size: 14px; font-weight: 600;">
                                                        选择收件地址
                                                    </h4>
                                                    <el-table :data="getAddressTableData(scope.row)" size="small"
                                                        style="width: 100%;" @row-click="onAddressRowClick">
                                                        <el-table-column width="40" align="center">
                                                            <template #default="{ row }">
                                                                <el-radio v-model="selectedLogisticsAddress" :label="row.fullAddress"
                                                                    @change="onAddressSelectionChange">
                                                                    <span></span>
                                                                </el-radio>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="收件地址" prop="regionAddress" min-width="150"
                                                            show-overflow-tooltip />
                                                        <el-table-column label="收件详细地址" prop="detailAddress" min-width="120"
                                                            show-overflow-tooltip />
                                                    </el-table>
                                                </div>
                                            </el-popover>
                                        </div>
                                        <!-- 多个地址但未设置 -->
                                        <div
                                            v-else-if="hasMultipleAddresses(scope.row)"
                                            style="display: flex; align-items: center; gap: 8px;">
                                            <el-tag size="small" type="warning">未设置</el-tag>
                                            <el-popover :width="500" placement="bottom" trigger="click"
                                                :visible="addressPopoverVisible && currentIncubatorForAddress?.incubatorRecordId === scope.row.incubatorRecordId"
                                                @hide="closeAddressPopover">
                                                <template #reference>
                                                    <el-button link size="small" type="primary"
                                                        :disabled="handoverLoading"
                                                        @click="setLogisticsAddress(scope.row)">
                                                        <el-icon>
                                                            <Setting />
                                                        </el-icon>
                                                        <span style="margin-left: 4px;">设置</span>
                                                    </el-button>
                                                </template>
                                                <div style="padding: 8px 0;">
                                                    <h4 style="margin: 0 0 12px 0; color: #303133; font-size: 14px; font-weight: 600;">
                                                        选择收件地址
                                                    </h4>
                                                    <el-table :data="getAddressTableData(scope.row)" size="small"
                                                        style="width: 100%;" @row-click="onAddressRowClick">
                                                        <el-table-column width="40" align="center">
                                                            <template #default="{ row }">
                                                                <el-radio v-model="selectedLogisticsAddress" :label="row.fullAddress"
                                                                    @change="onAddressSelectionChange">
                                                                    <span></span>
                                                                </el-radio>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="收货地址" prop="regionAddress" min-width="150"
                                                            show-overflow-tooltip />
                                                        <el-table-column label="详细地址" prop="detailAddress" min-width="120"
                                                            show-overflow-tooltip />
                                                    </el-table>
                                                </div>
                                            </el-popover>
                                        </div>
                                        <!-- 单一地址或无地址 -->
                                        <span v-else style="color: #909399; font-size: 12px;">
                                            {{ getSingleAddressStatus(scope.row) }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" fixed="right" label="操作" width="100">
                                    <template #default="scope">
                                        <el-popconfirm :title="`确认要撤销保温箱【${scope.row.incubatorNo}】吗？`"
                                            confirm-button-text="确定" cancel-button-text="取消"
                                            confirm-button-type="danger" width="250" placement="left"
                                            @confirm="removeIncubator(scope.row)">
                                            <template #reference>
                                                <el-button link size="small" type="danger" :disabled="handoverLoading">
                                                    <el-icon>
                                                        <Delete />
                                                    </el-icon>
                                                    <span style="margin-left: 4px;">撤销</span>
                                                </el-button>
                                            </template>
                                        </el-popconfirm>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>

                    <!-- 已选运单列表 -->
                    <div v-if="selectedWaybills.length > 0" :class="[
                        'table-container',
                        selectedIncubators.length > 0 ? 'has-both-tables' : 'single-table',
                        selectedWaybills.length <= 5 && selectedIncubators.length > 5 ? 'small-data' : '',
                        selectedWaybills.length > 5 && selectedIncubators.length <= 5 ? 'large-data' : ''
                    ]">
                        <div class="table-header">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="el-icon-document" style="color: #67C23A; font-size: 16px;"></i>
                                    <span style="color: #303133; font-size: 16px; font-weight: 600;">已选运单</span>
                                    <el-tag size="small" type="success">{{ selectedWaybills.length }} 个</el-tag>
                                </div>
                                <span style="color: #909399; font-size: 13px;">
                                    💡 可撤销不需要的运单，确认无误后进行交接
                                </span>
                            </div>
                        </div>
                        <div class="table-content" style="padding: 0;">
                            <el-table :data="selectedWaybills" border style="width: 100%; height: 100%;">
                                <el-table-column align="center" fixed="left" label="运单号" prop="waybillNo"
                                    show-overflow-tooltip width="160">
                                    <template #default="scope">
                                        <span
                                            style="color: #303133; font-weight: 500; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;">{{
                                            scope.row.waybillNo }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="件数" prop="count" show-overflow-tooltip
                                    width="80" />
                                <el-table-column align="left" label="收货公司" prop="logistics" show-overflow-tooltip
                                    min-width="150">
                                    <template #default="scope">
                                        <div style="display: flex; align-items: center; gap: 6px;">
                                            <i class="el-icon-office-building"
                                                style="color: #909399; font-size: 14px;"></i>
                                            <span>{{ scope.row.logistics }}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column align="left" label="收货地址" min-width="200" prop="collectAddress"
                                    show-overflow-tooltip>
                                    <template #default="scope">
                                        <span>{{ scope.row.collectAddress }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="left" label="详细地址" min-width="260" prop="deliveryAddress"
                                    show-overflow-tooltip>
                                    <template #default="scope">
                                        <div style="line-height: 1.5;">
                                            <div v-for="(address, index) in scope.row.deliveryAddress" :key="index"
                                                style="margin-bottom: 4px;">
                                                {{ address }}
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" fixed="right" label="操作" width="100">
                                    <template #default="scope">
                                        <el-popconfirm :title="`确认要撤销运单【${scope.row.waybillNo}】吗？`"
                                            confirm-button-text="确定" cancel-button-text="取消"
                                            confirm-button-type="danger" width="320" placement="left"
                                            @confirm="removeWaybill(scope.row)">
                                            <template #reference>
                                                <el-button link size="small" type="danger" :disabled="handoverLoading">
                                                    <el-icon>
                                                        <Delete />
                                                    </el-icon>
                                                    <span style="margin-left: 4px;">撤销</span>
                                                </el-button>
                                            </template>
                                        </el-popconfirm>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>

        <!-- 保温箱选择抽屉 -->
        <el-drawer v-model="incubatorSelectorVisible" size="80%" title="保温箱选择" @close="closeIncubatorSelector">
            <div style="height: 100%; display: flex; flex-direction: column;">
                <!-- 搜索区域 -->
                <div style="padding: 20px 20px 0 20px; background-color: #f8f9fa; border-bottom: 1px solid #e9ecef;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span style="color: #606266; font-weight: 500; min-width: 80px;font-size: 14px;">省市区选择：</span>
                        <el-cascader v-model="incubatorSearchRegion" :options="regionOptions" :props="cascaderProps"
                            :disabled="incubatorLoading" clearable filterable placeholder="请选择省市区" style="width: 300px;"
                            @change="handleIncubatorRegionChange" @visible-change="handleIncubatorCascaderVisible">
                        </el-cascader>
                        <el-button type="primary" style="margin-left: 10px;" :disabled="incubatorLoading"
                            :loading="incubatorLoading" @click="searchIncubators">
                            <span v-if="!incubatorLoading">搜索</span>
                            <span v-else>搜索中...</span>
                        </el-button>
                        <el-button :disabled="incubatorLoading" @click="resetIncubatorSearch">
                            重置
                        </el-button>
                        <span style="color: #909399; font-size: 13px; margin-left: 12px;">
                            💡 可按省市区筛选保温箱，便于批量选择同地区的货物
                        </span>
                    </div>
                </div>

                <!-- 操作区域 -->
                <div style="padding: 16px 20px; background-color: #ffffff; border-bottom: 1px solid #e9ecef;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;">
                            <el-button plain type="primary" :disabled="incubatorLoading"
                                @click="closeIncubatorSelector">
                                返回
                            </el-button>
                            <el-button type="primary" :disabled="incubatorLoading" :loading="incubatorLoading"
                                @click="confirmIncubatorSelection">
                                <span v-if="!incubatorLoading">确认选择</span>
                                <span v-else>加载中...</span>
                            </el-button>
                            <span style="color: #909399; font-size: 13px;margin-left: 15px;">
                                ✓ 勾选表格中的保温箱，然后点击确认选择
                            </span>
                        </div>
                        <div>
                            <span style="color: #409EFF; font-weight: 600; font-size: 14px;">已选 保温箱 {{
                                tempSelectedIncubators.length }} 个</span>
                        </div>
                    </div>
                </div>

                <!-- 列表内容区域 -->
                <div style="flex: 1; padding: 20px; background-color: #ffffff; overflow: hidden;">
                    <!-- 保温箱表格 -->
                    <el-table ref="incubatorTable" v-loading="incubatorLoading" :data="availableIncubators" border
                        height="100%" style="width: 100%;" element-loading-text="正在加载保温箱数据..."
                        @selection-change="handleIncubatorSelectionChange">
                        <el-table-column align="center" type="selection" width="55" />
                        <el-table-column align="center" label="保温箱" prop="incubatorNo" show-overflow-tooltip
                            width="120" />
                        <el-table-column align="center" label="单数/件数" prop="count" show-overflow-tooltip width="120" />
                        <el-table-column align="left" label="收货地址" prop="receiveAddress" show-overflow-tooltip
                            width="200">
                            <template #default="scope">
                                <!-- 在选择器中显示所有地址的省市区镇信息 -->
                                <div v-if="scope.row.addressList && scope.row.addressList.length > 0" style="line-height: 1.5;">
                                    <div v-for="(address, index) in scope.row.addressList" :key="index"
                                        style="margin-bottom: 4px;">
                                        {{ buildAddressStringFromAddressItem(address) }}
                                    </div>
                                </div>
                                <!-- 兼容旧数据结构 -->
                                <span v-else-if="scope.row.receiveAddress">{{ scope.row.receiveAddress }}</span>
                                <span v-else style="color: #c0c4cc;">无地址</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="left" label="详细地址" min-width="300" prop="receiveAddressDetail"
                            show-overflow-tooltip>
                            <template #default="scope">
                                <!-- 优先显示新数据结构中的详细地址 -->
                                <div v-if="scope.row.addressList && scope.row.addressList.length > 0" style="line-height: 1.5;">
                                    <div v-for="(address, index) in scope.row.addressList" :key="index"
                                        style="margin-bottom: 4px;">
                                        {{ address.receiveAddress || '' }}
                                    </div>
                                </div>
                                <!-- 兼容旧数据结构 -->
                                <div v-else style="line-height: 1.5;">
                                    <div v-for="(address, index) in scope.row.receiveAddressDetail" :key="index"
                                        style="margin-bottom: 4px;">
                                        {{ address }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <!-- 底部操作区域 -->
                <div style="padding: 16px 20px; background-color: #f8f9fa; border-top: 1px solid #e9ecef;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span style="color: #909399; font-size: 13px;">
                            📋 已选择 {{ tempSelectedIncubators.length }} 个保温箱，点击确认选择完成操作
                        </span>
                        <div>
                            <el-button :disabled="incubatorLoading" @click="closeIncubatorSelector">
                                取消
                            </el-button>
                            <el-button type="primary" :disabled="incubatorLoading" :loading="incubatorLoading"
                                @click="confirmIncubatorSelection">
                                <span v-if="!incubatorLoading">确认选择</span>
                                <span v-else>加载中...</span>
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>

        <!-- 运单选择抽屉 -->
        <el-drawer v-model="waybillSelectorVisible" size="80%" title="运单选择" @close="closeWaybillSelector">
            <div style="height: 100%; display: flex; flex-direction: column;">
                <!-- 搜索区域 -->
                <div style="padding: 20px 20px 0 20px; background-color: #f8f9fa; border-bottom: 1px solid #e9ecef;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span style="color: #606266; font-weight: 500; min-width: 80px;font-size: 14px;">省市区选择：</span>
                        <el-cascader v-model="waybillSearchRegion" :options="regionOptions" :props="cascaderProps"
                            :disabled="waybillLoading" clearable filterable placeholder="请选择省市区" style="width: 300px;"
                            @change="handleWaybillRegionChange" @visible-change="handleWaybillCascaderVisible">
                        </el-cascader>
                        <el-button type="primary" style="margin-left: 10px;" :disabled="waybillLoading"
                            :loading="waybillLoading" @click="searchWaybills">
                            <span v-if="!waybillLoading">搜索</span>
                            <span v-else>搜索中...</span>
                        </el-button>
                        <el-button :disabled="waybillLoading" @click="resetWaybillSearch">
                            重置
                        </el-button>
                        <span style="color: #909399; font-size: 13px; margin-left: 12px;">
                            💡 可按省市区筛选运单，便于查找特定地区的配送需求
                        </span>
                    </div>
                </div>

                <!-- 操作区域 -->
                <div style="padding: 16px 20px; background-color: #ffffff; border-bottom: 1px solid #e9ecef;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center;;">
                            <el-button plain type="primary" :disabled="waybillLoading" @click="closeWaybillSelector">
                                返回
                            </el-button>
                            <el-button type="primary" :disabled="waybillLoading" :loading="waybillLoading"
                                @click="confirmWaybillSelection">
                                <span v-if="!waybillLoading">确认选择</span>
                                <span v-else>加载中...</span>
                            </el-button>
                            <span style="color: #909399; font-size: 13px;margin-left: 15px;">
                                ✓ 勾选表格中的运单，然后点击确认选择
                            </span>
                        </div>
                        <div>
                            <span style="color: #409EFF; font-weight: 600; font-size: 14px;">已选 运单 {{
                                tempSelectedWaybills.length }} 个</span>
                        </div>
                    </div>
                </div>

                <!-- 列表内容区域 -->
                <div style="flex: 1; padding: 20px; background-color: #ffffff; overflow: hidden;">
                    <!-- 运单表格 -->
                    <el-table ref="waybillTable" v-loading="waybillLoading" :data="availableWaybills" border
                        height="100%" style="width: 100%;" element-loading-text="正在加载运单数据..."
                        @selection-change="handleWaybillSelectionChange">
                        <el-table-column align="center" type="selection" width="55" />
                        <el-table-column align="center" label="运单号" prop="waybillNo" show-overflow-tooltip
                            width="150" />
                        <el-table-column align="center" label="件数" prop="count" show-overflow-tooltip width="80" />
                        <el-table-column align="center" label="收货公司" prop="logistics" show-overflow-tooltip
                            min-width="150" />
                        <el-table-column align="center" label="收货地址" prop="collectAddress" show-overflow-tooltip
                            min-width="200" />
                        <el-table-column align="left" label="详细地址" min-width="300" prop="deliveryAddress"
                            show-overflow-tooltip>
                            <template #default="scope">
                                <div style="line-height: 1.5;">
                                    <div v-for="(address, index) in scope.row.deliveryAddress" :key="index"
                                        style="margin-bottom: 4px;">
                                        {{ address }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <!-- 底部操作区域 -->
                <div style="padding: 16px 20px; background-color: #f8f9fa; border-top: 1px solid #e9ecef;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span style="color: #909399; font-size: 13px;">
                            📋 已选择 {{ tempSelectedWaybills.length }} 个运单，点击确认选择完成操作
                        </span>
                        <div>
                            <el-button :disabled="waybillLoading" @click="closeWaybillSelector">
                                取消
                            </el-button>
                            <el-button type="primary" :disabled="waybillLoading" :loading="waybillLoading"
                                @click="confirmWaybillSelection">
                                <span v-if="!waybillLoading">确认选择</span>
                                <span v-else>加载中...</span>
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import { Close, Delete, Setting, View } from '@element-plus/icons-vue';
import { debounce, downloadNoData } from '@/utils';
import thirdPartyHandoverApi from '@/api/waybillManagement/pharmaceuticalloading/thirdPartyHandover';
import { selectDictLabel } from '@/utils/dictLabel';

export default {
    name: 'ThirdPartyHandover',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar,
        Close,
        View,
        Setting,
        Delete
    },
    data() {
        return {
            loading: false,
            showSearch: true,
            isShowAll: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                thirdWaybillNo: '', // 三方运单号
                thirdType: '', // 三方物流类型
                status: '', // 状态
                orderNo: '', // 订单号
                transOrderNo: '', // 运单号
                incubatorNo: '', // 保温箱号
                ownerName: '' // 货主名称
            },
            // 表格数据
            dataList: [],
            total: 0,
            selectedRows: [],
            // 缓存数据
            cachedRegionOptions: null,
            cachedDictData: {},
            // 防抖函数
            debouncedSearch: null,
            debouncedIncubatorSearch: null,
            debouncedWaybillSearch: null,
            // 请求控制
            currentRequestId: 0,
            // 表格列配置
            columns: [
                { title: '三方运单号', key: 'thirdWaybillNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '三方物流', key: 'thirdType', align: 'center', minWidth: '120px', columnShow: true },
                { title: '下单状态', key: 'status', align: 'center', minWidth: '120px', columnShow: true },
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '是否保温箱', key: 'isIncubator', align: 'center', minWidth: '100px', columnShow: true },
                { title: '保温箱号', key: 'incubatorNo', align: 'center', minWidth: '120px', columnShow: true },
                { title: '是否打印', key: 'isPrint', align: 'center', minWidth: '100px', columnShow: true },
                { title: '货主名称', key: 'ownerName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '发件人', key: 'sendName', align: 'center', minWidth: '100px', columnShow: true },
                { title: '发件人电话', key: 'sendPhone', align: 'center', minWidth: '120px', columnShow: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件人', key: 'receiveName', align: 'center', minWidth: '100px', columnShow: true ,showOverflowTooltip: true},
                { title: '收件人电话', key: 'receivePhone', align: 'center', minWidth: '120px', columnShow: true },
                { title: '收件公司', key: 'receiveCompany', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiveAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'operation', align: 'center', fixed: 'right', minWidth: '230px', hideFilter: true, columnShow: true }
            ],
            // 字典数据
            thirdPartyLogisticsList: [],
            thirdOrderStatusList: [],
            // 交接抽屉
            handoverDrawerVisible: false,
            selectedThirdParty: '',
            handoverLoading: false, // 交接操作加载状态
            // 校验相关
            thirdPartyError: false,
            thirdPartyErrorMessage: '',
            // 选中的保温箱数据
            selectedIncubators: [],
            // 选中的运单数据
            selectedWaybills: [],


            // 保温箱选择抽屉
            incubatorSelectorVisible: false,
            incubatorSearchRegion: [], // 保温箱搜索的省市区
            tempSelectedIncubators: [], // 临时选择的保温箱
            availableIncubators: [], // 可选保温箱列表
            incubatorLoading: false, // 保温箱列表加载状态

            // 运单选择抽屉
            waybillSelectorVisible: false,
            waybillSearchRegion: [], // 运单搜索的省市区
            tempSelectedWaybills: [], // 临时选择的运单
            availableWaybills: [], // 可选运单列表
            waybillLoading: false, // 运单列表加载状态

            // 级联选择器配置
            regionOptions: [],
            cascaderProps: {
                value: 'value',
                label: 'label',
                children: 'children',
                expandTrigger: 'hover',
                emitPath: true, // 返回完整的路径数组，用于获取省市区街道ID
                checkStrictly: true // 允许在任意层级选择，不强制选择到叶子节点
            },



            // 物流地址选择弹出框
            addressPopoverVisible: false,
            currentIncubatorForAddress: null, // 当前正在设置地址的保温箱
            selectedLogisticsAddress: '', // 选中的物流地址
            addressSelectionError: false,
            addressSelectionErrorMessage: ''
        };
    },

    created() {
        this.initializeDebouncedFunctions();
        this.getDict();
        this.getList();
    },
    beforeUnmount() {
        // 清理防抖函数和事件监听器
        this.cleanup();
    },
    computed: {
        /**
        * 格式化字典数据
        * @returns {function(*, *): string}
        */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },

        getThreePartyWaybill() {
            return (row) => {
                // 根据 row.thirdType 匹配 thirdPartyLogisticsList 的 value，返回对应的 name
                const item = this.thirdPartyLogisticsList.find(dict => dict.value == row.thirdType);
                return item ? item.name : '';
            };
        },

        /**
         * 是否有选中的保温箱或运单
         */
        hasSelectedItems() {
            return this.selectedIncubators.length > 0 || this.selectedWaybills.length > 0;
        },

        /**
         * 选中项目总数
         */
        selectedItemsCount() {
            return this.selectedIncubators.length + this.selectedWaybills.length;
        },

        /**
         * 是否可以进行交接
         */
        canHandover() {
            return this.selectedThirdParty && this.hasSelectedItems && !this.handoverLoading;
        },

        /**
         * 表格高度计算
         */
        tableHeight() {
            // 根据屏幕高度动态计算表格高度
            return window.innerHeight - 300;
        }
    },
    methods: {
        /**
         * 初始化防抖函数
         */
        initializeDebouncedFunctions() {
            this.debouncedSearch = debounce(this.performSearch, 300);
            this.debouncedIncubatorSearch = debounce(this.performIncubatorSearch, 500);
            this.debouncedWaybillSearch = debounce(this.performWaybillSearch, 500);
        },

        /**
         * 清理资源
         */
        cleanup() {
            // 清理防抖函数
            if (this.debouncedSearch) this.debouncedSearch.cancel?.();
            if (this.debouncedIncubatorSearch) this.debouncedIncubatorSearch.cancel?.();
            if (this.debouncedWaybillSearch) this.debouncedWaybillSearch.cancel?.();

            // 清理缓存
            this.cachedRegionOptions = null;
            this.cachedDictData = {};
        },

        /**
         * 获取字典数据 - 添加缓存机制
         * 初始化各种下拉选项的字典数据
         */
        async getDict() {
            try {
                // 使用缓存避免重复请求
                if (!this.cachedDictData.thirdPartyLogistics) {
                    this.cachedDictData.thirdPartyLogistics = await this.getDictList('third_party_logistics');
                }
                if (!this.cachedDictData.thirdOrderStatus) {
                    this.cachedDictData.thirdOrderStatus = await this.getDictList('third_party_order_status');
                }

                this.thirdPartyLogisticsList = this.cachedDictData.thirdPartyLogistics;
                this.thirdOrderStatusList = this.cachedDictData.thirdOrderStatus;
            } catch (error) {
                console.error('获取字典数据失败:', error);
                ElMessage.error('获取字典数据失败');
            }
        },

        // 撤销订单
        cancelOrder(row) {
            ElMessageBox.confirm(`确认要撤销订单 ${row.thirdWaybillNo} 吗？`, '撤销确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                // 开启全局 loading
                const loadingInstance = ElLoading.service({
                    text: '正在撤销订单...',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                try {
                    const response = await thirdPartyHandoverApi.cancelThirdPartyHandover(row.id);
                    if (response.code === 200) {
                        ElMessage.success('订单撤销成功');
                        this.getList();
                    } else {
                        ElMessage.error(response.message || '撤销订单失败');
                    }
                } catch (error) {
                    console.error('撤销订单失败:', error);
                    ElMessage.error('撤销订单失败，请稍后重试');
                } finally {
                    // 关闭全局 loading
                    loadingInstance.close();
                }
            });
        },

        // 清除三方快递错误
        clearThirdPartyError() {
            this.thirdPartyError = false;
            this.thirdPartyErrorMessage = '';
        },

        // 清除校验错误
        clearValidationErrors() {
            this.thirdPartyError = false;
            this.thirdPartyErrorMessage = '';
        },

        // 关闭交接抽屉
        closeHandoverDrawer() {
            this.handoverDrawerVisible = false;
            this.selectedThirdParty = '';
            this.selectedIncubators = [];
            this.selectedWaybills = [];
            this.handoverLoading = false;
            this.clearValidationErrors();
        },

        // 关闭保温箱选择器
        closeIncubatorSelector() {
            this.incubatorSelectorVisible = false;
            this.tempSelectedIncubators = [];
        },

        // 关闭运单选择器
        closeWaybillSelector() {
            this.waybillSelectorVisible = false;
            this.tempSelectedWaybills = [];
        },

        // 确认交接
        async confirmHandover() {
            // 清除之前的错误
            this.clearValidationErrors();

            // 校验表单
            if (!this.validateForm()) {
                return;
            }

            ElMessageBox.confirm('确认要交接选中的记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    this.handoverLoading = true;

                    // 构建请求参数
                    const requestData = this.buildHandoverRequest();

                    // 调用交接API
                    const response = await thirdPartyHandoverApi.handoverThirdPartyNew(requestData);

                    if (response.code === 200) {
                        ElMessage.success('交接成功');
                        this.closeHandoverDrawer();
                        this.getList();
                    }
                } catch (error) {
                    console.error('交接失败:', error);
                    ElMessage.error('交接失败，请稍后重试');
                } finally {
                    this.handoverLoading = false;
                }
            });
        },

        // 确认保温箱选择
        confirmIncubatorSelection() {
            this.selectedIncubators = [...this.tempSelectedIncubators];
            ElMessage.success(`已选择 ${this.selectedIncubators.length} 个保温箱`);
            this.closeIncubatorSelector();
        },

        // 确认运单选择
        confirmWaybillSelection() {
            this.selectedWaybills = [...this.tempSelectedWaybills];
            ElMessage.success(`已选择 ${this.selectedWaybills.length} 个运单`);
            this.closeWaybillSelector();
        },

        // 编辑记录
        editRecord(row) {
            ElMessage.info(`编辑记录: ${row.thirdWaybillNo}`);
        },
        // 获取保温箱列表 - 优化数据处理
        async getIncubatorList() {
            try {
                this.incubatorLoading = true;
                // 清空之前的数据，避免显示旧数据
                this.availableIncubators = [];

                const params = this.buildRegionParams(this.incubatorSearchRegion);
                const response = await thirdPartyHandoverApi.getIncubatorList(params);

                if (response.code === 200 && response.data) {
                    // 使用优化的数据转换方法
                    this.availableIncubators = this.transformIncubatorData(response.data);
                } else {
                    ElMessage.error('获取保温箱列表失败');
                    this.availableIncubators = [];
                }
            } catch (error) {
                console.error('获取保温箱列表失败:', error);
                ElMessage.error('获取保温箱列表失败');
                this.availableIncubators = [];
            } finally {
                this.incubatorLoading = false;
            }
        },

        /**
         * 构建地区参数 - 提取公共逻辑
         */
        buildRegionParams(searchRegion) {
            const params = {};
            if (!searchRegion || searchRegion.length === 0) {
                return params;
            }

            // 处理checkStrictly模式下的值格式，获取正确的路径数组
            let regionPath = searchRegion;
            if (typeof searchRegion === 'object' && searchRegion.pathValues) {
                regionPath = searchRegion.pathValues;
            } else if (Array.isArray(searchRegion) && searchRegion.length > 0 &&
                typeof searchRegion[0] === 'object' && searchRegion[0].pathValues) {
                regionPath = searchRegion[0].pathValues;
            }

            // 根据级联选择器的层级设置对应的地址ID
            if (regionPath && regionPath.length >= 1) {
                params.receiverProvinceId = regionPath[0]; // 省ID
            }
            if (regionPath && regionPath.length >= 2) {
                params.receiverCityId = regionPath[1]; // 市ID
            }
            if (regionPath && regionPath.length >= 3) {
                params.receiverCountyId = regionPath[2]; // 区ID
            }
            if (regionPath && regionPath.length >= 4) {
                params.receiverTownId = regionPath[3]; // 街道ID
            }

            return params;
        },

        /**
         * 转换保温箱数据 - 适配新的 addressList 数据结构
         */
        transformIncubatorData(data) {
            return data.map(item => {
                // 处理新的 addressList 数据结构
                let receiveAddress = '';
                let receiveAddressDetail = [];
                let receiveProvince = '';
                let receiveCity = '';
                let receiveCounty = '';
                let receiveTown = '';
                let originalReceiveAddress = '';

                if (item.addressList && Array.isArray(item.addressList) && item.addressList.length > 0) {
                    if (item.addressList.length === 1) {
                        // 单个地址：直接使用该地址信息
                        const address = item.addressList[0];
                        receiveProvince = address.receiveProvince || '';
                        receiveCity = address.receiveCity || '';
                        receiveCounty = address.receiveCounty || '';
                        receiveTown = address.receiveTown || '';
                        receiveAddress = this.buildAddressStringFromAddressItem(address);
                        receiveAddressDetail = [address.receiveAddress || ''];
                        originalReceiveAddress = address.receiveAddress || '';
                    } else {
                        // 多个地址：收货地址和详细地址显示为空，详细地址列表包含所有地址
                        receiveAddress = '';
                        receiveAddressDetail = item.addressList.map(addr => {
                            const regionPart = this.buildAddressStringFromAddressItem(addr);
                            const detailPart = addr.receiveAddress || '';
                            return regionPart + detailPart;
                        });
                        originalReceiveAddress = '';
                        // 省市区镇字段保持为空，因为有多个不同的地址
                        receiveProvince = '';
                        receiveCity = '';
                        receiveCounty = '';
                        receiveTown = '';
                    }
                } else {
                    // 兼容旧数据结构
                    receiveAddress = this.buildAddressString(item);
                    receiveAddressDetail = Array.isArray(item.receiveAddress) ? item.receiveAddress : [];
                    receiveProvince = item.receiveProvince || '';
                    receiveCity = item.receiveCity || '';
                    receiveCounty = item.receiveCounty || '';
                    receiveTown = item.receiveTown || '';
                    originalReceiveAddress = Array.isArray(item.receiveAddress) ?
                        item.receiveAddress.join('') : (item.receiveAddress || '');
                }

                return {
                    incubatorRecordId: item.incubatorRecordId,
                    incubatorNo: item.incubatorNo,
                    totalCount: item.totalCount,
                    totalBoxCount: item.totalBoxCount,
                    count: `${item.totalCount}单 ${item.totalBoxCount}件`,
                    receiveAddress: receiveAddress,
                    receiveAddressDetail: receiveAddressDetail,
                    // 保留原始的省市区镇字段，用于交接API
                    receiveProvince: receiveProvince,
                    receiveCity: receiveCity,
                    receiveCounty: receiveCounty,
                    receiveTown: receiveTown,
                    // 保留原始的详细地址，用于交接API
                    originalReceiveAddress: originalReceiveAddress,
                    // 使用 boxInfos 替换 details 作为箱贴详情信息
                    details: item.boxInfos || [],
                    // 保存原始的 addressList 用于地址选择
                    addressList: item.addressList || [],
                    // 用户选择的物流地址（初始为空）
                    selectedLogisticsAddress: null
                };
            });
        },

        /**
         * 构建地址字符串 - 提取公共逻辑
         */
        buildAddressString(item) {
            const addressParts = [];
            if (item.receiveProvince) addressParts.push(item.receiveProvince);
            if (item.receiveCity) addressParts.push(item.receiveCity);
            if (item.receiveCounty) addressParts.push(item.receiveCounty);
            if (item.receiveTown) addressParts.push(item.receiveTown);
            return addressParts.join('');
        },

        /**
         * 从 addressList 中的地址项构建地址字符串
         */
        buildAddressStringFromAddressItem(addressItem) {
            const addressParts = [];
            if (addressItem.receiveProvince) addressParts.push(addressItem.receiveProvince);
            if (addressItem.receiveCity) addressParts.push(addressItem.receiveCity);
            if (addressItem.receiveCounty) addressParts.push(addressItem.receiveCounty);
            if (addressItem.receiveTown) addressParts.push(addressItem.receiveTown);
            return addressParts.join('');
        },

        // 获取运单列表 - 优化数据处理
        async getWaybillList() {
            try {
                this.waybillLoading = true;
                // 清空之前的数据，避免显示旧数据
                this.availableWaybills = [];

                const params = this.buildRegionParams(this.waybillSearchRegion);
                const response = await thirdPartyHandoverApi.getWaybillList(params);

                if (response.code === 200 && response.data) {
                    // 使用优化的数据转换方法
                    this.availableWaybills = this.transformWaybillData(response.data);
                } else {
                    ElMessage.error('获取运单列表失败');
                    this.availableWaybills = [];
                }
            } catch (error) {
                console.error('获取运单列表失败:', error);
                ElMessage.error('获取运单列表失败');
                this.availableWaybills = [];
            } finally {
                this.waybillLoading = false;
            }
        },

        /**
         * 转换运单数据 - 提取数据转换逻辑
         */
        transformWaybillData(data) {
            return data.map(item => {
                const collectAddress = this.buildAddressString(item);
                const deliveryAddress = item.receiveAddress ? [item.receiveAddress] : [];

                return {
                    waybillNo: item.transOrderNo,
                    count: item.boxCount.toString(),
                    logistics: item.receiveCompany,
                    collectAddress: collectAddress,
                    deliveryAddress: deliveryAddress,
                    orderId: item.orderId,
                    transOrderNo: item.transOrderNo, // 保留原始的 transOrderNo 字段
                    receiveCompany: item.receiveCompany,
                    receiveAddress: collectAddress,
                    receiveAddressDetail: item.receiveAddress
                };
            });
        },

        // 获取列表数据 - 添加请求去重和错误重试
        async getList(page) {
            if (page) {
                this.queryParams.current = page;
            }

            // 生成请求ID，防止竞态条件
            const requestId = ++this.currentRequestId;
            this.loading = true;

            try {
                // 构建查询参数，过滤空值
                const params = this.buildCleanParams(this.queryParams);

                const response = await thirdPartyHandoverApi.getThirdPartyRecordList(params);

                // 检查请求是否已过期
                if (requestId !== this.currentRequestId) {
                    return; // 忽略过期的请求
                }

                if (response.code === 200 && response.data) {
                    this.dataList = response.data.records || [];
                    this.total = response.data.total || 0;
                } else {
                    ElMessage.error(response.message || '获取数据失败');
                    this.dataList = [];
                    this.total = 0;
                }
            } catch (error) {
                // 检查请求是否已过期
                if (requestId !== this.currentRequestId) {
                    return;
                }

                console.error('获取列表数据失败:', error);
                ElMessage.error('获取数据失败，请稍后重试');
                this.dataList = [];
                this.total = 0;
            } finally {
                // 只有当前请求才更新loading状态
                if (requestId === this.currentRequestId) {
                    this.loading = false;
                }
            }
        },

        /**
         * 构建清洁的查询参数
         */
        buildCleanParams(params) {
            const cleanParams = {};
            Object.keys(params).forEach(key => {
                if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
                    cleanParams[key] = params[key];
                }
            });
            return cleanParams;
        },

        /**
         * 执行搜索 - 防抖处理
         */
        performSearch() {
            this.getList(1);
        },

        /**
         * 处理分页事件
         */
        handlePagination(paginationData) {
            // 分页组件传递的是 { page, limit } 对象
            this.queryParams.current = paginationData.page;
            this.queryParams.size = paginationData.limit;
            this.getList();
        },

        // 查询处理 - 使用防抖
        handleQuery() {
            this.debouncedSearch();
        },

        // 获取订单状态类型
        getOrderStatusType(status) {
            const typeMap = {
                '0': 'info',
                '1': 'warning',
                '2': 'primary',
                '3': 'success',
                '4': 'success'
            };
            return typeMap[status] || 'info';
        },

        // 导出 - 优化用户体验
        async handleExport() {
            try {
                await ElMessageBox.confirm('是否导出全部数据？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                // 显示导出进度
                const loadingInstance = ElLoading.service({
                    text: '正在导出数据，请稍候...',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                try {
                    // 构建导出参数，使用当前的查询条件
                    const exportParams = this.buildExportParams();

                    // 调用导出API
                    const res = await thirdPartyHandoverApi.exportThirdPartyRecord(exportParams, {}, true, 'blob');

                    downloadNoData(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '三方交接记录.xlsx');
                    ElMessage.success('导出成功');
                } catch (error) {
                    console.error('导出失败:', error);
                    ElMessage.error('导出失败，请稍后重试');
                } finally {
                    loadingInstance.close();
                }
            } catch {
                // 用户取消导出
            }
        },

        /**
         * 构建导出参数
         */
        buildExportParams() {
            return {
                filename: '三方交接记录.xlsx',
                incubatorNo: this.queryParams.incubatorNo,
                orderNo: this.queryParams.orderNo,
                ownerName: this.queryParams.ownerName,
                status: this.queryParams.status,
                thirdType: this.queryParams.thirdType,
                thirdWaybillNo: this.queryParams.thirdWaybillNo,
                transOrderNo: this.queryParams.transOrderNo
            };
        },

        // 保温箱搜索相关方法
        handleIncubatorCascaderVisible(visible) {
            if (visible && this.regionOptions.length === 0) {
                this.loadRegionOptions();
            }
        },

        handleIncubatorRegionChange(value) {
            console.log('保温箱搜索区域变化:', value);
            // 当级联选择器值变化时，可以实时显示选择的地区信息
            if (value && value.length > 0) {
                // 处理checkStrictly模式下的值格式
                let regionPath = value;
                if (typeof value === 'object' && value.pathValues) {
                    regionPath = value.pathValues;
                } else if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].pathValues) {
                    regionPath = value[0].pathValues;
                }

                const regionInfo = {
                    provinceId: regionPath[0] || null,
                    cityId: regionPath[1] || null,
                    countyId: regionPath[2] || null,
                    townId: regionPath[3] || null
                };
                console.log('保温箱搜索地址ID参数:', regionInfo);
            }
        },

        // 保温箱选择变化
        handleIncubatorSelectionChange(selection) {
            this.tempSelectedIncubators = selection;
        },

        // 表格选择
        handleSelectionChange(selection) {
            this.selectedRows = selection;
        },

        // 交接三方快递
        handleThirdPartyHandover() {
            this.handoverDrawerVisible = true;
        },

        // 运单搜索相关方法
        handleWaybillCascaderVisible(visible) {
            if (visible && this.regionOptions.length === 0) {
                this.loadRegionOptions();
            }
        },

        handleWaybillRegionChange(value) {
            console.log('运单搜索区域变化:', value);
            // 当级联选择器值变化时，可以实时显示选择的地区信息
            if (value && value.length > 0) {
                // 处理checkStrictly模式下的值格式
                let regionPath = value;
                if (typeof value === 'object' && value.pathValues) {
                    regionPath = value.pathValues;
                } else if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && value[0].pathValues) {
                    regionPath = value[0].pathValues;
                }

                const regionInfo = {
                    provinceId: regionPath[0] || null,
                    cityId: regionPath[1] || null,
                    countyId: regionPath[2] || null,
                    townId: regionPath[3] || null
                };
                console.log('运单搜索地址ID参数:', regionInfo);
            }
        },

        // 运单选择变化
        handleWaybillSelectionChange(selection) {
            this.tempSelectedWaybills = selection;
        },

        // 懒加载省市区数据 - 添加缓存
        loadRegionOptions() {
            if (this.cachedRegionOptions) {
                this.regionOptions = this.cachedRegionOptions;
                return;
            }

            // 使用全局的省市区数据
            this.regionOptions = this.getSysAreas || [];
            this.cachedRegionOptions = this.regionOptions;
        },

        // 打开保温箱选择器
        openIncubatorSelector() {
            this.incubatorSelectorVisible = true;
            this.tempSelectedIncubators = []; // 先清空，等数据同步后再设置

            // 重置搜索条件，确保显示全部保温箱，便于查看已选中状态
            this.incubatorSearchRegion = [];

            this.getIncubatorList().then(() => {
                // 数据加载完成后设置表格选中状态
                this.$nextTick(() => {
                    this.setIncubatorTableSelection();
                });
            });
        },

        // 打开运单选择器
        openWaybillSelector() {
            this.waybillSelectorVisible = true;
            this.tempSelectedWaybills = []; // 先清空，等数据同步后再设置

            // 重置搜索条件，确保显示全部运单，便于查看已选中状态
            this.waybillSearchRegion = [];

            this.getWaybillList().then(() => {
                // 数据加载完成后设置表格选中状态
                this.$nextTick(() => {
                    this.setWaybillTableSelection();
                });
            });
        },

        // 打印记录
        printRecord(row) {
            ElMessage.success(`打印记录: ${row.thirdPartyOrderNo}`);
        },

        // 获取面单或打印箱签 - 优化用户体验
        async onClickFile(row) {
            // 如果已经获取了面单，则进行打印操作
            if (row.isGetWaybill && row.waybillUrl) {
                this.printWaybill(row);
                return;
            }

            // 显示加载状态
            const loadingInstance = ElLoading.service({
                text: `正在获取${this.getThreePartyWaybill(row)}面单...`,
                background: 'rgba(0, 0, 0, 0.7)'
            });

            try {
                // 调用获取运单附件接口
                const response = await thirdPartyHandoverApi.getWaybillAttachment(row.id);

                if (response.code === 200) {
                    ElMessage.success(`获取${this.getThreePartyWaybill(row)}面单成功，2s后刷新列表...`);
                    // 获取成功后延迟2秒刷新列表数据，确保后端数据已更新
                    setTimeout(async () => {
                        await this.getList();
                    }, 2000);
                } else {
                    ElMessage.error('获取面单数据失败：' + (response.message || '未知错误'));
                }
            } catch (error) {
                console.error('获取面单数据失败:', error);
                ElMessage.error('获取面单数据失败，请稍后重试');
            } finally {
                loadingInstance.close();
            }
        },

        // 打印面单
        async printWaybill(row) {
            ElMessage.success(`正在打印${this.getThreePartyWaybill(row)}箱签: ${row.thirdWaybillNo}`);

            try {
                // 处理面单打印数据
                await this.handlePrintData(row.waybillUrl, row);
            } catch (error) {
                console.error('打印面单失败:', error);
                ElMessage.error('打印面单失败，请稍后重试');
            }
        },
        // 处理打印数据 - 通过下载软件下载PDF
        async handlePrintData(data, row) {
            try {
                // 检查是否有waybillUrl
                if (!row.waybillUrl) {
                    ElMessage.error('打印失败：未找到面单URL');
                    return;
                }

                // 处理多个PDF URL，用逗号分隔
                let fileList = [];
                if (row.waybillUrl.includes(',')) {
                    fileList = row.waybillUrl.split(',').map(url => url.trim()).filter(url => url);
                } else {
                    fileList = [row.waybillUrl.trim()];
                }

                if (fileList.length === 0) {
                    ElMessage.error('打印失败：面单URL为空');
                    return;
                }

                // 下载每个PDF文件
                for (let i = 0; i < fileList.length; i++) {
                    const fileUrl = fileList[i];
                    try {
                        await this.downloadPDF(fileUrl, row, i + 1, fileList.length);
                        // 添加延迟，避免同时下载多个文件时浏览器阻止
                        if (i < fileList.length - 1) {
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    } catch (error) {
                        console.error(`下载第${i + 1}个PDF失败:`, error);
                        ElMessage.error(`下载第${i + 1}个PDF失败`);
                    }
                }

                ElMessage.success(`${this.getThreePartyWaybill(row)}箱签下载成功: ${row.thirdWaybillNo}`);

                // 下载成功后更新打印状态
                await this.updatePrintStatus(row);

            } catch (error) {
                console.error('处理打印数据失败:', error);
                ElMessage.error('打印失败，请稍后重试');
            }
        },

        /**
         * 下载PDF文件
         */
        async downloadPDF(fileUrl, row, index, total) {
            try {
                // 生成文件名
                let fileName = `${row.thirdWaybillNo || 'waybill'}_${index}`;
                if (total > 1) {
                    fileName += `_${index}of${total}`;
                }
                fileName += '.pdf';

                // 尝试通过fetch获取文件并下载
                try {
                    const response = await fetch(fileUrl);
                    if (response.ok) {
                        const blob = await response.blob();

                        // 创建下载链接
                        const link = document.createElement('a');
                        const url = window.URL.createObjectURL(blob);
                        link.href = url;
                        link.download = fileName;
                        link.style.display = 'none';

                        // 添加到DOM并触发下载
                        document.body.appendChild(link);
                        link.click();

                        // 清理DOM和URL对象
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);

                        console.log(`PDF下载已触发: ${fileName}`);
                        return;
                    }
                } catch (fetchError) {
                    console.warn('Fetch下载失败，使用新标签页打开:', fetchError);
                }

                // 如果fetch失败或不支持，在新标签页中打开PDF
                window.open(fileUrl, '_blank');
                console.log(`PDF已在新标签页打开: ${fileName}`);

            } catch (error) {
                console.error('下载PDF失败:', error);
                // 最终回退方案：在新标签页中打开
                window.open(fileUrl, '_blank');
                throw error;
            }
        },

        /**
         * 处理Blob数据
         */
        handleBlobData(data) {
            const binaryData = [data];
            const pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
            window.open(pdfUrl);
            // 延迟释放URL，确保文件能正常打开
            setTimeout(() => URL.revokeObjectURL(pdfUrl), 1000);
        },

        /**
         * 处理通用数据
         */
        handleGenericData(data) {
            const blob = new Blob([data], { type: 'application/pdf' });
            const pdfUrl = window.URL.createObjectURL(blob);
            window.open(pdfUrl);
            // 延迟释放URL，确保文件能正常打开
            setTimeout(() => URL.revokeObjectURL(pdfUrl), 1000);
        },

        /**
         * 统一错误处理
         */
        handleError(error, message = '操作失败') {
            console.error(message + ':', error);

            // 根据错误类型显示不同的提示
            if (error.response) {
                // 服务器响应错误
                const status = error.response.status;
                const errorMessage = error.response.data?.message || error.message;

                switch (status) {
                    case 401:
                        ElMessage.error('登录已过期，请重新登录');
                        break;
                    case 403:
                        ElMessage.error('权限不足，无法执行此操作');
                        break;
                    case 404:
                        ElMessage.error('请求的资源不存在');
                        break;
                    case 500:
                        ElMessage.error('服务器内部错误，请稍后重试');
                        break;
                    default:
                        ElMessage.error(errorMessage || `${message}，请稍后重试`);
                }
            } else if (error.request) {
                // 网络错误
                ElMessage.error('网络连接失败，请检查网络设置');
            } else {
                // 其他错误
                ElMessage.error(error.message || `${message}，请稍后重试`);
            }
        },

        /**
         * 更新打印状态
         */
        async updatePrintStatus(row) {
            try {
                const response = await thirdPartyHandoverApi.updatePrintStatus(row.id);
                if (response.code === 200) {
                    console.log(`打印状态更新成功: ${row.thirdWaybillNo}`);
                    // 更新本地数据状态，避免重新请求列表
                    row.isPrint = true;
                } else {
                    console.error('更新打印状态失败:', response.message);
                    ElMessage.warning('打印成功，但更新打印状态失败：' + (response.message || '未知错误'));
                }
            } catch (error) {
                console.error('更新打印状态失败:', error);
                ElMessage.warning('打印成功，但更新打印状态失败，请稍后重试');
            }
        },

        /**
         * 安全执行异步操作
         */
        async safeExecute(asyncFn, errorMessage = '操作失败') {
            try {
                return await asyncFn();
            } catch (error) {
                this.handleError(error, errorMessage);
                throw error; // 重新抛出错误，让调用者决定如何处理
            }
        },

        // 重新下单
        reOrder(row) {
            ElMessageBox.confirm(
                `确认要重新下单${row.thirdWaybillNo ? ' ' + row.thirdWaybillNo : ''}吗？`,
                '重新下单确认',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    // 开启全局 loading
                    const loadingInstance = ElLoading.service({
                        text: '正在重新下单...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });

                    try {
                        const response = await thirdPartyHandoverApi.reorderThirdPartyHandover({
                            id: row.id,
                            thirdType: row.thirdType
                        });
                        if (response.code === 200) {
                            ElMessage.success('重新下单成功');
                            this.getList();
                        } else {
                            ElMessage.error(response.message || '重新下单失败');
                        }
                    } catch (error) {
                        console.error('重新下单失败:', error);
                        ElMessage.error('重新下单失败，请稍后重试');
                    } finally {
                        // 关闭全局 loading
                        loadingInstance.close();
                    }
                });
        },

        // 设置保温箱表格选中状态 - 优化性能
        setIncubatorTableSelection() {
            // 使用 nextTick 确保 DOM 更新完成
            this.$nextTick(() => {
                if (!this.$refs.incubatorTable) return;

                // 清除所有选中状态
                this.$refs.incubatorTable.clearSelection();

                // 以最新数据为准，过滤掉不存在的已选项目
                const validSelectedIncubators = this.filterValidSelectedItems(
                    this.selectedIncubators,
                    this.availableIncubators,
                    'incubatorNo'
                );

                // 如果过滤后的数量与原来不同，说明有数据被删除，需要更新已选列表
                if (validSelectedIncubators.length !== this.selectedIncubators.length) {
                    const removedCount = this.selectedIncubators.length - validSelectedIncubators.length;
                    this.selectedIncubators = validSelectedIncubators;
                    if (removedCount > 0) {
                        ElMessage.warning(`有 ${removedCount} 个保温箱数据已不存在，已自动移除`);
                    }
                }

                // 更新临时选择列表
                this.tempSelectedIncubators = [...validSelectedIncubators];

                // 批量设置选中状态，减少DOM操作
                this.batchSetTableSelection(this.$refs.incubatorTable, this.availableIncubators, validSelectedIncubators, 'incubatorNo');
            });
        },

        // 设置运单表格选中状态 - 优化性能
        setWaybillTableSelection() {
            // 使用 nextTick 确保 DOM 更新完成
            this.$nextTick(() => {
                if (!this.$refs.waybillTable) return;

                // 清除所有选中状态
                this.$refs.waybillTable.clearSelection();

                // 以最新数据为准，过滤掉不存在的已选项目
                const validSelectedWaybills = this.filterValidSelectedItems(
                    this.selectedWaybills,
                    this.availableWaybills,
                    'waybillNo'
                );

                // 如果过滤后的数量与原来不同，说明有数据被删除，需要更新已选列表
                if (validSelectedWaybills.length !== this.selectedWaybills.length) {
                    const removedCount = this.selectedWaybills.length - validSelectedWaybills.length;
                    this.selectedWaybills = validSelectedWaybills;
                    if (removedCount > 0) {
                        ElMessage.warning(`有 ${removedCount} 个运单数据已不存在，已自动移除`);
                    }
                }

                // 更新临时选择列表
                this.tempSelectedWaybills = [...validSelectedWaybills];

                // 批量设置选中状态，减少DOM操作
                this.batchSetTableSelection(this.$refs.waybillTable, this.availableWaybills, validSelectedWaybills, 'waybillNo');
            });
        },

        /**
         * 过滤有效的已选项目 - 提取公共逻辑
         */
        filterValidSelectedItems(selectedItems, availableItems, keyField) {
            return selectedItems.filter(selected =>
                availableItems.some(available => available[keyField] === selected[keyField])
            );
        },

        /**
         * 批量设置表格选中状态 - 减少DOM操作
         */
        batchSetTableSelection(tableRef, availableItems, selectedItems, keyField) {
            const selectedKeys = new Set(selectedItems.map(item => item[keyField]));

            availableItems.forEach(row => {
                if (selectedKeys.has(row[keyField])) {
                    tableRef.toggleRowSelection(row, true);
                }
            });
        },

        // 移除保温箱
        removeIncubator(row) {
            const index = this.selectedIncubators.findIndex(item => item.incubatorNo === row.incubatorNo);
            if (index > -1) {
                this.selectedIncubators.splice(index, 1);
                ElMessage.success('已移除保温箱');
            }
        },

        // 移除运单
        removeWaybill(row) {
            const index = this.selectedWaybills.findIndex(item => item.waybillNo === row.waybillNo);
            if (index > -1) {
                this.selectedWaybills.splice(index, 1);
                ElMessage.success('已移除运单');
            }
        },

        resetIncubatorSearch() {
            this.incubatorSearchRegion = [];
            this.getIncubatorList(); // 重新加载保温箱列表
        },

        // 重置搜索
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.queryParams = {
                current: 1,
                size: 10,
                thirdWaybillNo: '', // 三方运单号
                thirdType: '', // 三方物流类型
                status: '', // 状态
                orderNo: '', // 订单号
                transOrderNo: '', // 运单号
                incubatorNo: '', // 保温箱号
                ownerName: '' // 货主名称
            };
            this.getList();
        },

        resetWaybillSearch() {
            this.waybillSearchRegion = [];
            this.getWaybillList(); // 重新加载运单列表
        },

        searchIncubators() {
            this.debouncedIncubatorSearch();
        },

        /**
         * 执行保温箱搜索 - 防抖处理
         */
        performIncubatorSearch() {
            console.log('搜索保温箱:', this.incubatorSearchRegion);
            this.getIncubatorList(); // 根据搜索条件获取保温箱列表
        },

        searchWaybills() {
            this.debouncedWaybillSearch();
        },

        /**
         * 执行运单搜索 - 防抖处理
         */
        performWaybillSearch() {
            console.log('搜索运单:', this.waybillSearchRegion);
            this.getWaybillList(); // 根据搜索条件获取运单列表
        },

        // 全选
        selectAll(selection) {
            this.selectedRows = selection;
        },

        // 设置物流地址
        setLogisticsAddress(row) {
            // 检查是否有多个详细地址
            if (!row.receiveAddressDetail || row.receiveAddressDetail.length <= 1) {
                ElMessage.warning('该保温箱只有一个或没有详细地址，无需设置');
                return;
            }

            // 打开地址选择弹出框
            this.currentIncubatorForAddress = row;
            this.selectedLogisticsAddress = row.selectedLogisticsAddress || ''; // 如果之前已选择过，显示之前的选择
            this.addressSelectionError = false;
            this.addressSelectionErrorMessage = '';
            this.addressPopoverVisible = true;
        },

        // 关闭地址选择弹出框
        closeAddressPopover() {
            this.addressPopoverVisible = false;
            this.currentIncubatorForAddress = null;
            this.selectedLogisticsAddress = '';
            this.addressSelectionError = false;
            this.addressSelectionErrorMessage = '';
        },

        // 地址选择变化时的处理（自动选择）
        onAddressSelectionChange(selectedAddress) {
            if (!selectedAddress || !this.currentIncubatorForAddress) {
                return;
            }

            // 更新保温箱的选中地址
            const incubatorIndex = this.selectedIncubators.findIndex(
                item => item.incubatorRecordId === this.currentIncubatorForAddress.incubatorRecordId
            );

            if (incubatorIndex !== -1) {
                // 直接更新对象属性，Vue 3会自动检测变化
                this.selectedIncubators[incubatorIndex].selectedLogisticsAddress = selectedAddress;
            }

            ElMessage.success('物流收件地址设置成功');
            // 自动关闭弹出框
            this.closeAddressPopover();
        },

        // 确认地址选择（保留用于兼容性，但现在使用自动选择）
        confirmAddressSelection() {
            this.onAddressSelectionChange(this.selectedLogisticsAddress);
        },

        // 展开/收起搜索
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },

        // 校验表单
        validateForm() {
            let isValid = true;

            // 校验三方快递
            if (!this.selectedThirdParty) {
                this.thirdPartyError = true;
                this.thirdPartyErrorMessage = '请选择三方快递';
                isValid = false;
            }

            // 校验保温箱或运单
            if (this.selectedIncubators.length === 0 && this.selectedWaybills.length === 0) {
                ElMessage.warning('请选择保温箱或运单');
                isValid = false;
            }

            // 校验保温箱的物流地址设置
            for (const incubator of this.selectedIncubators) {
                // 检查是否有多个地址需要设置物流地址
                const hasMultipleAddresses = (incubator.addressList && incubator.addressList.length > 1) ||
                    (incubator.receiveAddressDetail && incubator.receiveAddressDetail.length > 1);

                if (hasMultipleAddresses && !incubator.selectedLogisticsAddress) {
                    ElMessage.warning(`保温箱【${incubator.incubatorNo}】有多个详细地址，请先设置物流收件地址`);
                    isValid = false;
                    break; // 只显示第一个错误，避免多个弹窗
                }
            }

            return isValid;
        },

        // 构建交接请求参数
        buildHandoverRequest() {
            // 收集运单号
            const transOrderNos = [];

            // 从选中的运单中收集运单号
            this.selectedWaybills.forEach(waybill => {
                if (waybill.transOrderNo) {
                    transOrderNos.push(waybill.transOrderNo);
                }
            });

            // 构建保温箱信息
            const incubatorInfos = this.selectedIncubators.map(incubator => {
                // 确定最终的收件地址和省市区镇信息
                let finalReceiveAddress = '';
                let finalReceiveProvince = '';
                let finalReceiveCity = '';
                let finalReceiveCounty = '';
                let finalReceiveTown = '';

                if (incubator.selectedLogisticsAddress) {
                    // 如果用户选择了特定的物流地址，需要从 addressList 中找到对应的完整地址信息
                    const selectedAddressItem = incubator.addressList?.find(addr => {
                        const fullAddress = this.buildAddressStringFromAddressItem(addr) + (addr.receiveAddress || '');
                        return fullAddress === incubator.selectedLogisticsAddress;
                    });

                    if (selectedAddressItem) {
                        finalReceiveProvince = selectedAddressItem.receiveProvince || '';
                        finalReceiveCity = selectedAddressItem.receiveCity || '';
                        finalReceiveCounty = selectedAddressItem.receiveCounty || '';
                        finalReceiveTown = selectedAddressItem.receiveTown || '';
                        finalReceiveAddress = selectedAddressItem.receiveAddress || '';
                    } else {
                        // 如果找不到对应的地址项，使用选择的地址作为详细地址
                        finalReceiveAddress = incubator.selectedLogisticsAddress;
                    }
                } else if (incubator.addressList && incubator.addressList.length === 1) {
                    // 如果只有一个地址，直接使用该地址的信息
                    const addressItem = incubator.addressList[0];
                    finalReceiveProvince = addressItem.receiveProvince || '';
                    finalReceiveCity = addressItem.receiveCity || '';
                    finalReceiveCounty = addressItem.receiveCounty || '';
                    finalReceiveTown = addressItem.receiveTown || '';
                    finalReceiveAddress = addressItem.receiveAddress || '';
                } else {
                    // 兼容旧数据结构或作为后备方案
                    finalReceiveProvince = incubator.receiveProvince || '';
                    finalReceiveCity = incubator.receiveCity || '';
                    finalReceiveCounty = incubator.receiveCounty || '';
                    finalReceiveTown = incubator.receiveTown || '';
                    finalReceiveAddress = incubator.originalReceiveAddress || '';
                }

                return {
                    incubatorRecordId: incubator.incubatorRecordId,
                    receiveProvince: finalReceiveProvince,
                    receiveCity: finalReceiveCity,
                    receiveCounty: finalReceiveCounty,
                    receiveTown: finalReceiveTown,
                    receiveAddress: finalReceiveAddress
                };
            });

            // 构建请求体
            const requestData = {
                thirdType: parseInt(this.selectedThirdParty),
                transOrderNos: transOrderNos,
                incubatorInfos: incubatorInfos
            };

            console.log('交接请求参数:', requestData);
            return requestData;
        },

        // 查看详情
        viewDetails(row) {
            ElMessage.info(`查看详情: ${row.thirdPartyOrderNo}`);
        },



        // 查看操作日志
        viewOperationLog(row) {
            ElMessage.info(`查看操作日志: ${row.thirdWaybillNo}`);
            // 这里应该打开操作日志弹窗或跳转到日志页面
        },

        /**
         * 检查保温箱是否有多个地址
         */
        hasMultipleAddresses(incubator) {
            // 优先检查新的 addressList 结构
            if (incubator.addressList && Array.isArray(incubator.addressList)) {
                return incubator.addressList.length > 1;
            }
            // 兼容旧的 receiveAddressDetail 结构
            if (incubator.receiveAddressDetail && Array.isArray(incubator.receiveAddressDetail)) {
                return incubator.receiveAddressDetail.length > 1;
            }
            return false;
        },

        /**
         * 获取单一地址状态文本
         */
        getSingleAddressStatus(incubator) {
            // 检查新的 addressList 结构
            if (incubator.addressList && Array.isArray(incubator.addressList)) {
                if (incubator.addressList.length === 1) {
                    return '单一地址';
                } else if (incubator.addressList.length === 0) {
                    return '无地址';
                }
            }
            // 兼容旧的 receiveAddressDetail 结构
            if (incubator.receiveAddressDetail && Array.isArray(incubator.receiveAddressDetail)) {
                if (incubator.receiveAddressDetail.length === 1) {
                    return '单一地址';
                } else if (incubator.receiveAddressDetail.length === 0) {
                    return '无地址';
                }
            }
            return '无地址';
        },

        /**
         * 获取多地址的汇总信息（用于保温箱选择器）
         */
        getAddressSummary(incubator) {
            if (incubator.addressList && Array.isArray(incubator.addressList) && incubator.addressList.length > 1) {
                // 提取所有不同的省份
                const provinces = [...new Set(incubator.addressList.map(addr => addr.receiveProvince).filter(Boolean))];
                // 提取所有不同的城市
                const cities = [...new Set(incubator.addressList.map(addr => addr.receiveCity).filter(Boolean))];

                if (provinces.length === 1 && cities.length === 1) {
                    // 同一省市，显示省市 + 多地址标识
                    return `${provinces[0]}${cities[0]} (${incubator.addressList.length}个地址)`;
                } else if (provinces.length === 1) {
                    // 同一省份，不同城市
                    return `${provinces[0]} (${incubator.addressList.length}个地址)`;
                } else {
                    // 跨省份
                    return `多省市 (${incubator.addressList.length}个地址)`;
                }
            }

            // 兼容旧数据结构
            if (incubator.receiveAddressDetail && Array.isArray(incubator.receiveAddressDetail) && incubator.receiveAddressDetail.length > 1) {
                return `多地址 (${incubator.receiveAddressDetail.length}个)`;
            }

            return '多地址';
        },

        /**
         * 获取选中地址的省市区镇信息
         */
        getSelectedAddressRegion(incubator) {
            if (!incubator.selectedLogisticsAddress || !incubator.addressList) {
                return '';
            }

            // 从 addressList 中找到对应的地址项
            const selectedAddressItem = incubator.addressList.find(addr => {
                const fullAddress = this.buildAddressStringFromAddressItem(addr) + (addr.receiveAddress || '');
                return fullAddress === incubator.selectedLogisticsAddress;
            });

            if (selectedAddressItem) {
                return this.buildAddressStringFromAddressItem(selectedAddressItem);
            }

            return '';
        },

        /**
         * 获取选中地址的详细地址信息
         */
        getSelectedAddressDetail(incubator) {
            if (!incubator.selectedLogisticsAddress || !incubator.addressList) {
                return '';
            }

            // 从 addressList 中找到对应的地址项
            const selectedAddressItem = incubator.addressList.find(addr => {
                const fullAddress = this.buildAddressStringFromAddressItem(addr) + (addr.receiveAddress || '');
                return fullAddress === incubator.selectedLogisticsAddress;
            });

            if (selectedAddressItem) {
                return selectedAddressItem.receiveAddress || '';
            }

            return incubator.selectedLogisticsAddress;
        },

        /**
         * 获取地址表格数据
         */
        getAddressTableData(incubator) {
            if (!incubator.addressList || !Array.isArray(incubator.addressList)) {
                // 兼容旧数据结构
                if (incubator.receiveAddressDetail && Array.isArray(incubator.receiveAddressDetail)) {
                    return incubator.receiveAddressDetail.map(address => ({
                        fullAddress: address,
                        regionAddress: '', // 旧数据结构无法分离省市区镇
                        detailAddress: address
                    }));
                }
                return [];
            }

            return incubator.addressList.map(addr => ({
                fullAddress: this.buildAddressStringFromAddressItem(addr) + (addr.receiveAddress || ''),
                regionAddress: this.buildAddressStringFromAddressItem(addr),
                detailAddress: addr.receiveAddress || ''
            }));
        },

        /**
         * 处理表格行点击事件
         */
        onAddressRowClick(row) {
            this.selectedLogisticsAddress = row.fullAddress;
            this.onAddressSelectionChange(row.fullAddress);
        }
    }
};
</script>

<style scoped>
/* 抽屉头部样式 */
:deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
}

/* 抽屉内容样式 */
:deep(.el-drawer__body) {
    padding: 0;
}

/* 表格样式优化 */
.el-table {
    border: 1px solid #ebeef5;
}

.el-table th {
    background-color: #f5f7fa;
}

/* 表单校验错误样式 */
.is-error .el-input__wrapper {
    border-color: #f56c6c !important;
    box-shadow: 0 0 0 1px #f56c6c inset !important;
}

.is-error .el-select .el-input__wrapper {
    border-color: #f56c6c !important;
    box-shadow: 0 0 0 1px #f56c6c inset !important;
}

.is-error .el-select .el-input__wrapper:hover {
    border-color: #f56c6c !important;
}

.is-error .el-select .el-input__wrapper.is-focus {
    border-color: #f56c6c !important;
    box-shadow: 0 0 0 1px #f56c6c inset !important;
}

/* 表格容器样式 */
.table-container {
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* 表格标题区域 */
.table-header {
    flex-shrink: 0;
    margin-bottom: 15px;
}

/* 表格内容区域 */
.table-content {
    flex: 1;
    min-height: 0;
}

/* 单个表格时占满剩余空间 */
.single-table {
    flex: 1;
}

/* 两个表格都存在时的默认分配 */
.has-both-tables {
    flex: 1;
}

/* 数据量少的表格 */
.has-both-tables.small-data {
    flex: 0 0 auto;
    max-height: 240px;
    /* 约6行数据 */
}

/* 数据量多的表格 */
.has-both-tables.large-data {
    flex: 1;
    min-height: 0;
}

/* 两个表格都有数据但都不是特别多或特别少时，平均分配 */
.has-both-tables:not(.small-data):not(.large-data) {
    flex: 1;
}

/* 保温箱表格的底部间距 */
.table-container:not(:last-child) {
    margin-bottom: 20px;
}

/* 加载状态下的按钮样式优化 */
.el-button.is-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 加载状态下的选择器样式优化 */
.el-select.is-disabled .el-input__wrapper,
.el-cascader.is-disabled .el-input__wrapper {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
}

/* 加载状态提示文字样式 */
.loading-text {
    color: #909399;
    font-size: 12px;
    margin-left: 8px;
}

/* 地址选择表格样式 */
:deep(.el-popover .el-table) {
    border: 1px solid #ebeef5;
}

:deep(.el-popover .el-table th) {
    background-color: #f5f7fa;
    font-weight: 600;
}

:deep(.el-popover .el-table td) {
    padding: 8px 12px;
}

:deep(.el-popover .el-table .el-radio) {
    margin-right: 0;
}

:deep(.el-popover .el-table .el-radio__input) {
    margin-right: 0;
}

/* 地址选择表格行悬停效果 */
:deep(.el-popover .el-table tbody tr:hover) {
    background-color: #f5f7fa;
    cursor: pointer;
}
</style>
