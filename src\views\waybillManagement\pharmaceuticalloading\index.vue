<template>
    <div>
        <div class="app-container">
            <!-- s 搜索项-->
            <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <el-form ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form" @submit.prevent>
                    <el-form-item label="司机姓名" prop="driverCode" style="width: 250px">
                        <el-select v-model="queryParams.driverCode" clearable filterable placeholder="请选择司机姓名" @change="handleQuery">
                            <el-option v-for="(dict, index) in driverList" :key="index" :label="dict.driverName" :value="dict.driverCode" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="司机电话" prop="driverPhone" style="width: 200px">
                        <el-input v-model="queryParams.driverPhone" clearable placeholder="请输入司机电话" @keyup.enter="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAll" label="装车交接状态" prop="status">
                        <el-select v-model="queryParams.status" clearable placeholder="请选择装车交接状态" @change="handleQuery">
                            <el-option v-for="dict in loadingStatusList" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShowAll" label="发货方式" prop="orderWay">
                        <el-select v-model="queryParams.orderWay" clearable placeholder="请选择发货方式" @change="handleQuery">
                            <el-option v-for="dict in loadingShippingMethodList" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShowAll" label="配送方式" prop="receiptWay">
                        <el-select v-model="queryParams.receiptWay" clearable placeholder="请选择配送方式" @change="handleQuery">
                            <el-option v-for="dict in loadingDeliveryMethodList" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="装车时间" prop="queryTime" style="width: 305px">
                        <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束时间" range-separator="至" start-placeholder="开始时间" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                    </el-form-item>
                    <search-button :is-show-all="isShowAll" @handleQuery="getList(1)" @resetQuery="resetQuery('queryForm')" @showAllClick="showAllClick" />
                </el-form>
            </el-card>

            <el-card :body-style="{ padding: '10px' }" shadow="never">
                <div class="mb10" style="display: flex">
                    <el-button icon="el-icon-plus" type="primary" @click="operateLoading">装车</el-button>
                    <el-button icon="el-icon-download" type="warning" @click="handleExport">导出</el-button>
                    <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" style="margin-left: auto" table-i-d="Pharmaceuticalloading" @queryTable="getList"></right-toolbar>
                </div>

                <column-table key="Pharmaceuticalloading" ref="ColumnTable" v-loading="loading" :columns="columns" :data="alList" :defaultSort="{ prop: 'createDate', order: 'descending' }" :show-check-box="false" @select="handleSelectionChange" @select-all="selectAll">
                    <template #driver="{ row }">
                        <span>{{ row.driver.driverName }}</span>
                    </template>
                    <template #carClass="{ row }">
                        <span>{{ row.carClass == '0' ? '普通货车' : '冷藏车' }}</span>
                    </template>
                    <template #createBy="{ row }">
                        <span>{{ row.createBy.name }}</span>
                    </template>
                    <template #receiptWay="{ row }">
                        <span>{{ formDict(loadingDeliveryMethodList, row.receiptWay) }}</span>
                    </template>
                    <template #orderWay="{ row }">
                        <span>{{ formDict(loadingShippingMethodList, row.orderWay) }}</span>
                    </template>
                    <template #status="{ row }">
                        <el-button v-if="row.status == '0'" link size="small" type="danger" icon="el-icon-close" @click="cancelAnOrderClick(row)">撤销</el-button>
                        <el-button v-if="row.status == '0'" link size="small" type="primary" icon="el-icon-refresh" @click="continueLoading(row)">继续装车</el-button>
                        <el-button v-if="row.status == '1'" link size="small" title="确认发车" type="success" icon="el-icon-check" @click="confirmVehicle(row, 2)">确认发车</el-button>
                        <el-button v-if="row.status == '0'" link size="small" title="客户签收" type="warning" icon="el-icon-check" @click="confirmVehicle(row, 1)">完成装车</el-button>
                        <!-- <el-button v-if="row.status == '3'" link type='success' @click='dispatch(row)'>交接司机
					</el-button> -->
                        <el-button v-if="row.status == '1'" link size="small" type="danger" @click="fallbackModification(row)">回退修改</el-button>
                        <el-button link size="small" type="primary" icon="el-icon-info-filled" @click="viewLoadingDetails(row, '2')">装车详情</el-button>
                    </template>
                </column-table>
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
            </el-card>
        </div>
        <!-- 装车 -->
        <el-drawer v-model="vehicleOpen" :title="title" append-to-body size="60%" @close="closeLoading">
            <el-card class="box-card" shadow="never" style="margin: 0 10px 10px 10px">
                <div class="mb-2 flex items-center text-sm">
                    <el-radio-group v-model="radio">
                        <el-radio :label="1">公司自送</el-radio>
                        <el-radio :label="2">交接三方</el-radio>
                    </el-radio-group>
                </div>
            </el-card>
            <el-card class="box-card" shadow="never" style="margin: 0 10px 10px 10px">
                <el-form ref="formRef" :inline="true" :model="loadingList" :rules="driverRules" class="demo-form-inline" label-width="180px">
                    <el-form-item v-if="radio == 2" label="配送方式" prop="orderWay">
                        <el-radio-group v-model="loadingList.orderWay">
                            <el-radio label="1">三方上门取件</el-radio>
                            <el-radio label="2">送达三方网点</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="radio == 2" label="三方物流/承运公司" prop="flowCode">
                        <el-select v-model="loadingList.flowCode" clearable placeholder="三方物流/承运公司" style="width: 400px">
                            <el-option v-for="item in tripartiteLogisticsCompanyList" :key="item.id" :label="item.flowName" :value="item.flowCode" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="loadingList.orderWay == 2 || radio == 1" label="司机姓名" prop="driverName">
                        <el-select v-model="loadingList.driverName" clearable placeholder="请选择司机姓名" style="width: 400px" @change="driverInformation">
                            <el-option v-for="(item, index) in driverNameList" :key="index" :label="item.driverName" :value="item">
                                <span style="float: left; width: 120px">{{ item.driverName }}</span>
                                <span style="padding-left: 20px; color: var(--el-text-color-secondary); font-size: 13px">{{ item.carClass === '0' ? '普通货车' : '冷藏车' }}</span>
                                <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item.carCode }}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="loadingList.orderWay == 2 || radio == 1" label="司机电话" prop="driverPhone">
                        <el-input v-model="loadingList.driverPhone" clearable disabled placeholder="请输入司机电话" style="width: 400px" />
                    </el-form-item>
                    <el-form-item v-if="loadingList.orderWay == 2 || radio == 1" label="车牌号" prop="carCode">
                        <el-input v-model="loadingList.carCode" clearable disabled placeholder="请输入车牌号" style="width: 400px" />
                    </el-form-item>
                    <el-form-item v-if="loadingList.orderWay == 2 || radio == 1" label="车辆类型" prop="carClass">
                        <el-select v-model="loadingList.carClass" clearable disabled placeholder="清选择车辆类型" style="width: 400px">
                            <el-option v-for="item in fourplCarList" :key="item.code" :label="item.name" :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="loadingList.carClass == 1 && (loadingList.orderWay == 2 || radio == 1)" label="传感器" prop="deviceNo">
                        <el-input v-model="loadingList.deviceNo" disabled placeholder="请输入传感器编号" style="width: 400px"></el-input>
                    </el-form-item>
                </el-form>
            </el-card>
            <el-card class="box-card mb10" shadow="never" style="margin: 0 10px 10px 10px">
                <div>
                    <el-button type="primary" @click="selectStore()">选择集货区</el-button>
                    <el-button type="danger" @click="selectIncubator()">选择保温箱</el-button>
                </div>
            </el-card>
        </el-drawer>
        <!-- 继续装车 -->
        <el-dialog v-model="continueOpen" :title="title" append-to-body width="850px" @close="closeContinue">
            <el-card class="box-card mb10" shadow="never">
                <template #header>
                    <div class="card-header" style="color: #5670fe; display: flex; border-bottom: 1px solid #e6ebf5">
                        <div style="background-color: #5670fe; width: 5px; height: 20px; margin-right: 10px"></div>
                        <span>司机信息</span>
                    </div>
                </template>
                <el-descriptions :column="2">
                    <el-descriptions-item label="司机姓名：">{{ continueLoadingList.driver.driverName }} </el-descriptions-item>
                    <el-descriptions-item label="司机电话：">{{ continueLoadingList.driverPhone }}</el-descriptions-item>
                    <el-descriptions-item label="车牌号码：">{{ continueLoadingList.carCode }}</el-descriptions-item>
                    <el-descriptions-item label="车辆类型：">{{ continueLoadingList.carClass == 0 ? '普通货车' : '冷藏车' }} </el-descriptions-item>
                    <el-descriptions-item v-if="continueLoadingList.carClass === 1" label="传感器：">
                        {{ continueLoadingList.deviceNo }}
                    </el-descriptions-item>
                </el-descriptions>
            </el-card>
            <el-card v-if="!displayType" class="box-card mb10" shadow="never">
                <div>
                    <el-button type="success" @click="selectStore(1)">选择集货区</el-button>
                    <el-button type="danger" @click="selectIncubator(1)">选择保温箱</el-button>
                    <!--					<el-button type='primary' @click='dispatch(row)'>大客户</el-button>-->
                </div>
            </el-card>
            <el-card class="box-card mb10" shadow="never">
                <div style="width: 500px">
                    <el-collapse v-model="activeNames" @change="handleChange">
                        <el-collapse-item v-for="(item, index) in loadingRecordList.countyList" :key="item.id" @click="areaObtainWaybill(loadingRecordList, item, index)">
                            <template #title>
                                <div style="width: 450px; display: flex; justify-content: space-between; height: 30px; line-height: 30px">
                                    <div style="font-size: 14px; font-weight: 600">{{ item.provinceName }}{{ item.cityName }}{{ item.countyName }}</div>
                                    <div>( {{ item.transOrderNum }})单 {{ item.goodsTotalNum }}件</div>
                                </div>
                            </template>
                            <div v-for="(listItem, index1) in item.list" :key="index1" style="margin-left: 20px" @click.stop="areaObtainBoxCode(loadingRecordList, item, index, listItem, index1)">
                                <el-collapse-item :name="index1" title="Consistency">
                                    <template #title>
                                        <div style="width: 450px; display: flex; justify-content: space-between; height: 30px; line-height: 30px; padding-right: 20px">
                                            <div style="font-size: 14px; font-weight: 500">
                                                {{ listItem.transOrderNo }}
                                            </div>
                                            <div style="font-size: 14px; font-weight: 500">发件公司：{{ listItem.sendCompany }}</div>
                                            <div>{{ listItem.goodsTotalNum }}件</div>
                                        </div>
                                    </template>
                                    <div v-for="(trans, index3) in listItem.listTrans" :key="index3" style="margin-left: 20px; font-size: 12px; display: flex; justify-content: space-between; padding-right: 50px">
                                        <div>
                                            {{ trans.code }}
                                        </div>
                                        <div>
                                            {{ trans.codeDesc }}
                                        </div>
                                    </div>
                                </el-collapse-item>
                            </div>
                        </el-collapse-item>
                        <el-collapse-item v-for="(item, index) in loadingRecordList.incubatorList" :key="item.id">
                            <template #title>
                                <div style="width: 450px; display: flex; justify-content: space-between; height: 30px; line-height: 30px">
                                    <div style="font-size: 14px; font-weight: 600">{{ item.incubatorName }}</div>
                                    <div>( {{ item.transOrderNum }})单 {{ item.goodsTotalNum }}件</div>
                                </div>
                            </template>
                            <div v-for="(coun, index1) in item.countyList" :key="index1" style="margin-left: 20px" @click.stop="obtainWaybill(item, index, coun, index1)">
                                <el-collapse-item>
                                    <template #title>
                                        <div style="width: 450px; display: flex; justify-content: space-between; height: 30px; line-height: 30px">
                                            <div style="font-size: 14px; font-weight: 500">{{ coun.provinceName }}{{ coun.cityName }}{{ coun.countyName }}</div>
                                            <div>{{ coun.goodsTotalNum }}件</div>
                                        </div>
                                    </template>
                                    <div v-for="(listItem, index2) in coun.list" :key="index2" style="margin-left: 20px; font-size: 12px" @click.stop="obtainBoxCode(item, index, coun, index1, listItem, index2)">
                                        <el-collapse-item :name="index2">
                                            <template #title>
                                                <div style="width: 450px; display: flex; justify-content: space-between; height: 30px; line-height: 30px">
                                                    <div style="font-size: 14px; font-weight: 500">
                                                        {{ listItem.transOrderNo }}
                                                    </div>
                                                    <div style="font-size: 14px; font-weight: 500">发件公司：{{ listItem.sendCompany }}</div>
                                                    <div>{{ listItem.goodsTotalNum }}件</div>
                                                </div>
                                            </template>
                                            <div v-for="(trans, index3) in listItem.listTrans" :key="index3" style="margin-left: 20px; font-size: 12px; display: flex; justify-content: space-between">
                                                <div>
                                                    {{ trans.code }}
                                                </div>
                                                <div>
                                                    {{ trans.codeDesc }}
                                                </div>
                                            </div>
                                        </el-collapse-item>
                                    </div>
                                </el-collapse-item>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </el-card>
            <div class="dialog-footer">
                <!-- <el-button v-if="!displayType" type='primary' @click='changeDriver'>保存</el-button> -->
                <el-button
                    @click="
                        continueOpen = false;
                        getList();
                    "
                    >取 消
                </el-button>
            </div>
        </el-dialog>
        <!-- 集货区选择 -->
        <el-drawer v-model="storeOpen" :title="title" append-to-body size="60%">
            <el-card class="box-card" shadow="never" style="margin: 0 10px 10px 10px">
                <el-form ref="queryForm" :inline="true" :model="storeList" class="seache-form" label-width="80px" size="small" @submit.prevent>
                    <el-form-item label="集货区名称" prop="companyId">
                        <el-select v-model="storeList.companyId" clearable filterable placeholder="请选择集货区名称">
                            <el-option v-for="(dict, index) in storeGoodsList" :key="index" :label="dict.areaName" :value="dict.areaCode" />
                        </el-select>
                    </el-form-item>
                    <div>
                        <el-button type="primary" @click="collectionQuery">搜索</el-button>
                        <el-button @click="relocationCargoCollection">重置</el-button>
                    </div>
                </el-form>
            </el-card>
            <el-card class="box-card mb10" shadow="never" style="margin: 0 10px 10px 10px">
                <div style="display: flex">
                    <text style="height: 30px; line-height: 30px; font-size: 16px; font-weight: 400">集货区域</text>
                    <div style="margin-left: 100px">
                        <el-button plain @click="(storeOpen = false), (goodsList = []), (storeList.companyId = ''), renewGoodsType(1)"> 返回 </el-button>
                        <el-button plain type="primary" @click="completeLoading">确定</el-button>
                    </div>
                </div>
                <TreeGoods ref="renewGoods" :goodsList="goodsList" @func="getMsgFormSon"></TreeGoods>
            </el-card>
        </el-drawer>
        <!-- 保温箱选择 -->
        <el-drawer v-model="incubatorOpen" :title="title" append-to-body size="60%" @close="closeIncubator">
            <el-card class="box-card" shadow="never" style="margin: 0 10px 10px 10px">
                <el-form ref="queryForm" :inline="true" :model="addressList" class="seache-form" label-width="80px" size="small" @submit.prevent>
                    <el-form-item label="省市区选择" prop="shippingAddress">
                        <el-cascader v-model="addressList.shippingAddress" :options="options" clearable filterable placeholder="请选择省市区" style="width: 100%">
                            <template #default="{ node, data }">
                                <span>{{ data.label }}</span>
                            </template>
                        </el-cascader>
                    </el-form-item>
                    <div>
                        <el-button type="primary" @click="incubatorQuery">搜索</el-button>
                        <el-button @click="relocationCargoIncubator">重置</el-button>
                    </div>
                </el-form>
            </el-card>
            <el-card class="box-card mb10" shadow="never" style="margin: 0 10px 10px 10px">
                <div style="display: flex">
                    <text>保温箱</text>
                    <div style="margin-left: 100px">
                        <el-button plain @click="(incubatorOpen = false), (options = []), (addressList.shippingAddress = '')"> 返回 </el-button>
                        <el-button plain type="primary" @click="completeIncubator">确定</el-button>
                    </div>
                </div>
                <incubator :citiesList="cities" :sensorList="sensorList" @selectedIncubator="selectedIncubatorClick"></incubator>
            </el-card>
        </el-drawer>
        <!-- 撤销 -->
        <el-drawer v-model="revokeOpen" :title="title" size="40%" @close="closeRevokeOpen" @open="openRevokeOpen">
            <revoke ref="child" :continueLoadingList="continueLoadingList" @revocationRetuen="closeRevokeOpen"></revoke>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar';
import driverSelect from '@/components/driverSelect';
import TreeGoods from './tree-goods.vue';
import incubator from './incubator.vue';
import revoke from './revoke.vue';
import pharmaceuticalloading from '@/api/waybillManagement/pharmaceuticalloading';
import { Plus } from '@element-plus/icons-vue';
import moment from 'moment';
import { ElMessage, ElMessageBox } from 'element-plus';
import qs from 'qs';
import { createLogger } from 'vuex';

export default {
    name: 'Pharmaceuticalloading',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar,
        driverSelect,
        TreeGoods,
        incubator,
        Plus,
        revoke
    },
    data() {
        return {
            isShowAll: false,
            loadingStatusList: [],
            sysAreas: [],
            showSearch: true,
            vehicleOpen: false, //装车页面
            radio: 1,
            isSubmitting: false, // 防止重复提交标志
            // 司机
            loadingList: {
                driverPhone: '',
                carCode: '',
                carClass: '',
                deviceNo: '',
                orderWay: '2'
            },
            driverNameList: [], //司机列表
            driverRules: {
                driverName: [{ required: true, message: '请选择司机姓名', trigger: 'change' }],
                driverPhone: [
                    { required: true, message: '请输入司机电话', trigger: 'change' },
                    {
                        type: 'string',
                        pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/,
                        message: '请输入正确的电话',
                        trigger: 'blur'
                    }
                ],
                carCode: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
                carClass: [{ required: true, message: '清选择车辆类型', trigger: 'change' }],
                deviceNo: [{ required: true, message: '请输入传感器编号', trigger: 'blur' }],
                flowCode: [{ required: true, message: '请选择三方物流/承运公司', trigger: 'blur' }],
                orderWay: [{ required: true, message: '请选择配送方式', trigger: 'blur' }]
            },
            // 订单信息 数据
            form: {},
            addressList: {},
            // 搜索项
            queryParams: {
                current: 1,
                size: 10
            },
            total: 0, // 总条数
            columns: [
                { title: '司机姓名', key: 'driver', align: 'center', width: '180px', columnShow: true },
                { title: '司机电话', key: 'driverPhone', align: 'center', width: '120px', columnShow: true },
                {
                    title: '车牌号',
                    key: 'carCode',
                    align: 'center',
                    width: '170px',
                    columnShow: true,
                    showOverflowTooltip: true
                },
                { title: '车辆种类', key: 'carClass', align: 'center', width: '120px', columnShow: true },
                { title: '运单数量', key: 'transNum', align: 'center', columnShow: true },
                // { title: '普通件', key: 'goodsNum', width: '200px', align: 'center', columnShow: true, sortable: true },
                { title: '保温箱', key: 'incubatorNum', width: '120px', align: 'center', columnShow: true },
                { title: '总件数', key: 'goodsNum', width: '120px', align: 'center', columnShow: true },
                {
                    title: '装车时间',
                    key: 'assignTime',
                    width: '200px',
                    align: 'center',
                    columnShow: true,
                    showOverflowTooltip: true
                },
                { title: '配送方式', key: 'receiptWay', width: '120px', align: 'center', columnShow: true },
                { title: '公司名称', key: 'flowName', width: '120px', align: 'center', columnShow: true },
                {
                    title: '发货方式',
                    key: 'orderWay',
                    width: '200px',
                    align: 'center',
                    columnShow: true,
                    showOverflowTooltip: true
                },
                {
                    title: '操作人',
                    key: 'createBy',
                    width: '200px',
                    align: 'center',
                    columnShow: true,
                    showOverflowTooltip: true
                },
                {
                    title: '操作',
                    key: 'status',
                    align: 'center',
                    fixed: 'right',
                    width: '320px',
                    hideFilter: true,
                    columnShow: true
                }
            ],
            alList: [],
            continueOpen: false, //继续转装车弹窗
            title: '', //弹窗标题
            activeNames: '',
            storeOpen: false, //集货弹窗

            incubatorOpen: false, //保温箱
            data: [
                {
                    id: 1,
                    label: 'Level one 1',
                    children: []
                }
            ],
            fourplCarList: [], //车辆类型
            storeGoodsList: [], //集货区list
            storeList: {
                companyId: ''
            },
            storeDetails: [],
            goodsList: [],
            openTree: false,
            dressCar: [],
            dataList: [],
            sensorList: [], //保温箱列表
            continueLoadingList: [],
            provincialUrbanAreas: [],
            options: [],
            cities: [],
            selectedIncubatorList: [],
            displayType: false,
            loadingRecordList: [],
            revokeOpen: false,
            // 装车配送方式
            loadingDeliveryMethodList: [],
            // 装车发货方式
            loadingShippingMethodList: [],
            tripartiteLogisticsCompanyList: [], //三方物流
            driverList: []
        };
    },
    computed: {
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },
    created() {
        this.getList();
        this.queryDriver();
        this.dict();
        this.thirdLogistics();
        this.getDriverList();
    },

    methods: {
        handleQuery() {
            this.getList();
        },
        // 点击集货区运单获取数据
        areaObtainBoxCode(item, coun, index1, listItem, index2) {
            pharmaceuticalloading
                .getDetailGroupCode({
                    batchNo: item.batchNo,
                    countyId: coun.countyId,
                    transOrderNo: listItem.transOrderNo
                })
                .then((res) => {
                    if (res.code === 200) {
                        const data = res.data.map((item) => {
                            item.checked = false;
                            return item;
                        });
                        listItem.listTrans = data;
                    }
                });
        },
        // 点击集货区获取运单
        areaObtainWaybill(val, coun, index1) {
            console.log(111111111);
            pharmaceuticalloading
                .getDetailGroupOrder({
                    batchNo: val.batchNo,
                    countyId: coun.countyId
                })
                .then((res) => {
                    if (res.code === 200) {
                        const data = res.data.map((item) => {
                            item.checked = false;
                            return item;
                        });
                        coun.list = data;
                    }
                });
        },
        //撤销装车
        cancelAnOrderClick(val) {
            this.continueLoadingList = val;
            this.title = '撤销装车';
            this.revokeOpen = true;
        },
        closeContinue() {
            this.getList();
        },
        // 关闭保温箱时的回调
        closeIncubator() {
            this.options = [];
            this.incubatorOpen = false;
        },
        // 关闭装车
        closeLoading() {
            this.loadingOpen = false;
            this.loadingList = {
                driverPhone: '',
                carCode: '',
                carClass: '',
                deviceNo: ''
            };
        },
        // 关闭撤销
        closeRevokeOpen() {
            this.revokeOpen = false;
            this.continueLoadingList = [];
        },
        // g根据集货区查询货区
        collectionQuery() {
            console.log(this.storeList.companyId);
            if (this.storeList.companyId === '') {
                this.msgError('请选择集货区！');
                return false;
            }
            this.goodsList = [];
            var branchCode = this.$TOOL.data.get('NETWORK').branchCode;
            pharmaceuticalloading
                .getAreaList({
                    areaCode: this.storeList.companyId,
                    branchCode: branchCode
                })
                .then((res) => {
                    if (res.code == 200) {
                        if (res.data.length > 0) {
                            if (this.goodsList.length > 0) {
                                this.goodsList.forEach(function (item) {
                                    if (item.areaCode !== res.data[0].areaCode) {
                                        this.goodsList.push(res.data[0]);
                                    }
                                });
                            } else {
                                this.goodsList.push(res.data[0]);
                            }
                            this.renewGoodsType();
                            this.getList();
                        } else {
                            this.msgError('此集货区暂无货物！');
                        }
                    }
                });
        },
        // 保温箱装车
        completeIncubator() {
            if (!this.selectedIncubatorList.length) {
                this.msgError('请选择需要装车的保温箱！');
                return false;
            }
            var loadList = {};
            if (this.loadingList.driverPhone != '') {
                loadList.receiptWay = this.radio;
                loadList.driverCode = this.loadingList.driverCode;
                loadList.driverName = this.loadingList.driverName.driverName;
                loadList.driverPhone = this.loadingList.driverPhone;
                loadList.carCode = this.loadingList.carCode;
                loadList.carClass = this.loadingList.carClass;
                loadList.deviceNo = this.loadingList.deviceNo;
                loadList.flowCode = this.loadingList.flowCode;
                loadList.orderWay = this.loadingList.orderWay;
                loadList.dataSource = 2;
                loadList.dataList = this.selectedIncubatorList.map((item) => {
                    return {
                        dataId: item
                    };
                });
            } else {
                loadList.id = this.continueLoadingList.id;
                loadList.batchNo = this.continueLoadingList.batchNo;
                loadList.receiptWay = this.continueLoadingList.receiptWay;
                loadList.driverCode = this.continueLoadingList.driver.driverCode;
                loadList.driverName = this.continueLoadingList.driver.driverName;
                loadList.driverPhone = this.continueLoadingList.driverPhone;
                loadList.carCode = this.continueLoadingList.carCode;
                loadList.carClass = this.continueLoadingList.carClass;
                loadList.deviceNo = this.continueLoadingList.deviceNo;
                loadList.flowCode = this.loadingList.flowCode;
                loadList.orderWay = this.loadingList.orderWay;
                loadList.dataList = this.selectedIncubatorList.map((item) => {
                    return {
                        dataId: item
                    };
                });
                loadList.dataSource = 2;
            }

            pharmaceuticalloading.assignRecordLoadCar(loadList).then((res) => {
                if (res.code == 200) {
                    this.msgSuccess('装车成功');
                    this.incubatorOpen = false;
                    this.vehicleOpen = false;
                    this.getList();
                }
            });
        },

        // 完成集货区装车
        completeLoading() {
            if (this.dataList && this.dataList.length == 0) {
                this.msgError('请选择集货区货物！');
                return false;
            }
            var loadList = {};
            if (this.loadingList.driverPhone != '') {
                // 集货区装车信息
                loadList.receiptWay = this.loadingList.radio;
                loadList.driverCode = this.loadingList.driverCode;
                loadList.driverName = this.loadingList.driverName.driverName;
                loadList.driverPhone = this.loadingList.driverPhone;
                loadList.carCode = this.loadingList.carCode;
                loadList.carClass = this.loadingList.carClass;
                loadList.deviceNo = this.loadingList.deviceNo ? this.loadingList.deviceNo : '';
                loadList.dataList = this.dataList;
                loadList.orderWay = this.loadingList.orderWay;
                loadList.flowCode = this.loadingList.flowCode;
                loadList.dataSource = 1;
            } else {
                console.log(this.continueLoadingList);
                loadList.id = this.continueLoadingList.id;
                loadList.batchNo = this.continueLoadingList.batchNo;
                loadList.receiptWay = this.continueLoadingList.receiptWay;
                loadList.driverCode = this.continueLoadingList.driver.driverCode;
                loadList.driverName = this.continueLoadingList.driver.driverName;
                loadList.driverPhone = this.continueLoadingList.driverPhone;
                loadList.carCode = this.continueLoadingList.carCode;
                loadList.carClass = this.continueLoadingList.carClass;
                loadList.deviceNo = this.continueLoadingList.deviceNo;
                loadList.dataList = this.dataList;
                loadList.orderWay = this.loadingList.orderWay;
                loadList.flowCode = this.loadingList.flowCode;
                loadList.dataSource = 1;
            }
            pharmaceuticalloading.assignRecordLoadCar(loadList).then((res) => {
                if (res.code == 200) {
                    this.msgSuccess('装车成功');
                    this.storeOpen = false;
                    this.vehicleOpen = false;
                    this.continueOpen = false;
                    this.getList();
                }
            });
        },
        /*
         * 确认发车1、完成装车2
         * */
        confirmVehicle(val, type) {
            if (type === 1) {
                ElMessageBox.confirm('是否完成装车？', '完成装车', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        this.operationType(val.id, type);
                    })
                    .catch(() => {
                        ElMessage({
                            type: 'info',
                            message: '装车已取消！'
                        });
                    });
            } else if (type === 2) {
                ElMessageBox.confirm('是否确认发车？', '确认发车', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        this.operationType(val.id, type);
                    })
                    .catch(() => {
                        ElMessage({
                            type: 'info',
                            message: '发车已取消！'
                        });
                    });
            }
        },

        // 继续装车
        continueLoading(val) {
            this.title = '继续装车';
            this.continueOpen = true;
            this.displayType = false;
            this.continueLoadingList = val;
            this.getLoadingDetails(val);
        },
        async dict() {
            this.fourplCarList = await this.getDictList('fourpl_car_class');
            this.loadingStatusList = await this.getDictList('loading_status_type');
            // 装车配送方式
            this.loadingDeliveryMethodList = await this.getDictList('loading_delivery_method');
            // 装车发货方式
            this.loadingShippingMethodList = await this.getDictList('loading_shipping_method ');
        },
        // 司机信息填充
        driverInformation(val) {
            this.loadingList.driverCode = val.driverCode;
            this.loadingList.driverPhone = val.driverPhone;
            this.loadingList.carCode = val.carCode;
            this.loadingList.carClass = val.carClass;
            this.loadingList.deviceNo = val.deviceNo;
            this.loadingList.driverName = val.driverName;
        },
        // 回退修改
        fallbackModification(val) {
            ElMessageBox.confirm('是否回退修改？', '回退修改', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    pharmaceuticalloading
                        .getassignRecordRollback({
                            assignId: val.id
                        })
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage({
                                    type: 'success',
                                    message: '已回退!'
                                });
                                this.getList();
                            }
                        });
                })
                .catch(() => {
                    ElMessage({
                        type: 'info',
                        message: '装车已取消！'
                    });
                });
        },
        // 字典翻译
        formDict(data, val) {
            if (!val || !data?.length) return '--';
            return this.selectDictLabel(data, val);
        },
        // 获取承运商所有司机
        getDriverList() {
            pharmaceuticalloading.getDriverList().then((res) => {
                if (res.code === 200) {
                    this.driverList = res.data;
                }
            });
        },
        getIncubator() {
            this.options = [];
            pharmaceuticalloading.getIncubatorAddress().then((res) => {
                if (res.code == 200) {
                    let pList = {};
                    let cList = {};
                    this.provincialUrbanAreas = res.data;
                    this.provincialUrbanAreas.provinceList.forEach((item) => {
                        pList.value = item.provinceId;
                        pList.label = item.provinceName;
                        pList.children = [];
                        item.cityList.forEach((citys) => {
                            cList = {
                                value: citys.cityId,
                                label: citys.cityName
                            };
                            cList.children = [];
                            if (citys.countyList) {
                                citys.countyList.forEach((coun) => {
                                    cList.children.push({
                                        value: coun.countyId,
                                        label: coun.countyName
                                    });
                                });
                            }
                            pList.children.push(cList);
                        });
                        this.options.push(pList);
                    });
                    this.title = '保温箱选择';
                    this.incubatorOpen = true;
                }
            });
        },

        /** 装车列表 */
        getList(val) {
            let data = {
                'driver.driverCode': this.queryParams.driverCode,
                driverPhone: this.queryParams.driverPhone,
                beginCreateDate: this.queryParams.queryTime ? this.queryParams.queryTime[0] : null,
                endCreateDate: this.queryParams.queryTime ? this.queryParams.queryTime[1] : null,
                status: this.queryParams.status,
                orderWay: this.queryParams.orderWay,
                receiptWay: this.queryParams.receiptWay,
                current: this.queryParams.current,
                size: this.queryParams.size
            };
            pharmaceuticalloading.assignRecordList(data).then((res) => {
                if (res.code == 200) {
                    this.alList = res.data.records;
                    this.total = res.data.total;
                }
            });

            // if (val == 1) {
            // 	let data = {
            // 		'driver.driverCode': this.queryParams.driverCode,
            // 		driverPhone: this.queryParams.driverPhone,
            // 		beginCreateDate: this.queryParams.queryTime ? this.queryParams.queryTime[0] : null,
            // 		endCreateDate: this.queryParams.queryTime ? this.queryParams.queryTime[1] : null,
            // 		status: this.queryParams.status,
            // 		orderWay: this.queryParams.orderWay,
            // 		receiptWay: this.queryParams.receiptWay,
            // 		current:this.queryParams.current,
            // 		size:this.queryParams.size,
            // 	}
            // 	pharmaceuticalloading.assignRecordList(data).then((res) => {
            // 		if (res.code == 200) {
            // 			this.alList = res.data.records;
            // 			this.total = res.data.total;
            // 		}
            // 	});
            // } else {
            // 	pharmaceuticalloading.assignRecordList(this.queryParams).then((res) => {
            // 		if (res.code == 200) {
            // 			this.alList = res.data.records;
            // 			this.total = res.data.total;
            // 		}
            // 	});
            // }
        },
        // 装车记录详情
        getLoadingDetails(val) {
            pharmaceuticalloading
                .getassignRecordQueryById({
                    id: val.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.loadingRecordList = res.data;
                    }
                });
        },
        //接收子组件传值
        getMsgFormSon(data) {
            this.dataList = data;
        },
        // 集货区list
        getStoreGoods() {
            var branchCode = this.$TOOL.data.get('NETWORK').branchCode;
            pharmaceuticalloading
                .getBranchAreaList({
                    branchCode: branchCode,
                    areaClass: '3'
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.storeGoodsList = res.data;
                    }
                });
        },
        // 查询保温箱
        getselectSensor() {
            pharmaceuticalloading.getLoadCarSensorSelect().then((res) => {
                if (res.code == 200) {
                    this.sensorList = res.data.records;
                    var arr = this.sensorList.map((item) => {
                        return item.recordId;
                    });
                    this.cities = arr;
                }
            });
        },
        handleChange() {},
        // 导出
        handleExport() {
            let data = {
                'driver.driverCode': this.queryParams.driverCode,
                driverPhone: this.queryParams.driverPhone,
                beginCreateDate: this.queryParams.queryTime ? this.queryParams.queryTime[0] : '',
                endCreateDate: this.queryParams.queryTime ? this.queryParams.queryTime[1] : ''
            };
            pharmaceuticalloading
                .assignRecordExport(qs.stringify(data, { arrayFormat: 'repeat' }), '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '装车记录.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                    } else {
                        this.$message.error('导出异常请联系管理员');
                    }
                })
                .catch((err) => {
                    this.msgError(err);
                });
        },
        // 装车-保温箱选择列表-分组查询省市区搜索条件
        incubatorQuery() {
            pharmaceuticalloading
                .getLoadCarSensorSelect({
                    provinceId: this.addressList.shippingAddress[0] ? this.addressList.shippingAddress[0] : '',
                    cityId: this.addressList.shippingAddress[1] ? this.addressList.shippingAddress[1] : '',
                    countyId: this.addressList.shippingAddress[2] ? this.addressList.shippingAddress[2] : ''
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.sensorList = res.data.records;
                    }
                });
        },

        // 点击运单获取数据
        obtainBoxCode(item, index, coun, index1, listItem, index2) {
            pharmaceuticalloading
                .getDetailGroupCode({
                    recordId: item.id,
                    countyId: coun.countyId,
                    transOrderNo: listItem.transOrderNo
                })
                .then((res) => {
                    if (res.code === 200) {
                        const data = res.data.map((item) => {
                            item.checked = false;
                            return item;
                        });
                        listItem.listTrans = data;
                    }
                });
        },
        // 点击保温箱省市区获取数据
        obtainWaybill(item, index, coun, index1) {
            pharmaceuticalloading
                .getDetailGroupOrder({
                    recordId: item.id,
                    countyId: coun.countyId
                })
                .then((res) => {
                    if (res.code === 200) {
                        const data = res.data.map((item) => {
                            item.checked = false;
                            return item;
                        });
                        coun.list = data;
                    }
                });
        },
        //调用撤销组件的方法
        openRevokeOpen() {
            this.$refs.child.getLoadingDetails();
        },
        // 装车
        operateLoading() {
            this.title = '发件装车';
            this.vehicleOpen = true;
        },
        // 装车方法
        operationType(val, type) {
            pharmaceuticalloading
                .getchangeAssignStatus({
                    id: val,
                    status: type
                })
                .then((res) => {
                    if (res.code === 200) {
                        if (type === 1) {
                            ElMessage({
                                type: 'success',
                                message: '装车完成!'
                            });
                        } else if (type === 2) {
                            ElMessage({
                                type: 'success',
                                message: '已发车!'
                            });
                        }
                        this.getList();
                    }
                });
        },
        // 司机
        queryDriver() {
            pharmaceuticalloading
                .getLoadCarDriverSelect({
                    size: '-1',
                    current: '0'
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.driverNameList = res.data.records;
                    }
                });
        },
        // 集货区查询重置
        relocationCargoCollection() {
            this.goodsList = [];
            this.storeList.companyId = '';
        },
        // 保温箱重置
        relocationCargoIncubator() {
            this.addressList.shippingAddress = [];
            this.getselectSensor();
        },
        renewGoodsType(val) {
            if (val === 1) {
                this.$refs.renewGoods.renewGoods();
            } else {
                this.$refs.renewGoods.renewGoods();
            }
        },

        resetQuery() {
            this.queryParams = {
                current: 1,
                size: 10
            };
            this.getList();
        },

        //继续装车'保温箱
        selectIncubator(type) {
            if (type != 1) {
                this.$refs['formRef'].validate((valid) => {
                    if (valid) {
                        this.getIncubator();
                        this.getselectSensor();
                    }
                });
            } else {
                this.getIncubator();
                this.getselectSensor();
            }
        },

        //继续装车选择集货
        selectStore(val) {
            this.loadingList.radio = this.radio;
            if (val === 1) {
                this.getStoreGoods();
                this.title = '集货区选择';
                this.storeOpen = true;
            } else {
                this.$refs['formRef'].validate((valid) => {
                    if (valid) {
                        this.getStoreGoods();
                        this.title = '集货区选择';
                        this.storeOpen = true;
                    }
                });
            }
        },
        // 接受保温箱已选的集合
        selectedIncubatorClick(val) {
            this.selectedIncubatorList = val;
        },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        // 三方物流公司
        thirdLogistics() {
            pharmaceuticalloading.threePartFlowFlowList().then((res) => {
                if (res.code === 200) {
                    this.tripartiteLogisticsCompanyList = res.data.records;
                }
            });
        },
        // 传感器验证
        verifySensor() {
            console.log('验证传感器');
        },
        /*
         * 查看装车详情
         * */
        viewLoadingDetails(val, type) {
            if (type === '2') {
                this.displayType = true;
            }
            this.title = '装车详情';
            this.continueOpen = true;
            this.continueLoadingList = val;
            this.getLoadingDetails(val);
        }
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}

::v-deep {
    .el-collapse-item__wrap {
        border-bottom: 0px;
    }

    .el-collapse-item__header {
        border-bottom: 0px;
        height: 30px;
    }

    .el-collapse-item__content {
        padding-bottom: 0px;
    }
}

::v-deep {
    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .el-radio:last-child {
        margin-right: 30px;
    }

    .el-radio {
        margin-bottom: 10px;

        .el-radio__label {
            .el-input__wrapper {
                background: none;
                box-shadow: none;
            }
        }
    }
}
</style>
