/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-07-18 17:06:13
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-07-28 14:50:24
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\utils\isDeleteAuthority.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import tool from '@/utils/tool';
// 判断登录用户是否superadmin 是否创建人等
export default function isDeleteAuthority(createId, isDelte, status) {
    if(!createId || typeof createId  != 'string') return false
    const userInfo = tool.data?.get("USER_INFO");
    const roleList = tool.data?.get("ROLE_LIST");
    const createBy = userInfo?.id === createId;
    const isSuperAdmin = roleList?.some(item => item.roleType === 'superadmin');
    const isStatus = isDelte?.includes(status)
    if (isDelte?.length && status) {
        return !((createBy || isSuperAdmin) && isStatus);
    } else {
        return !(createBy || isSuperAdmin)
    }

}
