<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="serviceForm" :inline="true" :model="searchForm" :rules="searchFormRules">
                <el-form-item label="承运商" prop="carrierId" style="margin-bottom: 10px">
                    <el-select v-model="searchForm.carrierId" clearable filterable placeholder="请选择承运商" @change="handleCompanyChange">
                        <el-option v-for="item in companyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span class="text__tip">请先选择计费公式中需要的计费变量和需配置的计费参数。</span>
            <el-table v-if="searchForm.carrierId" ref="BillingFactorSelectionTable" v-loading="loading" :border="true" :data="orderList" @selection-change="handleSelectionChange" style="margin-top: 10px">
                <el-table-column align="center" type="selection" width="55"></el-table-column>
                <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
                <el-table-column align="center" label="变量名称" prop="variableName" width="120"></el-table-column>
                <el-table-column align="center" label="变量说明" prop="remark"></el-table-column>
                <el-table-column align="center" label="单位" prop="variableUnit" width="120"></el-table-column>
                <el-table-column align="center" label="变量值来源" prop="origin" width="280">
                    <template #default="scope">
                        <span v-if="scope.row.origin === '1'">因子</span>
                        <span v-else-if="scope.row.origin === '2'">参数</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
            </el-table>
            <div v-if="orderList && orderList.length > 0 && searchForm.carrierId" class="BillingFactorSelection__footer">
                <!--        <el-button type="info">取消</el-button>-->
                <el-button type="primary" @click="determineAndSelectChargingFactorVisible = true">确定</el-button>
            </div>
        </el-card>

        <!--  /确定选择计费因子 对话框  -->
        <el-dialog v-model="determineAndSelectChargingFactorVisible" append-to-body center class="dialog__determineAndSelectChargingFactor" title="选择计费因子" width="500px">
            <el-result v-loading="determineAndSelectChargingFactorLoading" :element-loading-text="determineAndSelectChargingFactorLoadingText" icon="warning" title="您确定使用选中变量吗？">
                <template #extra>
                    <el-button @click="determineAndSelectChargingFactorVisible = false">取消</el-button>
                    <el-button type="primary" @click="determineAndSelectChargingFactor()">确认</el-button>
                </template>
            </el-result>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import billingFactorSelection from '@/api/platformFeatures/billingFactorSelection';
import billingFactorSettings from '@/api/platformFeatures/billingFactorSettings';
import billingParameterSettings from '@/api/platformFeatures/billingParameterSettings';

export default {
    name: 'BillingFactorSelection',
    components: { ColumnTable },
    data() {
        return {
            loading: false,
            orderList: [],
            multipleSelection: [],
            determineAndSelectChargingFactorVisible: false,
            determineAndSelectChargingFactorLoading: false,
            determineAndSelectChargingFactorLoadingText: '加载中...',
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            searchForm: {},
            searchFormRules: {
                carrierId: [{ required: true, message: '请选择承运商', trigger: 'change' }]
            },
            companyList: [],
            TempCompanyId: ''
        };
    },
    created() {
        this.getCarrierList();
    },
    methods: {
        // 获取承运商列表
        getCarrierList() {
            billingFactorSelection
                .getCarrierList({ type: '1' })
                .then((res) => {
                    if (res.code === 200) {
                        this.companyList = res.data;
                    } else {
                        this.$message.error(res.message);
                    }
                })
                .catch((err) => {
                    this.$message.error(err.message);
                });
        },
        billingFormulaDetail(carrierId) {
            return new Promise((resolve, reject) => {
                billingFactorSelection
                    .billingFormulaDetail({ carrierId })
                    .then((res) => {
                        if (res.code === 200) {
                            resolve(res.data);
                        } else {
                            this.$message.error(res.message);
                            reject(res);
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        billingFactorListByFormula() {
            return new Promise((resolve, reject) => {
                billingFactorSettings
                    .billingFactorListByFormula({})
                    .then((res) => {
                        if (res.code === 200) {
                            resolve(res.data);
                        } else {
                            this.$message.error(res.message);
                            reject(res);
                        }
                    })
                    .catch((err) => {
                        this.loading = false;
                        reject(err);
                    });
            });
        },
        billingParameterListByFormula() {
            return new Promise((resolve, reject) => {
                billingParameterSettings
                    .billingParameterListByFormulaCopy({})
                    .then((res) => {
                        if (res.code === 200) {
                            resolve(res.data);
                        } else {
                            this.$message.error(res.message);
                            reject(res);
                        }
                    })
                    .catch((err) => {
                        this.loading = false;
                        reject(err);
                    });
            });
        },
        getTableData(carrierId) {
            this.loading = true;
            // billingParameterListByFormula billingFactorListByFormula 两个接口请求的参数合并 赋值给this.orderList
            Promise.all([this.billingParameterListByFormula(), this.billingFactorListByFormula(), this.billingFormulaDetail(carrierId)])
                .then((res) => {
                    this.orderList = res[0].concat(res[1]);
                    this.multipleSelection = res[2];
                    this.$nextTick(() => {
                        this.multipleSelection.forEach((item) => {
                            const index = this.orderList.findIndex((i) => i.id === item);
                            if (index !== -1) {
                                this.$refs.BillingFactorSelectionTable.toggleRowSelection(this.orderList[index]);
                            }
                        });
                    });
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            if (selection) {
                // 遍历 selection中的 id status 赋值给 this.selectData
                this.selectData = selection;
                this.single = selection.length !== 1;
                this.multiple = !selection.length;
            }
        },
        determineAndSelectChargingFactor() {
            this.$refs.serviceForm.validate((valid) => {
                if (valid) {
                    // 从this.selectData 中取出 id 和 origin
                    // this.selectData 去除 undefined
                    const data = this.selectData
                        .filter((item) => item)
                        .map((item) => {
                            return {
                                parametersId: item.id,
                                origin: item.origin,
                                carrierId: this.searchForm.carrierId,
                                variableUnit: item.variableUnit || '无'
                            };
                        });
                    billingFactorSelection
                        .addBillingFormula(data)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$notify({ title: '成功', message: '选择成功', type: 'success', duration: 2000 });
                                this.getTableData(this.TempCompanyId);
                            } else {
                                this.$notify({ title: '失败', message: res.message, type: 'error', duration: 2000 });
                            }
                        })
                        .finally(() => {
                            this.determineAndSelectChargingFactorVisible = false;
                        });
                }
            });
        },
        handleCompanyChange(val) {
            // 检索
            if (val) {
                this.TempCompanyId = val;
                this.getTableData(val);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
    padding-bottom: 20px;
}

.el-result {
    padding: 0;
}

.text__tip {
    color: #999;
    font-size: 12px;
    display: flex;
}

.BillingFactorSelection__footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
