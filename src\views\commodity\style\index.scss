.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.formBox {
	width: 100%;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
}

.text {
	font-size: 14px;
}

.item {
	margin-bottom: 18px;
	margin-top: -10px;
}

.dialog-footer button:first-child {
	margin-right: 10px;
}

.el-checkbox-group {
	margin-right: 10px;
}

.el-select-dropdown__list {
	.el-input {
		width: 90%;
		margin-left: 5%;
		margin-top: 10px;
		margin-bottom: 15px;
	}

	.el-pagination {
		margin-right: 20px;
		margin-top: 10px;
		margin-bottom: 10px;
	}
}

.cell {
	width: 100%;
	height: 100%;
}

.zhe {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.6);

	.imgDiv {
		max-width: 70%;
		max-height: 70%;

		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);

		img {
			width: 100%;
			height: 100%;
			min-width: 200px;
			color: #fff;
		}

		span {
			position: absolute;
			font-size: 25px;
			border-radius: 50%;
			height: 30px;
			width: 30px;
			line-height: 34px;
			text-align: center;
			color: #fff;
			right: -30px;
			top: -5px;
		}
	}
}

.col_title {
	color: #333;
	font-size: 18px;
	font-weight: bold;
	position: relative;
	padding-left: 8px;

	&::after {
		content: "";
		display: inline-block;
		width: 3px;
		height: 20px;
		background-color: #2878ff;
		border-radius: 2px;
		position: absolute;
		top: 15px;
		left: 0;
	}
}

.stateTitle {
	position: absolute;
	font-size: 15px;
	top: 22px;
	display: flex;
	right: 50px;

	span {
		margin-right: 20px;
	}

	.el-button {
		margin-top: -0.03rem;
	}
}


