<!--  传感器设置 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto">
                <el-form-item label="报警名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入报警名称" />
                </el-form-item>
                <el-form-item class="define" label="消息类型" prop="msgType">
                    <el-select v-model="queryParams.msgType">
                        <el-option v-for="item in messageList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item class="define" label="状态" prop="status">
                    <el-select v-model="queryParams.status">
                        <el-option v-for="item in stateList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="getList" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" icon="el-icon-plus" size="mini" type="primary" @click="batch">新增报警规则</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:export']" icon="el-icon-download" size="mini" type="warning" @click="exportList">导出</el-button>
                    </el-col>
                </el-row>
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table v-loading="loading" :data="abnormalprintList" border style="margin-top: 15px" @selection-change="handleSelectionChange">
                <el-table-column :index="idxMethod" align="center" label="序号" type="index" width="55" />
                <el-table-column align="center" label="报警名称" prop="name" width="120" />
                <el-table-column align="center" label="消息类型" prop="msgType" width="80">
                    <template #default="scope">
                        <span>{{ scope.row.msgType == 1 ? '短信' : '微信' }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="报警间隔" prop="alarmInterval" width="80">
                    <template #default="scope">
                        <span>{{ scope.row.alarmInterval }}分钟</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="应用范围" prop="applyRangeList" show-overflow-tooltip="true" width="400">
                    <template #default="scope">
                        <span v-for="item in scope.row.applyRangeList" :key="item.id">{{ item.alarmType }}{{ item.alarmValue }};</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="状态" prop="status" width="120">
                    <template #default="scope">
                        <el-switch v-model="scope.row.status" active-text="开" active-value="0" inactive-text="关" inactive-value="1" inline-prompt @change="alarmStatus(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="备注" min-width="120" prop="remark" show-overflow-tooltip/>
                <el-table-column align="center" label="操作" width="150" show-overflow-tooltip fixed="right">
                    <template #default="scope">
                        <el-button icon="el-icon-edit" link size="small" type="warning" @click="modifyJSON(scope.row)">修改</el-button>
                        <el-button icon="el-icon-delete" link size="small" type="danger" @click="alarmDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增 -->
        <el-drawer v-model="dialogVisible" :before-close="handleClose" :title="title" size="650" width="30%">
            <div style="padding: 0 20px">
                <el-form v-show="showSearch" ref="formRef" :inline="true" :model="form" :rules="rules" label-width="110px">
                    <div>
                        <el-form-item label="报警名称:" prop="name" style="width: 520px">
                            <el-input v-model="form.name" placeholder="请输入报警名称" />
                        </el-form-item>
                        <el-form-item class="define" label="消息类型:" prop="msgType">
                            <el-select v-model="form.msgType" placeholder="请选择消息类型" style="width: 410px">
                                <el-option v-for="item in messageList" :key="item.code" :label="item.name" :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item class="define" label="报警类型" prop="assetType">
                            <el-select v-model="form.assetType" placeholder="请选择类型" style="width: 430px">
                                <el-option v-for="dict in policeList" :key="dict.id" :label="dict.name" :value="dict.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item class="define" label="报警间隔:" prop="alarmInterval">
                            <el-input-number v-model="form.alarmInterval" :min="0" placeholder="请输入报警间隔" style="width: 410px" @change="handleChange" />
                        </el-form-item>
                        <div style="margin-left: 42px">
                            <span style="font-size: 14px; color: #606266">应用范围:</span>
                            <div style="margin-left: 100px">
                                <div v-for="(domain, index) in form.domains" :key="domain.key">
                                    <el-form-item v-if="domain.key === 0" :label="domain.value + ':'" prop="'domainsvalue'">
                                        <el-input v-model="domain.valuetype" disabled placeholder="自动获取" style="width: 250px" />
                                    </el-form-item>
                                    <el-form-item v-if="domain.key > 0 && domain.key < 4" :label="domain.value + ':'" prop="'domainsvalue'">
                                        <div style="display: flex">
                                            <el-input v-model="domain.valuetype" style="width: 250px" />
                                            <el-button v-if="index > 2" :icon="Plus" circle style="margin-left: 10px" @click="addDomain" />
                                            <el-button v-if="index > 3" :icon="Minus" circle class="mt-2" style="margin-left: 10px" @click.prevent="removeDomain(domain)" />
                                        </div>
                                    </el-form-item>
                                    <el-form-item v-if="domain.key > 3" :label="domain.value + index + ':'" prop="'domainsvalue'">
                                        <div style="display: flex">
                                            <el-input v-model="domain.valuetype" style="width: 250px" />
                                            <el-button v-if="index > 2" :icon="Plus" circle style="margin-left: 10px" @click="addDomain" />
                                            <el-button v-if="index > 3" :icon="Minus" circle class="mt-2" style="margin-left: 10px" @click.prevent="removeDomain(domain)" />
                                        </div>
                                    </el-form-item>
                                </div>
                            </div>
                        </div>
                        <el-form-item class="define" label="备注" prop="remark" style="width: 520px">
                            <el-input v-model="form.remark" :rows="2" maxlength="60" placeholder="请输入备注" type="textarea" />
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="determineAdd">确定</el-button>
                </span>
            </template>
        </el-drawer>
    </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { Minus, Plus } from '@element-plus/icons-vue';
import policeApi from '@/api/management/police';
import { verifyPhone } from '@/utils/verificate';
import qs from 'qs';
import SearchButton from '@/components/searchModule/SearchButton.vue';
const { proxy } = getCurrentInstance();

// import moment from 'moment';

// 表单验证
const rules = reactive({
    name: [{ required: true, message: '请输入报警名称', trigger: 'blur' }],
    msgType: [{ required: true, message: '请选择消息类型', trigger: 'change' }],
    alarmInterval: [{ required: true, message: '请输入报警间隔', trigger: 'blur' }],
    assetType: [{ required: true, message: '请选择报警类型', trigger: 'change' }],
    domainsvalue: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { validator: verifyPhone, trigger: 'blur' }
    ]
});

// 显示搜索条件
const showSearch = ref(true);
// 查询参数
const queryParams = ref({
    current: 1,
    size: 10,
    total: 0
});

const title = ref('');
const dialogVisible = ref(false);
/** 批量操作 */
function batch() {
    // reset();
    dialogVisible.value = true;
    title.value = '新增';
}

// 消息类型
const messageList = ref([]);
// 状态
const stateList = ref([
    { code: 0, name: '开启' },
    { code: 1, name: '关闭' }
]);

const removeDomain = (item) => {
    const index = form.value.domains.indexOf(item);
    if (index > 3) {
        form.value.domains.splice(index, 1);
    }
};

// 删除
function alarmDelete(row) {
    proxy
        .$confirm('是否确认删除数据?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            policeApi
                .alarmConfigDelete({
                    ids: row.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        getList();
                        proxy.msgSuccess('删除成功');
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        });
}

// 查询参数
const form = ref({
    domains: [
        {
            key: 0,
            value: '车辆司机',
            valuetype: ''
        },
        {
            key: 1,
            value: '设备告警人1',
            valuetype: ''
        },
        {
            key: 2,
            value: '设备告警人2',
            valuetype: ''
        },
        {
            key: 3,
            value: '设备告警人3',
            valuetype: ''
        }
    ]
});

// 确定新增
function determineAdd() {
    proxy.$refs['formRef'].validate((valid) => {
        if (valid) {
            var arr = form.value.domains.map(function (item) {
                let data = {};
                if (item.id) {
                    data = {
                        id: item.id,
                        alarmType: item.value,
                        alarmValue: item.valuetype
                    };
                } else {
                    data = {
                        alarmType: item.value,
                        alarmValue: item.valuetype
                    };
                }

                return data;
            });
            let data = {
                id: form.value.id ? form.value.id : null,
                msgType: form.value.msgType,
                name: form.value.name,
                alarmInterval: form.value.alarmInterval,
                remark: form.value.remark,
                applyRangeList: arr,
                sendConfig: 1,
                status: 0,
                assetType: form.value.assetType
            };
            policeApi
                .alarmConfigSave(data)
                .then((res) => {
                    if (res.code == 200) {
                        proxy.msgSuccess('新增成功');
                        getList();
                        dialogVisible.value = false;
                        form.value = {};
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        }
    });
}

// 表单重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    getList();
}

// 修改
function modifyJSON(row) {
    dialogVisible.value = true;
    title.value = '修改';
    // form.value = row;
    console.log(row);
    if (row.applyRangeList.length > 0) {
        var arr = row.applyRangeList.map(function (item, index) {
            let data = {};
            data = {
                key: index,
                id: item.id,
                value: item.alarmType,
                valuetype: item.alarmValue
            };
            return data;
        });
        form.value.alarmInterval = row.alarmInterval;
        form.value.assetType = row.assetType;
        form.value.delFlag = row.delFlag;
        form.value.id = row.id;
        form.value.msgType = row.msgType;
        form.value.name = row.name;
        form.value.remark = row.remark;
        form.value.sendConfig = row.sendConfig;
        form.value.status = row.status;
        form.value.domains = arr;
    }
}

const addDomain = () => {
    form.value.domains.push({
        key: Date.now(),
        value: '设备告警人'
    });
};

// 修改报警状态
function alarmStatus(val) {
    policeApi
        .updateStatus({
            id: val.id,
            status: val.status
        })
        .then((res) => {
            if (res.code == 200) {
                getList();
            }
        });
}

// 设备类型
const policeList = ref([]);
const operateList = ref([]);
// 字典请求
const getDict = async () => {
    messageList.value = await proxy.getDictList('alarm_message_type');
    operateList.value = await proxy.getDictList('operate_type');
    policeList.value = await proxy.getDictList('alarm_police_type');
};

// 查询设备列表数据
const abnormalprintList = ref([]);
function getList() {
    policeApi.alarmConfigList(queryParams.value).then((res) => {
        if (res.code == 200) {
            abnormalprintList.value = res.data.records;
            queryParams.value.total = res.data.total;
        }
    });
}
// getList();

onMounted(() => {
    getDict();
    getList();
});
const chooseList = ref([]);
const handleSelectionChange = (key) => {
    chooseList.value = key;
};
// 导出
// const newFilArr = ref([])
function exportList() {
    let list = {
        // id: selectIds,
        filename: '报警信息记录'
    };
    policeApi
        .alarmConfigExport(qs.stringify(list, { arrayFormat: 'repeat' }), '', '', 'blob')
        .then((res) => {
            var debug = res;
            if (debug) {
                var elink = document.createElement('a');
                elink.download = '报警设备记录.xlsx';
                elink.style.display = 'none';
                var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                elink.href = URL.createObjectURL(blob);
                document.body.appendChild(elink);
                elink.click();
                document.body.removeChild(elink);
            } else {
                this.$message.error('导出异常请联系管理员');
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
    // } else {
    //     proxy.msgError('请选择需要导出的数据！')
    // }
}
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.deviceata {
    border: 2px solid #f5f7fa;
    padding: 20px;
    width: 100%;
}
</style>
