<template>
    <div>
        <el-drawer v-if="visible" v-model="visible" :title="prepaymentRechargeModificationTitle" class="self-drawer" size="770px" @close="closeVisible">
            <el-form ref="prepaymentRechargeModificationForm" :model="prepaymentRechargeModificationForm" label-width="auto">
                <el-descriptions v-if="prepaymentRechargeModificationStatus === 'approval' || prepaymentRechargeModificationStatus === 'detail'" :column="2" border title="申请信息">
                    <el-descriptions-item label="开票金额">{{ prepaymentRechargeModificationForm.invoiceApply.invoiceAmount }}</el-descriptions-item>
                    <el-descriptions-item label="申请公司">{{ prepaymentRechargeModificationForm.invoiceApply.companyName }}</el-descriptions-item>
                    <el-descriptions-item label="预付款账号">{{ prepaymentRechargeModificationForm.paymentApplyDTO.advanceAccount }}</el-descriptions-item>
                    <el-descriptions-item label="发票抬头">{{ prepaymentRechargeModificationForm.invoiceApply.invoiceHead }}</el-descriptions-item>
                    <el-descriptions-item label="签约公司" span="2">{{ formatDictionaryData('companyList', prepaymentRechargeModificationForm.invoiceApply.signCompany) }}</el-descriptions-item>
                    <el-descriptions-item label="发票类型">{{ formatDictionaryData('invoiceTypeList', prepaymentRechargeModificationForm.invoiceApply.invoiceType) }}</el-descriptions-item>
                    <el-descriptions-item label="税点(%)">{{ prepaymentRechargeModificationForm.invoiceApply.taxPoint }}</el-descriptions-item>
                    <el-descriptions-item label="税号">{{ prepaymentRechargeModificationForm.invoiceApply.taxNo }}</el-descriptions-item>
                    <el-descriptions-item label="地址">{{ prepaymentRechargeModificationForm.invoiceApply.address }}</el-descriptions-item>
                    <el-descriptions-item label="电话">
                        <span class="whitespace-nowrap">{{ prepaymentRechargeModificationForm.invoiceApply.phone }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="开户银行">{{ prepaymentRechargeModificationForm.invoiceApply.openBank }}</el-descriptions-item>
                    <el-descriptions-item label="银行账号">
                        <span class="whitespace-nowrap">{{ prepaymentRechargeModificationForm.invoiceApply.bankAccount }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请时间">{{ prepaymentRechargeModificationForm.invoiceApply.applyTime }}</el-descriptions-item>
                </el-descriptions>
                <el-descriptions :column="1" border class="mt-10 mb-10">
                    <el-descriptions-item label="开票项目名称">
                        <span>{{ setProjectName(prepaymentRechargeModificationForm.invoiceApply?.projectName) }}</span>
                        <span class="mx-5">|</span>
                        <span class="text-red-500">{{ prepaymentRechargeModificationForm.invoiceApply?.projectName }}</span>
                    </el-descriptions-item>
                </el-descriptions>
                <div v-if="prepaymentRechargeModificationStatus === 'detail'">
                    <el-descriptions v-if="prepaymentRechargeModificationForm.invoiceApply?.projectName === '运输服务费' || prepaymentRechargeModificationForm.invoiceApply?.projectName === '国内运输费'" :column="2" border class="mt-10" title="特定业务信息">
                        <el-descriptions-item label="运输工具种类">{{ prepaymentRechargeModificationForm.invoiceApply.transWay }}</el-descriptions-item>
                        <el-descriptions-item label="运输工具牌号">{{ prepaymentRechargeModificationForm.invoiceApply.transCarNo }}</el-descriptions-item>
                        <el-descriptions-item label="起运地址">{{ prepaymentRechargeModificationForm.invoiceApply.startAddress }}</el-descriptions-item>
                        <el-descriptions-item label="到达地址">{{ prepaymentRechargeModificationForm.invoiceApply.endAddress }}</el-descriptions-item>
                        <el-descriptions-item label="运输货物名称" span="2">{{ prepaymentRechargeModificationForm.invoiceApply.transGooods }}</el-descriptions-item>
                        <el-descriptions-item label="备注" span="2">{{ prepaymentRechargeModificationForm.remark }}</el-descriptions-item>
                        <el-descriptions-item label="发票备注" span="2">{{ prepaymentRechargeModificationForm.invoiceApply.remark }}</el-descriptions-item>
                    </el-descriptions>
                </div>
                <div v-else>
                    <div v-if="prepaymentRechargeModificationForm.invoiceApply?.projectName === '运输服务费' || prepaymentRechargeModificationForm.invoiceApply?.projectName === '国内运输费'">
                        <el-descriptions class="mt-10" title="特定业务信息"></el-descriptions>
                        <el-form-item :rules="[{ required: true, message: '请输入运输工具种类', trigger: 'blur' }]" label="运输工具种类" prop="invoiceApply.transWay">
                            <el-input v-model="prepaymentRechargeModificationForm.invoiceApply.transWay" placeholder="请输入运输工具种类"></el-input>
                        </el-form-item>
                        <el-form-item :rules="{ required: true, message: '请输入运输工具牌号', trigger: 'blur' }" label="运输工具牌号" prop="invoiceApply.transCarNo">
                            <el-input v-model="prepaymentRechargeModificationForm.invoiceApply.transCarNo" placeholder="请输入运输工具牌号"></el-input>
                        </el-form-item>
                        <el-form-item :rules="{ required: true, message: '请输入起运地址', trigger: 'blur' }" label="起运地址" prop="invoiceApply.startAddress">
                            <el-input v-model="prepaymentRechargeModificationForm.invoiceApply.startAddress" placeholder="请输入起运地址"></el-input>
                        </el-form-item>
                        <el-form-item :rules="{ required: true, message: '请输入到达地址', trigger: 'blur' }" label="到达地址" prop="invoiceApply.endAddress">
                            <el-input v-model="prepaymentRechargeModificationForm.invoiceApply.endAddress" placeholder="请输入到达地址"></el-input>
                        </el-form-item>
                        <el-form-item :rules="{ required: true, message: '请输入运输货物名称', trigger: 'blur' }" label="运输货物名称" prop="invoiceApply.transGooods">
                            <el-input v-model="prepaymentRechargeModificationForm.invoiceApply.transGooods" placeholder="请输入运输货物名称"></el-input>
                        </el-form-item>
                    </div>
                    <el-form-item :rules="[{ required: true, message: '请输入备注', trigger: 'blur' }]" label="备注" prop="remark">
                        <el-input v-model="prepaymentRechargeModificationForm.remark" maxlength="200" placeholder="请输入备注" show-word-limit type="textarea"></el-input>
                    </el-form-item>
                    <el-form-item label="发票备注" prop="invoiceApply.remark">
                        <el-input v-model="prepaymentRechargeModificationForm.invoiceApply.remark" maxlength="150" placeholder="请输入发票备注" show-word-limit type="textarea"></el-input>
                    </el-form-item>
                    <el-divider border-style="dashed" class="my-5"></el-divider>
                </div>
                <div v-if="prepaymentRechargeModificationStatus === 'approval'">
                    <el-descriptions :column="2" border class="mt-10" title="审批">
                        <el-descriptions-item label="申请人">{{ prepaymentRechargeModificationForm.invoiceApplyDto.createBy.name }}</el-descriptions-item>
                        <el-descriptions-item label="申请时间">{{ formatDate(prepaymentRechargeModificationForm.invoiceApplyDto.createDate) }}</el-descriptions-item>
                    </el-descriptions>
                    <el-form-item :rules="{ required: true, message: '请选择审批意见', trigger: 'change' }" class="mt-10" label="审批意见" prop="approveStatus">
                        <el-radio-group v-model="prepaymentRechargeModificationForm.approveStatus">
                            <el-radio label="1">通过</el-radio>
                            <el-radio label="2">驳回</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
                <div v-else-if="prepaymentRechargeModificationStatus === 'detail'" class="mt-10">
                    <div v-if="prepaymentRechargeModificationForm.approveList && prepaymentRechargeModificationForm.approveList.length">
                        <el-descriptions title="审批"></el-descriptions>
                        <el-steps :active="1" :space="60" direction="vertical">
                            <el-step v-for="(item, index) in prepaymentRechargeModificationForm.approveList" :key="index" :description="`审批人：${item.approveUser}`" :title="`审批时间：${formatDate(item.approveTime)}`">
                                <template #icon>
                                    <span style="display: inline-block; width: 17px; height: 17px; border-radius: 50%; background: #a8abb2"></span>
                                </template>
                            </el-step>
                            <el-step :description="`申请时间：${formatDate(prepaymentRechargeModificationForm.invoiceApplyDto.createDate)}`" :title="`申请人：${prepaymentRechargeModificationForm.invoiceApplyDto.createBy.name}`">
                                <template #icon>
                                    <span style="display: inline-block; width: 17px; height: 17px; border-radius: 50%; background: #a8abb2"></span>
                                </template>
                            </el-step>
                        </el-steps>
                        <div style="margin-left: 20px">
                            <div class="flex mb-10 items-center">
                                <span>审批意见：</span>
                                <el-tag v-if="prepaymentRechargeModificationForm.approveList[0]?.approveStatus === '1'" effect="dark" type="success">已通过</el-tag>
                                <el-tag v-else effect="dark" type="danger">已驳回</el-tag>
                            </div>
                            <div class="flex">
                                <span class="whitespace-nowrap">审批备注：</span>
                                <span class="word-break-break-word">{{ prepaymentRechargeModificationForm.approveList[prepaymentRechargeModificationForm.approveList.length - 1]?.approveIdea }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <el-form-item v-if="prepaymentRechargeModificationStatus === 'approval'" :rules="prepaymentRechargeModificationForm.approveStatus === '2' ? [{ required: true, message: '请输入审批备注', trigger: 'blur' }] : []" label="审批备注" prop="approveIdea">
                    <el-input v-model="prepaymentRechargeModificationForm.approveIdea" :rows="4" maxlength="250" placeholder="请输入审批备注" show-word-limit type="textarea"></el-input>
                </el-form-item>
            </el-form>
            <template v-if="prepaymentRechargeModificationStatus === 'approval'" #footer>
                <div v-loading="prepaymentRechargeModificationLoading" style="margin-top: 10px">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm">提 交</el-button>
                </div>
            </template>
        </el-drawer>
        <el-image-viewer v-if="viewerVisible" v-model="viewerVisible" :url-list="viewerList" @close="viewerVisible = false" />
    </div>
</template>
<script>
import tool from '@/utils/tool';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import { selectDictLabel } from '@/utils/dictLabel';
import { Close, Delete, Plus } from '@element-plus/icons-vue';
import moment from 'moment';
import InvoiceApproval from '@/api/carrierEnd/InvoiceApproval';

export default {
    name: 'PrepaymentRechargeModification',
    components: { Close, Delete, Plus },
    model: {
        prop: 'prepaymentRechargeModificationVisible',
        event: 'update:prepaymentRechargeModificationVisible'
    },
    props: {
        /**
         * 开票申请id
         */
        prepaymentRechargeModificationInvoicingId: {
            type: String,
            default: undefined
        },
        prepaymentRechargeModificationStatus: {},
        prepaymentRechargeModificationTitle: {},
        prepaymentRechargeModificationVisible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            visible: this.prepaymentRechargeModificationVisible,
            viewerVisible: false,
            viewerList: [],
            prepaymentRechargeModificationForm: {
                approveList: [],
                approveStatus: undefined,
                remark: undefined,
                approveIdea: undefined,
                invoiceApplyDto: {
                    carrier: {
                        name: undefined
                    },
                    invoiceAmount: undefined,
                    applyTime: undefined,
                    remitFile: [],
                    createBy: {
                        name: undefined
                    },
                    createDate: undefined
                },
                paymentApplyDTO: {
                    advanceAccount: undefined
                },
                invoiceApply: {
                    companyName: undefined,
                    invoiceHead: undefined,
                    taxNo: undefined,
                    address: undefined,
                    phone: undefined,
                    signCompany: undefined,
                    invoiceType: undefined,
                    taxPoint: undefined,
                    remark: undefined,
                    applyTime: undefined,
                    invoiceAmount: undefined,
                    projectName: undefined,
                    transWay: undefined,
                    transCarNo: undefined,
                    startAddress: undefined,
                    endAddress: undefined,
                    transGooods: undefined,
                    openBank: undefined,
                    bankAccount: undefined
                }
            },
            prepaymentRechargeModificationLoading: false,
            companyList: [],
            invoiceTypeList: []
        };
    },
    computed: {
        /**
         * 格式化日期
         * @returns {function(*): *}
         */
        formatDate() {
            return (value) => {
                return moment(value).format('YYYY-MM-DD');
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    watch: {
        prepaymentRechargeModificationVisible: {
            handler(val) {
                this.visible = val;
            },
            immediate: true
        }
    },
    created() {
        if (this.prepaymentRechargeModificationInvoicingId) {
            this.getPrepaymentRechargeModificationInvoicingDetail(this.prepaymentRechargeModificationInvoicingId);
        }
        this.getDict();
    },
    methods: {
        /**
         * 关闭抽屉
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:prepaymentRechargeModificationVisible', false);
        },
        /**
         * 文件上传成功
         * @param res
         */
        fileUploadSuccess(res) {
            this.prepaymentRechargeModificationForm.invoiceApplyDto.remitFile.push({
                name: res.data.fileName,
                url: res.data.fileUrl
            });
        },
        /**
         * 获取默认备注
         * @param invoiceApply
         * @param firstApproval
         * @returns {string}
         */
        getDefaultRemark(invoiceApply, firstApproval) {
            if (invoiceApply?.signCompany === '91620103773436199W') {
                return '销方开户行：兴业银行兰州铁路支行  账号：612080100100054750';
            } else if (invoiceApply?.signCompany === '91620103MA7CXHAU6L') {
                return '销方开户行：交通银行兰州七里河支行  账号：621060108013000552930';
            } else {
                return firstApproval.remark;
            }
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
        },
        /**
         * 根据状态获取提示信息
         * @param approveStatus
         * @param data
         * @returns {*|string}
         */
        getMessageBasedOnStatus(approveStatus, data) {
            if (approveStatus === '2') {
                return '操作成功';
            } else if (data.code === 200) {
                return '开票成功';
            } else {
                return data.msg;
            }
        },
        /**
         * 根据提示信息获取提示类型
         * @param message
         * @returns {string}
         */
        getMessageType(message) {
            return message === '开票成功' || message === '操作成功' ? 'success' : 'error';
        },
        /**
         * 获取开票审批详情
         */
        async getPrepaymentRechargeModificationInvoicingDetail(id) {
            this.prepaymentRechargeModificationLoading = true;
            try {
                const res = await paymentOrderApproval.getInvoiceApprovalDetail({ id });

                if (res.code === 200) {
                    const { invoiceApply, approveList } = res.data;
                    const firstApproval = approveList?.[0] || {};
                    const isTransportService = invoiceApply?.projectName === '运输服务费' || invoiceApply?.projectName === '国内运输费';

                    // 确保paymentApplyDTO存在
                    if (!res.data.paymentApplyDTO) {
                        res.data.paymentApplyDTO = {
                            advanceAccount: '-'
                        };
                    }

                    // 使用解构和Object.assign来保持响应式
                    this.prepaymentRechargeModificationForm = {
                        ...res.data,
                        invoiceApply: {
                            ...res.data.invoiceApply,
                            transWay: isTransportService && firstApproval.transWay ? firstApproval.transWay : '公路运输',
                            transCarNo: isTransportService && firstApproval.transCarNo ? firstApproval.transCarNo : '甘A7DB33',
                            startAddress: isTransportService && firstApproval.startAddress ? firstApproval.startAddress : '兰州',
                            endAddress: isTransportService && firstApproval.endAddress ? firstApproval.endAddress : '省内',
                            transGooods: isTransportService && firstApproval.transGooods ? firstApproval.transGooods : '药品'
                        },
                        remark: this.getDefaultRemark(invoiceApply, firstApproval)
                    };
                }
            } catch (error) {
                console.error('Error fetching invoicing details:', error);
            } finally {
                this.prepaymentRechargeModificationLoading = false;
            }
        },
        /**
         * 设置项目名称
         * @param projectName
         * @returns {string}
         */
        setProjectName(projectName) {
            if (projectName === '运输服务费' || projectName === '国内运输费') {
                return '运输服务';
            } else if (projectName === '收派服务费' || projectName === '仓储服务费') {
                return '物流辅助服务';
            }
            return projectName || '';
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.prepaymentRechargeModificationForm.validate(async (valid) => {
                if (valid) {
                    this.prepaymentRechargeModificationLoading = true;
                    const form = this.prepaymentRechargeModificationForm;
                    const params = {
                        id: form.invoiceApplyDto.id,
                        approveStatus: form.approveStatus,
                        approveIdea: form.approveIdea,
                        remark: form.remark,
                        invoiceRemark: form.invoiceApply.remark
                    };

                    // 只有在特定条件下才添加额外的运输信息
                    if (['运输服务费', '国内运输费'].includes(form.invoiceApply?.projectName)) {
                        params.transWay = form.invoiceApply.transWay;
                        params.transCarNo = form.invoiceApply.transCarNo;
                        params.startAddress = form.invoiceApply.startAddress;
                        params.endAddress = form.invoiceApply.endAddress;
                        params.transGooods = form.invoiceApply.transGooods;
                    }
                    const res = await InvoiceApproval.submitInvoiceApproval(params);
                    if (res.code !== 200) {
                        this.$message.error(res.data.msg);
                        this.prepaymentRechargeModificationLoading = false;
                        return;
                    }

                    const message = this.getMessageBasedOnStatus(form.approveStatus, res.data);
                    this.$message[this.getMessageType(message)](message);

                    this.$emit('getList');
                    this.closeVisible();
                    this.prepaymentRechargeModificationLoading = false;
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.number__unit__element {
    position: relative;

    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
