<template>
  <div class="diagnosisRange">
    <el-card class="box-card">
      <div class="header">
        <el-input
            v-model="data.searchStr"
            :prefix-icon="Search"
            class="w-50 m-2"
            placeholder="请输入范围名称查询"
            @keyup.enter.native="searchBtn()"
        />
        <el-button type="primary" @click="searchBtn()">搜索</el-button>
        <el-button
            style="float: right; margin: 0 10px 20px 0"
            type="primary"
            @click="newAll()"
        >新增
        </el-button
        >
      </div>

      <div class="main">
        <el-table
            ref="xTree"
            v-loading="loadingFlag"
            :data="data.tableData"
            :load="load"
            :tree-props="{
						children: 'children',
						hasChildren: 'hasChildren',
					}"
            border
            lazy
            row-key="id"
            style="width: 100%"
        >
          <el-table-column
              label="质量名称"
              min-width="250px"
              prop="massName"
          />
          <el-table-column
              label="质量编号"
              min-width="150px"
              prop="massNo"
          />
          <el-table-column
              label="属性分类"
              prop="massType"
          >
            <template #default="scope">
              {{ scope.row.isStop == 1 ? "新范围" : "旧范围" }}
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="isStop">
            <template #default="scope">
              {{ scope.row.isStop == 1 ? "正常" : "停用" }}
            </template>
          </el-table-column>

          <el-table-column
              label="备注"
              min-width="150px"
              prop="remark"
          />
          <el-table-column
              align="center"
              label="操作"
              prop="address"
              width="283px"
          >
            <template #default="scope">
              <el-button
                  text
                  type="success"
                  @click="newAdd(scope.row)"
              ><img
                  src="../../assets/icons/newAdd.png"
                  style="margin: 0px 3px 0 0"
              />新增
              </el-button
              >
              <el-button
                  text
                  type="primary"
                  @click="editData(scope.row)"
              ><img
                  src="@/assets/icons/update.png"
                  style="margin: 0px 3px 0 0"
              />编辑
              </el-button
              >
              <el-button
                  text
                  type="danger"
                  @click="delData(scope.row)"
              ><img
                  src="@/assets/icons/delete.png"
                  style="margin: 0px 5px 0 0"
              />删除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
          v-model:current-page="pages.current"
          v-model:page-size="pages.size"
          :background="true"
          :disabled="disabled"
          :page-sizes="[5, 10, 20, 50]"
          :small="small"
          :total="pages.total"
          layout="->,total, sizes, prev, pager, next, jumper"
          style="margin-top: 20px"
          @size-change="handleCurrentChange"
          @current-change="handleCurrentChange"
      />
    </el-card>

    <div class="function">
      <!-- 新增弹窗 -->
      <el-dialog v-model="dialogFormVisible1" title="新增" width="500px">
        <el-form
            ref="creatform1"
            :label-position="labelPosition"
            :model="newAddForm"
            :rules="creatRules1"
            label-width="100px"
            style="max-width: 90%; margin-top: 10px"
        >
          <el-form-item label="上级" prop="name">
            <el-tree-select
                v-model="newAddForm.parent"
                :data="data.treeList"
                :props="{ value: 'id', label: 'massName' }"
                :render-after-expand="false"
                check-strictly
                node-key="id"
                value-key="id"
            />
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input
                v-model="newAddForm.name"
                placeholder="请输入名称"
            />
          </el-form-item>
          <el-form-item label="编号" prop="number">
            <el-input
                v-model="newAddForm.number"
                placeholder="请输入编号"
            />
          </el-form-item>
          <el-form-item label="序号" prop="sort">
            <el-input
                v-model="newAddForm.sort"
                placeholder="请输入序号"
                type="number"
                @change="sortFlag(1)"
            />
          </el-form-item>
          <el-form-item label="状态" prop="state">
            <el-select
                v-model="newAddForm.state"
                class="m-2"
                placeholder="请选择状态"
            >
              <el-option label="启用" value="1"/>
              <el-option label="停用" value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item label="分类" prop="Type">
            <el-select
                v-model="newAddForm.Type"
                class="m-2"
                placeholder="请选择范围"
            >
              <el-option label="新范围" value="1"/>
              <el-option label="旧范围" value="2"/>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
                v-model="newAddForm.remark"
                :rows="5"
                placeholder="请输入备注"
                type="textarea"
            />
          </el-form-item>
        </el-form>
        <template #footer>
			<span class="dialog-footer"><el-button @click="dialogFormVisible1 = false">取消</el-button>
				<el-button type="primary" @click="newAddBtn(creatform1)">确定</el-button>
			</span>
        </template>
      </el-dialog>

      <!--修改弹窗-->
      <el-dialog v-model="dialogFormVisible2" title="新增" width="500px">
        <el-form
            ref="creatform2"
            :label-position="labelPosition"
            :model="data.editForm"
            :rules="creatRules2"
            label-width="100px"
            style="max-width: 90%; margin-top: 10px"
        >
          <el-form-item label="上级" prop="name">
            <el-tree-select
                v-model="data.editForm.parent"
                :data="data.treeList"
                :props="{ value: 'id', label: 'massName' }"
                :render-after-expand="false"
                check-strictly
                node-key="id"
                value-key="id"
            />
          </el-form-item>

          <el-form-item label="名称" prop="name">
            <el-input
                v-model="data.editForm.name"
                placeholder="请输入名称"
            />
          </el-form-item>
          <el-form-item label="编号" prop="number">
            <el-input
                v-model="data.editForm.number"
                placeholder="请输入编号"
            />
          </el-form-item>
          <el-form-item label="序号" prop="sort">
            <el-input
                v-model="data.editForm.sort"
                placeholder="请输入序号"
                type="number"
                @change="sortFlag(2)"
            />
          </el-form-item>
          <el-form-item label="分类" prop="state">
            <el-select
                v-model="data.editForm.Type"
                class="m-2"
                placeholder="请选择状态"
            >
              <el-option label="新范围" value="1"/>
              <el-option label="旧范围" value="2"/>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="state">
            <el-select
                v-model="data.editForm.state"
                class="m-2"
                placeholder="请选择状态"
            >
              <el-option label="启用" value="1"/>
              <el-option label="停用" value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
                v-model="data.editForm.remark"
                :rows="5"
                placeholder="请输入备注"
                type="textarea"
            />
          </el-form-item>
        </el-form>
        <template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogFormVisible2 = false">取消</el-button>
				<el-button type="primary" @click="editButton(creatform2)">确定</el-button>
			</span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect,} from "vue";
import {Search} from "@element-plus/icons-vue";
import {ElMessage, ElMessageBox} from "element-plus";
import {massRange} from "@/api/model/assist/massRange/index";

const small = ref(false);
const disabled = ref(false);
const dialogFormVisible1 = ref(false);
const dialogFormVisible2 = ref(false);
const xTree = ref(null);
const loadingFlag = ref(false);
const loadMap = new Map();
const labelPosition = ref("right"); //表格方向
const newAddForm = reactive({
  name: "",
  number: "",
  state: null,
  remark: "",
  parent: null,
  sort: null,
  Type: "",
});

const creatform1 = ref(); //验证表单/流程新增
const creatRules1 = reactive({
  name: [{required: true, message: "请输入名称", trigger: "blur"}],
  number: [{required: true, message: "请选择编号", trigger: "blur"}],
  state: [{required: true, message: "请选择状态", trigger: "blur"}],
  Type: [{required: true, message: "请选择范围", trigger: "blur"}],
  sort: [{required: true, message: "请选择序号", trigger: "blur"}],
});
const creatform2 = ref(); //验证表单/流程新增
const creatRules2 = reactive({
  name: [{required: true, message: "请输入名称", trigger: "blur"}],
  number: [{required: true, message: "请选择编号", trigger: "blur"}],
  state: [{required: true, message: "请选择状态", trigger: "blur"}],
  Type: [{required: true, message: "请选择范围", trigger: "blur"}],
  sort: [{required: true, message: "请选择序号", trigger: "blur"}],
});

const sortFlag = (num) => {
  if (newAddForm.sort <= 0 && num == 1) {
    newAddForm.sort = null;
    ElMessage({
      type: "warning",
      message: "序号需大于0",
    });
  }
  if (data.editForm.sort <= 0 && num == 2) {
    data.editForm.sort = null;
    ElMessage({
      type: "warning",
      message: "序号需大于0",
    });
  }
};

const pages = reactive({
  total: 0,
  size: 10,
  current: 1,
});
const data = reactive({
  tableData: [],
  newStr: {},
  childrenStr: {},
  editForm: {
    id: "",
    name: "",
    Type: "",
    number: "",
    state: null,
    remark: "",
    parent: "",
    sort: null,
    parentId: "",
  },
  searchStr: "",
  treeList: [],
});

const searchBtn = () => {
  massRange
      .diagnosisList({
        "parent.id": "0",
        massName: data.searchStr,
        size: pages.size,
        current: pages.current,
      })
      .then((res) => {
        console.log(res);
        if (res.code == 200) {
          pages.total = res.data.total;
          pages.size = res.data.size;
          pages.current = res.data.current;
          data.tableData = res.data.records;
          data.tableData.forEach((item) => {
            item.hasChildren = true;
          });
        }
      });
};
const newAdd = (row) => {
  dialogFormVisible1.value = true;
  newAddForm.parent = row.id;
  data.newStr = row;
};
const newAll = () => {
  dialogFormVisible1.value = true;
  newAddForm.parent = "0";
  data.newStr = null;
};
const newAddBtn = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      massRange
          .newDiagnosis({
            parent: {
              id: newAddForm.parent,
            },
            parentId: newAddForm.parent,
            massName: newAddForm.name,
            massNo: newAddForm.number,
            massType: newAddForm.Type,
            remark: newAddForm.remark,
            isStop: newAddForm.state,
            sort: newAddForm.sort,
          })
          .then((res) => {
            if (res.code == 200) {
              getData();
              tableUpdate();
              dialogFormVisible1.value = false;
              newAddForm.name = "";
              newAddForm.number = "";
              newAddForm.state = null;
              newAddForm.Type = null;
              newAddForm.remark = "";
              newAddForm.sort = null;
              ElMessage({
                type: "success",
                message: "添加成功",
              });
            } else {
              ElMessage({
                type: "error",
                message: "添加失败，请稍后重试",
              });
            }
          });
    }
  });
};
const editButton = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      massRange
          .newDiagnosis({
            parent: {
              id: data.editForm.parent,
            },
            parentId: data.editForm.parent,
            id: data.editForm.id,
            massName: data.editForm.name,
            massType: data.editForm.Type,
            massNo: data.editForm.number,
            remark: data.editForm.remark,
            isStop: data.editForm.state,
            sort: data.editForm.sort,
          })
          .then((res) => {
            if (res.code == 200) {
              getData();
              dialogFormVisible2.value = false;
              if (data.editForm.parentId != 0) {
                const lazyTreeNodeData =
                    xTree.value.store.states.lazyTreeNodeMap.value;
                const index = lazyTreeNodeData[
                    data.editForm.parentId
                    ].findIndex((item) => item.id === data.editForm.id);
                lazyTreeNodeData[data.editForm.parentId][
                    index
                    ].massName = data.editForm.name;
                lazyTreeNodeData[data.editForm.parentId][
                    index
                    ].massNo = data.editForm.number;
                lazyTreeNodeData[data.editForm.parentId][
                    index
                    ].massType = data.editForm.Type;
                lazyTreeNodeData[data.editForm.parentId][
                    index
                    ].remark = data.editForm.remark;
                lazyTreeNodeData[data.editForm.parentId][
                    index
                    ].sort = data.editForm.sort;
                lazyTreeNodeData[data.editForm.parentId][
                    index
                    ].isStop = data.editForm.state;
              }

              ElMessage({
                type: "success",
                message: "编辑成功",
              });
            } else {
              ElMessage({
                type: "error",
                message: "编辑失败，请稍后重试",
              });
            }
          });
    }
  });
};
const tableUpdate = () => {
  if (loadMap.get(newAddForm.parent)) {
    const {row, treeNode, resolve} = loadMap.get(newAddForm.parent);
    xTree.value.store.states.lazyTreeNodeMap[newAddForm.parent] = []; //清空节点的数据
    load(row, treeNode, resolve);
  }
};
const editData = (row) => {
  dialogFormVisible2.value = true;
  data.editForm.parent = row.parentId;
  data.editForm.parentId = row.parentId;
  data.editForm.id = row.id;
  data.editForm.name = row.massName;
  data.editForm.Type = row.massType;
  data.editForm.state = row.isStop;
  data.editForm.remark = row.remark;
  data.editForm.number = row.massNo;
  data.editForm.sort = row.sort;
};
const handleCurrentChange = () => {
  getData();
};
const load = (row, treeNode, resolve) => {
  loadMap.set(row.id, {row, treeNode, resolve});
  massRange
      .diagnosisList({
        "parent.id": row.id,
        size: 1000
      })
      .then((res) => {
        if (res.code == 200 && res.data.records.length > 0) {
          let newData = res.data.records;
          newData.forEach((record) => {
            record.hasChildren = true;
          });
          resolve(newData);
        } else {
          resolve([]);
          ElMessage({
            type: "warning",
            message: "无数据",
          });
        }
      });
};

const delData = (row) => {
  ElMessageBox.confirm("确认删除吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        massRange
            .delDiagnosis({
              ids: row.id,
            })
            .then((res) => {
              if (res.code == 200) {
                getData();
                if (row.parentId != 0) {
                  const lazyTreeNodeData =
                      xTree.value.store.states.lazyTreeNodeMap.value;
                  const index = lazyTreeNodeData[
                      row.parentId
                      ].findIndex((item) => item.id === row.id);
                  lazyTreeNodeData[row.parentId].splice(index, 1);
                }

                ElMessage({
                  type: "success",
                  message: "删除成功",
                });
              } else {
                ElMessage({
                  type: "success",
                  message: "删除失败，请稍后重试",
                });
              }
            });
      })
      .catch(() => {
      });
};
const getData = () => {
  loadingFlag.value = true;
  massRange
      .diagnosisList({
        "parent.id": "0",
        size: pages.size,
        current: pages.current,
      })
      .then((res) => {
        if (res.code == 200) {
          massRange.diagnosisTreeList().then((res) => {
            data.treeList = [];
            data.treeList.push({
              massName: "根节点",
              id: "0",
              children: res.data,
            });
          });
          pages.total = res.data.total;
          pages.size = res.data.size;
          pages.current = res.data.current;
          data.tableData = res.data.records;
          data.tableData.forEach((item) => {
            item.hasChildren = true;
          });
        }
        loadingFlag.value = false;
      });
};
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  getData();
});
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data),
});
</script>
<style lang="scss" scoped>
.diagnosisRange {
  width: 100%;
  height: 100%;
  padding: 10px;
}

.header {
  margin: 10px 0 20px 0;

  .el-input {
    width: 300px;
  }

  .el-button {
    margin-left: 5px;
    height: 31px;
    font-weight: 400;
  }

  span {
    float: right;
    margin: 0 10px 0 0;
  }
}

.el-button--text {
  margin-right: 15px;
}

.el-select {
  width: 300px;
}

.el-input {
  width: 300px;
}

.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>
