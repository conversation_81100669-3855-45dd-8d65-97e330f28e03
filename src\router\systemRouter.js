/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-05 14:03:11
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-29 11:25:01
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\router\systemRouter.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import config from "@/config"

//系统路由
const routes = [
	{
		path: '/redirect',
		component: () => import(/* webpackChunkName: "layout" */ '@/layout'),
		hidden: true,
		children: [
		  {
			path: '/redirect/:path(.*)',
			component: (resolve) => require(['@/views/redirect'], resolve)
		  }
		]
	  },
	{
		name: "layout",
		path: "/",
		component: () => import(/* webpackChunkName: "layout" */ '@/layout'),
		redirect: config.DASHBOARD_URL || '/home',
		children: []
	},
	{
		path: "/login",
		component: () => import(/* webpackChunkName: "login" */ '@/views/login'),
		meta: {
			title: "登录"
		}
	},
	{
		path: "/user_register",
		component: () => import(/* webpackChunkName: "userRegister" */ '@/views/login/userRegister'),
		meta: {
			title: "注册"
		}
	},
	// {
	// 	path: "/reset_password",
	// 	component: () => import(/* webpackChunkName: "resetPassword" */ '@/views/login/resetPassword'),
	// 	meta: {
	// 		title: "重置密码"
	// 	}
	// }
	// 下载司机端APP
	{
		path: "/driverSideDownload",
		component: () => import(/* webpackChunkName: "downloadApp" */ '@/views/home/<USER>'),
		meta: {
			title: "下载司机端APP"
		}
	},
	// 下载集散端下APP
	{
		path: "/distributedDownload",
		component: () => import(/* webpackChunkName: "downloadApp" */ '@/views/home/<USER>'),
		meta: {
			title: "下载集散端下APP"
		}
	},
]

export default routes;
