<template>
    <div>
        <el-drawer v-if="visible" v-model="visible" :title="auditTitle" size="770px" @close="closeVisible">
            <div style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="preDepositTopUpAuditForm" :model="preDepositTopUpAuditForm" label-width="auto">
                        <el-descriptions :column="2" border size="small" title="申请信息">
                            <el-descriptions-item label="付款公司">{{ taskInfo.paymentApplyDto.companyName }}</el-descriptions-item>
                            <el-descriptions-item label="预付款账号">{{ taskInfo.paymentApplyDto.advanceAccount }}</el-descriptions-item>
                            <el-descriptions-item label="收款公司" span="2">{{ selectDictLabel(companyList, taskInfo.paymentApplyDto.receiveCompany) }}</el-descriptions-item>
                            <el-descriptions-item label="汇款金额">{{ taskInfo.paymentApplyDto.remitAmount }}</el-descriptions-item>
                            <el-descriptions-item label="汇款时间">{{ taskInfo.paymentApplyDto.remitTime }}</el-descriptions-item>
                            <el-descriptions-item label="付款凭证" span="2">
                                <el-image v-for="(item, index) in taskInfo.paymentApplyDto.remitFile" :key="index" :preview-src-list="[item.fileUrl]" :src="item.fileUrl" class="mr-5" fit="cover" style="width: 100px; height: 100px" />
                            </el-descriptions-item>
                            <el-descriptions-item label="备注">{{ taskInfo.paymentApplyDto.remark }}</el-descriptions-item>
                        </el-descriptions>
                        <el-form-item v-if="auditInfo.auditStatus == '0'" :rules="{ required: true, message: '请输入返利金额', trigger: 'blur' }" class="mt-10" label="返利金额" prop="rebateAmount">
                            <el-input-number v-model="preDepositTopUpAuditForm.rebateAmount" :max="99999999" :min="0" :precision="2" class="w-50 number__unit__element" controls-position="right" placeholder="请输入返利金额" />
                        </el-form-item>
                        <el-descriptions border class="mt-10 mb-10" size="small" title="审批">
                            <el-descriptions-item label="申请人">{{ taskInfo.paymentApplyDto.createBy.name }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ taskInfo.paymentApplyDto.createDate }}</el-descriptions-item>
                        </el-descriptions>
                        <audit-and-flow-records v-show="taskInfo?.approveList?.length > 0" ref="AuditAndFlowRecords"></audit-and-flow-records>
                        <div v-if="auditInfo.auditStatus == '0'  && type === 'examine'" class="mt-10">
                            <el-form-item :rules="{ required: true, message: '请选择审批意见', trigger: 'change' }" label="审批意见" prop="status">
                                <el-radio-group v-model="preDepositTopUpAuditForm.status">
                                    <el-radio label="1">通过</el-radio>
                                    <el-radio label="2">驳回</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item v-if="preDepositTopUpAuditForm?.status === '2'" :rules="{ required: true, message: '请输入驳回原因', trigger: 'blur' }" label="驳回原因" prop="auditIdea">
                                <el-input v-model="preDepositTopUpAuditForm.auditIdea" :rows="4" maxlength="500" placeholder="请输入驳回原因" show-word-limit type="textarea"></el-input>
                            </el-form-item>
                        </div>
                    </el-form>
                </el-card>
            </div>
            <template #footer v-if="auditInfo.auditStatus == '0'">
                <div v-loading="preDepositTopUpAuditLoading" style="text-align: right; margin-top: 10px">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm">提 交</el-button>
                </div>
            </template>
        </el-drawer>
    </div>
</template>
<script>
import { tasks } from '@/api/auditManagement/approvalTask';
import AuditAndFlowRecords from '@/views/auditManagement/AuditAndFlowRecords';

export default {
    name: 'PreDepositTopUpAudit',
    components: { AuditAndFlowRecords },
    model: {
        prop: 'preDepositTopUpAuditVisible',
        event: 'update:preDepositTopUpAuditVisible'
    },
    props: {
        taskInfo: {},
        auditInfo: {},
        auditTitle: {
            type: String,
            default: undefined
        },
        preDepositTopUpAuditVisible: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'details'
        }
    },
    data() {
        return {
            visible: this.preDepositTopUpAuditVisible,
            preDepositTopUpAuditForm: {
                status: undefined,
                auditIdea: undefined,
                rebateAmount: undefined
            },
            preDepositTopUpAuditLoading: false,
            companyList: []
        };
    },
    watch: {
        preDepositTopUpAuditVisible: {
            handler(val) {
                this.visible = val;
            },
            immediate: true
        }
    },
    created() {
        this.getDict();
        this.$nextTick(() => {
            if (this.taskInfo.approveList.length > 0) {
                this.$refs.AuditAndFlowRecords.timeFns(this.taskInfo.approveList);
            }
        });
    },
    methods: {
        /**
         * 关闭抽屉
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:preDepositTopUpAuditVisible', false);
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.preDepositTopUpAuditForm.validate(async (valid) => {
                if (valid) {
                    this.preDepositTopUpAuditLoading = true;
                    const params = {
                        taskId: this.auditInfo.id,
                        auditIdea: this.preDepositTopUpAuditForm.auditIdea,
                        status: this.preDepositTopUpAuditForm.status,
                        rebateAmount: this.preDepositTopUpAuditForm.rebateAmount
                    };
                    tasks
                        .auditSubmit(params)
                        .then((res) => {
                            if (res.code == 200) {
                                this.$message.success('操作成功');
                                this.$emit('success');
                                this.closeVisible();
                            }
                        })
                        .finally(() => {
                            this.preDepositTopUpAuditLoading = false;
                        });
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
}
.number__unit__element {
    position: relative;

    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
