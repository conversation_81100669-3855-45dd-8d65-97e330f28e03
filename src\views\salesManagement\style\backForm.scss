.formsDiv {
	margin-top: 20px;
}

.el-table {
	margin-top: 10px;
}

.BINP {
	width: 88.7%
}

.total {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	margin: 0px 20px;

	p {
		margin-right: 50px;
		margin-top: 20px;

		& span:nth-of-type(1) {
			font-size: 15px;
			font-weight: bold;
			color: #333;
			display: inline-block;
			width: 130px;
			text-align: right;
		}

		.red {
			font-size: 16px;
			font-weight: bold;
			color: red;
		}
	}
}

.rowStyle {
	.el-col {
		margin-top: 20px;
		font-size: 15px;

		.rowTitle {
			width: 120px;
			text-align: right;
			display: inline-block;
			font-size: 15px;
			font-weight: bolder;
			color: #000;
		}

		.rowMess {
			color: #4d4d4d;
			font-weight: 600;
		}

		.rowRed {
			color: red;
		}
	}
}

.messTable {
	width: 100%;
	background-color: #eaedf3;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	padding: 1px 1px 0 1px;

	tr {
		margin-bottom: 1px;
		display: flex;

		td {
			background-color: white;
			line-height: 40px;
		}

		td:nth-child(1) {
			flex: 1;
			padding: 0 10px;
			font-weight: bold;
			color: #505050;
			background: #f7f7f7;
		}

		td:nth-child(2) {
			color: #606266;
			padding: 0 10px;
			flex: 2
		}
	}
}

.detailTable {
	width: 100%;
	background-color: #eaedf3;
	font-size: 14px;
	border-radius: 5px;

	tr {
		height: 40px;

		td {
			background-color: white;
		}

		td:nth-child(1) {
			padding: 0 10px;
			font-weight: bold;
			width: 20%;
			color: #505050;
			background: #f7f7f7;
		}

		td:nth-child(2) {
			width: 80%;
			color: #606266;
			padding: 0 10px;
		}
	}

}

h3 {
	color: black;
}

.zhe {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.6);

	.imgDiv {
		max-width: 70%;
		max-height: 70%;

		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);

		img {
			width: 100%;
			height: 100%;
			min-width: 200px;
			color: #fff;
		}

		span {
			position: absolute;
			font-size: 25px;
			border-radius: 50%;
			height: 30px;
			width: 30px;
			line-height: 34px;
			text-align: center;
			color: #fff;
			right: -30px;
			top: -5px;
		}
	}
}

.el-select-dropdown__list {
	.el-input {
		width: 90%;
		margin-left: 5%;
		margin-top: 5px;
		margin-bottom: 15px;
	}

	.el-pagination {
		margin-right: 20px;
		margin-top: 10px;
		margin-bottom: 10px;
	}
}
