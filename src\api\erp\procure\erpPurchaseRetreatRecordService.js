import http from "@/utils/request"

export default {
  save: function (inputForm) {
    return http.post(
      '/erp/procure/retreat/erpPurchaseRetreatOutbound/save',
      inputForm
    )
  },
  export:function (params) {
    return http.post(
      '/file/importRecordeDownloadFile',
      params, { responseType: 'blob' },true
    )
  },
  delete: function (ids) {
    return http.delete(
      '/erp/procure/retreat/erpPurchaseRetreatOutbound/delete',
      {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
      '/erp/procure/retreat/erpPurchaseRetreatOutbound/queryById',
      {id: id}
    )
  },

  list: function (params) {
    return http.get(
      '/erp/procure/retreat/erpPurchaseRetreatOutbound/list',
      params
    )
  },

  exportTemplate: function () {
    return http.get(
      '/erp/procure/retreat/erpPurchaseRetreatOutbound/import/template',
      'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
      '/erp/procure/retreat/erpPurchaseRetreatOutbound/export',
      params,
      'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
      '/erp/procure/retreat/erpPurchaseRetreatOutbound/import',
      data
    )
  }
}
