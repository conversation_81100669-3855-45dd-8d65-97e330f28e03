<template>
  <div id="edior" style="height: 600px;width: 900px">
    <div id="tui-image-editor"></div>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import 'tui-image-editor/dist/tui-image-editor.css'
import 'tui-color-picker/dist/tui-color-picker.css'

const ImageEditor = require('tui-image-editor')
const locale_ru_RU = {
  // override default English locale to your custom
  'Crop': '裁剪',
  'Delete': '删除',
  'Delete-all': '删除所有',
  "Load": "选择图片",
  'Download': '下载图片',
  'Sharpen': '锐化',
  'Emboss': '浮雕',
  'Blur': '模糊',
  'Sepia': '深褐色',
  'Sepia2': '深褐色2',
  'Invert': '反色',
  'Grayscale': '灰度化',
  'Remove White': '移除白色',
  'Threshold': '阈值',
  'Distance': '距离',
  'Filter': '滤镜',
  'Color Filter': '颜色滤镜',
  'Pixelate': '像素化',
  'Noise': '噪点',
  'Brightness': '亮度',
  'Redo': '重做',
  'Undo': '撤销',
  'Reset': '重设',
  'Flip': '翻转',
  'Rotate': '旋转',
  'Draw': '绘画',
  'Shape': '图形',
  'Icon': '图标',
  'Text': '文字',
  'Mask': '遮罩',
  'Apply': '应用',
  'Cancel': '取消',
  'Custom': '自定义',
  'Square': '正方形',
  'Load Mask Image': '选择遮罩图片',
  'Range': '角度',
  'Color': '颜色',
  'Fill': '填充',
  'Stroke': '边框',
  'Rectangle': '长方形',
  'Circle': '圆形',
  'Triangle': '三角形',
  'Bold': '加粗',
  'Italic': '倾斜',
  'Underline': '下横线',
  'Left': '左对齐',
  'Center': '居中',
  'Right': '右对齐',
  'Text size': '文本大小',
  'Arrow': '箭头',
  'Arrow-2': '箭头2',
  'Arrow-3': '箭头3',
  'Value': '值',
  'Custom icon': '自定义图标',
  'Gradient transparency': '渐变透明',
  'Blend': '混合',
  'Multiply': '叠加',
  'Tint': '色调',
  'Heart': '心',
  'Bubble': '泡泡',
  'Location': '位置',
  'Polygon': '多变形',
  'Star-1': '星星1',
  'Star-2': '星星2',
  'Free': '自由',
  'Straight': '直线',
  'ZoomIn': "放大",
  'ZoomOut': "缩小",
  'Hand': "移动",
  'History': "历史",
  'DeleteAll': "删除全部"
  // etc...
};
const blackTheme = {
  // image 坐上角度图片
  "common.bi.image": "", // 在这里换上你喜欢的logo图片
  "common.bisize.width": "0px",
  "common.bisize.height": "0px",
  "common.backgroundImage": "none",
  "loadButton.display": "none", // 可以直接隐藏掉
  "downloadButton.display": "none", // 可以直接隐藏掉
}
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const data = reactive({})
const emit = defineEmits([])
const props = defineProps({
  imageUrl: {
    default: ""
  }
})
const instance = ref(null)
const {imageUrl} = toRefs(props)
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  instance.value = new ImageEditor(document.querySelector('#tui-image-editor'), {
    includeUI: {
      loadImage: {
        path: imageUrl.value,
        name: 'SampleImage',
      },
      locale: locale_ru_RU,
      theme: blackTheme, // or whiteTheme
      initMenu: '',
      menuBarPosition: 'bottom',
      menu: [
        "crop", // 裁切
        "flip", // 翻转
        "rotate", // 旋转
        // "draw", // 添加绘画
        // "shape", // 添加形状
        // "icon", // 添加图标
        // "text", // 添加文本
        // "mask", // 添加覆盖
        "filter", // 添加滤镜
      ],
    },
    cssMaxWidth: 900,
    cssMaxHeight: 450,
    selectionStyle: {
      cornerSize: 20,
      rotatingPointOffset: 70,
    },
  });
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  instance
})

</script>
<style lang='scss' scoped>
::v-deep .tui-image-editor-submenu {
  padding-top: 20px !important;
}

::v-deep .tie-btn-filter {
  display: none !important;
}
</style>
