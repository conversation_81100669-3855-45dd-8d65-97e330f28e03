<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="收款单号" prop="paymentOrderNo" style="width: 250px">
                    <el-input v-model="queryForm.paymentOrderNo" clearable placeholder="请输入收款单号" @clear="handleChangesTheOrderType('queryForm')" @keyup.enter="handleChangesTheOrderType('queryForm')"></el-input>
                </el-form-item>
                <el-form-item label="账单月度" prop="billDate" style="width: 200px">
                    <el-date-picker v-model="queryForm.billDate" class="w-full" placeholder="选择账单月度" type="month" value-format="YYYY-MM" @change="handleChangesTheOrderType('queryForm')"></el-date-picker>
                </el-form-item>
                <el-form-item label="付款单状态" prop="status" style="width: 250px">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择付款单状态" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in statusList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="是否发起付款申请" prop="applyStatus">
                    <el-select v-model="queryForm.applyStatus" clearable placeholder="请选择是否发起付款申请" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in applyStatusList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="是否发起开票申请" prop="invoicingStatus">
                    <el-select v-model="queryForm.invoicingStatus" clearable placeholder="请选择是否发起开票申请" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in invoiceStatusList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="承运商名称" prop="carrierId">
                    <el-select v-model="queryForm.carrierId" clearable filterable placeholder="请选择承运商名称" style="width: 100%" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in carrierList" :key="item.carrierId" :label="item.carrierName" :value="item.carrierId"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleChangesTheOrderType('queryForm')" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb-10">
                <!--<el-button :disabled="multiple" type="primary" @click="openInvoicing">申请开票</el-button>-->
                <!--<el-button :disabled="multiple" type="primary">批量支付</el-button>-->
                <!--<el-button :disabled="multiple" type="primary">全部导出</el-button>-->
                <el-button :disabled="multiple" type="primary" @click="handleClickSubmit">付款信息提交</el-button>
                <el-button :disabled="multiple" type="primary" @click="onOpenInvoice">申请开票</el-button>
                <el-button :disabled="!orderList || orderList.length === 0" icon="el-icon-download" type="warning" @click="handleExportAll">全部导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="paymentDocTable" @queryTable="getList" />
            </div>
            <column-table ref="paymentDocTable" v-loading="loading" :columns="columns" :data="orderList" :max-height="600" :show-summary="true" :showCheckBox="true" @selection-change="handleSelectionChange">
                <template #paymentDocType="{ row }">
                    <span>{{ formatDictionaryData('paymentReceiptType', row.paymentDocType) }}</span>
                </template>
                <template #applyStatus="{ row }">
                    <span :style="setApplyStatusColor(row.applyStatus)">{{ formatDictionaryData('applyStatusList', row.applyStatus) }}</span>
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ formatDictionaryData('statusList', row.status) }}</span>
                </template>
                <template #invoicingStatus="{ row }">
                    <span :style="setInvoicingStatusColor(row.invoicingStatus)">{{ formatDictionaryData('invoiceStatusList', row.invoicingStatus) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickBillingDetails(row)">账单明细</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!--  /账单明细   -->
        <el-drawer v-if="billDetailsVisible" v-model="billDetailsVisible" size="1200px" title="账单明细" @close="hideBillDetails">
            <div style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="billDetailsForm" :inline="true" :model="billDetailsForm" class="seache-form" label-width="auto">
                        <el-form-item label="订单号" prop="orderNo">
                            <el-input v-model="billDetailsForm.orderNo" clearable placeholder="请输入订单号" @clear="handleChangesTheOrderType('billDetailsForm')" @keyup.enter="handleChangesTheOrderType('billDetailsForm')"></el-input>
                        </el-form-item>
                        <el-form-item label="承运商名称">
                            <span>{{ billDetailsRow.carrierName }}</span>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="billDetailsForm.transType" clearable placeholder="请选择运输类型" style="width: 100%" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in productTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productType">
                            <el-select v-model="billDetailsForm.productType" clearable placeholder="请选择产品分类" style="width: 100%" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温区类型" prop="temperatureType">
                            <el-select v-model="billDetailsForm.temperatureType" clearable placeholder="请选择温区类型" style="width: 100%" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in temperatureTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="对账单状态" prop="status">
                            <el-select v-model="billDetailsForm.status" clearable placeholder="请选择对账单状态" @change="handleChangesTheOrderType('billDetailsForm')">
                                <el-option v-for="item in orderStatusList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单月度" prop="billDate">
                            <el-date-picker v-model="billDetailsForm.billDate" class="w-full" placeholder="选择账单月度" type="month" value-format="YYYY-MM" @change="handleChangesTheOrderType('billDetailsForm')"></el-date-picker>
                        </el-form-item>
                        <el-form-item style="margin-left: auto; margin-right: 0; margin-bottom: 0">
                            <el-button icon="el-icon-search" type="primary" @click="handleChangesTheOrderType('billDetailsForm')">搜索</el-button>
                            <el-button icon="el-icon-refresh" type="info" @click="resetBillDetailsForm">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <column-table :columns="billDetailsColumns" :data="billDetailsList" :loading="billDetailsLoading">
                        <template #transType="{ row }">
                            <span>{{ formatDictionaryData('productTypeList', row.transType) }}</span>
                        </template>
                        <template #temperatureType="{ row }">
                            <span>{{ formatDictionaryData('temperatureTypeList', row.temperatureType) }}</span>
                        </template>
                        <template #settlementMethod="{ row }">
                            <span>{{ formatDictionaryData('settlementMethodList', row.settlementMethod) }}</span>
                        </template>
                        <template #productType="{ row }">
                            <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                        </template>
                        <template #status="{ row }">
                            <div v-html="formatStatus(row.status)"></div>
                        </template>
                        <template #collectFormula="{ row }">
                            <el-popover placement="top" width="400">
                                <pre>{{ row.collectFormula }}</pre>
                                <template #reference>
                                    <el-link size="small">查看</el-link>
                                </template>
                            </el-popover>
                        </template>
                        <template #transportFormula="{ row }">
                            <el-popover placement="top" width="400">
                                <pre>{{ row.transportFormula }}</pre>
                                <template #reference>
                                    <el-link size="small">查看</el-link>
                                </template>
                            </el-popover>
                        </template>
                        <template #deliveryFormula="{ row }">
                            <el-popover placement="top" width="400">
                                <pre>{{ row.deliveryFormula }}</pre>
                                <template #reference>
                                    <el-link size="small">查看</el-link>
                                </template>
                            </el-popover>
                        </template>
                        <template #opt="{ row }">
                            <el-button icon="el-icon-info-filled" link plain size="small" type="primary" @click="handleClickFeeDetails(row)">费用详情</el-button>
                        </template>
                    </column-table>
                </el-card>
                <el-card v-if="badDebtRegistrationList.length > 0" :body-style="{ padding: '10px' }" shadow="never" style="margin-top: 10px">
                    <h3 class="mb10">坏账登记信息</h3>
                    <column-table :columns="badDebtRegistrationColumns" :data="badDebtRegistrationList" :element-loading-text="badDebtRegistrationText" :loading="badDebtRegistrationLoading">
                        <template #type="{ row }">
                            <span>{{ formatDictionaryData('badDebtTypeList', row.type) }}</span>
                        </template>
                        <template #createDate="{ row }">
                            <span>{{ row.createDate ? formatDate(row.createDate) : '' }}</span>
                        </template>
                        <template #opt="{ row }">
                            <el-button link size="small" type="primary" @click="handleClickBadDebtRegistration(row)">查看</el-button>
                        </template>
                    </column-table>
                </el-card>
            </div>
        </el-drawer>

        <!--  /订单费用详情  -->
        <order-fee-details-with-details v-if="orderCostVisible" v-model="orderCostVisible" :detail-data="detailData" :fee-breakdown-data="feeBreakdownData" />

        <!-- /图片预览 -->
        <el-image-viewer v-if="dialogVisible" :initial-index="0" :url-list="dialogImageUrl" :z-index="9999" @close="imgClose" />

        <!-- 提交付款信息 -->
        <payment-submission v-if="applyPrepaymentVisible" v-model="applyPrepaymentVisible" :initial-form-data="applyPrepaymentForm" @close="onCloseApplyPrepayment" @submit="onSubmitApplyPrepayment" />

        <!-- 开票 -->
        <invoice-drawer v-if="invoiceVisible" v-model="invoiceVisible" :initial-form-data="invoiceForm" :invoice-list="invoiceList" :is-collect-payment="isCollectPayment" @close="closeInvoice" @submit="submitInvoice" />

        <!-- 汇款金额与收款单金额不匹配 弹窗组件 -->
        <mismatch-dialog v-if="mismatchDialogVisible" v-model="mismatchDialogVisible" :initial-form-data="mismatchFormData" @close="closeMismatchDialog" @submit="submitMismatchDialog" />
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import moment from 'moment';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import paymentDoc from '@/api/shipperEnd/paymentDoc';
import OrderFeeDetailsWithDetails from '@/views/carrierFunction/OrderFeeDetailsWithDetails.vue';
import customerPaymentDocument from '@/api/carrierEnd/customerPaymentDocument';
import RightToolbar from '@/components/RightToolbar/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import tool from '@/utils/tool';
import { Close, Delete, Plus } from '@element-plus/icons-vue';
import invoiceInformationMaintenance from '@/api/shipperEnd/invoiceInformationMaintenance';
import customerPrepaymentBalance from '@/api/shipperEnd/customerPrepaymentBalance';
import PaymentSubmission from '@/components/PaymentSubmission.vue';
import InvoiceDrawer from '@/components/InvoiceDrawer.vue';
import { downloadNoData } from '@/utils';
import MismatchDialog from '@/components/MismatchDialog.vue';

export default {
    name: 'PaymentDoc',
    components: {
        MismatchDialog,
        RightToolbar,
        SearchButton,
        ColumnTable,
        OrderFeeDetailsWithDetails,
        Plus,
        Close,
        Delete,
        PaymentSubmission,
        InvoiceDrawer
    },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                paymentOrderNo: null,
                carrierId: null,
                status: null,
                invoicingStatus: null,
                applyStatus: null,
                billDate: undefined
            },
            carrierList: [],
            statusList: [],
            invoiceStatusList: [],
            total: 0,
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            columns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', width: '120px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '承运商名称', key: 'carrierName', align: 'center', width: '220px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', width: '220px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'billDate', align: 'center', width: '200px', columnShow: true },
                { title: '付款类型', key: 'paymentDocType', align: 'center', width: '100px', columnShow: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣合计', key: 'discountCost', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '其他服务费', key: 'warehouseFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true },
                { title: '纸箱费', key: 'cartonFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true },
                { title: '垫付费', key: 'advanceFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true },
                { title: '租箱费', key: 'rentalBoxFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true },
                { title: '其他费用', key: 'otherFee', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true },
                { title: '费用说明', key: 'feeDesc', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '坏账总金额', key: 'badDebtCost', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', width: '160px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '是否发起付款申请', key: 'applyStatus', align: 'center', width: '125px', columnShow: true, fixed: 'right' },
                { title: '付款单状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right' },
                { title: '是否发起开票申请', key: 'invoicingStatus', align: 'center', width: '125px', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '120px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            orderList: [],
            loading: false,
            billDetailsVisible: false,
            billDetailsForm: {
                current: 1,
                size: 10,
                orderNo: '',
                transType: '',
                productType: '',
                temperatureType: '',
                status: '',
                billDate: undefined
            },
            settlementMethodList: [],
            orderStatusList: [],
            billDetailsColumns: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '120px', columnShow: true, fixed: 'left' },
                { title: '调整流水号', key: 'adjustSerialNumber', align: 'center', width: '180px', columnShow: true },
                { title: '承运商名称', key: 'carrierName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '下单时间', key: 'orderDate', align: 'center', width: '120px', columnShow: true },
                { title: '运输类型', key: 'transType', align: 'center', width: '120px', columnShow: true },
                { title: '产品分类', key: 'productType', align: 'center', width: '120px', columnShow: true },
                { title: '温区类型', key: 'temperatureType', align: 'center', width: '120px', columnShow: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '120px', columnShow: true },
                { title: '结算方式', key: 'settlementMethod', align: 'center', width: '120px', columnShow: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收费用', key: 'collectCost', align: 'center', width: '120px', columnShow: true },
                { title: '揽收费用计算公式', key: 'collectFormula', align: 'center', width: '130px', columnShow: true },
                { title: '干线费用', key: 'transportCost', align: 'center', width: '120px', columnShow: true },
                { title: '干线费用计算公式', key: 'transportFormula', align: 'center', width: '130px', columnShow: true },
                { title: '配送费用', key: 'deliveryCost', align: 'center', width: '120px', columnShow: true },
                { title: '配送费用计算公式', key: 'deliveryFormula', align: 'center', width: '130px', columnShow: true },
                { title: '超区费用', key: 'exceedCountyCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true },
                { title: '增值服务总费用', key: 'addedServicesCost', align: 'center', width: '120px', columnShow: true },
                { title: '折扣金额', key: 'discountCost', align: 'center', width: '120px', columnShow: true },
                // { title: '异动费用', key: 'abnormalCost', align: 'center', width: '120px', columnShow: true },
                { title: '预估费用合计', key: 'estimateCost', align: 'center', width: '120px', columnShow: true },
                { title: '合同价合计', key: 'totalContractPrice', align: 'center', width: '160px', columnShow: true },
                { title: '应收合计', key: 'totalCost', align: 'center', width: '120px', columnShow: true },
                { title: '对账单状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            billDetailsList: [],
            billDetailsLoading: false,
            billDetailsTotal: 0,
            dataInvoicing: [],
            goodsTypeList: [],
            productTypeList: [],
            orderCostVisible: false,
            detailData: {},
            feeBreakdownData: [],
            temperatureTypeList: [],
            isShowAll: false,
            billDetailsRow: null,
            badDebtRegistrationColumns: [
                { title: '登记时间', key: 'createDate', align: 'center', width: '110px', columnShow: true },
                { title: '登记人', key: 'createName', align: 'center', width: '180px', columnShow: true },
                { title: '坏账金额', key: 'amount', align: 'center', width: '200px', columnShow: true },
                { title: '坏账类型', key: 'type', align: 'center', width: '120px', columnShow: true },
                { title: '坏账说明', key: 'reason', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '坏账资料', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            badDebtRegistrationList: [],
            badDebtRegistrationText: '加载中...',
            badDebtRegistrationLoading: false,
            dialogImageUrl: [],
            dialogVisible: false,
            badDebtTypeList: [],
            applyPrepaymentVisible: false,
            applyPrepaymentForm: {
                companyName: undefined,
                receiveCompany: undefined,
                remitAmount: undefined,
                remitTime: undefined,
                remitFile: []
            },
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            invoiceList: [],
            invoiceVisible: false,
            invoiceForm: {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined
            },
            paymentReceiptType: [],
            applyStatusList: [],
            mismatchDialogVisible: false,
            mismatchFormData: {
                selectedData: [],
                receivableCost: 0,
                unpaidCost: 0,
                remitAmount: 0
            }
        };
    },
    computed: {
        /**
         * 格式化时间
         */
        formatDate() {
            return (value) => {
                if (!value) {
                    return '-';
                }
                return moment(value).format('YYYY-MM-DD');
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        // 格式化对账单状态
        formatStatus() {
            return (value) => {
                const statusText = this.formatDictionaryData('orderStatusList', value);
                if (value === '0') {
                    return `<span style="color: #B1B1B1">${statusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #F4AC00">${statusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #5670FE">${statusText}</span>`;
                } else if (value === '3') {
                    return `<span style="color: #1ACD7E">${statusText}</span>`;
                } else {
                    return statusText;
                }
            };
        },
        // 判断是否为到付类型
        isCollectPayment() {
            return this.selectData[0]?.paymentDocType === '4';
        },
        /**
         * 设置是否发起付款申请颜色
         */
        setApplyStatusColor() {
            return (status) => {
                return (
                    {
                        '1': { color: '#606266' }, // 待申请
                        '2': { color: '#1ACD7E' } // 已申请
                    }[status] || { color: '#999' }
                );
            };
        },
        /**
         * 设置是否发起开票申请颜色
         */
        setInvoicingStatusColor() {
            return (status) => {
                return (
                    {
                        '1': { color: '#606266' }, // 未发起
                        '2': { color: '#1ACD7E' } // 已发起
                    }[status] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#606266' }, // 未付款
                        '1': { color: '#F4AC00' }, // 部分付款
                        '2': { color: '#1ACD7E' } // 已付款
                    }[status] || { color: '#999' }
                );
            };
        }
    },
    created() {
        this.getDict();
        // billDate 默认上一月
        this.queryForm.billDate = moment(new Date()).subtract(1, 'months').format('YYYY-MM');
        this.applyPrepaymentForm.remitTime = moment().format('YYYY-MM-DD HH:mm:ss');
        this.handleChangesTheOrderType('queryForm');
    },
    methods: {
        /**
         * 关闭开票
         */
        closeInvoice() {
            this.invoiceVisible = false;
            this.invoiceForm = {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined
            };
        },
        /**
         * 关闭汇款金额与收款单金额不匹配 弹窗
         */
        closeMismatchDialog() {
            this.mismatchDialogVisible = false;
            this.mismatchFormData = {
                selectedData: [],
                receivableCost: 0,
                remitAmount: 0
            };
        },
        /**
         * 上传成功
         */
        fileUploadSuccess(res) {
            this.applyPrepaymentForm.remitFile.push({
                fileUrl: res.data.fileUrl,
                fileName: res.data.fileName
            });
        },
        /**
         * 获取坏账记录
         */
        getBadDebtRegistrationList(paymentDocId) {
            this.badDebtRegistrationLoading = true;
            this.badDebtRegistrationText = '加载中...';
            customerPaymentDocument
                .getBadDebtRegistrationList({ paymentDocId })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.badDebtRegistrationList = res.data || [];
                    } else {
                        this.badDebtRegistrationList = [];
                    }
                })
                .catch(() => {
                    this.badDebtRegistrationList = [];
                })
                .finally(() => {
                    this.badDebtRegistrationLoading = false;
                });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.productTypeList = await this.getDictList('fourpl_product_type');
            this.orderStatusList = await this.getDictList('expense_order_status');
            this.temperatureTypeList = await this.getDictList('fourpl_temperature_type');
            this.statusList = await this.getDictList('receipt_status_status');
            this.invoiceStatusList = await this.getDictList('cost_invoicing_status_receipt');
            this.badDebtTypeList = await this.getDictList('bad_debt_type');
            this.paymentReceiptType = await this.getDictList('cost_payment_doc_type');
            this.applyStatusList = await this.getDictList('apply_status');
        },
        /**
         * 获取发票抬头列表
         */
        async getInvoiceHeadList(ownerId) {
            const res = (await invoiceInformationMaintenance.getInvoiceHeadList({ ownerId })) || [];
            if (res.code === 200) {
                this.invoiceList = res.data || [];
                if (this.invoiceList.length === 0) {
                    this.$message.warning('未查询到发票抬头信息,请先前往【发票信息维护】新增发票抬头');
                }
            } else {
                this.invoiceList = [];
            }
        },
        getList() {
            this.loading = true;
            const { sendAddress, receiverAddress, ...params } = this.queryForm;
            paymentDoc
                .paymentListOwner(params)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.orderList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        handleBillDetailsQuery() {
            this.billDetailsForm.current = 1;
            this.statementList();
        },
        /**
         * 改变订单类型
         */
        handleChangesTheOrderType(formName) {
            if (formName === 'queryForm') {
                this.queryForm.current = 1;
                const { sendAddress, receiverAddress, carrierId, current, size, ...params } = this.queryForm;
                paymentDoc
                    .getCarrierList({ ...params })
                    .then((res) => {
                        if (res.code === 200 && res.data?.length > 0) {
                            this.carrierList = res.data;
                        } else {
                            this.carrierList = [];
                            this.queryForm.carrierId = undefined;
                        }
                    })
                    .catch(() => {
                        this.carrierList = [];
                        this.queryForm.carrierId = undefined;
                    })
                    .finally(() => {
                        this.getList();
                    });
            } else if (formName === 'billDetailsForm') {
                this.handleBillDetailsQuery();
            }
        },
        /**
         * 查看坏账登记图片
         * @param {Object} row - 行数据对象
         */
        handleClickBadDebtRegistration(row) {
            if ('fileUrl' in row && row.fileUrl.trim()) {
                // 将逗号分隔的字符串转换为数组
                const fileUrls = row.fileUrl.split(',').map((url) => url.trim());

                // 如果所有项都不是空字符串，则展示图片
                if (fileUrls.every((url) => url !== '')) {
                    this.dialogImageUrl = fileUrls;
                    this.dialogVisible = true;
                } else {
                    this.$message.error('未获取到有效的坏账资料链接');
                }
            } else {
                this.$message.error('未获取到坏账资料');
            }
        },
        // 打开账单明细
        handleClickBillingDetails(row) {
            this.billDetailsVisible = true;
            this.billDetailsLoading = true;
            this.billDetailsLoadingText = '加载中...';
            // 临时存储row
            this.billDetailsRow = row;
            // 查询账单明细
            this.handleChangesTheOrderType('billDetailsForm');
            if ('id' in row) {
                this.getBadDebtRegistrationList(row.id);
            }
        },
        // 查看费用详情
        handleClickFeeDetails(data) {
            this.orderCostVisible = true;
            // this.detailData 初始为空
            this.detailData = {};
            this.detailData = { ...data };
            const { addedServices, costList } = data;
            let addedServicesFilter = [];
            if (addedServices) {
                // addedServices addedServicesContractPrice 变为 costContractPrice, addedServicesCost 变为 costData,addedServicesName 变为 costType 赋值给 addedServices
                // 使用 for of 循环遍历 addedServices
                for (const item of addedServices) {
                    item.costContractPrice = item.addedServicesContractPrice;
                    item.costData = item.addedServicesCost;
                    item.costType = item.addedServicesName;
                }
                // 去除 addedServices 中 costData 和 costContractPrice 都为 '0' 的数据 其中一个为 '0' 的数据不去除
                // 赋值给 addedServicesFilter
                addedServicesFilter = addedServices.filter((item) => item.costData != 0 || item.costContractPrice != 0);
            }
            // 筛选 costList 中 costData 和 costContractPrice 都不等于0的数据
            const costListFilter = costList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
            costListFilter.sort((a, b) => {
                return a.costType - b.costType;
            });

            // addedServices 为 null 解构赋值给 this.feeBreakdownData []
            this.feeBreakdownData = [...(addedServicesFilter || []), ...(costListFilter || [])];
        },
        /**
         * 打开 付款信息提交
         */
        async handleClickSubmit() {
            // 初始化一个数组来收集需要提示的收款单号
            const paymentDocTypeIsOne = [];

            // 遍历selectData数组
            this.selectData.forEach((item) => {
                // paymentDocType 收款单类型(1-预付款 2-其他 3-月结 4-到付 5-现付) 取消选中 预付款(1) 和 其他(2) 到付(4) 的行
                // status 付款状态(2-已付款 1-部分付款 0-未付款) 取消选中已付款(2) 的行
                // applyStatus 是否发起付款申请(1-待申请 2-已申请) 取消选中已申请(2) 的行
                if ((item.paymentDocType !== '3' && item.paymentDocType !== '5') || item.status === '2' || item.applyStatus === '2') {
                    // 取消选中不符合条件的行
                    this.$refs.paymentDocTable.$refs.ColumnTable.toggleRowSelection(item, false);
                    // 收集不符合条件的付款单号,用于后续提示
                    paymentDocTypeIsOne.push(item.paymentOrderNo);
                }
            });

            // 检查是否有选中数据
            if (this.selectData.length === 0) {
                this.$message({
                    message: '请选择【付款类型：月结、现付】【付款单状态：未付款、部分付款】【是否发起付款申请：待申请】数据',
                    type: 'warning'
                });
                return;
            }

            // 新增 判断 付款类型必须为同一种类型
            const paymentDocType = this.selectData[0].paymentDocType;
            const isSamePaymentDocType = this.selectData.every((item) => item.paymentDocType === paymentDocType);
            if (!isSamePaymentDocType) {
                this.$message.warning('请选择相同【付款类型】的数据');
                return;
            }

            // 收款单类型 付款类型(1-预付款 2-其他 3-月结 4-到付 5-现付)
            this.applyPrepaymentForm.paymentDocType = this.selectData[0]?.paymentDocType ?? '';

            // 计算选中数据的未付金额总和
            this.applyPrepaymentForm.remitAmount = this.selectData.reduce((total, item) => {
                // 尝试将字符串转换为数字，并处理NaN情况
                const unpaidCost = Number(item.unpaidCost) || 0;
                // 累加有效数字并保留2位小数
                return Number((total + unpaidCost).toFixed(2));
            }, 0);

            // carrierId 承运商id
            this.applyPrepaymentForm.carrierId = this.selectData[0]?.carrierId ?? '';

            // 默收款公司
            const Organization = this.$TOOL.data.get('Organization');
            const res = await customerPrepaymentBalance.getCompanyInfo(Organization[0].id, this.selectData[0].carrierId);
            if (res.code === 200) {
                this.applyPrepaymentForm.receiveCompany = res.data.signCompany;
            }

            // 获取当前登录用户
            const userInfo = this.$TOOL.data.get('USER_INFO');

            if (userInfo) {
                this.applyPrepaymentForm.companyName = userInfo.name;
                this.applyPrepaymentForm.companyId = userInfo.id;
                await this.getInvoiceHeadList(this.$TOOL.data.get('Organization')[0].id);
            } else {
                this.$message.error('无法获取当前登录用户信息，请重新登录或联系管理员');
            }
            this.applyPrepaymentVisible = true;

            // 如果没有选中任何数据，则显示警告信息并终止后续操作
            if (this.selectData.length > 0) {
            } else {
                if (paymentDocTypeIsOne.length > 0) {
                    this.$message.warning('请选择【付款类型：月结、到付、现付】【付款单状态：未付款】【是否发起付款申请：待申请】数据');
                }
            }
        },
        /**
         * 全部导出
         */
        handleExportAll() {
            // 提示框 询问是否全部导出
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    const { ...params } = this.queryForm;
                    paymentDoc
                        .exportPayment({ filename: '付款单.xls', ...params }, '', '', 'blob')
                        .then((res) => {
                            downloadNoData(res, 'application/vnd.ms-excel', '付款单.xlsx');
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 多选框选中数据
         * @param selection
         */
        handleSelectionChange(selection) {
            // 遍历 selection中的 id status 赋值给 this.selectData
            this.selectData = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        hideBillDetails() {
            this.billDetailsVisible = false;
            this.$refs.billDetailsForm.resetFields();
        },
        imgClose() {
            this.dialogVisible = false;
        },
        /**
         * 关闭预付款充值申请
         */
        onCloseApplyPrepayment() {
            this.applyPrepaymentVisible = false;
        },
        /**
         * 打开开票
         */
        async onOpenInvoice() {
            // 初始化一个数组来收集需要提示的收款单号
            const paymentDocTypeIsOne = [];
            this.selectData.forEach((item) => {
                // paymentDocType 付款类型(1-预付款 2-其他 3-月结 4-到付 5-现付)
                // invoicingStatus 是否发起开票申请(2-已发起 1-未发起)
                if (item.paymentDocType === '2' || item.paymentDocType === '4' || item.invoicingStatus === '2') {
                    this.$refs.paymentDocTable.$refs.ColumnTable.toggleRowSelection(item, false);
                    // 收集预付款类型的收款单号
                    paymentDocTypeIsOne.push(item.paymentOrderNo);
                }
            });

            // 如果没有选中任何数据，则显示警告信息并终止后续操作
            if (this.selectData.length > 0) {
                // 校验是否为同一货主
                const ownerId = this.selectData[0].ownerId;
                const isSameOwner = this.selectData.every((item) => item.ownerId === ownerId);
                if (!isSameOwner) {
                    this.$message.warning('请选择同一货主的付款单');
                    this.$refs.paymentDocTable.$refs.ColumnTable.clearSelection();
                    return;
                }

                // 校验付款类型是否符合规则
                const paymentTypes = [...new Set(this.selectData.map((item) => item.paymentDocType))];

                // 检查是否包含预付款(1)或到付(4)
                const hasPrePay = paymentTypes.includes('1');
                const hasCollectPay = paymentTypes.includes('4');

                // 检查是否包含月结(3)或现付(5)
                const hasMonthlyPay = paymentTypes.includes('3');
                const hasCashPay = paymentTypes.includes('5');

                // 验证付款类型组合
                if (hasPrePay || hasCollectPay) {
                    // 如果包含预付款或到付，不能与其他类型混合
                    if (paymentTypes.length > 1) {
                        this.$message.warning('【预付款】或【到付】类型的付款单不能与其他类型混合开票');
                        return;
                    }
                } else if (!(hasMonthlyPay || hasCashPay)) {
                    // 如果既不是预付款/到付，也不是月结/现付的组合
                    this.$message.warning('选择的付款类型不支持开票');
                    return;
                }

                // 校验 this.selectData 中的 companyId 是否一致
                const companyId = this.selectData[0].companyId;
                const isSameCompany = this.selectData.every((item) => item.companyId === companyId);
                if (!isSameCompany) {
                    this.$message.warning('请选择同一承运商的付款单');
                    return;
                }

                // 计算选中数据的未付金额总和
                this.invoiceForm.invoiceAmount = this.selectData.reduce((total, item) => {
                    // 尝试将字符串转换为数字，并处理NaN情况
                    const receivableCost = Number(item.receivableCost) || 0;
                    // 累加有效数字并保留2位小数
                    return Number((total + receivableCost).toFixed(2));
                }, 0);
                // 初始化发票表单数据
                this.invoiceForm = {
                    invoiceAmount: this.invoiceForm.invoiceAmount,
                    carrierId: this.selectData[0].carrierId,
                    companyId: this.selectData[0].companyId,
                    paymentDocType: this.selectData[0]?.paymentDocType || '',
                    payMethod: this.selectData[0]?.paymentDocType === '4' ? '4' : '3', // 4-到付, 3-月结&现付
                    // 根据付款类型组合设置开票业务类型
                    openInvoiceType: (() => {
                        // 如果只有一种付款类型
                        if (paymentTypes.length === 1) {
                            switch (paymentTypes[0]) {
                                case '1':
                                    return '1'; // 预付款开票
                                case '3':
                                    return '3'; // 月结开票
                                case '4':
                                    return '4'; // 到付开票
                                case '5':
                                    return '5'; // 现付开票
                            }
                        }
                        // 如果包含月结和现付的组合
                        if (paymentTypes.includes('3') || paymentTypes.includes('5')) {
                            return '2'; // 月结&现付开票
                        }
                        return '2'; // 默认月结&现付开票
                    })(),
                    // 如果是到付类型，初始化空的发票信息对象
                    invoiceData: this.isCollectPayment
                        ? {
                              invoiceHead: '',
                              taxNo: '',
                              address: '',
                              phone: '',
                              openBank: '',
                              bankAccount: ''
                          }
                        : null,
                    remark: ''
                };

                // 默认收款公司
                const Organization = this.$TOOL.data.get('Organization');
                // 获取付款类型
                const res = await customerPrepaymentBalance.getCompanyInfo(Organization[0].id, this.selectData[0].carrierId);
                if (res.code === 200 && res.data?.signCompany) {
                    this.invoiceForm.signCompany = res.data.signCompany;
                    this.invoiceForm.invoiceList = res.data.invoiceList;
                } else {
                    this.$message.warning('未获取到签约公司信息');
                    this.invoiceForm.signCompany = undefined;
                    this.invoiceForm.invoiceList = [];
                }

                // 获取当前登录用户
                const userInfo = this.$TOOL.data.get('USER_INFO');
                if (userInfo) {
                    this.invoiceForm.companyName = userInfo.name;
                    this.invoiceForm.companyId = userInfo.id;
                    await this.getInvoiceHeadList(this.$TOOL.data.get('Organization')[0].id);
                } else {
                    this.$message.error('无法获取当前登录用户信息，请重新登录或联系管理员');
                }
                this.invoiceVisible = true;
            } else {
                if (paymentDocTypeIsOne.length > 0) {
                    this.$message.warning('请选择月结或者现付类型的付款单发起开票申请！');
                }
            }
        },
        /**
         * 提交预付款充值申请
         */
        onSubmitApplyPrepayment(formData) {
            // 收款单应收合计金额
            this.mismatchFormData.receivableCost = this.selectData.reduce((total, item) => {
                const receivableCost = Number(item.receivableCost) || 0;
                return Number((total + receivableCost).toFixed(2));
            }, 0);
            // 收款单未收合计金额
            this.mismatchFormData.unpaidCost = this.selectData.reduce((total, item) => {
                const unpaidCost = Number(item.unpaidCost) || 0;
                return Number((total + unpaidCost).toFixed(2));
            }, 0);
            // 汇款合计金额
            this.mismatchFormData.remitAmount = formData.remitAmount;
            this.mismatchFormData.selectedData = this.selectData;
            // 存储 formData
            this.mismatchFormData.formData = formData;
            this.mismatchDialogVisible = true;
        },
        resetBillDetailsForm() {
            this.resetForm('billDetailsForm');
            this.handleChangesTheOrderType('billDetailsForm');
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleChangesTheOrderType('queryForm');
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 查询账单明细
         * clientType 1=承运端 2-货主端
         */
        statementList() {
            this.billDetailsLoading = true;
            const { sendAddress, receiverAddress, ...params } = this.billDetailsForm;
            customerPaymentDocument
                .statementDetail({ ...params, paymentId: this.billDetailsRow.id, clientType: '2' })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.billDetailsList = res.data || [];
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.billDetailsLoading = false;
                });
        },
        /**
         * 提交开票
         */
        submitInvoice(formData) {
            const params = {
                applyWay: '1', // 1-货主 2-承运商 (预付款默认货主发起)， 不传默认货主发起
                businessType: '2', //1-预存款开票 2-付款单开票
                carrierId: formData.carrierId,
                companyId: formData.isCollectPayment ? undefined : formData.companyId,
                companyName: formData.companyName,
                invoiceId: formData.isCollectPayment ? undefined : formData.invoiceData.id,
                invoiceHead: formData.invoiceData.invoiceHead,
                taxNo: formData.invoiceData.taxNo,
                address: formData.invoiceData.address,
                phone: formData.invoiceData.phone,
                openBank: formData.invoiceData.openBank,
                bankAccount: formData.invoiceData.bankAccount,
                invoiceAmount: formData.invoiceAmount,
                payIdList: this.selectData.map((item) => item.id),
                remark: formData.remark,
                payMethod: formData.payMethod,
                openInvoiceType: formData.openInvoiceType,
                invoiceType: formData.invoiceType,
                projectName: formData.projectFeeType,
                signCompany: formData.signCompany,
                taxPoint: formData.taxPoint
            };

            paymentDoc.applyInvoice(params).then((res) => {
                if (res.code === 200) {
                    this.$message.success('开票申请成功');
                    this.closeInvoice();
                    this.getList();
                }
            });
        },
        /**
         * 确认汇款金额与收款单金额不匹配
         */
        submitMismatchDialog(mismatchFormData) {
            const loading = this.$loading({
                lock: true,
                text: '提交中...',
                spinner: 'el-icon-loading'
            });

            const payDocList = mismatchFormData.selectedData.map((item) => ({
                payId: item.id,
                paidCost: item.paidCost
            }));
            const payMethod = this.selectData[0].paymentDocType; // 支付方式

            // 转换附件信息为JSON字符串
            const params = {
                ...mismatchFormData.formData,
                applyWay: '1', // 申请方式 1-货主发起 2-承运商发起
                businessType: '2', // 业务类型 1-预存款充值 2-付款单支付 3-收款单收款
                payDocList,
                remitFile: JSON.stringify(mismatchFormData.formData.remitFile),
                payMethod
            };

            paymentDoc
                .applyPayment(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.$message.success('提交成功');
                        this.closeMismatchDialog();
                        this.onCloseApplyPrepayment();
                        this.getList();
                    }
                })
                .finally(() => {
                    loading.close();
                });
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
:deep(.el-input.is-disabled .el-input__inner) {
    color: #333 !important;
    -webkit-text-fill-color: #333 !important;
}

.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}

.invoicingApplication {
    .invoicingApplication__card {
        .card__content {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .content__left {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .content__title {
                font-weight: bold;
            }
        }
    }
}
.card__inline {
    display: flex;
    justify-content: space-between;
}
.box__card__two {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 15px;
}
.number__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
.input__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 10px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
