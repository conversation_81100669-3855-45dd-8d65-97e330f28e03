<template>
    <div>
        <!-- 搜索 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item prop="warehouse">
                    <el-select v-model="queryParams.warehouse" placeholder="请选择所属仓" @change="handlerWare" clearable @clear="getList">
                        <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="storeHouse">
                    <el-select v-model="queryParams.storeHouse" @change="handlerWare3" placeholder="请选择所属库" clearable @clear="getList">
                        <el-option v-for="item in libraryOptions" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="storeHouseArea">
                    <el-select v-model="queryParams.storeHouseArea" placeholder="请选择所属库区" clearable @change="handleSearch" @clear="getList">
                        <el-option v-for="item in libraryOptions3" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入货架名称或简称快速查询" clearable class="form_225" @clear="getList">
                        <template v-slot:suffix>
                            <el-icon @click="handleSearch">
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(creatForm)" class="creatSpan">新增</el-button>
            <RightToptipBarV2 @handleRefresh="getList" className="shelfInformation" style="float:right;margin-top:10px" />
            <DragTableColumn :columns="columns" :tableData="recordList" className="shelfInformation"
                v-model:queryParams="queryParams" :getList="getList">
                <template v-slot:operate="{ scopeData }">
                    <el-button link type="primary" @click="handleEdit(scopeData.row, 'edit')">
                        <img src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                    <el-button link type="danger" @click="handleDelete(scopeData.row)"><img src="@/assets/icons/delete.png"
                            style="margin-right:5px" />删除</el-button>
                    <el-button link @click="handlerLog(scopeData.row)" style="color:#67c23a"><img
                            src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                </template>
            </DragTableColumn>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增的弹框 -->
        <el-dialog v-model="dialogFormVisible" width="50%" title="新增货架信息" :before-close="() => handlerClose()">
            <el-form :model="dialogform" label-width="110px" :rules="rules" ref="creatForm">
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="所属仓" prop="warehouse">
                            <el-select v-model="dialogform.warehouse" @change="handlerWare" placeholder="请选择所属仓" clearable
                                style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库" prop="storeHouse">
                            <el-select v-model="dialogform.storeHouse" @change="handlerWare2" placeholder="请选择所属库" clearable
                                style="width:100%;">
                                <el-option v-for="item in libraryOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库区" prop="storeHouseArea">
                            <el-select v-model="dialogform.storeHouseArea" placeholder="请选择所属库区" clearable
                                style="width:100%;">
                                <el-option v-for="item in libraryOptions2" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货架编码" prop="code">
                            <el-input v-model="dialogform.code" placeholder="请输入货架编码" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架名称" prop="name">
                            <el-input v-model="dialogform.name" placeholder="请输入货架名称" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架简称" prop="nameSimplified">
                            <el-input v-model="dialogform.nameSimplified" placeholder="请输入货架简称" clearable
                                @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货架简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="dialogform.nameSimplifiedPinyin" placeholder="请输入货架简拼" clearable
                                @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架宽度(cm)" prop="width">
                            <el-input v-model="dialogform.width" placeholder="请输入货架宽度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架长度(cm)" prop="length2">
                            <el-input v-model="dialogform.length2" placeholder="请输入货架长度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货架高度(cm)" prop="hight">
                            <el-input v-model="dialogform.hight" placeholder="请输入货架高度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架层数" prop="layers">
                            <el-input v-model="dialogform.layers" placeholder="请输入货架层数" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="最大承重(kg)" prop="maxLoad">
                            <el-input v-model="dialogform.maxLoad" placeholder="请输入最大承重" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="dialogform.remark" :rows="1" type="textarea" placeholder="请输入备注" />
                </el-form-item>

            </el-form>
            <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="dialogImageUrl" />
            </el-dialog>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="creat(creatForm)">确定</el-button>
            </div>
        </el-dialog>
        <!-- 编辑的弹框 -->
        <el-dialog v-model="editdialogFormVisible" width="50%" title="修改货架信息" :before-close="() => handlerClose()">
            <el-form :model="editdialogform" label-width="110px" :rules="rules2" ref="editcreatForm">
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="所属仓" prop="warehouse">
                            <el-select v-model="editdialogform.warehouse" @change="handlerWare4" placeholder="请选择所属仓"
                                clearable style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库" prop="storeHouse">
                            <el-select v-model="editdialogform.storeHouse" @change="handlerWare5" placeholder="请选择所属库"
                                clearable style="width:100%;">
                                <el-option v-for="item in libraryOptions4" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库区" prop="storeHouseArea">
                            <el-select v-model="editdialogform.storeHouseArea" placeholder="请选择所属库区" clearable
                                style="width:100%;">
                                <el-option v-for="item in libraryOptions5" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货架编码" prop="code">
                            <el-input v-model="editdialogform.code" placeholder="请输入货架编码" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架名称" prop="name">
                            <el-input v-model="editdialogform.name" placeholder="请输入货架名称" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架简称" prop="nameSimplified">
                            <el-input v-model="editdialogform.nameSimplified" placeholder="请输入货架简称" clearable
                                @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货架简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="editdialogform.nameSimplifiedPinyin" placeholder="请输入货架简拼" clearable
                                @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架宽度(cm)" prop="width">
                            <el-input v-model="editdialogform.width" placeholder="请输入货架宽度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架长度(cm)" prop="length">
                            <el-input v-model="editdialogform.length" placeholder="请输入货架长度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货架高度(cm)" prop="hight">
                            <el-input v-model="editdialogform.hight" placeholder="请输入货架高度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货架层数" prop="layers">
                            <el-input v-model="editdialogform.layers" placeholder="请输入货架层数" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="最大承重(kg)" prop="maxLoad">
                            <el-input v-model="editdialogform.maxLoad" placeholder="请输入最大承重" clearable
                                @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="editdialogform.remark" :rows="1" type="textarea" placeholder="请输入备注" />
                </el-form-item>

            </el-form>
            <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="dialogImageUrl" />
            </el-dialog>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="edit(editcreatForm)">确定</el-button>
            </div>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import informationMaintenance from '@/api/erp/warehouseManagement/informationMaintenance'
import libraryInformation from '@/api/erp/warehouseManagement/libraryInformation'
import reservoirInformation from '@/api/erp/warehouseManagement/reservoirInformation'
import shelfInformation from '@/api/erp/warehouseManagement/shelfInformation'
import { Plus,Search } from '@element-plus/icons-vue'
import { ElMessage,ElMessageBox } from 'element-plus'
import logList from './logList.vue'
const reviewRow = ref({})
import tool from '@/utils/tool';
const queryParams = reactive({
    current: 1,
    size: 10,
})
const libraryOptions = ref([])
const { proxy } = getCurrentInstance();
const recordList = ref([])
const reviewVisible = ref(false)
const libraryOptions2 = ref([])
const libraryOptions3 = ref([])
const libraryOptions4 = ref([])
const libraryOptions5 = ref([])
const selected = ref([])
const warehouseOptions = ref([])
const dialogFormVisible = ref(false)
const editdialogFormVisible = ref(false)
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const dialogform = reactive([])
const editdialogform = ref([])
const creatForm = ref()
const editcreatForm = ref()
const ids = ref('')
const total = ref(0)
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const columns = ref(
    [
        {
            label: '所属仓简称',
            prop: 'wareHouse.nameSimplified',
            // type: "sort",
            // fixed: 'left'
        },
        {
            label: '所属库简称',
            prop: 'storeHouse.nameSimplified'
        }, {
            label: '所属库区简称',
            prop: 'storeHouseArea.nameSimplified'
        }, {
            label: '货架编码',
            prop: 'code'
        }, {
            label: '货架名称',
            prop: 'name'
        }
        , {
            label: '货架简称',
            prop: 'nameSimplified'
        }, {
            label: '货架简拼',
            prop: 'nameSimplifiedPinyin'
        }, {
            label: '货架宽度(cm)',
            prop: 'width'
        }, {
            label: '货架长度(cm)',
            prop: 'length'
        }, {
            label: '货架高度(cm)',
            prop: 'hight'
        }, {
            label: '货架层数',
            prop: 'layers'
        }, {
            label: '最大承重(kg)',
            prop: 'maxLoad'
        }, {
            label: '操作',
            prop: 'operate',
            type: 'operate',
            minWidth: 300,
            fixed: 'right'
        },
    ]
)
const rules = reactive({
    warehouse: [{ required: true, message: '请选择所属仓', trigger: 'blur' },],
    storeHouse: [{ required: true, message: '请选择所属库', trigger: 'blur' },],
    storeHouseArea: [{ required: true, message: '请选择所属库区', trigger: 'blur' },],
    code: [{ required: true, message: '请输入货架编码', trigger: 'blur' },],
    name: [{ required: true, message: '请输入库架名称', trigger: 'blur' },],
    nameSimplified: [{ required: true, message: '请输入库架简称', trigger: 'blur' },],
    nameSimplifiedPinyin: [{ required: true, message: '请输入库架简拼', trigger: 'blur' },],
    width: [{ required: true, message: '请输入货架宽度', trigger: 'blur' },],
    length2: [{ required: true, message: '请输入货架长度', trigger: 'blur' },],
    hight: [{ required: true, message: '请输入货架高度', trigger: 'blur' },],
    layers: [{ required: true, message: '请输入货架层数', trigger: 'blur' },],
    maxLoad: [{ required: true, message: '请输入最大承重', trigger: 'blur' },],
})
const rules2 = reactive({
    warehouse: [{ required: true, message: '请选择所属仓', trigger: 'blur' },],
    storeHouse: [{ required: true, message: '请选择所属库', trigger: 'blur' },],
    storeHouseArea: [{ required: true, message: '请选择所属库区', trigger: 'blur' },],
    code: [{ required: true, message: '请输入货架编码', trigger: 'blur' },],
    name: [{ required: true, message: '请输入库架名称', trigger: 'blur' },],
    nameSimplified: [{ required: true, message: '请输入库架简称', trigger: 'blur' },],
    nameSimplifiedPinyin: [{ required: true, message: '请输入库架简拼', trigger: 'blur' },],
    width: [{ required: true, message: '请输入货架宽度', trigger: 'blur' },],
    length: [{ required: true, message: '请输入货架长度', trigger: 'blur' },],
    hight: [{ required: true, message: '请输入货架高度', trigger: 'blur' },],
    layers: [{ required: true, message: '请输入货架层数', trigger: 'blur' },],
    maxLoad: [{ required: true, message: '请输入最大承重', trigger: 'blur' },],
})
//搜索
const handleSearch = () => {
    getList()
}
//获取仓列表
function getwarehouseList() {
    informationMaintenance.list().then(res => {
        if (res.code == 200) {
            warehouseOptions.value = res.data.records
        }
    })
}
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
const handlerWare = (item) => {
    dialogform.storeHouse = ''
    dialogform.storeHouseArea = ''
    queryParams.storeHouse = ''
    queryParams.storeHouseArea = ''
    handleSearch()
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            console.log(res);
            libraryOptions.value = res.data.records
            // total.value = res.data.total
        }
    })
}
handlerWare()
const handlerWare2 = (item) => {
    dialogform.storeHouseArea = ''
    //获取库区
    reservoirInformation.list({
        'storeHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions2.value = res.data.records
            // total.value = res.data.total
        }
    })
}
const handlerWare3 = (item) => {
    handleSearch()
    queryParams.storeHouseArea = ''
    //获取库区
    reservoirInformation.list({
        'storeHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions3.value = res.data.records
            // total.value = res.data.total
        }
    })
}
handlerWare3()
const handlerWare4 = (item) => {
    editdialogform.value.storeHouse = ''
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            console.log(res);
            libraryOptions4.value = res.data.records
            // total.value = res.data.total
        }
    })
}
const handlerWare5 = (item) => {
    editdialogform.value.storeHouseArea = ''
    //获取库区
    reservoirInformation.list({
        'storeHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions5.value = res.data.records
            // total.value = res.data.total
        }
    })
}
getwarehouseList()
// 新增按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    formEl.resetFields()
}
//货架列表
function getList() {
    const { warehouse,storeHouse,storeHouseArea,...rest } = queryParams;
    const queryParamsUpdated = {
        'wareHouse.id': warehouse,
        'storeHouse.id': storeHouse,
        'storeHouseArea.id':storeHouseArea,
        ...rest
    };
    console.log(queryParamsUpdated);
    shelfInformation.list({...queryParamsUpdated}).then(res => {
        if (res.code == 200) {
            recordList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
// 新增请求
const creat = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            const params = {
                storeHouseArea: {
                    id: dialogform.storeHouseArea
                },
                code: dialogform.code,
                name: dialogform.name,
                nameSimplified: dialogform.nameSimplified,
                nameSimplifiedPinyin: dialogform.nameSimplifiedPinyin,
                width: dialogform.width,
                length: dialogform.length2,
                hight: dialogform.hight,
                layers: dialogform.layers,
                maxLoad: dialogform.maxLoad,
                remark: dialogform.remark,
            }
            shelfInformation.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        dialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "添加失败，请稍后重试",
                        });
                    }
                })
        }
    });
};
// 删除货架
const handleDelete = (row) => {
    proxy.$confirm('是否确认删除此货架信息?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        shelfInformation.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
// 编辑按钮
const handleEdit = (row) => {
    editdialogFormVisible.value = true
    handlerWare()
    handlerWare2()
    handlerWare4()
    handlerWare5()
    shelfInformation.detail({ id: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.value = res.data
            ids.valus = res.data.id
            console.log('res',res.data);
            editdialogform.value.warehouse = res.data.wareHouse.id
            editdialogform.value.storeHouse = res.data.storeHouse.id
            editdialogform.value.storeHouseArea = res.data.storeHouseArea.id
        }
    })
}
// 编辑请求
const edit = () => {
    const params = {
        storeHouseArea: {
            id:  editdialogform.value.storeHouseArea
        },
        code:  editdialogform.value.code,
        name:  editdialogform.value.name,
        nameSimplified:  editdialogform.value.nameSimplified,
        nameSimplifiedPinyin:  editdialogform.value.nameSimplifiedPinyin,
        width:  editdialogform.value.width,
        length:  editdialogform.value.length,
        hight:  editdialogform.value.hight,
        layers:  editdialogform.value.layers,
        maxLoad:  editdialogform.value.maxLoad,
        remark:  editdialogform.value.remark,
    }
    console.log(params);
    params.id = ids.valus
    shelfInformation.save(params)
        .then(res => {
            if (res.code == 200) {
                ElMessage({
                    message: "修改成功",
                    type: "success",
                });
                editdialogFormVisible.value = false
                getList()
            } else {
                ElMessage({
                    type: "error",
                    message: "修改失败，请稍后重试",
                });
            }
        })
}
// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
</script>

<style lang="scss" scoped>
.dialog {
    padding: 30px
}

::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.dialogP {
    position: relative;
    top: -10px;
    left: 100px
}

.creatSpan {
    margin-bottom: 10px;
}

.dialog-footer {
    display: flex;
    justify-content: end;
}
</style>
