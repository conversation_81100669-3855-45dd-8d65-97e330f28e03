<!--
 * @Descripttion: 系统计划任务配置
 * @version: 1.2
 * @Author: sakuya
 * @Date: 2021年7月7日09:28:32
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-12-21 09:10:15
-->

<template>
  <el-dialog :title="titleMap[mode]" v-model="visible" :width="400" destroy-on-close @closed="$emit('closed')">
    <el-form :model="form" :rules="rules" ref="dialogForm" label-width="100px" label-position="left" :inline="true">
      <el-form-item label="描述" prop="name">
        <el-input v-model="form.name" placeholder="计划任务标题" clearable></el-input>
      </el-form-item>
      <el-form-item label="执行类" prop="excuteClass">
        <el-input v-model="form.excuteClass" placeholder="计划任务执行类名称" clearable></el-input>
      </el-form-item>

      <el-form-item label='参数名' prop="param[0].paramName">
        <el-input v-model="form.param[0].paramName" clearable></el-input>
      </el-form-item>
      <el-form-item label='参数值' prop="param[0].paramValue">
        <el-input v-model="form.param[0].paramValue" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <!-- <i class="el-icon-delete" style="visibility:hidden;"></i> -->
      </el-form-item>
      <div v-for="(item, index) in form.param" :key="index">
        <div v-if="index >= 1">
          <el-form-item :label="'参数名' + (index + 1)" :prop="'param.' + index + '.paramName'">

            <el-input v-model="item.paramName" clearable></el-input>
          </el-form-item>
          <el-form-item :label="'参数值' + (index + 1)" :prop="'param.' + index + '.paramValue'">
            <el-input v-model="item.paramValue" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-icon :size="size" @click="deleteItem(item, index)">
              <el-icon-delete />
              <!-- <Delete /> -->
            </el-icon>
          </el-form-item>
        </div>
      </div>

      <el-form-item label="定时规则" prop="timingRules">
        <sc-cron v-model="form.timingRules" placeholder="请输入定时规则" clearable :shortcuts="shortcuts"></sc-cron>
      </el-form-item>
      <el-form-item label="是否启用" prop="isEnable">
        <el-switch v-model="form.isEnable"></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="addItem">添加参数</el-button>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="isSaveing" @click="submit()">保 存</el-button>
    </template>
  </el-dialog>
</template>

<script>
import scCron from "@/components/scCron";
import { Delete } from '@element-plus/icons-vue'

export default {
  components: {
    scCron,
  },
  emits: ["success", "closed"],
  data() {
    return {
      mode: "add",
      titleMap: {
        add: "新增计划任务",
        edit: "编辑计划任务",
      },
      form: {
        param: [
          {
            "paramName": '',
            "paramValue": ''
          }
        ]
      },
      rules: {
        name: [{ required: true, message: "请填写标题" }],
        excuteClass: [{ required: true, message: "请填写执行类" }],
        timingRules: [{ required: true, message: "请填写定时规则" }],
        // param: [{ required: true, message: "请填写参数" }],
      },
      visible: false,
      isSaveing: false,
      shortcuts: [
        {
          text: "每天8点和12点 (自定义追加)",
          value: "0 0 8,12 * * ?",
        },
      ],
      size: '20px'
    };
  },
  mounted() { },
  methods: {
    addItem() {
      this.form.param.push({
        paramName: '',
        paramValue: ''
      })
    },
    // 删除一组输入框
    deleteItem(item, index) {
      this.form.param.splice(index, 1)
    },
    /*
     * 添加视图
     * @author: 路正宁
     * @date: 2023-03-29 11:29:14
     */
    addView() {
      this.mode = "add";
      this.visible = true;
      return this;
    },
    /*
     * 编辑视图
     * @author: 路正宁
     * @date: 2023-03-29 11:29:36
     */
    editView(task) {
      if (task.param instanceof Array) {
        this.form = task;
      } else {
        let param = JSON.parse(task.param);
        let arr = Object.entries(param);
        let arrNew = [];
        arr.forEach(item => {
          arrNew.push({
            paramName: item[0],
            paramValue: item[1],
          });
        });
        this.form = task;
        this.form.param = arrNew;
      }
      this.mode = "edit";
      this.visible = true;
      return this;
    },
    //表单提交方法
    async submit() {
      var valid = await this.$refs.dialogForm.validate().catch(() => { });
      if (!valid) {
        return false;
      }
      //锁定提交按钮
      this.isSaveing = true;
      if (this.form.param.length > 0) {
        const map = new Map();
        for (var i = 0; i < this.form.param.length; i++) {
          map.set(this.form.param[i].paramName, this.form.param[i].paramValue);
        }
        this.form.param = JSON.stringify(Object.fromEntries(map));
      }
      var res = await this.$API.sysTaskService.save(this.form);
      if (res.code == 200) {
        //关闭页面
        this.visible = false;
        this.$message.success("操作成功");
        //回调函数
        this.$emit("success", res.data, this.mode);
      } else {
        this.$Response.errorNotice(res, "保存失败");
      }
      //释放提交按钮
      this.isSaveing = false;
    },
  },
};
</script>

<style></style>
