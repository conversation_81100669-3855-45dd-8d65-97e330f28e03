<template>
    <div class="app-container">
        <!--搜索-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item label="货主公司" prop="companyId" style="width: 240px">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">
                        <el-option v-for="(dict, index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <el-button v-hasPermi="['tms:config:company:template:add']" icon="el-icon-plus" size="mini" type="primary" @click="handleAdd(0, 0)">模板配置 </el-button>
                <right-toolbar v-model:showSearch="showSearch" table-i-d="HandoverOrderConfiguration" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="HandoverOrderConfiguration" ref="ColumnTable" v-loading="loading" :columns="columns" :data="dataList" :defaultSort="{ prop: 'createDate', order: 'descending' }">
				<template #opt="scope">
					<el-button icon="el-icon-info-filled" link plain size="mini" type="primary" @click="preview(scope.row)">预览</el-button>
					<el-button v-hasPermi="['tms:config:company:template:edit']" icon="el-icon-edit" link plain size="mini" type="success" @click="handleUpdate(scope.row)">编辑 </el-button>
					<el-button v-hasPermi="['tms:config:company:template:del']" icon="el-icon-delete" link plain size="mini" type="danger" @click="handleDelete(scope.row)">删除 </el-button>
				</template>
			</column-table>
        </el-card>
        <!-- 添加或编辑模板配置 -->
        <el-dialog v-model="open" :title="title" append-to-body width="500px">
            <el-form ref="form" :model="form" :rules="rules" label-width="150px">
                <el-form-item label="货主公司" prop="companyId" >
                    <el-select v-model="form.companyId" :disabled="companyIdDisabled" clearable filterable placeholder="请选择货主公司" style="width: 100%;">
                        <el-option v-for="(dict, index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />
                    </el-select>
                </el-form-item>
                <el-form-item label="模板名称" prop="templateName">
                    <el-select v-model="form.templateId" clearable filterable placeholder="请选择模板类型" style="width: 100%;" @change="formChangeHandle">
                        <el-option v-for="(dict, index) in templateList" :key="index" :label="dict.name" :value="dict.id"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="模板编号" prop="templateId">
                    <el-input v-model="form.templateId" disabled maxlength="500" placeholder="请输入模板编号" show-word-limit type="text" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="500" placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</template>
        </el-dialog>
    </div>
</template>

<script>
import RightToolbar from '@/components/RightToolbar';
import SearchButton from '@/components/searchModule/SearchButton';
import ColumnTable from '@/components/ColumnTable';
import handoverOrderConfiguration from '@/api/carrierEnd/handoverOrderConfiguration.js';
export default {
    name: 'HandoverOrderConfiguration',
    components: { SearchButton, RightToolbar, ColumnTable },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            ownerList: [], // 货主公司列表
			templateList: [], // 模板列表
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                companyId: null,
				current: 1,
				size: 10,
            },
            dataList: [],
            columns: [
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '模板名称', key: 'templateName', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '模板编号', key: 'templateId', align: 'center', minWidth: '180px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '120px', columnShow: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '120px',columnShow: true },
                { title: '操作', key: 'opt', align: 'center', fixed: 'right', width: '220px', hideFilter: true, columnShow: true }
            ],
            // 表单参数
            form: {
                companyId: null,
				templateType:'1',
                templateName: null,
                templateId: null,
                remark: null
            },
            // 表单校验
            rules: {
				companyId: [{ required: true, message: '请选择货主公司', trigger: 'blur' }],
				templateName: [{ required: true, message: '请选择模板名称', trigger: 'blur' }]
            },
			companyIdDisabled:false, // 货主是否可编辑
        };
    },
    async created() {
    	this.getCompanySelect();
    	this.getTemplateSelect();
        this.getList();
    },
    methods: {
        /**
         * 获取货主公司下拉
         */
        getCompanySelect() {
			handoverOrderConfiguration.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
		/**
		 * 获取模板下拉
		 */
		getTemplateSelect(){
			handoverOrderConfiguration.xmlTemplateSelect({fGroupId:'9137a9b026fd4e0196dd9ec559427d27'}).then((response) => {
				this.templateList = response.data;
			});
		},

        /** 查询模板配置列表 */
        getList() {
			this.loading = true;
			this.dataList = [];
			handoverOrderConfiguration.listTemplate(this.queryParams).then((response) => {
				if (response.code == 200) {
					this.dataList = response?.data?.records || [];
					this.total = response.data.total || 0;
				}
				this.loading = false;
			}).catch(e => {
				this.loading = false;
			});
		},
        /**
         *	选择模板
         * @param e
         */
        formChangeHandle(e) {
        	let tmp = this.templateList.find((item) => item.id == e)
			if(tmp){
				this.form.templateName = tmp.name;
			}
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
				companyId: null,
				templateType:'1',
				templateName: null,
				templateId: null,
				remark: null
            };
            this.$refs['form'] ? this.$refs['form'].resetFields() : '';
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.handleQuery();
        },
		/**
		 * 预览
		 * @param row
		 */
		preview(row){
			handoverOrderConfiguration.viewTemplate({templateId:row.templateId}).then((response) => {
				const binaryData = [];
				binaryData.push(response);
				//获取blob链接
				let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
				window.open(pdfUrl)
			})
		},
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加模板配置';
            this.companyIdDisabled = false;
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
			this.form = JSON.parse(JSON.stringify(row));
			this.open = true;
			this.title = '修改模板配置';
			this.companyIdDisabled = true;
            // handoverOrderConfiguration.queryCarTypeById({ id }).then((response) => {
            //
            // });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                    	let params = {
							id:this.form.id,
							templateId:this.form.templateId,
							templateName:this.form.templateName,
							remark:this.form.remark,
						}
                        handoverOrderConfiguration.editTemplate(params).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                                this.getTreeSelect();
                            }
                        });
                    } else {
                        handoverOrderConfiguration.addTemplate(this.form).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                                this.getTreeSelect();
                            }
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$confirm('确认删除配置？', '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return handoverOrderConfiguration.delTemplate({ id: ids });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        }
    }
};
</script>

<style scoped></style>
