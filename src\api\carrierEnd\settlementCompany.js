import request from '@/utils/request';
export default {
    // 列表
    getSettlementCompanyList: function (params) {
        return request.get('/cost/settlement/company/list', params);
    },
    // 新增结算公司
    addSettlementCompany: function (params) {
        return request.post('/cost/settlement/company/add', params);
    },
    // 修改结算公司
    updateSettlementCompany: function (params) {
        return request.post('/cost/settlement/company/edit', params);
    },
    // 删除结算公司
    deleteSettlementCompany: function (params) {
        return request.delete('/cost/settlement/company/delete', params);
    },
    // 导出
    exportSettlementCompany: function (params, config, resDetail, responseType) {
        return request.get('/cost/settlement/company/export', params, config, resDetail, responseType);
    },
    // 下拉框列表
    getSettlementCompanySelectList: function (params) {
        return request.get('/cost/settlement/company/settlementCompanySelect', params);
    }
};
