<template>
    <div class="app-container customer-auto-height-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="订单号" prop="orderNo" style="width: 230px">
                    <el-input v-model="queryForm.orderNo" clearable placeholder="请输入订单号" @clear="handleChangesTheOrderType" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单号" prop="transOrderNo" style="width: 250px">
                    <el-input v-model="queryForm.transOrderNo" clearable placeholder="请输入运单号" @clear="handleChangesTheOrderType" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item label="对账状态" prop="status" style="width: 245px">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择对账状态" @change="handleChangesTheOrderType">
                        <el-option v-for="item in reconciliationStatusList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item class="self-label-color" label="下单时间" prop="queryTime" style="width: 308px">
                    <el-date-picker v-model="queryForm.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleChangesTheOrderType"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单类型" prop="type">
                    <el-select v-model="queryForm.type" clearable disabled placeholder="请选择订单类型" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in settlementManagementOrderTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="queryForm.type === '2'" v-show="isShowAll" class="self-label-color" label="货主公司" prop="companyId">
                    <el-select v-model="queryForm.companyId" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运输类型" prop="transType">
                    <el-select v-model="queryForm.transType" clearable placeholder="请选择运输类型" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in productTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="产品分类" prop="productType">
                    <el-select v-model="queryForm.productType" clearable placeholder="请选择产品分类" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="温区类型" prop="temperatureType">
                    <el-select v-model="queryForm.temperatureType" clearable placeholder="请选择温区类型" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in temperatureTypeDicts" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" class="self-label-color" label="付款方式" prop="settlementMethod">
                    <el-select v-model="queryForm.settlementMethod" clearable placeholder="请选择付款方式" @change="handleChangesTheOrderType">
                        <el-option v-for="item in settlementMethodList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件公司" prop="sendCompany" style="width: 250px">
                    <el-input v-model="queryForm.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件地址" prop="sendAddress">
                    <el-cascader v-model="queryForm.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable placeholder="请选择发件地址" @change="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件公司" prop="receiverCompany" style="width: 250px">
                    <el-input v-model="queryForm.receiverCompany" clearable placeholder="请输入收件公司" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="结算公司" prop="settlementCompanyId">
                    <el-select v-model="queryForm.settlementCompanyId" :filter-method="handleInput" clearable filterable placeholder="请选择结算公司" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in settlementCompanyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件地址" prop="receiverAddress">
                    <el-cascader v-model="queryForm.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable placeholder="请选择收件地址" @change="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="操作人" prop="adjustName" style="width: 250px">
                    <el-input v-model="queryForm.adjustName" clearable placeholder="请输入操作人" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleChangesTheOrderType" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <div class="flex justify-around">
                <el-statistic :precision="2" :value="overview.totalEstimateCost" :value-style="{ color: '#5670FE' }" group-separator="," title="预估费用合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalAdjustCost" :value-style="{ color: '#1ACD7E' }" group-separator="," title="调整金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalContractPrice" :value-style="{ color: '#F4AC00' }" group-separator="," title="合同价合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalDiscountCost" :value-style="{ color: '#5670FE' }" group-separator="," title="折扣金额合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalReceivableCost" :value-style="{ color: '#1ACD7E' }" group-separator="," title="应收费用合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalAmount" :value-style="{ color: '#F4AC00' }" group-separator="," title="总账单金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.checkedAmount" :value-style="{ color: '#5670FE' }" group-separator="," title="已核对账单金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.toBeCheckedAmount" :value-style="{ color: '#1ACD7E' }" group-separator="," title="待核对账单金额"></el-statistic>
            </div>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div class="mb10">
                <el-button :disabled="multiple" type="primary" @click="generatePaymentDocument">生成收款单</el-button>
                <el-button :disabled="multiple" type="primary" @click="handleClickBulkChecks">批量核对</el-button>
                <el-button :disabled="multiple" type="warning" @click="handleClickBulkRevoke">撤销核对</el-button>
                <el-button v-if="dataList && dataList.length" type="primary" @click="exportStatement">全部导出</el-button>
                <el-tooltip content="请选择下单时间(必须是同一月)、货主公司、付款方式再执行该操作" placement="top">
                    <el-button :disabled="!dataList || dataList.length === 0" type="primary" @click="generatePaymentDocumentAll">全部生成收款单</el-button>
                </el-tooltip>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="customerStatement" @queryTable="getList" />
            </div>
            <column-table ref="customerStatement" v-loading="loading" :columns="columns" :data="dataList" :show-summary="true" :showCheckBox="true" class="customer-auto-height-table" element-loading-text="加载中..." max-height="null" @selection-change="handleSelectionChange">
                <template #type="{ row }">
                    <span>{{ formatDictionaryData('settlementManagementOrderTypeList', row.type) }}</span>
                </template>
                <template #transType="{ row }">
                    <span>{{ formatDictionaryData('productTypeList', row.transType) }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                </template>
                <template #temperatureType="{ row }">
                    <span>{{ formatDictionaryData('temperatureTypeDicts', row.temperatureType) }}</span>
                </template>
                <template #settlementMethod="{ row }">
                    <span>{{ formatDictionaryData('settlementMethodList', row.settlementMethod) }}</span>
                </template>
                <template #collectFormula="{ row }">
                    <el-popover placement="top" width="400">
                        <pre>{{ row.collectFormula }}</pre>
                        <template #reference>
                            <el-link size="small">查看</el-link>
                        </template>
                    </el-popover>
                </template>
                <template #transportFormula="{ row }">
                    <el-popover placement="top" width="400">
                        <pre>{{ row.transportFormula }}</pre>
                        <template #reference>
                            <el-link size="small">查看</el-link>
                        </template>
                    </el-popover>
                </template>
                <template #deliveryFormula="{ row }">
                    <el-popover placement="top" width="400">
                        <pre>{{ row.deliveryFormula }}</pre>
                        <template #reference>
                            <el-link size="small">查看</el-link>
                        </template>
                    </el-popover>
                </template>
                <template #orderStatus="{ row }">
                    <div v-html="formatOrderStatus(row.orderStatus)"></div>
                </template>
                <template #status="{ row }">
                    <div v-html="formatStatus(row.status)"></div>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickFeeDetails(row)">费用详情</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>

        <!--  /生成收款单  -->
        <el-drawer v-if="generatePaymentDocumentVisible" v-model="generatePaymentDocumentVisible" size="1140px" title="生成收款单" @close="closeGeneratePaymentDocument">
            <div v-loading="generatePaymentDocumentLoading" :element-loading-text="generatePaymentDocumentLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <div class="generatePaymentDocument__card card__two__column">
                        <div class="d-box-content" style="flex: 1">
                            <div class="d-box-column">
                                <div class="d-box-info">
                                    <span>货主公司</span>
                                    <span>{{ companyData.companyName }}</span>
                                </div>
                                <div class="d-box-info">
                                    <span>下单时间</span>
                                    <span>{{ companyData.startDate }} ~ {{ companyData.endDate }}</span>
                                </div>
                            </div>
                            <div class="d-box-column">
                                <div class="d-box-info">
                                    <span>总订单数</span>
                                    <span>{{ companyData.orderNum }}</span>
                                </div>
                                <div class="d-box-info">
                                    <span>付款单合计金额</span>
                                    <span>{{ Number(companyData.cost).toFixed(2) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card__btn">
                            <el-button type="primary" @click="confirmToGeneratePaymentDocument">生成收款单</el-button>
                        </div>
                    </div>
                </el-card>
                <el-card v-if="!isPrepaid" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="receiptDiscountsForm" :inline="true" :model="receiptDiscountsForm" class="form-mb0 box-search">
                        <el-form-item label="折扣原因" prop="discountReason">
                            <el-select v-model="receiptDiscountsForm.discountReason" clearable placeholder="请选择">
                                <el-option v-for="item in discountReasonList" :key="item.code" :label="item.name" :value="item.name"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="折扣方式" prop="discountType">
                            <el-select v-model="receiptDiscountsForm.discountType" clearable placeholder="请选择">
                                <el-option v-for="item in discountTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="receiptDiscountsForm.discountType === '1'" prop="discount">
                            <el-tooltip content="折扣范围在0.00-1.00之间，1代表不打折" placement="top">
                                <el-icon>
                                    <el-icon>
                                        <el-icon-warning />
                                    </el-icon>
                                </el-icon>
                            </el-tooltip>
                            <el-input-number v-model="receiptDiscountsForm.discount" :disabled="!receiptDiscountsForm.discountReason" :max="1" :min="0" :precision="2" :step="0.01" placeholder="请输入" style="margin-left: 10px"></el-input-number>
                        </el-form-item>
                        <el-form-item v-if="receiptDiscountsForm.discountType === '1'" label="折扣后金额">
                            <span class="font__color__primary">{{ formatTheDiscountAmount }}</span>
                        </el-form-item>
                        <el-form-item v-if="receiptDiscountsForm.discountType === '2'" prop="discountAmount">
                            <el-input-number v-model="receiptDiscountsForm.discountAmount" :disabled="!receiptDiscountsForm.discountReason" :max="Number(companyData.cost)" :min="0" :precision="2" :step="0.01" placeholder="请输入"></el-input-number>
                        </el-form-item>
                    </el-form>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="receiptDiscountsForm" :inline="true" :model="receiptDiscountsForm" class="form-mb0 box-search">
                        <el-form-item label="结算公司" prop="settlementCompanyId">
                            <el-select ref="settlementCompanyId" v-model="receiptDiscountsForm.settlementCompanyId" :filter-method="handleInput" clearable filterable placeholder="请选择结算公司">
                                <el-option v-for="item in settlementCompanyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <!-- 纸箱费、垫付费、租箱费、其他费用、费用说明 -->
                        <el-form-item label="纸箱费" prop="cartonFee">
                            <el-input-number v-model="receiptDiscountsForm.cartonFee" :max="100000" :min="0" :precision="2" :step="1" class="number__unit__element" controls-position="right" placeholder="请输入纸箱费" style="width: 160px"></el-input-number>
                        </el-form-item>
                        <el-form-item label="垫付费" prop="advanceFee">
                            <el-input-number v-model="receiptDiscountsForm.advanceFee" :max="100000" :min="0" :precision="2" :step="1" class="number__unit__element" controls-position="right" placeholder="请输入垫付费" style="width: 160px"></el-input-number>
                        </el-form-item>
                        <el-form-item label="租箱费" prop="rentalBoxFee">
                            <el-input-number v-model="receiptDiscountsForm.rentalBoxFee" :max="100000" :min="0" :precision="2" :step="1" class="number__unit__element" controls-position="right" placeholder="请输入租箱费" style="width: 160px"></el-input-number>
                        </el-form-item>
                        <el-form-item label="其他服务费" prop="warehouseFee">
                            <el-input-number v-model="receiptDiscountsForm.warehouseFee" :max="100000" :min="0" :precision="2" :step="1" class="number__unit__element" controls-position="right" placeholder="请输入其他服务费" style="width: 160px"></el-input-number>
                        </el-form-item>
                        <el-form-item label="其他费用" prop="otherFee">
                            <el-input-number v-model="receiptDiscountsForm.otherFee" :max="100000" :min="0" :precision="2" :step="1" class="number__unit__element" controls-position="right" placeholder="请输入其他费用" style="width: 180px"></el-input-number>
                        </el-form-item>
                        <el-form-item label="费用说明" prop="feeDesc">
                            <el-input v-model="receiptDiscountsForm.feeDesc" maxlength="60" placeholder="请输入费用说明" show-word-limit />
                        </el-form-item>
                        <el-form-item label="收款单应收合计费用" prop="totalReceivableCost">
                            <span class="font__color__primary">{{ formatTheTotalReceivableCost() }}</span>
                        </el-form-item>
                    </el-form>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <column-table :columns="columnsGeneratePaymentDocument" :data="companyData.orderCostInfoList">
                        <template #transType="{ row }">
                            <span>{{ formatDictionaryData('productTypeList', row.transType) }}</span>
                        </template>
                        <template #productType="{ row }">
                            <span>{{ formatDictionaryData('productTypeList', row.productType) }}</span>
                        </template>
                        <template #temperatureType="{ row }">
                            <span>{{ formatDictionaryData('temperatureTypeDicts', row.temperatureType) }}</span>
                        </template>
                        <template #totalReceivableCost="{ row }">
                            <div style="color: #5670fe">{{ row.totalReceivableCost }}</div>
                        </template>
                        <template #status="{ row }">
                            <div v-html="formatStatus(row.status)"></div>
                        </template>
                    </column-table>
                </el-card>
            </div>
        </el-drawer>

        <!--  /订单费用详情  -->
        <order-fee-details-with-details v-model="orderCostVisible" :detail-data="detailData" :fee-breakdown-data="feeBreakdownData" />
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import OrderFeeDetailsWithDetails from '@/views/carrierFunction/OrderFeeDetailsWithDetails';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import customerStatement from '@/api/carrierEnd/customerStatement';
import RightToolbar from '@/components/RightToolbar/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import { downloadNoData } from '@/utils';
import moment from 'moment';
import accountStatement from '@/api/shipperEnd/accountStatement';
import settlementCompany from '@/api/carrierEnd/settlementCompany';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'CustomerStatement',
    components: {
        RightToolbar,
        SearchButton,
        ColumnTable,
        OrderFeeDetailsWithDetails
    },
    data() {
        return {
            queryForm: {
                current: 1,
                size: 10,
                orderNo: undefined,
                transOrderNo: undefined,
                type: '2',
                companyId: undefined,
                transType: undefined,
                productType: undefined,
                temperatureType: undefined,
                status: undefined,
                settlementMethod: undefined,
                sendCompany: undefined,
                sendAddress: [],
                sendProvinceId: undefined,
                sendCityId: undefined,
                sendCountyId: undefined,
                sendTownId: undefined,
                receiverCompany: undefined,
                settlementCompanyId: undefined,
                receiverAddress: [],
                receiverProvinceId: undefined,
                receiverCityId: undefined,
                receiverCountyId: undefined,
                receiverTownId: undefined,
                adjustName: undefined,
                queryTime: []
            },
            settlementMethodList: [],
            reconciliationStatusList: [],
            columns: [
                { title: '下单时间', key: 'orderDate', align: 'center', width: '120px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '订单号', key: 'orderNo', align: 'center', width: '120px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', minWidth: '140px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', width: '220px', columnShow: true, showOverflowTooltip: true },
                { title: '温区类型', key: 'temperatureType', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '件数', key: 'goodsPackages', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '付款方式', key: 'settlementMethod', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '调整流水号', key: 'adjustSerialNumber', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '订单类型', key: 'type', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '运输类型', key: 'transType', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '产品分类', key: 'productType', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '车型', key: 'carTypeDesc', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '公里数', key: 'kilometre', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收费用', key: 'collectCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收费用计算公式', key: 'collectFormula', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '干线费用', key: 'transportCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '干线费用计算公式', key: 'transportFormula', align: 'center', width: '130px', columnShow: true, showOverflowTooltip: true },
                { title: '配送费用', key: 'deliveryCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '配送费用计算公式', key: 'deliveryFormula', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '超区费用', key: 'exceedCountyCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '增值服务总费用', key: 'addedServicesCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '保价金额', key: 'insuranceAmount', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '保费', key: 'premium', align: 'center', labelClassName: 'isShowSummary', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '预估费用合计', key: 'estimateCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '合同价合计', key: 'totalContractPrice', labelClassName: 'isShowSummary', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '折扣金额', key: 'discountCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '应收费用合计', key: 'totalReceivableCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '调整金额', key: 'adjustCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '操作人', key: 'adjustName', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '调整日期', key: 'adjustTime', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'createDate', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '对账人', key: 'checkedName', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '对账时间', key: 'checkedTime', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '订单状态', key: 'orderStatus', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '对账状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            showSearch: true,
            loading: false,
            dataList: [],
            total: 0,
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            generatePaymentDocumentVisible: false,
            columnsGeneratePaymentDocument: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '160px', columnShow: true, fixed: 'left' },
                { title: '调整流水号', key: 'adjustSerialNumber', align: 'center', width: '180px', columnShow: true },
                { title: '下单时间', key: 'orderDate', align: 'center', width: '120px', columnShow: true },
                { title: '运输类型', key: 'transType', align: 'center', width: '120px', columnShow: true },
                { title: '温区类型', key: 'temperatureType', align: 'center', width: '120px', columnShow: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '120px', columnShow: true },
                { title: '应收费用合计', key: 'totalReceivableCost', align: 'center', width: '130px', columnShow: true },
                { title: '对账状态', key: 'status', align: 'center', width: '120px', columnShow: true }
            ],
            generatePaymentDocumentLoading: false,
            generatePaymentDocumentLoadingText: '生成中...',
            companyData: {},
            orderCostVisible: false,
            detailData: {},
            customerList: [],
            settlementManagementOrderTypeList: [],
            productTypeList: [],
            goodsTypeList: [],
            feeBreakdownData: [],
            expenseAccountList: [],
            overview: {
                totalEstimateCost: 0,
                totalAdjustCost: 0,
                totalContractPrice: 0,
                totalDiscountCost: 0,
                totalReceivableCost: 0,
                totalAmount: 0,
                checkedAmount: 0,
                toBeCheckedAmount: 0
            },
            receiptDiscountsForm: {
                discountType: undefined,
                discount: 1,
                discountReason: '',
                discountAmount: 0,
                settlementCompanyId: undefined,
                cartonFee: 0,
                advanceFee: 0,
                rentalBoxFee: 0,
                warehouseFee: 0,
                otherFee: 0,
                feeDesc: undefined
            },
            discountReasonList: [],
            sysAreas: [],
            isShowAll: false,
            temperatureTypeDicts: [],
            shortcuts: setDatePickerShortcuts(),
            discountTypeList: [],
            isPrepaid: false,
            orderStatusList: [],
            settlementCompanyList: [],
            settlementCompanyListCopy: []
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 格式化订单状态
         * @returns {(function(*): (string|string|string))|*}
         */
        formatOrderStatus() {
            return (value) => {
                if (this.orderStatusList && value) {
                    const statusText = selectDictLabel(this.orderStatusList, value) || '-';
                    if (value === '0') {
                        return `<span style="color: #B1B1B1">${statusText}</span>`;
                    } else if (value === '1') {
                        return `<span style="color: #F4AC00">${statusText}</span>`;
                    } else if (value === '2') {
                        return `<span style="color: #5670FE">${statusText}</span>`;
                    } else if (value === '3') {
                        return `<span style="color: #1ACD7E">${statusText}</span>`;
                    } else {
                        return statusText;
                    }
                }
            };
        },
        // 格式化订单状态
        formatStatus() {
            return (value) => {
                if (this.reconciliationStatusList?.length && value) {
                    const statusText = selectDictLabel(this.reconciliationStatusList, value) || '-';
                    if (value === '0') {
                        return `<span style="color: #B1B1B1">${statusText}</span>`;
                    } else if (value === '1') {
                        return `<span style="color: #F4AC00">${statusText}</span>`;
                    } else if (value === '2') {
                        return `<span style="color: #5670FE">${statusText}</span>`;
                    } else if (value === '3') {
                        return `<span style="color: #1ACD7E">${statusText}</span>`;
                    } else {
                        return statusText;
                    }
                } else {
                    return '-';
                }
            };
        },
        /**
         * 格式化折扣金额
         */
        formatTheDiscountAmount() {
            let discountAmount = 0;

            // 验证折扣是否存在
            if (this.receiptDiscountsForm.discount && this.companyData?.orderCostInfoList) {
                this.companyData.orderCostInfoList.forEach((item) => {
                    // 验证 totalReceivableCost 是否存在并为数值类型
                    const receivableCost = Number(item.totalReceivableCost);
                    if (!isNaN(receivableCost)) {
                        discountAmount += this.receiptDiscountsForm.discount * receivableCost;
                    }
                });

                // 保留2位小数并返回结果
                return discountAmount.toFixed(2);
            } else {
                return '-';
            }
        }
    },
    watch: {
        // 监听折扣原因
        'receiptDiscountsForm.discountReason': {
            handler: function (val) {
                if (!val) {
                    this.receiptDiscountsForm.discount = 1;
                }
            },
            immediate: true
        }
    },
    created() {
        this.getDict();
        this.getSettlementCompanyList();
        // 将默认设置调整为7天
        this.queryForm.startDate = moment().subtract(7, 'days').format('YYYY-MM-DD');
        this.queryForm.endDate = moment().format('YYYY-MM-DD');
        this.queryForm.queryTime = [this.queryForm.startDate, this.queryForm.endDate];

        this.handleChangesTheOrderType();
        // 获取地址级联数据
        this.sysAreas = this.getSysAreas;
    },
    methods: {
        /**
         * 关闭生成收款单弹窗
         */
        closeGeneratePaymentDocument() {
            this.generatePaymentDocumentVisible = false;
            // 重置表单 receiptDiscountsForm
            this.$refs['receiptDiscountsForm'].resetFields();
            this.isPrepaid = false;
        },
        /**
         * 确定 生成收款单
         */
        async confirmToGeneratePaymentDocument() {
            // 检查结算公司是否为空
            if (!this.receiptDiscountsForm.settlementCompanyId) {
                try {
                    await this.$confirm('结算公司未选择，是否继续？', '提示', {
                        confirmButtonText: '是',
                        cancelButtonText: '否',
                        type: 'warning',
                        buttonSize: 'default'
                    });
                } catch (error) {
                    return; // 用户点击"否"，保留在当前页面
                }
            }

            // 构建参数对象
            const params = {
                discountType: this.receiptDiscountsForm.discountType,
                discount: this.receiptDiscountsForm.discountType === '1' ? this.receiptDiscountsForm.discount : undefined,
                discountAmount: this.receiptDiscountsForm.discountType === '2' ? this.receiptDiscountsForm.discountAmount : undefined,
                discountReason: this.receiptDiscountsForm.discountReason,
                statementIdList: this.companyData.orderCostInfoList.map((item) => item.id),
                settlementCompanyId: this.receiptDiscountsForm.settlementCompanyId,
                cartonFee: this.receiptDiscountsForm.cartonFee,
                advanceFee: this.receiptDiscountsForm.advanceFee,
                rentalBoxFee: this.receiptDiscountsForm.rentalBoxFee,
                warehouseFee: this.receiptDiscountsForm.warehouseFee,
                otherFee: this.receiptDiscountsForm.otherFee,
                feeDesc: this.receiptDiscountsForm.feeDesc,
                totalAmount: this.companyData.cost
            };

            // 设置加载状态
            this.generatePaymentDocumentLoading = true;
            this.generatePaymentDocumentLoadingText = '生成中...';

            try {
                // 调用生成付款单的API
                const res = await customerStatement.createPayment(params);
                if (res.code === 200) {
                    // 成功处理
                    this.$message.success('生成收款单成功！');
                    this.handleChangesTheOrderType();
                    this.closeGeneratePaymentDocument();
                } else {
                    // 业务逻辑错误处理
                    this.$message.error(`生成收款单失败：${res.message}`);
                }
            } catch (error) {
                // 网络错误或其他异常处理
                this.$message.error('网络异常，请稍后再试');
            } finally {
                // 无论成功或失败，都关闭加载状态
                this.generatePaymentDocumentLoading = false;
            }
        },
        /**
         * 导出对账单
         */
        exportStatement() {
            // 提示框 询问是否导出全部
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;
                    if (sendAddress && sendAddress.length > 0) {
                        const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = sendAddress;
                        this.queryForm.sendProvinceId = sendProvinceId || undefined;
                        this.queryForm.sendCityId = sendCityId || undefined;
                        this.queryForm.sendCountyId = sendCountyId || undefined;
                        this.queryForm.sendTownId = sendTownId || undefined;
                    }
                    if (receiverAddress && receiverAddress.length > 0) {
                        const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = receiverAddress;
                        this.queryForm.receiverProvinceId = receiverProvinceId || undefined;
                        this.queryForm.receiverCityId = receiverCityId || undefined;
                        this.queryForm.receiverCountyId = receiverCountyId || undefined;
                        this.queryForm.receiverTownId = receiverTownId || undefined;
                    }
                    // eslint-disable-next-line no-unused-vars
                    customerStatement
                        .export({ filename: '客户对账单.xls', ...params }, '', '', 'blob')
                        .then((res) => {
                            downloadNoData(res, 'application/vnd.ms-excel', '客户对账单.xlsx');
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 格式化收款单应收合计费用金额
         */
        formatTheTotalReceivableCost() {
            // 折扣率的情况 折扣后金额+纸箱费+垫付费+租箱费+其他费用
            // 折扣金额的情况 付款单合计金额-折扣金额+纸箱费+垫付费+租箱费+其他费用
            // 不选择折扣的情况 付款单合计金额+纸箱费+垫付费+租箱费+其他费用
            let totalCost = Number(this.companyData.cost); // 付款单合计金额
            let discountType = this.receiptDiscountsForm.discountType; // 折扣方式
            let discountRate = this.receiptDiscountsForm.discount; // 折扣率
            let discountAmount = this.receiptDiscountsForm.discountAmount; // 折扣金额

            // 纸箱费、垫付费、租箱费、其他费用
            let cartonFee = this.receiptDiscountsForm.cartonFee;
            let advanceFee = this.receiptDiscountsForm.advanceFee;
            let rentalBoxFee = this.receiptDiscountsForm.rentalBoxFee;
            let warehouseFee = this.receiptDiscountsForm.warehouseFee;
            let otherFee = this.receiptDiscountsForm.otherFee;

            let totalReceivableCost; // 收款单应收合计费用

            if (discountType === '1') {
                // 折扣率的情况
                totalReceivableCost = totalCost * discountRate + cartonFee + advanceFee + rentalBoxFee + warehouseFee + otherFee;
            } else if (discountType === '2') {
                // 折扣金额的情况
                totalReceivableCost = totalCost - discountAmount + cartonFee + advanceFee + rentalBoxFee + warehouseFee + otherFee;
            } else {
                // 不选择折扣的情况
                totalReceivableCost = totalCost + cartonFee + advanceFee + rentalBoxFee + warehouseFee + otherFee;
            }

            return totalReceivableCost.toFixed(2); // 保留两位小数并返回
        },
        /**
         * 生成收款单
         */
        generatePaymentDocument() {
            let idList = [];
            let hasPrepaid = false;

            // 筛选出状态为已核对且结算方式不为预付款的对账单
            this.selectData.forEach((item) => {
                if (item.status !== '1') {
                    this.$refs.customerStatement.$refs.ColumnTable.toggleRowSelection(item, false);
                } else if (item.settlementMethod === '7') {
                    hasPrepaid = true;
                    idList.push(item.id);
                } else {
                    idList.push(item.id);
                }
            });

            // 检查是否所有选中的对账单都是预付款订单
            const isAllPrepaid = this.selectData.every((item) => item.settlementMethod === '7');

            // 如果没有选中任何对账单，显示警告
            if (idList.length === 0) {
                this.$message({
                    message: '请选择状态为已核对的对账单！',
                    type: 'warning'
                });
                return;
            }

            // 如果有预付款订单，但不是所有订单都是预付款订单，则显示警告
            if (idList.length > 0 && hasPrepaid && !isAllPrepaid) {
                this.$message({
                    message: '预付款订单只能与同一个货主的预付款订单一同生成收款单！',
                    type: 'warning'
                });
                return;
            }

            // 判断下单时间是否为同一个月份
            const orderDateList = this.selectData.map((item) => item.orderDate);
            const isSameMonth = orderDateList.every((date) => {
                const currentDate = new Date(date);
                const firstDate = new Date(orderDateList[0]);
                return currentDate.getFullYear() === firstDate.getFullYear() && currentDate.getMonth() === firstDate.getMonth();
            });

            if (!isSameMonth) {
                this.$message({
                    message: '下单时间不在同一个月份，无法生成收款单！',
                    type: 'warning'
                });
                return;
            }

            // 获取付款单数据
            customerStatement
                .getPaymentData({ idList })
                .then((res) => {
                    if (res.code === 200) {
                        this.companyData = res.data || {};
                        this.generatePaymentDocumentVisible = true;
                    } else {
                        // 如果获取数据失败，显示错误信息
                        this.$message.error('获取付款单数据失败，请稍后再试。');
                    }
                })
                .catch(() => {
                    // 网络错误或其他异常处理
                    this.$message.error('网络异常，请稍后再试。');
                });
        },
        /**
         * 全部生成收款单
         */
        generatePaymentDocumentAll() {
            // 验证必选筛选条件
            if (!this.queryForm.queryTime || this.queryForm.queryTime.length !== 2) {
                this.$message({
                    type: 'warning',
                    message: '请选择下单时间',
                    offset: 80
                });
                return;
            }
            if (!this.queryForm.companyId) {
                this.$message({
                    type: 'warning',
                    message: '请选择货主公司',
                    offset: 80
                });
                return;
            }
            if (!this.queryForm.settlementMethod) {
                this.$message({
                    type: 'warning',
                    message: '请选择付款方式',
                    offset: 80
                });
                return;
            }
            this.$confirm('是否确认全部生成收款单？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const loading = this.$loading({
                    lock: true,
                    text: '生成中...',
                    spinner: 'el-icon-loading'
                });
                customerStatement
                    .getPaymentData(this.queryForm)
                    .then((res) => {
                        if (res.code === 200) {
                            this.companyData = res.data || {};
                            this.generatePaymentDocumentVisible = true;
                        }
                    })
                    .finally(() => {
                        loading.close();
                    })
                    .catch(() => {
                        // 网络错误或其他异常处理
                        this.$message.error('网络异常，请稍后再试。');
                    });
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.reconciliationStatusList = await this.getDictList('expense_order_status');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.settlementManagementOrderTypeList = await this.getDictList('cost_settlement_management_order_type');
            this.productTypeList = await this.getDictList('fourpl_product_type');
            this.expenseAccountList = await this.getDictList('cost_order_type');
            this.temperatureTypeDicts = await this.getDictList('fourpl_temperature_type');
            this.discountReasonList = await this.getDictList('reason_for_discount');
            this.discountTypeList = await this.getDictList('discount_type');
            this.orderStatusList = await this.getDictList('fourpl_order_status');
        },
        getList() {
            this.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;

            // 获取列表数据
            customerStatement
                .statementList(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.dataList = res.data?.records || [];
                        this.total = res.data?.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });

            // 获取统计数据
            // eslint-disable-next-line no-unused-vars
            const { current, size, ...statsParams } = params;
            customerStatement
                .statementCostStatistics(statsParams)
                .then((res) => {
                    if (res.code === 200) {
                        const { totalEstimateCost, totalContractPrice, totalReceivableCost, totalAdjustCost, totalDiscountCost, checkedAmount, toBeCheckedAmount, totalAmount } = res.data || {};

                        this.overview = {
                            totalEstimateCost: totalEstimateCost * 1 || 0.0,
                            totalAdjustCost: totalAdjustCost * 1 || 0.0,
                            totalContractPrice: totalContractPrice * 1 || 0.0,
                            totalDiscountCost: totalDiscountCost * 1 || 0.0,
                            totalReceivableCost: totalReceivableCost * 1 || 0.0,
                            totalAmount: totalAmount * 1 || 0.0,
                            checkedAmount: checkedAmount * 1 || 0.0,
                            toBeCheckedAmount: toBeCheckedAmount * 1 || 0.0
                        };
                    }
                })
                .catch(() => {});
        },
        /**
         * 获取结算公司列表
         */
        async getSettlementCompanyList() {
            const res = await settlementCompany.getSettlementCompanySelectList();
            if (res.code === 200) {
                this.settlementCompanyListCopy = res.data || [];
            } else {
                this.settlementCompanyListCopy = [];
            }
        },
        /**
         * 改变订单类型
         */
        handleChangesTheOrderType() {
            this.handleQuery();
            // eslint-disable-next-line no-unused-vars
            const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;
            customerStatement
                .getCustomerList(params)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.customerList = res.data || [];
                    } else {
                        this.customerList = [];
                        this.queryForm.companyId = null;
                    }
                })
                .catch(() => {
                    this.customerList = [];
                    this.queryForm.companyId = null;
                });
        },
        /**
         * 批量核对
         */
        handleClickBulkChecks() {
            // 遍历 this.selectData id 赋值为 idList
            let idList = [];
            this.selectData.forEach((item) => {
                // item status 为 0 时才能调整 其他情况 不选中 toggleRowSelection 只有第一次提示 之后不提示
                if (item.status !== '0') {
                    this.$refs.customerStatement.$refs.ColumnTable.toggleRowSelection(item, false);
                } else {
                    idList.push(item.id);
                }
            });
            if (idList.length === 0) {
                this.$message({
                    message: '请选择状态为待核对的对账单！',
                    type: 'warning'
                });
                return;
            }

            this.$confirm('是否确认核对？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    // 执行核对操作
                    const loading = this.$loading({
                        lock: true,
                        text: '核对中...',
                        spinner: 'el-icon-loading'
                    });
                    accountStatement
                        .batchCheck(idList)
                        .then((res) => {
                            if (res.code === 200) {
                                if (res.data.successNum) {
                                    this.$message({
                                        type: 'success',
                                        message: res.data.successNum
                                    });
                                }
                                if (res.data.errorNum) {
                                    this.$message({
                                        type: 'error',
                                        message: res.data.errorNum,
                                        offset: 80
                                    });
                                }
                            }
                            this.handleChangesTheOrderType();
                        })
                        .catch(() => {})
                        .finally(() => {
                            loading.close();
                        });
                })
                .catch(() => {});
        },
        /**
         * 批量撤销
         */
        handleClickBulkRevoke() {
            // 对账单状态为"已核对"的对账单允许发起撤销
            let idList = [];
            this.selectData.forEach((item) => {
                if (item.status === '1') {
                    idList.push(item.id);
                } else {
                    this.$refs.customerStatement.$refs.ColumnTable.toggleRowSelection(item, false);
                }
            });
            if (idList.length === 0) {
                this.$message({
                    message: '请选择状态为已核对的对账单！',
                    type: 'warning'
                });
                return;
            }
            this.$confirm('是否确认撤销？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const loading = this.$loading({
                        lock: true,
                        text: '撤销中...',
                        spinner: 'el-icon-loading'
                    });
                    customerStatement
                        .cancelCheck(idList)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message({
                                    type: 'success',
                                    message: '撤销成功'
                                });
                            }
                            this.handleChangesTheOrderType();
                        })
                        .catch(() => {})
                        .finally(() => {
                            loading.close();
                        });
                })
                .catch(() => {});
        },
        // 查看费用详情
        handleClickFeeDetails(data) {
            const { id } = data;
            customerStatement
                .statementDetail({ id })
                .then((res) => {
                    if (res.code === 200) {
                        this.detailData = res.data;
                        this.orderCostVisible = true;
                        const { statementAddedServiceList, statementCostList } = res.data;
                        let addedServicesFilter = [];
                        if (statementAddedServiceList) {
                            // statementAddedServiceList addedServicesContractPrice 变为 costContractPrice, addedServicesCost 变为 costData,addedServicesName 变为 costType 赋值给 statementAddedServiceList
                            // 使用 for of 循环遍历 statementAddedServiceList
                            for (const item of statementAddedServiceList) {
                                item.costContractPrice = item.addedServicesContractPrice;
                                item.costData = item.addedServicesCost;
                                item.costType = item.addedServicesName;
                            }
                            // 去除 statementAddedServiceList 中 costData 和 costContractPrice 都为 '0' 的数据 其中一个为 '0' 的数据不去除
                            // 赋值给 addedServicesFilter
                            addedServicesFilter = statementAddedServiceList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
                        }
                        let costListFilter = [];
                        if (statementCostList) {
                            // 筛选 statementCostList 中 costData 和 costContractPrice 都不等于0的数据
                            costListFilter = statementCostList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
                            costListFilter.sort((a, b) => {
                                return a.costType - b.costType;
                            });
                        }
                        // statementAddedServiceList 为 null 解构赋值给 this.feeBreakdownData []
                        this.feeBreakdownData = [...(addedServicesFilter || []), ...(costListFilter || [])];
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .catch(() => {});
        },
        handleInput(e) {
            if (e) {
                // 去掉所有空格后再进行过滤
                const searchText = e.replace(/\s+/g, '');
                this.settlementCompanyList = this.settlementCompanyListCopy.filter((item) => {
                    return item.name.replace(/\s+/g, '').includes(searchText);
                });
            } else {
                this.settlementCompanyList = this.settlementCompanyListCopy;
            }
        },
        handleQuery() {
            this.queryForm.current = 1;
            const { queryTime, sendAddress, receiverAddress } = this.queryForm;

            // 验证queryTime是否包含两个合法的日期字符串
            const isValidDateRange = queryTime && queryTime.length === 2 && !queryTime.some((date) => date === 'Invalid Date');

            if (isValidDateRange) {
                this.queryForm.startDate = queryTime[0] + ' 00:00:00';
                this.queryForm.endDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startDate = null;
                this.queryForm.endDate = null;
            }
            if (sendAddress && sendAddress.length > 0) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = sendAddress;
                this.queryForm.sendProvinceId = sendProvinceId || undefined;
                this.queryForm.sendCityId = sendCityId || undefined;
                this.queryForm.sendCountyId = sendCountyId || undefined;
                this.queryForm.sendTownId = sendTownId || undefined;
            } else {
                this.queryForm.sendProvinceId = undefined;
                this.queryForm.sendCityId = undefined;
                this.queryForm.sendCountyId = undefined;
                this.queryForm.sendTownId = undefined;
            }
            if (receiverAddress && receiverAddress.length > 0) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = receiverAddress;
                this.queryForm.receiverProvinceId = receiverProvinceId || undefined;
                this.queryForm.receiverCityId = receiverCityId || undefined;
                this.queryForm.receiverCountyId = receiverCountyId || undefined;
                this.queryForm.receiverTownId = receiverTownId || undefined;
            } else {
                this.queryForm.receiverProvinceId = undefined;
                this.queryForm.receiverCityId = undefined;
                this.queryForm.receiverCountyId = undefined;
                this.queryForm.receiverTownId = undefined;
            }
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            // 遍历 selection中的 id status 赋值给 this.selectData
            this.selectData = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.queryForm.sendAddress = [];
            this.queryForm.sendProvinceId = undefined;
            this.queryForm.sendCityId = undefined;
            this.queryForm.sendCountyId = undefined;
            this.queryForm.sendTownId = undefined;
            this.queryForm.receiverAddress = [];
            this.queryForm.receiverProvinceId = undefined;
            this.queryForm.receiverCityId = undefined;
            this.queryForm.receiverCountyId = undefined;
            this.queryForm.receiverTownId = undefined;
            this.handleChangesTheOrderType();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(thead th) {
    border-right: none !important;
}

:deep(.form-mb0 .el-form-item) {
    margin-bottom: 4px;
    margin-top: 4px;
}

:deep(.el-drawer__header) {
    margin-bottom: 20px;
}

:deep(label.el-radio) {
    margin-right: 8px;
}

:deep(.box-footer) {
    padding-left: 8px;
    display: flex;
    justify-content: space-between;

    .el-form-item__content {
        margin-left: 0 !important;
    }
}

:deep(.el-button-group > .el-button:not(:last-child)) {
    margin-right: 8px;
}

:deep(.el-tabs__header.is-top) {
    margin-bottom: 0;
}

:deep(.el-tabs__nav-scroll) {
    padding-left: 32px;
}

:deep(.card-pb-10 .el-card__body) {
    padding-bottom: 10px;
}

:deep(.el-dialog__header) {
    padding-bottom: 20px;
}

:deep(.el-table__footer-wrapper .cell.isShowSummary) {
    font-weight: bold;
    color: #5670fe;
}
.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.box__PromptText {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #999999;

    > span {
        font-size: 14px;
    }
}

.expenseAdjustment__top {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 30px;

    .top__title {
        color: #666666;
    }
}

.generatePaymentDocument__card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;

    .card__Describe {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 16px;
    }
}

.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}

.card__two__column {
    .d-box-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        font-size: 14px;

        .d-box-column {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            .d-box-info {
                display: flex;
                justify-content: space-between;

                :nth-child(1) {
                    flex-shrink: 0;
                    flex-grow: 0;
                    text-align: left;
                    margin-right: 10px;
                    color: #999999;
                }

                :nth-child(2) {
                    color: #333333;
                }
            }
        }

        .d-box-column:first-child {
            border-right: 1px solid #e6ebf5;
            padding-right: 10px;
        }

        .d-box-column:last-child {
            padding-left: 10px;
        }
    }
}

.font__color__primary {
    color: #5670fe;
}

pre {
    color: #666666;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.number__unit__element {
    position: relative;

    :deep(.el-input__inner) {
        text-align: left;
    }

    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}

:deep(.self-label-color) {
    .el-form-item__label {
        color: #ff8c00;
    }
}
</style>
