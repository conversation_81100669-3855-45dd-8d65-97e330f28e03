<template>
    <div class="app-container">
        <!--搜索-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item label="物流名称" prop="flowName">
                    <el-input v-model="queryParams.flowName" clearable maxlength="20" placeholder="请输入物流名称" show-word-limit @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <div>
                    <el-button v-hasPermi="['three:flow:add']" icon="el-icon-plus" size="mini" type="primary" @click="handleAdd">新增</el-button>
                    <el-button v-hasPermi="['three:flow:delete']" :disabled="multiple" icon="el-icon-delete" size="mini" type="danger" @click="handleDelete">批量删除</el-button>
                </div>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" :tableID="'TripartiteLogistics'" @queryTable="getList"></right-toolbar>
            </div>

            <column-table key="TripartiteLogistics" :max-height="600" v-loading="loading" v-model:columns="columns" :data="flowList" :show-check-box="true" border element-loading-text="加载中..." @selection-change="handleSelectionChange">
                <template #isSendCustomer="{ row }">
                    <span>{{ isSendCustomerFormat(row) }}</span>
                </template>
                <template #status="{ row }">
                    <span>{{ statusFormat(row) }}</span>
                </template>
                <template #opt="scope">
                    <el-button v-hasPermi="['three:flow:edit']" icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button v-hasPermi="['three:flow:delete']" icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </column-table>
            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
            </div>
        </el-card>

        <!-- 添加或修改三方物流信息对话框 -->
        <el-dialog v-model="open" v-dialogDrag :title="title" append-to-body width="650px">
            <el-form ref="form" :model="form" :rules="rules" label-width="150px">
                <el-form-item label="三方物流公司名称" prop="flowName">
                    <el-input v-model="form.flowName" maxlength="20" placeholder="请输入三方物流名称" show-word-limit />
                </el-form-item>
                <el-form-item label="三方物流公司性质" prop="isSendCustomer">
                    <el-radio v-for="(item, index) in isSendCustomerOptions" v-model="form.isSendCustomer" :label="item.value">{{ item.name }}</el-radio>
                </el-form-item>
                <el-form-item label="联系人" prop="userName">
                    <el-input v-model="form.userName" maxlength="20" placeholder="请输入联系人" show-word-limit />
                </el-form-item>
                <el-form-item label="联系电话" prop="phone">
                    <el-input v-model="form.phone" placeholder="请输入联系电话" />
                </el-form-item>
                <el-form-item label="三方物流状态">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.name }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="60" placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import serviceNetwork from '@/api/logisticsConfiguration/serviceNetwork.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
export default {
    name: 'TripartiteLogistics',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 三方物流信息表格数据
            flowList: [],
            columns: [
                { title: '三方物流公司名称', key: 'flowName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '三方物流公司性质', key: 'isSendCustomer', align: 'center', minWidth: '130px', columnShow: true },
                { title: '联系人', key: 'userName', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '联系电话', key: 'phone', align: 'center', minWidth: '130px', columnShow: true, showOverflowTooltip: true },
                { title: '三方物流状态', key: 'status', align: 'center', minWidth: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '150px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                flowCode: null,
                flowName: null,
                status: null
            },
            // 表单参数
            form: {
                flowName: '',
                isSendCustomer: '0',
                userName: '',
                phone: '',
                remark: ''
            },
            // 表单校验
            rules: {
                flowName: [{ required: true, message: '三方物流名称不能为空', trigger: 'blur' }],
                isSendCustomer: [{ required: true, message: '三方物流性质', trigger: 'blur' }],
                phone: [
                    {
                        validator: (rule, value, callback) => {
                            if (value && !/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value)) {
                                callback(new Error('请输入正则的联系方式'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ]
            },
            statusOptions: [],
            isSendCustomerOptions: [
                {
                    name: '普通三方公司',
                    value: '0'
                },
                {
                    name: '送达客户公司',
                    value: '1'
                }
            ]
        };
    },
    async created() {
        this.statusOptions = await this.getDictList('sys_normal_disable');
        this.getList();
    },
    methods: {
        /** 状态字典转换 */
        statusFormat(val) {
            return this.selectDictLabel(this.statusOptions, val.status);
        },
        /** 三方物流公司性质 */
        isSendCustomerFormat(val) {
            return this.selectDictLabel(this.isSendCustomerOptions, val.isSendCustomer);
        },
        /** 查询三方物流信息列表 */
        getList() {
            this.loading = true;
            this.flowList = [];
            serviceNetwork.listThreePartFlow(this.queryParams).then((response) => {
                if (response.code === 200 && response.data) {
                    this.flowList = response.data.records || [];
                    this.total = response.data.total || 0;
                }
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                flowCode: null,
                flowName: null,
                status: '0',
                delFlag: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: null,
                isSendCustomer: '0',
                userName: '',
                phone: ''
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        resetForm(formName) {
            this.$refs[formName] ? this.$refs[formName].resetFields() : '';
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.names = selection.map((item) => item.flowName);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加三方物流信息';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            serviceNetwork.queryThreePartFlowById({ id }).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = '修改三方物流信息';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        serviceNetwork.saveThreePartFlow(this.form).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        serviceNetwork.saveThreePartFlow(this.form).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids.join(',');
            const names = row.flowName || this.names;
            this.$confirm('是否确认删除三方物流信息"' + names + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return serviceNetwork.delThreePartFlow({ ids: ids });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'three/flow/export',
                {
                    ...this.queryParams
                },
                `three_flow.xlsx`
            );
        }
    }
};
</script>
<style scoped>
.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
</style>
