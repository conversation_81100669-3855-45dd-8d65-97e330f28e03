<template>
    <div>
        <el-dialog v-model="dialogVisible" :before-close="close" :title="title" width="85%">
            <div v-loading="viewLoading">
                <!--步骤-->
                <el-steps v-if="type != 'view'" :active="stepAction" align-center>
                    <el-step title="选择到款记录" />
                    <el-step title="选择支付信息" />
                    <el-step title="提交认款申请" />
                </el-steps>
                <div v-if="type != 'view'" v-show="stepAction == 1">
                    <!--搜索-->
                    <el-form ref="incomingQueryForm" :inline="true" :model="incomingQueryForm" class="seache-form" label-width="120px" @submit.native.prevent>
                        <el-form-item label="到款单位/人" prop="arriveUnit">
                            <el-input v-model="incomingQueryForm.arriveUnit" clearable placeholder="请输入到款单位/人" @keyup.enter.native="handleQuery('incoming')" />
                        </el-form-item>
                        <el-form-item label="认款日期" prop="queryTime" style="width: 310px">
                            <el-date-picker
                                v-model="incomingQueryForm.queryTime"
                                :shortcuts="shortcuts"
                                end-placeholder="结束日期"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                unlink-panels
                                value-format="YYYY-MM-DD"
                                @change="handleQuery('incoming')"
                            ></el-date-picker>
                        </el-form-item>
                        <el-form-item class="search-btn">
                            <el-button icon="el-icon-search" type="primary" @click="handleQuery('incoming')">查询</el-button>
                        </el-form-item>
                    </el-form>
                    <!--表格数据-->
                    <column-table key="incomingTable" ref="incomingTable" :columns="incomingColumns" :data="incomingDataList" :show-check-box="true" highlight-current-row rowKey="id" show-index @selection-change="(selection) => handleSelectionChange(selection, 'Incoming')">
                        <template #arriveDate="{ row }">
                            <span>{{ row.arriveDate ? formatDate(row.arriveDate) : '--' }}</span>
                        </template>
                        <template #status="{ row }">
                            <span>{{ row.status ? selectDictLabel(statusOptions, row.status) : '--' }}</span>
                        </template>
                        <template #arriveSource="{ row }">
                            <span>{{ selectDictLabel(arriveSourceOptions, row.arriveSource)}}</span>
                        </template>
                        <template #theAmountOfThisSubscription="{ row }">
                            <div>
                                <el-input-number
                                    v-if="row.theAmountOfThisSubscriptionShow"
                                    v-model="row.theAmountOfThisSubscription"
                                    :max="row.stayAmount"
                                    :min="0"
                                    :precision="2"
                                    clearable
                                    placeholder="认款金额"
                                    style="width: 100%; height: 30px"
                                    @blur="handleInputBlur(row, 'theAmountOfThisSubscription')"
                                    @keyup.enter.native="handleInputBlur(row, 'theAmountOfThisSubscription')"
                                />
                                <el-tooltip v-else class="item" content="单击编辑" effect="dark" placement="left">
                                    <div class="text-red-500" @click="incomingClick(row, 'theAmountOfThisSubscription')">
                                        <i class="mr5 el-icon-edit-outline"></i>
                                        <span>{{ row.theAmountOfThisSubscription || '请输入认款金额' }}</span>
                                    </div>
                                </el-tooltip>
                            </div>
                        </template>
                    </column-table>
                    <div class="flex justify-between">
                        <div class="text-main-500 font-bold">到款金额：{{ calculatedAmount(selectIncomingRows, 'theAmountOfThisSubscription') }}</div>
                        <pagination v-show="incomingQueryForm.total > 0" v-model:limit="incomingQueryForm.size" v-model:page="incomingQueryForm.current" :total="incomingQueryForm.total" @pagination="getIncomingList" />
                    </div>
                </div>
                <div v-if="type != 'view'" v-show="stepAction == 2">
                    <!--搜索-->
                    <el-form ref="paymentQueryForm" :inline="true" :model="paymentQueryForm" class="seache-form" label-width="120px" @submit.native.prevent>
                        <el-form-item label="付款公司" prop="companyId">
                            <el-select v-model="paymentQueryForm.companyId" clearable filterable placeholder="请选择付款公司" style="width: 200px" @change="handleQuery('payment')">
                                <el-option v-for="item in companyIdList" :key="item.id" :label="item.name" :value="item.id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="时间" prop="queryTime" style="width: 310px">
                            <el-date-picker
                                v-model="paymentQueryForm.queryTime"
                                :shortcuts="shortcuts"
                                end-placeholder="结束日期"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                unlink-panels
                                value-format="YYYY-MM-DD"
                                @change="handleQuery('payment')"
                            ></el-date-picker>
                        </el-form-item>
                        <el-form-item class="search-btn">
                            <el-button icon="el-icon-search" type="primary" @click="handleQuery('payment')">查询</el-button>
                        </el-form-item>
                    </el-form>
                    <!--表格数据-->
                    <column-table key="paymentTable" ref="paymentTable" v-loading="paymentLoading" :columns="paymentColumns" :data="paymentDataList" :show-check-box="true" rowKey="id" show-index @selection-change="(selection) => handleSelectionChange(selection, 'Payment')">
                        <template #businessType="{ row }">
                            <span :style="setBusinessTypeColor(row.businessType)">{{ selectDictLabel(businessTypeList, row.businessType) }}</span>
                        </template>
                        <template #status="{ row }">
                            <span :style="setStatusColor(row.status)">{{ selectDictLabel(statusList, row.status) }}</span>
                        </template>
                        <template #receiveCompany="{ row }">
                            <span>{{ row.receiveCompany ? selectDictLabel(receiverCompanyOptions, row.receiveCompany) : '--' }}</span>
                        </template>
                    </column-table>
                    <!--                <pagination v-show="paymentQueryForm.total > 0" v-model:limit="paymentQueryForm.size" v-model:page="paymentQueryForm.current" :total="paymentQueryForm.total" @pagination="getPaymentList" />-->
                </div>
                <div v-show="type == 'view' || stepAction == 3">
                    <el-descriptions v-if="type == 'view'">
                        <el-descriptions-item v-if="applyInfo?.claimApply?.applyNo" label="申请编号：">{{ applyInfo.claimApply.applyNo }}</el-descriptions-item>
                        <el-descriptions-item v-if="applyInfo?.claimApply?.applyUser" label="申请人：">{{ applyInfo.claimApply.applyUser }}</el-descriptions-item>
                        <el-descriptions-item v-if="applyInfo?.claimApply?.claimDate" label="申请时间：">{{ applyInfo.claimApply.claimDate }}</el-descriptions-item>
                    </el-descriptions>
                    <!--到款信息-->
                    <div>
                        <h3 style="margin: 15px 0 10px">到款信息</h3>
                        <!--表格数据-->
                        <column-table key="incomingViewTable" ref="incomingViewTable" :columns="incomingColumns" :data="selectIncomingRows" rowKey="id" show-index>
                            <template #status="{ row }">
                                <span>{{ row.status ? selectDictLabel(statusOptions, row.status) : '--' }}</span>
                            </template>
                            <template #arriveDate="{ row }">
                                <span>{{ row.arriveDate ? formatDate(row.arriveDate) : '--' }}</span>
                            </template>
                            <template #arriveSource="{ row }">
                                <span>{{ selectDictLabel(arriveSourceOptions, row.arriveSource)}}</span>
                            </template>
                            <template #accountAmount="{ row }">
                                <span>{{ row.accountAmount?row.accountAmount:'--'}}</span>
                            </template>
                            <template #theAmountOfThisSubscription="{ row }">
                                <div v-if="type != 'view'">
                                    <el-input-number
                                        v-if="row.theAmountOfThisSubscriptionShow"
                                        v-model="row.theAmountOfThisSubscription"
                                        :max="row.stayAmount"
                                        :min="0"
                                        :precision="2"
                                        clearable
                                        placeholder="认款金额"
                                        style="width: 100%; height: 30px"
                                        @blur="handleInputBlur(row, 'theAmountOfThisSubscription')"
                                        @keyup.enter.native="handleInputBlur(row, 'theAmountOfThisSubscription')"
                                    />
                                    <el-tooltip v-else class="item" content="单击编辑" effect="dark" placement="left">
                                        <div class="text-red-500" @click="incomingClick(row, 'theAmountOfThisSubscription')">
                                            <i class="mr5 el-icon-edit-outline"></i>
                                            <span>{{ row.theAmountOfThisSubscription || '请输入认款金额' }}</span>
                                        </div>
                                    </el-tooltip>
                                </div>
                                <div v-else>
                                    <div class="text-red-500">
                                        <i class="mr5 el-icon-edit-outline"></i>
                                        <span>{{ row.theAmountOfThisSubscription }}</span>
                                    </div>
                                </div>
                            </template>
                        </column-table>
                        <div class="text-main-500 font-bold">合计认款金额：{{ calculatedAmount(selectIncomingRows, 'theAmountOfThisSubscription') }}</div>
                    </div>
                    <!--支付信息-->
                    <div>
                        <h3 style="margin: 15px 0 10px">支付信息</h3>
                        <!--表格数据-->
                        <column-table key="paymentViewTable" ref="paymentViewTable" :columns="paymentColumns" :data="selectPaymentRows" rowKey="id" show-index>
                            <template #businessType="{ row }">
                                <span :style="setBusinessTypeColor(row.businessType)">{{ selectDictLabel(businessTypeList, row.businessType) }}</span>
                            </template>
                            <template #status="{ row }">
                                <span :style="setStatusColor(row.status)">{{ selectDictLabel(statusList, row.status) }}</span>
                            </template>
                            <template #receiveCompany="{ row }">
                                <span>{{ row.receiveCompany ? selectDictLabel(receiverCompanyOptions, row.receiveCompany) : '--' }}</span>
                            </template>
                        </column-table>
                        <div class="text-main-500 font-bold">合计支付金额：{{ calculatedAmount(selectPaymentRows, 'remitAmount') }}</div>
                    </div>

                    <!--提交表单-->
                    <el-form ref="baseForm" :model="baseForm" class="mt10" @submit.native.prevent>
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="baseForm.remark" :disabled="type == 'view'" class="w-full" clearable maxlength="100" placeholder="请输入备注" show-word-limit type="textarea" />
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <template #footer>
                <el-button v-if="type == 'view' || stepAction == 1" @click="close">取 消</el-button>
                <el-button v-if="type != 'view' && stepAction != 1" @click="prevClick">上一步</el-button>
                <el-button v-if="type != 'view' && stepAction != 3" type="primary" @click="nextClick">下一步</el-button>
                <el-button v-if="type != 'view' && stepAction == 3" :loading="submitLoading" type="primary" @click="submitForm">{{ submitText }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import receiptOfPayment from '@/api/carrierEnd/receiveThePayment/receiptOfPayment';
import receiptRecords from '@/api/carrierEnd/receiveThePayment/receiptRecords';
import { ElMessageBox, ElMessage } from 'element-plus';
import moment from 'moment';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
export default {
    name: 'SubscriptionApply',
    components: {
        ColumnTable
    },
    model: {
        prop: 'visible',
        event: 'update:visible'
    },
    props: {
        applyId: {
            type: String,
            default: undefined
        },
        title: {
            type: String,
            default: undefined
        },
        visible: {
            type: Boolean,
            default: false
        },
        type: {
            // view查看，add新增，edit编辑
            type: String,
            default: 'add'
        }
    },
    data() {
        return {
            dialogVisible: this.visible, // 控制弹窗显示
            stepAction: 1, // 当前激活步骤
            shortcuts: [
                {
                    text: '无',
                    value: (e) => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: (e) => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ],
            // 到款记录查询参数
            incomingQueryForm: {
                total: 0,
                current: 1,
                size: 10,
                arriveUnit: undefined,
                queryTime: []
            },
            incomingLoading: false, // 到款记录加载状态
            incomingColumns: [
                { title: '到款日期', key: 'arriveDate', align: 'center', width: '110px', columnShow: true },
                { title: '到款单位/人', key: 'arriveUnit', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '到款金额', key: 'arriveAmount', align: 'center', width: '100px', columnShow: true },
                { title: '到款渠道', key: 'arriveSource', align: 'center', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '入账金额', key: 'accountAmount', align: 'center', width: '100px', columnShow: true },
                { title: '认款状态', key: 'status', align: 'center', width: '80px', columnShow: true },
                { title: '认款日期', key: 'claimDate', align: 'center', width: '110px', columnShow: true },
                { title: '认款金额', key: 'claimAmount', align: 'center', width: '100px', columnShow: true },
                { title: '待认款金额', key: 'stayAmount', align: 'center', width: '100px', columnShow: true },
                { title: '本次认款金额', key: 'theAmountOfThisSubscription', fixed: 'right', align: 'center', width: '160px', columnShow: true }
            ], // 到款记录列
            incomingDataList: [], // 数据列表
            statusOptions: [], // 认款状态
            selectIncomingRows: [], // 到款记录选中行
            // 到款记录查询参数
            paymentQueryForm: {
                total: 0,
                current: 1,
                size: 10,
                companyId: undefined,
                queryTime: []
            },
            paymentLoading: false, // 支付信息加载状态
            paymentColumns: [
                { title: '汇款时间', key: 'remitTime', align: 'center', width: '160px', columnShow: true },
                { title: '付款类型', key: 'businessType', align: 'center', minWidth: '100px', columnShow: true },
                { title: '付款公司', key: 'ownerName', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '收款公司', key: 'receiveCompany', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '汇款金额（元）', key: 'remitAmount', align: 'center', minWidth: '120px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settleCompany', align: 'center', width: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '审批状态', key: 'status', align: 'center', fixed: 'right', minWidth: '160px', columnShow: true }
            ], // 支付信息列
            paymentDataList: [], // 数据列表
            selectPaymentRows: [], // 支付信息选中行
            businessTypeList: [], // 付款类型
            statusList: [], // 审批状态
            companyIdList: [], // 付款公司
            receiverCompanyOptions: [], // 收款公司
            arriveSourceOptions:[], // 到款渠道
            baseForm: {
                remark: undefined
            },
            viewLoading: false, // 查看详情加载状态
            applyInfo: {}, // 申请信息
            submitLoading: false, // 提交加载状态
            submitText: '提 交' // 提交按钮文本
        };
    },
    computed: {
        /**
         * 设置业务类型颜色
         */
        setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': {}
                    }[businessType] || { color: '#999' }
                );
            };
        },
        /**
         * 设置审批状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' },
                        '3': { color: '#999' }
                    }[status] || { color: '#999' }
                );
            };
        },
        /**
         * 计算属性：计算某个属性key的和
         * @returns {function(*, *): *}
         */
        calculatedAmount() {
            return (data, key) => {
                // 计算属性key的和 保留两位小数
                return data
                    .reduce((total, item) => {
                        return total + parseFloat(item[key]);
                    }, 0)
                    .toFixed(2);
            };
        },
        /**
         * 格式化日期
         * @returns {function(*=): *}
         */
        formatDate() {
            return function (date) {
                return moment(date).format('YYYY-MM-DD');
            };
        }
    },
    async created() {
        await this.getDict();
        if (this.type == 'add') {
            await this.getIncomingList();
            await this.getPaymentList();
        }
        if (this.type == 'add' || this.type == 'edit') {
            const orgKey = this.$TOOL.data.get('orgKey');
            // 货主下拉接口
            await this.getApplyCompanySelect(this.$TOOL.data.get('Organization')[orgKey].id);
        }
        if (this.type == 'view' || this.type == 'edit') {
            this.getDetail();
        }
    },
    methods: {
        /**
         * 获取详情
         */
        getDetail() {
            this.viewLoading = true;
            receiptOfPayment
                .queryById({ id: this.applyId })
                .then((res) => {
                    if (res.code == 200) {
                        this.applyInfo = res.data;

                        // 到款信息
                        this.incomingDataList = res.data.claimArriveList.map((item) => {
                            item['theAmountOfThisSubscriptionShow'] = false;
                            item.currentClaimAmount = parseFloat(item.currentClaimAmount);
                            item.stayAmount = parseFloat(item.stayAmount) + item.currentClaimAmount;
                            item.theAmountOfThisSubscription = item.currentClaimAmount;
                            this.selectIncomingRows.push(item);
                            this.$nextTick(() => {
                                if (this.type == 'edit') this.$refs.incomingTable.toggleRowSelection(item, true);
                            });
                            return item;
                        });
                        // 支付信息
                        this.paymentDataList = res.data.claimPayApplyList.map((item) => {
                            // 转为浮点数
                            item.remitAmount = parseFloat(item.remitAmount);
                            this.selectPaymentRows.push(item);
                            this.$nextTick(() => {
                                if (this.type == 'edit') this.$refs.paymentTable.toggleRowSelection(item, true);
                            });
                            return item;
                        });
                        if (this.applyInfo?.claimApply?.remark) this.baseForm.remark = this.applyInfo.claimApply.remark;
                    }
                })
                .finally(() => {
                    this.viewLoading = false;
                });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            // 付款类型
            this.businessTypeList = await this.getDictList('payment_type');
            // 审批状态
            this.statusList = await this.getDictList('payment_approval_type');
            // 认款状态
            this.statusOptions = await this.getDictList('fourpl_subscription_status');
            // 收款公司
            this.receiverCompanyOptions = await this.getDictList('signing_company');
            // 到款渠道
            this.arriveSourceOptions = await this.getDictList('fourpl_arrive_source');
        },
        /**
         * 到款记录 获取数据
         */
        async getIncomingList() {
            this.incomingLoading = true;
            this.incomingDataList = [];
            const { queryTime, total, ...params } = this.incomingQueryForm;
            params.startArriveDate = null;
            params.endArriveDate = null;
            if (queryTime != undefined && queryTime.length != 0 && queryTime[0] != 'Invalid Date') {
                params.startArriveDate = queryTime[0] + ' 00:00:00';
                params.endArriveDate = queryTime[1] + ' 23:59:59';
            }
            params.claimStatus = '1,2';
            try {
                const res = await receiptRecords.getMoneyRecordList(params);
                if (res.code == 200) {
                    this.incomingDataList = res.data.records.map((item) => {
                        item['theAmountOfThisSubscriptionShow'] = false;
                        item.stayAmount = parseFloat(item.stayAmount);
                        item.theAmountOfThisSubscription = item.stayAmount;
                        return item;
                    });
                    //	编辑时选中已认款的数据
                    if (this.type == 'edit' && this.applyInfo?.claimArriveList?.length > 0) {
                        this.$nextTick(() => {
                            this.applyInfo.claimArriveList.forEach((item2) => {
                                let index = this.incomingDataList.findIndex((item) => item.id == item2.id);
                                if (index > -1) {
                                    this.incomingDataList[index].stayAmount = item2.currentClaimAmount + this.incomingDataList[index].stayAmount; //处理编辑时，认款金额为0的情况
                                    this.incomingDataList[index].theAmountOfThisSubscription = item2.currentClaimAmount; //处理编辑时，本次认款金额为0的情况
                                    this.selectIncomingRows.push(this.incomingDataList[index]);
                                    this.$refs.incomingTable.toggleRowSelection(this.incomingDataList[index], true);
                                }
                            });
                        });
                    }
                    this.incomingQueryForm.total = res.data.total || 0;
                }
            } catch {
                this.incomingQueryForm.total = 0;
            } finally {
                this.incomingLoading = false;
            }
        },
        /**
         * 点击到款记录
         * @param row
         * @param column
         */
        incomingClick(row, key) {
            if (!row.theAmountOfThisSubscription || row.theAmountOfThisSubscription == 0 || row.stayAmount == 0) {
                return this.msgError('无法操作待认款金额为0的数据');
            }
            if (key != 'theAmountOfThisSubscription') return;
            row[key + 'Show'] = true;
        },
        /**
         * 输入框失去焦点
         * @param row
         * @param property
         */
        handleInputBlur(row, property) {
            // 正则判断是否是正数
            if (!/^\d+(.\d{0,2})?$/.test(row[property]) || row[property] == 0) {
                this.msgError('请输入大于0的正数');
                return;
            }
            if (row[property] > row.stayAmount) {
                this.msgError('认款金额不能大于待认款金额');
                return;
            }
            row[property] = parseFloat(row[property]);
            row[property + 'Show'] = false;
        },
        /**
         * 支付信息 获取数据
         */
        async getPaymentList() {
            this.paymentLoading = true;
            this.paymentDataList = [];
            this.paymentQueryForm.total = 0;

            const { queryTime, total, ...params } = this.paymentQueryForm;
            params.queryStartDate = null;
            params.queryEndDate = null;
            if (queryTime != undefined && queryTime.length != 0 && queryTime[0] != 'Invalid Date') {
                params.queryStartDate = queryTime[0] + ' 00:00:00';
                params.queryEndDate = queryTime[1] + ' 23:59:59';
            }
            params.status = '1';
            params.claimStatus = '1,2';
            try {
                const res = await receiptOfPayment.getPaymentApply(params);
                if (res.code == 200) {
                    this.paymentDataList = res.data.map((item) => {
                        // 转为浮点数
                        item.remitAmount = parseFloat(item.remitAmount);
                        return item;
                    });
                    //	编辑时选中已认款的数据
                    if (this.type == 'edit' && this.applyInfo?.claimPayApplyList?.length > 0) {
                        this.$nextTick(() => {
                            this.applyInfo.claimPayApplyList.forEach((item2) => {
                                let item = this.paymentDataList.find((item) => item.id == item2.id);
                                if (item) {
                                    if (item2.id == item.id) {
                                        this.selectPaymentRows.push(item);
                                        this.$refs.paymentTable.toggleRowSelection(item, true);
                                    }
                                } else {
                                    this.paymentDataList.push(item2);
                                    this.selectPaymentRows.push(item2);
                                    this.$refs.paymentTable.toggleRowSelection(item2, true);
                                }
                            });
                        });
                    }
                    // this.paymentQueryForm.total = res.data.total || 0;
                }
            } finally {
                this.paymentLoading = false;
            }
        },
        /**
         * 货主下拉接口
         */
        async getApplyCompanySelect(val) {
            const res = await paymentOrderApproval.getApplyCompanySelect(val);
            if (res.code === 200) {
                this.companyIdList = res.data;
            } else {
                this.companyIdList = [];
            }
        },
        /**
         *
         * @param selection
         * @param key
         * @param current
         */
        handleSelectionChange(selection, key, current) {
            this['select' + key + 'Rows'] = selection;
        },
        /**
         * 点击查询按钮
         * * @param cloumn
         */
        handleQuery(key) {
            this[key + 'QueryForm'].current = 1;
            // 调相应的查询方法 并cloumn 首字符大写
            const capitalize = (str) => `${str.charAt(0).toUpperCase()}${str.slice(1)}`;
            this['get' + capitalize(key) + 'List']();
        },
        /**
         * 点击上一步
         */
        prevClick() {
            this.stepAction--;
        },
        /**
         * 点击下一步
         */
        nextClick() {
            if (this.stepAction == 1) {
                if (this.selectIncomingRows.length == 0) {
                    this.msgError('请选择到款记录');
                    return;
                }
                let flag = false;
                this.selectIncomingRows.forEach((item) => {
                    if (!item.theAmountOfThisSubscription) {
                        flag = true;
                        return this.msgError(`请输入打款单位/人为${item.arriveUnit}的本次认款金额！`);
                    }
                    if (item.theAmountOfThisSubscription > item.stayAmount) {
                        flag = true;
                        return this.msgError(`打款单位/人为${item.arriveUnit}的本次认款金额不能大于待认款余额`);
                    }
                });
                if (flag) return;
            }
            if (this.stepAction == 2 && this.selectPaymentRows.length == 0) {
                this.msgError('请选择支付信息');
                return;
            }
            this.stepAction++;
        },
        /**
         * 提交
         */
        submitForm() {
            const claimAmount = this.calculatedAmount(this.selectIncomingRows, 'theAmountOfThisSubscription');
            if (claimAmount != this.calculatedAmount(this.selectPaymentRows, 'remitAmount')) {
                return this.msgError('“合计认款金额”必须等于“合计支付金额”！');
            }
            this.submitLoading = true;
            this.submitText = '提交中...';

            // 去重 this.selectPaymentRows.map((item) => item.ownerName)
            const payCompanys = [...new Set(this.selectPaymentRows.map((item) => item.ownerName))].toString();
            const arriveList = this.selectIncomingRows.map((item) => {
                return { id: item.id, currentClaimAmount: item.theAmountOfThisSubscription };
            });
            const paymentList = this.selectPaymentRows.map((item) => {
                return { id: item.id };
            });
            const params = {
                claimApply: {
                    // 认款申请
                    claimAmount: claimAmount,
                    payCompany: payCompanys || null, // 付款公司
                    remark: this.baseForm.remark || null
                },
                arriveList, // 到款记录
                paymentList // 支付信息
            };
            if (this.applyId) params.applyId = this.applyId;
            if (this.applyInfo?.claimApply?.applyNo) params.applyNo = this.applyInfo.claimApply.applyNo;
            receiptOfPayment
                .submitApply(params)
                .then((res) => {
                    if (res.code == 200) {
                        this.msgSuccess('提交成功');
                        this.dialogVisible = false;
                        this.$emit('update:visible', false);
                        this.$emit('success');
                    }
                })
                .finally(() => {
                    this.submitLoading = false;
                    this.submitText = '提 交';
                });
        },
        /**
         * 关闭弹窗
         */
        close() {
            if (this.type == 'view') {
                this.dialogVisible = false;
                this.$emit('update:visible', false);
                return;
            }
            ElMessageBox.confirm('页面未保存确定取消编辑吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.dialogVisible = false;
                    this.$emit('update:visible', false);
                })
                .catch(() => {});
        }
    }
};
</script>

<style scoped>
.seache-form {
    padding: 20px 0;
}
.search-btn {
    margin-left: 60px;
}
</style>
