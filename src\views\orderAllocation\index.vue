<template>
    <div>
        <!-- 表头 -->
        <el-card class="box-card Botm" style="margin:10px">
            <el-form :model="queryParams" ref="queryRef" :inline="true" class="form_130">
                <TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
                    <el-form-item label="客户" prop="customer">
                        <el-select v-model="queryParams.customer" placeholder="请选择客户" filterable clearable @change=""
                            class="form_225">
                            <el-option v-for="item in clientOptions" :key="item.id" :label="item.enterpriseName"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="经手人" prop="handleBy">
                        <el-select v-model="queryParams.handleBy" placeholder="请选择经手人" filterable clearable @change=""
                            class="form_225">
                            <el-option v-for="item in handlingOptions" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建人" prop="creator">
                        <el-input v-model="queryParams.creator" placeholder="请输入创建人" size="normal" clearable @change=""
                            class="form_225"></el-input>
                    </el-form-item>
                    <el-form-item label="创建日期" prop="createDate">
                        <el-date-picker v-model="queryParams.createDate" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" />
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="" class="form_225">
                            <el-option v-for="item in typeOptions" :key="item.value" :label="item.name" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </TopTitle>
            </el-form>
        </el-card>
        <!-- 表单区域 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(creatForm)" class="creatSpan">新增</el-button>
            <el-table v-loading="loading" :data="configeList" @selection-change="handleSelectionChange" class="el-table"
                border>
                <el-table-column label="ID" align="left" prop="id">
                </el-table-column>
                <el-table-column label="经手人" align="left" prop="handleBy">
                </el-table-column>
                <el-table-column label="客户" align="left" prop="customer">
                </el-table-column>
                <el-table-column label="状态" align="left" prop="status">
                    <template #default="scope">
                        {{ scope.row.status == 'true' ? '正常' : '停用' }}
                    </template>
                </el-table-column>
                <el-table-column label="创建日期" align="left" prop="createDate"
                    :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')">
                </el-table-column>
                <el-table-column label="创建人" align="left" prop="createBy.name">
                </el-table-column>
                <el-table-column label="操作" align="center" width="400">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row.id)"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                        <el-button link type="danger" @click="handleDelete(scope.row)"><img src="@/assets/icons/delete.png"
                                style="margin-right:5px" />删除</el-button>
                        <el-button link type="danger" @click="handlerLog(scope.row)" style="color:#67c23a"><img
                                src="@/assets/icons/review.png" style="margin-right:5px;" />操作日志</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增弹框 -->
        <el-dialog v-model="dialogFormVisible" title="销售订单配置模板新增" style="width:42%;" top="15px"
            :before-close="() => handlerClose()">
            <p>基本信息</p>
            <el-divider border-style="dashed" class="el-divider" />
            <el-form :model="dialogform" label-width="70px" :rules="rules" ref="creatForm">
                <el-row :gutter="20">
                    <el-col :span="18">
                        <el-form-item label="经手人" prop="handleBy">
                            <el-select v-model="dialogform.handleBy" placeholder="请录入经手人账号" multiple filterable clearable
                                @change="" style="width:100%;" :disabled="inputDisabled">
                                <el-option v-for="item in handlingOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-checkbox label="全部" size="large" v-model="creatHandleBy"
                            @change="inputDisabled = !inputDisabled; dialogform.handleBy = ''" style="margin-top:-5px;" />
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="18">
                        <el-form-item label="客户" prop="customer">
                            <el-select v-model="dialogform.customer" placeholder="请录入客户名称，可多选" filterable multiple clearable
                                style="width:100%;" :disabled="inputDisabled2">
                                <el-option v-for="item in clientOptions" :key="item.id" :label="item.enterpriseName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-checkbox label="全部" size="large" v-model="creatCustomer"
                            @change="inputDisabled2 = !inputDisabled2; dialogform.customer = ''" style="margin-top:-5px;" />
                    </el-col>
                </el-row>
                <el-form-item label="状态" prop="n4">
                    <el-radio-group v-model="dialogform.status">
                        <el-radio label="true">正常</el-radio>
                        <el-radio label="false">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
                <p>模板配置</p>
                <el-divider border-style="dashed" class="el-divider" />
                <el-form-item label="三方章选项" label-width="100px" prop="stampOption">
                    <el-select v-model="dialogform.stampOption" placeholder="请选择三方章" style="width:30%">
                        <el-option v-for="(item, index) in tripartiteList" :key="index" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="货主章选项" label-width="100px" prop="cargoOwnerOption">
                    <el-select v-model="dialogform.cargoOwnerOption" placeholder="请选择货主章" style="width:30%">
                        <el-option v-for="(item, index) in ownerList" :key="index" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="装箱选项" label-width="100px" prop="packingOption">
                    <el-select v-model="dialogform.packingOption" placeholder="请选择装箱选项" style="width:30%">
                        <el-option v-for="(item, index) in encasementList" :key="index" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="单据返回选项" prop="receiptReturnOption" label-width="100px">
                    <el-checkbox-group v-model="dialogform.receiptReturnOption">
                        <el-checkbox v-for="(item, index) in receiptList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="留存资料选项" prop="retainedOption" label-width="100px">
                    <el-radio-group v-model="dialogform.retainedOption">
                        <el-radio label="false">非装箱联全部保留
                        </el-radio>
                        <el-radio label="true">不保留</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="是否拼箱" prop="isConsolidation" label-width="100px">
                    <el-radio-group v-model="dialogform.isConsolidation">
                        <el-radio label="false">否</el-radio>
                        <el-radio label="true">是</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="质检报告" prop="inspectionReportOption" label-width="100px">
                    <el-checkbox-group v-model="dialogform.inspectionReportOption">
                        <el-checkbox v-for="(item, index) in qualityList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="收集随货资料" prop="followGenOption" label-width="100px">
                    <el-checkbox-group v-model="dialogform.followGenOption">
                        <el-checkbox v-for="(item, index) in profileList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="质量单据返回" prop="qualityReceiptReturn" label-width="100px">
                    <el-checkbox-group v-model="dialogform.qualityReceiptReturn">
                        <el-checkbox v-for="(item, index) in returnList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="dialogform.remark" :rows="3" maxlength="100" show-word-limit style="width: 40vw"
                        type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="saveAlarm(creatForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 编辑弹框 -->
        <el-dialog v-model="editdialogFormVisible" title="销售订单配置模板编辑" style="width:42%;" top="15px"
            :before-close="() => handlerClose()">
            <p>基本信息</p>
            <el-divider border-style="dashed" class="el-divider" />
            <el-form :model="editdialogform" label-width="70px" :rules="rules" ref="editForm">
                <el-row :gutter="20">
                    <el-col :span="18">
                        <el-form-item label="经手人" prop="handleBy">
                            <el-select v-model="editdialogform.handleBy" placeholder="请录入经手人账号" multiple filterable
                                clearable @change="" style="width:100%;" :disabled="editinputDisabled">
                                <el-option v-for="item in handlingOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-checkbox v-model="edithandleBy" label="全部" size="large"
                            @change="editinputDisabled = !editinputDisabled; editdialogform.handleBy = ''"
                            style="margin-top:-5px;" />
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="18">
                        <el-form-item label="客户" prop="customer">
                            <el-select v-model="editdialogform.customer" placeholder="请录入客户名称，可多选" filterable multiple
                                clearable style="width:100%;" :disabled="editinputDisabled2">
                                <el-option v-for="item in clientOptions" :key="item.id" :label="item.enterpriseName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-checkbox v-model="editCustomer" label="全部" size="large"
                            @change="editinputDisabled2 = !editinputDisabled2; editdialogform.customer = ''"
                            style="margin-top:-5px;" />
                    </el-col>
                </el-row>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="editdialogform.status">
                        <el-radio label="true">正常</el-radio>
                        <el-radio label="false">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
                <p>模板配置</p>
                <el-divider border-style="dashed" class="el-divider" />
                <el-form-item label="三方章选项" label-width="100px" prop="stampOption">
                    <el-select v-model="editdialogform.stampOption" placeholder="请选择三方章" style="width:30%">
                        <el-option v-for="(item, index) in tripartiteList" :key="index" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="货主章选项" label-width="100px" prop="cargoOwnerOption">
                    <el-select v-model="editdialogform.cargoOwnerOption" placeholder="请选择货主章" style="width:30%">
                        <el-option v-for="(item, index) in ownerList" :key="index" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="装箱选项" label-width="100px" prop="packingOption">
                    <el-select v-model="editdialogform.packingOption" placeholder="请选择装箱选项" style="width:30%">
                        <el-option v-for="(item, index) in encasementList" :key="index" :label="item.name"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="单据返回选项" prop="receiptReturnOption" label-width="100px">
                    <el-checkbox-group v-model="editdialogform.receiptReturnOption">
                        <el-checkbox v-for="(item, index) in receiptList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="留存资料选项" prop="retainedOption" label-width="100px">
                    <el-radio-group v-model="editdialogform.retainedOption">
                        <el-radio label="false">非装箱联全部保留
                        </el-radio>
                        <el-radio label="true">不保留</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="是否拼箱" prop="isConsolidation" label-width="100px">
                    <el-radio-group v-model="editdialogform.isConsolidation">
                        <el-radio label="false">否</el-radio>
                        <el-radio label="true">是</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="质检报告" prop="inspectionReportOption" label-width="100px">
                    <el-checkbox-group v-model="editdialogform.inspectionReportOption">
                        <el-checkbox v-for="(item, index) in qualityList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="收集随货资料" prop="followGenOption" label-width="100px">
                    <el-checkbox-group v-model="editdialogform.followGenOption">
                        <el-checkbox v-for="(item, index) in profileList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="质量单据返回" prop="qualityReceiptReturn" label-width="100px">
                    <el-checkbox-group v-model="editdialogform.qualityReceiptReturn">
                        <el-checkbox v-for="(item, index) in returnList" :key="index" :label="item.value">{{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="editdialogform.remark" :rows="3" maxlength="100" show-word-limit style="width: 40vw"
                        type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="editAlarm(editForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
const { proxy } = getCurrentInstance();
import order from '@/api/erp/order'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopTitle from '@/components/topTitle';
import logList from './logList.vue'
import moment from 'moment'
const queryParams = ref({
    current: 1,
    size: 10
})
const total = ref(0)
const dialogform = reactive({
    status: 'true'
})
const edithandleBy = ref(false)
const creatHandleBy = ref(false)
const creatCustomer = ref(false)
const editdialogform = reactive({})
const reviewRow = ref({})
const creatForm = ref()
const dialogFormVisible = ref(false)
const editdialogFormVisible = ref(false)
const inputDisabled = ref(false)
const editinputDisabled = ref(false)
const editinputDisabled2 = ref(false)
const inputDisabled2 = ref(false)
const editCustomer = ref(false)
const reviewVisible = ref(false)
const receiptList = ref([])
const clientOptions = ref([])
const handlingOptions = ref([])
const returnList = ref([])
const typeOptions = ref([])
const qualityList = ref([])
const profileList = ref([])
const tripartiteList = ref([])
const ownerList = ref([])
const editForm = ref()
const queryRef = ref()
const encasementList = ref([])
const ids = ref('')
const institutionName = ref('')
const managerName = ref('')
const clientName = ref('')
const configeList = ref([])
const rules = reactive({
    handleBy: [{ required: true, message: '请录入经手人账号', trigger: 'blur' },],
    customer: [{ required: true, message: '请录入客户名称', trigger: 'blur' },],
})
//新增订单配置模板的按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    inputDisabled.value = false
    inputDisabled2.value = false
    creatHandleBy.value = false
    creatCustomer.value = false
    formEl.resetFields()
}
//编辑按钮
const handleEdit = (id) => {
    ids.value = id
    editdialogFormVisible.value = true
    order.details({ id: id }).then(res => {
        if (res.code == 200) {
            edithandleBy.value = false
            editinputDisabled.value = false
            editCustomer.value = false
            editinputDisabled2.value = false
            if (res.data.handleBy == '') {
                editdialogform.handleBy = ''
                edithandleBy.value = true
                editinputDisabled.value = true
            } else {
                editdialogform.handleBy = res.data.handleBy.split(',')
            }
            if (res.data.customer == '') {
                editdialogform.customer = ''
                editCustomer.value = true
                editinputDisabled2.value = true
            } else {
                editdialogform.customer = res.data.customer.split(',')
            }
            editdialogform.followGenOption = res.data.followGenOption?.split(',')
            editdialogform.inspectionReportOption = res.data.inspectionReportOption?.split(',')
            editdialogform.qualityReceiptReturn = res.data.qualityReceiptReturn?.split(',')
            editdialogform.receiptReturnOption = res.data.receiptReturnOption?.split(',')
            editdialogform.retainedOption = res.data.retainedOption?.toString()
            editdialogform.isConsolidation = res.data.isConsolidation?.toString()
            editdialogform.stampOption = res.data?.stampOption
            editdialogform.cargoOwnerOption = res.data?.cargoOwnerOption
            editdialogform.packingOption = res.data?.packingOption
            editdialogform.status = res.data?.status
            editdialogform.remark = res.data?.remark
        }
    })
}
// 重置
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
//搜索
function handleQuery() {
    getList();
}
//客户
const getClient = () => {
    var params = {
        current: 1,
        size: 1000,
    }
    order.clientType(params).then(res => {
        if (res.code == 200) {
            clientOptions.value = res.data.records
            console.log('1232', clientOptions.value);
        }
    })
}
getClient()
//经手人列表
const gethandling = () => {
    var Organization = JSON.parse(localStorage.getItem("Organization"))
    var orgKey = JSON.parse(localStorage.getItem("orgKey"))
    var num = orgKey.content
    var userList = Organization.content
    var userId = userList[num].id
    institutionName.value = userList[num].name
    var params = {
        'sysOrg.id': userId,
        current: 1,
        size: 10000,
    }
    order.getUser(params).then(res => {
        if (res.code == 200) {
            handlingOptions.value = res.data.records
        }
    })
}
gethandling()
//模板列表
const getList = () => {
    var params = { ...queryParams.value }
    if (params.status == "all") {
        delete params.status
    }
    if (params.createDate) {
        params.beginCreateDate = moment(params.createDate[0]).format('YYYY-MM-DD 00:00:00')
        params.endCreateDate = moment(params.createDate[1]).format('YYYY-MM-DD 23:59:59')
        delete params.createDate
    }
    if(params.creator){
         params = {
            createByName:params.creator,
           ...queryParams.value
        }
        delete params.creator
    }
    order.list(params).then(res => {
        if (res.code == 200) {
            handlingOptions.value.forEach((i) => {
                res.data.records.forEach((v) => {
                    managerName.value = v.createBy.name
                    if (i.id == v.handleBy.split(',')[0]) {
                        if (v.handleBy.split(',').length > 1) {
                            v.handleBy = i.name + '，等'
                        } else {
                            v.handleBy = i.name
                        }
                    }
                    if (v.handleBy == '') {
                        v.handleBy = '全部'
                    }
                })
            })
            clientOptions.value.forEach((i) => {
                res.data.records.forEach((v) => {
                    if (i.id == v.customer.split(',')[0]) {
                        if (v.customer.split(',').length > 1) {
                            v.customer = i.enterpriseName + '，等'
                        } else {
                            v.customer = i.enterpriseName
                        }
                    }
                    if (v.customer == '') {
                        v.customer = '全部'
                    }
                })
            })
            configeList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
//新增模板请求
const saveAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var params = {
                ...dialogform
            }
            params.followGenOption = params.followGenOption?.toString()
            params.customer = params.customer?.toString()
            params.handleBy = params.handleBy?.toString()
            params.inspectionReportOption = params.inspectionReportOption?.toString()
            params.qualityReceiptReturn = params.qualityReceiptReturn?.toString()
            params.receiptReturnOption = params.receiptReturnOption?.toString()
            // delete params.customer
            // delete params.handleBy
            order.searchRepeat(params).then(res => {
                if (res.data.length != 0) {
                    let ids = ''
                    res.data.forEach((v) => {
                        ids = v.id
                    })
                    let clientName = []
                    dialogform.customer.forEach((v) => {
                        clientOptions.value.forEach((i) => {
                            if (v == i.id) {
                                // clientName.value = i.enterpriseName
                                clientName.push(i.enterpriseName)
                            }
                        })
                    })
                    console.log(clientName.toString());
                    ElMessageBox.confirm(`${institutionName.value}_${managerName.value}_${clientName.toString()}销售订单配置模板已存在,ID=${ids}`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        order.save(params).then(res => {
                            if (res.code == 200) {
                                ElMessage({
                                    message: "保存成功",
                                    type: "success",
                                });
                                dialogFormVisible.value = false
                                getList()
                            } else {
                                ElMessage({
                                    type: "error",
                                    message: "添加失败，请稍后重试",
                                });
                            }
                        })
                    }).catch(() => {

                    });
                } else {
                    order.save(params).then(res => {
                        if (res.code == 200) {
                            ElMessage({
                                message: "保存成功",
                                type: "success",
                            });
                            dialogFormVisible.value = false
                            getList()
                        } else {
                            ElMessage({
                                type: "error",
                                message: "添加失败，请稍后重试",
                            });
                        }
                    })
                }
            })
        }
    });
}
//编辑模板请求
const editAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            console.log(111);
            var params = {
                id: ids.value,
                ...editdialogform
            }
            params.followGenOption = params.followGenOption?.toString()
            params.customer = params.customer?.toString()
            params.handleBy = params.handleBy?.toString()
            params.inspectionReportOption = params.inspectionReportOption?.toString()
            params.qualityReceiptReturn = params.qualityReceiptReturn?.toString()
            params.receiptReturnOption = params.receiptReturnOption?.toString()
            console.log(params);
            order.save(params).then(res => {
                if (res.code == 200) {
                    ElMessage({
                        message: "修改成功",
                        type: "success",
                    });
                    editdialogFormVisible.value = false
                    getList()
                } else {
                    ElMessage({
                        type: "error",
                        message: "添加失败，请稍后重试",
                    });
                }
            })
        }
    });
}
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
//删除
function handleDelete(row) {
    proxy.$confirm('是否确认删除此配置模板?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        order.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
// 字典请求
const getDict = async () => {
    receiptList.value = await proxy.getDictList("erp_receipts");
    qualityList.value = await proxy.getDictList("erp_testing");
    profileList.value = await proxy.getDictList("erp_gather");
    tripartiteList.value = await proxy.getDictList("erp_stamp");
    ownerList.value = await proxy.getDictList("erp_owner");
    encasementList.value = await proxy.getDictList("erp_encasement");
    typeOptions.value = await proxy.getDictList("order_type");
    returnList.value = await proxy.getDictList("erp_return");
}
getDict()
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

.creatSpan {
    margin-bottom: 10px;
}

.el-divider {
    height: 20px;
    margin-top: 10px;
    margin-bottom: 5px;
}

::v-deep .el-divider--horizontal {
    // border-top: #28282b;
    border-top: 1px dashed #686666;
}
</style>