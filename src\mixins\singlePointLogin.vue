<script>
import objectUtils from "@/utils/objectUtils";
import homeApi from '@/api/sys/home'
import tool from '@/utils/tool';
import systemApi from '@/api/model/system';
export default {
    mounted() {
        const params = this.parseUrlParams(location.href)
        if (params.code) {
            this.doosLogin(params)
        }
    },
    methods: {
        parseUrlParams(url) {
            var params = {};

            // 判断 URL 中是否存在参数
            if (url.indexOf('?') !== -1) {
                // 获取参数部分
                var paramString = url.split('?')[1];

                // 分割参数字符串，得到参数数组
                var paramArray = paramString.split('&');

                // 遍历参数数组，解析每个参数并存入 params 对象中
                for (var i = 0; i < paramArray.length; i++) {
                    var param = paramArray[i].split('=');
                    var paramName = decodeURIComponent(param[0]);
                    var paramValue = decodeURIComponent(param[1]);

                    // 判断参数名是否已存在，如果已存在则将当前参数值添加到已存在的参数值的数组中
                    if (params[paramName]) {
                        params[paramName] = [].concat(params[paramName], paramValue);
                    } else {
                        params[paramName] = paramValue;
                    }
                }
            }

            return params;
        },
        getPermissions(sysMenu) {
            var permissions = [];
            for (var i = 0; i < sysMenu.length; i++) {
                if (objectUtils.isNotEmpty(sysMenu[i].permission)) {
                    permissions.push(sysMenu[i].permission);
                }
                if (objectUtils.isEmpty(sysMenu[i].children) == false) {
                    var pe = getPermissions(sysMenu[i].children);
                    for (var j = 0; j < pe.length; j++) {
                        if (objectUtils.isNotEmpty(pe[j])) {
                            permissions.push(pe[j]);
                        }
                    }
                }
            }
            return permissions;
        },
        sysMenuToUiTree(sysMenu) {
            var menuTree = [];
            for (var i = 0; i < sysMenu.length; i++) {
                menuTree[i] = {
                    name: sysMenu[i].alias,
                    path: sysMenu[i].path,
                    meta: {
                        title: sysMenu[i].name,
                        icon: sysMenu[i].logo,
                        type: sysMenu[i].type,
                        hidden: sysMenu[i].hide,
                        color: sysMenu[i].affix,
                        fullpage: sysMenu[i].wholePageRoute,
                    },
                    component: sysMenu[i].view,
                };
                if (sysMenu[i].children?.length) {
                    menuTree[i].children = sysMenuToUiTree(sysMenu[i].children);
                }
            }
            return Promise.resolve(menuTree);
        },
        doosLogin(params) {
            homeApi.codeLogin({ code: params.code }).then(res => {
                if (res.code == 200) {
                    tool.cookie.set("TOKEN", res.data.token);
                    tool.data.set("USER_INFO", res.data.userInfo);
                    tool.data.set("ROLE_LIST", res.data.roleList);
                    tool.data.set("Organization", res.data.orgList);
                    if (res.data.orgList?.length) {
                        systemApi.menuTreeByOrg({ orgId: res.data.orgList[0]?.id }).then(response => {
                            var menuTree =  this.sysMenuToUiTree(response.data)
                            tool.data.set("MENU", menuTree);
                            var permissions = this.getPermissions(response.data)
                            tool.data.set("PERMISSIONS", permissions);
                            tool.data.set("orgKey", 0);
                            this.$router.replace({
                                    path: "/home",
                                });
                        })


                    }
                } else {
                    this.$router.replace('/login')
                    return false;
                }
            }).catch(() => {
                this.$router.replace('/login')
                return false;
            })
            return Promise.resolve()
        }
    }

}

</script>

<style lang="scss" scoped></style>