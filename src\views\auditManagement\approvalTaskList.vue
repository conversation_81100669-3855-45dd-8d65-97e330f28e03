<template>
    <div v-loading="fullLoading" class="app-container" element-loading-text="请求中...">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="发起时间" prop="queryTime">
                    <el-date-picker v-model="queryForm.queryTime" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" style="width: 260px" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <!-- 新增 状态  status-状态  auditStatus-审批状态 -->
                <el-form-item label="任务状态" prop="status" style="width: 230px">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择任务状态" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in statusOptions" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审批状态" prop="auditStatus" style="width: 230px">
                    <el-select v-model="queryForm.auditStatus" clearable placeholder="请选择审批状态" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in auditStatusOptions" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="当前节点" prop="nodeId" v-show="isShowAll">
                    <div class="flex">
                        <el-select v-model="queryForm.orgFlowId" clearable filterable placeholder="请选择审批流程" @change="getNodeData">
                            <el-option v-for="item in flowOptions" :key="item.id" :label="item?.flow?.name" :value="item.id"></el-option>
                        </el-select>
                        <el-select v-model="queryForm.nodeId" clearable placeholder="请选择节点"  @change="handleQuery">
                            <el-option v-for="item in nodeOptions" :key="item.id" :label="item.nodeName" :value="item.id"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 40px">
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" style="margin-left: auto" table-i-d="approvalTaskList" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="approvalTaskList" ref="approvalTaskList" v-loading="loading" :border="true" :columns="columns" :data="dataList" element-loading-text="加载中..." show-index>
                <template #flow="{ row }">
                    <span>{{ row.flow.name || '--' }}</span>
                </template>
                <template #businessType="{ row }">
                    <span>{{ selectDictLabel(auditFormOptions, row.businessType) }}</span>
                </template>
                <template #currentNode="{ row }">
                    <span>{{ row.currentNode.nodeName || '--' }}</span>
                </template>
                <template #createBy="{ row }">
                    <span>{{ row.createBy.name || '--' }}</span>
                </template>
                <template #beforeNode="{ row }">
                    <span>{{ row.beforeNode ? row.beforeNode.nodeName : '--' }}</span>
                </template>
                <template #nextNode="{ row }">
                    <span>{{ row.nextNode ? row.nextNode.nodeName : '--' }}</span>
                </template>
                <template #auditStatus="{ row }">
                    <span :style="displayColor(row.auditStatus)">{{ selectDictLabel(auditStatusOptions, row.auditStatus) }}</span>
                </template>
                <template #status="{ row }">
                    <span :style="row.status == '2' ? 'color:#039D12' : row.status == '3' ? 'color:#E6A23C' : ''">{{ selectDictLabel(statusOptions, row.status) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if=" row.status == '2' || row.auditStatus == '2'" icon="el-icon-info-filled" link size="small" type="primary" @click="auditOperation(row,'details')">详情</el-button>
                    <el-button v-else icon="el-icon-check" link size="small" type="warning" @click="auditOperation(row,'examine')">审核</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" class="mb0" @pagination="getList" />
        </el-card>
        <!--付款充值申请-->
        <pre-deposit-top-up-audit v-if="reconciliationShow && businessType == '1'" v-model:pre-deposit-top-up-audit-visible="reconciliationShow" :audit-info="auditInfo" :audit-title="auditTitle" :task-info="taskInfo" :type="auditType" @success="successfulAudit"></pre-deposit-top-up-audit>
        <!--付款单支付申请-->
        <payment-order-audit
            v-if="reconciliationShow && (businessType == '2' || businessType == '4')"
            v-model:payment-order-audit-visible="reconciliationShow"
            :audit-info="auditInfo"
            :audit-title="auditTitle"
            :task-info="taskInfo"
            :type="auditType"
            @success="successfulAudit"
        ></payment-order-audit>
        <!-- 认款申请-->
        <receipt-application-audit v-if="reconciliationShow && businessType == '3'" v-model:receipt-application-audit-visible="reconciliationShow" :audit-info="auditInfo" :audit-title="auditTitle" :type="auditType"  @success="successfulAudit"></receipt-application-audit>
        <!--红冲申请-->
        <red-punch-approval v-if="reconciliationShow && (businessType == '5' || businessType == '6')" v-model:red-punch-approval-visible="reconciliationShow" :audit-info="auditInfo" :audit-title="auditTitle" :task-info="taskInfo" :type="auditType"  @success="successfulAudit"></red-punch-approval>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import { tasks } from '@/api/auditManagement/approvalTask';
import PreDepositTopUpAudit from '@/views/auditManagement/PreDepositTopUpAudit';
import PaymentOrderAudit from '@/views/auditManagement/PaymentOrderAudit';
import ReceiptApplicationAudit from '@/views/auditManagement/ReceiptApplicationAudit';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RedPunchApproval from '@/views/auditManagement/RedPunchApproval.vue';
import { flows, node } from '@/api/auditManagement/auditAndFlow.js';
import {ElMessage} from 'element-plus'

export default {
    name: 'ApprovalTaskList',
    components: {
        RedPunchApproval,
        ReceiptApplicationAudit,
        PaymentOrderAudit,
        PreDepositTopUpAudit,
        ColumnTable,
        RightToolbar,
        SearchButton
    },
    data() {
        return {
            fullLoading: false,
            // 搜索部分
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                businessType: null,
                orgFlowId:undefined,
                nodeId:undefined,
                queryTime: []
            },
            auditFormOptions: [], // 流程表单
            auditStatusOptions: [], // 审批状态
            statusOptions: [], // 状态
            // 表格部分
            loading: false,
            dataList: [],
            total: 0,
            columns: [
                { title: '流程标题', key: 'topic', columnShow: true, minWidth: '220px', showOverflowTooltip: true },
                { title: '所属主流程', key: 'flow', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '业务类型', key: 'businessType', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '发起人', key: 'createBy', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '发起时间', key: 'createDate', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '上一个节点', key: 'beforeNode', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '当前节点', key: 'currentNode', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '下一个节点', key: 'nextNode', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '审批状态', key: 'auditStatus', minWidth: '80px', columnShow: true },
                { title: '任务状态', key: 'status', minWidth: '80px', columnShow: true },
                { title: '操作', key: 'opt', width: '90px', columnShow: true, align: 'center', fixed: 'right' }
            ],
            // 审核弹窗
            reconciliationShow: false,
            auditTitle: '', // 审核弹窗标题
            taskInfo: {}, // 任务详情
            auditInfo: {}, // 审核信息
            auditType:null,
            businessType: null, // 任务类型
            flowOptions:[], // 流程列表
            nodeOptions:[], // 节点列表
            isShowAll: false,
        };
    },
    computed: {
        /**
         * 审核状态文字颜色
         * @return string
         */
        displayColor() {
            return (status) => {
                switch (status) {
                    case '1':
                        return 'color: #039D12';
                    case '2':
                        return 'color: #D9001B';
                    default:
                        return 'color: #999999';
                }
            };
        }
    },
    async created() {
        /*流程表单*/
        this.auditFormOptions = await this.getDictList('audit_form');
        /*审批任务状态*/
        this.auditStatusOptions = await this.getDictList('audit_status');
        /*审批状态*/
        this.statusOptions = await this.getDictList('audit_task_status');
        this.handleQuery();
        this.getFlowData();
    },
    methods: {
        /**
         * 获取流程列表数据
         */
        getFlowData() {
            this.nodeOptions = [];
            this.flowOptions = [];
            this.queryForm.orgFlowId = undefined;
            this.queryForm.nodeId = undefined;
            flows.getOrgFlowList({ current: 1, size:999999 }).then((res) => {
                    if (res.code === 200) {
                        this.flowOptions = res.data.records;
                    }
                });
        },
        /**
         * 获取节点列表数据
         * @param e
         */
        getNodeData(e){
            if(!e){
                return;
            }
            this.nodeOptions = [];
            this.queryForm.nodeId = undefined;
            node.nodeList({orgFlowId:e}).then((res) => {
                if (res.code === 200) {
                    this.nodeOptions = res.data;
                }
            });
        },
        /**
         * 审核弹窗
         */
        auditOperation(row,type) {
            this.auditType = type;
            if (row.businessType == '3') {
                this.auditInfo = row;
                this.fullLoading = false;
                this.reconciliationShow = true;
                this.auditTitle = this.selectDictLabel(this.auditFormOptions, row.businessType) + '审批';
                if (this.auditType === 'details') {
                    this.auditTitle += '详情';
                }
                this.businessType = row.businessType;
                this.taskInfo = {};
                return;
            }
            const { id } = row;
            this.fullLoading = true;
            tasks
                .queryById({ id })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.taskInfo = res?.data;
                    }
                    this.auditInfo = row;
                    this.fullLoading = false;
                    this.reconciliationShow = true;
                        if(row.businessType === '5' || row.businessType === '6') {
                            this.auditTitle = '红字发票申请审批';
                        }else{
                            this.auditTitle = this.selectDictLabel(this.auditFormOptions, row.businessType) + '审批';
                        }

                    if (this.auditType === 'details') {
                        this.auditTitle += '详情';
                    }
                    this.businessType = row.businessType;
                })
                .finally(() => {
                    this.fullLoading = false;
                });
        },
        /**
         * 数据查询接口
         * */
        getList() {
            this.loading = true;
            this.dataList = [];
            const { queryTime, nodeId,orgFlowId,...params } = this.queryForm;
            tasks
                .listTasks(params)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {
                    this.dataList = [];
                    this.total = 0;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 搜索
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { queryTime,nodeId,orgFlowId } = this.queryForm;
            this.queryForm.beginCreateDate = null;
            this.queryForm.endCreateDate = null;
            if (queryTime && queryTime.length) {
                this.queryForm.beginCreateDate = queryTime[0] + ' 00:00:00';
                this.queryForm.endCreateDate = queryTime[1] + ' 23:59:59';
            }
            if(orgFlowId && !nodeId){
                ElMessage({
                    message: '只选择审批流程无法查询当前节点',
                    type: 'warning',
                })
            }
            if(nodeId){
              this.queryForm['currentNode.id'] = nodeId || undefined;
            }else{
                this.queryForm['currentNode.id'] = undefined
            }
            this.getList();
        },
        /**
         * 搜索重置
         * */
        resetQuery(formName) {
            console.log(formName);
            this.$refs[formName].resetFields();
            this.queryForm.orgFlowId = undefined
            this.queryForm['currentNode.id'] = undefined
            this.handleQuery();
        },
        /**
         * 审核成功回调
         */
        successfulAudit() {
            this.reconciliationShow = false;
            this.getList();
        },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
            this.handleQuery();
        }
    }
};
</script>
<style lang="scss" type="text/css">
.drawer-custom {
    .el-drawer__body {
        background-color: #f5f7fd;
    }
}
</style>
