<template>
    <div class="app-container">
        <!--搜索-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item class="form-mb0" label="区域名称" prop="areaName">
                    <el-input v-model="queryParams.areaName" clearable placeholder="请输入区域名称" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="所属网点" prop="branchCode">
                    <el-select v-model="queryParams.branchCode" clearable filterable placeholder="请选择所属网点" style="width: 100%" @change="handleQuery">
                        <el-option v-for="(item, idx) in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="集货类型" prop="areaType">
                    <el-select v-model="queryParams.areaType" clearable filterable placeholder="请选择集货类型" style="width: 100%" @change="handleQuery">
                        <el-option v-for="(item, idx) in collectionType" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <div>
                    <el-button icon="el-icon-plus" type="primary" @click="handleAdd">新增</el-button>
                    <el-button :disabled="multiple" icon="el-icon-delete" type="danger" @click="handleDelete">批量删除</el-button>
                    <el-button :disabled="single" icon="el-icon-download" type="warning" @click="printLabel">打印标签</el-button>
                </div>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="CollectionArea" @queryTable="getList"></right-toolbar>
            </div>

            <column-table key="CollectionArea" :max-height="600" v-loading="loading" v-model:columns="columns" :data="areaList" :show-check-box="true" element-loading-text="加载中..." @selection-change="handleSelectionChange">
                <template #areaType="scope">
                    <span>{{ collectionTypeFilters(scope.row) }}</span>
                </template>
                <template #status="scope">
                    <el-switch v-model="scope.row.status" active-color="#13ce66" size="small" active-text="正常" active-value="0" inactive-text="禁用" inactive-value="1" @change="changeStatus($event, scope.row)"> </el-switch>
                </template>
                <template #branchName="{ row }">
                    <span>{{ row?.branch?.branchName }}</span>
                </template>
                <template #opt="scope">
                    <el-button icon="el-icon-printer" link size="small" type="primary" @click="printBoxTag(scope.row)">打印标签</el-button>
                    <el-button icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- 添加或修改集货区域对话框 -->
        <el-dialog v-model="open" v-dialogDrag :title="title" append-to-body width="650px">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="所属网点" prop="branchCode">
                    <el-select v-model="form.branchCode" clearable filterable placeholder="请选择网点名称" size="small" style="width: 100%" @change="chooseBranch($event)">
                        <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="集货区域名称" prop="areaName">
                    <el-input v-model="form.areaName" maxlength="20" placeholder="请输入集货区域名称" show-word-limit />
                </el-form-item>
                <el-form-item label="集货类型" prop="areaType">
                    <el-radio-group v-model="form.areaType">
                        <el-radio v-for="(item, index) in collectionType" :key="index" :label="item.value">{{ item.name }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import serviceNetwork from '@/api/logisticsConfiguration/serviceNetwork.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'CollectionArea',
    components: { SearchButton, ColumnTable, RightToolbar },
    data() {
        return {
            props: {
                multiple: true
            },
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 集货区域表格数据
            areaList: [],
            columns: [
                { title: '集货区域名称', key: 'areaName', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '区域编码', key: 'areaCode', align: 'center', minWidth: '250px', columnShow: true },
                { title: '集货类型', key: 'areaType', align: 'center', minWidth: '250px', columnShow: true },
                { title: '所属网点', key: 'branchName', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '集货区状态', key: 'status', align: 'center', minWidth: '150px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '220px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            areaWide: [],
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                areaCode: null,
                areaName: null,
                areaType: null,
                areaWide: null,
                status: null,
                branchCode: null
            },
            // 表单参数
            form: {
                areaType: '0'
            },
            branchList: [],
            sysAreas: [],
            wideList: [],
            // 表单校验
            rules: {
                areaName: [{ required: true, message: '请输入集货区域名称', trigger: 'blur' }],
                areaType: [{ required: true, message: '请选择集货类型', trigger: 'change' }],
                branchCode: [{ required: true, message: '请选择所属网点', trigger: 'blur' }]
            },
            collectionType: []
        };
    },
    computed: {
        collectionTypeFilters() {
            return (value) => {
                return this.selectDictLabel(this.collectionType, value.areaType);
            };
        }
    },

    created() {
        this.getList();

        this.getBranchAll();
        this.getCollectionType();
    },
    methods: {
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        changeStatus(status, row) {
            if (row?.id) {
                serviceNetwork.queryAreaById({ id: row.id }).then((response) => {
                    if (response.code == 200) {
                        let form = response.data;
                        form.branchCode = form.branch.branchCode;
                        form.branchName = form.branch.branchName;
                        form.id = row.id;
                        form.status = status;
                        serviceNetwork.saveArea(form).then((response) => {
                            this.msgSuccess('修改成功');
                            this.getList();
                        });
                    }
                });
            }
        },

        //新增时选择网点赋值
        chooseBranch(obj) {
            this.form.branchCode = obj;
            let index = this.branchList.findIndex((item) => item.branchCode == obj);
            this.form.branchName = this.branchList[index].branchName;
        },

        //获取承运商所有网点
        getBranchAll() {
            serviceNetwork.getBranchList().then((response) => {
                this.branchList = response.data;
            });
        },
        /**
         * @description: 集货类型字典
         * @return {*}  Object
         */
        async getCollectionType() {
            this.collectionType = await this.getDictList('fourpl_collect_area_type');
        },
        /** 查询集货区域列表 */
        getList() {
            this.loading = true;
            this.areaList = [];
            let params = { ...this.queryParams };
            params.areaClass = '3';
            serviceNetwork.listArea(params).then((response) => {
                if (response.code === 200 && response.data) {
                    this.areaList = response.data.records || [];
                    this.total = response.data.total || 0;
                }
                this.loading = false;
            });
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.sysAreas = this.getSysAreasThird;
            this.areaWide = [];
            this.reset();
            this.open = true;
            this.title = '添加集货区域';
        },

        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids.join(',');
            const names = row.areaName || this.names;
            this.$confirm('是否确认删除集货区域"' + names + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return serviceNetwork.delArea({ ids: ids });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.names = selection.map((item) => item.areaName);
            this.areacode = selection.map((item) => item.areaCode);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.sysAreas = this.getSysAreasThird;
            this.reset();
            const id = row.id || this.ids;
            serviceNetwork.queryAreaById({ id }).then((response) => {
                this.form = response.data;
                this.form.branchCode = this.form.branch.branchCode;
                this.form.branchName = this.form.branch.branchName;
                this.form.id = id;
                this.open = true;
                this.title = '修改集货区域';
            });
        },
        /**
         * 打印标签
         * @param row
         */
        printBoxTag(row) {
            this.pdfLabelView('3e03eabe6c4f4b4cb79ce1dfa847f715', row.areaCode);
        },
        // 打印标签
        printLabel() {
            this.pdfLabelView('3e03eabe6c4f4b4cb79ce1dfa847f715', this.areacode);
        },
        // 表单重置
        reset() {
            this.form = {
                areaName: null,
                status: 0,
                branchCode: null,
                branchName: null,
                areaType: '0'
            };
            this.resetForm('form');
        },
        resetForm(formName) {
            this.$refs[formName] ? this.$refs[formName].resetFields() : '';
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        serviceNetwork.saveArea(this.form).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        serviceNetwork.saveArea({ ...this.form, areaClass: '3' }).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
</style>
