<template>
    <div v-loading="fullLoading" class="app-container customer-auto-height-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never" style="overflow: initial">
            <el-form ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form">
                <el-form-item v-show="isShowAll" label="订单号" prop="orderNo" style="width: 280px">
                    <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="运单号" prop="transOrderNo" style="width: 195px">
                    <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单号" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="货主公司" prop="companyId" style="width: 280px">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">
                        <el-option v-for="dict in ownerList" :key="dict.companyId" :label="dict.companyName" :value="dict.companyId" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单状态" prop="transStatus">
                    <el-select v-model="queryParams.transStatus" clearable filterable multiple placeholder="请选择运单状态" @change="handleQuery">
                        <el-option v-for="item in fourplWaybillStatusOptions" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单状态" prop="status">
                    <el-select v-model="queryParams.status" clearable filterable placeholder="请选择订单状态" @change="handleQuery">
                        <el-option v-for="item in statusDicts" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件公司" prop="sendCompany" style="width: 280px">
                    <el-input v-model="queryParams.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件地址" prop="sendAddress">
                    <el-cascader v-model="queryParams.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择发件地址" @change="handleQuery" @visible-change="visibleChange" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件公司" prop="receiverCompany" style="width: 280px">
                    <el-input v-model="queryParams.receiverCompany" clearable placeholder="请输入收件公司" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件地址" prop="receiverAddress">
                    <el-cascader v-model="queryParams.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择收件地址" @change="handleQuery" @visible-change="visibleChange" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="产品分类" prop="productClass">
                    <el-select v-model="queryParams.productClass" clearable filterable placeholder="请选择产品分类" @change="handleQuery">
                        <el-option v-for="dict in fourplProductClassOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运输类型" prop="productType">
                    <el-select v-model="queryParams.productType" clearable filterable placeholder="请选择运输类型" @change="handleQuery">
                        <el-option v-for="dict in fourplProductTypeOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="温层类型" prop="temperatureTypeTemp" style="width: 280px">
                    <el-select v-model="queryParams.temperatureTypeTemp" clearable collapse-tags collapse-tags-tooltip filterable multiple placeholder="请选择温层类型" @change="handleQuery">
                        <el-option v-for="(dict, index) in temperatureTypeDicts" :key="index" :label="dict.describtion" :value="dict.id" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="付款方式" prop="paymentMethod">
                    <el-select v-model="queryParams.paymentMethod" clearable filterable placeholder="请选择付款方式" @change="handleQuery">
                        <el-option v-for="(dict, index) in fourplPaymentMethodOptions" :key="index" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="揽收方式" prop="orderType">
                    <el-select v-model="queryParams.orderType" clearable filterable placeholder="请选择揽收方式" @change="handleQuery">
                        <el-option v-for="(dict, index) in collectionMethod" :key="index" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="件数" prop="goodsPackages">
                    <div>
                        <el-select v-model="queryParams.prePackageOption" clearable filterable style="width: 45%">
                            <el-option v-for="(item, index) in prePackageOptions" :key="index" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                        <el-input v-model="queryParams.goodsPackages" clearable placeholder="请输入件数" style="width: 55%" @keyup.enter="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item class="date-screening" label="下单时间" prop="queryTime" style="width: 320px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div style="margin-bottom: 16px">
                <el-button icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="trajectoryTracking1" @queryTable="getList" />
            </div>
            <column-table key="trajectoryTracking1" v-loading="loading" :columns="columns" :data="traceRecordList" :defaultSort="{ prop: 'createDate', order: 'descending' }" class="customer-auto-height-table" max-height="null" @row-dblclick="(row) => toTransDetail(row)">
                <template #orderType="{ row }">
                    <span>{{ orderTypeFormatting(row) }}</span>
                </template>
                <template #transStatus="{ row }">
                    <span style="color: red">{{ transStatusFormatting(row) }}</span>
                </template>

                <template #sendComplateAddress="{ row }">
                    <span>{{ row.sendComplateAddress }}</span>
                </template>

                <template #receiverComplateAddress="{ row }">
                    <span>{{ row.receiverComplateAddress }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ fourplProductTypeFormat(row) }}</span>
                </template>
                <template #productClass="{ row }">
                    <span>{{ itemTypeFormatting(row) }}</span>
                </template>
                <template #temperatureTypeDesc="{ row }">
                    <span>{{ row.temperatureTypeDesc || '--' }}</span>
                </template>
                <template #paymentMethod="{ row }">
                    <span>{{ paymentMethodFormatting(row) }}</span>
                </template>
                <template #printDeliveryFlag="{ row }">
                    <span :style="row.printDeliveryFlag == '1' ? 'color: #1acd7e' : 'color: #ff2a2a'">{{ row.printDeliveryFlag == '1' ? '是' : '否' }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="row.tempType === '3' || row.tempType === '4'" icon="el-icon-printer" link size="small" type="warning" @click="printBoxCode(row)">打印保温箱码</el-button>
                    <el-button v-if="row.status == '3' || row.status == '4' || row.status == '6'" icon="el-icon-printer" link size="small" type="warning" @click="cancelOrder(row)">打印箱签 </el-button>
                    <el-button v-hasPermi="['trans:trajectoryTracking:info']" icon="el-icon-info-filled" link size="small" type="primary" @click="toTransDetail(row)">运输记录</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
        <!--    运单详情-->
        <transport-record v-if="transInfoOpen" :info-open="transInfoOpen" :order-info="transOrderInfo" title="运输记录" @closeSlider="transInfoOpen = false"></transport-record>
        <!--    打印箱签滑块-->
        <el-drawer v-model="printBox" direction="rtl" size="50%" title="打印箱签">
            <print-box-tag v-if="printBox" :orderInfo="orderInfo" @callbackMethod="closePrintBoxLabel"></print-box-tag>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import PrintBoxTag from '@/views/orderComponents/PrintBoxTag.vue';
import TransportRecord from '@/views/logisticsManagement/waybillTracking/TransportRecord.vue';
import waybillTracking from '@/api/logisticsManagement/waybillTracking.js'; // 运单跟踪
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import printLabelView from '@/api/print/printLabelView.js';
import { setDatePickerShortcuts } from '@/utils/config-store.js';
import moment from 'moment';

export default {
    name: 'TrackingListOfWaybills',
    components: {
        ColumnTable,
        SearchButton,
        RightToolbar,
        PrintBoxTag,
        TransportRecord
    },
    data() {
        return {
            // customerSignVisible: false,
            showSearch: true, // 控制搜索显示隐藏
            total: 0, // 总条数
            printBox: false,
            orderInfo: {
                addedServices: [],
                fileList: [],
                orderDetailList: []
            },
            queryParams: {
                // 搜索参数
                queryTime: [],
                orderNo: null, // 订单号
                transOrderNo: null, // 运单号
                transStatus: null, // 运单状态
                transWay: null, // 运输方式
                beginCreateDate: null, // 创建开始时间
                endCreateDate: null, // 创建结束时间
                companyId: null, // 货主
                sendCompany: null, // 发货公司
                sendAddress: [], // 发件地址
                receiverCompany: null, // 收件公司
                receiverAddress: [], // 收件地址
                productClass: null, // 产品分类
                productType: null, // 运输类型
                temperatureTypeTemp: null, // 温层类型
                paymentMethod: null, // 付款方式
                orderType: null, // 揽收方式
                goodsPackages: null,
                prePackageOption: null, // 件数
                source: null, // 来源
                // 件数
                size: 10,
                current: 1
            },
            columns: [
                // 【全部】显示配置
                { title: '订单号', key: 'orderNo', align: 'center', width: '120px', fixed: 'left', columnShow: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '120px', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收方式', key: 'orderType', align: 'center', width: '120px', columnShow: true },
                { title: '运单状态', key: 'transStatus', align: 'center', width: '150px', columnShow: true },
                { title: '是否打印', key: 'printDeliveryFlag', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件人', key: 'sendUser', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '发件人电话', key: 'sendUserPhone', align: 'center', width: '180px', columnShow: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendComplateAddress', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件人', key: 'receiverUser', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件人电话', key: 'receiverUserPhone', align: 'center', width: '180px', columnShow: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverComplateAddress', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '90px', columnShow: true },
                { title: '公里数', key: 'kilometre', align: 'center', columnShow: true },
                { title: '运输类型', key: 'productType', align: 'center', width: '120px', columnShow: true },
                { title: '产品分类', key: 'productClass', align: 'center', width: '120px', columnShow: true },
                { title: '温层类型', key: 'temperatureTypeDesc', align: 'center', width: '150px', columnShow: true },
                { title: '付款方式', key: 'paymentMethod', align: 'center', width: '120px', columnShow: true },
                { title: '下单人', key: 'createOrderUser', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '下单时间', key: 'orderTime', align: 'center', width: '180px', columnShow: true, sortable: true },
                { title: '操作', key: 'opt', align: 'center', minWidth: '320px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            traceRecordList: [], // 数据
            loading: false, // 加载中
            trajectoryTrackingDetailOpen: false, // 详情显示与隐藏
            mainInfo: null, // 主表ID
            fourplMainRecordTransWayOptions: [], // 运单状态主记录运输方式字典值
            ownerList: [], // 货主列表
            fourplProductClassOptions: [], // 货主列表
            fourplProductTypeOptions: [], // 产品分类
            temperatureTypeDicts: [], // 温层类型
            fourplPaymentMethodOptions: [], // 付款方式
            collectionMethod: [], // 揽收方式
            prePackageOptions: [],
            sysAreas: [], // 地址
            transOrderInfo: {},
            transInfoOpen: false,
            fullLoading: false, //全屏加载
            // fourplTaskIsTimeoutOptions: [], // 任务是否超时
            fourplWaybillStatusOptions: [], // 运单状态
            statusDicts: [], // 订单状态
            shortcuts: setDatePickerShortcuts(),
            fourplTransSignWayOptions: [],
            isShowAll: false
        };
    },
    computed: {
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },
    async created() {
        /** 运单状态主记录运输方式 */
        /** 产品分类 */
        this.fourplProductClassOptions = await this.getDictList('fourpl_product_class');
        // 运输类型4PL
        this.fourplProductTypeOptions = await this.getDictList('fourpl_product_type');
        /** 付款方式 */
        let fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
        this.fourplPaymentMethodOptions = fourplPaymentMethodOptions.filter((item) => item.value != '4' && item.value != '5' && item.value != '6');
        /** 揽收方式 */
        this.collectionMethod = await this.getDictList('fourpl_mail_service');
        /** 付款状态订单件数搜索条件 */
        this.prePackageOptions = await this.getDictList('fourpl_order_equals');
        /** 4PL运单状态 */
        this.fourplWaybillStatusOptions = await this.getDictList('fourpl_waybill_status');
        // 订单状态字典
        this.statusDicts = await this.getDictList('fourpl_order_status');
        /** 签收方式 */
        this.fourplTransSignWayOptions = await this.getDictList('fourpl_trans_sign_way');
        this.getCompanySelectList(); // 货主公司
        this.getTemperatureType(); // 温层类型
        // 默认设置当天
        let now = moment(new Date()).format('YYYY-MM-DD');
        this.queryParams.queryTime = [now, now];
        this.handleQuery();
    },
    methods: {
        /*打印箱码*/
        printBoxCode(row) {
            this.loading = true;
            let params = {
                templateId: '0b686154274a4a029ec5a2dfddd570b9',
                data: {}
            };
            // let that = this;
            waybillTracking
                .getIncubatorQrCode(row.orderNo)
                .then((res) => {
                    if (res.code == 200 && res.data) {
                        params.data = res.data;
                        new Promise((resolve, reject) => {
                            printLabelView.printPdfWithData(params).then((res) => {
                                const binaryData = [];
                                binaryData.push(res);
                                //获取blob链接
                                let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
                                window.open(pdfUrl);
                            });
                            resolve();
                        })
                            .then((data) => {
                                this.msgSuccess('打印成功');
                                this.loading = false;
                            })
                            .catch((err) => {
                                this.msgError('打印失败');
                                this.loading = false;
                            });
                    } else {
                        this.msgError('未查询到装箱信息，请确认订单是否装箱');
                        this.loading = false;
                    }
                })
                .catch((err) => {
                    this.loading = false;
                });
        },

        //打印箱签显示窗口
        cancelOrder(row) {
            this.orderInfo = { ...row, orderno: row.orderNo, transorderno: row.transOrderNo, paymentmethod: row.paymentMethod };
            this.printBox = true;
        },
        //关闭打印箱签
        closePrintBoxLabel() {
            this.printBox = false;
            this.getList();
        },
        /** 运输类型4PL字典转换 */
        fourplProductTypeFormat(val) {
            return this.selectDictLabel(this.fourplProductTypeOptions, val.productType);
        },
        /** 任务是否超时4PL字典转换 */
        fourplTaskIsTimeoutFormat(val) {
            return this.selectDictLabel(this.fourplTaskIsTimeoutOptions, val.isTimeout);
        },

        /** 签收方式字典转换 */
        fourplTransSignWayFormat(val) {
            return this.selectDictLabel(this.fourplTransSignWayOptions, val.source);
        },
        // 获取货主列表
        getCompanySelectList() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
        /** 查询运单信息列表 */
        getList() {
            this.traceRecordList = [];
            this.loading = true;
            let params = { ...this.queryParams };
            params.transStatus = params.transStatus.toString();
            params.tempId = params.temperatureTypeTemp.toString();
            delete params.queryTime;
            delete params.sendAddress;
            delete params.receiverAddress;
            delete params.temperatureTypeTemp;
            waybillTracking.transOperatRecordList(params).then((response) => {
                if (response.code === 200 && response.data && response.data.records) {
                    this.traceRecordList = response.data.records;
                    this.loading = false;
                }
                this.total = response.data.total || 0;
                this.loading = false;
            });
        },
        // 获取温层类型
        getTemperatureType() {
            otherConfiguration.getTemperatureTypeList({ status: '0' }).then((res) => {
                if (res.code === 200 && res?.data?.records.length > 0) {
                    this.temperatureTypeDicts = res.data.records;
                }
            });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.fullLoading = true;
            let params = { ...this.queryParams };
            params.transStatus = params.transStatus.toString();
            params.tempId = params.temperatureTypeTemp.toString();
            delete params.queryTime;
            delete params.sendAddress;
            delete params.receiverAddress;
            delete params.temperatureTypeTemp;
            waybillTracking
                .exportRecordData({ filename: '运单跟踪.xls', ...params }, { responseType: 'blob' })
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '运单跟踪.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                        this.msgSuccess('运单跟踪导出任务已生成！');
                    } else {
                        this.msgError('导出异常请联系管理员');
                    }
                    this.fullLoading = false;
                })
                .catch(() => {
                    this.fullLoading = false;
                });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = null;
                this.queryParams.endCreateDate = null;
            }
            if (this.queryParams.sendAddress) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = this.queryParams.sendAddress;
                this.queryParams.sendProvinceId = sendProvinceId || null;
                this.queryParams.sendCityId = sendCityId || null;
                this.queryParams.sendCountyId = sendCountyId || null;
                this.queryParams['sendTown.id'] = sendTownId || null;
            } else {
                this.queryParams.sendProvinceId = null;
                this.queryParams.sendCityId = null;
                this.queryParams.sendCountyId = null;
                this.queryParams['sendTown.id'] = null;
            }
            if (this.queryParams.receiverAddress) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = this.queryParams.receiverAddress;
                this.queryParams.receiverProvinceId = receiverProvinceId || null;
                this.queryParams.receiverCityId = receiverCityId || null;
                this.queryParams.receiverCountyId = receiverCountyId || null;
                this.queryParams['receiverTown.id'] = receiverTownId || null;
            } else {
                this.queryParams.receiverProvinceId = null;
                this.queryParams.receiverCityId = null;
                this.queryParams.receiverCountyId = null;
                this.queryParams['receiverTown.id'] = null;
            }
            this.queryParams.current = 1;
            this.getList();
        },
        /** 产品分类 字典转换 */
        itemTypeFormatting(row) {
            return this.selectDictLabel(this.fourplProductClassOptions, row.productClass);
        },
        /** 运单状态主记录运输方式 翻译*/
        mainRecordTransWayFormatting(row) {
            return this.selectDictLabel(this.fourplMainRecordTransWayOptions, row.transWay);
        },
        /** 揽收方式 字典转换 */
        orderTypeFormatting(row) {
            return this.selectDictLabel(this.collectionMethod, row.orderType);
        },
        /** 付款方式 字典转换 */
        paymentMethodFormatting(row) {
            return this.selectDictLabel(this.fourplPaymentMethodOptions, row.paymentMethod);
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryParams.queryTime = [];
            this.queryParams.prePackageOption = null;
            let now = moment(new Date()).format('YYYY-MM-DD');
            this.queryParams.queryTime = [now, now];
            this.timeInterval = null;
            this.handleQuery();
        },
        // 展开或者合上
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        // 显示详情
        toDetail(row) {
            this.mainInfo = row;
            this.trajectoryTrackingDetailOpen = true;
        },
        // 查看详情
        toTransDetail(row) {
            // getTransOrder(row.id).then((response) => {
            this.transInfoOpen = true;
            this.transOrderInfo = { ...row, orderId: row.id };
            // })
        },
        trajectoryTrackingDetailClose() {
            this.trajectoryTrackingDetailOpen = false;
        },
        /** 运单状态 字典转换 */
        transStatusFormatting(row) {
            return this.selectDictLabel(this.fourplWaybillStatusOptions, row.transStatus);
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        }
    }
};
</script>

<style scoped></style>
