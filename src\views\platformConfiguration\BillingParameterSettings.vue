<template>
  <div class="app-container">
    <!--  /搜索区域  -->
    <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
      <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
        <el-form-item label="计费参数" prop="variableName">
          <el-input v-model="queryParams.variableName" clearable placeholder="请输入计费参数" @clear="handleQuery" @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item class="last-form-item">
          <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" type="info" @click="resetQuery('queryForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- / 表格内容 -->
    <el-card :body-style="{ padding: '10px' }" shadow="never">
      <div style="margin-bottom: 10px">
        <el-button type="primary" @click="openTheNewChargingParameter('add')">新增</el-button>
        <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="billingParameterSettingPlatformSideTable" @queryTable="getList" />
      </div>
      <column-table v-loading="loading" :columns="columns" :data="orderList" :showIndex="true">
        <template #status="{ row }">
          <el-switch v-model="row.status" :before-change="() => changeStatus(row)" active-color="#13ce66" active-text="启用" active-value="0" inactive-text="禁用" inactive-value="1" inline-prompt />
          <!--          <el-button icon="el-icon-edit" plain  style="padding: 5px 10px" type="primary" @click="openTheNewChargingParameter('edit', row)">修改</el-button>
          <el-button icon="el-icon-delete" plain  style="padding: 5px 10px" type="danger" @click="openTheDeleteDialogBox(row)">删除</el-button>-->
        </template>
      </column-table>
      <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
    </el-card>

    <!-- /新增计费参数 抽屉  -->
    <el-drawer v-model="addChargingParametersVisible" :title="addChargingParametersTitle" size="500px" @close="hideNewChargingParameters()">
      <div v-loading="addChargingParametersLoading" :element-loading-text="addChargingParametersLoadingText" style="background-color: #f2f2f2; padding: 10px">
        <el-card shadow="never">
          <el-form ref="addChargingParametersForm" :model="addChargingParametersForm" :rules="addChargingParametersRules" class="addChargingParametersForm" label-width="auto">
            <el-form-item label="变量名（中文）" prop="variableName">
              <el-input v-model="addChargingParametersForm.variableName" clearable placeholder="请输入变量名（中文）"></el-input>
            </el-form-item>
            <el-form-item label="单位" prop="variableUnit">
              <el-select v-model="addChargingParametersForm.variableUnit" clearable placeholder="请选择单位" style="width: 100%">
                <el-option v-for="item in unitList" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="变量说明" prop="remark">
              <el-input v-model="addChargingParametersForm.remark" :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入变量说明" type="textarea"></el-input>
            </el-form-item>
          </el-form>
        </el-card>
        <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
          <el-button type="info" @click="hideNewChargingParameters()">取消</el-button>
          <el-button v-if="addChargingParametersModify" type="primary" @click="confirmModification()">修改</el-button>
          <el-button v-else type="primary" @click="confirmToAdd()">确定</el-button>
        </div>
      </div>
    </el-drawer>

    <!--  /删除计费参数 对话框  -->
    <el-dialog v-model="deleteBillingParametersVisible" append-to-body center class="dialog__deleteBillingParameters" title="删除计费参数" width="500px">
      <el-result v-loading="deleteBillingParametersLoading" :element-loading-text="deleteBillingParametersLoadingText" icon="warning" title="您确定删除吗？">
        <template slot="extra">
          <el-button @click="deleteBillingParametersVisible = false">取消</el-button>
          <el-button type="primary" @click="hideDeleteBillingParameters()">确认</el-button>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import billingParameterSettings from '@/api/platformFeatures/billingParameterSettings';

export default {
  name: 'BillingParameterSettings',
  components: {
    RightToolbar,
    ColumnTable
  },
  data() {
    return {
      showSearch: true,
      queryParams: {
        current: 1,
        size: 10,
        variableName: null
      },
      columns: [
        { title: '变量名称', key: 'variableName', align: 'center', columnShow: true },
        { title: '单位', key: 'variableUnit', align: 'center', columnShow: true },
        { title: '备注', key: 'remark', align: 'center', columnShow: true },
        { title: '状态', key: 'status', align: 'center', width: '200px', columnShow: true, hideFilter: true, fixed: 'right' }
      ],
      loading: false,
      orderList: [],
      total: 0,
      addChargingParametersVisible: false,
      addChargingParametersTitle: '新增计费参数',
      addChargingParametersLoading: false,
      addChargingParametersLoadingText: '加载中...',
      addChargingParametersForm: {
        variableName: '',
        variableUnit: '',
        remark: ''
      },
      addChargingParametersRules: {
        variableName: [{ required: true, message: '请输入变量名（中文）', trigger: 'blur' }],
        variableUnit: [{ required: true, message: '请选择单位', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入变量说明', trigger: 'blur' }]
      },
      addChargingParametersModify: false,
      deleteBillingParametersVisible: false,
      deleteBillingParametersLoading: false,
      deleteBillingParametersLoadingText: '删除中...',
      deleteBillingParametersId: '',
      unitList: []
    };
  },
  created() {
    this.getDict();
    this.getList();
  },
  methods: {
    /**
     * 获取字典值
     * @returns {Promise<void>}
     */
    async getDict() {
      this.unitList = await this.getDictList('cost_param_factor_unit');
    },
    // 确定修改计费参数
    confirmModification() {
      this.$refs.addChargingParametersForm.validate((valid) => {
        if (valid) {
          this.addChargingParametersLoading = true;
          this.addChargingParametersLoadingText = '修改中...';
          // customerId 随机数
          updateBillingParameter(this.addChargingParametersForm)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({ title: '成功', message: '修改计费参数成功', type: 'success', duration: 2000 });
                this.hideNewChargingParameters();
                this.getList();
              } else {
                this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
              }
            })
            .finally(() => {
              this.addChargingParametersLoading = false;
            });
        }
      });
    },
    // 确定新增计费参数
    confirmToAdd() {
      this.$refs.addChargingParametersForm.validate((valid) => {
        if (valid) {
          this.addChargingParametersLoading = true;
          this.addChargingParametersLoadingText = '新增中...';
          billingParameterSettings.addBillingParameter(this.addChargingParametersForm)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({ title: '成功', message: '新增计费参数成功', type: 'success', duration: 2000 });
                this.hideNewChargingParameters();
                this.getList();
              } else {
                this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
              }
            })
            .finally(() => {
              this.addChargingParametersLoading = false;
            });
        }
      });
    },
    // 获取详情
    getDetail(id) {
      this.addChargingParametersLoading = true;
      this.addChargingParametersLoadingText = '加载中...';
      billingParameterDetail(id)
        .then((res) => {
          if (res.code === 200) {
            // res.data 只取出来 id variableName parameterType remark 赋值给 addChargingParametersForm
            const { id, variableName, remark } = res.data;
            this.addChargingParametersForm = { id, variableName, remark };
          }
        })
        .finally(() => {
          this.addChargingParametersLoading = false;
        });
    },
    getList() {
      this.loading = true;
      billingParameterSettings.billingParameterList(this.queryParams)
        .then((res) => {
          if (res.code === 200 && res.data.records) {
            this.orderList = res.data.records || [];
            this.total = res.data.total || 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    // 确定删除计费参数
    hideDeleteBillingParameters() {
      this.deleteBillingParametersLoading = true;
      deleteBillingParameter(this.deleteBillingParametersId)
        .then((res) => {
          if (res.code === 200) {
            this.$notify({ title: '成功', message: '删除计费参数成功', type: 'success', duration: 2000 });
            this.deleteBillingParametersVisible = false;
            this.getList();
          } else {
            this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
          }
        })
        .finally(() => {
          this.deleteBillingParametersLoading = false;
        });
    },
    // 关闭新增计费参数弹窗
    hideNewChargingParameters() {
      this.addChargingParametersVisible = false;
      this.$refs.addChargingParametersForm.resetFields();
      this.addChargingParametersForm = {
        variableName: '',
        remark: ''
      };
    },
    openTheDeleteDialogBox(row) {
      const { id } = row;
      if (id) {
        this.deleteBillingParametersVisible = true;
        this.deleteBillingParametersId = id;
      } else {
        this.$notify({ title: '失败', message: '请选择要删除的计费参数', type: 'error', duration: 2000 });
      }
    },
    // 打开新增计费参数弹窗
    openTheNewChargingParameter(type, row) {
      this.addChargingParametersVisible = true;
      if (type === 'add') {
        this.addChargingParametersTitle = '新增计费参数';
        this.addChargingParametersModify = false;
      } else if (type === 'edit') {
        this.addChargingParametersTitle = '修改计费参数';
        const { id } = row;
        // 查询计费参数详情
        this.getDetail(id);
        this.addChargingParametersModify = true;
      }
    },
    resetQuery(formName) {
      this.$refs[formName].resetFields();
      this.handleQuery();
    },
    // 修改计费参数状态
    changeStatus(row) {
      const { id, status } = row;
      this.loading = true;
      const params = {
        id,
        status: status === '0' ? '1' : '0'
      };
      billingParameterSettings.updateBillingParamStatus(params)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success('修改成功');
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}

.dialog__deleteBillingParameters {
  :deep(.el-dialog__header) {
    padding-bottom: 20px;
  }

  :deep(.el-result) {
    padding: 0;
  }
}
</style>
