<template>
    <div v-loading="fullLoading">
        <div class="app-container">
            <!-- s 搜索项-->
            <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form">
                    <el-form-item class="date-screening" label="下单时间" prop="queryTime" style="width: 500px">
                        <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束时间"
                            range-separator="至" start-placeholder="开始时间" type="daterange"
                            value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                    </el-form-item>
                    <search-button :is-show-all="isShow" :isShowAllSwitch="false" @handleQuery="handleQuery"
                        @resetQuery="resetQuery" />
                </el-form>
            </el-card>

            <el-card :body-style="{ padding: '10px' }" shadow="never">
                <div class="mb10" style="display: flex">
                    <el-button v-hasPermi="['fourPL:order:add']" type="primary" @click="openTheNewOrderSlider">按时间重新生成
                    </el-button>
                    <el-button type="warning" @click="batchConfirmationOrder">按订单重新生成</el-button>
                    <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" style="margin-left: auto"
                        table-i-d="TimelinessReport" @queryTable="handleQuery"></right-toolbar>
                </div>

                <column-table key="TimelinessReport" ref="ColumnTable" v-loading="loading" :columns="columns" :data="alList"
                    :defaultSort="{ prop: 'createDate', order: 'descending' }" :show-check-box="true" :maxHeight="600"
                    @select="handleSelectionChange" @select-all="selectAll" @row-dblclick="(row) => getDetail(row)">
                    <template #orderTime="{ row }">
                        <span>{{ timeFormatting(row.orderTime) }}</span>
                    </template>
                    <template #lsRenlinTime="{ row }">
                        <span>{{ timeFormatting(row.lsRenlinTime) }}</span>
                    </template>
                    <template #lsGpTime="{ row }">
                        <span>{{ timeFormatting(row.lsGpTime) }}</span>
                    </template>
                    <template #lsXgTime="{ row }">
                        <span>{{ timeFormatting(row.lsXgTime) }}</span>
                    </template>
                    <template #orderConfirmTime="{ row }">
                        <span>{{ timeFormatting(row.orderConfirmTime) }}</span>
                    </template>
                    <template #lsReceiptTime="{ row }">
                        <span>{{ timeFormatting(row.lsReceiptTime) }}</span>
                    </template>
                    <template #branchWhTime="{ row }">
                        <span>{{ timeFormatting(row.branchWhTime) }}</span>
                    </template>
                    <template #branchFjTime="{ row }">
                        <span>{{ timeFormatting(row.branchFjTime) }}</span>
                    </template>
                    <template #branchZxTime="{ row }">
                        <span>{{ timeFormatting(row.branchZxTime) }}</span>
                    </template>
                    <template #branchZcTime="{ row }">
                        <span>{{ timeFormatting(row.branchZcTime) }}</span>
                    </template>
                    <template #psReceiptTime="{ row }">
                        <span>{{ timeFormatting(row.psReceiptTime) }}</span>
                    </template>
                    <template #psRenlinTime="{ row }">
                        <span>{{ timeFormatting(row.psRenlinTime) }}</span>
                    </template>
                    <template #orderSignTime="{ row }">
                        <span>{{ timeFormatting(row.orderSignTime) }}</span>
                    </template>
                    <template #orderAuditTime="{ row }">
                        <span>{{ timeFormatting(row.orderAuditTime) }}</span>
                    </template>
                    <template #createDate="{ row }">
                        <span>{{ timeFormatting(row.createDate) }}</span>
                    </template>
                    <!-- 揽收方式 -->
                    <template #orderType="{ row }">
                        <span>{{ dictionaryFormatting(collectionMethod, row.orderType) }}</span>
                    </template>
                    <!-- 订单状态 -->
                    <template #status="{ row }">
                        <span v-if="row.status == 1" style="color: #5670fe">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 2" style="color: #ff2a2a">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 3" style="color: #ff2a2a">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 4" style="color: #1acd7e">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 5" style="color: #b1b1b1">{{ statusFormater(row) }}</span>
                        <span v-else>{{ statusFormater(row) }}</span>
                    </template>
                </column-table>

                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
                    :total="total" class="mb0" @pagination="handleQuery" />
            </el-card>
        </div>
        <!--    按订单重新生成-->
        <el-dialog v-model="batchConfirmationOrderShow" :show-close="false" append-to-body class="icon-dialog"
            modal-append-to-body title="按订单重新生成" width="400px" center>
            <div style="margin-top: 10px;display: flex;justify-content: center;">
                确认按订单重新生成！
            </div>
            <template #footer>
                <el-button @click="cancelBatchConfirmationOrder">关闭</el-button>
				<el-button type="primary" @click="submitBatchConfirmationOrder">确 定</el-button>
			</template>
        </el-dialog>
        <!-- 按时间生成 -->
        <el-dialog v-model="openTime" :show-close="false" append-to-body class="icon-dialog" modal-append-to-body
            title="按时间重新生成" width="600px" center>
            <div style="margin-top: 10px;display: flex;justify-content: center;">
                <el-date-picker v-model="byTime" :shortcuts="shortcuts" end-placeholder="结束时间" range-separator="至"
                    start-placeholder="开始时间" type="daterange" value-format="YYYY-MM-DD"
                    @change="handleQuery"></el-date-picker>
            </div>
            <div style="font-size: 12px; color: #F59A86;">时间区间不能大于30天</div>
            <template #footer>
                <el-button @click="openTime = false">关闭</el-button>
				<el-button type="primary" @click="confirmationTime">确 定</el-button>
			</template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar';
import timelinessReportApi from '@/api/logisticsManagement/timelinessReport';
import moment from 'moment';

export default {
    name: 'TimelinessReport',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar
    },
    data() {
        return {
            // 显示搜索条件
            showSearch: true,
            // // 订单信息 数据
            form: {
            },
            // 搜索项
            queryParams: {
                current: 1,
                size: 10,
            },
            multiple: true,
            //订单列表
            orderList: [],
            total: 0, // 总条数
            loading: false,
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', width: '120px', fixed: 'left', columnShow: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '120px', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收方式', key: 'orderType', align: 'center', width: '120px', columnShow: true },
                { title: '订单状态', key: 'status', align: 'center', columnShow: true },
                { title: '下单时间', key: 'orderTime', width: '160px', align: 'center', columnShow: true, sortable: true },
                { title: '揽收接单人', key: 'lsRenlinName', width: '120px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '揽收接单时间', key: 'lsRenlinTime', width: '160px', align: 'center', columnShow: true },
                { title: '揽收改派人', key: 'lsGpName', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '揽收改派司机', key: 'lsGpNewName', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '揽收改派时间', key: 'lsGpTime', width: '160px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '订单修改人', key: 'lsXgName', width: '120px', align: 'center', columnShow: true },
                { title: '订单修改时间', key: 'lsXgTime', width: '160px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '订单确认人', key: 'orderConfirmName', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '订单确认时间', key: 'orderConfirmTime', align: 'center', width: '200px', columnShow: true },
                { title: '揽收交接网点人', key: 'lsReceiptName', align: 'center', width: '160px', columnShow: true },
                { title: '揽收交接网点时间', key: 'lsReceiptTime', align: 'center', width: '160px', columnShow: true },
                { title: '网点入库人', key: 'branchWhName', align: 'center', width: '160px', columnShow: true },
                { title: '网点入库时间', key: 'branchWhTime', width: '160px', align: 'center', columnShow: true },
                { title: '网点分拣人', key: 'branchFjName', width: '160px', align: 'center', columnShow: true },
                { title: '网点分拣时间', key: 'branchFjTime', width: '160px', align: 'center', columnShow: true },
                { title: '网点装箱人', key: 'branchZxName', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '网点装箱时间', key: 'branchZxTime', width: '160px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '网点装车人', key: 'branchZcName', align: 'center', width: '160px', hideFilter: true, columnShow: true },
                { title: '网点装车时间', key: 'branchZcTime', align: 'center', width: '160px', hideFilter: true, columnShow: true },
                { title: '配送司机', key: 'psDriverName', align: 'center', width: '200px', hideFilter: true, columnShow: true },
                { title: '配送任务改派人', key: 'psGpName', align: 'center', width: '200px', hideFilter: true, columnShow: true },
                { title: '配送任务改派司机', key: 'psGpNewName', align: 'center', width: '200px', hideFilter: true, columnShow: true },
                { title: '三方物流名称', key: 'flowName', align: 'center', width: '200px', hideFilter: true, columnShow: true },
                { title: '配送交接三方物流时间', key: 'psReceiptTime', align: 'center', width: '200px', hideFilter: true, columnShow: true },
                { title: '地配领货司机', key: 'psRenlinName', align: 'center', width: '200px', hideFilter: true, columnShow: true },
                { title: '地配领货时间', key: 'psRenlinTime', align: 'center', width: '160px', hideFilter: true, columnShow: true },
                { title: '送达客户时间', key: 'orderSignTime', align: 'center', width: '160px', hideFilter: true, columnShow: true },
                { title: '签收审核时间', key: 'orderAuditTime', align: 'center', width: '160px', hideFilter: true, columnShow: true },
                { title: '数据生成时间', key: 'createDate', align: 'center', width: '160px', hideFilter: true, columnShow: true },
            ],
            alList: [],
            collectionMethod: [],
            receivingModeList: [], // 揽收方式
            statusDicts: [],
            batchConfirmationOrderShow: false,
            openTime: false,
            byTime: [],

        };
    },
    computed: {
        // 字典翻译
        dictionaryFormatting() {
            return (data, value) => {
                return this.selectDictLabel(data, value);
            };
        },
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                if (!val) return '';
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },

    async created() {
        // 订单状态字典
        this.statusDicts = await this.getDictList('fourpl_order_status');
        /** 揽收方式 */
        this.collectionMethod = await this.getDictList('fourpl_mail_service');
        this.receivingModeList = JSON.parse(JSON.stringify(this.collectionMethod));
        this.resetQuery();
        this.handleQuery();
    },
    methods: {
        // //批量确认订单
        batchConfirmationOrder() {
            this.selectData = [];
            if (this.selectRows != undefined && this.selectRows.length > 0) {
                this.batchConfirmationOrderShow = true;
                // this.selectRows.forEach((item) => {
                //     this.selectData.push(item.id);
                // });
            } else {
                this.msgError('请选择需要重新生成的数据！');
                this.batchConfirmationOrderShow = false;
            }
        },

        // // 取消批量确认
        cancelBatchConfirmationOrder() {
            this.batchConfirmationOrderShow = false;
        },
        // 按时间重新生成
        openTheNewOrderSlider() {
            this.openTime = true;
        },
        // 确认按时间
        confirmationTime() {
            if (
                Date.parse(this.byTime[1]) - Date.parse(this.byTime[0]) >
                30 * 24 * 60 * 60 * 1000 ||
                Date.parse(this.byTime[0]) > Date.parse(this.byTime[1])
            ) {
                this.$message.error(
                    '时间范围不能超过30天或结束时间不能小于开始时间'
                );
                return;
            } else {
                let data = {
                    startTime:this.byTime[0],
                    endTime: this.byTime[1],
                    synType: '1',
                };
                timelinessReportApi.synOrderTime(data).then((res) => {
                    if (res.code === 200) {
                        this.openTime = false;
                        this.msgSuccess('重新生成成功');
                        this.handleQuery();
                    }
                })
                    .catch((err) => {
                        this.msgError('err');
                    });
            }
        },
        // 确定需要按订单生成
        submitBatchConfirmationOrder() {
            //拼接的数组字符串，接口传参
            var idStr = this.selectRows.map((v) => v.orderId).join(",");
            let data = {
                synType: '1',
                orderIds: idStr,
            };
            timelinessReportApi.synOrderTime(data).then((res) => {
                if (res.code === 200) {
                    this.batchConfirmationOrderShow = false;
                    this.msgSuccess('重新生成成功');
                    this.handleQuery();
                }
            })
                .catch((err) => {
                    this.msgError('err');
                });
        },
        handleQuery() {
            this.alList = [];
            if (this.queryParams.queryTime == null) {
                this.msgError('请选择筛选时间！');
                return false;
            }
            this.queryParams.startTime = this.queryParams.queryTime[0];
            this.queryParams.endTime = this.queryParams.queryTime[1];
            timelinessReportApi.orderTimeList(this.queryParams)
                .then((res) => {
                    if (res.code === 200) {
                        this.loading = false;
                        this.alList = res.data.records || [];
                        this.total = res.data.total || 0;
                    } else {
                        this.loading = false;
                    }
                })
                .catch((err) => {
                    this.msgError('err');
                });
        },
        // 多选框选中数据
        handleSelectionChange(selection, row) {
            this.selectRows = [];
            this.selectRows = selection;
            this.multiple = !this.selectRows.length;
        },
        resetQuery() {
            this.resetForm('queryForm');
            const currentDate = new Date();
            currentDate.setDate(currentDate.getDate() - 1);
            const formattedDate = currentDate.toISOString();
            let now1 = moment(new Date(formattedDate)).format('YYYY-MM-DD HH:mm:ss');
            let now = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            this.queryParams.queryTime = [now1, now];
            this.byTime = [now1, now];
            this.queryParams.prePackageOption = null;
            this.handleQuery();
        },
        // 全选
        selectAll(selection) {
            this.selectRows = selection;
            this.multiple = !this.selectRows.length;
        },
        // 展开或者合上
        showAllClick() {
            this.isShow = !this.isShow;
        },
        statusFormater(row) {
            return this.selectDictLabel(this.statusDicts, row.status);
        },

    }
};
</script>

<style lang="scss" scoped>
.app-container {
    height: 700px;
}

.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}

::v-deep {
    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .el-radio:last-child {
        margin-right: 30px;
    }

    .el-radio {
        margin-bottom: 10px;

        .el-radio__label {
            .el-input__wrapper {
                background: none;
                box-shadow: none;
            }
        }
    }
}
</style>
