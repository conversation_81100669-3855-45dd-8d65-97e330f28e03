<template>
    <div v-loading="loading" class="orderDetails" element-loading-text="加载中...">
        <el-card class="mb10" shadow="never">
            <div class="box__two__columns">
                <div class="d-box-content" style="margin-bottom: 10px">
                    <div class="d-box-column">
                        <div class="d-box-info">
                            <span>订单号</span>
                            <span>{{ dataInfo.orderNo }}</span>
                        </div>
                    </div>
                    <div class="d-box-column">
                        <div v-if="dataInfo.transOrderNo" class="d-box-info">
                            <span>运单号</span>
                            <span>{{ dataInfo.transOrderNo }}</span>
                        </div>
                        <div v-if="dataInfo.handCode" class="d-box-info">
                            <span>承运单号</span>
                            <span>{{ dataInfo.handCode }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="dataInfo.lanTaskDTO" class="box__two__columns">
                <div class="d-box-content">
                    <!-- 揽收任务未领取 lanTask.status=0 -->
                    <div v-if="dataInfo.lanTaskDTO && dataInfo.lanTaskDTO.status == 0" class="d-box-column">
                        <div class="d-box-info">
                            <span>揽收任务领取状态</span>
                            <span style="color: #ff2a2a">未领取</span>
                        </div>
                    </div>

                    <!-- 揽收任务已领取 lanTaskDTO.status!=0 -->
                    <div v-if="dataInfo.lanTaskDTO && dataInfo.lanTaskDTO.status != 0" class="d-box-column">
                        <div class="d-box-info">
                            <span>揽收任务领取状态</span>
                            <span v-if="dataInfo.lanTaskDTO.status == '4'" style="color: #ff2a2a">已取消</span>
                            <span v-else style="color: #1acd7e">已领取</span>
                        </div>
                    </div>
                    <div v-if="dataInfo.lanTaskDTO && dataInfo.lanTaskDTO.status != 0" class="d-box-column">
                        <div v-if="dataInfo.lanTaskDTO.status != 0 && dataInfo.lanTaskDTO.driver" class="d-box-info">
                            <span>揽收司机</span>
                            <span>{{ dataInfo.lanTaskDTO.driver.driverName || '暂无' }}</span>
                        </div>
                        <div v-if="dataInfo.lanTaskDTO.status != 0 && dataInfo.lanTaskDTO.driver" class="d-box-info">
                            <span>司机电话</span>
                            <span>{{ dataInfo.lanTaskDTO.driver.driverPhone || '暂无' }}</span>
                        </div>
                        <div v-if="dataInfo.lanTaskDTO.status != 0 && dataInfo.lanTaskDTO.status != 3 && dataInfo.lanTaskDTO.address" class="d-box-info">
                            <span>司机位置</span>
                            <span>{{ dataInfo.lanTaskDTO.address || '暂无' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>
        <!-- /揽收方式 付款方式-->
        <el-card v-if="dataInfo" class="mb10" shadow="never">
            <div class="box__flex__2">
                <div>
                    <span>揽收方式：</span>
                    <span>{{ dataInfo.orderTypeDesc }}</span>
                </div>
                <div>
                    <span>付款方式：</span>
                    <span>{{ dataInfo.paymentMethodDesc }}</span>
                </div>
                <div v-if="dataInfo.sendBranch && dataInfo.sendBranchName">
                    <span>网点：</span>
                    <span>{{ dataInfo.sendBranchName }}</span>
                </div>
                <div v-if="dataInfo.carrierWay">
                    <span>运输方式：</span>
                    <span>{{ selectDictLabel(carrierWayDicts, dataInfo.carrierWay) }}</span>
                </div>
            </div>
        </el-card>

        <!-- /订单信息 货主公司信息-->
        <div class="box-orderInformation">
            <!--  /订单信息-->
            <el-card class="mb10" shadow="never">
                <card-header title="订单信息" />
                <div class="d-box-content">
                    <div class="d-box-column">
                        <div class="d-box-info">
                            <span>发件地址</span>
                            <span>{{ dataInfo?.sendTown?.province || '' }}{{ dataInfo?.sendTown?.city || '' }}{{ dataInfo?.sendTown?.county || '' }}{{ dataInfo?.sendTown?.town || '' }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>发件详细地点</span>
                            <span>{{ dataInfo.sendAddress }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>发件联系人</span>
                            <span>{{ dataInfo.sendUser }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>发件人电话</span>
                            <span>{{ dataInfo.sendUserPhone }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>发件公司</span>
                            <span>{{ dataInfo.sendCompany }}</span>
                        </div>
                    </div>
                    <div class="d-box-column">
                        <div class="d-box-info">
                            <span>收件地址</span>
                            <span>{{ dataInfo?.receiverTown?.province || '' }}{{ dataInfo?.receiverTown?.city || '' }}{{ dataInfo?.receiverTown?.county || '' }}{{ dataInfo?.receiverTown?.town || '' }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>收件详细地点</span>
                            <span>{{ dataInfo.receiverAddress }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>收件人名称</span>
                            <span>{{ dataInfo.receiverUser }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>收件人电话</span>
                            <span>{{ dataInfo.receiverUserPhone }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>收件公司</span>
                            <span>{{ dataInfo.receiverCompany }}</span>
                        </div>
                    </div>
                </div>
            </el-card>
            <!--  /货主公司信息-->
            <el-card class="mb10" shadow="never">
                <card-header title="货品信息" />
                <div class="d-box-content">
                    <div class="d-box-column">
                        <div class="d-box-info">
                            <span>运输类型</span>
                            <span>{{ dataInfo.productTypeDesc }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>产品分类</span>
                            <span>{{ dataInfo.productClassDesc }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>件数</span>
                            <span>{{ dataInfo.goodsPackages }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>重量</span>
                            <span>{{ dataInfo.productWeight }}kg</span>
                        </div>
                        <div class="d-box-info">
                            <span>容积</span>
                            <span>{{ dataInfo.productVolume }}升</span>
                        </div>
                        <div class="d-box-info">
                            <span>货值金额</span>
                            <span>{{ dataInfo.goodsAmount }}</span>
                        </div>
                    </div>
                    <div class="d-box-column">
                        <div class="d-box-info">
                            <span>温层类型</span>
                            <span>{{ dataInfo.temperatureType.describtion }}</span>
                        </div>
                        <div v-if="dataInfo?.temperatureType?.type != '1' && dataInfo?.temperatureType?.type != '2'" class="d-box-info">
                            <span>使用自有设备</span>
                            <span>{{ dataInfo.isDevice == '1' ? '是' : '否' }}</span>
                        </div>
                        <div v-if="dataInfo.carTypeName" class="d-box-info">
                            <span>车辆类型</span>
                            <span>{{ dataInfo.carTypeName }}</span>
                        </div>
                        <div v-if="dataInfo.kilometre" class="d-box-info">
                            <span>公里数</span>
                            <span>{{ dataInfo.kilometre }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>货品属性</span>
                            <span>{{ dataInfo.productPropertiesDesc }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>随货同行单号</span>
                            <span>{{ dataInfo.externalOrderNo }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>备注</span>
                            <span>{{ dataInfo.remark }}</span>
                        </div>
                        <div class="d-box-info">
                            <span>详情说明</span>
                            <span>{{ dataInfo.detailDesc }}</span>
                        </div>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- /冷链明细-->
        <el-card v-if="dataInfo.orderDetailList && dataInfo.orderDetailList.length > 0" class="mb10" shadow="never">
            <card-header title="冷链明细" />
            <div class="mb15" style="text-align: right">
                <template v-if="coldChainHandoverType && coldChainHandoverType.length == 1">
                    <el-button plain size="small" type="primary" @click="printColdChainHandover(coldChainHandoverType[0].templateId)">打印冷链交接单<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                </template>
                <template v-else-if="coldChainHandoverType && coldChainHandoverType.length > 0">
                    <el-dropdown @command="printColdChainHandover">
                        <el-button plain size="small" type="primary">打印冷链交接单<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item v-for="(item, index) in coldChainHandoverType" :key="index" :command="item.templateId">
                                    {{ item.templateName }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </div>
            <el-table :data="dataInfo.orderDetailList" :fit="true" class="coldChainTable" style="width: 100%">
                <el-table-column align="center" label="序号" prop="index" width="50">
                    <template #default="scope">{{ scope.$index + 1 }}</template>
                </el-table-column>
                <el-table-column :show-overflow-tooltip="true" align="center" label="通用名称" prop="name" />
                <el-table-column :show-overflow-tooltip="true" align="center" label="规格/型号" prop="specifications" />
                <el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" prop="manufacturer" />
                <el-table-column :show-overflow-tooltip="true" align="center" label="单位" prop="basicUnit" />
                <el-table-column align="center" label="数量" prop="quantity" />
                <el-table-column :show-overflow-tooltip="true" align="center" label="批号/序列号" min-width="100" prop="batchNumber" />
                <el-table-column :show-overflow-tooltip="true" align="center" label="使用期限/失效日期" min-width="160" prop="validityDate" />
                <el-table-column :show-overflow-tooltip="true" align="center" label="批准文号/注册证号/备案证号" min-width="200" prop="registrationNumber" />
                <el-table-column :show-overflow-tooltip="true" align="center" label="上市许可持有人/注册人/备案人" min-width="200" prop="listPermitHolder" />
                <el-table-column align="center" label="资料是否齐全">
                    <template #default="scope">
                        {{ scope.row.completeInformation == 1 ? '是' : '否' }}
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- /增值服务 -->
        <el-card v-if="dataInfo.addServiceList && dataInfo.addServiceList.length > 0" class="mb10" shadow="never">
            <card-header title="增值服务" />
            <div v-if="dataInfo.addServiceList && dataInfo.addServiceList.length > 0">
                <el-tag v-for="(service, index) in dataInfo.addServiceList" :key="index" style="margin: 1%">
                    {{ service.name }}<span v-if="service.inputValue" style="color: #666666">：{{ service.inputValue }}</span>
                </el-tag>
            </div>
            <el-empty v-else :image-size="20" description="无增值服务" style="height: 20px"></el-empty>
        </el-card>
        <!-- /费用明细 -->
        <el-card v-if="dataInfo.costList && dataInfo.costList.length > 0" class="mb10" shadow="never">
            <card-header title="费用明细" />
            <div class="cost__card">
                <el-card shadow="never" style="min-width: 250px">
                    <div slot="header" class="cost__card__header">
                        <span>基础费用</span>
                        <div class="card__header__num">
                            <span class="card__header__num__label">¥</span>
                            <span class="card__header__num__value">{{ dataInfo.basicCost }}</span>
                        </div>
                    </div>
                    <div v-if="dataInfo.costList && dataInfo.costList.length > 0" class="cost__box">
                        <div v-for="(item, index) in dataInfo.costList" :key="index" class="cost__item">
                            <div class="card__header__num">
                                <span class="card__header__num__label">¥</span>
                                <span class="card__header__num__value">{{ item.costContractPrice }}</span>
                            </div>
                            <span class="cost__item__label">{{ costOrderTypeFormat(item.costType) }}</span>
                        </div>
                    </div>
                    <div v-else style="color: #999999">暂无数据</div>
                </el-card>
                <el-card shadow="never" style="min-width: 250px">
                    <div slot="header" class="cost__card__header">
                        <span>增值服务费用</span>
                        <div class="card__header__num">
                            <span class="card__header__num__label">¥</span>
                            <span class="card__header__num__value">{{ dataInfo.serviceCost }}</span>
                        </div>
                    </div>
                    <div v-if="dataInfo.addedServices && dataInfo.addedServices.length > 0" class="cost__box">
                        <div v-for="(item, index) in dataInfo.addedServices" :key="index" class="cost__item">
                            <div class="card__header__num">
                                <span class="card__header__num__label">¥</span>
                                <span class="card__header__num__value">{{ item.addedServicesContractPrice }}</span>
                            </div>
                            <span class="cost__item__label">{{ item.addedServicesName }}</span>
                        </div>
                    </div>
                    <div v-else style="color: #999999">暂无数据</div>
                </el-card>
            </div>
            <div class="cost__card__estimate">
                <span>预估费用</span>
                <div class="cost__card__estimate__main">
                    <span class="cost__card__estimate__main__label">¥</span>
                    <span class="cost__card__estimate__main__value">{{ dataInfo.orderCost }}</span>
                </div>
            </div>
        </el-card>
        <el-card v-if="(receiptFileList.length > 0 || signFileList.length > 0) && ((dataInfo.transStatus == '15' && source == '2') || source == '1')" class="mb10" shadow="never">
            <card-header title="签收信息" />
            <div class="d-box-content">
                <el-descriptions :column="2">
                    <el-descriptions-item label="回执单">
                        <div v-if="receiptFileList.length > 0" class="flex file-list">
                            <el-tooltip v-for="(img, i) in receiptFileList" :key="i" content="点击查看大图" effect="dark" placement="right">
                                <div @click="handlePreview(receiptFileList, i)">
                                    <img v-if="checkIsPDFFile(img.fileUrl)" src="/img/pdf.png" />
                                    <img v-else :src="img.fileUrl" />
                                </div>
                            </el-tooltip>
                        </div>
                        <div v-else class="no-pictures">
                            <span>暂无回执单信息</span>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="客户签名">
                        <template #default>
                            <div v-if="signFileList.length > 0" class="flex file-list">
                                <el-tooltip v-for="(img, i) in signFileList" :key="i" class="item" content="点击查看大图" effect="dark" placement="top">
                                    <div @click="handlePreview(signFileList, i)">
                                        <img v-if="checkIsPDFFile(img.fileUrl)" src="/img/pdf.png" />
                                        <img v-else :src="img.fileUrl" />
                                    </div>
                                </el-tooltip>
                            </div>
                            <div v-else class="no-pictures">
                                <span>暂无客户签名信息</span>
                            </div>
                        </template>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-card>
        <component :is="initComponent" v-if="dialogVisible" :current="initialIndex" :url-list="fileUrlList" @close="dialogVisible = false"></component>
    </div>
</template>

<script>
import CardHeader from '@/components/CardHeader';
import orderManagement from '@/api/logisticsManagement/orderManagement';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration';
import handoverOrderConfiguration from '@/api/carrierEnd/handoverOrderConfiguration.js';
import { printLabelView } from '@/utils';
// 打印
export default {
    name: 'OrderDetails',
    components: {
        CardHeader
    },
    props: {
        orderInfo: {
            required: true,
            type: Object
        },
        source: {
            type: String,
            default: '1'
        }
    },
    data() {
        return {
            coldChainHandoverType: [], // 冷链交接单类型
            collectionMethod: [], // 揽收方式
            fourplPaymentMethodOptions: [], // 付款方式
            orderTypeList: [],
            fourplProductClassDicts: [], // 产品分类
            statusDicts: [], // 订单状态字典
            productTypeDicts: [], // 运输类型4PL
            productAttributeDictionary: [], // 商品属性
            fourplOrderPaymentStatusDicts: [], // 付款状态
            costOrderTypeOptions: [], // 费用类型
            receiptFileList: [], // 回执单
            signFileList: [], // 客户签名
            dataInfo: {},
            typeOptions: [], // 车辆类型
            loading: false,
            carrierWayDicts: [], // 运输方式 字典值
            dialogVisible: false,
            fileUrlList: [], // 图片预览地址
            initialIndex: 0
        };
    },
    async created() {
        this.loading = true;
        this.getCarType(); // 车辆类型
        /** 产品分类 */
        this.fourplProductClassDicts = await this.getDictList('fourpl_product_class');
        // 运输类型4PL
        this.productTypeDicts = await this.getDictList('fourpl_product_type');
        /** 付款方式 */
        this.fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
        /** 揽收方式 */
        this.collectionMethod = await this.getDictList('fourpl_mail_service');
        // 商品属性 字典
        this.productAttributeDictionary = await this.getDictList('fourpl_product_attributes');
        /** 费用类型 */
        this.costOrderTypeOptions = await this.getDictList('cost_order_type');
        /** 运输方式 */
        this.carrierWayDicts = await this.getDictList('fourpl_transportation_mode');
        this.receiptFileList = [];
        this.signFileList = [];
        orderManagement
            .queryOrderDrugById({ id: this.orderInfo.orderId })
            .then((response) => {
                if (response.code === 200) {
                    this.dataInfo = response.data;
                    this.$nextTick(() => {
                        if (this.dataInfo.fileList && this.dataInfo.fileList.length > 0) {
                            // 回执单
                            this.receiptFileList = this.dataInfo.fileList.filter((item) => item.fileType == '1');
                            // 客户签名
                            this.signFileList = this.dataInfo.fileList.filter((item) => item.fileType == '2');
                        }
                        orderManagement.orderDetailCostInfo({ orderId: this.orderInfo.orderId }).then((response) => {
                            if (response.code == 200 && response.data.costList) {
                                this.dataInfo.costList = response.data.costList;
                                this.dataInfo.basicCost = this.dataInfo.costList.reduce(function (acc, obj) {
                                    return acc + Number(obj.costContractPrice);
                                }, 0);
                            }
                            if (response.code == 200 && response.data.addedServiceDTOList) {
                                this.dataInfo.addedServices = response.data.addedServiceDTOList;
                                this.dataInfo.serviceCost = this.dataInfo.addedServices.reduce(function (acc, obj) {
                                    return acc + Number(obj.addedServicesContractPrice);
                                }, 0);
                            }
                        });
                        let timer = setTimeout(() => {
                            this.dataInfo.productPropertiesDesc = this.selectDictLabels(this.productAttributeDictionary, this.dataInfo.productProperties);
                            this.dataInfo.productTypeDesc = this.selectDictLabel(this.productTypeDicts, this.dataInfo.productType);
                            this.dataInfo.paymentMethodDesc = this.selectDictLabel(this.fourplPaymentMethodOptions, this.dataInfo.paymentMethod);
                            this.dataInfo.orderTypeDesc = this.selectDictLabel(this.collectionMethod, this.dataInfo.orderType);
                            this.dataInfo.productClassDesc = this.selectDictLabel(this.fourplProductClassDicts, this.dataInfo.productClass);
                            if (this.dataInfo.carType) {
                                this.dataInfo.carTypeName = this.selectDictLabel(this.typeOptions, this.dataInfo.carType);
                            }
                            clearTimeout(timer);
                        }, 500);
                    });
                }
            })
            .catch((e) => {});

        // 冷链交接单模板
        this.getByCompanyTemplate();

        // // 订单状态字典
        // this.statusDicts = await this.getDictList('fourpl_order_status');

        // /** 付款状态 */
        // this.fourplOrderPaymentStatusDicts = await this.getDictList('fourpl_order_payment_status');
        // 关闭遮罩
        this.loading = false;
    },
    computed: {
        /**
         * 是否为pdf文件
         * @returns {boolean}
         */
        checkIsPDFFile() {
            return (fileUrl) => {
                return fileUrl.toLowerCase().endsWith('.pdf');
            };
        }
    },
    methods: {
        /**
         * 预览
         * @param data
         * @param index
         */
        handlePreview(data, index) {
            import('@/components/thePreviewDialog/ThePreviewDialog.vue').then((module) => {
                this.initComponent = module.default;
                this.fileUrlList = data.map((item) => item.fileUrl);
                this.initialIndex = index;
                this.dialogVisible = true;
            });
        },
        /**
         * 获取冷链交接单模板
         */
        getByCompanyTemplate() {
            handoverOrderConfiguration.getByCompanyTemplate({ companyId: this.orderInfo.companyId, carrierId: this.orderInfo.carrierId }).then((response) => {
                this.coldChainHandoverType = [];
                if (response.code === 200 && response?.data?.length > 0) {
                    this.coldChainHandoverType = response?.data;
                }
            });
        },
        // 查询车辆类型
        getCarType() {
            operationConfiguration.listCarType({ carrierId: this.orderInfo.carrierId, status: '0', size: 1000 }).then((response) => {
                this.typeOptions = [];
                if (response.code === 200 && response?.data?.records.length > 0) {
                    this.typeOptions = response?.data?.records.map((item) => {
                        return { name: item.typeName, value: item.typeCode };
                    });
                }
            });
        },
        // 打印冷链交接单
        printColdChainHandover(templateId) {
            orderManagement.packColdChainPassOnSheet({ orderIds: this.dataInfo.id }).then((res) => {
                if (res.code === 200) {
                    orderManagement.updatePrintDeliveryFlag({ orderId: this.dataInfo.id }).then((response) => {
                        if (response.code === 200 && response.data) {
                            printLabelView({ templateId, dataList: res.data });
                        } else {
                            this.$message.error('打印失败');
                        }
                    });
                } else {
                    this.$message.error('打印失败');
                }
            });
        },
        // 费用明细翻译
        costOrderTypeFormat(value) {
            return this.selectDictLabel(this.costOrderTypeOptions, value);
        },
        // 跳转至 揽收区域 管理 页面
        jumpCollectionRegionSettings() {
            this.$router.push({ name: 'CollectArea' });
        }
    }
};
</script>

<style lang="scss" scoped>
.file-list {
    display: flex;
    flex-flow: wrap;
    gap: 10px;
    img {
        width: 100px;
        height: 100px;
    }
}
.orderDetails {
    .heading {
        color: #999999;
    }

    .heading-value {
        color: #707070;
    }

    .el-card__body {
        padding-bottom: 15px;
    }

    .box__flex__2 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 50px;
        font-size: 14px;
        > div {
            :nth-child(1) {
                color: #999999;
            }

            :nth-child(2) {
                color: #666666;
            }
        }
    }
}

.box__two__columns {
    .d-box-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        font-size: 14px;

        .d-box-column {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            > .d-box-info {
                display: flex;
                justify-content: space-between;

                > :nth-child(1) {
                    flex-shrink: 0;
                    flex-grow: 0;
                    text-align: left;
                    margin-right: 10px;
                    color: #999999;
                }

                a:nth-child(2) {
                    color: #333333;
                }
            }
        }

        .d-box-column:first-child {
            border-right: 1px solid #e6ebf5;
            padding-right: 10px;
        }
        .d-box-column:last-child {
            padding-left: 10px;
        }
    }
}
.box-orderInformation {
    // display: grid;
    // grid-template-columns: 1fr 1fr;
    // gap: 16px;
    .d-box-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        font-size: 14px;

        .d-box-column {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            .d-box-info {
                display: flex;
                justify-content: space-between;

                :nth-child(1) {
                    flex-shrink: 0;
                    flex-grow: 0;
                    text-align: left;
                    margin-right: 10px;
                    color: #999999;
                    width: 90px;
                }

                :nth-child(2) {
                    color: #333333;
                }
            }
        }

        .d-box-column:first-child {
            border-right: 1px solid #e6ebf5;
            padding-right: 10px;
        }
        .d-box-column:last-child {
            padding-left: 10px;
        }
    }
}
.cost__card__estimate {
    display: flex;
    gap: 15px;
    font-size: 18px;
    align-items: baseline;
    .cost__card__estimate__main {
        display: flex;
        align-items: baseline;
        gap: 2px;
        color: #ff2a2a;
        .cost__card__estimate__main__label {
            font-size: 18px;
        }
        .cost__card__estimate__main__value {
            font-size: 22px;
        }
    }
}
.cost__card {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    ::v-deep .el-card__header {
        background: #21292508;
    }
}
.cost__card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.card__header__num {
    display: flex;
    align-items: baseline;
    gap: 2px;
    color: #5670fe;
}
.card__header__num__label {
    font-size: 12px;
}
.card__header__num__value {
    font-size: 18px;
}
.cost__box {
    display: flex;
    gap: 20px;
    .cost__item {
        display: flex;
        flex-direction: column;
        gap: 5px;
        .cost__item__label {
            font-size: 12px;
            color: #999999;
        }
    }
}
.cost__card__estimate {
    display: flex;
    gap: 15px;
    font-size: 18px;
    align-items: baseline;
    .cost__card__estimate__main {
        display: flex;
        align-items: baseline;
        gap: 2px;
        color: #ff2a2a;
        .cost__card__estimate__main__label {
            font-size: 18px;
        }
        .cost__card__estimate__main__value {
            font-size: 22px;
        }
    }
}
</style>
