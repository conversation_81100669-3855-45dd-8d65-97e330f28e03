<template>
    <div>
        <!-- 查询表头 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item label="大类" prop="bigType">
                    <el-select v-model="queryParams.bigType" placeholder="请选择大类" @change="(v) => handleChange(v)">
                        <el-option :label="item.name" :value="item.value" v-for=" item in categoryList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小类" prop="smallType">
                    <el-select v-model="queryParams.smallType" placeholder="请选择小类">
                        <el-option :label="item.name" :value="item.value" v-for=" item in subclassList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="资质文件类型" prop="categoryName">
                    <el-select v-model="queryParams.categoryName" placeholder="请选择资质文件类型" >
                        <el-option :label="item.name" :value="item.value" v-for=" item in fileList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchQuery">搜索</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表单区域 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(addForm)" class="creatSpan">新增</el-button>
            <el-table v-loading="loading" :data="configeList" style=" margin-top: 10px;" border>
                <el-table-column label="大类" align="left" prop="bigType"
                    :formatter="(row) => formDict(categoryList, row.bigType)">
                </el-table-column>
                <el-table-column label="小类" align="left" prop="remark">
                </el-table-column>
                <el-table-column label="资质文件类型" align="left" prop="categoryName"
                    :formatter="(row) => formDict(fileList, row.categoryName)">
                </el-table-column>
                <el-table-column label="创建日期" align="left" prop="createDate"
                    :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')">
                </el-table-column>
                <el-table-column label="修改日期" align="left" prop="updateDate"
                    :formatter="(row) => moment(row.updateDate).format('YYYY-MM-DD')">
                </el-table-column>
                <el-table-column label="创建人" align="left" prop="createBy.name">

                </el-table-column>
                <el-table-column label="操作" align="center" width="350">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row, editForm)"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                        <el-button link type="danger" @click="handleDelete(scope.row)"><img src="@/assets/icons/delete.png"
                                style="margin-right:5px" />删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 点击增加的弹窗 -->
        <el-dialog v-model="dialogFormVisible" title="创建资质文件配置" width="30%" :before-close="() => handlerClose()">
          <el-form ref="addForm" :model="dialogform" :rules="rules" label-width="110px">
                <el-form-item label="大类" prop="bigType">
                    <el-select v-model="dialogform.bigType" placeholder="请选择大类" style="width:80%;"
                        @change="(v) => handleChange(v)">
                        <el-option :label="item.name" :value="item.value" v-for=" item in categoryList" :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小类" prop="smallType">
                    <el-select v-model="dialogform.smallType" placeholder="请选择小类" style="width:80%;"
                        @change="(v) => handleChange2(v)">
                        <el-option :label="item.name" :value="item.value" v-for=" item in subclassList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="资质文件类型" prop="categoryName">
                    <el-select filterable v-model="dialogform.categoryName" placeholder="请选择资质文件类型" style="width:80%;">
                        <el-option :label="item.name" :value="item.value" v-for=" item in fileList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="statusAble4" style="margin:-10px 0 0 10px;">
                    <el-checkbox true-label="1" false-label="0" v-model="dialogform.isUpload" label="必填" size="large" />
                    <el-checkbox true-label="1" false-label="0" v-model="dialogform.isFile" label="附件" size="large" />
                    <el-checkbox true-label="1" false-label="0" v-model="dialogform.isMultiPage" label="多页" size="large" />
                </el-form-item>
                <el-form-item prop="statusAble4" style="margin:0 0 0 10px;display: flex;">
                    <el-checkbox true-label="1" false-label="0" v-model="dialogform.isTemplate" label="固定模板" size="large"
                                 @change="changeModel"/>
                    <el-input v-if="elinput" v-model="dialogform.templateSign" style="width:56%;margin-left:20px;"
                        placeholder="请填写模板名称" size="normal" clearable>
                    </el-input>
                </el-form-item>
                <el-form-item label="表单" prop="formSign" style="margin-top:10px;">
                    <el-select multiple v-model="dialogform.formSign" placeholder="请选择表单" style="width:80%;">
                        <el-option :label="item.name" :value="item.value" v-for=" item in signList" :key="item.id" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="saveAlarm(addForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 编辑 -->
        <el-dialog v-model="editdialogFormVisible" title="修改资质文件配置" width="30%" :before-close="() => handlerClose()">
            <el-form :model="editdialogform" ref="editForm" :rules="rules" label-width="110px" size="normal">
                <el-form-item label="大类" prop="bigType">
                    <el-select v-model="editdialogform.bigType" placeholder="请选择大类" style="width:80%;"
                        @change="(v) => handleChange(v)">
                        <el-option :label="item.name" :value="item.value" v-for=" item in categoryList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小类" prop="smallType">
                    <el-select v-model="editdialogform.smallType" placeholder="请选择小类" style="width:80%;">
                        <el-option :label="item.name" :value="item.value" v-for=" item in subclassList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="资质文件类型" prop="categoryName">
                    <el-select v-model="editdialogform.categoryName" placeholder="请选择资质文件类型" style="width:80%;">
                        <el-option :label="item.name" :value="item.value" v-for=" item in fileList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="statusAble" style="margin:-10px 0 0 10px;">
                    <el-checkbox true-label="1" false-label="0" v-model="editdialogform.isUpload" label="必填" size="large" />
                    <el-checkbox true-label="1" false-label="0" v-model="editdialogform.isFile" label="附件" size="large" />
                    <el-checkbox true-label="1" false-label="0" v-model="editdialogform.isMultiPage" label="多页"
                        size="large" />
                </el-form-item>
                <el-form-item prop="statusAble4" style="margin:0 0 0 10px;display: flex;">
                    <el-checkbox true-label="1" false-label="0" v-model="editdialogform.isTemplate" label="固定模板"
                                 size="large" @change="changeModel2"/>
                    <el-input v-if="elinput2" v-model="editdialogform.templateSign" style="width:56%;margin-left:20px;"
                        placeholder="请填写模板名称" size="normal" clearable>
                    </el-input>
                </el-form-item>
                <el-form-item label="表单" prop="formSign" style="margin-top:10px;">
                    <el-select multiple v-model="editdialogform.formSign" placeholder="请选择表单" style="width:80%;">
                        <el-option :label="item.name" :value="item.value" v-for=" item in signList" :key="item.id" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="editAlarm(editForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import {getCurrentInstance, reactive, ref} from 'vue';
import documentDirectory from "@/api/erp/documentDirectory";
import {ElMessage, ElMessageBox} from "element-plus";
// import logList from './logList.vue'
import moment from 'moment';
import tool from '@/utils/tool';

const { proxy } = getCurrentInstance();
const dialogFormVisible = ref(false);
const editdialogFormVisible = ref(false)
const queryParams = ref({
    current: 1,
    size: 10,
})
const total = ref(0)
const elinput = ref(false)
const elinput2 = ref(false)
const supplier = ref(false)
const customer = ref(false)
const categoryList = ref([])
const fileList = ref([])
const signList = ref([])
const queryForm = ref()
const editForm = ref()
const addForm = ref()
const subclassList = ref([])
const dialogform = reactive({})
const editdialogform = reactive({})
const configeList = ref([])
const smallTypeName = ref('')
const alarmName = ref('')
const documentId = ref('')
const rules = reactive({
    bigType: [{ required: true, message: '请选择大类', trigger: 'blur' },],
    smallType: [{ required: true, message: '请选择小类', trigger: 'blur' },],
    categoryName: [{ required: true, message: '请选择资质文件类型', trigger: 'blur' },],
    formSign: [{ required: true, message: '请选择表单', trigger: 'blur' },],
})
// 增加按钮
const handleAdd = (formEl) => {
    subclassList.value = []
    signList.value = []
    dialogFormVisible.value = true
    dialogform.isUpload = '0'
    dialogform.isFile = '0'
    dialogform.isMultiPage = '0'
    dialogform.isTemplate = '0'
    elinput.value = false
    formEl.resetFields()
}
// 创建人

// 编辑按钮
const handleEdit = async (row, status) => {
    editdialogFormVisible.value = true
    elinput2.value = false
    documentId.value = row.id
    editdialogform.isUpload = '0'
    editdialogform.isFile = '0'
    editdialogform.isMultiPage = '0'
    editdialogform.isTemplate = '0'
    documentDirectory.details({ id: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.bigType = res.data.bigType
            editdialogform.smallType = res.data.smallType
            if (res.data.bigType == '1') {
                const getsub = async () => {
                    subclassList.value = await proxy.getDictList("manufacturer_type");
                    signList.value = await proxy.getDictList("factory");
                }
                getsub()
            }
            if (res.data.bigType == '2') {
                const getsub = async () => {
                    subclassList.value = await proxy.getDictList("drug_type");
                    signList.value = await proxy.getDictList("drugs");
                }
                getsub()
            }
            if (res.data.bigType == '3') {
                const getsub = async () => {
                    subclassList.value = await proxy.getDictList("instrument_type");
                    signList.value = await proxy.getDictList("instrument");
                }
                getsub()
            }
            if (res.data.bigType == '4') {
                const getsub = async () => {
                    subclassList.value = await proxy.getDictList("food_type");
                    signList.value = await proxy.getDictList("foods");
                }
                getsub()
            }
            if (res.data.bigType == '5') {
                const getsub = async () => {
                    subclassList.value = await proxy.getDictList("kill_type");
                    signList.value = await proxy.getDictList("strilitate");
                }
                getsub()
            }
            if (res.data.bigType == '6') {
                const getsub = async () => {
                    subclassList.value = await proxy.getDictList("supplier_type");
                  if (res.data.smallType == '1' || res.data.smallType == '3' || res.data.smallType == '5' || res.data.smallType == '7') {
                    signList.value = await proxy.getDictList("production_supplier");
                  } else if (res.data.smallType == '2' || res.data.smallType == '4' || res.data.smallType == '6' || res.data.smallType == '8') {
                    signList.value = await proxy.getDictList("wholesale_supplier");
                  }
                }
                getsub()
            }
            if (res.data.bigType == '7') {
                const getsub = async () => {
                    subclassList.value = await proxy.getDictList("customer_type");
                  if (res.data.smallType == '9') {
                    signList.value = await proxy.getDictList("retail_pharmacy");
                  } else {
                    signList.value = await proxy.getDictList("wholesale_customer");
                  }
                }
                getsub()
            }
            editdialogform.categoryName = res.data.categoryName
            if (res.data.formSign) {
                editdialogform.formSign = res.data.formSign.split(',')
            }
            if (res.data.isUpload) {
                editdialogform.isUpload = res.data.isUpload
            }
            if (res.data.isFile) {
                editdialogform.isFile = res.data.isFile
            }
            if (res.data.isMultiPage) {
                editdialogform.isMultiPage = res.data.isMultiPage
            }
            if (res.data.isTemplate) {
                editdialogform.isTemplate = res.data.isTemplate
            }
            if (res.data.templateSign) {
                elinput2.value = true
                editdialogform.templateSign = res.data.templateSign
            }
        }
    })
}
//搜索
function searchQuery() {
    getList();
}
// 重置
function resetQuery(formEl) {
    formEl.resetFields()
    getList()
}
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
const changeModel = (e) => {
  e == 0 ? elinput.value = false : elinput.value = true
}
const changeModel2 = (e) => {
  e == 0 ? elinput2.value = false : elinput2.value = true
  editdialogform.templateSign = ''
}

// 大类改变
const handleChange = async (v) => {
    queryParams.value.smallType = ''
    dialogform.smallType = ''
    dialogform.formSign = ''
    editdialogform.smallType = ''
    editdialogform.formSign = ''
    if (v == '1') {
        supplier.value = false
        customer.value = false
        subclassList.value = await proxy.getDictList("manufacturer_type");
        signList.value = await proxy.getDictList("factory");
    }
    if (v == '2') {
        supplier.value = false
        customer.value = false
        subclassList.value = await proxy.getDictList("drug_type");
        signList.value = await proxy.getDictList("drugs");
    }
    if (v == '3') {
        supplier.value = false
        customer.value = false
        subclassList.value = await proxy.getDictList("instrument_type");
        signList.value = await proxy.getDictList("instrument");
    }
    if (v == '4') {
        supplier.value = false
        customer.value = false
        subclassList.value = await proxy.getDictList("food_type");
        signList.value = await proxy.getDictList("foods");
    }
    if (v == '5') {
        supplier.value = false
        customer.value = false
        subclassList.value = await proxy.getDictList("kill_type");
        signList.value = await proxy.getDictList("strilitate");
    }
    if (v == '6') {
        supplier.value = true
        customer.value = false
        subclassList.value = await proxy.getDictList("supplier_type");
        // signList.value = await proxy.getDictList("supplier_name");
    }
    if (v == '7') {
        supplier.value = false
        customer.value = true
        subclassList.value = await proxy.getDictList("customer_type");
      // signList.value = await proxy.getDictList("wholesale_customer");
    }
}
//小类改变(存储小类名字)
const handleChange2 = async (v) => {
    // signList.value = []
    if (supplier.value == true) {
        if (v == '1' || v == '3' || v == '5' || v == '7') {
            dialogform.formSign = ''
            signList.value = await proxy.getDictList("production_supplier");
        } else {
            dialogform.formSign = ''
            signList.value = await proxy.getDictList("wholesale_supplier");
        }
    }
    if (customer.value == true) {
        if (v == '1' || v == '2' || v == '3' || v == '4') {
            dialogform.formSign = ''
            signList.value = await proxy.getDictList("wholesale_customer");
        } else if (v == '5' || v == '6' || v == '7' || v == '8') {
            dialogform.formSign = ''
          signList.value = await proxy.getDictList("wholesale_customer");
        }
        else {
            dialogform.formSign = ''
          signList.value = await proxy.getDictList("retail_pharmacy");
        }
    }
    subclassList.value.forEach((i) => {
        if (v == i.value) {
            smallTypeName.value = i.name
        }
    })
}
//列表
function getList() {
    const params = {
        ...queryParams.value,
    }
    documentDirectory.list(params).then(res => {
        if (res.code == 200) {
            configeList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
//大类回显
const formDict = (data, val) => {
    return (data && val) ? proxy.selectDictLabel(data, val) : '--'
}
//新增模板请求
const saveAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
          if (dialogform.templateSign || dialogform.isTemplate != 1) {
            var params = {
              ...dialogform
            }
            params.remark = smallTypeName.value
            params.formSign = params.formSign.toString()
            documentDirectory.save(params).then(res => {
              if (res.code == 200) {
                ElMessage({
                  message: "保存成功",
                  type: "success",
                });
                dialogFormVisible.value = false
                getList()
              } else {
                ElMessage({
                  type: "error",
                  message: "添加失败，请稍后重试",
                });
              }
            })
          } else {
            ElMessage.error('请填写模板名称')
          }
        }
    });
}
//修改模板请求
const editAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
          if (editdialogform.templateSign || editdialogform.isTemplate != 1) {
            var params = {
              ...editdialogform
            }
            params.id = documentId.value
            params.formSign = params.formSign.toString()
            subclassList.value.forEach((i) => {
              if (params.smallType == i.value) {
                params.remark = i.name
              }
            })
            console.log(params);
            documentDirectory.save(params).then(res => {
              if (res.code == 200) {
                ElMessage({
                  message: "修改成功",
                  type: "success",
                });
                editdialogFormVisible.value = false
                getList()
              } else {
                ElMessage({
                  type: "error",
                  message: "添加失败，请稍后重试",
                });
              }
            })
          } else {
            ElMessage.error('请填写模板名称')
          }
        }
    });
}
//删除
function handleDelete(row) {
    proxy.$confirm('是否确认删除此配置模板?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        documentDirectory.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
//大类字典请求
const getDict = async () => {
    categoryList.value = await proxy.getDictList("directory_category");
    fileList.value = await proxy.getDictList("directory_file_name");
    alarmName.value = tool.data.get("USER_INFO")?.name
}
getDict()
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}
</style>
