<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-25 17:45:26
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-07-12 13:50:13
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\views\assist\cooperationQualification\custom\review.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="app-container">
        <el-dialog v-model="reviewVisible" title="操作记录" width="50%" :before-close="beforeClose">
            <div style="max-height:800px;min-height: 500px; overflow-y:scroll" v-loading="loading">
                <LogQuery ref="childRef"/>
            </div>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button @click="beforeClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
 
<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, onMounted } from 'vue'
import LogQuery from '@/components/detailsForm/logQuery.vue'
import custom from '@/api/erp/custom'
import { drugApi } from "@/api/model/commodity/drug/index";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const list = ref([])
const childRef = ref(null)
const props = defineProps({
    reviewVisible: {
        type: Boolean,
        default: false
    },
    beforeClose: {
        type: Object,
        default: () => { }
    },
    data: {
        type: Object,
        default: () => { }
    }
})
const { reviewVisible, beforeClose, data } = toRefs(props)
onMounted(async () => {
    loading.value = true
    let reviewRes = await custom.erpCustomersApproval({ 'customers.id': data.value.id })
    let resq = await drugApi.drugLog({ masterId: data.value.id })
    if ((reviewRes.code == 200 && reviewRes.data) || (resq.code == 200 && resq.data)) {
        childRef.value.timeFns(reviewRes.data?.records,resq.data?.records)
        }
        loading.value = false
})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    ::v-deep .el-form-item {
        width: 22%;
    }
}

::v-deep .labelStyle {
    .el-form-item__label {
        margin-left: 10px;
    }
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}
</style>