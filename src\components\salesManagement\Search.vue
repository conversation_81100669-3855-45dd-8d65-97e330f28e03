<template>
	<el-card body-style="padding-bottom:2px" class="box-card">

    <el-form ref="queryRef" :inline="true" :model="searchForm" class="form_130" label-width="130px">
			<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
				<el-form-item label="客户名称">
          <el-input v-model="searchForm.n3" class="form_225" clearable placeholder="请输入商户名称"
                    style="width: 220px"/>
				</el-form-item>
				<el-form-item label="商品名称">
          <el-input v-model="searchForm.n4" class="form_225" clearable placeholder="请输入商品名称"
                    style="width: 220px"/>
				</el-form-item>
        <el-form-item label="结算方式">
          <el-select v-model="searchForm.n5" class="form_225" placeholder="请选择结算方式" style="width: 220px">
            <el-option label="全部" value=""/>
            <el-option v-for="(item, index) in data.clearingFormType" :key="index" :label="item.name"
                       :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item v-show="showSearch" label="单据日期">
					<div class="xBox">
            <el-date-picker v-model="searchForm.n1" class="form_225" end-placeholder="结束日期" format="YYYY/MM/DD"
                            range-separator="至" size="default" start-placeholder="开始日期" style="width: 220px"
                            type="daterange" value-format="YYYY-MM-DD HH:mm:ss"/>
					</div>
				</el-form-item>
				<el-form-item v-show="showSearch" label="收款期限" prop="noticeTitle">
					<div class="xBox">
            <el-date-picker v-model="searchForm.n2" class="form_225" end-placeholder="结束日期" format="YYYY/MM/DD"
                            range-separator="至" size="default" start-placeholder="开始日期" style="width: 220px"
                            type="daterange" value-format="YYYY-MM-DD HH:mm:ss"/>
					</div>
				</el-form-item>


        <el-form-item v-show="showSearch" label="订单状态">
          <el-select v-model="searchForm.status" class="form_225" placeholder="请选择订单状态" style="width: 220px">
            <el-option label="全部" value=""/>
            <el-option v-for="(item, index) in data.statusType" :key="'status' + index" :label="item.name"
                       :value="item.value"/>
					</el-select>
				</el-form-item>
        <el-form-item v-show="showSearch" label="出库状态">
          <el-select v-model="searchForm.n7" class="form_225" placeholder="请选择出库状态" style="width: 220px">
            <el-option label="待出库" value="0"/>
            <el-option label="出库中" value="1"/>
            <el-option label="出库完成" value="2"/>
					</el-select>
				</el-form-item>

        <el-form-item v-show="showSearch" label="制单人">
          <el-input v-model="searchForm.n8" class="form_225" clearable placeholder="请输入制单人"
                    style="width: 220px"/>
				</el-form-item>

				<el-form-item v-show="showSearch" label="库号">
          <el-input v-model="searchForm.n9" class="form_225" clearable placeholder="请输入库号" style="width: 220px"/>
				</el-form-item>

        <el-form-item v-show="showSearch" label="经手人">
          <el-input v-model="searchForm.n10" class="form_225" clearable placeholder="请输入经手人"
                    style="width: 220px"/>
				</el-form-item>

        <el-form-item v-show="showSearch" label="自营扣率">
          <el-select v-model="searchForm.n11" class="form_225" placeholder="请选择自营扣率" style="width: 220px">
            <el-option v-for="(item, index) in data.discount" :key="index" :label="item.name"
                       :value="item.value"/>
					</el-select>
				</el-form-item>

        <el-form-item v-show="showSearch" label="单据编号">
          <el-input v-model="searchForm.n12" class="form_225" clearable placeholder="请输入单据编号"
                    style="width: 220px"/>
				</el-form-item>
        <el-form-item v-show="showSearch" label="审核人">
          <el-input v-model="searchForm.n13" class="form_225" clearable placeholder="请输入审核人"
                    style="width: 220px"/>
				</el-form-item>
        <el-form-item v-show="showSearch" label="业务类型">
          <el-select v-model="searchForm.n14" class="form_225" placeholder="请选择业务类型" style="width: 220px">
            <el-option v-for="(item, index) in data.business" :key="index" :label="item.name"
                       :value="item.value"/>
					</el-select>
				</el-form-item>
			</TopTitle>
		</el-form>

	</el-card>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import TopTitle from "@/components/topTitle/index.vue";

const {proxy} = getCurrentInstance();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const searchForm = ref({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: "",
	status: "",
	n7: "",
	n8: "",
	n9: "",
	n10: "",
	n11: "",
	n12: "",
	n13: "",
	n14: "",
	n15: "",
});
const showSearch = ref(false);
const data = reactive({
	discount: [],
	clearingFormType: [],
	business: [],
});
const emit = defineEmits(["handleQuery"]);
const handleQuery = () => {
	emit("handleQuery", searchForm.value);
};
const resetQuery = () => {
	for (let i in searchForm.value) {
		searchForm.value[i] = "";
	}
	emit("handleQuery", searchForm.value);
};
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	if (localStorage.getItem('orderSearchType')) {
		let obj = JSON.parse(localStorage.getItem('orderSearchType'))
		data.discount = obj.discount
		data.clearingFormType = obj.clearingFormType
		data.business = obj.business
		data.statusType = obj.statusType
	} else {
		data.discount = await proxy.getDictList("erp_discount");
		data.clearingFormType = await proxy.getDictList("erp_clearingForm");
		data.business = await proxy.getDictList("erp_business");
		data.statusType = await proxy.getDictList("status_sales");
		let obj = {
			discount: data.discount,
			clearingFormType: data.clearingFormType,
			business: data.business,
			statusType: data.statusType
		}
		localStorage.setItem('orderSearchType', JSON.stringify(obj))
	}
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	searchForm
});
</script>
<style lang="scss" scoped>
.xBox {
	width: 220px
}
</style>
