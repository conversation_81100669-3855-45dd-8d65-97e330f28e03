<template v-loading="dialogLoading">
  <el-cascader :options="dataList" :props="props" :show-all-levels="true"></el-cascader>
</template>
<script>
export default {
  props: {
    vmodel: { type: Object, default: () => ({}) },
  },
  watch: {},
  data() {
    return {
      dialogLoading: false,
      props: {
        value: "id",
        label: "name",
        children: "children",
        leaf: "leaf",
        lazy: false,
      },
      dataList: [],
      //查询表单
      searchForm: {},
    };
  },
  mounted() {
    //刷新数据列表
    this.getDataList("620000");
  },
  methods: {
    async getDataList(parentId) {
      this.dialogLoading = true;
      //初始化数据列表
      this.dataList = [];
      //只查询甘肃省
      this.searchForm = {
        "parent.id": parentId,
      };
      //请求接口
      var res = await this.$API.sysAreaService.treeDataByParent({ parentId: parentId });
      if (res.code == 200) {
        this.dataList = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      this.dialogLoading = true;
    },
  },
};
</script>
