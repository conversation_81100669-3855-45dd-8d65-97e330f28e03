<template>
    <div>
        <!-- 搜索 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item prop="warehouse">
                    <el-input v-model="queryParams.name" placeholder="请输入仓库名称或简称查询" clearable class="form_225"
                        @clear="getList" @keydown.enter="handleSearch">
                        <template v-slot:suffix>
                            <el-icon @click="handleSearch">
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <!-- <el-button type="primary" size="default" @click="handlerForm">222</el-button> -->

            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(creatForm)" class="creatSpan">新增</el-button>
            <RightToptipBarV2 @handleRefresh="getList" className="informationMaintenance"
                style="float:right;margin-top:10px" />
            <DragTableColumn :columns="columns" :tableData="stashList" className="informationMaintenance"
                v-model:queryParams="queryParams" :getList="getList">
                <template v-slot:operate="{ scopeData }">
                    <el-button link type="primary" @click="handleEdit(scopeData.row, editcreatForm)"><img
                            src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                    <el-button link type="danger" @click="handleDelete(scopeData.row)"><img src="@/assets/icons/delete.png"
                            style="margin-right:5px" />删除</el-button>
                    <el-button link @click="handlerLog(scopeData.row)" style="color:#67c23a"><img
                            src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                </template>
            </DragTableColumn>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增的弹框 -->
        <el-dialog v-model="dialogFormVisible" width="41%" title="新增仓信息" :before-close="() => handlerClose()">
            <el-form :model="dialogform" label-width="100px" :rules="rules" ref="creatForm">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="仓库编码" prop="code">
                            <el-input v-model="dialogform.code" autocomplete="off" placeholder="请输入仓库编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="仓库名称" prop="name">
                            <el-input v-model="dialogform.name" autocomplete="off" placeholder="请输入仓库名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="仓库简称" prop="nameSimplified">
                            <el-input v-model="dialogform.nameSimplified" autocomplete="off" placeholder="请输入仓库简称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="仓库简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="dialogform.nameSimplifiedPinyin" autocomplete="off" placeholder="请输入仓库简拼" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="仓库地址" prop="addressCode">
                    <el-cascader :options="sysAreas" v-model="dialogform.addressCode" placeholder="请选择仓库地址"
                        style="width:100%" @change="handleChange">
                    </el-cascader>
                </el-form-item>
                <el-form-item label="详细地址" prop="addressDetail">
                    <el-input v-model="dialogform.addressDetail" autocomplete="off" placeholder="请输入详细地址" />
                </el-form-item>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="经度" prop="longitude">
                            <el-input v-model="dialogform.longitude" autocomplete="off" placeholder="请输入经度" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="纬度" prop="latitude">
                            <el-input v-model="dialogform.latitude" autocomplete="off" placeholder="请输入纬度" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="dialogform.remark" autocomplete="off" placeholder="请输入备注" />
                </el-form-item>
                <el-form-item label="仓库平面图" prop="plane">
                    <el-upload v-model:file-list="dialogform.plane" :action="uploadUrl" list-type="picture-card"
                        :headers='headers' :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-dialog v-model="dialogVisible">
                    <img w-full :src="dialogImageUrl" alt="Preview Image" />
                </el-dialog>
                <el-form-item label="仓库CRT图" prop="crtPicture">
                    <el-upload v-model:file-list="dialogform.crtPicture" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="creat(creatForm)">确定</el-button>
            </div>
        </el-dialog>
        <!-- 编辑的弹框 -->
        <el-dialog v-model="editdialogFormVisible" width="40%" title="编辑仓信息" :before-close="() => handlerClose()">
            <el-form :model="editdialogform" label-width="100px" :rules="rules2" ref="editcreatForm">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="仓库编码" prop="code">
                            <el-input v-model="editdialogform.code" autocomplete="off" placeholder="请输入仓库编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="仓库名称" prop="name">
                            <el-input v-model="editdialogform.name" autocomplete="off" placeholder="请输入仓库名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="仓库简称" prop="nameSimplified">
                            <el-input v-model="editdialogform.nameSimplified" autocomplete="off" placeholder="请输入仓库简称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="仓库简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="editdialogform.nameSimplifiedPinyin" autocomplete="off"
                                placeholder="请输入仓库简拼" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="仓库地址" prop="addressCode">
                    <el-cascader :options="sysAreas" v-model="addressCode" placeholder="请选择仓库地址"
                        style="width: 100%;min-width:100px" @change="edithandleChange">
                    </el-cascader>
                </el-form-item>
                <el-form-item label="详细地址" prop="addressDetail">
                    <el-input v-model="editdialogform.addressDetail" autocomplete="off" placeholder="请输入详细地址" />
                </el-form-item>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="经度" prop="longitude">
                            <el-input v-model="editdialogform.longitude" autocomplete="off" placeholder="请输入经度" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="纬度" prop="latitude">
                            <el-input v-model="editdialogform.latitude" autocomplete="off" placeholder="请输入纬度" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="editdialogform.remark" autocomplete="off" placeholder="请输入备注" />
                </el-form-item>
                <el-form-item label="仓库平面图" prop="plane">
                    <el-upload v-model:file-list="editdialogform.plane" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload" :data="{ fileType: '1', fileName: '仓库平面图' }">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item label="仓库CRT图" prop="crtPicture">
                    <el-upload v-model:file-list="editdialogform.crtPicture" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload" :data="{ fileType: '1', fileName: '仓库平面图' }">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="edit(editcreatForm)">确定</el-button>
            </div>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
        <!-- 图片预览 -->
        <viewImg v-if="uploadVisible" :visible="uploadVisible" :src="uploadViewImgUrl"
            :beforeClose="() => uploadVisible = false" />

    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { genFileId } from 'element-plus'
import informationMaintenance from '@/api/erp/warehouseManagement/informationMaintenance'
const { proxy } = getCurrentInstance();
import tool from '@/utils/tool';
import logList from './logList.vue'
const queryParams = reactive({
    current: 1,
    size: 10,
})
const selected = ref([])
const sysAreas = ref([])
const addressCode = ref([])
const plane = ref([])
const qualityCodeRef = ref()
const crtPicture = ref([])
const stashList = ref([])
const addressList = ref([])
const addressFull = ref('')
const ids = ref('')
const resAddressList = ref([])
const editaddressList = ref([])
const dialogFormVisible = ref(false)
const editdialogFormVisible = ref(false)
const reviewVisible = ref(false)
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const uploadVisible = ref(false)
const dialogform = ref({})
const reviewRow = ref({})
const editdialogform = ref([])
const creatForm = ref()
const editcreatForm = ref()
const uploadViewImgUrl = ref("")
const total = ref(0)
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const columns = ref(
    [
        {
            label: '仓库编码',
            prop: 'code',
        },
        {
            label: '仓库名称',
            prop: 'name'
        }, {
            label: '仓库简称',
            prop: 'nameSimplified'
        }, {
            label: '仓库简拼',
            prop: 'nameSimplifiedPinyin'
        }, {
            label: '仓库地址',
            prop: 'addressFull',
            minWidth: 200,
        }
        , {
            label: '仓库类别',
            prop: 'commodityPackageSpecification'
        }, {
            label: '经度',
            prop: 'longitude'
        }, {
            label: '纬度',
            prop: 'latitude'
        }, {
            label: '操作',
            prop: 'operate',
            type: 'operate',
            minWidth: 200,
        },
    ]
)
const rules = reactive({
    code: [{ required: true, message: '请输入仓库编码', trigger: 'blur' },],
    name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' },],
    nameSimplified: [{ required: true, message: '请输入仓库简称', trigger: 'blur' },],
    nameSimplifiedPinyin: [{ required: true, message: '请输入仓库简拼', trigger: 'blur' },],
    addressCode: [{ required: true, message: '请选择仓库地址', trigger: 'blur' },],
    addressDetail: [{ required: true, message: '请输入仓库详细地址', trigger: 'blur' },],
    longitude: [{ required: true, message: '请输入经度', trigger: 'blur' },],
    latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' },],
    plane: [{ required: true, message: '请上传仓库平面图', trigger: 'blur' },],
    crtPicture: [{ required: true, message: '请上传仓库CRT图', trigger: 'blur' },],
})
const rules2 = reactive({
    code: [{ required: true, message: '请输入仓库编码', trigger: 'blur' },],
    name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' },],
    nameSimplified: [{ required: true, message: '请输入仓库简称', trigger: 'blur' },],
    nameSimplifiedPinyin: [{ required: true, message: '请输入仓库简拼', trigger: 'blur' },],
    addressCode: [{ required: true, message: '请选择仓库地址', trigger: 'blur' },],
    addressDetail: [{ required: true, message: '请输入仓库详细地址', trigger: 'blur' },],
    longitude: [{ required: true, message: '请输入经度', trigger: 'blur' },],
    latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' },],
    plane: [{ required: true, message: '请上传仓库平面图', trigger: 'blur' },],
    crtPicture: [{ required: true, message: '请上传仓库CRT图', trigger: 'blur' },],
})
sysAreas.value = proxy.getSysAreas.map(v => {
    return { value: v.value, label: v.label, children: v.children }
})
const beforeFile = (file) => {
    if (file.size > 2097152) {
        proxy.msgError("文件不能大于2M");
        return false;
    }
}
const handleUploadSuccess = (res, file, fileList, index, type) => {
    if (res.code == 200) {
        if (type == 3) {
            dialogform.value.plane = [res.data]
        }
    } else {
        proxy.msgError(res.msg)
    }
}
const handleExceed = (files, ref) => {
    ref.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    ref.handleStart(file)
    ref.submit()
}
// 新增按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    addressList.value = []
    formEl.resetFields()
}
// 上传图片
const handlePictureCardPreview = (uploadFile) => {
    uploadViewImgUrl.value = uploadFile?.url
    uploadVisible.value = true
    //     dialogImageUrl.value = uploadFile.url
    //   dialogVisible.value = true
}
const handleRemove = (uploadFile, uploadFiles) => {
    console.log(uploadFile, uploadFiles)
}
const beforeUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
        ElMessage.error('只允许上传图片文件!')
    }
    return isImage;
}
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
//搜索
const handleSearch = () => {
    getList()
}
//仓信息列表
function getList() {
    informationMaintenance.list({
        ...queryParams
    }).then(res => {
        if (res.code == 200) {
            stashList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
// 编辑按钮
const handleEdit = (row, formEl) => {
    editdialogFormVisible.value = true
    // formEl.resetFields()
    informationMaintenance.detail({ id: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.value = res.data
            addressFull.value = res.data.addressFull
            ids.valus = res.data.id
            addressCode.value = res.data.addressCode.split(',')
            editdialogform.value.plane = []
            editdialogform.value.crtPicture = []
            res.data.commonFileDTOList.forEach((item) => {
                // console.log(item);
                if (item.fileType == '1') {
                    editdialogform.value.plane.push({
                        url: item.fileUrl,
                        name: item.fileName,
                        id: item.id
                    })
                } else {
                    editdialogform.value.crtPicture.push({
                        url: item.fileUrl,
                        name: item.fileName,
                        id: item.id
                    })
                }
            })
        }
    })
}
const edithandleChange = () => {
    let depth1 = (node, nodeList = []) => {
        node.forEach((item) => {
            nodeList.push(item)
            if (item.children) {
                depth1(item.children, nodeList)
            }
        })
        return nodeList
    }
    var arr = depth1(proxy.getSysAreas)
    let arrlist = () => {
        arr.forEach((v) => {
            addressCode.value.forEach(item => {
                if (v.value == item) {
                    editaddressList.value.push(v.label)
                }
            })
        },
        );
    }
    arrlist()
}

const edit = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            const params = {
                ...editdialogform.value,
            }
            params.addressCode = addressCode.value.toString()
            if (editaddressList.value == '') {
                params.addressFull = addressFull.value
            } else {
                params.addressFull = editaddressList.value.toString().replace(/,/g, '') + params.addressDetail
            }
            params.commonFileDTOList = []
            editdialogform.value.plane.forEach((item) => {
                params.commonFileDTOList.push({
                    fileType: '1',
                    id: item.id,
                    fileName: item.name,
                    fileUrl: item.url
                })
            })
            editdialogform.value.crtPicture.forEach((item) => {
                params.commonFileDTOList.push({
                    fileType: '2',
                    id: item.id,
                    fileName: item.name,
                    fileUrl: item.url
                })
            })
            // console.log(params.commonFileDTOList);
            // console.log(params);
            params.id = ids.valus
            console.log(params);
            informationMaintenance.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "修改成功",
                            type: "success",
                        });
                        editdialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "修改失败，请稍后重试",
                        });
                    }
                })
        }
    });
};

// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
//四级地址
const handleChange = () => {
    let depth1 = (node, nodeList = []) => {
        node.forEach((item) => {
            nodeList.push(item)
            if (item.children) {
                depth1(item.children, nodeList)
            }
        })
        return nodeList
    }
    var arr = depth1(proxy.getSysAreas)
    let arrlist = () => {
        arr.forEach((v) => {
            dialogform.value.addressCode.forEach(item => {
                if (v.value == item) {
                    addressList.value.push(v.label)
                }
            })
        },
        );
    }
    arrlist()
}
//创建仓信息
const creat = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        console.log(11111);
        if (valid) {
            const params = {
                ...dialogform.value,
            }
            params.addressCode = params.addressCode.toString()
            params.addressFull = addressList.value.toString().replace(/,/g, '') + params.addressDetail
            params.commonFileDTOList = []
            if (params.plane) {
                params.plane.forEach((item) => {
                    params.commonFileDTOList.push({
                        fileType: '1',
                        fileName: item.response.data.name,
                        fileUrl: item.response.data.url
                    })
                })
                delete params.plane
            }
            if (params.crtPicture) {
                params.crtPicture.forEach((item) => {
                    params.commonFileDTOList.push({
                        fileType: '2',
                        fileName: item.response.data.name,
                        fileUrl: item.response.data.url
                    })
                })
                delete params.crtPicture
            }
            informationMaintenance.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        dialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "添加失败，请稍后重试",
                        });
                    }
                })
        }
    });
};
// 删除
const handleDelete = (row) => {
    proxy.$confirm('是否确认删除此仓信息?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        informationMaintenance.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
</script>

<style lang="scss" scoped>
.dialog {
    padding: 30px
}

::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.dialogP {
    position: relative;
    top: -5px;
    left: 100px
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
    display: contents !important;
}

.creatSpan {
    margin-bottom: 10px;
}

.dialog-footer {
    display: flex;
    justify-content: end;
}</style>
