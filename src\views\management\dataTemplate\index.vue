<!--  数据模版 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto">
                <el-form-item label="模版名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入模版名称" @keyup.enter="handleQuery" />
                </el-form-item>
                <!-- <el-form-item label="数据时间">
                            <el-date-picker v-model="queryParams.timeList" style="width: 400px"
                                value-format="YYYY-MM-DD HH:mm:ss" type="datetimerange" range-separator="-"
                                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item> -->
                <search-button :is-show-all-switch="false" @handleQuery="getList" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" :icon="Upload" size="mini" type="success" @click="DownloadUpload">下载导入模版</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" size="mini" type="primary" @click="batch">新增</el-button>
                    </el-col>
                </el-row>
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table v-loading="loading" :data="incubatorList" border style="margin-top: 15px" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column align="center" label="模板名称" prop="name" width="200" />
                <el-table-column align="center" label="高温阈值" prop="highTempThreshold" width="100" />
                <el-table-column align="center" label="低温阈值" prop="lowTempThreshold" width="100" />
                <el-table-column align="center" label="数据开始时间" min-width="160" prop="dataStartDate" />
                <el-table-column align="center" label="数据截止时间" min-width="160" prop="dataEndDate" />
                <el-table-column align="center" label="时长（分钟）" prop="duration" width="100" />
                <el-table-column align="center" label="开关" prop="isEnable">
                    <template #default="scope">
                        <el-switch v-model="scope.row.isEnable" active-text="开" inactive-text="关" inline-prompt />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="220" show-overflow-tooltip fixed="right">
                    <template #default="scope">
                        <el-button icon="el-icon-download" link size="small" type="warning" @click="handleExport(scope.row)">导出</el-button>
                        <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="viewJSON(scope.row)">查看</el-button>
                        <el-button icon="el-icon-delete" link size="small" type="danger" @click="showJSON(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增 -->
        <el-dialog v-model="dialogtype" :before-close="handleClose" :title="title" width="30%">
            <div style="color: #f7a944; margin-bottom: 10px">上传模板的预设数据</div>
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="form" label-width="70px">
                <div>
                    <el-form-item label="模版名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入模版名称" style="width: 380px" />
                    </el-form-item>
                    <el-form-item class="define" label="高温阀值" prop="highTempThreshold">
                        <el-input-number v-model="form.highTempThreshold" controls-position="right" placeholder="请输入高温阀值" style="width: 380px" />
                    </el-form-item>
                    <el-form-item class="define" label="低温阀值" prop="lowTempThreshold">
                        <el-input-number v-model="form.lowTempThreshold" controls-position="right" placeholder="请输入低温阀值" style="width: 380px" />
                    </el-form-item>
                    <el-form-item label="开始时间" prop="dataStartDate">
                        <el-date-picker v-model="form.dataStartDate" placeholder="请选择数据开始时间" style="width: 380px" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="截止时间" prop="dataEndDate">
                        <el-date-picker v-model="form.dataEndDate" placeholder="请选择数据截止时间" style="width: 380px" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="是否启用" prop="isEnable">
                        <el-switch v-model="form.isEnable" active-text="开" inactive-text="关" inline-prompt size="large" style="margin-left: 20px" />
                    </el-form-item>
                </div>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancellation">取消</el-button>
                    <el-button type="primary" @click="determine">确定</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 上传 -->
        <el-dialog v-model="dialogVisible" :before-close="handleClose" :title="title" width="30%">
            <div style="color: #f7a944; margin-bottom: 10px">上传模板的预设数据</div>
            <div style="text-align: center">
                <el-upload
                    ref="upload"
                    v-model:file-list="fileList"
                    :action="uploadUrl"
                    :before-remove="beforeRemove"
                    :headers="headers"
                    :limit="1"
                    :on-exceed="(files) => handleExceed(files, upload)"
                    :on-preview="handlePreview"
                    :on-progress="uploadVideoProcess"
                    :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, index)"
                    class="upload-demo"
                    drag
                >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">请拖拽此处或<em>点击上传</em></div>
                    <template #tip>
                        <div class="el-upload__tip">最大文件上传为500kb</div>
                    </template>
                </el-upload>
            </div>
        </el-dialog>
        <!-- 查看 -->
        <el-drawer v-show="calibrationOpen" v-model="calibrationOpen" :title="title" append-to-body size="800" style="padding: 0 30px">
            <el-card class="box-card Botm">
                <div style="display: flex; justify-content: space-between; align-items: center; font-size: 14px">
                    <div style="display: flex">
                        <div>
                            模板名称： <span>{{ detailsList.name }}</span>
                        </div>
                        <div style="margin-left: 30px">
                            数据时间：<span>{{ detailsList.dataStartDate }}-{{ detailsList.dataEndDate }}</span>
                        </div>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; font-size: 14px">
                    <div style="display: flex">
                        <div>
                            高温阈值： <span>{{ detailsList.highTempThreshold }}℃</span>
                        </div>
                        <div style="margin-left: 30px">
                            低温阈值：<span>{{ detailsList.lowTempThreshold }}℃</span>
                        </div>
                    </div>
                </div>
            </el-card>
            <div style="margin-top: 10px">
                <el-button v-hasPermi="['device:iceRaft:add']" :icon="Upload" size="mini" type="danger" @click="batch(1)">上传模版</el-button>
            </div>
            <div style="font-size: 14px; margin-top: 10px">
                <span style="margin-bottom: 10px">验证记录</span>
                <el-table :data="calData" border style="width: 100%">
                    <el-table-column label="记录时间" prop="separate" />
                    <el-table-column label="温度" prop="temp" />
                </el-table>
                <div style="float: right; margin: 15px 0">
                    <pagination :total="templateDetails.total" v-model:page="templateDetails.current" v-model:limit="templateDetails.size" @pagination="getdetailsList(detailsList)" />
                </div>
                <div style="margin: 30px 50%; padding-top: 50px">
                    <el-button @click="calibrationOpen = false">取 消</el-button>
                    <!-- <el-button type="primary" @click="submitCalibration">确 定</el-button> -->
                </div>
            </div>
        </el-drawer>
    </div>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue';
import { Download, Upload } from '@element-plus/icons-vue';
import dataTemplateApi from '@/api/management/dataTemplate';
import { genFileId } from 'element-plus';
import tool from '@/utils/tool';
import qs from 'qs';
import SearchButton from '@/components/searchModule/SearchButton.vue';
const { proxy } = getCurrentInstance();
// import moment from 'moment';

// 显示搜索条件
const showSearch = ref(true);
// 查询参数
const queryParams = ref({
    current: 1,
    size: 10,
    total: 0
});
const templateDetails = ref({
    current: 1,
    size: 10,
    total: 0
});

const title = ref('');
const dialogVisible = ref(false);
const dialogtype = ref(false);

/** 上传模版 */
function batch(val) {
    if (val === 1) {
        dialogVisible.value = true;
        title.value = '上传模版';
    } else {
        dialogtype.value = true;
        title.value = '新增';
    }
}
const form = ref({});

// 确定
function determine() {
    let startTime = form.value.dataStartDate;
    let endTime = form.value.dataEndDate;
    let minuteDiff = (new Date(endTime).getTime() - new Date(startTime).getTime()) / (1000 * 60);
    form.value = {
        duration: Math.trunc(minuteDiff),
        name: form.value.name,
        highTempThreshold: form.value.highTempThreshold,
        lowTempThreshold: form.value.lowTempThreshold,
        isEnable: form.value.isEnable,
        dataStartDate: form.value.dataStartDate,
        dataEndDate: form.value.dataEndDate
    };
    dataTemplateApi
        .tempTempletSave(form.value)
        .then((res) => {
            if (res.code == 200) {
                proxy.msgSuccess('新增成功');
                getList();
                dialogtype.value = false;
                form.value = {};
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
            dialogtype.value = false;
        });
}

// 取消
function cancellation() {
    dialogtype.value = false;
    form.value = {};
}
// 重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    getList();
}

// 查看
const calibrationOpen = ref(false);
const detailsList = ref([]);
const detailsLisy = ref([]);
function viewJSON(row) {
    detailsLisy.value = row;
    getdetailsList(row);
    detailsList.value = row;
    calibrationOpen.value = true;
    title.value = '详情';
}

// 删除
function showJSON(row) {
    proxy
        .$confirm('是否确认删除数据?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            dataTemplateApi
                .tempTempletDelete({
                    ids: row.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        proxy.msgSuccess('删除成功');
                        getList();
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        });
}

const uploadUrl = '/device/tempTempletData/import';
const videoUploadPercent = ref();
const fileList = ref([]);
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
    ContentType: 'multipart/form-data',
    clientType: 'pc'
};
function uploadVideoProcess(event, file, fileList) {
    videoUploadPercent.value = Math.floor(event.percent);
}
// 点击已经上传的文件触发
const handlePreview = (uploadFile) => {
    console.log(uploadFile);
};
const uploadSucceed = ref(0);
const uploadFail = ref(0);
// 删除之前触发
const beforeRemove = (uploadFile, uploadFiles) => {
    return proxy
        .$confirm(`是否确认删除${uploadFile.name}?`, '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            if (uploadSucceed.value >= 1) {
                uploadSucceed.value -= 1;
            }
            if (uploadFail.value >= 1) {
                uploadFail.value -= 1;
            }
            uploadDiv2.value = false;
        })
        .catch(() => {});
};
const uploadDiv = ref(false);
const uploadDiv2 = ref(false);
const failInfo = ref([]);
const infoValue = ref('');
const resultValue = ref('');
const itemcount = ref(0);
// 超出限制时触发
const handleExceed = (files, ref) => {
    ref.clearFiles();
    const file = files[0];
    file.uid = genFileId();
    ref.handleStart(file);
    ref.submit();
};
//文件上传成功
const handleUploadSuccess = (res, file, fileList, index, type) => {
    if (res.code == 200) {
        getdetailsList(detailsLisy);
        dialogVisible.value = false;
        var result = JSON.parse(res.data);
        fileList.value = [{ ...res.data }];
        uploadDiv.value = true;
        uploadDiv2.value = false;
        var info = [result.info.split(',').join(',')];
        failInfo.value.push(info);
        infoValue.value = failInfo.value[failInfo.value.length - 1].join(',');
        uploadSucceed.value = 0;
        uploadFail.value = 0;
        uploadSucceed.value += Number(result.successNum);
        uploadFail.value += Number(result.failNum);
        if (result.result == '失败') {
            resultValue.value = result.result;
            uploadDiv2.value = true;
            itemcount.value += 1;
        }
    } else {
        uploadDiv2.value = false;
        proxy.msgError(res.msg);
    }
};

// 下载上传模版
function DownloadUpload() {
    dataTemplateApi
        .importTemplate('', '', '', 'blob')
        .then((res) => {
            var debug = res;
            if (debug) {
                var elink = document.createElement('a');
                elink.download = '数据模版.xlsx';
                elink.style.display = 'none';
                var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                elink.href = URL.createObjectURL(blob);
                document.body.appendChild(elink);
                elink.click();
                document.body.removeChild(elink);
            } else {
                this.$message.error('导出异常请联系管理员');
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

// 模版详情列表
const calData = ref([]);
function getdetailsList(val) {
    dataTemplateApi
        .tempTempletDataList({
            'tempTemplet.id': val.id,
            current: templateDetails.value.current,
            size: templateDetails.value.size
        })
        .then((res) => {
            if (res.code == 200) {
                calData.value = res.data.records;
                templateDetails.value.total = res.data.total;
            }
        });
}

// 导出模版
const chooseList = ref([]);

const handleSelectionChange = (key) => {
    chooseList.value = key;
};
// 导出
function handleExport(val) {
    dataTemplateApi
        .tempTempletDataExport(
            {
                'tempTemplet.id': val.id
            },
            '',
            '',
            'blob'
        )
        .then((res) => {
            var debug = res;
            if (debug) {
                var elink = document.createElement('a');
                elink.download = '数据记录.xlsx';
                elink.style.display = 'none';
                var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                elink.href = URL.createObjectURL(blob);
                document.body.appendChild(elink);
                elink.click();
                document.body.removeChild(elink);
            } else {
                this.$message.error('导出异常请联系管理员');
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

const operateList = ref([]);
// 字典请求
const getDict = async () => {
    operateList.value = await proxy.getDictList('operate_type');
};
getDict();

// 查询设备列表数据
const incubatorList = ref([]);
function getList() {
    let data = {
        current: queryParams.value.current,
        size: queryParams.value.size,
        total: queryParams.value.total,
        name: queryParams.value.name,
        dataStartDate: queryParams.value.timeList ? queryParams.value.timeList[0] : null,
        dataEndDate: queryParams.value.timeList ? queryParams.value.timeList[1] : null
    };
    queryParams.value.timeList;

    dataTemplateApi.tempTempletList(data).then((res) => {
        if (res.code == 200) {
            incubatorList.value = res.data.records;
            queryParams.value.total = res.data.total;
        }
    });
}
function handleQuery() {
    getList();
}
getList();
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.deviceata {
    border: 2px solid #f5f7fa;
    padding: 20px;
    width: 100%;
}

.drawer-footer1 {
    position: absolute;
    bottom: 20px;
    left: 400px;
}
</style>
