import request from '@/utils/request';
export default {
    // 列表
    orderCostStats: function (params) {
        return request.get('/cost/order/costDetailed/orderCostStats', params);
    },
    // 导出
    paymentExport: function (params, config, resDetail, responseType) {
        return request.get('/cost/order/costDetailed/orderCostStats/export', params, config, resDetail, responseType);
    },
	// 客户名称下拉框
	customerNameList: function (params) {
	    return request.post('/cost/order/costDetailed/queryCompanySelect', params);
	},
	// 收货客户下拉框
	receivingCustomerList: function (params) {
	    return request.post('/cost/order/costDetailed/receiverCompanySelect', params);
	}
};
