<template>
    <div class="app-container">
        <el-dialog v-model="reviewVisible2" title="操作记录" width="50%" :before-close="beforeClose">
            <div style="max-height:800px;min-height: 500px; overflow-y:scroll" v-loading="loading">
                <LogQuery ref="childRef"/>
            </div>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button @click="beforeClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
 
<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, onMounted ,onBeforeMount} from 'vue'
import LogQuery from '@/components/detailsForm/logQuery.vue'
import supplier from '@/api/erp/supplier';
import { drugApi } from "@/api/model/commodity/drug/index";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const list = ref([])
const childRef = ref(null)
const props = defineProps({
    reviewVisible2: {
        type: Boolean,
        default: false
    },
    beforeClose: {
        type: Object,
        default: () => { }
    },
    data: {
        type: Object,
        default: () => { }
    }
})
const { reviewVisible2, beforeClose, data } = toRefs(props)
onMounted(
     async () => {
    // loading.value = true
    let reviewRes = await supplier.erpSupplierProductionApproval({ 'supplier.id': data.value.id })
    let resq = await drugApi.drugLog({ masterId: data.value.id })
    if ((reviewRes.code == 200 && reviewRes.data) || (resq.code == 200 && resq.data)) {
        childRef.value.timeFns(reviewRes.data?.records,resq.data?.records)
        }
        loading.value = false
    }
)
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    ::v-deep .el-form-item {
        width: 22%;
    }
}

::v-deep .labelStyle {
    .el-form-item__label {
        margin-left: 10px;
    }
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}
</style>