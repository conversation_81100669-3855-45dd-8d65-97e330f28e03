<template>
    <div v-loading="loading" class="app-container" element-loading-text="加载中...">
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="申请公司" prop="companyName" style="width: 230px">
                    <el-input v-model="queryForm.companyName" clearable placeholder="请输入申请公司" @change="handleQuery" />
                </el-form-item>
                <el-form-item label="审批状态" prop="status" style="width: 230px">
                    <el-select v-model="queryForm.status" clearable filterable placeholder="请选择审批状态" @change="handleQuery">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="时间" prop="queryDate" style="width: 310px">
                    <el-date-picker v-model="queryForm.queryDate" clearable end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"> </el-date-picker>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb-40">
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="InvoiceApproval" @queryTable="getList"></right-toolbar>
            </div>
            <column-table ref="InvoiceApproval" :columns="columns" :data="dataList" :maxHeight="tableHeight" show-summary>
                <template #company="{ row }">
                    {{ row.company?.name }}
                </template>
                <template #openInvoiceType="{ row }">
                    {{ formatDictionaryData('openInvoiceTypeList', row.openInvoiceType) }}
                </template>
                <template #invoiceType="{ row }">
                    <div class="flex items-center justify-center">
                        <span class="mr-5">{{ formatDictionaryData('invoiceTypeList', row.invoiceType) }}</span>
                        <el-button v-if="row?.invoiceResult == '1'" link size="small" type="primary" @click="onDownloadInvoice(row)">下载</el-button>
                        <span v-else-if="row?.invoiceResult == '0'" class="text-red-500">开票失败</span>
                    </div>
                </template>
                <template #businessType="{ row }">
                    <span :style="setBusinessTypeColor(row.businessType)">{{ formatDictionaryData('businessTypeList', row.businessType) }}</span>
                </template>
                <template #redFlag="{ row }">
                    <span :style="setRedFlagColor(row.redFlag)">{{ formatDictionaryData('redFlagList', row.redFlag) }}</span>
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ formatDictionaryData('statusList', row.status) }}</span>
                </template>
                <template #opt="{ row }">
                    <div>
                        <!-- 1-预存款充值 2-付款单支付 -->
                        <!-- 审批状态 status 0-待审批 1-审批通过 2-审批驳回 3-作废 -->
                        <el-button v-if="row.businessType === '2' && row.status === '0'" icon="el-icon-edit" link size="small" type="warning" @click="onOpenPaymentApproval('approval', row)">开票审批</el-button>
                        <el-button v-if="row.businessType === '1' && row.status === '0'" icon="el-icon-edit" link size="small" type="warning" @click="onOpenPrepaymentRechargeModification('approval', row)">开票审批</el-button>
                        <el-button v-if="row.status === '2'" icon="el-icon-delete" link size="small" type="danger" @click="onDelete(row)">撤销</el-button>
                        <el-button v-if="row.status === '1' && row?.invoiceResult == '1' && row?.redFlag === '1'" icon="el-icon-delete" link size="small" type="danger" @click="onOpenRedInvoice(row)">红冲</el-button>
                        <el-button v-if="row.businessType === '2'" icon="el-icon-info-filled" link size="small" type="primary" @click="onOpenPaymentApproval('detail', row)">查看详情</el-button>
                        <el-button v-if="row.businessType === '1'" icon="el-icon-info-filled" link size="small" type="primary" @click="onOpenPrepaymentRechargeModification('detail', row)">查看详情</el-button>
                    </div>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- / 付款单审批、修改	-->
        <payment-order-approval-invoicing-detail
            v-if="paymentOrderApprovalVisible"
            v-model:payment-order-approval-visible="paymentOrderApprovalVisible"
            :payment-order-approval-invoicingId="paymentOrderApprovalInvoicingId"
            :payment-order-approval-status="paymentOrderApprovalStatus"
            :payment-order-approval-title="paymentOrderApprovalTitle"
            @getList="getList"
        />

        <!--  / 预付款充值审批、修改  -->
        <prepayment-recharge-modification-invoicing-detail
            v-if="prepaymentRechargeModificationVisible"
            v-model:prepayment-recharge-modification-visible="prepaymentRechargeModificationVisible"
            :prepayment-recharge-modification-invoicingId="prepaymentRechargeModificationInvoicingId"
            :prepayment-recharge-modification-status="prepaymentRechargeModificationStatus"
            :prepayment-recharge-modification-title="prepaymentRechargeModificationTitle"
            @getList="getList"
        />
        <!-- 红冲 -->
        <invoice-red-invoicing-detail
            v-if="invoiceRedInvoicingVisible"
            v-model:invoice-red-invoicing-visible="invoiceRedInvoicingVisible"
            :invoice-apply-id="invoiceApplyId"
            :invoice-red-invoicing-source="invoiceRedInvoicingSource"
            :invoice-red-invoicing-title="invoiceRedInvoicingTitle"
            :invoice-red-invoicing-type="invoiceRedInvoicingType"
            @close="closeRedInvoice"
            @submit="submitRedInvoice"
        />
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import PaymentOrderApprovalInvoicingDetail from '@/views/carrierFunction/PaymentOrderApprovalInvoicingDetail.vue';
import PrepaymentRechargeModificationInvoicingDetail from '@/views/carrierFunction/PrepaymentRechargeModificationInvoicingDetail.vue';
import InvoiceApplication from '@/api/shipperEnd/InvoiceApplication';
import InvoiceRedInvoicingDetail from '@/views/carrierFunction/InvoiceRedInvoicingDetail.vue';
import InvoiceApproval from '@/api/carrierEnd/InvoiceApproval';

export default {
    name: 'InvoiceApproval',
    components: { PrepaymentRechargeModificationInvoicingDetail, PaymentOrderApprovalInvoicingDetail, ColumnTable, RightToolbar, SearchButton, InvoiceRedInvoicingDetail },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                queryDate: [],
                companyName: undefined,
                status: undefined,
                queryType: '2' // 1-货主 2-承运商
            },
            columns: [
                { title: '申请时间', key: 'applyTime', align: 'center', minWidth: '160px', columnShow: true },
                { title: '申请公司', key: 'companyName', align: 'center', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '发票抬头', key: 'invoiceHead', align: 'center', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'businessType', align: 'center', minWidth: '120px', columnShow: true },
                { title: '开票业务类型', key: 'openInvoiceType', align: 'center', minWidth: '120px', columnShow: true },
                { title: '开票金额（元）', key: 'invoiceAmount', align: 'center', minWidth: '120px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '审批状态', key: 'status', align: 'center', minWidth: '120px', columnShow: true },
                { title: '是否发起红冲', key: 'redFlag', align: 'center', minWidth: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '发票类型', key: 'invoiceType', align: 'center', minWidth: '120px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', minWidth: '250px', columnShow: true, fixed: 'right',showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            businessTypeList: [],
            redFlagList: [],
            statusList: [],
            paymentOrderApprovalVisible: false,
            paymentOrderApprovalInvoicingId: undefined,
            paymentOrderApprovalTitle: '付款单审批',
            paymentOrderApprovalStatus: undefined,
            prepaymentRechargeModificationVisible: false,
            prepaymentRechargeModificationInvoicingId: undefined,
            prepaymentRechargeModificationTitle: undefined,
            prepaymentRechargeModificationStatus: undefined,
            invoiceTypeList: [],
            tableHeight: 400,
            openInvoiceTypeList: [],
            invoiceRedInvoicingVisible: false,
            invoiceApplyId: undefined,
            invoiceRedInvoicingTitle: undefined,
            invoiceRedInvoicingType: undefined,
			invoiceRedInvoicingSource: undefined
		};
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置业务类型颜色
		 * 1:预存款充值 2:付款单支付 3:收款单收款
         */
        setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': { color: '#5cb85c' },
                        '3': { color: '#2196f3' }
                    }[businessType] || { color: '#999' }
                );
            };
        },
                /**
         * 设置红冲状态颜色
         */
         setRedFlagColor() {
            return (redFlag) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': { color: '#5cb85c' }
                    }[redFlag] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' },
                        '3': { color: '#999' }
                    }[status] || { color: '#999' }
                );
            };
        }
    },
    created() {
        this.getDict();
        this.queryForm['carrier.id'] = this.$TOOL.data.get('Organization')[0].id;
        this.handleQuery();
    },
    methods: {
        /**
         * 关闭红冲
         */
        closeRedInvoice() {
            this.invoiceRedInvoicingVisible = false;
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.businessTypeList = await this.getDictList('payment_type');
            this.statusList = await this.getDictList('payment_approval_type');
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
            this.openInvoiceTypeList = await this.getDictList('invoicing_business_type');
            this.redFlagList = await this.getDictList('invoice_red_flag');
        },
        /**
         * 获取列表
         */
        async getList() {
            this.loading = true;
            const { ...params } = this.queryForm;
            const res = await InvoiceApplication.getList(params);
            if (res.code === 200) {
                this.tableHeight = window.innerHeight - this.$refs.searchCard.$el.offsetHeight - 240;
                this.dataList = res.data.records;
                this.total = res.data.total;
            } else {
                this.dataList = [];
                this.total = 0;
            }
            this.loading = false;
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { queryDate } = this.queryForm;
            if (queryDate && queryDate.length) {
                this.queryForm.queryStartDate = queryDate[0] ? queryDate[0] + ' 00:00:00' : '';
                this.queryForm.queryEndDate = queryDate[1] ? queryDate[1] + ' 23:59:59' : '';
            } else {
                this.queryForm.queryStartDate = undefined;
                this.queryForm.queryEndDate = undefined;
            }
            this.getList();
        },
        /**
         * 删除
         * @param row
         */
        onDelete(row) {
            this.$confirm(`<div style="padding: 20px 0"><div style="color: red">确认撤销？</div><div>撤销后付款单将被全部释放为“未开票”状态!</div></div>`, '', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '确 认',
                cancelButtonText: '取 消',
                showClose: false,
                center: true,
                customClass: 'dialog-confirm',
                confirmButtonClass: 'el-button--large',
                cancelButtonClass: 'el-button--large'
            })
                .then(async () => {
                    try {
                        const res = await InvoiceApplication.delete({
                            businessType: row.businessType,
                            applyIds: row.id
                        });
                        if (res.code === 200) {
                            this.$message.success('撤销成功');
                            await this.getList();
                        } else {
                            this.$message.error(res.msg || '撤销失败');
                        }
                    } catch (err) {
                        this.$message.error('撤销失败');
                        console.error(err);
                    }
                })
                .catch(() => {});
        },
        /**
         * 下载发票
         * @param row
         */
        onDownloadInvoice(row) {
            if ('invoiceUrl' in row) {
                window.open(row.invoiceUrl);
            } else {
                this.$message.error('未获取到发票地址');
            }
        },
        /**
         * 打开付款单审批
         * @param status approval 审批 detail 查看
         * @param row
         * @return {Promise<void>}
         */
        onOpenPaymentApproval(status, row) {
            this.paymentOrderApprovalVisible = true;
            if (status === 'approval') {
                this.paymentOrderApprovalTitle = '付款单开票审批';
            } else if (status === 'detail') {
                this.paymentOrderApprovalTitle = '付款单开票详情';
            }
            this.paymentOrderApprovalStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.paymentOrderApprovalInvoicingId = row.id;
            }
        },
        /**
         * 打开预付款充值修改
         * @param status approval 审批 detail 查看
         * @param row
         */
        onOpenPrepaymentRechargeModification(status, row) {
            this.prepaymentRechargeModificationVisible = true;
            if (status === 'approval') {
                this.prepaymentRechargeModificationTitle = '审批';
            } else if (status === 'detail') {
                this.prepaymentRechargeModificationTitle = '查看';
            }
            this.prepaymentRechargeModificationStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.prepaymentRechargeModificationInvoicingId = row.id;
            }
        },
        /**
         * 红冲
         * @param row
         */
        onOpenRedInvoice(row) {
            this.invoiceRedInvoicingVisible = true;
            this.invoiceApplyId = row.id;
            this.invoiceRedInvoicingTitle = '红字发票申请';
            this.invoiceRedInvoicingType = row.businessType;
			this.invoiceRedInvoicingSource = 'carrier';
        },
        /**
         * 重置表单
         * @param formName
         */
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
        },
        /**
         * 重置查询条件
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /**
         * 提交红冲
         */
        submitRedInvoice(formData) {
            const params = {
                source: '2', // 1-货主发起 2-承运商发起
                companyId: formData.companyId,
                companyName: formData.companyName,
                invoiceApplyId: formData.invoiceApplyId,
                reason: formData.reason,
                detailDesc: formData.detailDesc,
                invoiceMoney: formData.invoiceMoney,
                redInvoiceAmount: formData.redInvoiceAmount
            };
            InvoiceApproval.submitInvoiceRedInvoicing(params).then(res => {
                if (res.code === 200) {
                    this.$message.success('红冲申请成功');
                    this.closeRedInvoice();
                    this.getList();
                } else {
                    this.$message.error(res.msg || '红冲申请失败');
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.number__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 10px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
