<template>
    <div class="app-container">
        <!--搜索项-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto">
                <el-form-item label="货主公司" prop="companyId">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="handleQuery">
                        <el-option v-for="(item, idx) in ownerList" :key="item.companyId" :label="item.companyName" :value="item.companyId">
                            <span style="float: left">{{ item.companyName }}</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.companyId }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item class="date-screening" label="出库时间" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryParams.queryTime" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!--数据-->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button icon="el-icon-plus" size="mini" type="primary" @click="addRecord">登记</el-button>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="WarehouseRecords" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="WarehouseRecords" v-loading="loading" :columns="columns" :data="recordList" :defaultSort="{ prop: 'outDate', order: 'descending' }">
                <template #company="{ row }">
                    <span>{{ row.company.name }}</span>
                </template>
                <template #signFile="{ row }">
                    <div v-if="row.signFile" class="img" @click="handlePictureCardPreview(row.signFile)">
                        <img :src="row.signFile" />
                    </div>
                </template>
                <template #outDate="{ row }">
                    <span>{{ row.outDate.substr(0, 10) }}</span>
                </template>
                <template #createBy="{ row }">
                    <span>{{ row.createBy.name }}</span>
                </template>

                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="openDetailRecordDrawer(row)">详情</el-button>
                    <el-button icon="el-icon-edit" link size="small" type="success" @click="editRecord(row)">修改</el-button>
                    <el-button icon="el-icon-close" link size="small" type="danger" @click="delRecord(row)">删除</el-button>
                </template>
            </column-table>

            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
        <!--  图片查看-->
        <el-image-viewer v-if="dialogVisible" :initial-index="0" :url-list="dialogImageUrl" :z-index="9999" @close="dialogVisible = false" />
        <el-drawer v-model="addRecordOpen" :title="recordTitle" direction="rtl" size="50%">
            <WarehouseOutSelfLifting v-if="addRecordOpen" :recordInfo="recordInfo" @callbackMethod="closeRecordDrawer"></WarehouseOutSelfLifting>
        </el-drawer>
        <el-drawer v-model="detailRecordOpen" direction="rtl" size="50%" title="登记详情">
            <WarehouseDetail v-if="detailRecordOpen" :recordInfo="recordInfo" @callbackMethod="closeDetailRecordDrawer"></WarehouseDetail>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import WarehouseDetail from './WarehouseDetail';
import WarehouseOutSelfLifting from './WarehouseOutSelfLifting';
import outboundManagement from '@/api/logisticsManagement/outboundManagement.js'; // 出库登记
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import SearchButton from '@/components/searchModule/SearchButton.vue'; // 合作配置
export default {
    name: 'WarehouseRecords',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar,
        WarehouseDetail,
        WarehouseOutSelfLifting
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                companyId: null,
                queryTime: []
            },
            columns: [
                { title: '货主公司', key: 'company', align: 'center', fixed: 'left', columnShow: true },
                { title: '出库日期', key: 'outDate', align: 'center', columnShow: true },
                { title: '交接人', key: 'createBy', align: 'center', columnShow: true },
                { title: '提货人签名', key: 'signFile', align: 'center', width: '100px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '220px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            recordList: [], // 数据
            dialogImageUrl: [],
            dialogVisible: false,
            // 货主列表
            ownerList: [],
            addRecordOpen: false, // 显示隐藏编辑界面
            recordTitle: '自提登记',
            recordInfo: {},
            detailRecordOpen: false
        };
    },
    created() {
        this.handleQuery();
        this.getCompanyCarrierSelect();
    },
    methods: {
        openDetailRecordDrawer(row) {
            this.recordInfo = row;
            this.detailRecordOpen = true;
        },
        closeDetailRecordDrawer() {
            this.detailRecordOpen = false;
        },
        // 查询所有货主信息
        getCompanyCarrierSelect() {
            // 获取 查询当前货主的承运商列表
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
        // 删除
        delRecord(row) {
            this.loading = true;
            let that = this;
            this.$confirm('确认删除已登记的出库自提信息吗？', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    outboundManagement
                        .deleteRecord({ recordId: row.id })
                        .then((response) => {
                            if (response.code == 200) {
                                that.msgSuccess('删除成功');
                                that.getList();
                            } else {
                                that.msgError('删除失败');
                            }
                            that.loading = false;
                        })
                        .catch((e) => {
                            that.loading = false;
                        });
                })
                .catch((e) => {
                    that.loading = false;
                });
        },
        // 添加自提
        addRecord() {
            this.recordInfo = {};
            this.recordTitle = '自提登记';
            this.addRecordOpen = true;
        },
        editRecord(row) {
            this.recordTitle = '修改';
            this.addRecordOpen = true;
            this.recordInfo = row;
        },
        // 关闭编辑弹窗
        closeRecordDrawer() {
            this.addRecordOpen = false;
            this.getList();
        },
        // 放大图片
        handlePictureCardPreview(imgUrl) {
            this.dialogImageUrl = [imgUrl];
            this.dialogVisible = true;
        },
        getList() {
            this.loading = true;
            this.recordList = [];
            outboundManagement
                .findOutboundList(this.queryParams)
                .then((response) => {
                    if (response.code === 200 && response.data && response.data.records) {
                        this.recordList = response.data.records || [];
                        this.total = response.data.total;
                    }
                    this.loading = false;
                })
                .catch((e) => {
                    this.loading = false;
                });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                this.queryParams.beginOutDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endOutDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginOutDate = null;
                this.queryParams.endOutDate = null;
            }
            if (this.queryParams.companyId) {
                this.queryParams['company.id'] = this.queryParams.companyId;
            } else {
                this.queryParams['company.id'] = null;
            }

            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.queryParams = {
                current: 1,
                size: 10,
                companyId: null,
                queryTime: []
            };
            this.resetForm('queryForm');
            this.handleQuery();
        }
    }
};
</script>

<style lang="scss" scoped>
.img {
    width: 40px;
    height: 40px;
    border: 1px dashed #d9d9d9;
    margin-top: 0;
    margin-left: 10px;

    img {
        width: 30px;
        height: 30px;
        margin: 5px;
    }
}
</style>
