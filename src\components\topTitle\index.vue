<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-30 14:59:29
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-07 09:39:06
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\components\topTitle\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <div v-if="!title" ref="soltRef"
         style="display:flex;align-items:center;flex-wrap: wrap;width: 100%;justify-content: end;">
      <slot></slot>
      <div style="margin-top: -15px;flex: 1;text-align: center;margin-right: 32px;">
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <el-button v-if="props.selFlag" :icon="`el-icon-arrow-${showSearch ? 'up' : 'down'}`" class="btn" link
                   type="primary"
                   @click="handleClick">{{
            !showSearch ? '展开' : "收起"
          }}
        </el-button>
      </div>
    </div>
    <div v-if="title" style="display:flex;justify-content:start;align-items:center;">
      <div>
        <slot></slot>
      </div>


    </div>
  </div>
</template>

<script setup>
import {reactive, ref, getCurrentInstance, toRefs, watch, defineProps} from 'vue'

const props = defineProps({
  handleQuery: {
    type: Function,
    default: () => {
    }
  },
  resetQuery: {
    type: Function,
    default: () => {
    }
  },
  title: {
    type: String,
    default: undefined
  },
  selFlag: {
    default: true
  }
})
const {handleQuery, resetQuery, title} = toRefs(props)
const soltRef = ref(null)
const row = ref(0)
const showSearch = ref(false)
watch(() => soltRef.value, (newValue, oldValue) => {
  if (soltRef.value.children.length) {
    showSearch.value = false
    for (let i = 0; i < soltRef.value.children.length; i++) {
      if (i > 2 && i < soltRef.value.children.length - 1) {
        soltRef.value.children[i].style.display = 'none'
      }
    }
    row.value = (soltRef.value.children?.length - 1) % 4
  }
}, {deep: true, immediate: true})
watch(() => showSearch.value, (newValue, oldValue) => {
  let btn = soltRef.value.children[soltRef.value.children.length - 1]
  if (showSearch.value) {
    if (soltRef.value.children.length) {
      for (let i = 0; i < soltRef.value.children.length; i++) {

        soltRef.value.children[i].style.display = 'flex'
        soltRef.value.children[i].style.flex = '1'
        btn.style.justifyContent = 'end'
        btn.style.margin = ' 0 32px 20px 0'
        if (row.value === 1) {
          btn.style.flex = '3'
          btn.style.paddingLeft = '64px'
        }
        if (row.value === 2) {
          btn.style.flex = '2'
          btn.style.paddingLeft = '32px'
        }
        // if (soltRef.value.children[i].role == 'group') {
        //   soltRef.value.children[i].children[1].innerHTML = `<div class="box_date">${soltRef.value.children[i].children[1].innerHTML}</div>`
        // }
      }
    }
  } else {
    if (soltRef.value.children.length) {
      for (let i = 0; i < soltRef.value.children.length; i++) {
        if (i > 2 && i < soltRef.value.children.length - 1) {
          soltRef.value.children[i].style.display = 'none'
          btn.style.width = 'auto'
          btn.style.margin = ' -15px 32px 0 0'
        }
        if (i == soltRef.value.children.length - 1) {
          btn.style.justifyContent = 'center'
        }
      }
    }
  }
})
const handleClick = () => {
  showSearch.value = !showSearch.value
}
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}

.box_date {
  width: 220px;
}

.btn {
  color: #2A76F8;
  font-size: 16px;
}</style>
