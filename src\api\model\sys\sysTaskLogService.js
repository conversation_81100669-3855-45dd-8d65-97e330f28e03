import http from "@/utils/request"

/*
 *@description: 系统任务日志
 *@author: 路正宁
 *@date: 2023-03-17 12:01:34
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/sys/taskLog/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/sys/taskLog/delete',
       {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
       '/sys/taskLog/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/sys/taskLog/list',
       params
    )
  },

  exportTemplate: function () {
    return http.get(
       '/sys/taskLog/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/sys/taskLog/export',
       params,
       'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
       '/sys/taskLog/import',
       data
    )
  }
}
