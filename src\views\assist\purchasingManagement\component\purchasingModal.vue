<template>
    <div class="app-container">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog :title="title" v-model="open" width="85%" :before-close="beforClose" :show-close="!loading"
            append-to-body destroy-on-close>
            <el-collapse v-model="activeNames" @change="handleChange" v-loading="loading">
                <el-collapse-item title="基本信息" name="1">
                    <template #title>
                        <span class="col_title">基本信息</span>
                    </template>
                    <BasicInformation :modalType="modalType" ref="basicInformationRef" v-if="open"
                        :essentialInformation="essentialInformation" />
                </el-collapse-item>
                <el-collapse-item title="细单信息" name="2">
                    <template #title>
                        <span class="col_title">细单信息</span>
                    </template>
                    <FineSingle :modalType="modalType" ref="fineSingleRef" :form="form" v-if="open"
                        :detailedOrderInformation="detailedOrderInformation" />
                </el-collapse-item>
                <el-collapse-item title="入库记录" name="3" v-if="modalType == 'detail'">
                    <template #title>
                        <span class="col_title">入库记录</span>
                    </template>
                    <WarehousingRecords v-if="open" ref="warehousingRecordsRef" :list="WarehousingList" />
                </el-collapse-item>
                <el-collapse-item title="合计信息" name="4">
                    <template #title>
                        <span class="col_title">合计信息</span>
                    </template>
                    <div class="total" v-if="open">
                        <p><span>单据数量：</span><span>{{ fineSingleRef.num.toFixed(0) }}</span></p>
                        <p><span>单据金额：</span><span>{{ fineSingleRef.totalCost.toFixed(2) }}</span></p>
                        <p><span>折后总金额：</span><span>{{ fineSingleRef.discountTotalCost.toFixed(2) }}</span></p>
                    </div>
                </el-collapse-item>
            </el-collapse>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button type="primary" @click="() => handleSubmit('1')"
                        v-if="modalType !== 'detail'">保存草稿</el-button>
                    <el-button type="primary" @click="() => handleSubmit('2')"
                        v-if="modalType !== 'detail'">提交下单</el-button>
                    <el-button @click="close">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import {defineProps, getCurrentInstance, onMounted, reactive, ref, toRefs, watch} from 'vue'
import BasicInformation from './basicInformation.vue';
import FineSingle from './fineSingle.vue';
import WarehousingRecords from './warehousingRecords.vue'
import purchasingManagement from '@/api/erp/purchasingManagement'
import moment from 'moment'

const { proxy } = getCurrentInstance();
const loading = ref(false);
const activeNames = ref(['1', '4'])
const fineSingleRef = ref(null)
const basicInformationRef = ref(null)
const form = ref({})
const detailData = ref(null)
const essentialInformation = ref({}) // 基本信息详情
const detailedOrderInformation = ref([]) // 细单信息
const WarehousingList = ref([])
const warehousingRecordsRef = ref(null)
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    },
    rules: {

    },
});

const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    beforClose: {
        type: Function,
        default: () => { }
    },
    title: {
        type: String,
        default: ''
    },
    modalType: {
        type: String,
        default: ""
    },
    getList: {
        type: Function,
        default: () => { }
    },
    detailRow: {
        type: Object,
        default: () => { { } }
    },
    closeNoTip: {
        type: Function,
        default: () => { }
    },
})

const { open, beforClose, title, modalType, getList, detailRow,closeNoTip } = toRefs(props)
const { queryParams, rules } = toRefs(data);
watch(() => basicInformationRef.value.form, (newValue, oldValue) => {
    form.value = newValue
}, { deep: true })
/**
 * @description: 获取订单详情
 * @return {*}
 */
const getDetail = async () => {
    loading.value = true
    essentialInformation.value = {}
    try {
        let res = await purchasingManagement.getIdOrder({ id: detailRow.value?.id })
        if (res.code == 200) {
            detailData.value = res.data
            essentialInformation.value = {
                supplier: res.data?.erpPurchaseOrderDTO?.supplier?.id,
                supplierRepresentative: res.data?.erpPurchaseOrderDTO?.supplierRepresentative?.id,
                settlementMethod: res.data?.erpPurchaseOrderDTO?.settlementMethod,
                termsPayment: res.data?.erpPurchaseOrderDTO?.termsPayment,
                invoiceType: res.data?.erpPurchaseOrderDTO?.invoiceType,
                logisticsMode: res.data?.erpPurchaseOrderDTO?.logisticsMode,
                warehouseNumber: res.data?.erpPurchaseOrderDTO?.warehouseNumber?.id,
                handledBy: res.data?.erpPurchaseOrderDTO?.handledBy?.id,
                preparedBy: res.data?.erpPurchaseOrderDTO?.preparedBy?.name,
                preparedBys: res.data?.erpPurchaseOrderDTO?.preparedBy?.id,
                selfRate: res.data?.erpPurchaseOrderDTO?.selfRate,
                remark: res.data?.erpPurchaseOrderDTO?.remark,
                discountAmount: res.data?.erpPurchaseOrderDTO?.discountAmount,
                isGenerateContract: res.data?.erpPurchaseOrderDTO?.isGenerateContract,
                id: res.data?.erpPurchaseOrderDTO?.id
            }
            detailedOrderInformation.value = res.data?.orderFormDTOS
        }
        if (modalType.value == 'detail') {
            let WarehousingRecords = await purchasingManagement.erpPurchaseWarehousingRecord({ orderCode: detailRow?.orderCode })
            if (WarehousingRecords.code == 200) {
                WarehousingList.value = WarehousingRecords.data?.records
            }
        }

        loading.value = false
    } catch (error) {
        proxy.msgError(error)
    } finally {
        loading.value = false
    }



}

const close = () => {
    // essentialInformation.value = {}
    // detailedOrderInformation.value = []
    // WarehousingList.value = []
    beforClose.value()


}
const handleSubmit = (type) => {
    basicInformationRef.value.rulesSubmit()
    setTimeout(() => {
      if (!basicInformationRef.value.isRulesPass) {
        if (activeNames.value.indexOf('1') == -1) {
          activeNames.value.push('1')
        }
      }
        if (!basicInformationRef.value.isRulesPass) return
        if (!fineSingleRef.value.list?.length && type == '2') {
            return proxy.msgError("至少需要一条细单信息！")
        }
        fineSingleRef.value.listReg()
        if(fineSingleRef.value.listRegIndex !== fineSingleRef.value?.list?.length) return
        const form = JSON.parse(JSON.stringify(basicInformationRef.value.form))
        const list = JSON.parse(JSON.stringify(fineSingleRef.value.list))
        const orderFormDTOS = []
        const erpPurchaseOrderDTO = {
            id: form?.id,
            supplier: {
                id: form?.supplier
            },
            supplierRepresentative: {
                id: form?.supplierRepresentative
            },
            settlementMethod: form?.settlementMethod,
            termsPayment: moment(form.termsPayment)?.format("YYYY-MM-DD"),
            invoiceType: form?.invoiceType,
            logisticsMode: form?.logisticsMode,
            warehouseNumber: {
                id: form?.warehouseNumber
            },
            handledBy: {
                id: form?.handledBy
            },
            preparedBy: {
                id: form?.preparedBys
            },
            selfRate: form?.selfRate,
            remark: form?.remark,
            discountAmount: form?.discountAmount,
            isGenerateContract: form?.isGenerateContract ? '1' : '0',
            totalAmountAfterDiscount: fineSingleRef.value?.discountTotalCost.toFixed(2),
            totalAmount: fineSingleRef.value?.totalCost ? fineSingleRef.value?.totalCost.toFixed(2) : '0.00',
            totalQuantity: fineSingleRef.value?.num ? fineSingleRef.value?.num.toFixed(0) : 0
        }
        list.forEach(item => {
            const paramsObj = {
                id: item.orderFormId,
                commodity: {
                    id: item?.commodityId
                },
                quantity: String(item?.quantity),
                unitPrice: Number(item?.unitPrice)?.toFixed(2) ?? '0.00',
                amountMoney: item?.amountMoney,
                arrivalQuantity: item?.arrivalQuantity,
                receivingQuantity: item?.receivingQuantity,
                manufacturer: {
                    id: item?.manufacture?.id
                },
                taxRate: item?.taxRate,
                unitLoading: item?.ratio,
                pieceNumber: ((item.quantity ? Number(item.quantity || 0) : 0) / (item.ratio ? Number(item.ratio || 0) : 1)).toFixed(0),
                basicUnit: item?.basicUnit,
                dosageForm: item?.dosageForm,
                inventoryBalance: item?.availableInventory,
                lastPurchasePrice: item?.lastDifferencePrice,
                lastSupplier: item?.lastSupplier,
                lastSupplierId: item?.lastSupplierId
            }
            orderFormDTOS.push(paramsObj)
        })
        const params = {
            erpPurchaseOrderDTO,
            orderFormDTOS,
            operate: type == '1' ? 'save' : 'submit'
        }
        loading.value = true
        purchasingManagement.save(params).then(res => {
            if (res.code == 200) {
                open.value = false
                loading.value = false
                getList.value()
                proxy.msgSuccess(type == '1' ? '保存成功' : '提交成功')
                closeNoTip.value()
                essentialInformation.value = {}
                detailedOrderInformation.value = []
                WarehousingList.value = []
            } else {
                proxy.msgError(res.msg)
            }
        }).finally(() => {
            loading.value = false
        })
    }, 0);
}

async function dict() {
    // reviewStatus.value = await proxy.getDictList('erp_review_status')
}
onMounted(() => {
    dict()
    if (modalType.value == 'edit') {
        getDetail()
    }

})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.total {
    display: flex;
    margin: 10px 20px;

    p {
        margin-right: 50px;

        & span:nth-of-type(1) {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        & span:nth-of-type(2) {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}
</style>
