<template>
    <div class="app-container">
        <!-- 审核主流程配置 -->
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>组织审核主流程配置</span>
                    <div>
                        <el-button circle icon="el-icon-refresh" size="mini" @click="getFlowData" />
                    </div>
                </div>
            </template>
            <div>
                <el-table v-loading="loading1" :data="flowList" highlight-current-row border style="width: 100%; margin-bottom: 12px" @row-click="(row) => details(row)">
                    <el-table-column label="流程名称" prop="flow.name" />
                    <el-table-column label="对应表单名称" prop="formName" />
                    <el-table-column label="组织" prop="org.name" />
                </el-table>
            </div>
            <pagination v-show="flowData.total > 0" v-model:limit="flowData.pageSize" v-model:page="flowData.pageNum" :total="flowData.total" @pagination="getFlowData" />
        </el-card>
        <!-- 审核节点配置 -->
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>审核节点配置</span>
                    <el-button v-hasPermi="['audit:orgFlow:node:add']" v-show="newNodeShow && nodeList.length < 9" class="button" type="primary" size="mini" @click="addNode()">新增 </el-button>
                </div>
            </template>
            <div>
                <el-table v-loading="loading2" :cell-style="{ textAlign: 'left' }" :data="nodeList" border style="width: 100%; margin-bottom: 12px">
                    <el-table-column label="节点名称" prop="nodeName" />
                    <el-table-column label="节点类型" prop="nodeType" :formatter="getNodeTypeFormat" />
                    <el-table-column label="节点步骤" prop="sort" />
                    <el-table-column label="审核角色" prop="roleNames" />
                    <el-table-column label="发起机构类型" prop="commitType" :formatter="getOrgTypeFormat" />
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button v-hasPermi="['audit:orgFlow:node:edit']" plain size="mini" style="padding: 5px 5px" text type="primary" @click="editSteps(scope.row)">编辑 </el-button>
                            <el-button plain size="mini" style="padding: 5px 5px" text type="danger" @click="delStep(scope.row)">删除 </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-card>

        <!-- 对话框Form表单 新增节点配置-->
        <el-dialog v-model="formVisible" title="节点配置" width="500px">
            <el-form ref="baseForm" :label-position="labelPosition" :model="form" :rules="rules" label-width="120px" style="max-width: 90%; margin-top: 10px">
                <el-form-item label="所属主流程">
                    <b>{{ flowInfo.name }}</b>
                </el-form-item>
                <el-form-item label="节点名称" prop="nodeName">
                    <el-input v-model="form.nodeName" placeholder="请输入节点名称" maxlength="60" show-word-limit />
                </el-form-item>
                <el-form-item label="节点类型" prop="nodeType">
                    <el-select v-model="form.nodeType" :disabled="form.id" :reserve-keyword="false" allow-create default-first-option filterable placeholder="请选择节点类型" size="default" style="width: 100%">
                        <el-option v-for="(item, index) in nodeTypeOptions" :key="index" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
<!--                <el-form-item label="节点步骤" prop="sort" v-if="form.id">-->
<!--                    <el-input v-model="form.sort" placeholder="请输入节点步骤" :min="1" maxlength="1" show-word-limit />-->
<!--                </el-form-item>-->
                <el-form-item label="发起机构类型" prop="commitType" v-if="form.nodeType == '1'">
                    <el-select v-model="form.commitType" :reserve-keyword="false" allow-create default-first-option filterable placeholder="请选择发起机构类型" size="default" style="width: 100%">
                        <el-option v-for="(item, index) in orgTypeOptions" :key="index" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="审核角色" prop="roleIds" v-if="form.nodeType == '2'">
                    <el-select v-model="form.roleIds" :reserve-keyword="false" allow-create default-first-option filterable multiple placeholder="请选择审核角色" size="default" style="width: 100%">
                        <el-option v-for="(item, index) in listOfRoles" :key="index" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="formVisible = false">取消</el-button>
                    <el-button type="primary" @click="stepsBtn()"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { flows, node } from '@/api/auditManagement/auditAndFlow.js';
export default {
    name: 'CarrierAuditAndFlow',
    data() {
        return {
            listOfRoles: [], // 角色列表
            orgTypeOptions: [], // 机构类型
            nodeTypeOptions: [], // 节点类型
            formVisible: false,
            labelPosition: 'right',
            loading1: false,
            loading2: false,
            flowList: [], // 审核流程列表
            flowData: {
                pageSize: 4, //每页条数
                pageNum: 1, //流程 当前页码
                total: 0 //总数据数量
            },
            nodeList: [], // 审核节点列表
            form: {
                // 新增节点配置
                orgId: undefined,
                nodeType: undefined,
                flowId: undefined,
                orgFlowId: undefined,
                formId: undefined,
                formName: undefined,
                nodeId: undefined,
                nodeName: undefined,
                commitType: undefined,
                roleIds: []
            },
            // 验证表单
            rules: {
                // 节点
                nodeType: [{ required: true, message: '请选择节点类型', trigger: 'blur' }],
                nodeName: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
                roleIds: [{ required: true, message: '请选择审核角色', type: 'array', trigger: 'blur' }],
                // sort: [
                //     { required: true, message: '请输入节点步骤', trigger: 'blur' },
                //     { pattern: /^(\d)+$/, message: '节点步骤只能输入正整数', trigger: 'blur' }
                // ],
                commitType: [{ required: true, message: '请选择发起机构类型', trigger: 'blur' }]
            },
            newNodeShow: false, // 显示隐藏新增节点按钮
            flowInfo: {} // 流程信息
        };
    },
    methods: {
        /**
         * 获取节点类型字典
         * @param row
         * @returns {*}
         */
        getNodeTypeFormat(row) {
            return this.selectDictLabel(this.nodeTypeOptions, row.nodeType);
        },
        /**
         * 获取机构类型字典
         * @param row
         */
        getOrgTypeFormat(row) {
            return this.selectDictLabel(this.orgTypeOptions, row.commitType);
        },
        /**
         * 获取角色列表
         * @param orgId 组织机构id
         */
        async getRoleList(orgId) {
            this.listOfRoles = [];
            const res = await flows.getroleList({ orgId });
            if (res.code == 200) {
                this.listOfRoles = res.data || [];
            }
        },
        /**
         * 删除节点配置
         */
        delStep(row) {
            let that = this;
            this.$confirm('确认删除此节点配置?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    node.delNode({ ids: row.id }).then((res) => {
                        if (res.code == 200) {
                            that.msgSuccess('删除成功');
                            that.details(that.flowInfo);
                        } else {
                            that.msgError(res.msg);
                        }
                    });
                })
                .catch((e) => {});
        },
        /**
         * 编辑节点
         */
        async editSteps(row) {
            node.queryById({ id: row.id }).then(async (res) => {
                if (res.code == 200) {
                    this.form = { ...res.data };
                    this.formVisible = true;
                    if (this.form.nodeType == '2') {
                        await this.getRoleList(this.form.org.id);
                    }
                } else {
                    this.msgError(res.msg);
                }
            });
        },
        /**
         * 获取节点详情
         */
        details(row) {
            this.flowInfo = { ...row };
            this.flowInfo.name = row.flow.name;
            this.flowInfo.auditProcessFormId = row.id;
            this.loading2 = true;
            this.nodeList = [];

            node.nodeList({
                'orgFlowId': row.id
            })
                .then((res) => {
                    this.nodeList = res.data;
                })
                .finally(() => {
                    this.newNodeShow = true;
                    this.loading2 = false;
                });
        },
        /**
         * 新增节点
         * @returns {Promise<void>}
         */
        async addNode() {
            this.formVisible = true;
            this.reset();
            await this.getRoleList(this.flowInfo.org.id);
        },
        reset() {
            this.form = {
                // 新增节点配置
                orgId: undefined,
                nodeType: undefined,
                flowId: undefined,
                orgFlowId: undefined,
                formId: undefined,
                formName: undefined,
                nodeId: undefined,
                nodeName: undefined,
                commitType: undefined,
                roleIds: []
            };
        },
        /**
         * 保存节点提交
         */
        async stepsBtn() {
            await this.$refs['baseForm'].validate((valid) => {
                if (valid) {
                    const params = {
                        orgId: this.flowInfo.org.id,
                        nodeType: this.form.nodeType,
                        flowId: this.flowInfo.flow.id,
                        orgFlowId: this.flowInfo.id,
                        formId: this.flowInfo.formId,
                        formName: this.flowInfo.formName,
                        nodeName: this.form.nodeName,
                        commitType: this.form.commitType,
                        roleIds: this.form.roleIds
                    };
                    if (this.form.id) params.nodeId = this.form.id;
                    if (this.form.sort) params.sort = this.form.sort;
                    node.saveNode(params)
                        .then((res) => {
                            if (res.code == 200) {
                                this.details(this.flowInfo);
                                this.msgSuccess(this.form.id ? '编辑成功' : '添加成功');
                                this.form = {
                                    users: []
                                };
                                this.formVisible = false;
                            }
                        })
                        .catch(() => {
                            this.msgError('添加失败，请稍后重试');
                        });
                }
            });
        },
        /**
         * 获取流程列表数据
         */
        getFlowData() {
            this.loading1 = true;
            this.flowInfo = {};
            this.nodeList = [];
            this.flowList = [];
            flows
                .getOrgFlowList({
                    current: this.flowData.pageNum,
                    size: this.flowData.pageSize,
                    ...this.queryParams
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.flowList = res.data.records;
                        this.flowData.total = res.data.total;
                    }
                })
                .catch((e) => {
					this.flowData.total = 0;
				})
                .finally(() => {
                    this.loading1 = false;
                });
        }
    },
    async created() {
        this.getFlowData();
        /*流程表单*/
        this.formTypes = await this.getDictList('audit_form');
        // 机构类型
        this.orgTypeOptions = await this.getDictList('fourpl_org_type');
        // 节点类型
        this.nodeTypeOptions = await this.getDictList('fourpl_node_type');
    }
};
</script>

<style scoped lang="scss">
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
