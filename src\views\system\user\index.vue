<template>
  <el-container>
    <el-aside v-if="!userDataSource" width="300px">
      <el-container v-loading="leftListLoading">
        <el-header>
          <el-input
              v-model="leftName"
              clearable @keyup.enter="getleftDataList" @clear="getleftDataList"
              placeholder="输入关键字进行过滤"
          ></el-input>
        </el-header>
        <el-main class="nopadding">
          <ytzhTable
              ref="dataTable"
              :data="leftDataList"
              :pageChangeHandle="getleftDataList"
              :refreshDataListHandle="getleftDataList"
              :tablePage="leftTablePage"
              :treeLoadHandle="leftTreeLoadHandle"
              hideDo="false"
              highlight-current-row
              paginationLayout="total, sizes, prev, pager, next"
              row-key="id"
              size="small"
              stripe
              style="cursor: pointer"
              @current-change="leftHandleCurrentChange"
          >
            <el-table-column label="机构名称" prop="name"></el-table-column>
            <el-table-column label="是否启用" prop="enable" width="80">
              <template #default="scope">
                <el-tag v-if="scope.row.enable == true" type="success">启用</el-tag>
                <el-tag v-if="scope.row.enable == false">停用</el-tag>
              </template>
            </el-table-column>
          </ytzhTable>
        </el-main>
      </el-container>
    </el-aside>
    <el-container v-loading="listLoading" class="is-vertical">
      <el-header>
        <div class="left-panel">
          <el-button icon="el-icon-d-arrow-left" @click="switchDataSource">{{ switchBtnName }}</el-button>
          <el-button icon="el-icon-plus" style="font-size: 18px" type="primary" @click="addForm"></el-button>
          <el-button
              :disabled="selection.length == 0"
              icon="el-icon-delete"
              plain
              style="font-size: 18px"
              type="danger"
              @click="deleteDatas"
          ></el-button>
          <el-tooltip class="item" content="同步用户数据" effect="dark" placement="top">
            <el-button icon="el-icon-download" style="font-size: 18px" type="primary" @click="userSync"></el-button>
          </el-tooltip>
          <el-input
              v-model="rightParams.name" style="margin-left: 20px;"
              clearable @keyup.enter="getUserDataList" @clear="getUserDataList"
              placeholder="输入用户名称进行过滤"
          ></el-input>
          <el-input
              v-model="rightParams.phone" style="margin-left: 20px;"
              clearable @keyup.enter="getUserDataList" @clear="getUserDataList"
              placeholder="输入手机号码进行过滤"
          ></el-input>
        </div>
      </el-header>
      <el-main class="nopadding">
        <ytzhTable
            ref="dataTable"
            :data="dataList"
            :pageChangeHandle="getDataList"
            :refreshDataListHandle="getDataList"
            :tablePage="tablePage"
            :type="expand"
            row-key="id"
            stripe
            @selection-change="selectionChange"
        >
          <el-table-column type="selection"></el-table-column>
          <!--          <el-table-column-->
          <!--            label="组织机构"-->
          <!--            prop="sysOrg.name"-->
          <!--            width="150"-->
          <!--            :show-overflow-tooltip="true"-->
          <!--          ></el-table-column>-->
          <!--          <el-table-column label="归属部门" prop="sysOffice.name"></el-table-column>-->
          <el-table-column label="姓名" prop="name" minWidth="150"></el-table-column>
          <el-table-column label="登录名" prop="loginName" minWidth="150"></el-table-column>
          <el-table-column label="手机" prop="phone" width="160"></el-table-column>

          <el-table-column label="是否可登录" prop="loginFlag" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.loginFlag == true" type="success">是</el-tag>
              <el-tag v-if="scope.row.loginFlag == false">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否是管理员" prop="admin" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.admin == true" type="success">是</el-tag>
              <el-tag v-if="scope.row.admin == false">否</el-tag>
            </template>
          </el-table-column>

          <el-table-column align="right" fixed="right" label="操作" width="400">
            <template #default="scope">
              <el-button plain size="small" @click="viewForm(scope.row)">查看</el-button>
              <el-button plain size="small" type="primary" @click="editForm(scope.row)"
              >编辑
              </el-button
              >
              <el-button plain size="small" @click="openSysOrgDialog(scope.row)"
              >机构配置
              </el-button
              >
              <el-button plain size="small" type="warning" @click="roleConfig(scope.row)"
              >角色配置
              </el-button
              >
              <el-popconfirm
                  title="确定删除吗？"
                  @confirm="deleteData(scope.row, scope.$index)"
              >
                <template #reference>
                  <el-button plain size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </ytzhTable>
      </el-main>
    </el-container>
  </el-container>
  <form-dialog
      v-if="formDiaogDisplay"
      ref="formDialog"
      :callback="formCallback"
      @closed="formDiaogDisplay = false"
      @success="handleSaveSuccess"
  ></form-dialog>
  <sysRoleSelect
      v-if="sysRoleSelectDialog"
      ref="sysRoleSelected"
      :isMultiple="true"
      :selectChange="roleSelectChange"
      draggable
      @closed="sysRoleSelectDialog = false"
  ></sysRoleSelect>
  <sysOrgSelect
      v-if="sysOrgSelectDialog"
      ref="sysOrgSelected"
      :isMultiple="true"
      :selectChange="orgSelectChange"
      draggable
      @closed="sysOrgSelectDialog = false"
  ></sysOrgSelect>
</template>

<script>
import formDialog from "./form";

export default {
  components: {
    formDialog,
  },
  data() {
    return {
      //数据列表
      leftDataList: {},
      //分页参数
      leftTablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
      },
      //查询表单
      leftSearchForm: {},
      rightParams:{},
      //列表加载
      leftListLoading: false,
      //选中的组织机构数据
      leftSelectData: {},
      //////////////////////////////////////////////////////////////////////////////////////
      //数据列表
      dataList: {},
      //分页参数
      tablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
        //排序
        //orders: [{ column: "createDate", asc: false }],
      },
      //查询表单
      searchForm: {
        name: "",
        url: "",
      },
      leftName:'',
      //数据列选中行
      selection: [],
      //列表加载
      listLoading: false,
      //表单显示隐藏
      formDiaogDisplay: false,
      //角色配置弹框
      sysRoleSelectDialog: false,
      //组织机构弹框
      sysOrgSelectDialog: false,
      userDataSource: false,
      switchBtnName: "查询全部用户"
    };
  },
  mounted() {
    //刷新数据列表
    this.getleftDataList();
  },
  methods: {
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getleftDataList() {
      //初始化数据列表
      this.leftDataList = [];
      //请求接口
      var res = await this.getReqeustList({name:this.leftName});
      if (res.code == 200) {
        //总数据条数
        this.leftTablePage.total = res.data.total;
        //数据列表
        this.leftDataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
    },
    /*
     * 列表请求接口
     * @author: 路正宁
     * @date: 2023-03-30 11:20:01
     */
    async getReqeustList(form = {}, pageSize = this.leftTablePage.pageSize) {
      //页面加载
      this.leftListLoading = true;
      var res = await this.$API.sysOrgService.list({
        //当前页码
        current: this.leftTablePage.currentPage,
        //每页条数
        size: pageSize,
        //排序查询
        orders: this.leftTablePage.orders,
        //查询参数
        ...form,
      });
      this.leftListLoading = false;
      return res;
    },
    /*
     * 表格选择后回调事件，单选
     * @author: 路正宁
     * @date: 2023-03-31 17:38:42
     */
    leftHandleCurrentChange(val) {
      this.leftSelectData = val;
      this.getDataList();
    },
    /*
     * 头部搜索框
     * @author: 路正宁
     * @date: 2023-03-24 14:58:47
     */
    leftSearch() {
      this.getleftDataList();
    },
    /*
     * 树表格点击展开
     * @author: 路正宁
     * @date: 2023-03-30 11:16:33
     */
    async leftTreeLoadHandle(tree, treeNode, resolve) {
      //查询当前节点下的子节点
      var form = {
        "parent.id": tree.id,
      };
      //请求接口
      var res = await this.getReqeustList(form, 100);
      if (res.code == 200) {
        if (res.data.records == null || res.data.records.length == 0) {
          this.$message.warning("无数据");
        } else {
          //同步本地
          tree.children = res.data.records;
        }
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      resolve(res.data.records);
    },
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      if (this.userDataSource) {
        this.getUserDataList();
      } else {
        this.getOrgUserDataList()
      }
    },
    async getUserDataList() {
      //页面加载
      this.listLoading = true;
      this.dataList = null;
      //查询条件处理
      if (this.$ObjectUtils.isNotEmpty(this.leftSelectData)) {
        this.searchForm = {
          "sysOrg.id": this.leftSelectData.id,
        };
      }
      var res = await this.$API.sysUserService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: this.tablePage.pageSize,
        //排序查询
        orders: this.tablePage.orders,
        ...this.searchForm,
        ...this.rightParams,
      });
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        //数据列表
        this.dataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      this.listLoading = false;
    },
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getOrgUserDataList() {
      //页面加载
      this.listLoading = true;
      this.dataList = null;
      //查询条件处理
      if (this.$ObjectUtils.isNotEmpty(this.leftSelectData)) {
        this.searchForm = {
          "org.id": this.leftSelectData.id,
        };
      }
      var res = await this.$API.sysOrgUserService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: this.tablePage.pageSize,
        //排序查询
        orders: this.tablePage.orders,
        ...this.searchForm,
      });
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        this.dataList = [];
        for (var i = 0; i < res.data.records.length; i++) {
          //取出角色对象，添加到角色列表中
          this.dataList.push(res.data.records[i].user);
        }
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      this.listLoading = false;
    },
    /*
     * 添加数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:27
     */
    addForm() {
      if (this.$ObjectUtils.isEmpty(this.leftSelectData)) {
        this.$message.warning("请选择左侧组织机构");
        return;
      }
      this.formDiaogDisplay = true;
      this.$nextTick(() => {
        this.$refs.formDialog.addView(this.leftSelectData);
      });
    },
    /*
     * 编辑数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:41
     */
    editForm(row) {
      this.formDiaogDisplay = true;
      this.$nextTick(() => {
        this.$refs.formDialog.editView(row);
      });
    },
    /*
     * 查看数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:55
     */
    viewForm(row) {
      this.formDiaogDisplay = true;
      this.$nextTick(() => {
        this.$refs.formDialog.view(row);
      });
    },
    /*
     * 删除数据，行内删除
     * @author: 路正宁
     * @date: 2023-03-24 14:35:00
     */
    async deleteData(row, index) {
      this.listLoading = true;
      var res = await this.$API.sysUserService.delete(row.id);
      if (res.code == 200) {
        this.$refs.dataTable.removeIndex(index);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      this.listLoading = false;
    },
    async userSync() {
      this.listLoading = true;
      var res = await this.$API.sysUserService.userSync();
      if (res.code == 200) {
        this.$message.success("同步成功");
        this.getleftDataList();
      }
      this.listLoading = false;
    },
    /*
     * 批量删除
     * @author: 路正宁
     * @date: 2023-03-24 14:36:11
     */
    async deleteDatas() {
      //确认删除弹框
      var confirmRes = await this.$confirm(
          `确定删除选中的 ${this.selection.length} 项吗？`,
          "提示",
          {
            type: "warning",
            confirmButtonText: "删除",
            confirmButtonClass: "el-button--danger",
          }
      ).catch(() => {
      });
      //确认结果处理
      if (!confirmRes) {
        return false;
      }
      //要删除的id数组
      var ids = this.selection.map((v) => v.id);
      //拼接的数组字符串，接口传参
      var idStr = this.selection.map((v) => v.id).join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysUserService.delete(idStr);
      if (res.code == 200) {
        //从列表中移除已删除的数据
        this.$refs.dataTable.removeKeys(ids);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      //释放页面加载中
      this.listLoading = false;
    },
    /*
     * 表格选择后回调事件
     * @author: 路正宁
     * @date: 2023-03-24 14:51:09
     */
    selectionChange(selection) {
      this.selection = selection;
    },
    /*
     * 数据表单回调函数，表单提交成功以后会调用此方法
     * 为了减少网络请求，直接变更表格内存数据
     * @author: 路正宁
     * @date: 2023-03-24 14:57:49
     */
    formCallback(data, mode) {
      if (mode == "add") {
        this.$refs.dataTable.unshiftRow(data);
      } else if (mode == "edit") {
        this.$refs.dataTable.updateKey(data);
      }
    },
    /*
     * 头部搜索框
     * @author: 路正宁
     * @date: 2023-03-24 14:58:47
     */
    search() {
      this.getDataList();
    },
    /*
     * 用户角色配置
     * @author: 路正宁
     * @date: 2023-04-06 10:06:26
     */
    async roleConfig(row) {
      var roleList = [];
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysUserRoleService.list({
        "sysUser.id": row.id,
        //当前页码
        current: 1,
        //每页条数
        size: 1000,
      });
      if (res.code == 200) {
        for (var i = 0; i < res.data.records.length; i++) {
          //取出角色对象，添加到角色列表中
          roleList.push(res.data.records[i].sysRole);
        }
      } else {
        this.$Response.errorNotice(res, "角色查询失败");
        //释放页面加载中
        this.listLoading = false;
        return;
      }
      //显示角色选择框
      this.sysRoleSelectDialog = true;
      this.$nextTick(() => {
        this.$refs.sysRoleSelected.init(row, roleList);
      });
      //释放页面加载中
      this.listLoading = false;
    },
    /*
     * 角色选择弹框提交回调事件
     * @author: 路正宁
     * @date: 2023-04-06 10:29:53
     */
    async roleSelectChange(user, roleList) {
      var roleId = [];
      for (var i = 0; i < roleList.length; i++) {
        roleId[i] = roleList[i].id;
      }
      //角色ID字符串
      var roleIdStr = roleId.join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysUserRoleService.configUserRole(user.id, roleIdStr);
      if (res.code == 200) {
        this.$message.success("配置成功");
      } else {
        this.$Response.errorNotice(res, "角色查询失败");
      }
      //释放页面加载中
      this.listLoading = false;
    },
    /*
      * 打开组织机构选择框
      * @author: 路正宁
      * @date: 2023-04-03 10:27:55
      */
    async openSysOrgDialog(row) {
      //页面加载中
      this.listLoading = true;
      var datas = [];
      var res = await this.$API.sysOrgUserService.list({
        "user.id": row.id,
        //当前页码
        current: 1,
        //每页条数
        size: 1000,
      });
      //页面加载中
      this.listLoading = false;
      if (res.code == 200) {
        for (var i = 0; i < res.data.records.length; i++) {
          //取出角色对象，添加到角色列表中
          datas.push(res.data.records[i].org);
        }
      } else {
        this.$Response.errorNotice(res, "组织机构查询失败");
        return;
      }
      this.sysOrgSelectDialog = true;
      this.$nextTick(() => {
        this.$refs.sysOrgSelected.selecteds(datas, row);
      });
    },
    /*
     * 组织机构弹框回调选择事件
     * @author: 路正宁
     * @date: 2023-04-03 10:02:56
     */
    async orgSelectChange(orgList, user) {
      var orgId = [];
      for (var i = 0; i < orgList.length; i++) {
        orgId[i] = orgList[i].id;
      }
      //组织机构ID字符串
      var orgIdStr = orgId.join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysOrgUserService.configOrgUser(user.id, orgIdStr);
      if (res.code == 200) {
        this.$message.success("配置成功");
      } else {
        this.$Response.errorNotice(res, "配置失败");
      }
      //页面加载中
      this.listLoading = false;
    },
    switchDataSource() {
      if (this.userDataSource) {
        this.userDataSource = false;
        this.switchBtnName = "查询全部用户";
      } else {
        this.userDataSource = true;
        this.switchBtnName = "机构查询用户"
        this.leftSelectData = null;
        this.getUserDataList();
      }
    }
  },
};
</script>

<style></style>
