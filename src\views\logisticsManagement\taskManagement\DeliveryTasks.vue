<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryForm" class="seache-form">
                <el-form-item label="运单号" prop="transOrderNo" style="width: 230px">
                    <el-input v-model="queryForm.transOrderNo" clearable placeholder="请输入运单号" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="配送司机" prop="driverCode" style="width: 230px">
                    <el-select v-model="queryForm.driverCode" clearable filterable placeholder="请选择配送司机" @change="handleQuery">
                        <el-option v-for="(item, idx) in driverList" :key="idx" :label="item.driverName" :value="item.driverCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="创建时间" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryForm.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单号" prop="orderNo">
                    <el-input v-model="queryForm.orderNo" clearable placeholder="请输入订单号" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="货主公司" prop="companyId" style="width: 240px">
                    <el-select v-model="queryForm.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">
                        <el-option v-for="(dict, index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单状态" prop="orderStatus">
                    <el-select v-model="queryForm.orderStatus" clearable placeholder="请选择订单状态" @change="handleQuery">
                        <el-option v-for="dict in statusDicts" :key="dict.code" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="任务状态" prop="status">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择订单状态" @change="handleQuery">
                        <el-option v-for="dict in deliveryTaskStatus" :key="dict.code" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单状态" prop="transStatus">
                    <el-select v-model="queryForm.transStatus" clearable filterable placeholder="请选择状态" @change="handleQuery">
                        <el-option v-for="(dict, idx) in fourplDriverStatusOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="任务来源" prop="taskSource">
                    <el-select v-model="queryForm.taskSource" clearable filterable placeholder="请选择任务来源" @change="handleQuery">
                        <el-option v-for="(d, index) in taskSourceOptions" :key="index" :label="d.name" :value="d.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="交接方式" prop="handWay">
                    <el-select v-model="queryForm.handWay" clearable filterable placeholder="请选择交接方式" @change="handleQuery">
                        <el-option v-for="(d, index) in fourplHandWayOptions" :key="index" :label="d.name" :value="d.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="任务类型" prop="taskType">
                    <el-select v-model="queryForm.taskType" clearable filterable placeholder="请选择任务类型" @change="handleQuery">
                        <el-option v-for="(d, index) in taskTypeOptions" :key="index" :label="d.name" :value="d.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件公司" prop="receiverCompany">
                    <el-input v-model="queryForm.receiverCompany" clearable placeholder="请输入收件公司" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件地址" prop="receiverAddress">
                    <el-cascader v-model="queryForm.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择收件地址" @change="handleQuery" @visible-change="visibleChange" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="三方物流单号" prop="threeFlowNo">
                    <el-input v-model="queryForm.threeFlowNo" clearable placeholder="请输入三方物流单号" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="三方物流编码" prop="threeFlowCode">
                    <el-input v-model="queryForm.threeFlowCode" clearable placeholder="请输入三方物流编码" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10">
                <el-button :disabled="multiple" type="warning" @click="batchChangeDriverShow">批量改派</el-button>
                <el-button :disabled="multiple" type="danger" @click="batchForcedCompletion">批量强制完结</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="driverTask" @queryTable="getList"></right-toolbar>
            </div>
            <column-table ref="driverTask" v-loading="loading" :columns="columns" :data="taskList" :defaultSort="{ prop: 'createDate', order: 'descending' }" :show-check-box="true" @selection-change="handleSelectionChange">
                <template #taskType="{ row }">
                    <span>{{ formatDictionaryData('taskTypeOptions', row.taskType) }}</span>
                </template>
                <template #taskSource="{ row }">
                    <span>{{ formatDictionaryData('taskSourceOptions', row.taskSource) }}</span>
                </template>
                <template #handWay="{ row }">
                    <span>{{ formatDictionaryData('fourplHandWayOptions', row.handWay) }}</span>
                </template>
                <template #transStatus="{ row }">
                    <span>{{ formatDictionaryData('fourplDriverStatusOptions', row.transStatus) }}</span>
                </template>
                <template #splitFlag="{ row }">
                    <span>{{ formatDictionaryData('fourplTaskSplitStatusOptions', row.splitFlag) }}</span>
                </template>
                <template #orderStatus="{ row }">
                    <span>{{ formatDictionaryData('statusDicts', row.orderStatus) }}</span>
                </template>
                <template #status="{ row }">
                    <span>{{ formatDictionaryData('deliveryTaskStatus', row.status) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="row.status === '1' || row.status === '2'" icon="el-icon-edit" link size="small" type="warning" @click="showChangeDriver(row)">改派</el-button>
                    <el-button v-if="(row.orderStatus === '4' || row.orderStatus === '5') && row.status !== '5'" icon="el-icon-close" link size="small" type="danger" @click="handleCompulsoryCompletion(row)">强制完结</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!--  改派 批量改派-->
        <el-drawer v-model="changeDriverOpen" :size="batchChangeDriverOpen ? '850px' : '500px'" title="改派" @close="handleCloseDriver">
            <div style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="formChangeDriver" :model="formChangeDriver" :rules="rulesChangeDriver" label-width="auto">
                        <div v-if="batchChangeDriverOpen">
                            <el-table :data="selectionRows" border height="250" style="width: 100%; margin-bottom: 20px">
                                <el-table-column align="center" label="订单号" prop="orderNo"></el-table-column>
                                <el-table-column align="center" label="发件公司" prop="sendCompany"></el-table-column>
                                <el-table-column align="center" label="货物数量" prop="goodsPackages"></el-table-column>
                                <el-table-column align="center" label="当前司机" prop="driverName"></el-table-column>
                            </el-table>
                        </div>
                        <div v-else>
                            <el-form-item label="订单号" prop="orderNo">
                                <el-input v-model="formChangeDriver.orderNo" disabled placeholder="" />
                            </el-form-item>
                            <el-form-item label="发件公司" prop="sendCompany">
                                <el-input v-model="formChangeDriver.sendCompany" disabled placeholder="" />
                            </el-form-item>
                            <el-form-item label="货物数量" prop="goodsPackages">
                                <el-input v-model="formChangeDriver.goodsPackages" disabled placeholder="" />
                            </el-form-item>
                            <el-form-item v-if="formChangeDriver.transOrderNo" label="运单号" prop="transOrderNo">
                                <el-input v-model="formChangeDriver.transOrderNo" disabled placeholder="请输入运单号" />
                            </el-form-item>
                        </div>
                        <el-form-item label="转给司机" prop="driver">
                            <el-select v-model="formChangeDriver.driver" clearable filterable placeholder="请选择司机" value-key="driverCode">
                                <el-option v-for="item in driverList" :key="item.driverCode" :label="item.driverName" :value="item" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="转单原因" prop="reason">
                            <el-input v-model="formChangeDriver.reason" maxlength="255" placeholder="请输入转单原因" rows="5" show-word-limit type="textarea" />
                        </el-form-item>
                        <div class="dialog-footer" style="text-align: center">
                            <el-button v-if="batchChangeDriverOpen" type="primary" @click="batchChangeDriverHandle">保存</el-button>
                            <el-button v-else type="primary" @click="changeDriver">保存</el-button>
                            <el-button @click="handleCloseDriver">取消</el-button>
                        </div>
                    </el-form>
                </el-card>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import deliveryTasks from '@/api/carrierEnd/deliveryTasks';
import RightToolbar from '@/components/RightToolbar/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import moment from 'moment';

export default {
    name: 'DeliveryTasks',
    components: {
        RightToolbar,
        SearchButton,
        ColumnTable
    },
    data() {
        return {
            loading: false,
            showSearch: true,
            total: 0,
            taskList: [],
            title: '',
            isShowAll: false,
            queryForm: {
                current: 1,
                size: 10,
                companyId: undefined,
                orderStatus: undefined,
                transOrderNo: undefined,
                orderNo: undefined,
                taskSource: undefined,
                handWay: undefined,
                taskType: undefined,
                startDate: undefined,
                endDate: undefined,
                transStatus: undefined,
                driverCode: undefined,
                receiverCompany: undefined,
                threeFlowNo: undefined,
                threeFlowCode: undefined,
                queryTime: [],
                receiverAddress: []
            },
            taskTypeOptions: [], // 任务类型
            fourplDriverStatusOptions: [], // 运单状态
            driverList: [], //配送司机列表
            fourplHandWayOptions: [], // 交接方式
            columns: [
                { title: '任务编码', key: 'taskCode', align: 'center', width: '200px', fixed: 'left', columnShow: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '200px', columnShow: true },
                { title: '订单号', key: 'orderNo', align: 'center', width: '180px', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '配送司机', key: 'driverName', align: 'center', width: '180px', columnShow: true },
                { title: '任务来源', key: 'taskSource', align: 'center', width: '80px', columnShow: true },
                { title: '交接方式', key: 'handWay', align: 'center', width: '180px', columnShow: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', width: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '订单件数', key: 'goodsPackages', align: 'center', width: '80px', columnShow: true },
                { title: '收件人', key: 'receiverUser', align: 'center', width: '180px', columnShow: true },
                { title: '收件人电话', key: 'receiverUserPhone', align: 'center', width: '120px', columnShow: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', width: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '发件人', key: 'sendUser', align: 'center', width: '180px', columnShow: true },
                { title: '发件人电话', key: 'sendUserPhone', align: 'center', width: '120px', columnShow: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '三方物流单号', key: 'threeFlowNo', align: 'center', width: '180px', columnShow: true },
                { title: '三方物流编码', key: 'threeFlowCode', align: 'center', width: '180px', columnShow: true },
                { title: '上游司机任务', key: 'upTaskCode', align: 'center', width: '180px', columnShow: true },
                { title: '拆分标志', key: 'splitFlag', align: 'center', width: '80px', columnShow: true },
                { title: '任务类型', key: 'taskType', align: 'center', width: '80px', columnShow: true },
                { title: '创建时间', key: 'createDate', align: 'center', width: '180px', columnShow: true, sortable: true },
                { title: '订单状态', key: 'orderStatus', align: 'center', width: '80px', columnShow: true, fixed: 'right' },
                { title: '任务状态', key: 'status', align: 'center', width: '80px', columnShow: true, fixed: 'right' },
                { title: '运单状态', key: 'transStatus', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '180px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            taskSourceOptions: [], // 任务来源
            fourplTaskSplitStatusOptions: [], // 任务拆分
            selectionRows: [],
            multiple: true,
            changeDriverOpen: false,
            formChangeDriver: {},
            rulesChangeDriver: {
                driver: [{ required: true, message: '转给司机不能为空' }]
            },
            batchChangeDriverOpen: false,
            ownerList: [],
            sysAreas: [],
            shortcuts: [
                {
                    text: '无',
                    value: (e) => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: (e) => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ],
            statusDicts: [],
            deliveryTaskStatus: []
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    created() {
        this.getDict();
        // 将默认设置调整为当天
        this.queryForm.startDate = moment().format('YYYY-MM-DD');
        this.queryForm.endDate = moment().format('YYYY-MM-DD');
        this.queryForm.queryTime = [this.queryForm.startDate, this.queryForm.endDate];
        // 获取货主公司
        this.getCompanySelect();
        // 获取配送司机列表
        this.getDriverList();
        this.handleQuery();
    },
    methods: {
        /**
         * 批量改派
         */
        batchChangeDriverHandle() {
            this.$refs.formChangeDriver.validate(async (valid) => {
                if (valid) {
                    try {
                        // 解构表单数据
                        const { driver, reason } = this.formChangeDriver;
                        const { driverCode: newDriverCode, driverName: newDriverName, carBindId } = driver;
                        const taskCodeList = this.selectionRows.map((item) => item.taskCode);

                        // 发起改派请求
                        const res = await deliveryTasks.transferLanTask({
                            taskCodeList,
                            newDriverCode,
                            newDriverName,
                            carBindId,
                            reason
                        });

                        // 检查响应码
                        if (res.code === 200) {
                            this.msgSuccess('改派成功');
                            this.handleCloseDriver();
                            this.getList();
                        }
                    } catch (error) {
                        // 在控制台打印错误信息以便调试
                        this.msgError('网络或服务器错误，请稍后再试');
                    }
                }
            });
        },
        /**
         * 显示批量改派弹窗
         * @returns {boolean}
         */
        batchChangeDriverShow() {
            this.selectionRows.forEach((item) => {
                // item status 为 0 和 1 时才能调整 其他情况 不选中 toggleRowSelection 只有第一次提示 之后不提示
                if (item.status !== '1' && item.status !== '2') {
                    this.$refs.driverTask.$refs.ColumnTable.toggleRowSelection(item, false);
                }
            });
            if (this.selectionRows.length === 0) {
                this.msgError('【待揽收】和【已揽收】运单才能改派');
                return false;
            }
            this.resetForm('formChangeDriver');
            this.formChangeDriver = {
                newDriverCode: '',
                reason: ''
            };
            deliveryTasks
                .driverList()
                .then((res) => {
                    if (res.code === 200) {
                        if (res.data?.length) {
                            this.driverList = res.data;
                            this.changeDriverOpen = true;
                            this.batchChangeDriverOpen = true;
                        } else {
                            this.msgError('暂无司机可改派');
                        }
                    }
                })
                .catch(() => {});
        },
        /**
         * 批量强制完结
         */
        batchForcedCompletion() {
            this.selectionRows.forEach((item) => {
                // item (row.orderStatus === '4' || row.orderStatus === '5') && row.status !== '5' 时才能调整
                // 其他情况 不选中 toggleRowSelection 只有第一次提示 之后不提示
                if ((item.orderStatus !== '4' && item.orderStatus !== '5') || item.status === '5') {
                    this.$refs.driverTask.$refs.ColumnTable.toggleRowSelection(item, false);
                }
            });
            if (this.selectionRows.length === 0) {
                this.msgError('【已签收】和【已取消】运单才能强制完结');
                return false;
            }
            this.$confirm('是否确认强制完结该任务？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    deliveryTasks
                        .forceEndLanTask(this.selectionRows.map((item) => item.id))
                        .then((res) => {
                            if (res.code === 200) {
                                this.msgSuccess('强制完结成功');
                                this.handleQuery();
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {});
        },
        /**
         * 改派
         */
        changeDriver() {
            this.$refs.formChangeDriver.validate(async (valid) => {
                if (valid) {
                    try {
                        // 解构表单数据
                        const { driver, reason, taskCode } = this.formChangeDriver;
                        const { driverCode: newDriverCode, driverName: newDriverName, carBindId } = driver;
                        const taskCodeList = taskCode.split(','); // 将字符串转换为数组

                        // 发起改派请求
                        const res = await deliveryTasks.transferLanTask({
                            taskCodeList,
                            newDriverCode,
                            newDriverName,
                            carBindId,
                            reason
                        });

                        // 检查响应码
                        if (res.code === 200) {
                            this.msgSuccess('改派成功');
                            this.handleCloseDriver();
                            await this.getList(); // 使用await确保getList完成后再关闭弹窗
                        }
                    } catch (error) {
                        // 在控制台打印错误信息以便调试
                        this.msgError('网络或服务器错误，请稍后再试');
                    }
                } else {
                    // 提供验证失败的提示信息
                    this.$message.error('表单验证未通过，请检查后重试');
                }
            });
        },
        /**
         * 获取货主公司下拉
         */
        getCompanySelect() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.taskTypeOptions = await this.getDictList('delivery_task_task_type');
            this.fourplDriverStatusOptions = await this.getDictList('delivery_task_waybill_status');
            this.fourplHandWayOptions = await this.getDictList('fourpl_hand_way');
            this.taskSourceOptions = await this.getDictList('fourpl_driver_task_source');
            this.fourplTaskSplitStatusOptions = await this.getDictList('fourpl_task_split_status');
            this.statusDicts = await this.getDictList('fourpl_order_status');
            this.deliveryTaskStatus = await this.getDictList('delivery_task_status');
        },
        getDriverList() {
            deliveryTasks
                .driverList()
                .then((res) => {
                    if (res.code === 200) {
                        if (res.data?.length) {
                            this.driverList = res.data;
                        }
                    }
                })
                .catch(() => {});
        },
        /**
         * 查询配送司机任务列表
         */
        getList() {
            this.loading = true;
            this.taskList = [];
            const { receiverAddress, queryTime, ...params } = this.queryForm;
            deliveryTasks
                .listTask(params)
                .then((response) => {
                    if (response.code === 200 && response.data) {
                        this.taskList = response.data.records || [];
                        this.total = response.data.total;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 关闭派单弹窗
         */
        handleCloseDriver() {
            this.changeDriverOpen = false;
            this.batchChangeDriverOpen = false;
        },
        /**
         * 强制完结
         * @param row
         */
        handleCompulsoryCompletion(row) {
            const { id } = row;
            if (id) {
                // 确认框提示
                this.$confirm('是否确认强制完结该任务？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        deliveryTasks
                            .forceEndLanTask([id])
                            .then((res) => {
                                if (res.code === 200) {
                                    this.msgSuccess('强制完结成功');
                                    this.handleQuery();
                                }
                            })
                            .catch(() => {});
                    })
                    .catch(() => {});
            }
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryForm.current = 1;
            const { receiverAddress, queryTime } = this.queryForm;
            const isValidDateRange = queryTime && queryTime.length === 2 && !queryTime.some((date) => date === 'Invalid Date');
            if (isValidDateRange) {
                this.queryForm.startDate = queryTime[0] + ' 00:00:00';
                this.queryForm.endDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startDate = null;
                this.queryForm.endDate = null;
            }

            if (receiverAddress) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = receiverAddress;
                this.queryForm.receiverProvinceId = receiverProvinceId || null;
                this.queryForm.receiverCityId = receiverCityId || null;
                this.queryForm.receiverCountyId = receiverCountyId || null;
                this.queryForm.receiverTownId = receiverTownId || null;
            } else {
                this.queryForm.receiverProvinceId = null;
                this.queryForm.receiverCityId = null;
                this.queryForm.receiverCountyId = null;
                this.queryForm.receiverTownId = null;
            }

            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.selectionRows = selection;
            this.multiple = !selection.length;
        },
        resetForm(formName) {
            this.$refs[formName] ? this.$refs[formName].resetFields() : '';
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.queryForm.resetFields();
            this.handleQuery();
        },
        // 展开或者合上
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 单个改派
         * @param row
         */
        showChangeDriver(row) {
            this.orderInfo = row;
            this.resetForm('formChangeDriver');
            this.formChangeDriver = { ...row, newDriverCode: '', reason: '' };
            deliveryTasks
                .driverList()
                .then((res) => {
                    if (res.code === 200) {
                        if (res.data?.length) {
                            this.driverList = res.data;
                            this.changeDriverOpen = true;
                        } else {
                            this.msgError('暂无司机可改派');
                        }
                    }
                })
                .catch(() => {});
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep {
    .el-input.is-disabled .el-input__inner {
        color: #666666 !important;
        -webkit-text-fill-color: #666666 !important;
    }
    .el-drawer__header {
        margin-bottom: 20px;
    }
}
</style>
