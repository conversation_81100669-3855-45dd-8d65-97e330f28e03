import {backApi, manageApi} from "@/api/model/salesManagement";

export const switchTable = (row) => {
    let formInline1 = {
        n1: row.customer.id,
        n2: row.delegate.id,
        n3: row.settlementMethod,
        n4: row.collectionPeriod,
        n5: row.invoiceType,
        n6: row.businessType,
        n7: row.logisticsMode,
        n8: row.thirdPartyOgistics,
        n9: row.sendAddressCode.split(","),
        n10: row.sendAddressDetail,
        n11: row.consignee,
        n12: row.consigneePhone,
        n13: row.warehouseNumber.id,
        n15: row.selfRate,
        n16: row.preparedBy,
        n17: row.remark,
        n19: Number(row.discountAmount),
        n20: row.isInvoice,
        n21: row.contractTemplate,
        n22: row.isCreateContract,
    }
    return formInline1
}

export const switchGoods = (addGoods, editState, title) => {
    let newGoodsList = []
    console.log(addGoods, 'addGoods')
    console.log(editState)
    addGoods.allGoods.forEach((item) => {
        newGoodsList.push({
            amountMoney: item.total.toFixed(2),
            approvalNumber: item.approvalNumber,
            basicUnit: item.commodity.basicUnit,
            batchNum: item.batchNumber,
            commodity: {
                id: item.commodity.id,
            },
            purchaseInboundRecord: {
                id: item.purchaseRecordId
            },
            commodityCode: item.commodity.commodityCode, //TODO
            costUnitPrice: item.unitPrice,
            differencePrice: null, //TODO
            expirationTime: item.validityTime,
            dosageForm: item.commodity.dosageForm,
            grugsType: item.commodity.grugsType,
            id: title == '编辑' ? item.id : null,
            intoTime: item.intoTime,
            inventoryBalance: item.inventoryBalance,
            intoQuantity: item.intoQuantity,
            lastSalesPrice: item.lastSalesPrice,
            manufacturer: {
                id: item.manufacture.id,
            },
            openableQuantity: item.openableQuantity,
            originPlace: item.commodity.originPlace,
            outQuantity: null, //TODO
            outTime: null, //TODO
            packageSpecification: item.commodity.packageSpecification,
            pieceNumber: item.pieceNumber,
            produceDate: item.produceDate,
            quantity: item.num.str,
            salesOrder: {
                id: editState && title == '编辑' ? editState.id : null,
            },
            purchaseOrderFrom: {
                id: item.purchaseOrderForm?.id
            },
            storageTemperature: item.commodity.storageTemperature,
            supplier: {id: item.supplier.id},
            taxRate: item.commodity.taxRate,
            tradeName: item.commodity.tradeName,
            unitLoading: item.commodity.ratio,
            unitPrice: item.price.str.toFixed(2),
            validityTime: item.commodity.validityTime,
            manufacturerName: item.manufacture.enterpriseName,
            supplierName: item.supplier.enterpriseName,
        });
    });
    return newGoodsList
}

export const switchSend = (formInline1, formInline2, status, title, editStrs, newGoodsList, footForm) => {
    console.log(editStrs)
    return manageApi
        .saveManage({
            orderConfig: {
                cargoOwnerOption: formInline2.n2,
                followGenOption: formInline2.n9.toString(),
                id:
                    title == "编辑"
                        ? editStrs.orderConfig.id
                        : null,
                inspectionReportOption: formInline2.n7.toString(),
                isConsolidation: formInline2.n4,
                packingOption: formInline2.n3,
                qualityReceiptReturn: formInline2.n8.toString(),
                receiptReturnOption: formInline2.n5.toString(),
                remark: formInline2.n10,
                retainedOption: formInline2.n6,
                salesOrder: {
                    id:
                        title == "编辑"
                            ? editStrs.orderConfig.salesOrder.id
                            : null,
                },
                stampOption: formInline2.n1,
            },
            orderFormList: newGoodsList,
            orderHeader: {
                businessType: formInline1.n6,
                collectionPeriod: formInline1.n4,
                consignee: formInline1.n11,
                consigneePhone: formInline1.n12,
                contractTemplate: formInline1.n21,
                customer: {
                    id: formInline1.n1,
                },
                delegate: {
                    id: formInline1.n2,
                },
                discountAmount: Number(formInline1.n19).toFixed(2),
                handledBy: {
                    id: formInline1.n14,
                },
                id:
                    title == "编辑"
                        ? editStrs.orderHeader.id
                        : null,
                inventoryStatus:
                    title == "编辑"
                        ? editStrs.orderHeader.orderHeader
                        : null,
                invoiceType: formInline1.n5,
                isCreateContract: formInline1.n22,
                isDiscount: 1,
                isInvoice: formInline1.n20,
                logisticsMode: formInline1.n7,
                preparedBy: {
                    id: formInline1.n16.id,
                },
                selfRate: formInline1.n15,
                sendAddressCode: formInline1.n9.toString(),
                sendAddressDetail: formInline1.n10,
                sendAddressFull: formInline1.n9 + formInline1.n10,
                settlementMethod: formInline1.n3,
                thirdPartyOgistics: formInline1.n8,
                totalAmount: Number(footForm.price).toFixed(2),
                totalAmountAfterDiscount: Number(footForm.discount).toFixed(
                    2
                ),
                totalQuantity: footForm.num,
                warehouseNumber: {
                    id: formInline1.n13,
                },
                remark: formInline1.n17,
            },
            orderStatus: status,
        })
}

export const switchBack = (chooseGoods, backForm, formPrice) => {
    let newarr = JSON.parse(JSON.stringify(chooseGoods))
    newarr.forEach(item => {
        item.commodityBasicUnit = item.commodity.basicUnit
        item.commodityDosageForm = item.commodity.dosageForm
        item.commodityManufactureName = item.commodity.manufactureName
        item.commodityName = item.commodity.tradeName
        item.commodityOriginPlace = item.commodity.originPlace
        item.commodityPackageSpecification = item.commodity.packageSpecification
        item.commodityProduceDate = item.produceDate
        item.commoditySelfCode = item.commodity.commoditySelfCode
        item.commodityUnitPrice = item.unitPrice
        item.commodityvalidityTime = item.commodity.validityTime
        item.retreatQuantity = item.amount.num
        item.customer = {
            id: backForm.n9
        }
        item.id = null
        item.retreatAmount = item.price
        item.customerName = backForm.n10
    })
    let fileArr = []
    backForm.n6.forEach(item => {
        fileArr.push({
            name: item.name,
            url: item.resFileUrl
        })
    })
    return backApi.saveList({
        //提交状态，submit为提交审核，其它为未提交/保存草稿
        "formType": "submit",
        //销退主表
        "salesRetreat": {
            //客户信息
            customer: {
                id: backForm.n9
            },
            //客户名称
            "customerName": backForm.n10,
            'files': JSON.stringify(fileArr),
            'retreatAmount': formPrice.allPrice,
            'retreatQuantity': formPrice.allNum
        },
        //销退细单
        "salesRetreatForms": newarr,
        //退货信息/原因
        "salesRetreatReason": {
            "deductionAmount": backForm.n4.toFixed(2),//扣款金额
            "hasCommodity": backForm.n2,//有无实货
            "remark": backForm.n5,
            "responsPerson": backForm.n3,//责任人
            "returnReason": backForm.n1,//退货信息
        }
    })
}
export const switchPrice = (chooseGoods, id) => {
    let newarr = []
    chooseGoods.forEach(item => {
        newarr.push({
            salesAdjust: {
                id: id
            },
            salesOutBound: {
                id: item.id
            },
            commodity: {
                id: item.commodity.id
            },
            uintPrice: item.unitPrice,
            remark: item.remark,
        })
    })
    return newarr
}
