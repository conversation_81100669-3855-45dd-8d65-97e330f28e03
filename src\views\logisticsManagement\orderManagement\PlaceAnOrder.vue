<template>
    <div class="app-container">
        <el-form ref="form" :model="form" :rules="ruleForm" label-width="130px" size="small">
            <el-card v-if="!form.id" class="mb10" shadow="never">
                <div class="box">
                    <el-form-item label="货主公司" prop="companyId">
                        <el-select v-model="form.companyId" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="ownerChange">
                            <el-option v-for="item in ownerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="揽收方式" prop="orderType">
                        <el-select v-model="form.orderType" clearable filterable placeholder="请选择揽收方式" style="width: 100%" @change="formChangeHandle($event, 'orderType')">
                            <el-option v-for="item in fourplOrderTypeOptions" :key="item.value" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="付款方式" prop="paymentMethod">
                        <el-select v-model="form.paymentMethod" placeholder="请选择付款方式" style="width: 100%" @change="formChangeHandle($event, 'paymentMethod')">
                            <el-option v-for="(dict, index) in ownerPaymentMethodOptions" :key="index" :label="dict.name" :value="dict.value + ''" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="预存款余额">
                        <div v-if="ownerAccountInfo && ownerAccountInfo?.advanceSettlementFlag == '1' && ownerAccountInfo?.advancePaymentInfo" style="display: flex; justify-content: space-between; align-items: center; width: 100%; background: #f2f2f2">
                            <div style="flex-shrink: 0; margin-right: 10px">
                                <span style="color: #ea0008; font-weight: bold">{{ ownerAccountInfo.advancePaymentInfo.balance || 0 }}</span
                                ><span style="color: #ea0008">元</span>
                            </div>
                            <div v-if="Number(ownerAccountInfo?.advancePaymentInfo?.balance) <= Number(ownerAccountInfo?.advancePaymentInfo.balanceWarn)">
                                <span style="color: #ea0008">预存款金额为{{ ownerAccountInfo.advancePaymentInfo.balance || 0 }}元，请及时缴纳预存款!</span>
                            </div>
                        </div>
                        <div v-else>无预存款</div>
                    </el-form-item>
                    <el-form-item v-if="form.orderType == '2'" label="网点" prop="sendBranch">
                        <el-select v-model="form.sendBranch" placeholder="请选择网点" style="width: 100%" @change="formChangeHandle($event, 'sendBranch')">
                            <el-option v-for="(item, index) in branchList" :key="index" :label="item.branchName" :value="item.branchCode" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="运输方式" prop="carrierWay">
                        <el-select v-model="form.carrierWay" disabled placeholder="请选择运输方式" style="width: 100%" @change="formChangeHandle($event, 'carrierWay')">
                            <el-option v-for="(item, index) in carrierWayDicts" :key="index" :label="item.name" :value="item.value" />
                        </el-select>
                    </el-form-item>
                </div>
            </el-card>
            <!-- /订单信息   -->
            <el-card class="mb10" shadow="never">
                <template #header>
                    <div class="mb5" style="display: flex; justify-content: space-between; align-items: center">
                        <card-header :line="false" size="mini" title="订单信息" />
                        <el-popover v-model:visible="visibleSend" :width="700" placement="top" style="justify-self: end" trigger="contextmenu">
                            <el-form @submit.prevent>
                                <el-input v-model="sendAddressParams.searchValue" clearable placeholder="输入关键字搜索" size="mini" @keyup.enter="getSendAddressBooks" />
                            </el-form>
                            <el-table :data="sendAddressBook" height="300" size="mini" style="width: 700px" @row-click="getSendAddress">
                                <el-table-column align="center" label="姓名" prop="user" show-overflow-tooltip width="110px" />
                                <el-table-column align="center" label="公司" prop="company" show-overflow-tooltip width="200px" />
                                <el-table-column align="center" label="电话" prop="phone" width="110px" />
                                <el-table-column align="center" label="地址" prop="address" show-overflow-tooltip width="400px">
                                    <template #default="scope"> {{ scope.row?.town?.province || '' }}{{ scope.row?.town?.city || '' }}{{ scope.row?.town?.county || '' }}{{ scope.row?.town?.town || '' }}{{ scope.row?.address || '' }} </template>
                                </el-table-column>
                            </el-table>
                            <pagination v-show="sendAddressParams.total > 0" v-model:limit="sendAddressParams.size" v-model:page="sendAddressParams.current" :total="sendAddressParams.total" style="margin-bottom: 0" @pagination="getSendAddressBooks" />
                            <template #reference>
                                <el-button class="address-btn" round size="mini" type="primary" @click="sendClick">
                                    <template #icon>
                                        <el-icon>
                                            <Notebook />
                                        </el-icon>
                                    </template>
                                    地址簿
                                </el-button>
                            </template>
                        </el-popover>
                        <el-popover v-model:visible="visibleCollect" :width="700" placement="top" style="justify-self: end" trigger="contextmenu">
                            <el-form @submit.prevent>
                                <el-input v-model="receiverAddressParams.searchValue" clearable placeholder="输入用关键字搜索" size="mini" @keyup.enter="getReceiverAddressBooks" />
                            </el-form>
                            <el-table :data="receiverAddressBook" height="300" size="mini" @row-click="getReceiverAddress">
                                <el-table-column align="center" label="姓名" prop="user" show-overflow-tooltip width="110px" />
                                <el-table-column align="center" label="公司" prop="company" show-overflow-tooltip width="200px" />
                                <el-table-column align="center" label="电话" prop="phone" width="110px" />
                                <el-table-column align="center" label="地址" prop="address" show-overflow-tooltip width="400px">
                                    <template #default="scope"> {{ scope.row?.town?.province || '' }}{{ scope.row?.town?.city || '' }}{{ scope.row?.town?.county || '' }}{{ scope.row?.town?.town || '' }}{{ scope.row?.address || '' }} </template>
                                </el-table-column>
                            </el-table>
                            <pagination v-show="receiverAddressParams.total > 0" v-model:limit="receiverAddressParams.size" v-model:page="receiverAddressParams.current" :total="receiverAddressParams.total" style="margin-bottom: 0" @pagination="getReceiverAddressBooks" />
                            <template #reference>
                                <el-button class="address-btn" round size="mini" type="primary" @click="receiverClick">
                                    <template #icon>
                                        <el-icon>
                                            <Notebook />
                                        </el-icon>
                                    </template>
                                    地址簿
                                </el-button>
                            </template>
                        </el-popover>
                    </div>
                    <div class="border-bottom-1"></div>
                </template>

                <div class="boxOrder">
                    <div>
                        <el-form-item label="发件地址" prop="pickupAddress">
                            <el-cascader v-model="form.pickupAddress" ref="pickupAddress" :options="sysAreas" clearable filterable placeholder="请选择省市区" style="width: 100%" @change="formChangeHandle($event, 'pickupAddress')" @visible-change="visibleChange" />
                        </el-form-item>
                    </div>
                    <div>
                        <el-form-item label="收件地址" prop="shippingAddress">
                            <el-cascader v-model="form.shippingAddress" ref="shippingAddress" :options="sysAreas" clearable filterable placeholder="请选择省市区" style="width: 100%" @change="formChangeHandle($event, 'shippingAddress')" @visible-change="visibleChange" />
                        </el-form-item>
                    </div>
                    <el-form-item label="发件详细地址" prop="sendAddress">
                        <el-input v-model="form.sendAddress" maxlength="200" placeholder="请输入详细地址" show-word-limit @change="formChangeHandle($event, 'sendAddress')"></el-input>
                    </el-form-item>
                    <el-form-item label="收件详细地址" prop="receiverAddress">
                        <el-input v-model="form.receiverAddress" maxlength="200" placeholder="请输入详细地址" show-word-limit @change="formChangeHandle($event, 'receiverAddress')"></el-input>
                    </el-form-item>
                    <el-form-item label="发件联系人" prop="sendUser">
                        <el-input v-model="form.sendUser" maxlength="20" placeholder="请输入发件联系人" show-word-limit @change="formChangeHandle($event, 'sendUser')"></el-input>
                    </el-form-item>
                    <el-form-item label="收件人名称" prop="receiverUser">
                        <el-input v-model="form.receiverUser" maxlength="20" placeholder="请输入收件人名称" show-word-limit @change="formChangeHandle($event, 'receiverUser')"></el-input>
                    </el-form-item>
                    <el-form-item label="发件人电话" prop="sendUserPhone">
                        <el-input v-model="form.sendUserPhone" placeholder="请输入发件人电话" @change="formChangeHandle($event, 'sendUserPhone')"></el-input>
                    </el-form-item>
                    <el-form-item label="收件人电话" prop="receiverUserPhone">
                        <el-input v-model="form.receiverUserPhone" placeholder="请输入收件人电话" @change="formChangeHandle($event, 'receiverUserPhone')"></el-input>
                    </el-form-item>
                    <el-form-item label="发件公司" prop="sendCompany">
                        <el-input v-model="form.sendCompany" maxlength="60" placeholder="请输入发件公司" show-word-limit @change="formChangeHandle($event, 'sendCompany')"></el-input>
                    </el-form-item>
                    <el-form-item label="收件公司" prop="receiverCompany">
                        <el-input v-model="form.receiverCompany" maxlength="60" placeholder="请输入收件公司" show-word-limit @change="formChangeHandle($event, 'receiverCompany')"></el-input>
                    </el-form-item>
                </div>
            </el-card>

            <!-- /货品信息   -->
            <el-card class="mb10" shadow="never">
                <template #header>
                    <card-header size="mini" title="货品信息" />
                </template>
                <div class="box">
                    <el-form-item label="产品分类" prop="productClass">
                        <el-select v-model="form.productClass" placeholder="请选择产品分类" style="width: 100%" @change="formChangeHandle($event, 'productClass')">
                            <el-option v-for="(dict, index) in fourplProductClassDicts" :key="index" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="温层类型" prop="temperatureType">
                        <el-select v-model="form.temperatureType" placeholder="请选择温层类型" style="width: 100%" @change="formChangeHandle($event, 'temperatureType')">
                            <el-option v-for="(dict, index) in temperatureTypeDicts" :key="index" :label="dict.describtion" :value="dict.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="orderDetailCheck" label="是否使用自有设备" prop="isDevice">
                        <el-switch v-model="form.isDevice" active-text="是" active-value="1" inactive-text="否" inactive-value="0" />
                    </el-form-item>
                    <el-form-item label="运输类型" prop="productType">
                        <el-select v-model="form.productType" placeholder="请选择运输类型" style="width: 100%" @change="formChangeHandle($event, 'productType')">
                            <el-option v-for="(dict, index) in productTypeDicts" :key="index" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="form.productType == 2" label="车辆类型" prop="carType">
                        <el-tree-select v-model="form.carType" :data="typeOptions" :render-after-expand="false" node-key="value" placeholder="请选择车辆类型" style="width: 100%" value-key="value" @change="formChangeHandle($event, 'carType')" />
                    </el-form-item>
                    <el-form-item v-if="form.productType == 2" label="公里数" prop="kilometre">
                        <el-input v-model="form.kilometre" placeholder="请输入公里数" @change="formChangeHandle($event, 'kilometre')"></el-input>
                    </el-form-item>
                    <el-form-item label="件数" prop="goodsPackages">
                        <el-input v-model="form.goodsPackages" placeholder="请输入件数" @change="formChangeHandle($event, 'goodsPackages')"></el-input>
                    </el-form-item>
                    <el-form-item label="货品属性" prop="productProperties">
                        <el-select v-model="form.productProperties" multiple placeholder="请选择货品属性" style="width: 100%" @change="formChangeHandle($event, 'productProperties')">
                            <el-option v-for="dict in productAttributeDictionary" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="重量" prop="productWeight">
                        <el-input v-model="form.productWeight" placeholder="请输入重量" @change="formChangeHandle($event, 'productWeight')">
                            <template #suffix>
                                <span>kg</span>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="容积" prop="productVolume">
                        <el-input v-model="form.productVolume" placeholder="请输入容积" @change="formChangeHandle($event, 'productVolume')">
                            <template #suffix>
                                <span>升</span>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="随货同行单号" prop="externalOrderNo">
                        <el-input v-model="form.externalOrderNo" placeholder="请输入随货同行单号" @change="formChangeHandle($event, 'externalOrderNo')"></el-input>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="form.remark" maxlength="100" placeholder="请输入备注" show-word-limit @change="formChangeHandle($event, 'remark')"></el-input>
                    </el-form-item>
                    <el-form-item label="货值金额" prop="goodsAmount">
                        <el-input-number v-model="form.goodsAmount" :max="99999999.99" :min="0" :precision="2" :step="0.1" style="width: 100%" @change="formChangeHandle($event, 'goodsAmount')"></el-input-number>
                    </el-form-item>
                    <el-form-item label="详情说明" prop="detailDesc" style="grid-column-end: span 2">
                        <el-input v-model="form.detailDesc" autosize placeholder="请输入详情说明" type="textarea" @change="formChangeHandle($event, 'detailDesc')"></el-input>
                    </el-form-item>
                </div>
            </el-card>

            <!-- /冷链明细   -->
            <el-card v-if="orderDetailCheck" class="mb10" shadow="never">
                <template #header>
                    <div class="mb5" style="display: flex; justify-content: space-between; align-items: center">
                        <card-header :line="false" size="mini" title="冷链明细" />
                        <el-upload ref="uploadOrderDetailExcel" :auto-upload="false" :limit="1" :on-change="handleOrderDetailExcel" :show-file-list="false" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" action="">
                            <el-button slot="trigger" class="address-btn" icon="el-icon-upload" round size="mini" type="primary">上传模板</el-button>
                            <el-button class="address-btn" icon="el-icon-download" round size="mini" style="margin-left: 10px" type="primary" @click.stop="handleExport">下载模板 </el-button>
                        </el-upload>
                    </div>
                    <div class="border-bottom-1"></div>
                </template>
                <el-table :data="coldChainProductData" :fit="true" class="coldChainTable" style="width: 100%">
                    <el-table-column align="center" label="序号" prop="index" width="50">
                        <template #default="scope">{{ scope.$index + 1 }}</template>
                    </el-table-column>
                    <el-table-column align="center" label="通用名称" min-width="180" prop="name">
                        <template #default="scope">
                            <el-input v-model="scope.row.name" maxlength="60" placeholder="请输入通用名称" show-word-limit size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="规格/型号" min-width="180" prop="specifications">
                        <template #default="scope">
                            <el-input v-model="scope.row.specifications" maxlength="60" placeholder="请输入规格/型号" show-word-limit size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="生产厂家" min-width="180" prop="manufacturer">
                        <template #default="scope">
                            <el-input v-model="scope.row.manufacturer" maxlength="60" placeholder="请输入生产厂家" show-word-limit size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="单位" min-width="180" prop="basicUnit">
                        <template #default="scope">
                            <el-input v-model="scope.row.basicUnit" maxlength="10" placeholder="请输入单位" show-word-limit size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="数量" min-width="180" prop="quantity">
                        <template #default="scope">
                            <el-input-number v-model="scope.row.quantity" :min="1" size="small" type="number"></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="批号/序列号" min-width="180" prop="batchNumber">
                        <template #default="scope">
                            <el-input v-model="scope.row.batchNumber" maxlength="60" placeholder="请输入批号/序列号" show-word-limit size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="使用期限/失效日期" min-width="210" prop="validityDate">
                        <template #default="scope">
                            <el-date-picker v-model="scope.row.validityDate" placeholder="请输入使用期限/失效日期" size="small" style="width: 100%" type="date" value-format="YYYY-MM-DD"></el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="批准文号/注册证号/备案证号" min-width="210" prop="registrationNumber">
                        <template #default="scope">
                            <el-input v-model="scope.row.registrationNumber" maxlength="60" placeholder="请输入批准文号/注册证号/备案证号" show-word-limit size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="上市许可持有人/注册人/备案人" min-width="210" prop="listPermitHolder">
                        <template #default="scope">
                            <el-input v-model="scope.row.listPermitHolder" maxlength="60" placeholder="请输入上市许可持有人/注册人/备案人" show-word-limit size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="资料是否齐全" min-width="160" prop="completeInformation">
                        <template #default="scope">
                            <el-radio-group v-model="scope.row.completeInformation" size="small">
                                <el-radio label="0">否</el-radio>
                                <el-radio label="1">是</el-radio>
                            </el-radio-group>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" fixed="right" label="操作" width="60">
                        <template #default="scope">
                            <el-button size="mini" type="text" @click="removeColdChainItems(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="mb10" style="display: flex; justify-content: center; margin-top: 15px">
                    <el-button icon="el-icon-plus" type="text" @click="addColdChainProducts">添加冷链明细</el-button>
                </div>
            </el-card>

            <!-- 增值服务 -->
            <el-card v-if="addedServiceDicts && addedServiceDicts.length > 0" class="mb10" shadow="never" style="overflow: inherit">
                <template #header>
                    <card-header size="mini" title="增值服务" />
                </template>
                <div class="mb10">
                    <el-checkbox-group v-model="addServiceList" class="box-valueAddedServiceButton" @change="formChangeHandle($event, 'addServiceList')">
                        <el-link v-for="(service, index) in addedServiceDicts" :key="index" :underline="false" :value="service.id" @click.stop="addServiceListClick(index)">
                            <el-checkbox :label="service" @change="addServiceListCheckBox($event, index)" @click.stop><br /> </el-checkbox>
                            <span>{{ service.name }} </span>
                            <span v-if="service.isNeedInput == '1'" style="margin-left: 10px">{{ service.inputValue }} {{ service.unit }} </span>
                            <span v-if="service.cost" style="margin-left: 10px">¥{{ service.cost }} </span>
                        </el-link>
                    </el-checkbox-group>
                </div>
            </el-card>

            <!-- /阅读条款  -->
            <el-card shadow="never">
                <div class="box-footer">
                    <el-form-item style="margin-bottom: 0">
                        <div style="display: flex; flex-direction: column">
                            <div style="display: flex; flex-direction: row; align-items: center; color: #bbbbbb">
                                <div>预估费用</div>
                                <div style="margin: 0 0 15px 15px; font-size: 40px; color: #ea0008">¥{{ form.orderCost && form.orderCost != 0 ? form.orderCost : '--' }}</div>
                                <el-button v-if="costDataList && costDataList.length > 0" icon="el-icon-view" style="margin-left: 10px; margin-bottom: 15px" type="primary" @click="costCreakdownClick">明细 </el-button>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item prop="agreeToTermsCarrier" style="margin-bottom: 15px">
                        <div style="display: flex; flex-direction: column">
                            <el-checkbox v-model="form.agreeToTermsCarrier" :checked="form.agreeToTermsCarrier" class="mb10" style="color: #bbbbbb">
                                阅读并同意
                                <el-button style="color: #fcb824" type="text" @click="protocolShow = true"> 《违禁品协议》</el-button>
                            </el-checkbox>
                            <div style="display: flex; flex-direction: row">
                                <el-button type="info" @click="resetForm('form')">全部清空</el-button>
                                <el-button :disabled="quicklyPlaceOrderConfirmDisabled" type="primary" @click="onSubmit('form', '1')">保存订单 </el-button>
                                <el-button :disabled="quicklyPlaceOrderConfirmDisabled" type="primary" @click="onSubmit('form', '2')">提交订单 </el-button>
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </el-card>
        </el-form>
        <!--s 违禁品协议组件  -->
        <protocol-popup v-if="protocolShow" v-model:show="protocolShow" title="违禁品协议" @cancel="form.agreeToTermsCarrier = false" @confirm="agreeToTheAgreement"></protocol-popup>
        <!--e 违禁品协议组件  -->
        <!--费用展示  -->
        <el-dialog v-model="costCreakdownShow" :show-close="false" append-to-body class="icon-dialog" modal-append-to-body title="费用明细" width="700px">
            <div v-for="(item, index) in costDataList" :key="index">
                <div v-if="item.data.length > 0" :key="index" style="margin-top: 10px">
                    <div style="display: flex; font-size: 13px; color: #333333">
                        <div>{{ item.name }}</div>
                        <div style="color: #ea0008; margin-left: 20px">¥{{ item.cost }}</div>
                    </div>
                    <div style="display: flex; padding: 15px 0; color: #999999; font-size: 11px">
                        <div v-for="(val, i) in item.data" :key="i" style="display: flex; width: 33.33%">
                            <div>{{ val.name }}</div>
                            <div style="margin-left: 10px">¥{{ val.cost }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="formula" style="padding-bottom: 15px">计算公式：{{ formula }}</div>
        </el-dialog>
        <!--增值服务-->
        <el-dialog v-model="addedServiceShow" :show-close="false" append-to-body class="icon-dialog" modal-append-to-body title="增值服务" width="600px">
            <el-form ref="formAddedService" :model="formService" :rules="serviceRules" label-width="auto">
                <el-form-item :label="formService.name" prop="inputValue">
                    <el-input-number v-model="formService.inputValue" :min="0" :precision="formService.unit == '元' ? 2 : 0" placeholder="请输入值" style="width: 200px !important"></el-input-number>
                    <span style="margin-left: 5px">{{ formService.unit }}</span>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelService">关闭</el-button>
                <el-button type="primary" @click="submitFormService">确 定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import CardHeader from '@/components/CardHeader';
import ProtocolPopup from '@/components/ProtocolPopup';
import { Notebook } from '@element-plus/icons-vue';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation.js'; // 合作配置
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration.js'; // 其他配置
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js'; // 运营配置
import serviceNetwork from '@/api/logisticsConfiguration/serviceNetwork.js'; // 服务网点
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
import { coldChainDetailVerification, formatDateToExcel, handleTree, verifyThatTheAddressesAreConsistent } from '@/utils/index.js';
import { ElLoading, ElMessageBox } from 'element-plus';
import moment from 'moment';

export default {
    name: 'PlaceAnOrder',
    components: {
        ProtocolPopup,
        CardHeader,
        Notebook
    },
    props: {
        orderType: {
            type: String,
            default: '1'
        }
    },
    data() {
        return {
            temperatureType: '', // 添加温区类型变量
            visibleSend: false,
            visibleCollect: false,
            //冷链明细 数据
            coldChainProductData: [
                {
                    name: undefined, // 通用名称
                    specifications: undefined, // 规格/型号
                    basicUnit: undefined, // 单位
                    quantity: 1, // 数量
                    registrationNumber: undefined, // 批准文号/注册证号/备案证号
                    listPermitHolder: undefined, // 上市许可持有人/注册人/备案人
                    manufacturer: undefined, // 生产厂家
                    batchNumber: undefined, // 批号/序列号
                    validityDate: undefined, // 使用期限/失效日期
                    completeInformation: '1' // 资料是否齐全
                }
            ],
            // 订单信息 数据
            form: {
                orderSource: '2', // 订单来源 1-PC货主公司下单 2-PC承运商下单 3-司机端APP下单 4-批量下单 5-承运商交接
                orderType: '1', //取件方式
                pickupAddress: '', //发件地址
                shippingAddress: '', //收件地址
                sendAddress: '', //发件地址 详细地址
                receiverAddress: '', //收件地址 详细地址
                sendUser: '', //发件联系人
                receiverUser: '', //收件人名称
                sendUserPhone: '', //发件人电话
                receiverUserPhone: '', //收件人电话
                remark: '', //备注
                sendCompany: '', //发件公司
                receiverCompany: '', //收件公司
                productType: '', //货品类型
                temperatureType: '', //温层类型
                isDevice: '0', // 是否使用自有设备
                productClass: '', //货品名称
                carType: null, //车型选择
                goodsPackages: '', //件数
                productProperties: null, //货品属性
                productWeight: 0, //重量
                externalOrderNo: '', //随货同行单号
                productVolume: 0, //容积
                paymentMethod: '', //付款方式
                deliveryTime: '', //送达日期
                appointmentTime: '', //取件时间
                addedServices: [], //附加服务
                agreeToTermsCarrier: true, //阅读并同意
                companyId: '', //货主公司
                orderCost: 0.0, // 预估费用
                costData: [], // 费用明细
                kilometre: 0, // 公里数
                goodsAmount: 0, // 货值金额
                detailDesc: undefined, // 详情说明
                carrierWay: '1', // 运输方式
                flowWay: undefined, // 承运单位
                carrierNo: undefined // 承运单位订单号
            },
            ruleForm: {
                orderType: [
                    {
                        required: true,
                        message: '请选择取件方式',
                        trigger: 'change'
                    }
                ],
                sendBranch: [
                    {
                        required: false,
                        message: '请选择网点',
                        trigger: 'change'
                    }
                ],
                pickupAddress: [
                    {
                        required: true,
                        message: '请输入发件地址',
                        trigger: 'change'
                    }
                ],
                shippingAddress: [
                    {
                        required: true,
                        message: '请输入收件地址',
                        trigger: 'change'
                    }
                ],
                sendAddress: [
                    {
                        required: true,
                        message: '请输入发件地址详细地址',
                        trigger: ['blur', 'change']
                    },
                    {
                        max: 200,
                        message: '详细地址不能超过200个字符',
                        trigger: 'blur'
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (verifyThatTheAddressesAreConsistent(value, this.pickupAddressText)) {
                                callback();
                            } else {
                                callback(new Error('发件详细地址与发件地址不符'));
                            }
                        },
                        trigger: ['blur', 'change']
                    }
                ],
                receiverAddress: [
                    {
                        required: true,
                        message: '请输入收件地址详细地址',
                        trigger: ['blur', 'change']
                    },
                    {
                        max: 200,
                        message: '详细地址不能超过200个字符',
                        trigger: 'blur'
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (verifyThatTheAddressesAreConsistent(value, this.shippingAddressText)) {
                                callback();
                            } else {
                                callback(new Error('收件详细地址与收件地址不符'));
                            }
                        },
                        trigger: ['blur', 'change']
                    }
                ],
                sendUser: [
                    {
                        required: true,
                        message: '请输入发件联系人',
                        trigger: 'change'
                    }
                ],
                receiverUser: [
                    {
                        required: true,
                        message: '请输入收件人名称',
                        trigger: 'change'
                    }
                ],
                sendUserPhone: [
                    {
                        required: true,
                        message: '请输入发件人电话',
                        trigger: 'change'
                    },
                    {
                        pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/,
                        message: '请输入正确的电话',
                        trigger: 'blur'
                    }
                ],
                receiverUserPhone: [
                    {
                        required: true,
                        message: '请输入收件人电话',
                        trigger: 'change'
                    },
                    {
                        pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/,
                        message: '请输入正确的电话',
                        trigger: 'blur'
                    }
                ],
                sendCompany: [
                    {
                        required: true,
                        message: '请输入发件公司',
                        trigger: 'change'
                    }
                ],
                receiverCompany: [
                    {
                        required: true,
                        message: '请输入收件公司',
                        trigger: 'change'
                    }
                ],
                productType: [
                    {
                        required: true,
                        message: '请选择运输类型',
                        trigger: 'change'
                    }
                ],
                temperatureType: [
                    {
                        required: true,
                        message: '请选择温层类型',
                        trigger: 'change'
                    }
                ],
                isDevice: [
                    {
                        required: true,
                        message: '请选择是否使用自有设备',
                        trigger: 'change'
                    }
                ],
                productClass: [
                    {
                        required: true,
                        message: '请选择产品分类',
                        trigger: 'change'
                    }
                ],
                carType: [
                    {
                        required: true,
                        message: '请选择车辆类型',
                        trigger: 'change'
                    }
                ],
                kilometre: [
                    {
                        required: true,
                        message: '请输入公里数',
                        trigger: 'blur'
                    },
                    {
                        pattern: /^(\d)+$/,
                        message: '公里数只能输入正整数',
                        trigger: 'blur'
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (value <= 0) {
                                callback(new Error('公里数必须大于0'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                goodsPackages: [
                    {
                        required: true,
                        message: '请输入件数',
                        trigger: 'blur'
                    },
                    {
                        pattern: /^(\d)+$/,
                        message: '件数只能输入正整数',
                        trigger: 'blur'
                    },
                    {
                        validator: (rule, value, callback) => {
                            if (value < 1) {
                                callback(new Error('件数必须大于等于1'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                productWeight: [
                    {
                        pattern: /^(\d)+$/,
                        message: '重量只能输入正整数',
                        trigger: 'change'
                    }
                ],
                productVolume: [
                    {
                        pattern: /^(\d)+$/,
                        message: '容积只能输入正整数',
                        trigger: 'change'
                    }
                ],
                externalOrderNo: [
                    {
                        required: true,
                        message: '请输入随货同行单号',
                        trigger: 'change'
                    }
                    // {
                    //   pattern: /^[a-zA-Z0-9]+$/,
                    //   message: '请输入正确的随货同行单号',
                    //   trigger: 'change'
                    // }
                ],
                paymentMethod: [
                    {
                        required: true,
                        message: '请选择付款方式',
                        trigger: 'change'
                    }
                ],
                companyId: [
                    {
                        required: true,
                        message: '请选择货主公司',
                        trigger: 'change'
                    }
                ],
                goodsAmount: [
                    {
                        required: true,
                        message: '请输入货值金额',
                        trigger: 'change'
                    }
                ],
                carrierWay: [
                    {
                        required: true,
                        message: '请选择运输方式',
                        trigger: 'change'
                    }
                ]
            },
            productAttributeDictionary: [], // 商品属性 字典
            productTypeDicts: [], // 运输类型 字典
            addedServiceDicts: [], // 增值服务
            sysAreas: [], //省市区数据
            fourplOrderTypeOptions: [], // 揽收方式
            belongUserId: '95', //订单所属人ID（租户/货主公司ID）
            createUserId: '5', //下单人ID
            pickupAddressDefaultAddress: '0', //发件地址默认地址
            shippingAddressDefaultAddress: '0', //收件地址默认地址
            addServiceList: [], //新增订单增值服务
            // 发件地址
            sendAddressParams: {
                companyId: null,
                total: 0,
                type: 1,
                current: 1,
                size: 10,
                searchValue: ''
            },
            receiverAddressBook: [],
            sendAddressBook: [],
            // 收件地址
            receiverAddressParams: {
                companyId: null,
                total: 0,
                type: 2,
                current: 1,
                size: 10,
                searchValue: ''
            },
            temperatureTypeDicts: [], //温层类型字典
            orderDetailCheck: false, //冷链详情开关
            pickerOptions: {
                disabledDate(time) {
                    // 今天之前的日期禁止选择，不包含今天
                    return time.getTime() < Date.now() - 86400000;
                },
                shortcuts: [
                    {
                        text: '今天',
                        onClick(picker) {
                            picker.$emit('pick', new Date());
                        }
                    },
                    {
                        text: '明天',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24);
                            picker.$emit('pick', date);
                        }
                    },
                    {
                        text: '后天',
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24 * 2);
                            picker.$emit('pick', date);
                        }
                    }
                ]
            },
            typeOptions: [],
            //违禁品协议组件
            agreementView: false,
            fourplPaymentMethodOptions: [], // 付款方式
            ownerList: [], // 货主公司列表
            quicklyPlaceOrderConfirmDisabled: false, //快速下单确认按钮禁用
            fourplProductClassDicts: [], // 产品分类
            costCreakdownShow: false, // 费用明细弹窗
            orderBranchData: {}, // 获取网点等信息
            costDataList: [], // 结算费用展示
            addedServiceShow: false, // 是否显示增值服务弹窗
            formService: {}, // 增值服务修改项
            serviceRules: {
                inputValue: [
                    {
                        required: true,
                        message: '请输入值',
                        trigger: 'blur'
                    }
                ]
            },
            formula: null, //计算公式
            ownerAccountInfo: {}, // 货主公司账户信息
            ownerPaymentMethodOptions: [],
            protocolShow: false, // 协议弹窗
            branchList: [], // 网点
            carrierWayDicts: [], // 运输方式 字典值
            pickupAddressText: [],
            shippingAddressText: []
        };
    },
    watch: {
        'receiverAddressParams.searchValue'() {
            // if(this.receiverAddressParams.searchValue){
            this.getReceiverAddressBooks(true);
            // }
        },
        'sendAddressParams.searchValue'() {
            // if(this.sendAddressParams.searchValue){
            this.getSendAddressBooks(true);
            // }
        },
        'form.pickupAddress'(newVal, oldVal) {
            if (newVal.length > 1 && JSON.stringify(newVal) != JSON.stringify(oldVal)) {
                this.getOrderCost();
            }
        },
        'form.shippingAddress'(newVal, oldVal) {
            if (newVal.length > 1 && JSON.stringify(newVal) != JSON.stringify(oldVal)) {
                this.getOrderCost();
            }
        },
        'form.companyId'() {
            this.getCostCalculation();
        },
        'form.productClass'() {
            this.getCostCalculation();
        },
        'form.temperatureType'() {
            this.getCostCalculation();
        },
        'form.goodsPackages'() {
            this.getCostCalculation();
        },
        'form.productType'() {
            this.getCostCalculation();
        },
        'form.productWeight'() {
            this.getCostCalculation();
        },
        'form.productVolume'() {
            this.getCostCalculation();
        },
        'form.carType'() {
            this.getCostCalculation();
        },
        'form.orderType'() {
            this.getCostCalculation();
        },
        'form.paymentMethod'() {
            this.getCostCalculation();
        },
        'form.kilometre'() {
            this.getCostCalculation();
        }
    },
    async created() {
        this.form.status = 1;
        this.form.orderType = this.orderType;
        const loading = ElLoading.service({
            lock: true,
            text: '生成订单信息中...',
            background: 'rgba(0, 0, 0, 0.7)'
        });
        setTimeout(() => {
            loading.close();
        }, 500);
        /** 产品分类 */
        this.fourplProductClassDicts = await this.getDictList('fourpl_product_class');
        // 运输类型4PL
        this.productTypeDicts = await this.getDictList('fourpl_product_type');
        // 揽收任务状态字典
        // this.fourplLanTaskStatusOptions = await this.getDictList('fourpl_lan_task_status');
        // 商品属性 字典
        this.productAttributeDictionary = await this.getDictList('fourpl_product_attributes');
        /** 交接方式 */
        // this.fourplHandWayOptions = await this.getDictList('fourpl_hand_way');
        /** 付款方式 */
        let fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
        this.fourplPaymentMethodOptions = fourplPaymentMethodOptions.filter((item) => item.value != '4' && item.value != '5' && item.value != '6');
        /** 揽收方式 */
        this.fourplOrderTypeOptions = await this.getDictList('fourpl_mail_service');
        /** 运输方式 */
        this.carrierWayDicts = await this.getDictList('fourpl_transportation_mode');

        this.getTemperatureType();
        // 获取车辆数据
        this.getTreeSelect();
        // 获取货主公司
        this.getCompanySelect();
    },
    methods: {
        /**
         * 表单变化处理
         * @param value
         * @param type
         */
        formChangeHandle(value, type) {
            if (this.form.companyId == '') {
                this.msgError('请选择货主公司');
                this.resetForm('form');
                return;
            }
            switch (type) {
                case 'orderType':
                    if (this.form.orderType == '2') {
                        this.getBranchList();
                        this.ruleForm.sendBranch[0].required = true;
                    }
                    break;
                case 'temperatureType':
                    this.checkTemperature(value);
                    this.form.isDevice = this.ownerAccountInfo.isDevice || '0'; // 是否使用自有设备
                    break;
                case 'sendBranch':
                    this.setSendByBranch(value);
                    break;
                case 'productType':
                    this.form.kilometre = 0;
                    break;
                case 'pickupAddress':
                case 'shippingAddress':
                    this[type + 'Text'] = this.$refs[type].getCheckedNodes()[0]?.pathLabels.slice(0, 3);
                    break;
            }
        },
        /**
         * 根据网点设置发件信息
         * @param value
         */
        setSendByBranch(value) {
            let item = this.branchList.find((item) => item.branchCode == value);
            if (item) {
                this.form.sendAddress = item.address;
                this.form.pickupAddress = [];
                this.form.pickupAddress.push('' + item.provinceId + '');
                this.form.pickupAddress.push('' + item.cityId + '');
                this.form.pickupAddress.push('' + item.countyId + '');
                this.form.pickupAddress.push('' + item.townId + '');
                this.visibleChange();
                this.$nextTick(() => {
                    this.formChangeHandle(this.form.pickupAddress, 'pickupAddress');
                });
            }
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        },
        /**
         * 获取网点
         */
        getBranchList() {
            serviceNetwork
                .getBranchList()
                .then((res) => {
                    if (res.code == 200) {
                        this.branchList = res.data;
                        if (this.branchList.length > 0) {
                            this.form.sendBranch = this.branchList[0].branchCode;
                            this.formChangeHandle(this.form.sendBranch, 'sendBranch');
                        }
                    }
                })
                .catch(() => {});
        },

        //获取货主公司账号信息
        getAdvanceByCompany() {
            this.ownerAccountInfo = {};
            this.ownerPaymentMethodOptions = this.fourplPaymentMethodOptions.filter((item) => item.value != '7' && item.value != '1');
            this.form.paymentMethod = null;
            if (!this.form.companyId) return;
            enterpriseCooperation
                .getAdvanceByCompany({ companyId: this.form.companyId })
                .then((res) => {
                    if (res.code == 200 && res.data) {
                        this.ownerAccountInfo = res.data;
                        if (this.ownerAccountInfo?.advanceSettlementFlag == '1' && this.ownerAccountInfo?.advancePaymentInfo && Number(this.ownerAccountInfo?.advancePaymentInfo?.balance) <= Number(this.ownerAccountInfo?.advancePaymentInfo?.balanceWarn)) {
                            if (this.ownerAccountInfo?.advancePaymentInfo?.orderAuth == '1') {
                                this.msgError('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，余额下限为' + this.ownerAccountInfo.advancePaymentInfo.balanceWarn + '元，已低于下限，暂时无法下单，请提醒货主公司及时充值');
                            } else {
                                this.msgError('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，余额下限为' + this.ownerAccountInfo.advancePaymentInfo.balanceWarn + '元，已低于下限，请提醒货主公司及时充值');
                            }
                        }
                        if (this.ownerAccountInfo?.monthlySettlementFlag == '1') {
                            //月结
                            this.ownerPaymentMethodOptions = this.fourplPaymentMethodOptions.filter((item) => item.value != '7' && item.value != '3');
                            this.form.paymentMethod = '1';
                        }
                        if (this.ownerAccountInfo?.advanceSettlementFlag == '1' && this.ownerAccountInfo?.advancePaymentInfo) {
                            // 预存款
                            this.ownerPaymentMethodOptions = this.fourplPaymentMethodOptions.filter((item) => item.value != '1' && item.value != '3');
                            this.form.paymentMethod = '7';
                        }
                        this.form.isDevice = this.ownerAccountInfo.isDevice || '0';
                    }
                })
                .catch(() => {});
        },

        addServiceListClick(index) {
            let i = this.addServiceList.findIndex((a) => a.id == this.addedServiceDicts[index].id);
            if (i > -1) {
                if (this.addedServiceDicts[index].isNeedInput == '1') {
                    this.addedServiceShow = true;
                    this.formService = this.addedServiceDicts[index];
                    this.formService.inputValue = this.addedServiceDicts[index].inputValue || this.addedServiceDicts[index].defaultValue || 0;
                }
            } else {
                this.addServiceList.push(this.addedServiceDicts[index]);
                this.addServiceListCheckBox(true, index);
            }
        },
        // 保存增值服务信息
        submitFormService() {
            this.$refs['formAddedService'].validate((valid) => {
                if (valid) {
                    this.addedServiceShow = false;
                    let formServices = [];
                    this.addServiceList.forEach((val) => {
                        let obj = {};
                        obj.id = val.id;
                        obj.name = val.name;
                        obj.inputValue = val.inputValue;
                        formServices.push(obj);
                    });
                    this.form.addedServices = formServices;
                    this.getAddedServiceCalculate(this.formService.id, this.formService.inputValue, this.formService.name);
                }
            });
        },
        // 关闭弹窗
        cancelService() {
            this.addedServiceShow = false;
            let index = this.addServiceList.findIndex((a) => a.id == this.formService.id);
            if (index > -1) {
                this.addServiceList.splice(index, 1);
            }
            let i = this.addedServiceDicts.findIndex((a) => a.id == this.formService.id);
            if (i > -1) {
                this.addedServiceDicts[i].inputValue = null;
                this.addedServiceDicts[i].cost = null;
            }
            this.formService = {};
            this.$forceUpdate();
            this.getCostCalculation();
        },

        // 获取增值服务费用
        getAddedServiceCalculate(id, inputValue) {
            this.getCostCalculation();
            orderManagement
                .calculateAddedService({ id, inputValue })
                .then((res) => {
                    if (res.code == 200) {
                        let i = this.addedServiceDicts.findIndex((a) => a.id == res.data.id);
                        if (i > -1) {
                            this.addedServiceDicts[i].cost = res.data.result;
                        }
                    }
                })
                .catch(() => {});
        },
        // 封装展示数据
        getCostDataList(data) {
            this.costDataList = [];
            let basicCost = 0,
                serviceCost = 0;
            let basicCostList = [],
                serviceCostList = [];
            data.forEach((item) => {
                if (item.type == '1') {
                    basicCost = (Number(basicCost) + Number(Number(item.cost).toFixed(2))).toFixed(2);
                    basicCostList.push({ ...item, cost: Number(Number(item.cost).toFixed(2)) });
                } else {
                    serviceCost = (Number(serviceCost) + Number(Number(item.cost).toFixed(2))).toFixed(2);
                    serviceCostList.push({ ...item, cost: Number(Number(item.cost).toFixed(2)) });
                }
            });
            if (data.length > 0) {
                this.costDataList = [
                    { name: '基础费用', cost: basicCost, data: basicCostList },
                    { name: '增值服务费用', cost: serviceCost, data: serviceCostList }
                ];
            }
        },
        // 验证
        checkAddedServices(value) {
            let flag = true;
            if (value && value.length > 0) {
                value.forEach((item) => {
                    let i = this.addedServiceDicts.findIndex((a) => a.id == item.id);
                    if (i > -1) {
                        if (item.inputValue == '' && this.addedServiceDicts[i].isNeedInput == '1') {
                            this.msgError('请输入' + item.name + '的值');
                            flag = false;
                            return false;
                        }
                    }
                });
            }
            return flag;
        },
        // 显示明细
        costCreakdownClick() {
            this.costCreakdownShow = true;
        },
        // 下单价格实时计算
        getOrderCost() {
            this.orderBranchData = {};
            this.form.collectBranchCode = null;
            this.form.collectAreaCode = null;
            this.form.transportStartBranchCode = null;
            this.form.transportEndBranchCode = null;
            this.form.deliveryBranchCode = null;
            this.form.deliveryAreaCode = null;
            this.costDataList = [];
            this.form.costData = [];
            this.form.orderCost = 0.0;
            this.formula = null;
            if (!this.form.pickupAddress || this.form.pickupAddress.length == 0 || this.form.pickupAddress.length == 1) {
                return false;
            }
            if (!this.form.shippingAddress || this.form.shippingAddress.length == 0 || this.form.shippingAddress.length == 1) {
                return false;
            }

            let params = {
                sendCountyId: this.form.pickupAddress[2],
                receiverCountyId: this.form.shippingAddress[2]
            };
            orderManagement.getOrderCost(params).then((res) => {
                if (res.code == 200) {
                    this.orderBranchData = res.data;
                    this.form.collectBranchCode = res.data.sendBranchCode; // 揽收网点编号
                    this.form.collectAreaCode = res.data.sendAreaCode; // 揽收区域编号
                    this.form.transportStartBranchCode = res.data.sendBranchCode; // 运输网点-起始网点
                    this.form.transportEndBranchCode = res.data.receiverBranchCode; //  运输网点-到达网点编号
                    this.form.deliveryBranchCode = res.data.receiverBranchCode; //  配送网点编号
                    this.form.deliveryAreaCode = res.data.receiverAreaCode; //  配送区域编号
                    this.getCostCalculation();
                }
            });
        },
        // 获取数据订单
        getCostCalculation() {
            let params = {
                companyId: this.form.companyId, // 客户ID
                orderType: this.form.orderType, // 订单类型
                orderClass: '2', // 订单类型2-货主订单 4-承运商订单
                collectBranchCode: this.orderBranchData.sendBranchCode, // 揽收网点编号
                collectAreaCode: this.orderBranchData.sendAreaCode, // 揽收区域编号
                transportStartBranchCode: this.orderBranchData.sendBranchCode, // 运输网点-起始网点
                transportEndBranchCode: this.orderBranchData.receiverBranchCode, //  运输网点-到达网点编号
                deliveryBranchCode: this.orderBranchData.receiverBranchCode, //  配送网点编号
                deliveryAreaCode: this.orderBranchData.receiverAreaCode, //  配送区域编号
                transType: this.form.productType, // 运输类型 1-零担 2-整车
                temperatureType: this.temperatureType, // 温区类型 是大类型
                goodsPackages: this.form.goodsPackages, // 件数
                productType: this.form.productClass, // 产品分类
                productWeight: this.form.productWeight || 0, // 重量
                productVolume: this.form.productVolume || 0, // 货物体积
                carType: this.form.carType, // 车辆类型
                kilometre: this.form.kilometre, // 公里数
                type: '1', // 计算方式  1-实时计算 2-计算并新增订单费用 3-计算并修改订单费用
                addedServiceList: this.form.addedServices.map((item) => {
                    return {
                        id: item.id,
                        inputValue: item.inputValue
                    };
                }), // 增值服务
                receiverTownId: this.form.shippingAddress[3]
            };
            if (this.form.productType == '2') {
                params.carType = this.form.carType; // 车辆类型
                params.kilometre = this.form.kilometre; // 公里数
            } else {
                params.carType = ''; // 车辆类型
                params.kilometre = '';
            }
            let requiredField = ['collectBranchCode', 'companyId', 'temperatureType', 'goodsPackages', 'productType', 'transType'];
            if (this.checkRequiredData(params, requiredField)) {
                this.$refs['form'].validateField('goodsPackages', (valid) => {
                    if (valid) {
                        this.costDataList = [];
                        this.form.costData = [];
                        this.form.orderCost = 0.0;
                        this.formula = null;
                        orderManagement.getCostCalculation(params).then((res) => {
                            if (res.code == 200) {
                                this.form.costData = res.data.costData;
                                this.form.orderCost = Number(res.data.allPrice);
                                this.formula = res.data.describe;
                                this.getCostDataList(this.form.costData);
                                // res.data.costData中costType等于5的cost如果大于0，代表存在超区费用，增加超区服务费提醒(提醒内容为：您的收件地址超出服务范围，将收取XX元超区服务费)，提醒信息点击确认后可继续操作
                                let costData = res.data.costData;
                                let costDataIndex = costData.findIndex((item) => item.costType == '5');
                                if (costDataIndex > -1 && costData[costDataIndex].cost > 0) {
                                    ElMessageBox.alert('您的收件地址超出服务范围，将收取<span style="color: red;">' + costData[costDataIndex].cost + '</span>元超区服务费', '提示', {
                                        confirmButtonText: '确定',
                                        dangerouslyUseHTMLString: true,
                                        closeOnClickModal: true
                                    });
                                }
                            } else {
                                // 费用计算失败时，将订单费用设置为0
                                this.form.orderCost = '0';
                                this.form.costData = [];
                                this.formula = null;
                                this.costDataList = [];
                            }
                        });
                    }
                });
            }
        },
        // 检验必填项
        checkRequiredData(obj, requiredField) {
            let flag = true;
            for (let key of requiredField) {
                if (obj[key] == undefined || obj[key] == '') {
                    flag = false;
                    return flag;
                }
                if ((key = 'goodsPackages' && obj[key] <= 0)) {
                    flag = false;
                    return flag;
                }
            }
            return flag;
        },
        /** 产品分类 */
        fourplProductClassFormat(val) {
            return this.selectDictLabel(this.fourplProductClassDicts, val.productClass);
        },
        // 选择
        ownerChange(e) {
            this.form.companyId = e;

            // 清空增值服务相关数据
            this.addServiceList = [];
            this.form.addedServices = [];
            this.addedServiceDicts = [];

            if (!e) {
                // 如果清空了公司选择,直接返回
                return;
            }

            // 继续处理选择公司后的逻辑
            this.getSendAddressBooks();
            this.getReceiverAddressBooks();
            this.getAdvanceByCompany(); // 货主公司货主公司账号信息
            this.getAllAvailableAddService(); // 获取增值服务

            // 重置相关字段
            this.form.pickupAddress = []; //发件地址
            this.form.shippingAddress = []; //收件地址
            this.form.sendAddress = ''; //发件地址 详细地址
            this.form.receiverAddress = ''; //收件地址 详细地址
            this.form.sendUser = ''; //发件联系人
            this.form.receiverUser = ''; //收件人名称
            this.form.sendUserPhone = ''; //发件人电话
            this.form.receiverUserPhone = ''; //收件人电话
            this.form.sendCompany = ''; //发件公司
            this.form.receiverCompany = ''; //收件公司
            this.orderBranchData = {};
            this.form.collectBranchCode = null;
            this.form.collectAreaCode = null;
            this.form.transportStartBranchCode = null;
            this.form.transportEndBranchCode = null;
            this.form.deliveryBranchCode = null;
            this.form.deliveryAreaCode = null;
        },
        // 点击收货地址簿
        receiverClick() {
            if (!this.form.companyId || this.form.companyId == '') {
                this.msgError('请选择货主公司');
                return false;
            }
            this.visibleCollect = true;
        },
        // 点击
        sendClick() {
            if (!this.form.companyId || this.form.companyId == '') {
                this.msgError('请选择货主公司');
                return false;
            }
            this.visibleSend = true;
        },
        // 获取货主公司下拉
        getCompanySelect() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
        // 发件地址簿
        getSendAddressBooks() {
            // companyId
            this.sendAddressParams.companyId = this.form.companyId;
            let params = {
                ...this.sendAddressParams,
                queryType: '1' // 查询类型：0-货主公司查询 1-承运商查询
            };
            orderManagement.listAddressBook(params).then((res) => {
                if (res.code == 200) {
                    this.sendAddressParams.total = res.data.total || 0;
                    this.sendAddressBook = res.data.records || [];
                    if (this.sendAddressBook.length > 0 && this.sendAddressBook[0].isDefaults) {
                        this.getSendAddress(this.sendAddressBook[0]);
                    }
                }
            });
        },
        // 获取温层类型
        getTemperatureType() {
            otherConfiguration.getTemperatureTypeList({ status: '0' }).then((res) => {
                if (res.code === 200 && res?.data?.records.length > 0) {
                    this.temperatureTypeDicts = res.data.records;
                }
            });
        },
        // 查询车辆类型下拉树结构
        getTreeSelect() {
            operationConfiguration.listCarType({ status: '0', size: 1000 }).then((response) => {
                this.typeOptions = [];
                if (response.code == 200 && response?.data?.records.length > 0) {
                    let typeData = response?.data?.records.map((item) => {
                        return { label: item.typeName, value: item.typeCode, parentId: item.parentCode };
                    });
                    typeData.push({ label: '主类型', value: '0', parentId: '' });
                    this.typeOptions = handleTree(typeData, 'value');
                }
            });
        },
        // 新建订单
        onSubmit(formName, status) {
            //没有选中货品类型 整车
            if (this.form.productType != '2') {
                this.form.carType = null;
            }
            if (this.form.orderType == 2) {
                this.form.appointmentTime = null;
            }
            if (!this.form.productProperties || !this.form.productProperties.length) {
                this.form.productProperties = null;
            }
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 协议是否同意
                    if (this.form.agreeToTermsCarrier === false) {
                        this.msgError('请先阅读并同意服务协议和隐私政策');
                        return;
                    }

                    if (this.orderDetailCheck) {
                        if (this.coldChainProductData.length === 0) {
                            this.msgError('冷链订单货品明细不能为空');
                            return;
                        } else {
                            const msg = coldChainDetailVerification(this.coldChainProductData);
                            if (msg != null) {
                                this.msgError(msg);
                                return;
                            }
                        }
                    } else {
                        this.coldChainProductData = [];
                    }
                    if (!this.checkAddedServices(this.form.addedServices)) {
                        return;
                    }
                    if (Object.values(this.ownerAccountInfo).length != 0 && this.ownerAccountInfo?.advancePaymentInfo && this.form.paymentMethod == '7') {
                        if (Number(this.ownerAccountInfo?.advancePaymentInfo?.balance) <= 0 || Number(this.form?.orderCost) > Number(this.ownerAccountInfo?.advancePaymentInfo?.balance)) {
                            this.msgError('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，已低于总费用，请提醒货主公司及时充值');
                        } else {
                            if (Number(this.form?.orderCost) <= Number(this.ownerAccountInfo?.advancePaymentInfo?.balance)) {
                                if (Number(this.ownerAccountInfo?.advancePaymentInfo?.balance) <= Number(this.ownerAccountInfo?.advancePaymentInfo?.balanceWarn)) {
                                    this.msgError('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，余额下限为' + this.ownerAccountInfo.advancePaymentInfo.balanceWarn + '元，已低于下限，请提醒货主公司及时充值');
                                }
                            }
                        }
                    }
                    // 拆分发件人地址

                    this.form.sendProvinceId = this.form.pickupAddress[0];
                    this.form.sendCityId = this.form.pickupAddress[1];
                    this.form.sendCountyId = this.form.pickupAddress[2];
                    // this.form.sendTownId = this.form.pickupAddress[3];
                    this.form.sendTown = { id: this.form.pickupAddress[3] };

                    // 拆分收件人地址
                    this.form.receiverProvinceId = this.form.shippingAddress[0];
                    this.form.receiverCityId = this.form.shippingAddress[1];
                    this.form.receiverCountyId = this.form.shippingAddress[2];
                    // this.form.receiverTownId = this.form.shippingAddress[3];
                    this.form.receiverTown = { id: this.form.shippingAddress[3] };
                    this.quicklyPlaceOrderConfirmDisabled = true;
                    const loading = ElLoading.service({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    this.form.orderSource = '2'; // 订单来源 1-PC货主公司下单 2-PC承运商下单 3-司机端APP下单 4-批量下单 5-承运商交接
                    // this.form.paymentStatus = ''; // 付款状态
                    // this.form.orderCost = '0';// 订单费用
                    // this.form.actualCost = ''; //实际费用
                    this.form.whStatus = '0'; // 入库状态 0-未入库 1-部分入库 2-全部入库
                    this.form.orderClass = '2'; // 订单创建方式 2-正常下单 4-承运商交接
                    let param = {
                        ...this.form,
                        orderDetailList: this.coldChainProductData,
                        addServiceList: this.form.addedServices,
                        temperatureType: { id: this.form.temperatureType },
                        isChange: '0',
                        goodsName: this.fourplProductClassFormat(this.form),
                        status // 订单状态
                    };
                    if (this.form.productProperties && this.form.productProperties.length > 0) {
                        param.productProperties = this.form.productProperties.join(',');
                    }
                    delete param.shippingAddress;
                    delete param.pickupAddress;
                    delete param.agreeToTermsCarrier;
                    delete param.addedServices;
                    orderManagement
                        .saveOrderDrug(param)
                        .then((response) => {
                            if (response.code === 200) {
                                this.msgSuccess(this.form.id ? '修改成功' : '新建成功');
                                this.$emit('changeAddOrderShow', false);
                                // 重置表单
                                this.resetForm('form');
                                this.coldChainProductData = [];
                                this.quicklyPlaceOrderConfirmDisabled = false;
                                this.form.orderCost = 0.0; // 预估费用
                                this.form.costData = []; // 费用明细
                                this.costDataList = []; //费用展示
                                this.addServiceList = [];
                                this.form.kilometre = 0;
                                if (this.addedServiceDicts && this.addedServiceDicts.length > 0) {
                                    this.addedServiceDicts.forEach((item, i) => {
                                        // if (item.cost && item.cost != '') {
                                        this.addedServiceDicts[i].cost = null;
                                        this.addedServiceDicts[i].inputValue = '';
                                        // }
                                        if (item.isDefault == '1') {
                                            this.addedServiceDicts[i].inputValue = item.defaultValue;
                                            this.addServiceList.push(item);
                                            this.getAddedServiceCalculate(item.id, item.defaultValue, item.name);
                                        }
                                    });
                                    if (this.addServiceList.length > 0) {
                                        let formServices = [];
                                        this.addServiceList.forEach((val) => {
                                            let obj = {};
                                            obj.id = val.id;
                                            obj.name = val.name;
                                            obj.inputValue = val.inputValue || '';
                                            formServices.push(obj);
                                        });
                                        this.form.addedServices = formServices;
                                    }
                                }
                                this.form.companyId = param.companyId;
                                this.getSendAddressBooks(); // 发件地址
                                this.getAdvanceByCompany();
                                this.formService = {};
                                this.orderDetailCheck = false;
                            } else {
                                this.quicklyPlaceOrderConfirmDisabled = false;
                            }
                            loading.close();
                        })
                        .catch(() => {
                            loading.close();
                            this.quicklyPlaceOrderConfirmDisabled = false;
                        });
                } else {
                    this.quicklyPlaceOrderConfirmDisabled = false;
                }
            });
        },

        // 删除冷链货品
        removeColdChainItems(index) {
            this.$confirm('确定删除该该明细？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.coldChainProductData.splice(index, 1);
                })
                .catch(() => {
                    this.msgError('已取消删除');
                });
        },
        // 重置表单
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
            this.coldChainProductData = [];
            this.form.orderCost = 0.0; // 预估费用
            this.form.costData = []; // 费用明细
            this.addServiceList = [];
            if (this.addedServiceDicts && this.addedServiceDicts.length > 0) {
                this.addedServiceDicts.forEach((item, i) => {
                    if (item.cost && item.cost != '') {
                        this.addedServiceDicts[i].cost = null;
                        this.addedServiceDicts[i].inputValue = '';
                    }
                });
            }
        },
        // 新增冷链货品
        addColdChainProducts() {
            this.coldChainProductData.push({
                name: undefined, // 通用名称
                specifications: undefined, // 规格/型号
                basicUnit: undefined, // 单位
                quantity: 1, // 数量
                registrationNumber: undefined, // 批准文号/注册证号/备案证号
                listPermitHolder: undefined, // 上市许可持有人/注册人/备案人
                manufacturer: undefined, // 生产厂家
                batchNumber: undefined, // 批号/序列号
                validityDate: undefined, // 使用期限/失效日期
                completeInformation: '1' // 资料是否齐全
            });
        },
        addServiceListCheckBox(e, index) {
            if (e == false) {
                let y = this.form.addedServices.findIndex((c) => c.id == this.addedServiceDicts[index].id);
                if (y > -1) {
                    this.form.addedServices.splice(y, 1);
                }
                this.addedServiceDicts[index].inputValue = '';
                this.addedServiceDicts[index].cost = null;
                if (this.addedServiceDicts[index].id == this.formService.id) {
                    this.formService.cost = null;
                    this.formService.inputValue = null;
                    this.formService = {};
                    this.$forceUpdate();
                }
                this.getCostCalculation();
                return false;
            }
            if (this.addedServiceDicts[index].isNeedInput == '1') {
                this.addedServiceShow = true;
                this.formService = this.addedServiceDicts[index];
                this.formService.inputValue = this.addedServiceDicts[index].defaultValue || 0;
            } else {
                let formServices = [];
                this.addServiceList.forEach((val) => {
                    let obj = {};
                    obj.id = val.id;
                    obj.name = val.name;
                    obj.inputValue = val.inputValue || '';
                    formServices.push(obj);
                });
                this.form.addedServices = formServices;
                this.getAddedServiceCalculate(this.addedServiceDicts[index].id, this.addedServiceDicts[index].inputValue, this.addedServiceDicts[index].name);
            }
        },
        // 更改温层选择后的操作
        checkTemperature() {
            this.temperatureTypeDicts.forEach((t) => {
                if (t.id * 1 === this.form.temperatureType * 1) {
                    this.temperatureType = t.type;
                    this.orderDetailCheck = t.type !== '1' && t.type !== '2';
                    // 当温层类型改变时重新获取增值服务
                    if (this.form.companyId) {
                        this.getAllAvailableAddService();
                    }
                }
            });
            if (this.orderDetailCheck && this.coldChainProductData.length == 0) {
                this.addColdChainProducts();
            }
        },
        /**
         * 获取增值服务
         */
        getAllAvailableAddService() {
            this.addServiceList = [];
            this.form.addedServices = [];
            // 如果没有选择公司或温层类型,直接返回
            if (!this.form.companyId || !this.temperatureType) {
                this.addedServiceDicts = [];
                return;
            }
            orderManagement
                .getOrderAddedService({
                    ownerId: this.form.companyId,
                    tempType: this.temperatureType // 添加温区类型参数
                })
                .then((response) => {
                    this.addedServiceDicts = [];
                    if (response.code == 200) {
                        this.addedServiceDicts = response.data;
                        this.addedServiceDicts.forEach((item, index) => {
                            if (item.isDefault == '1') {
                                this.addedServiceDicts[index].inputValue = item.defaultValue;
                                this.addServiceList.push(item);
                                this.getAddedServiceCalculate(item.id, item.defaultValue, item.name);
                            }
                        });
                        if (this.addServiceList.length > 0) {
                            let formServices = [];
                            this.addServiceList.forEach((val) => {
                                let obj = {};
                                obj.id = val.id;
                                obj.name = val.name;
                                obj.inputValue = val.inputValue || '';
                                formServices.push(obj);
                            });
                            this.form.addedServices = formServices;
                        }
                    }
                });
        },
        // 填写地址簿信息 收件
        getReceiverAddress(row) {
            const { user, phone, address, provinceId, cityId, countyId, town, company } = row;
            this.form.receiverUser = user;
            this.form.receiverUserPhone = phone;
            this.form.receiverAddress = address;
            this.form.receiverCompany = company;
            this.form.shippingAddress = [];
            this.form.shippingAddress.push('' + provinceId + '');
            this.form.shippingAddress.push('' + cityId + '');
            this.form.shippingAddress.push('' + countyId + '');
            this.form.shippingAddress.push('' + town.id + '');
            this.visibleChange();
            this.$nextTick(() => {
                this.formChangeHandle(this.form.shippingAddress, 'shippingAddress');
            });
            // 取消pickupAddress验证提示
            this.$refs.form.clearValidate('shippingAddress');
            this.visibleCollect = false;
            this.msgSuccess('地址填写完成');
        },
        // 查询收件地址簿
        getReceiverAddressBooks() {
            // companyId
            this.receiverAddressParams.companyId = this.form.companyId;
            let params = {
                ...this.receiverAddressParams,
                queryType: '1' // 查询类型：0-货主公司查询 1-承运商查询
            };
            orderManagement.listAddressBook(params).then((res) => {
                if (res.code == 200) {
                    this.receiverAddressParams.total = res.data.total || 0;
                    this.receiverAddressBook = res.data.records || [];
                    if (this.receiverAddressBook.length > 0 && this.receiverAddressBook[0].isDefaults) {
                        this.getReceiverAddress(this.receiverAddressBook[0]);
                    }
                }
            });
        },
        // 填写地址簿信息 取件
        getSendAddress(row) {
            const { user, phone, address, company, provinceId, cityId, countyId, town } = row;
            this.form.sendUser = user || null;
            this.form.sendUserPhone = phone || null;
            this.form.sendAddress = address || null;
            this.form.sendCompany = company || null;
            this.form.pickupAddress = [];
            this.form.pickupAddress.push('' + provinceId + '');
            this.form.pickupAddress.push('' + cityId + '');
            this.form.pickupAddress.push('' + countyId + '');
            this.form.pickupAddress.push('' + town.id + '');
            this.visibleChange();
            this.$nextTick(() => {
                this.formChangeHandle(this.form.pickupAddress, 'pickupAddress');
            });
            // 取消pickupAddress验证提示
            this.$refs.form.clearValidate('pickupAddress');
            this.visibleSend = false;
            // 提示地址填写完成
            this.msgSuccess('地址填写完成');
        },
        // 获取时间段
        getTimePeriod(data) {
            this.form.appointmentTime = data;
        },
        // 同意协议
        agreeToTheAgreement() {
            this.form.agreeToTermsCarrier = true;
        },
        handleOrderDetailExcel(file) {
            this.fileTemp = file.raw;
            if (this.fileTemp) {
                if (this.fileTemp.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || this.fileTemp.type == 'application/vnd.ms-excel') {
                    this.importCodeGoodsFile(this.fileTemp);
                } else {
                    this.msgError('附件格式错误，请删除后重新上传！');
                }
                this.$refs['uploadOrderDetailExcel'].clearFiles();
            } else {
                this.msgError('请上传附件！');
            }
        },
        // 上传冷链模板文件
        importCodeGoodsFile() {
            let _this = this;
            this.file = event.currentTarget.files[0];

            let rABS = false; //是否将文件读取为二进制字符串
            let f = this.file;

            let reader = new FileReader();
            //if (!FileReader.prototype.readAsBinaryString) {
            FileReader.prototype.readAsBinaryString = function (f) {
                let binary = '';
                let rABS = false; //是否将文件读取为二进制字符串
                let wb; //读取完成的数据
                let outdata;
                let reader = new FileReader();
                reader.onload = function () {
                    let bytes = new Uint8Array(reader.result);
                    let length = bytes.byteLength;
                    for (let i = 0; i < length; i++) {
                        binary += String.fromCharCode(bytes[i]);
                    }
                    //此处引入，用于解析excel
                    let XLSX = require('xlsx');
                    if (rABS) {
                        wb = XLSX.read(btoa(fixdata(binary)), {
                            //手动转化
                            type: 'base64'
                        });
                    } else {
                        wb = XLSX.read(binary, {
                            type: 'binary'
                        });
                    }
                    outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
                    //outdata就是读取的数据（不包含标题行即表头，表头会作为对象的下标）
                    // 删除空数据
                    _this.coldChainProductData = _this.coldChainProductData.filter(
                        (item) => item.name != '' && item.specifications != '' && item.basicUnit != '' && item.registrationNumber != '' && item.listPermitHolder != '' && item.manufacturer != '' && item.batchNumber != '' && item.validityDate != ''
                    );
                    //此处可对数据进行处理
                    outdata.map((v) => {
                        let obj = {};
                        obj.name = v['通用名称'];
                        obj.specifications = v['规格/型号'];
                        obj.basicUnit = v['单位'];
                        if (typeof v['数量'] == 'string') {
                            obj.quantity = parseInt(v['数量'].match(/\d+(.\d+)?/g));
                        } else {
                            obj.quantity = v['数量'];
                        }
                        obj.registrationNumber = v['批准文号/注册证号/备案证号'];
                        obj.listPermitHolder = v['上市许可持有人/注册人/备案人'];
                        obj.manufacturer = v['生产厂家'];
                        obj.batchNumber = v['批号/序列号'];
                        // 日期格式转换
                        const dateValue = typeof v['使用期限/失效日期'] == 'number' ? formatDateToExcel(v['使用期限/失效日期'], '/') : v['使用期限/失效日期'];
                        obj.validityDate = v['使用期限/失效日期'] && v['使用期限/失效日期'] != '' && v['使用期限/失效日期'] != undefined ? moment(dateValue).format('YYYY-MM-DD') : undefined;
                        obj.completeInformation = v['资料是否齐全'] == '是' ? '1' : '0';
                        _this.coldChainProductData.push(obj);
                    });
                    return;
                };
                reader.readAsArrayBuffer(f);
            };
            if (rABS) {
                reader.readAsArrayBuffer(f);
            } else {
                reader.readAsBinaryString(f);
            }
        },
        /** 导出按钮操作 */
        handleExport() {
            orderManagement.importTemplate('', '', '', 'blob').then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '冷链明细.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    this.msgError('导出异常请联系管理员');
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .mb5 {
        margin-bottom: 5px;
    }
    .address-btn {
        height: 26px;
    }
    .border-bottom-1 {
        margin-bottom: 0;
    }
    .el-card__header {
        padding: 10px 20px 0 20px;
    }
    .el-card__body {
        padding: 10px 20px 0;
    }
    .el-form-item {
        align-items: center;
        .el-input__inner {
            --el-input-inner-height: calc(var(--el-input-height, 24px));
        }
    }

    thead th {
        border-right: none !important;
    }

    .el-drawer__header {
        margin-bottom: 20px;
    }

    label.el-radio {
        margin-right: 24px;
    }

    .box-footer {
        padding-left: 8px;
        display: flex;
        justify-content: space-between;
        align-items: end;

        .el-form-item__content {
            margin-left: 0 !important;
        }
    }
    label.el-radio.is-checked span.el-radio__label {
        color: #666666;
    }
}

.boxOrder {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    justify-items: stretch;
    align-items: stretch;
}

.box {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    justify-items: stretch;
    align-items: stretch;
}
.el-form-item {
    margin-bottom: 15px;
}
.box-valueAddedServiceButton {
    display: flex;
    flex-wrap: wrap;
    gap: 0 24px;
}

.mb8 {
    margin-bottom: 8px !important;
}
.icon-dialog {
    ::v-deep .el-dialog {
        .el-dialog__header {
            padding-top: 14px;
        }

        .el-dialog__body {
            margin: 20px;
            padding: 0;
            overflow: auto;
        }

        .el-dialog__footer {
            padding: 10px 20px;
        }
    }
}
</style>
