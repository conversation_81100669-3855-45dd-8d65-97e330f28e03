<template>
    <div>
        <el-dialog v-model="visible" :close-on-click-modal="operationType === 'detail'" :title="invoiceRedInvoicingTitle" width="90%" @close="closeDialog">
            <el-descriptions v-if="operationType === 'detail'" :column="3" border class="mb-10" size="small" title="基本信息">
                <el-descriptions-item label="红字发票申请编号">
                    <span class="whitespace-nowrap">{{ invoiceForm.applyNo }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="申请日期">
                    <span class="whitespace-nowrap">{{ invoiceForm.applyTime }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="申请人">
                    <span class="whitespace-nowrap">{{ invoiceForm.applyUser }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="红冲原因">
                    <span class="whitespace-nowrap">{{ selectDictLabel(reasonList, invoiceForm.reason) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="红字发票费">
                    <span class="whitespace-nowrap">{{ invoiceForm.redInvoiceAmount }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="审核状态">
                    <span class="whitespace-nowrap">{{ selectDictLabel(auditStatusList, invoiceForm.auditStatus) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="开票状态">
                    <span class="whitespace-nowrap">{{ selectDictLabel(redInvoiceFlagList, invoiceForm.redInvoiceFlag) }}</span>
                </el-descriptions-item>
            </el-descriptions>

            <!-- 发票信息区域 -->
            <div v-if="invoiceForm.auditStatus === '1' && invoiceForm.redInvoiceFlag === '1'" class="flex mt-10 w-full">
                <!-- 蓝字发票信息 -->
                <el-descriptions :column="1" border size="small" style="width: 50%; flex-shrink: 0; margin-right: 10px" title="蓝字发票信息">
                    <el-descriptions-item label="发票号码">
                        <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceNo }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票代码">
                        <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceCode }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票客户">
                        <span class="whitespace-nowrap">{{ invoiceForm.invoiceCustomer }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="纳税人识别号">
                        <span class="whitespace-nowrap">{{ invoiceForm.taxNo }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="开票日期">
                        <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceDate }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票类型">
                        <span class="whitespace-nowrap">{{ selectDictLabel(invoiceTypeOptions, invoiceForm.invoiceType) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="开票金额">
                        <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceAmount }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="附件">
                        <el-link v-if="invoiceForm.blueInvoiceFile" type="primary" @click="() => previewInvoiceFile(invoiceForm.blueInvoiceFile)"
                            ><el-icon><download /></el-icon><span style="font-size: 13px">点击下载</span></el-link
                        >
                        <span v-else>--</span>
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 红字发票信息 -->
                <el-descriptions :column="1" border size="small" style="width: 50%; flex-shrink: 0" title="红字发票信息">
                    <el-descriptions-item label="发票号码">
                        <span class="whitespace-nowrap">{{ invoiceForm.redInvoiceNo || '--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票代码">
                        <span class="whitespace-nowrap">{{ invoiceForm.redInvoiceCode || '--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票客户">
                        <span class="whitespace-nowrap">{{ invoiceForm.invoiceCustomer || '--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="纳税人识别号">
                        <span class="whitespace-nowrap">{{ invoiceForm.taxNo || '--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="开票日期">
                        <span class="whitespace-nowrap">{{ invoiceForm.redInvoiceDate || '--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票类型">
                        <span class="whitespace-nowrap">{{ selectDictLabel(invoiceTypeOptions, invoiceForm.invoiceType) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="开票金额">
                        <span class="whitespace-nowrap">{{ invoiceForm.redInvoiceAmount || '--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="附件">
                        <el-link v-if="invoiceForm.redInvoiceFile" type="primary" @click="() => previewInvoiceFile(invoiceForm.redInvoiceFile)" ><el-icon><download /></el-icon><span style="font-size: 13px">点击下载</span></el-link >
                        <el-link v-else-if="invoiceForm.id" type="primary" @click="() => previewFile(invoiceForm)" ><el-icon><download /></el-icon><span style="font-size: 13px">点击下载</span></el-link >
                        <span v-else>--</span>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 未审批通过或未开票通过时只显示蓝字发票信息 -->
            <el-descriptions v-else :column="2" border size="small" title="蓝字发票信息">
                <el-descriptions-item label="发票号码">
                    <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceNo }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="发票代码">
                    <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceCode }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="发票客户">
                    <span class="whitespace-nowrap">{{ invoiceForm.invoiceCustomer }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="纳税人识别号">
                    <span class="whitespace-nowrap">{{ invoiceForm.taxNo }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="开票日期">
                    <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceDate }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="发票类型">
                    <span class="whitespace-nowrap">{{ selectDictLabel(invoiceTypeOptions, invoiceForm.invoiceType) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="开票金额">
                    <span class="whitespace-nowrap">{{ invoiceForm.blueInvoiceAmount }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="附件">
                    <el-link v-if="invoiceForm.blueInvoiceFile" type="primary" @click="() => previewInvoiceFile(invoiceForm.blueInvoiceFile)"
                        ><el-icon><download /></el-icon><span style="font-size: 13px">点击下载</span></el-link
                    >
                </el-descriptions-item>
            </el-descriptions>
            <el-form ref="invoiceRedInvoicingForm" :model="invoiceRedInvoicingForm" :rules="rules" :show-message="false">
                <div v-if="operationType === 'edit'" class="flex items-center mt-10">
                    <el-form-item label="红冲原因:" prop="reason" style="margin-bottom: 0">
                        <el-select v-model="invoiceRedInvoicingForm.reason" clearable filterable placeholder="请选择红冲原因">
                            <el-option v-for="item in reasonList" :key="item.value" :label="item.name" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <span class="ml-10">红字发票费:0.00</span>
                </div>
                <el-descriptions v-if="invoiceRedInvoicingType !== '1'" :title="invoiceRedInvoicingType === '2' ? '付款单明细' : '收款单明细'" class="mt-10" size="small"></el-descriptions>
                <column-table v-if="invoiceRedInvoicingType !== '1'" :columns="paymentOrderApprovalColumns" :data="invoiceForm.docList" :show-summary="true">
                    <template #companyName="{ row }">
                        {{ setCompanyName(row) }}
                    </template>
                    <template #paymentDocType="{ row }">
                        {{ selectDictLabel(paymentDocTypeOptions, row.paymentDocType) }}
                    </template>
                    <template #startDate="{ row }"> {{ formatDate(row.startDate) }} ~ {{ formatDate(row.endDate) }} </template>
                    <template #discountType="{ row }">
                        {{ formatDictionaryData('discountTypeList', row.discountType) }}
                    </template>
                </column-table>
                <el-form-item :style="{ marginBottom: '10px', marginTop: invoiceRedInvoicingType === '1' ? '10px' : '0' }" label="合计开票金额:" prop="invoiceAmount">
                    <span class="whitespace-nowrap text-red-500 font-bold">{{ invoiceForm.redInvoiceAmount }}</span>
                </el-form-item>
                <el-form-item label="详细说明:" prop="detailDesc" style="margin-bottom: 0">
                    <el-input v-if="operationType === 'edit'" v-model="invoiceRedInvoicingForm.detailDesc" maxlength="100" placeholder="请输入详细说明" show-word-limit type="textarea"></el-input>
                    <span v-else>{{ invoiceForm.detailDesc || '-' }}</span>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeDialog">取 消</el-button>
                    <el-button v-if="operationType === 'edit'" type="primary" @click="submitForm">提 交</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import moment from 'moment';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import InvoiceApproval from '@/api/carrierEnd/InvoiceApproval';
import { Download } from '@element-plus/icons-vue';
export default {
    name: 'InvoiceRedInvoicingDetail',
    components: { ColumnTable, Download },
    model: {
        prop: 'invoiceRedInvoicingVisible',
        event: 'update:invoiceRedInvoicingVisible'
    },
    props: {
        invoiceApplyId: {
            type: String,
            default: undefined
        },
        invoiceRedInvoicingId: {
            type: String,
            default: undefined
        },
        /**
         * 来源 owner:货主 carrier:承运商
         */
        invoiceRedInvoicingSource: {
            type: String,
            default: undefined
        },
        invoiceRedInvoicingTitle: {
            type: String,
            default: undefined
        },
        /**
         * 付款类型 1:预存款充值 2:付款单支付 3:收款单收款
         */
        invoiceRedInvoicingType: {
            type: String,
            default: undefined
        },
        invoiceRedInvoicingVisible: {
            type: Boolean,
            default: false
        },
        /**
         * 操作类型 1:编辑 2:详情
         */
        operationType: {
            type: String,
            default: 'edit'
        }
    },
    data() {
        return {
            visible: this.invoiceRedInvoicingVisible,
            invoiceForm: {},
            invoiceRedInvoicingForm: {
                reason: undefined,
                detailDesc: undefined
            },
            rules: {
                reason: [{ required: true, message: '请选择红冲原因', trigger: 'blur' }]
            },
            paymentOrderApprovalColumns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'paymentDocType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'startDate', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '纸箱费用', key: 'cartonFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '垫付费用', key: 'advanceFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '租箱费用', key: 'rentalBoxFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '其他费用', key: 'otherFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '费用说明', key: 'feeDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '坏账总金额', key: 'badDebtCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '操作员', key: 'operator', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '调整日期', key: 'adjustTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true }
            ],
            discountTypeList: [],
            invoiceTypeOptions: [],
            paymentDocTypeOptions: [],
            reasonList: [],
            auditStatusList: [],
            redInvoiceFlagList: []
        };
    },
    computed: {
        /**
         * 格式化日期
         * @returns {function(*): *}
         */
        formatDate() {
            return (value) => (value ? moment(value).format('YYYY-MM-DD') : '-');
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置公司名称
         */
        setCompanyName() {
            return (row) => {
                const Organization = this.$TOOL.data.get('Organization');
                const orgId = Organization[0]?.id;
                return row.carrierId == orgId ? row.companyName : row.companyId == orgId ? row.carrierName : '';
            };
        }
    },
    watch: {
        invoiceRedInvoicingVisible(val) {
            this.visible = val;
        }
    },
    created() {
        // 开票审批和开票申请入口
        if (this.invoiceApplyId) {
            paymentOrderApproval.getInvoiceApprovalDetail({ id: this.invoiceApplyId }).then((res) => {
                if (res.code === 200) {
                    this.invoiceForm.blueInvoiceNo = res.data?.invoiceApply?.invoiceNo;
                    this.invoiceForm.taxNo = res.data?.invoiceApply?.taxNo;
                    this.invoiceForm.blueInvoiceDate = res.data?.invoiceApply?.invoiceDate;
                    this.invoiceForm.invoiceType = res.data?.invoiceApply?.invoiceType;
                    this.invoiceForm.blueInvoiceAmount = res.data?.invoiceApply?.invoiceAmount;
                    // 根据发票类型设置对应的单据列表
                    this.invoiceForm.docList = res.data?.docList;
                    this.invoiceForm.redInvoiceAmount = Number((-res.data?.invoiceApply?.invoiceAmount).toFixed(2));
                    this.invoiceForm.companyId = res.data?.invoiceApply?.companyId;
                    this.invoiceForm.companyName = res.data?.invoiceApply?.companyName;
                    this.invoiceForm.carrierId = res.data?.invoiceApply?.carrierId;
                }
            });
        }
        // 获取红冲详情 红字发票申请入口
        if (this.invoiceRedInvoicingId) {
            InvoiceApproval.getInvoiceRedInvoicingDetail({ id: this.invoiceRedInvoicingId }).then((res) => {
                if (res.code === 200) {
                    this.invoiceForm.id = res.data.id;
                    this.invoiceForm.applyNo = res.data.applyNo;
                    this.invoiceForm.applyTime = res.data.applyTime;
                    this.invoiceForm.applyUser = res.data.applyUser;
                    this.invoiceForm.reason = res.data.reason;
                    this.invoiceForm.invalidType = res.data.invalidType;
                    this.invoiceForm.redInvoiceAmount = res.data.redInvoiceAmount;
                    this.invoiceForm.companyId = res.data.companyId;
                    this.invoiceForm.companyName = res.data.companyName;
                    this.invoiceForm.carrierId = res.data?.carrierId;
                    this.invoiceForm.auditStatus = res.data.auditStatus;
                    this.invoiceForm.redInvoiceFlag = res.data.redInvoiceFlag;
                    this.invoiceForm.blueInvoiceNo = res.data.blueInvoiceNo;
                    this.invoiceForm.blueInvoiceCode = res.data.blueInvoiceCode;
                    this.invoiceForm.invoiceCustomer = res.data.invoiceCustomer;
                    this.invoiceForm.taxNo = res.data.taxNo;
                    this.invoiceForm.blueInvoiceDate = res.data.blueInvoiceDate;
                    this.invoiceForm.invoiceType = res.data.invoiceType;
                    this.invoiceForm.blueInvoiceAmount = res.data.blueInvoiceAmount;
                    this.invoiceForm.blueInvoiceFile = res.data.blueInvoiceFile;
                    this.invoiceForm.detailDesc = res.data.detailDesc;
                    this.invoiceForm.docList = res.data.docList;
                    this.invoiceForm.invoiceApplyId = res.data.invoiceApplyId;
                    this.invoiceForm.redInvoiceNo = res.data.redInvoiceNo;
                    this.invoiceForm.redInvoiceCode = res.data.redInvoiceCode;
                    this.invoiceForm.redInvoiceDate = res.data.redInvoiceDate;
                    this.invoiceForm.redInvoiceAmount = res.data.redInvoiceAmount;
                    this.invoiceForm.redInvoiceFile = res.data.redInvoiceFile;

                    this.invoiceRedInvoicingForm.reason = res.data.reason;
                    this.invoiceRedInvoicingForm.detailDesc = res.data.detailDesc;
                }
            });
        }
        this.getDict();
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.$refs.invoiceRedInvoicingForm?.resetFields();
            this.$emit('close');
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.invoiceTypeOptions = await this.getDictList('collaborating_shipper_invoice_type');
            this.paymentDocTypeOptions = await this.getDictList('cost_payment_doc_type');
            this.reasonList = await this.getDictList('red_reason');
            this.auditStatusList = await this.getDictList('payment_approval_type');
            this.redInvoiceFlagList = await this.getDictList('red_invoice_result');
            this.discountTypeList = await this.getDictList('discount_type');
        },
        /**
         * 预览发票文件
         */
        previewInvoiceFile(fileUrl) {
            if (fileUrl) {
                window.open(fileUrl);
            } else {
                this.$message.error('文件不存在');
            }
        },
        /**
         * 预览红字发票文件
         */
        previewFile(invoiceForm) {
            if (invoiceForm.id) {
                InvoiceApproval.getInvoiceRedInvoicingList({ applyId: invoiceForm.id }).then((res) => {
                    if (res.code === 200) {
                        window.open(res.data);
                    } else {
                        this.$message.error('文件地址不存在');
                    }
                });
            } else {
                this.$message.error('未找到红冲发票');
            }
        },
        selectDictLabel,
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.invoiceRedInvoicingForm?.validate((valid, fields) => {
                if (valid) {
                    // 构建提交参数
                    const submitParams = {
                        ...this.invoiceRedInvoicingForm,
                        invoiceMoney: '0.0',
                        redInvoiceAmount: this.invoiceForm.redInvoiceAmount,
                        invoiceApplyId: this.invoiceApplyId || this.invoiceForm.invoiceApplyId
                    };

                    // 根据来源添加公司信息
                    if (this.invoiceRedInvoicingSource === 'carrier') {
                        submitParams.companyId = this.invoiceForm?.companyId;
                        submitParams.companyName = this.invoiceForm?.companyName;
                    } else {
                        submitParams.carrierId = this.invoiceForm?.carrierId;
                    }

                    // 编辑模式下添加id和单号
                    if (this.operationType === 'edit') {
                        submitParams.id = this.invoiceForm?.id;
                        submitParams.applyNo = this.invoiceForm?.applyNo;
                    }

                    this.$emit('submit', submitParams);
                } else {
                    if (fields.reason) {
                        this.$message.error(fields.reason[0].message);
                    }
                }
            });
        }
    },
    emits: ['update:invoiceRedInvoicingVisible', 'close', 'submit']
};
</script>

<style scoped>
.dialog-footer {
    text-align: right;
    padding-top: 20px;
}
</style>
