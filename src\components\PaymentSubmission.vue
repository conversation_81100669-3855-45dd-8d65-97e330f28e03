<template>
    <el-drawer v-model="visible" size="600px" title="提交付款信息" @close="onClose">
        <div class="p-10" style="background-color: #f2f2f2">
            <el-card shadow="never">
                <el-form ref="applyPrepaymentForm" :model="form" :rules="rules" label-width="auto">
                    <el-form-item label="付款公司" prop="companyName">
                        <el-input v-model="form.companyName" :disabled="form.paymentDocType !== '4'" class="w-full" clearable placeholder="请输入付款公司" />
                    </el-form-item>
                    <el-form-item label="收款公司" prop="receiveCompany">
                        <el-select v-model="form.receiveCompany" :disabled="form.paymentDocType === '3' || form.paymentDocType === '5'" class="w-full" clearable filterable placeholder="请选择收款公司">
                            <el-option v-for="dict in companyList" :key="dict.code" :label="dict.name" :value="dict.code" />
                        </el-select>
                    </el-form-item>
                    <div class="grid" style="grid-template-columns: minmax(0, 0.8fr) minmax(0, 1fr); grid-gap: 10px">
                        <el-form-item label="汇款金额" prop="remitAmount">
                            <el-input-number 
                                v-model="form.remitAmount" 
                                :max="9999999.99" 
                                :min="0" 
                                :precision="2"
                                :step="0.01"
                                :controls="true"
                                class="number__unit__element w-full" 
                                controls-position="right" 
                                placeholder="请输入汇款金额"
                                @input="handleAmountInput"
                                @change="handleAmountChange"
                            />
                        </el-form-item>
                        <el-form-item label="汇款时间" prop="remitTime">
                            <el-date-picker v-model="form.remitTime" class="w-full" placeholder="请选择汇款时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                        </el-form-item>
                    </div>
                    <el-form-item label="付款凭证" prop="remitFile">
                        <el-upload ref="uploadFile" :action="uploadFileUrl" :headers="headers" :limit="9" :on-remove="handleRemove" :on-success="handleSuccess" :on-preview="handlePreview" accept="image/*" list-type="picture-card">
                            <el-icon><Plus /></el-icon>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="form.remark" class="w-full" clearable maxlength="200" placeholder="请输入备注" show-word-limit type="textarea" />
                    </el-form-item>
                </el-form>
            </el-card>
            <div class="flex justify-end mt-10">
                <el-button @click="onClose">取 消</el-button>
                <el-button type="primary" @click="onSubmit">提 交</el-button>
            </div>
        </div>
        <el-dialog v-model="previewVisible" title="图片预览" width="50%">
            <img :src="previewUrl" style="width: 100%" />
        </el-dialog>
    </el-drawer>
</template>

<script>
import {Plus} from '@element-plus/icons-vue';
import tool from '@/utils/tool';

export default {
    name: 'PaymentSubmission',
    components: {
        Plus
    },
    props: {
        modelValue: {
            type: Boolean,
            default: false
        },
        initialFormData: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['update:modelValue', 'submit', 'close'],
    data() {
        return {
            form: {
                companyName: '',
                receiveCompany: '',
                remitAmount: null,
                remitTime: '',
                remitFile: [],
                paymentDocType: ''
            },
            rules: {
                companyName: [{ required: true, message: '请选择付款公司', trigger: 'change' }],
                receiveCompany: [{ required: true, message: '请选择收款公司', trigger: 'change' }],
                remitAmount: [{ required: true, message: '请输入汇款金额', trigger: 'change' }],
                remitTime: [{ required: true, message: '请选择汇款时间', trigger: 'change' }],
                remitFile: [{ required: true, message: '请上传付款凭证', trigger: 'change' }]
            },
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            companyList: [],
            previewVisible: false,
            previewUrl: ''
        };
    },
    computed: {
        visible: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    watch: {
        initialFormData: {
            handler(newVal) {
                const formData = { ...newVal };
                if (formData.remitAmount !== undefined && formData.remitAmount !== null) {
                    formData.remitAmount = Number(Number(formData.remitAmount).toFixed(2));
                }
                this.form = { ...this.form, ...formData };

                if (this.form.paymentDocType === '3' || this.form.paymentDocType === '5') {
                    const defaultCompany = this.companyList[0];
                    if (defaultCompany) {
                        this.form.receiveCompany = defaultCompany.code;
                    }
                }
            },
            immediate: true,
            deep: true
        },
        'form.remitAmount': {
            handler(val) {
                if (val !== null && val !== undefined) {
                    this.form.remitAmount = Number(Number(val).toFixed(2));
                }
            }
        }
    },
    created() {
        this.getDict();
    },
    methods: {
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
        },
        handleRemove(file) {
            this.form.remitFile = this.form.remitFile.filter((item) => item.fileUrl !== file.fileUrl);
        },
        handleSuccess(res) {
            const fileUrl = res.data.fileUrl;
            const exists = this.form.remitFile.some(item => item.fileUrl === fileUrl);
            
            if (!exists) {
                const currentForm = { ...this.form };
                this.form = {
                    ...currentForm,
                    remitFile: [
                        ...currentForm.remitFile,
                        {
                            fileUrl: fileUrl,
                            fileName: res.data.fileName
                        }
                    ]
                };
            }
        },
        onClose() {
            this.form.remitFile = [];
            this.$refs.applyPrepaymentForm?.resetFields();
            this.$refs.uploadFile?.clearFiles();
            this.$emit('close');
            this.visible = false;
        },
        onSubmit() {
            this.$refs.applyPrepaymentForm.validate((valid) => {
                if (valid) {
                    const formData = { 
                        ...this.form,
                        remitAmount: this.form.remitAmount !== null ? Number(Number(this.form.remitAmount).toFixed(2)) : null
                    };
                    this.$emit('submit', formData);
                }
            });
        },
        handleAmountInput(value) {
            if (value !== null && value !== undefined) {
                const formattedValue = Number(value).toFixed(2);
                this.form.remitAmount = Number(formattedValue);
            }
        },
        handleAmountChange(value) {
            if (value !== null && value !== undefined) {
                const formattedValue = Number(value).toFixed(2);
                this.form.remitAmount = Number(formattedValue);
            }
        },
        /**
         * 处理图片预览
         * @param {Object} file - 当前预览的文件对象
         */
        handlePreview(file) {
            this.previewUrl = file.url || file.fileUrl;
            this.previewVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.number__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
