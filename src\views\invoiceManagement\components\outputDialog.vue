<template>
  <div>
    <p v-if="!formFlag">开票申请编号
      <el-select v-model="backForm.search" :placeholder="formFlag ? ' ' : '请选择开票申请编号'" filterable multiple
                 style="width: 50%;margin-left: 15px">
        <el-option v-for="item in options" :key="item.id" :label="item.applyNo" :value="item.applyNo"/>
      </el-select>
      <el-button v-show="!formFlag" style="margin-left: 15px" type="primary" @click="addFn">添加</el-button>
    </p>
    <el-table :cell-style="{ textAlign: 'center' }" :data="tableData" :header-cell-style="{ 'text-align': 'center' }"
              style="width: 100%;margin-top: 15px">
      <el-table-column :show-overflow-tooltip="true" label="开票申请编号" prop="applyNo"/>
      <el-table-column label="申请类型" prop="billingType">
        <template #default="scope">
          {{ scope.row.billingType == '0' ? '销售出库' : '销退入库' }}
        </template>
      </el-table-column>
      <el-table-column label="发票客户" prop="customer.enterpriseName" width="180px"/>
      <el-table-column :label="typeDetail == 0 ? '开票金额(含税)' : '开票金额'" prop="totalPriceTax">
        <template #default="scope">
          {{
            typeDetail == 0 ? Number(scope.row.totalPriceTax).toFixed(2) : Number(scope.row.invoiceAmount).toFixed(2)
          }}
        </template>
      </el-table-column>
      <el-table-column :label="typeDetail == 0 ? '可开票金额(含税)' : '可开票金额'" prop="" width="190px">
        <template #default="scope">
          <el-input-number v-if="!formFlag" v-model="scope.row.amount" :max="scope.row.amountMax" :min="0"
                           :placeholder="'请输入开票金额'" :precision="2" :step="1" :value-on-clear="0" size="small"
                           style="width: 150px;"
                           @change="countFn"/>
          <p v-if="formFlag">{{ Number(scope.row.amount).toFixed(2) }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票类型" prop="invoiceType">
        <template #default="scope">
          {{ echo(scope.row.invoiceType) }}
        </template>
      </el-table-column>
      <el-table-column label="申请人" prop="applyBy.name"/>
      <el-table-column label="申请日期" prop="applyDate">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.createDate) }}
        </template>
      </el-table-column>
      <el-table-column v-if="!formFlag" align="center" label="操作" prop="createDate">
        <template #default="scope">
          <el-button text type="primary" @click="delIn(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <table v-if="formFlag" border="0" cellpadding="0" cellspacing="1" class="messTable">
      <tr>
        <td>发票客户</td>
        <td>{{ backForm.n6 }}</td>
      </tr>
      <tr>
        <td>纳税人识别号</td>
        <td>{{ backForm.n7 }}</td>
      </tr>
      <tr>
        <td>发票类型</td>
        <td>{{ echo(backForm.n9) }}</td>
      </tr>
      <tr>
        <td>发票号码</td>
        <td>{{ backForm.n1 }}</td>
      </tr>
      <tr>
        <td>发票代码</td>
        <td>{{ backForm.n2 }}</td>
      </tr>
      <tr>
        <td>开票日期</td>
        <td>{{ functionIndex.transformTimestamp(backForm.n3) }}</td>
      </tr>
      <tr>
        <td>{{ typeDetail == 0 ? '开票金额(含税)' : '开票金额' }}</td>
        <td>{{ Number(backForm.n4).toFixed(2) }}</td>
      </tr>
      <tr>
        <td>上传附件</td>
        <td>
          <p v-for="(item, index) in backForm.n5" :key="index"
             :style="backForm.n5.length == 1 ? 'color:#2a75f6;cursor: pointer;' : 'color:#2a75f6;cursor: pointer;line-height: 25px'"
             style="overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:15em" @click="handlePreview(item)">
            {{
              item.name
            }}</p>
        </td>
      </tr>
      <tr style="border-left: 1px solid #e9ecf1"></tr>
    </table>
    <el-form v-if="!formFlag" ref="creatform" :disabled="formFlag" :inline="true" :model="backForm" :rules="creatRules"
             class="form_130" label-width="120px" style="margin-top: 20px">
      <el-form-item :prop="formFlag ? null : 'n6'" label="发票客户">
        <el-input v-model="backForm.n6" :disabled="true" :placeholder="formFlag ? ' ' : '请选择发票自动填入'"
                  class="form_225"
                  clearable style="width: 100%;"/>
      </el-form-item>
      <el-form-item :prop="formFlag ? null : 'n7'" label="纳税人识别号">
        <el-input v-model="backForm.n7" :disabled="true" :placeholder="formFlag ? ' ' : '请选择发票自动填入'"
                  class="form_225"
                  clearable style="width: 100%;"/>
      </el-form-item>
      <el-form-item :prop="formFlag ? null : 'n9'" label="发票类型">
        <el-input :disabled="true" :model-value="echo(backForm.n9)" :placeholder="formFlag ? ' ' : '请选择发票自动填入'"
                  class="form_225" clearable style="width: 100%;"/>
      </el-form-item>
      <el-form-item :prop="formFlag ? null : 'n1'" label="发票号码">
        <el-input v-model="backForm.n1" :placeholder="formFlag ? ' ' : '请输入发票号码'" class="form_225" clearable
                  maxlength="20"
                  show-word-limit style="width: 100%;"
                  @input="(val) => { backForm.n1 = backForm.n1.replace(/[^0-9.]/g, '') }"/>
      </el-form-item>
      <el-form-item :prop="formFlag ? null : 'n2'" label="发票代码">
        <el-input v-model="backForm.n2" :placeholder="formFlag ? ' ' : '请输入发票代码'" class="form_225" clearable
                  maxlength="20"
                  show-word-limit style="width: 100%;"
                  @input="(val) => { backForm.n2 = backForm.n2.replace(/[^0-9.]/g, '') }"/>
      </el-form-item>
      <el-form-item :prop="formFlag ? null : 'n3'" label="开票日期">
        <el-date-picker v-model="backForm.n3" :placeholder="formFlag ? ' ' : '请选择开票日期'" class="form_225"
                        size="default"
                        style="width: 100%;" type="date" value-format="YYYY-MM-DD HH:mm:ss"/>
      </el-form-item>
      <el-form-item :prop="formFlag ? null : 'n4'" label="开票金额(含税)">
        <el-input :disabled="true" :model-value="Number(backForm.n4).toFixed(2)"
                  :placeholder="formFlag ? ' ' : '请选择发票自动填入'"
                  class="form_225" clearable style="width: 100%;"/>
      </el-form-item>
      <br/>
      <el-form-item :prop="formFlag ? null : 'n5'" label="上传附件">
        <el-upload v-model:file-list="backForm.n5" :before-upload="(file) => beforeFile(file)" :data="uploadData"
                   :headers="headers" :limit="0" :on-error="() => errorFile()" :on-preview="handlePreview"
                   :on-remove="removeFn"
                   :on-success="(response) => successFile(response)" action="/file/uploadd" class="upload-demo"
                   multiple>
          <el-button v-show="!formFlag" type="primary">点击上传</el-button>
          <template #tip>
            <div v-show="!formFlag" class="el-upload__tip">
              文件大小不大于5MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect, getCurrentInstance, watch} from 'vue';
import tool from "@/utils/tool";
import {ElMessage, ElMessageBox} from "element-plus";
import {applocation, outPut} from "@/api/model/invoice";
import {functionIndex} from "../../salesManagement/functionIndex";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const data = reactive({
  imgUrl: [],
  checkFlag: false
})
const {proxy} = getCurrentInstance();
const delFilesUrl = ref([])
const removeFn = (UploadFile) => {
  if (UploadFile.resFileUrl || UploadFile.url) {
    delFilesUrl.value.push(UploadFile.resFileUrl || UploadFile.url)
  }
}
const emit = defineEmits([])
const formFlag = ref(false)
const creatform = ref(); //验证表单/流程新增
const creatRules = reactive({
  n1: [{required: true, message: "请输入发票号码", trigger: "blur"}],
  n2: [{required: true, message: "请输入发票代码", trigger: "blur"}],
  n3: [{required: true, message: "请选择开票日期", trigger: "blur"}],
  n4: [{required: true, message: "请输入开票金额", trigger: "blur"}],
  n5: [{required: true, message: "请上传文件", trigger: "blur"}],
  n6: [{required: true, message: "请输入发票客户", trigger: "blur"}],
  n7: [{required: true, message: "请输入纳税人识别号", trigger: "blur"}],
  n9: [{required: true, message: "请选择发票类型", trigger: "blur"}],
});
const invoiceType = ref()
const typeDetail = ref(1)
const tableData = ref([])
const backForm = ref({
  n1: "",
  n2: "",
  n3: "",
  n4: 0,
  n5: [],
  search: [],
  n6: "",
  n7: "",
  n8: "",
  n9: "",
})
const upload = ref()
const list = ref([])
const options = ref([])
const loading = ref(false)
const close = () => {
  data.checkFlag = false;
  data.imgUrl = []
}
const echo = (value) => {
  return invoiceType.value.find(item => item.value == value)?.name || ''
}
const maxPrice = ref(0)
const countFn = () => {
  backForm.value.n4 = 0
  tableData.value.forEach(item => {
    backForm.value.n4 += item.amount
  })
  maxPrice.value = backForm.value.n4
}
const addFn = () => {
  backForm.value.search.forEach(item => {
    if (!tableData.value.find(items => items.applyNo === item)) {
      applocation.getList({
        size: 1000,
        applyNo: item,
        // status:3 //审核通过
      }).then(res => {
        if (res.code == 200) {
          if (tableData.value.length > 0) {
            if (tableData.value[0].customer.id == res.data.records[0].customer.id) {
              if (tableData.value[0].billingType == res.data.records[0].billingType) {
                if (tableData.value[0].invoiceType == res.data.records[0].invoiceType) {
                  tableData.value.push(res.data.records[0])
                  if (!tableData.value[tableData.value.length - 1].invoicedAmount) {
                    tableData.value[tableData.value.length - 1].amount = tableData.value[tableData.value.length - 1].totalPriceTax
                    tableData.value[tableData.value.length - 1].amountMax = tableData.value[tableData.value.length - 1].totalPriceTax
                  } else {
                    tableData.value[tableData.value.length - 1].amount = tableData.value[tableData.value.length - 1].totalPriceTax - tableData.value[tableData.value.length - 1].invoicedAmount
                    tableData.value[tableData.value.length - 1].amountMax = tableData.value[tableData.value.length - 1].amount
                  }
                  backForm.value.n6 = res.data.records[0].customer.enterpriseName
                  backForm.value.n7 = res.data.records[0].customer.socialCreditCode
                  backForm.value.n8 = res.data.records[0].customer.id
                  backForm.value.n9 = res.data.records[0].invoiceType
                  countFn()
                } else {
                  ElMessage.error('请选择相同发票类型')
                }
              } else {
                ElMessage.error('请选择相同申请类型')
              }
            } else {
              ElMessage.error('请选择相同客户')
            }
          } else {
            tableData.value.push(res.data.records[0])
            typeDetail.value = res.data.records[0].billingType
            if (typeDetail.value == 0) {
              if (!tableData.value[tableData.value.length - 1].invoicedAmount) {
                tableData.value[tableData.value.length - 1].amount = tableData.value[tableData.value.length - 1].totalPriceTax
                tableData.value[tableData.value.length - 1].amountMax = tableData.value[tableData.value.length - 1].totalPriceTax
              } else {
                tableData.value[tableData.value.length - 1].amount = tableData.value[tableData.value.length - 1].totalPriceTax - tableData.value[tableData.value.length - 1].invoicedAmount
                tableData.value[tableData.value.length - 1].amountMax = tableData.value[tableData.value.length - 1].amount
              }
            } else {
              if (!tableData.value[tableData.value.length - 1].invoicedAmount) {
                tableData.value[tableData.value.length - 1].amount = tableData.value[tableData.value.length - 1].invoiceAmount
                tableData.value[tableData.value.length - 1].amountMax = tableData.value[tableData.value.length - 1].invoiceAmount
              } else {
                tableData.value[tableData.value.length - 1].amount = tableData.value[tableData.value.length - 1].invoiceAmount - tableData.value[tableData.value.length - 1].invoicedAmount
                tableData.value[tableData.value.length - 1].amountMax = tableData.value[tableData.value.length - 1].amount
              }
            }

            backForm.value.n6 = res.data.records[0].customer.enterpriseName
            backForm.value.n7 = res.data.records[0].customer.socialCreditCode
            backForm.value.n8 = res.data.records[0].customer.id
            backForm.value.n9 = res.data.records[0].invoiceType
            countFn()
          }
        } else {
          ElMessage.error(item + '添加失败')
        }
      })
    }
  })
  console.log(tableData.value)
}
const props = defineProps({
  UUID: {
    default: null
  }
})
const delIn = (row) => {
  ElMessageBox.confirm("确认删除此项吗?(删除后不可撤销)", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        if (row.oldList) {
          outPut.delInvoice({ids: row.delId}).then((res) => {
            if (res.code == 200) {
              ElMessage.success('删除成功')
              tableData.value.forEach((item, index) => {
                if (item.applyNo === row.applyNo) {
                  tableData.value.splice(index, 1)
                }
                countFn()
              })
            } else {
              ElMessage.error('删除失败，错误信息', res.msg)
            }
          })
        } else {
          tableData.value.forEach((item, index) => {
            if (item.applyNo === row.applyNo) {
              tableData.value.splice(index, 1)
            }
            countFn()
          })
        }
      })
      .catch(() => {
      });
}
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  applocation.getList({
    size: 1000,
  }).then(res => {
    options.value = res.data.records
  })
  let typeLo = JSON.parse(window.localStorage.getItem('erp_invoice'))

  if (typeLo) {
    invoiceType.value = typeLo
  } else {
    invoiceType.value = await proxy.getDictList("invoice_type")
  }
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  formFlag,
  creatform,
  backForm,
  tableData,
  delFilesUrl,
  typeDetail
})
const uploadData = ref(null)
const beforeFile = (file) => {
  if (file.size > 5242880) {
    ElMessage.error("文件不能大于5M");
    return false;
  }
  uploadData.value = {
    uuid: props.UUID,
    fileGroup: "invoiceManagement",
    businessType: "outputInvoice",
    fileType: 51
  }
}
const headers = {
  Authorization: "Bearer " + tool.cookie.get("TOKEN"),
  ContentType: "multipart/form-data",
  clientType:'pc',
};
const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    applocation.getList({
      size: 1000,
      applyNo: query
    }).then(res => {
      loading.value = false
      options.value = res.data.records
    })
  } else {
    options.value = []
  }
}
const errorFile = () => {
  ElMessage.error("上传失败");
};
const handlePreview = (uploadFile) => {
  const fileName = uploadFile.name.split(".")
  data.imgUrl = []
  if (
      fileName[fileName.length - 1] == "gif" ||
      fileName[fileName.length - 1] == "jpeg" ||
      fileName[fileName.length - 1] == "apng" ||
      fileName[fileName.length - 1] == "jpg" ||
      fileName[fileName.length - 1] == "avif" ||
      fileName[fileName.length - 1] == "png" ||
      fileName[fileName.length - 1] == "svg" ||
      fileName[fileName.length - 1] == "webp"
  ) {
    data.checkFlag = true;
    data.imgUrl.push(uploadFile.resFileUrl)
  }
}
const successFile = (res) => {
  ElMessage.success("上传成功");
  backForm.value.n5[backForm.value.n5.length - 1].resFileUrl = res.data.fileUrl
  backForm.value.n5[backForm.value.n5.length - 1].newFile = true
};
</script>
<style lang='scss' scoped>
::v-deep .el-input.is-disabled .el-input__wrapper {
  background: none !important;
  box-shadow: none !important;
  color: #000 !important;
}

::v-deep .el-input.is-disabled .el-input__icon {
  display: none !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
  -webkit-text-fill-color: #000 !important;
}

::v-deep .el-upload {
  display: inline;
}

::v-deep .el-upload-list {
  margin-top: 0px;
}

.messTable {
  width: 100%;
  background-color: #eaedf3;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 1px 1px 0 1px;
  margin-top: 20px;

  tr {
    margin-bottom: 1px;
    display: flex;
    background: #fff;

    td {
      background-color: white;
      line-height: 40px;
    }

    td:nth-child(1) {
      flex: 1;
      padding: 0 10px;
      font-weight: bold;
      color: #505050;
      background: #f7f7f7;
    }

    td:nth-child(2) {
      color: #606266;
      padding: 0 10px;
      flex: 2
    }
  }
}
</style>
