<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form" label-width="auto">
                <el-form-item label="参数类型" prop="parameterType">
                    <el-select v-model="queryParams.parameterType" clearable placeholder="请选择" @change="handleQuery">
                        <el-option v-for="item in paramTypeList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="产品分类" prop="productType">
                    <el-select v-model="queryParams.productType" clearable placeholder="请选择" @change="handleQuery">
                        <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户" prop="companyId">
                    <el-select v-model="queryParams.companyId" clearable placeholder="请选择" @change="handleQuery">
                        <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button type="primary" @click="openBillingParameters">批量新增</el-button>
                <el-button :disabled="multiple" type="primary" @click="openChargingParameterModification('batch')">批量修改</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="billingParameterCarrierTable" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="orderList" :showCheckBox="true" :showIndex="true" :max-height="600" :showOverflowTooltip="false" @selection-change="handleSelectionChange">
                <template #parameterType="{ row }">
                    <span>{{ formatDictionaryData('paramTypeList', row.parameterType) }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                </template>
                <template #scoped="{ row }">
                    <span>{{ formatDictionaryData('scopedList', row.scoped) }}</span>
                </template>
                <template #status="{ row }">
                    <el-switch v-model="row.status" size="small" :before-change="() => handleStatusChange(row)" active-color="#13ce66" active-text="启用" active-value="1" inactive-text="禁用" inactive-value="0" inline-prompt></el-switch>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-edit" link size="small" type="warning" @click="openChargingParameterModification('single', row)">修改</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- /计费参数 抽屉  -->
        <el-drawer v-model="billingParameterVisible" :title="billingParameterTitle" size="1040px" @close="hideBillingParameter">
            <div v-loading="billingParameterLoading" :element-loading-text="billingParameterLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="billingParameterForm" :inline="true" :model="billingParameterForm" :rules="billingParameterRules" class="billingParameterForm" label-width="auto">
                        <div class="">
                            <el-form-item label="参数类型" prop="parameterType">
                                <el-select v-model="billingParameterForm.parameterType" clearable placeholder="请选择" @change="handleBillingParameterFormChange">
                                    <el-option v-for="item in paramTypeList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="产品分类" prop="productType">
                                <el-select v-model="billingParameterForm.productType" clearable placeholder="请选择" @change="getBillingParamList">
                                    <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                            <el-form-item v-if="billingParameterForm.parameterType == 2" label="客户" prop="companyId">
                                <el-select v-model="billingParameterForm.companyId" clearable filterable placeholder="请选择" @change="getBillingParamList">
                                    <el-option v-for="item in collaboratedShippersList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                                </el-select>
                            </el-form-item>
                            <el-form-item v-if="billingParameterForm.parameterType" label="作用域" prop="scoped">
                                <el-radio-group v-model="billingParameterForm.scoped" @change="getBillingParamList">
                                    <el-radio v-for="item in scopedList" :key="item.code" :label="item.code">{{ item.name }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </div>
                        <column-table v-loading="billingParameterTableLoading" :columns="billingParameterTableColumns" :data="billingParameterTableData" :showIndex="true">
                            <template #variableValue="{ row }">
                                <el-input v-model="row.variableValue" />
                            </template>
                            <template #status="{ row }">
                                <el-switch v-model="row.status" active-color="#13ce66" active-text="启用" active-value="1" inactive-text="禁用" inactive-value="0" inline-prompt />
                            </template>
                        </column-table>
                        <pagination v-show="billingParameterForm.total > 0" v-model:limit="billingParameterForm.size" v-model:page="billingParameterForm.current" :total="billingParameterForm.total" class="mb16" @pagination="getBillingParamList" />
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideBillingParameter">取消</el-button>
                    <el-button type="primary" @click="confirmToAdd">确定</el-button>
                </div>
            </div>
        </el-drawer>

        <!-- /计费参数修改 抽屉  -->
        <el-drawer v-model="modificationOfBillingParametersVisible" :title="modificationOfBillingParametersTitle" size="80vw" @close="hideModificationOfBillingParameters">
            <div style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <column-table v-loading="modificationOfBillingParametersTableLoading" :columns="modificationOfBillingParametersTableColumns" :data="modificationOfBillingParametersTableData" :showIndex="true">
                        <template #parameterType="{ row }">
                            <el-select v-model="row.parameterType" clearable placeholder="请选择" @change="handleModificationOfBillingParametersTableChange(row)">
                                <el-option v-for="item in paramTypeList" :key="item.code" :label="item.name" :value="item.code" />
                            </el-select>
                        </template>
                        <template #productType="{ row }">
                            <el-select v-model="row.productType" clearable placeholder="请选择">
                                <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code" />
                            </el-select>
                        </template>
                        <template #companyId="{ row }">
                            <el-select v-model="row.companyId" :disabled="row.parameterType != 2" clearable filterable placeholder="请选择">
                                <el-option v-for="item in collaboratedShippersList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                            </el-select>
                        </template>
                        <template #variableValue="{ row }">
                            <el-input v-model="row.variableValue" />
                        </template>
                        <template #status="{ row }">
                            <el-switch v-model="row.status" active-color="#13ce66" active-text="启用" active-value="1" inactive-text="禁用" inactive-value="0" inline-prompt size="small"></el-switch>
                        </template>
                    </column-table>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideModificationOfBillingParameters">取消</el-button>
                    <el-button type="primary" @click="confirmModification">修改</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import billingParameterConfiguration from '@/api/carrierEnd/billingParameterConfiguration';
import { selectDictLabel } from '@/utils/dictLabel';
import RightToolbar from '@/components/RightToolbar/index.vue';
import enterpriseCooperation from "@/api/logisticsConfiguration/enterpriseCooperation";

export default {
    name: 'BillingParameterConfiguration',
    components: {
        RightToolbar,
        SearchButton,
        ColumnTable
    },
    data() {
        return {
            showSearch: true,
            queryParams: {
                current: 1,
                size: 10,
                parameterType: null,
                productType: null,
                companyId: null
            },
            paramTypeList: [],
            goodsTypeList: [],
            customerList: [],
            collaboratedShippersList: [],
            columns: [
                { title: '变量名称', key: 'variableName', align: 'center', width: '200px', columnShow: true, fixed: 'left' },
                { title: '参数类型', key: 'parameterType', align: 'center', width: '100px', columnShow: true },
                { title: '产品分类', key: 'productType', align: 'center', width: '100px', columnShow: true },
                { title: '作用域', key: 'scoped', align: 'center', width: '145px', columnShow: true },
                { title: '变量值', key: 'variableValue', align: 'center', width: '100px', columnShow: true },
                { title: '单位', key: 'variableUnit', align: 'center', width: '100px', columnShow: true },
                { title: '变量说明', key: 'remark', minWidth: '120px', align: 'center', columnShow: true },
                { title: '客户', key: 'companyName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '状态', key: 'status', align: 'center', width: '80px', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true }
            ],
            loading: false,
            total: 0,
            orderList: [],
            billingParameterTitle: '新增计费参数',
            billingParameterVisible: false,
            billingParameterLoading: false,
            billingParameterLoadingText: '加载中',
            billingParameterForm: {
                parameterType: '',
                productType: '',
                companyId: '',
                scoped: '1',
                size: 10,
                current: 1,
                total: 0
            },
            billingParameterRules: {
                parameterType: [{ required: true, message: '请选择参数类型', trigger: 'blur' }],
                productType: [{ required: true, message: '请选择货物类型', trigger: 'blur' }],
                companyId: [{ required: false, message: '请选择客户', trigger: 'blur' }],
                scoped: [{ required: true, message: '请选择作用域', trigger: 'blur' }]
            },
            billingParameterModify: false,
            billingParameterTableLoading: false,
            billingParameterTableColumns: [
                { title: '变量名称', key: 'variableName', align: 'center', width: '200px', columnShow: true, fixed: 'left' },
                { title: '变量值', key: 'variableValue', align: 'center', columnShow: true },
                { title: '单位', key: 'variableUnit', width: '120px', align: 'center', columnShow: true },
                { title: '变量说明', key: 'remark', align: 'center', columnShow: true },
                { title: '状态', key: 'status', align: 'center', width: '120px', columnShow: true }
            ],
            billingParameterTableData: [],
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            modificationOfBillingParametersTitle: '修改计费参数',
            modificationOfBillingParametersVisible: false,
            modificationOfBillingParametersTableLoading: false,
            modificationOfBillingParametersTableColumns: [
                { title: '变量名称', key: 'variableName', align: 'center', width: '200px', columnShow: true, fixed: 'left' },
                { title: '变量值', key: 'variableValue', align: 'center', columnShow: true },
                { title: '参数类型', key: 'parameterType', align: 'center', columnShow: true },
                { title: '产品分类', key: 'productType', align: 'center', columnShow: true },
                { title: '客户', key: 'companyId', width: '200px', align: 'center', columnShow: true },
                { title: '变量说明', key: 'remark', width: '200px', align: 'center', columnShow: true },
                { title: '状态', key: 'status', align: 'center', width: '100px', columnShow: true }
            ],
            modificationOfBillingParametersTableData: [],
            scopedList: [],
            isShowAll: false
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    created() {
        this.getDict();
        this.getCollaboratedShippersList();
        this.getList();
    },
    methods: {
        confirmModification() {
            this.modificationOfBillingParametersTableLoading = true;

            const isNull = this.modificationOfBillingParametersTableData.some((item) => {
                return !item.variableValue;
            });
            if (isNull) {
                this.$message.error('请填写变量值');
                this.modificationOfBillingParametersTableLoading = false;
                return;
            }

            billingParameterConfiguration
                .branchEdit(this.modificationOfBillingParametersTableData)
                .then((res) => {
                    if (res.code === 200) {
                        this.$message.success('修改成功');
                        this.getList();
                        this.hideModificationOfBillingParameters();
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .finally(() => {
                    this.modificationOfBillingParametersTableLoading = false;
                });
        },
        confirmToAdd() {
            this.$refs.billingParameterForm.validate((valid) => {
                if (valid) {
                    this.billingParameterLoading = true;
                    this.billingParameterLoadingText = '新增中...';
                    // this.billingParameterTableData 数组中的 parameterType productType companyId 三个字段 使用 this.billingParameterForm 中的值 如果为空 则不传
                    const isNull = this.billingParameterTableData.some((item) => {
                        return !item.variableValue;
                    });
                    if (isNull) {
                        this.$message.error('请填写变量值');
                        this.billingParameterLoading = false;
                        return;
                    }
                    this.billingParameterTableData.forEach((item) => {
                        item.parameterType = this.billingParameterForm.parameterType || item.parameterType;
                        item.productType = this.billingParameterForm.productType || item.productType;
                        item.companyId = this.billingParameterForm.companyId || item.companyId;
                        item.billingParametersId = this.billingParameterForm.id || item.id;
                        item.id = '';
                        item.scoped = this.billingParameterForm.scoped;
                    });

                    billingParameterConfiguration
                        .addBillingParameterCarrier(this.billingParameterTableData)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success(res.data);
                                this.getList();
                                this.hideBillingParameter();
                            }
                        })
                        .finally(() => {
                            this.billingParameterLoading = false;
                        });
                }
            });
        },
        // 查询未设置的参数
        getBillingParamList() {
            this.billingParameterTableLoading = true;
            billingParameterConfiguration
                .getBillingParamList(this.billingParameterForm)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.billingParameterTableData = res.data || [];
                        this.billingParameterForm.total = res.total || 0;
                    }
                })
                .finally(() => {
                    this.billingParameterTableLoading = false;
                });
        },
        /**
         * 获取已合作的货主列表
         */
        getCollaboratedShippersList() {
            enterpriseCooperation
                .cooperateSelect({ status: '1' })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.collaboratedShippersList = res.data;
                    }
                })
                .catch(() => {});
        },
        /**
         * 获取客户列表
         */
        getCustomerList() {
            billingParameterConfiguration.customerList().then((res) => {
                if (res.code === 200 && res.data) {
                    this.customerList = res.data;
                }
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.scopedList = await this.getDictList('cost_parameters_scoped');
            this.paramTypeList = await this.getDictList('cost_parameter_type');
        },
        getList() {
			this.getCustomerList();
            this.loading = true;
            billingParameterConfiguration
                .billingParameterListCarrier(this.queryParams)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.orderList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        getSelectedParams() {
            this.billingParameterTableLoading = true;
            getSelectedParams()
                .then((res) => {
                    if (res.code === 200) {
                        this.billingParameterTableData = res.data || [];
                        // this.billingParameterTableData 长度大于0 属性中新增 variableValue 和 status
                        // if (this.billingParameterTableData.length > 0) {
                        //   this.billingParameterTableData.forEach((item) => {
                        //     item.variableValue = '';
                        //     item.status = '0';
                        //   });
                        // }
                        console.log(this.billingParameterTableData);
                        this.billingParameterForm.total = res.data.length;
                    }
                })
                .finally(() => {
                    this.billingParameterTableLoading = false;
                });
        },
        handleBillingParameterFormChange(e) {
            if (e === '1') {
                this.billingParameterRules.companyId = [{ required: false, message: '请选择客户', trigger: 'change' }];
            } else {
                this.billingParameterRules.companyId = [{ required: true, message: '请选择客户', trigger: 'change' }];
            }
            // 重置货物类型
            this.billingParameterForm.productType = '';
            // 重置表格
            this.billingParameterTableData = [];
            this.billingParameterForm.total = 0;
        },
        handleModificationOfBillingParametersTableChange(row) {
            // 清掉 companyId
            row.companyId = '';
        },
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            if (selection) {
                // 遍历 selection中的 id status 赋值给 this.selectData
                this.selectData = selection;
                this.single = selection.length !== 1;
                this.multiple = !selection.length;
            }
        },
        /**
         * 状态切换
         * @param row
         */
        handleStatusChange(row) {
            const { id, status } = row;

            // 取反操作，如果是0则变成1，如果是1则变成0
            const updatedStatus = status === '0' ? '1' : '0';

            billingParameterConfiguration.changeStatus({ id, status: updatedStatus }).then((res) => {
                if (res.code === 200) {
                    this.$message.success('操作成功');
                    this.getList();
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        hideBillingParameter() {
            this.billingParameterVisible = false;
            this.$refs.billingParameterForm.resetFields();
            this.billingParameterTableData = [];
        },
        hideModificationOfBillingParameters() {
            this.modificationOfBillingParametersVisible = false;
        },
        openBillingParameters() {
            this.billingParameterVisible = true;
            this.billingParameterTitle = '新增计费参数';
            // this.getSelectedParams();
        },
        /**
         * 打开计费参数修改抽屉
         * @param type batch 批量修改 single 单个修改
         * @param data
         */
        openChargingParameterModification(type, data) {
            if (type === 'single') {
                this.modificationOfBillingParametersTableData = [];
                this.modificationOfBillingParametersTableData.push(data);
            } else {
                this.modificationOfBillingParametersTableData = JSON.parse(JSON.stringify(this.selectData));
            }
            this.modificationOfBillingParametersVisible = true;
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleQuery();
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
}

.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.dialog__deleteValueAddedService {
    ::v-deep {
        .el-dialog__header {
            padding-bottom: 20px;
        }

        .el-result {
            padding: 0;
        }
    }
}
</style>
