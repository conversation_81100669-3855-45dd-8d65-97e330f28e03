<!--  1. 保温箱管理 -->
<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form">
                <el-form-item label="设备编号" prop="serialNumber" style="width: 250px">
                    <el-input v-model="queryParams.serialNumber" clearable placeholder="请输入设备编号" />
                </el-form-item>
                <el-form-item label="设备状态" prop="status" style="width: 250px">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择设备状态">
                        <el-option v-for="dict in deviceStatusList" :key="dict.id" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="使用状态" prop="operateStatus" style="width: 250px">
                    <el-select v-model="queryParams.operateStatus" clearable placeholder="请选择使用状态">
                        <el-option v-for="dict in useList" :key="dict.dictValue" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" id="collapse" label="类型" prop="type">
                    <el-select v-model="queryParams.type" clearable placeholder="请选择类型">
                        <el-option v-for="dict in deviceList" :key="dict.id" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" id="collapse" label="生产厂家" prop="manufacturer">
                    <el-input v-model="queryParams.manufacturer" clearable placeholder="请输入生产厂家" />
                </el-form-item>
                <el-form-item v-show="isShowAll" id="collapse" label="验证日期" style="width: 330px">
                    <el-date-picker v-model="queryParams.verificationDate" end-placeholder="结束日期" range-separator="-" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" id="collapse" label="验证有效期至" style="width: 330px">
                    <el-date-picker v-model="queryParams.incubatorValidTime" end-placeholder="结束日期" range-separator="-" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" id="collapse" label="验证结果" prop="incubatorCheckResult">
                    <el-select v-model="queryParams.incubatorCheckResult">
                        <el-option v-for="item in verifyTypeList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                    <!-- <el-input v-model="queryParams.verificationResult" placeholder="请输入验证结果" clearable /> -->
                </el-form-item>
                <!-- <el-form-item label="效期状态" prop="validityStauts" v-show="isShowAll" id="collapse">
                            <el-select v-model="queryParams.validityStauts" placeholder="请选择类型" clearable>
                                <el-option v-for="dict in validityTypeList" :key="dict.id" :label="dict.name"
                                    :value="dict.code" />
                            </el-select>
                        </el-form-item> -->
                <search-button :is-show-all="isShowAll" @handleQuery="getList(1)" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" icon="el-icon-plus" type="primary" @click="handleAdd">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:remove']" icon="el-icon-delete" type="danger" @click="handleDeletes">删除</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:export']" icon="el-icon-download" type="warning" @click="handleExport">导出</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:edit']" icon="el-icon-edit" type="primary" @click="downloadTemplate">下载模版</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:edit']" icon="el-icon-edit" type="primary" @click="uploadTemplate">上传EXCEL导入</el-button>
                    </el-col>
                    <!-- <right-toolbar :showSearch="showSearch" @queryTable="getList"></right-toolbar> -->
                </el-row>
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>

            <el-table :data="incubatorList" border style="margin-top: 15px" @selection-change="handleSelectionChange">
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column align="center" label="保温箱名称" prop="name" width="120" />
                <el-table-column align="center" label="保温箱编号" prop="serialNumber" width="120" />
                <el-table-column :formatter="(row) => formDict(deviceStatusList, row.status)" align="center" label="设备状态" prop="status" />
                <el-table-column :formatter="(row) => formDict(useList, row.operateStatus)" align="center" label="使用状态" prop="operateStatus" />
                <el-table-column :formatter="(row) => formDict(deviceList, row.type)" align="center" label="类型" prop="type" />
                <el-table-column align="center" label="规格" min-width="200" prop="format" show-overflow-tooltip/>
                <el-table-column align="center" label="容量" prop="capacity" />
                <el-table-column align="center" label="内径长度 (mm)" prop="indLength" width="120" />
                <el-table-column align="center" label="内径宽度 (mm)" prop="indWidth" width="120" />
                <el-table-column align="center" label="内径高度 (mm)" prop="indHeight" width="120" />
                <el-table-column align="center" label="外径长度 (mm)" prop="exdLength" width="120" />
                <el-table-column align="center" label="外径宽度 (mm)" prop="exdWidth" width="120" />
                <el-table-column align="center" label="外径高度 (mm)" prop="exdHeight" width="120" />
                <el-table-column align="center" label="生产厂家" min-width="160" prop="manufacturer" show-overflow-tooltip/>
                <el-table-column :formatter="(row) => formDict(validityTypeList, row.validityStauts)" align="center" label="效期状态" prop="validityStauts" />
                <el-table-column align="center" label="效期时间" prop="calibrationValidity" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.calibrationValidity ? moment(scope.row.calibrationValidity).format('YYYY-MM-DD HH:mm:ss') : '暂无' }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="附件URL" prop="fileUrl" show-overflow-tooltip/>
                <el-table-column align="center" label="备注" min-width="160" prop="remark" show-overflow-tooltip/>
                <el-table-column align="center" class-name="small-padding fixed-width" fixed="right" label="操作" width="210" show-overflow-tooltip>
                    <template #default="scope">
                        <el-button v-hasPermi="['incubator:incubator:remove']" icon="el-icon-edit" link size="small" type="primary" @click="testVerify(scope.row)">验证</el-button>
                        <el-button v-hasPermi="['incubator:incubator:edit']" icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(scope.row)">修改</el-button>
                        <el-button v-hasPermi="['incubator:incubator:remove']" icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
        <!-- 添加或修改device 保温箱对话框 -->
        <div v-show="open">
            <el-drawer v-model="open" :title="title" append-to-body size="650">
                <div style="background-color: #f2f2f2; padding: 10px">
                    <el-card shadow="never">
                        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                            <el-form-item class="define" label="保温箱名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入设备名称" />
                            </el-form-item>
                            <el-form-item class="define" label="保温箱编号" prop="serialNumber">
                                <el-input v-model="form.serialNumber" placeholder="请输入设备编号" />
                            </el-form-item>
                            <el-form-item class="define" label="类型" prop="type">
                                <el-select v-model="form.type" placeholder="请选择类型" style="width: 430px">
                                    <el-option v-for="dict in deviceList" :key="dict.id" :label="dict.name" :value="dict.code"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item class="define" label="规格" prop="format">
                                <el-input v-model="form.format" placeholder="请输入规格" />
                            </el-form-item>
                            <el-form-item class="define" label="容量" prop="capacity">
                                <el-input v-model="form.capacity" placeholder="请输入容量" />
                            </el-form-item>
                            <el-form-item class="define" label="设备厂家" prop="manufacturer">
                                <el-input v-model="form.manufacturer" placeholder="请输入生产厂家" />
                            </el-form-item>
                            <el-form-item class="define" label="设备状态" prop="status">
                                <el-radio-group v-model="form.status">
                                    <el-radio v-for="dict in deviceStatusList" :key="dict.id" :label="dict.code">{{ dict.name }}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item class="define" label="使用状态" prop="operateStatus">
                                <el-select v-model="form.operateStatus" clearable placeholder="请选择使用状态" style="width: 430px">
                                    <el-option v-for="dict in useList" :key="dict.id" :label="dict.name" :value="dict.code" />
                                </el-select>
                            </el-form-item>
                            <el-form-item class="define" label="内径长度 (mm)" prop="indLength">
                                <el-input v-model="form.indLength" placeholder="请输入内径长度 (mm)" />
                            </el-form-item>
                            <el-form-item class="define" label="内径宽度 (mm)" prop="indWidth">
                                <el-input v-model="form.indWidth" placeholder="请输入内径宽度 (mm)" />
                            </el-form-item>
                            <el-form-item class="define" label="内径高度 (mm)" prop="indHeight">
                                <el-input v-model="form.indHeight" placeholder="请输入内径高度 (mm)" />
                            </el-form-item>
                            <el-form-item class="define" label="外径长度 (mm)" prop="exdLength">
                                <el-input v-model="form.exdLength" placeholder="请输入外径长度 (mm)" />
                            </el-form-item>
                            <el-form-item class="define" label="外径宽度 (mm)" prop="exdWidth">
                                <el-input v-model="form.exdWidth" placeholder="请输入外径宽度 (mm)" />
                            </el-form-item>
                            <el-form-item class="define" label="外径高度 (mm)" prop="exdHeight">
                                <el-input v-model="form.exdHeight" placeholder="请输入外径高度 (mm)" />
                            </el-form-item>
                            <el-form-item class="define" label="附件URL" prop="fileUrl">
                                <el-input v-model="form.fileUrl" placeholder="请输入附件" />
                            </el-form-item>
                            <el-form-item class="define" label="备注" prop="remark">
                                <el-input v-model="form.remark" maxlength="60" placeholder="请输入备注" />
                            </el-form-item>
                        </el-form>
                    </el-card>
                </div>
				<template #footer>
					<el-button @click=" open = false; form = {};">取 消</el-button>
					<el-button type="primary" @click="submitForm">确 定</el-button>
				</template>
            </el-drawer>
        </div>

        <!-- 验证 -->
        <div v-show="calibrationOpen">
            <el-drawer v-model="calibrationOpen" :title="title" append-to-body size="1200">
                <el-card class="box-card Botm">
                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 14px">
                        <div style="display: flex">
                            <div>
                                保温箱名称： <span>{{ calibrationList.name }}</span>
                            </div>
                            <div style="margin-left: 30px">
                                保温箱编号：<span>{{ calibrationList.serialNumber }}</span>
                            </div>
                        </div>
                        <el-button type="primary" @click="calibrationAdd">添加</el-button>
                    </div>
                </el-card>
                <div style="font-size: 14px; margin-top: 10px">
                    <span>验证记录</span>
                    <el-table :data="calData" border style="width: 100%">
                        <el-table-column label="验证日期" prop="checkTime" />
                        <el-table-column label="验证有效期至" prop="validTime" />
                        <el-table-column label="验证人" prop="checkBy" />
                        <el-table-column label="验证人电话" prop="checkByPhone" width="120" />
                        <el-table-column label="验证时长" prop="precoolHour" width="80" />
                        <el-table-column label="外包装" prop="isPacking" width="80">
                            <template #default="scope">
                                <span>{{ scope.row.isPacking ? '是' : '否' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :formatter="(row) => formDict(verifyTypeList, row.checkResult)" label="验证结果" prop="checkResult" width="80" />
                        <el-table-column label="备注" prop="remark" />
                        <el-table-column align="center" label="操作" width="50">
                            <template #default="scope">
                                <el-button link type="danger" @click="deleteJSON(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="drawer-footer1">
					<el-button @click="calibrationOpen = false">取 消</el-button>
                    <!-- <el-button type="primary" @click="submitCalibration">确 定</el-button> -->
                </div>
            </el-drawer>
        </div>
        <el-dialog v-model="dialogVisible" :title="title" width="30%">
            <div style="text-align: center">
                <el-form ref="verifyForm" :model="verifyList" :rules="rulesing" label-width="120px">
                    <el-form-item label="验证日期" prop="checkTime">
                        <el-date-picker v-model="verifyList.checkTime" placeholder="请选择验证日期" style="width: 380px" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="验证有效期至" prop="validTime">
                        <el-date-picker v-model="verifyList.validTime" placeholder="请选择" style="width: 380px" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="验证人" prop="checkBy" style="width: 500px">
                        <el-input v-model="verifyList.checkBy" maxlength="20" placeholder="请输入验证人" />
                    </el-form-item>
                    <el-form-item label="验证人电话" prop="checkByPhone" style="width: 500px">
                        <el-input v-model="verifyList.checkByPhone" placeholder="请输入验证人电话" />
                    </el-form-item>
                    <el-form-item label="预冷时长/h" prop="precoolHour" style="width: 500px">
                        <el-input v-model="verifyList.precoolHour" placeholder="请输入预冷时长" @input="validateInput" />
                    </el-form-item>
                    <el-form-item label="外包装" prop="isPacking">
                        <el-select v-model="verifyList.isPacking" style="width: 380px">
                            <el-option v-for="item in externalList" :key="item.id" :label="item.name" :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="验证结果" prop="checkResult">
                        <el-select v-model="verifyList.checkResult" style="width: 380px">
                            <el-option v-for="item in verifyTypeList" :key="item.id" :label="item.name" :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark" style="width: 500px">
                        <el-input v-model="verifyList.remark" autosize maxlength="60" placeholder="请输入备注" type="textarea" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click=" dialogVisible = false; verifyList = [];">取消</el-button>
                    <el-button type="primary" @click="verifyRecordAdd">确定</el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog v-model="uploadOpen" :title="title" width="30%">
            <div style="text-align: center">
                <el-upload
                    ref="upload"
                    v-model:file-list="fileList"
                    :action="uploadUrl"
                    :before-remove="beforeRemove"
                    :headers="headers"
                    :limit="1"
                    :on-exceed="(files) => handleExceed(files, upload)"
                    :on-preview="handlePreview"
                    :on-progress="uploadVideoProcess"
                    :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, index)"
                    class="upload-demo"
                    drag
                >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">请拖拽此处或<em>点击上传</em></div>
                    <template #tip>
                        <div class="el-upload__tip">最大文件上传为500kb</div>
                    </template>
                </el-upload>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="uploadAdd">确定</el-button>
                    <el-button type="primary" @click="uploadOpen = false"> 取消 </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import moment from 'moment';
import { UploadFilled } from '@element-plus/icons-vue';
// import RightToolbar from '@/components/RightToolbar';
import incubatorApi from '@/api/management/incubator';
import { verifyPhone } from '@/utils/verificate';
import tool from '@/utils/tool';
import qs from 'qs';
import { genFileId } from 'element-plus';
import SearchButton from '@/components/searchModule/SearchButton.vue';

const { proxy } = getCurrentInstance();

// 查询参数
const queryParams = ref({
    current: 1,
    size: 10,
    total: 0
});
const open = ref(false);
// 显示搜索条件
const showSearch = ref(true);
const title = ref('');

/** 新增按钮操作 */
function handleAdd() {
    // reset();
    open.value = true;
    title.value = '添加保温箱';
}

const form = ref({
    status: '1'
});

// 表单重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    getList();
}

/** 修改按钮操作 */
function handleUpdate(data) {
    form.value = JSON.parse(JSON.stringify(data));
    open.value = true;
    title.value = '修改保温箱';
}

const isShowAll = ref(false);

function showAllClick() {
    isShowAll.value = !isShowAll.value;
}

// 淡出效果
function fadeOut(element) {
    let opacity = 1;
    let fadeEffect = setInterval(function() {
        if (opacity > 0) {
            opacity -= 0.1;
            element.style.opacity = opacity;
        } else {
            clearInterval(fadeEffect);
            element.style.display = 'none';
        }
    }, 10);
}

// 淡入效果
function fadeIn(element) {
    let opacity = 0;
    element.style.display = 'block';
    let fadeEffect = setInterval(function() {
        if (opacity < 1) {
            opacity += 0.1;
            element.style.opacity = opacity;
        } else {
            clearInterval(fadeEffect);
        }
    }, 15);
}

const incubatorList = ref([]);
// 查询设备列表数据
function getList(val) {
    if (val == 1) {
        if (queryParams.value.verificationDate) {
            if (queryParams.value.verificationDate.length > 0) {
                queryParams.value.beginIncubatorCheckTime = queryParams.value.verificationDate[0];
                queryParams.value.endIncubatorCheckTime = queryParams.value.verificationDate[1];
            }
        }
        if (queryParams.value.incubatorValidTime) {
            if (queryParams.value.incubatorValidTime.length > 0) {
                queryParams.value.beginIncubatorValidTime = queryParams.value.incubatorValidTime[0];
                queryParams.value.endIncubatorValidTime = queryParams.value.incubatorValidTime[1];
            }
        }
        incubatorApi
            .assetIncubatorList({
                serialNumber: queryParams.value.serialNumber,
                status: queryParams.value.status,
                operateStatus: queryParams.value.operateStatus,
                type: queryParams.value.type,
                manufacturer: queryParams.value.manufacturer,
                beginIncubatorCheckTime: queryParams.value.beginIncubatorCheckTime,
                endIncubatorCheckTime: queryParams.value.endIncubatorCheckTime,
                beginIncubatorValidTime: queryParams.value.beginIncubatorValidTime,
                endIncubatorValidTime: queryParams.value.endIncubatorValidTime,
                verificationResult: queryParams.value.verificationResult,
                validityStauts: queryParams.value.validityStauts,
                incubatorCheckResult: queryParams.value.incubatorCheckResult
            })
            .then((res) => {
                if (res.code == 200) {
                    incubatorList.value = res.data.records;
                    queryParams.value.total = res.data.total;
                }
            });
    } else {
        incubatorApi.assetIncubatorList(queryParams.value).then((res) => {
            if (res.code == 200) {
                incubatorList.value = res.data.records;
                queryParams.value.total = res.data.total;
            }
        });
    }
}

// 表单验证
const rules = reactive({
    name: [{ required: true, message: '请输入保温箱名称', trigger: 'blur' }],
    serialNumber: [{ required: true, message: '请输入保温箱编号', trigger: 'blur' }],
    type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    format: [{ required: true, message: '请输入规格', trigger: 'blur' }],
    capacity: [{ required: true, message: '请输入容量', trigger: 'blur' }, { pattern: /^(\d)+$/, message: '容量只能输入正整数', trigger: 'blur' } ,{
        validator: (rule, value, callback) => {
            if (value && value <= 0) {
                callback(new Error('容量只能输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur'
    }],
    manufacturer: [{ required: true, message: '请输入生产厂家', trigger: 'blur' }],
    status: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
    operateStatus: [{ required: true, message: '请选择使用状态', trigger: 'change' }],
    indLength:[{ pattern: /^(\d)+$/, message: '内径长度只能输入正整数', trigger: 'blur' },{
        validator: (rule, value, callback) => {
            if (value && value <= 0) {
                callback(new Error('内径长度只能输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur'
    }],
    indWidth:[{ pattern: /^(\d)+$/, message: '内径宽度只能输入正整数', trigger: 'blur' },{
        validator: (rule, value, callback) => {
            if (value && value <= 0) {
                callback(new Error('内径宽度只能输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur'
    }],
    indHeight:[{ pattern: /^(\d)+$/, message: '内径高度只能输入正整数', trigger: 'blur' },{
        validator: (rule, value, callback) => {
            if (value && value <= 0) {
                callback(new Error('内径高度只能输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur'
    }],
    exdLength:[{ pattern: /^(\d)+$/, message: '外径长度只能输入正整数', trigger: 'blur' },{
        validator: (rule, value, callback) => {
            if (value && value <= 0) {
                callback(new Error('外径长度只能输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur'
    }],
    exdWidth:[{ pattern: /^(\d)+$/, message: '外径宽度只能输入正整数', trigger: 'blur' },{
        validator: (rule, value, callback) => {
            if (value && value <= 0) {
                callback(new Error('外径宽度只能输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur'
    }],
    exdHeight:[{ pattern: /^(\d)+$/, message: '外径高度只能输入正整数', trigger: 'blur' },{
        validator: (rule, value, callback) => {
            if (value && value <= 0) {
                callback(new Error('外径高度只能输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur'
    }],
});
const rulesing = reactive({
    checkTime: [{ required: true, message: '请选择验证日期', trigger: 'change' }],
    validTime: [{ required: true, message: '请选择验证有效期至', trigger: 'change' }],
    checkBy: [{ required: true, message: '请输入验证人', trigger: 'change' }],
    checkByPhone: [
        { required: true, message: '请输入验证人电话', trigger: 'blur' },
        { validator: verifyPhone, trigger: 'blur' }
    ],
    precoolHour: [
        { required: true, message: '请输入预冷时长', trigger: 'blur' },
        { validator: validateNumber, trigger: 'blur' }
    ],
    isPacking: [{ required: true, message: '请选择外包装', trigger: 'change' }],
    checkResult: [{ required: true, message: '请选择验证结果', trigger: 'change' }]
});

// 保存设备
function submitForm() {
    proxy.$refs['formRef'].validate((valid) => {
        if (valid) {
            incubatorApi
                .assetIncubatorSave(form.value)
                .then((res) => {
                    if (res.code == 200) {
                        if (form.value.id) {
                            proxy.msgSuccess('修改成功');
                            form.value = {};
                        } else {
                            proxy.msgSuccess('新增成功');
                        }
                        getList();
                        open.value = false;
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        }
    });
}

// 时长验证
function validateNumber(rule, value, callback) {
    // 正则表达式，匹配正数，小数点后一位
    const regex = /^\d+(\.\d{1})?$/;
    if (regex.test(value)) {
        callback(); // 验证通过
    } else {
        callback(new Error('请输入正数，小数点后一位')); // 验证失败
    }
}

function validateInput() {
    // 在输入时也进行验证
    proxy.$refs.input.validate();
}

// 删除
function handleDelete(row) {
    proxy
        .$confirm('是否确认删除保温箱' + row.serialNumber + '吗?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            incubatorApi
                .assetIncubatorDelete({
                    ids: row.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        proxy.msgSuccess('删除成功');
                        getList();
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        })
        .catch(() => {
        });
}

// 删除多条
function handleDeletes() {
    if (chooseList.value.length > 0) {
        let ids = chooseList.value.map((item) => item.id).join(',');
        proxy
            .$confirm('是否确认删除所选保温箱吗?', '提示', {
                type: 'warning',
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            })
            .then(() => {
                incubatorApi
                    .assetIncubatorDelete({
                        ids: ids
                    })
                    .then((res) => {
                        if (res.code == 200) {
                            proxy.msgSuccess('删除成功');
                            getList();
                        }
                    })
                    .catch((err) => {
                        proxy.msgError(err.msg);
                    });
            })
            .catch(() => {
            });
    } else {
        proxy.msgError('请选择需要导出的数据！');
    }
}

//验证
const calibrationOpen = ref(false);
const calibrationList = ref({});

function testVerify(row) {
    calibrationList.value = row;
    calibrationOpen.value = true;
    assetIncubatorCheckList();
}

// 保温箱验证列表
const calData = ref([]);

function assetIncubatorCheckList() {
    incubatorApi
        .assetIncubatorCheckList({
            'assetIncubator.id': calibrationList.value.id
        })
        .then((res) => {
            if (res.code == 200) {
                calData.value = res.data.records;
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

// 添加保温箱验证
const dialogVisible = ref(false);

function calibrationAdd() {
    title.value = '验证记录';
    dialogVisible.value = true;
}

// 删除保温箱验证记录assetIncubatorCheckDelete
function deleteJSON(row) {
    proxy
        .$confirm('是否确认删除该条数据吗?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            incubatorApi.assetIncubatorCheckDelete({ ids: row.id }).then((res) => {
                if (res.code === 200) {
                    proxy.msgSuccess('删除成功');
                    assetIncubatorCheckList();
                } else {
                    proxy.msgError('删除失败');
                }
            });
        })
        .catch(() => {
        });
}

// 确定提交验证记录
const verifyList = ref({});

function verifyRecordAdd() {
    proxy.$refs['verifyForm'].validate((valid) => {
        if (valid) {
            incubatorApi
                .assetIncubatorCheckSave({
                    checkBy: verifyList.value.checkBy,
                    isPacking: verifyList.value.isPacking ? true : false,
                    remark: verifyList.value.remark,
                    checkResult: verifyList.value.checkResult,
                    checkTime: verifyList.value.checkTime,
                    checkByPhone: verifyList.value.checkByPhone,
                    validTime: verifyList.value.validTime,
                    precoolHour: parseInt(verifyList.value.precoolHour),
                    assetIncubator: {
                        'id': calibrationList.value.id
                    }
                })
                .then((res) => {
                    if (res.code == 200) {
                        proxy.msgSuccess('新增成功');
                        assetIncubatorCheckList();
                        verifyList.value = [];
                        dialogVisible.value = false;
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        }
    });
}

// 下载模版
function downloadTemplate() {
    incubatorApi
        .importTemplate('', '', '', 'blob')
        .then((res) => {
            var debug = res;
            if (debug) {
                var elink = document.createElement('a');
                elink.download = '保温箱导入模版.xlsx';
                elink.style.display = 'none';
                var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                elink.href = URL.createObjectURL(blob);
                document.body.appendChild(elink);
                elink.click();
                document.body.removeChild(elink);
            } else {
                this.$message.error('导出异常请联系管理员');
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

const chooseList = ref([]);
const newFilArr = ref([]);
const handleSelectionChange = (key) => {
    chooseList.value = key;
};

// 导出
function handleExport() {
    if (chooseList.value.length > 0) {
        let list = {
            // id: selectIds,
            filename: '报警信息记录'
            // exportFields: []
        };
        incubatorApi
            .assetIncubatorExport(qs.stringify(list, { arrayFormat: 'repeat' }), '', '', 'blob')
            .then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '保温箱导出记录.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    this.$message.error('导出异常请联系管理员');
                }
            })
            .catch((err) => {
                proxy.msgError(err.msg);
            });
    } else {
        proxy.msgError('请选择需要导出的数据！');
    }
}

// 上传
const uploadOpen = ref(false);

function uploadTemplate() {
    uploadOpen.value = true;
    title.value = '上传';
}

const uploadUrl = '/device/assetIncubator/import';
const videoUploadPercent = ref();
const fileList = ref([]);
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
    ContentType: 'multipart/form-data',
    clientType: 'pc'
};

function uploadVideoProcess(event, file, fileList) {
    videoUploadPercent.value = Math.floor(event.percent);
}

// 点击已经上传的文件触发
const handlePreview = (uploadFile) => {
    console.log(uploadFile);
};
const uploadSucceed = ref(0);
const uploadFail = ref(0);
// 删除之前触发
const beforeRemove = (uploadFile, uploadFiles) => {
    return proxy
        .$confirm(`是否确认删除${uploadFile.name}?`, '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            if (uploadSucceed.value >= 1) {
                uploadSucceed.value -= 1;
            }
            if (uploadFail.value >= 1) {
                uploadFail.value -= 1;
            }
            uploadDiv2.value = false;
        })
        .catch(() => {
        });
};
const uploadDiv = ref(false);
const uploadDiv2 = ref(false);
const failInfo = ref([]);
const infoValue = ref('');
const resultValue = ref('');
const itemcount = ref(0);
// 超出限制时触发
const handleExceed = (files, ref) => {
    ref.clearFiles();
    const file = files[0];
    file.uid = genFileId();
    ref.handleStart(file);
    ref.submit();
};
//文件上传成功
const handleUploadSuccess = (res, file, fileList, index, type) => {
    if (res.code == 200) {
        var result = JSON.parse(res.data);
        fileList.value = [{ ...res.data }];
        uploadDiv.value = true;
        uploadDiv2.value = false;
        var info = [result.info.split(',').join(',')];
        failInfo.value.push(info);
        infoValue.value = failInfo.value[failInfo.value.length - 1].join(',');
        uploadSucceed.value = 0;
        uploadFail.value = 0;
        uploadSucceed.value += Number(result.successNum);
        uploadFail.value += Number(result.failNum);
        if (result.result == '失败') {
            resultValue.value = result.result;
            uploadDiv2.value = true;
            itemcount.value += 1;
        }
    } else {
        uploadDiv2.value = false;
        proxy.msgError(res.msg);
    }
};

function uploadAdd() {
    proxy.msgSuccess('导入成功');
    getList();
    uploadOpen.value = false;
}

//字典回显
const formDict = (data, val) => {
    return data && val ? proxy.selectDictLabel(data, val) : '--';
};
// 设备状态
const deviceStatusList = ref([]);
// 使用状态
const useList = ref([]);
// 设备类型
const deviceList = ref([]);
// 外包装
const externalList = ref([]);
// 验证结果
const verifyTypeList = ref([]);
// 效期状态
const validityTypeList = ref([]);
// 字典请求
const getDict = async () => {
    deviceStatusList.value = await proxy.getDictList('incubator_status');
    useList.value = await proxy.getDictList('incubator_use_status');
    deviceList.value = await proxy.getDictList('Incubator_type');
    externalList.value = await proxy.getDictList('external_type');
    verifyTypeList.value = await proxy.getDictList('verify_type');
    validityTypeList.value = await proxy.getDictList('incubator_validity_status');
};
getDict();

getList();
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.define {
    width: 550px;
}

.drawer-footer1 {
    position: absolute;
    bottom: 20px;
    left: 600px;
}
</style>
