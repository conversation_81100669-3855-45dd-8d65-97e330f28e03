<!-- 订单详情滑块-->
<template>
  <div>
    <el-drawer :title="title" v-model="open" size="1100px" @close="onClose">
      <div class="p-16 orderDetails" style="background-color: #f5f7fd">
        <el-radio-group v-model="type" style="margin-bottom: 15px" @change="tabClick">
          <el-radio-button label="1">详情</el-radio-button>
          <el-radio-button label="2">轨迹</el-radio-button>
          <!-- <el-radio-button label="3">异动</el-radio-button> -->
          <el-radio-button label="4">支付</el-radio-button>
          <el-radio-button label="5">温度记录</el-radio-button>
          <el-radio-button label="6">异动记录</el-radio-button>
        </el-radio-group>
        <div v-if="type == 1">
          <el-card class="mb10" shadow="never">
            <div class="box-top">
              <div>
                <span>订单号：</span>
                <span @click="copyOrderNumber">{{ info.orderNo }}</span>
              </div>
              <div>
                <span>运单号：</span>
                <span>{{ info.transOrderNo }}</span>
              </div>
              <div v-if="orderInfo.handCode">
                <span>承运单号：</span>
                <span>{{ orderInfo.handCode }}</span>
              </div>
            </div>
          </el-card>

          <!--货物揽收方式 付款方式-->
          <el-card class="mb10" shadow="never" v-if='info.orderType && info.paymentMethod'>
            <div class="box-top">
              <div>
                <span>货物揽收方式：</span>
                <span>{{ formatTheWayTheGoodsAreCollected(info.orderType) }}</span>
              </div>
              <div>
                <span>付款方式：</span>
                <span>{{ formatThePaymentMethod(info.paymentMethod) }}</span>
              </div>
            </div>
          </el-card>

          <!-- /订单信息 货物信息-->
          <div class="box-orderInformation">
            <!--  /订单信息-->
            <el-card class="mb10" shadow="never">
              <div slot="header">
                <div class="titleLayout">
                  <span class="verticalBar"></span>
                  <span class="title">订单信息</span>
                </div>
              </div>
              <div class="d-box-content">
                <div class="d-box-column">
                  <div class="d-box-info">
                    <span>发件地址</span>
                    <span>{{ info.sendTown.province + info.sendTown.city + info.sendTown.county + info.sendTown.town
                    }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>详细地点</span>
                    <span>{{ info.sendAddress }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>发件联系人</span>
                    <span>{{ info.sendUser }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>发件人电话</span>
                    <span>{{ info.sendUserPhone }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>发件公司</span>
                    <span>{{ info.sendCompany }}</span>
                  </div>
                </div>
                <div class="d-box-column">
                  <div class="d-box-info">
                    <span>收件地址</span>
                    <span>{{ info.receiverTown.province + info.receiverTown.city + info.receiverTown.county +
                      info.receiverTown.town }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>详细地点</span>
                    <span>{{ info.receiverAddress }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>收件人名称</span>
                    <span>{{ info.receiverUser }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>收件人电话</span>
                    <span>{{ info.receiverUserPhone }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>所属客户公司</span>
                    <span>{{ info.receiverCompany }}</span>
                  </div>
                </div>
              </div>
            </el-card>
            <!--  /货物信息-->
            <el-card class="mb10" shadow="never">
              <div slot="header">
                <div class="titleLayout">
                  <span class="verticalBar"></span>
                  <span class="title">货物信息</span>
                </div>
              </div>
              <div class="d-box-content">
                <div class="d-box-column">
                  <div class="d-box-info">
                    <span>运输类型</span>
                    <span>{{ fourplProductTypeFormat(info.productType) }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>产品分类</span>
                    <span>{{ info.goodsName }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>件数</span>
                    <span>{{ info.goodsPackages }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>重量</span>
                    <span>{{ info.productWeight }}kg</span>
                  </div>
                  <div class="d-box-info">
                    <span>容积</span>
                    <span>{{ info.productVolume }}升</span>
                  </div>
                </div>
                <div class="d-box-column">
                  <div class="d-box-info">
                    <span>温层类型</span>
                    <span>{{ info.temperatureType.describtion }}</span>
                  </div>
                  <div class="d-box-info" v-if="info.carType">
                    <span>车辆类型</span>
                    <span>{{ forMatterCar(info.carType) }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>公里数</span>
                    <span>{{ info.kilometre }}</span>
                  </div>
                  <div class="d-box-info">
                    <span>货品属性</span>
                    <span>{{ forMatterProperties(info.productProperties) }}</span>
                  </div>
                  <div class="d-box-info" v-if="info.externalOrderNo">
                    <span>随货同行单号</span>
                    <span>{{ info.externalOrderNo }}</span>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mb10" shadow="never">
              <div slot="header">
                <div class="titleLayout">
                  <span class="verticalBar"></span>
                  <span class="title">签收信息</span>
                </div>
              </div>
              <div class="d-box-content">
                <div>
                  <p style="margin: -5px 0px 10px 15px;">回执单</p>
                  <div v-for="img in hzFileList">
                    <el-image :src="img.fileUrl" :preview-src-list="hzFileList.map(item => item.fileUrl)"
                      style="width: 100px; height: 100px;margin-left:30px" class="ml10 mb10" />
                  </div>
                </div>
                <div>
                  <p style="margin: -5px 0px 10px 405px;">客户签名</p>
                  <div v-for="img in khqmFileList" :key="img">
                    <el-image :preview-src-list="khqmFileList.map(item => item.fileUrl)" :src="img.fileUrl"
                      style="width: 100px; height: 100px; margin-left:420px" />
                  </div>
                </div>
              </div>
            </el-card>
          </div>
          <!-- /冷链货品明细-->
          <el-card v-if="info.lenlianDetail && info.lenlianDetail.length > 0" class="mb10" shadow="never">
            <div slot="header">
              <div class="titleLayout">
                <span class="verticalBar"></span>
                <span class="title">冷链货品明细</span>
              </div>
            </div>
            <div style="text-align: right">
              <el-dropdown @command="printColdChainHandover">
                <el-button plain size="mini" type="primary"> 打印冷链交接单<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="(item, index) in coldChainHandoverType" :key="index" :command="item.dictValue">
                    {{ item.dictLabel }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <el-table :data="info.lenlianDetail" :fit="true" class="coldChainTable" style="width: 100%">
              <el-table-column align="center" label="序号" prop="index" width="50">
                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
              </el-table-column>
              <el-table-column align="center" label="商品名称" prop="name"></el-table-column>
              <el-table-column align="center" label="规格" prop="specifications"></el-table-column>
              <el-table-column align="center" label="生产厂家" prop="manufacturer"></el-table-column>
              <el-table-column align="center" label="批号" prop="batchNumber"></el-table-column>
              <el-table-column align="center" label="数量" prop="quantity"></el-table-column>
              <el-table-column align="center" label="资料齐全">
                <template slot-scope="scope">
                  {{ scope.row.completeInformation == 1 ? '是' : '否' }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <!-- /增值服务-->
          <el-card class="mb10" shadow="never" v-if="info.addServiceList">
            <div slot="header">
              <div class="titleLayout">
                <span class="verticalBar"></span>
                <span class="title">增值服务</span>
              </div>
            </div>
            <!-- <div v-if="info.addedServices && info.addedServices.length != 0"> -->
            <el-tag v-for="(service, index) in info.addServiceList" :key="index" style="margin: 1%">
              {{ service.name || service.serviceName }}<span v-if="service.inputValue" style="color: #666666">：{{
                service.inputValue }}</span>
            </el-tag>
            <!-- </div> -->
            <!-- <el-empty v-else :image-size="40" description="无增值服务" style="height: 20px"></el-empty> -->
          </el-card>
          <!-- /费用明细 -->
          <el-card class="mb10" shadow="never">
            <div slot="header">
              <div class="titleLayout">
                <span class="verticalBar"></span>
                <span class="title">费用明细</span>
              </div>
            </div>
            <div class="cost__card">
              <el-card shadow="never" style="min-width: 250px">
                <div slot="header" class="cost__card__header">
                  <span>基础费用</span>
                  <div class="card__header__num">
                    <span class="card__header__num__label">¥</span>
                    <span class="card__header__num__value">{{ info.basicCost }}</span>
                  </div>
                </div>
                <div v-if="info.costList && info.costList.length > 0" class="cost__box">
                  <div v-for="(item, index) in info.costList" :key="index" class="cost__item">
                    <div class="card__header__num">
                      <span class="card__header__num__label">¥</span>
                      <span class="card__header__num__value">{{ item.costContractPrice }}</span>
                    </div>
                    <span class="cost__item__label">{{ costOrderTypeFormat(item.costType) }}</span>
                  </div>
                </div>
                <div v-else style=" color: #999999">暂无数据</div>
              </el-card>
              <el-card shadow="never" style="min-width: 250px">
                <div slot="header" class="cost__card__header">
                  <span>增值服务费用</span>
                  <div class="card__header__num">
                    <span class="card__header__num__label">¥</span>
                    <span class="card__header__num__value">{{ info.serviceCost }}</span>
                  </div>
                </div>
                <div v-if="info.addedServices && info.addedServices.length > 0" class="cost__box">
                  <div v-for="(item, index) in info.addedServices" :key="index" class="cost__item">
                    <div class="card__header__num">
                      <span class="card__header__num__label">¥</span>
                      <span class="card__header__num__value">{{ item.addedServicesContractPrice }}</span>
                    </div>
                    <span class="cost__item__label">{{ item.addedServicesName }}</span>
                  </div>
                </div>
                <div v-else style="color: #999999">暂无数据</div>
              </el-card>
            </div>
            <div class="cost__card__estimate">
              <span>预估费用</span>
              <div class="cost__card__estimate__main">
                <span class="cost__card__estimate__main__label">¥</span>
                <span class="cost__card__estimate__main__value">{{ info.orderCost }}</span>
              </div>
            </div>
          </el-card>
        </div>
        <div v-if="type == 2">
          <div class="titleLayout" style="margin-bottom: 15px">
            <span class="verticalBar"></span>
            <span class="title">订单轨迹</span>
          </div>
          <div class="commonTopBox">
            <el-timeline :reverse="true">
              <el-timeline-item v-for="(activity, index) in activities" :key="index"
                :timestamp="formatDate(activity.createDate)">
                {{ activity.content }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
        <div v-if="type == 4">
          <div class="titleLayout" style="margin-bottom: 15px">
            <span class="verticalBar"></span>
            <span class="title">支付费用</span>
          </div>
        </div>
        <div v-if="type == 5">
          <el-card class="mb10" shadow="never">
            <el-table :data="printRecordList">
              <el-table-column align="center" label="设备编号" prop="deviceNo" />
              <el-table-column align="center" label="查询开始时间" prop="queryStartTime" />
              <el-table-column align="center" label="查询结束时间" prop="queryEndTime" />
              <el-table-column align="center" label="打印数据">
                <template slot-scope="scope">
                  <el-link type="primary" @click="viewDevicePrintData(scope.row)">点击查看</el-link>
                </template>
              </el-table-column>
              <el-table-column align="center" label="打印人" prop="createBy" />
              <el-table-column align="center" label="打印时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button icon="el-icon-printer" size="mini" type="primary"
                    @click="printDeviceRecord(scope.row)">打印数据</el-button>
                </template>
              </el-table-column>
            </el-table>

            <pagination v-show="printRecordTotal > 0" v-model:limit="printRecordQueryParams.pageSize"
              v-model:page="printRecordQueryParams.pageNum" :total="printRecordTotal" @pagination="getPrintRecord" />
          </el-card>

          <el-drawer :modal="false" :visible.sync="printRecordReturnDataViewShow" title="打印数据" width="55vh">
            <div style="margin: 20px">
              <el-divider>打印参数</el-divider>
              <el-descriptions column="1">
                <el-descriptions-item label="运输单位">
                  {{ printRecord.params.transportCompany }}
                </el-descriptions-item>
                <el-descriptions-item label="运单编号">
                  {{ printRecord.params.transOrderNo }}
                </el-descriptions-item>
                <el-descriptions-item label="订单编号">
                  {{ printRecord.params.orderNo }}
                </el-descriptions-item>
                <el-descriptions-item label="发件公司">
                  {{ printRecord.params.sendCompany }}
                </el-descriptions-item>
                <el-descriptions-item label="收件公司">
                  {{ printRecord.params.receiveCompany }}
                </el-descriptions-item>
              </el-descriptions>
              <el-divider>温度数据</el-divider>
              <el-table :data="printRecord.data" height="500px" size="mini">
                <el-table-column align="center" label="时间" prop="dataTime" />
                <el-table-column align="center" label="温度">
                  <template slot-scope="scope"> {{ scope.row.temperature }}℃ </template>
                </el-table-column>
              </el-table>
            </div>
          </el-drawer>
        </div>
        <div v-if="type == 6">
          <el-card class="mb10" shadow="never">
            <el-timeline v-loading="recordListLoading" :style="recordListLoading ? 'min-height: 300px' : ''"
              element-loading-text="获取订单异动信息中..." style="padding: 0">
              <el-timeline-item v-for="item in recordList" :timestamp="formatDate(item.createTime)" placement="top">
                <el-card shadow="hover">
                  <div class="item-line">
                    <div class="item-label">修改类型</div>
                    <div class="item-content">
                      {{ fourplFormat(fourplOrderChangeTypeOptions, item.type) }}
                    </div>
                  </div>
                  <div v-if="item.changeDetails" class="item-line">
                    <div class="item-label">修改前</div>
                    <div class="item-content">
                      <div v-for="(record, idx) in item.changeDetails" class="mb5">
                        <div style="display: flex; align-items: center">
                          <div class="mr5">{{ idx + 1 }}</div>
                          <div class="mr5">{{ record.name }}:</div>
                          <div v-if="record.properties == 'temperatureType'">
                            {{ getTemperatureTypeVal(record.oldValue) }}
                          </div>
                          <div v-else-if="record.properties == 'productProperties'">
                            {{ getDictVals(productAttributeDictionary, record.oldValue) }}
                          </div>
                          <div v-else-if="record.properties == 'productClass'">
                            {{ getDictVals(fourplProductClassDicts, record.oldValue) }}
                          </div>
                          <div v-else-if="record.properties == 'addServiceList'">
                            {{ fourplAddedServices(record.oldValue) }}
                          </div>
                          <div v-else-if="record.properties == 'orderType'">
                            {{ getDictVals(orderTypeList, record.oldValue) }}
                          </div>
                          <div v-else-if="record.properties == 'orderDetailList'">
                            <el-table :data="stringToObject(record.oldValue)" :fit="true" class="coldChainTable"
                              style="width: 100%">
                              <el-table-column align="center" label="序号" prop="index" width="50">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                              </el-table-column>
                              <el-table-column align="center" label="商品名称" prop="name"> </el-table-column>
                              <el-table-column align="center" label="规格" prop="specifications"> </el-table-column>
                              <el-table-column align="center" label="生产厂家" prop="manufacturer"> </el-table-column>
                              <el-table-column align="center" label="批号" prop="batchNumber"> </el-table-column>
                              <el-table-column align="center" label="数量" prop="quantity" width="160"> </el-table-column>
                              <el-table-column align="center" label="资料齐全" prop="completeInformation">
                                <template slot-scope="scope">
                                  {{ scope.row.completeInformation == '1' ? '是' : '否' }}
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
                          <div v-else>{{ record.oldValueDesc ? record.oldValueDesc : record.oldValue != 'null' ?
                            record.oldValue : '' }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="item.changeDetails" class="item-line">
                    <div class="item-label">修改后</div>
                    <div class="item-content">
                      <div v-for="(record, idx) in item.changeDetails" class="mb5">
                        <div style="display: flex; align-items: center">
                          <div class="mr5">{{ idx + 1 }}</div>
                          <div class="mr5">{{ record.name }}:</div>
                          <div v-if="record.properties == 'temperatureType'">
                            {{ getTemperatureTypeVal(record.value) }}
                          </div>
                          <div v-else-if="record.properties == 'productProperties'">
                            {{ getDictVals(productAttributeDictionary, record.value) }}
                          </div>
                          <div v-else-if="record.properties == 'productClass'">
                            {{ getDictVals(fourplProductClassDicts, record.value) }}
                          </div>
                          <div v-else-if="record.properties == 'addServiceList'">
                            {{ fourplAddedServices(record.value) }}
                          </div>
                          <div v-else-if="record.properties == 'orderType'">
                            {{ getDictVals(orderTypeList, record.value) }}
                          </div>
                          <div v-else-if="record.properties == 'orderDetailList'">
                            <el-table :data="stringToObject(record.value)" :fit="true" class="coldChainTable"
                              style="width: 100%">
                              <el-table-column align="center" label="序号" prop="index" width="50">
                                <template slot-scope="scope">{{ scope.$index + 1 }}</template>
                              </el-table-column>
                              <el-table-column align="center" label="商品名称" prop="name"> </el-table-column>
                              <el-table-column align="center" label="规格" prop="specifications"> </el-table-column>
                              <el-table-column align="center" label="生产厂家" prop="manufacturer"> </el-table-column>
                              <el-table-column align="center" label="批号" prop="batchNumber"> </el-table-column>
                              <el-table-column align="center" label="数量" prop="quantity" width="160"> </el-table-column>
                              <el-table-column align="center" label="资料齐全" prop="completeInformation">
                                <template slot-scope="scope">
                                  {{ scope.row.completeInformation == '1' ? '是' : '否' }}
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
                          <div v-else>{{ record.valueDesc ? record.valueDesc : record.value != 'null' ? record.value : ''
                          }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="item.description" class="item-line">
                    <div class="item-label">异动备注</div>
                    <div class="item-content">
                      {{ item.description }}
                    </div>
                  </div>
                  <div v-if="item.imgList && item.imgList.length > 0" class="item-line">
                    <div class="item-label">照片</div>
                    <div class="item-content">
                      <div class="img-list">
                        <div v-for="(img, i) in item.imgList" class="img" @click="handlePictureCardPreview(img)">
                          <img :src="img" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else-if="item.imgUrls" class="item-line">
                    <div class="item-label">照片</div>
                    <div class="item-content">
                      <div class="img-list">
                        <div class="img" @click="handlePictureCardPreview(item.imgUrls)">
                          <img :src="item.imgUrls" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <p>王小虎 提交于 2018/4/12 20:46</p> -->
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-if="!recordListLoading && recordList && recordList.length == 0" description="不存在异动信息"></el-empty>
          </el-card>
        </div>
      </div>
    </el-drawer>

    <!-- <el-image-viewer v-if="dialogVisible" :on-close="imgClose" :url-list="dialogImageUrl" :z-index="9999" /> -->
  </div>
</template>
<script>
import { formatDate } from '@/utils/index';
import XEClipboard from 'xe-clipboard';
import sign from '@/api/signAudit/sign.js';
import CardHeader from '@/components/CardHeader';
import orderManagement from "@/api/logisticsManagement/orderManagement";
import operationConfiguration from "@/api/logisticsConfiguration/operationConfiguration";

export default {
  name: 'orderDetail',
  components: {
    CardHeader,
  },
  props: {
    infoOpen: {
      required: true,
      type: Boolean
    },
    orderInfo: {
      required: true,
      type: Object
    },
    title: {
      required: true,
      type: String
    }
  },
  data() {
    return {
      type: 1,
      activities: [], // 订单轨迹
      info: {}, // 订单详情
      costOrderTypeOptions:[], // 费用类型
      addServiceList: [], //新增订单增值服务
      // 表单参数
      form: {
        productType: '', //货品类型
        temperatureType: '', //温层类型
        goodsName: '', //货品名称
        vehicleSelection: '', //车型选择
        goodsPackages: '', //件数
        productProperties: '', //货品属性
        productWeight: null, //重量
        externalOrderNo: '', //随货同行单号
        productVolume: null, //体积
        stock: '', //备货
        paymentMethod: '', //付款方式
        deliveryTime: '', //送达时间
        appointmentTime: '', //取件时间
        addServiceList: [], //附加服务
        agreeToTermsCarrier: [] //阅读并同意
      },
      coldChainProductData: [], // 冷链货品明细
      productAttributeDictionary: [], // 商品属性 字典
      addedServiceDicts: [], // 增值服务
      // 表单验证
      rules: {},
      // 冷链交接单类型
      coldChainHandoverType: [],
      printRecordList: [],
      printRecord: {
        params: {},
        data: []
      },
      printRecordTotal: 0,
      open: false,
      printRecordReturnDataViewShow: false,
      printRecordQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      fourplPaymentMethodOptions: [], // 付款方式
      fourplProductTypeOptions: [], // 产品类型
      costDataList: [], // 结算费用展示
      // 产品分类
      fourplProductClassDicts: [],
      recordListLoading: false,
      // 异动记录数据
      recordList: [],
      // 回执单
      hzFileList: [],
      // 客户签名
      khqmFileList: [],
      // 异动类型
      fourplOrderChangeTypeOptions: [],
      propertiesList: [],
      carTypeList: [],
      productTypeDicts: [],
      dialogImageUrl: '',
      paramsTransorder: '',
      paramsId: '',
      dialogVisible: false,
      // 货物揽收方式
      collectionMethod: [],
      orderTypeList: [
        {
          dictLabel: '预约上门取件',
          dictValue: '1'
        },
        {
          dictLabel: '送达集散网点',
          dictValue: '2'
        }
      ]

    };
  },
  async created() {
    this.type = 1;
    this.open = this.infoOpen;
    // this.getTrackTrajectory();
    this.getTransDetail()
    this.getCarType()
    // this.getDict()
    /** 产品分类 */
    this.fourplProductClassDicts = await this.getDictList('fourpl_product_class');
    // 运输类型4PL
    this.productTypeDicts = await this.getDictList('fourpl_product_type');
    /** 付款方式 */
    this.fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
    /** 揽收方式 */
    this.collectionMethod = await this.getDictList('fourpl_mail_service');
    // 商品属性 字典
    this.productAttributeDictionary = await this.getDictList('fourpl_product_attributes');
    this.propertiesList = await this.getDictList('fourpl_product_attributes');
    this.costOrderTypeOptions = await this.getDictList('cost_order_type');
  },
  methods: {
    // 增值服务格式化
    fourplAddedServices(vals) {
      let arr = [];
      if (Array.isArray(vals)) {
        if (vals.length > 0) {
          vals.forEach(item => {
            let text = item.serviceName;
            if (item.content) {
              text += ':' + item.content
            }
            let index = this.addedServiceDicts.findIndex((a) => a.id == item.type);
            if (index > -1 && item.content) {
              text += this.addedServiceDicts[index].unit
            }
            arr.push(text);
          })
        }
      }
      return arr.join(',')
    },
    // 费用明细翻译
    costOrderTypeFormat(value) {
      return this.selectDictLabel(this.costOrderTypeOptions, value)
    },
    addServiceListCheckBox() {
      let formServices = [];
      this.addServiceList.forEach((val) => {
        let obj = {};
        obj.type = val.dictValue;
        obj.content = val.defaultContent;
        formServices.push(obj);
      });
      this.form.addServiceList = formServices;
    },
    cancel() { },
    // 复制
    copyOrderNumber() {
      if (XEClipboard.copy(this.orderInfo.orderNo)) {
        this.$notify({
          title: '提示',
          message: '复制成功',
          type: 'success'
        });
      } else {
        this.$notify({
          title: '提示',
          message: '复制失败',
          type: 'warning'
        });
      }
    },
    // 标准时间格式化
    formatDate(cellValue) {
      return formatDate(cellValue);
    },
    // 格式化付款方式
    formatThePaymentMethod(val) {
      return this.selectDictLabel(this.fourplPaymentMethodOptions, val);
    },
    // 格式化货物揽收方式
    formatTheWayTheGoodsAreCollected(val) {
      return this.selectDictLabel(this.collectionMethod, val);
    },
    // 格式化运输类型
    fourplProductTypeFormat(val) {
      return this.selectDictLabel(this.productTypeDicts, val);
    },
    // 车辆类型
    forMatterCar(val) {
      return this.selectDictLabel(this.carTypeList, val);
    },
    forMatterProperties(val) {
      return this.selectDictLabels(this.propertiesList, val);
    },
    /** 4PL字典转换 */
    fourplFormat(data, val) {
      return this.selectDictLabel(data, val);
    },
    // 查询车辆类型
    getCarType() {
      operationConfiguration.listCarType({ carrierId: this.orderInfo.carrierId, status: '0', size: 1000 }).then((response) => {
        this.carTypeList = [];
        if (response.code == 200 && response?.data?.records.length > 0) {
          this.carTypeList = response?.data?.records.map(item => {
            return { name: item.typeName, value: item.typeCode }
          })
        }
      });
    },
    // 获取订单异动数据
    getOrderChangeData() {
      sign.getOrderChangeDetails({ orderId: this.paramsId }).then((response) => {
        if (response.code == 200) {

        }
      })

    },
    // 温度打印记录
    getPrintRecord() {
      this.printRecordQueryParams.transOrderNo = this.orderInfo.transOrderNo;
      getPrintRecord(this.printRecordQueryParams).then((res) => {
        this.printRecordList = res.data.list;
        this.printRecordTotal = res.data.total;
      });
    },
    // 温层类型翻译
    getTemperatureTypeVal(val) {
      let index = this.temperatureTypeDicts.findIndex((t) => t.id == parseInt(val));
      if (index > -1) {
        return this.temperatureTypeDicts[index].describtion;
      }
      return '';
    },
    // 获取订单轨迹
    getTrackTrajectory() {
      // console.log(this.paramsTransorder);
      sign.getRecordByTransOrderNo({ transOrderNo: this.paramsTransorder }).then((response) => {
        if (response.code == 200) {
          this.activities = response.data.records;
        }
      });
    },
    // 获取运单明细
    getTransDetail() {
      var params = {}
      if (this.orderInfo.orderDrug) {
        params = { id: this.orderInfo.orderDrug.id }
      } else {
        params = { id: this.orderInfo.id }
      }
      sign.getTransDetail(params).then((response) => {
        if (response.code == 200) {
          this.info = response.data;
          this.paramsTransorder = response.data.transOrderNo
          this.paramsId = response.data.id
          if (response.data.fileList) {
            this.hzFileList = response.data.fileList.filter(item => item.fileType == '1');
            this.khqmFileList = response.data.fileList.filter(item => item.fileType == '2');
          }
          this.$nextTick(() => {
            var params2 = {}
            if (this.orderInfo.orderDrug) {
              params2 = { orderId: this.orderInfo.orderDrug.id }
            } else {
              params2 = { orderId: this.orderInfo.id }
            }
            orderManagement.orderDetailCostInfo(params2).then((response) => {
              if (response.code == 200 && response.data.costList) {
                this.info.costList = response.data.costList;
                this.info.basicCost = this.info.costList.reduce(function (acc, obj) {
                  return acc + Number(obj.costContractPrice);
                }, 0);
              }
              if (response.code == 200 && response.data.addedServiceDTOList) {
                this.info.addedServices = response.data.addedServiceDTOList;
                this.info.serviceCost = this.info.addedServices.reduce(function (acc, obj) {
                  return acc + Number(obj.addedServicesContractPrice);
                }, 0);
              }
            });
          })
        }
      });
    },
    // 放大图片
    handlePictureCardPreview(imgUrl) {
      this.dialogImageUrl = [];
      this.dialogImageUrl.push(imgUrl);
      this.dialogVisible = true;
    },
    imgClose() {
      this.dialogVisible = false;
    },
    onClose() {
      this.$emit('closeSlider');
    },
    // 打印冷链交接单
    printColdChainHandover(templateId) {
      this.pdfLabelView(templateId, this.orderInfo.orderBid);
    },
    // 打印温度记录数据
    printDeviceRecord(row) {
      let printQueryParams = JSON.parse(row.printParams);
      let printData = JSON.parse(row.printData);
      let high = Math.max.apply(
        Math,
        printData.map((item) => {
          return item.temperature;
        })
      );
      let low = Math.min.apply(
        Math,
        printData.map((item) => {
          return item.temperature;
        })
      );
      let printParams = {
        templateId: '8e03f081411647a4a7d05d2536aee6ad',
        data: {
          transportCompany: printQueryParams.transportCompany,
          transOrderNo: printQueryParams.transOrderNo,
          externalOrderNo: printQueryParams.orderNo,
          sendCompany: printQueryParams.sendCompany,
          receiverCompany: printQueryParams.receiveCompany,
          deviceNo: row.deviceNo,
          deviceBindTime: printData[0].dataTime,
          signTime: printData[printData.length - 1].dataTime,
          highTemperature: high,
          lowTemperature: low,
          properties: {
            data: printData
          }
        }
      };
      printPdfWithData(printParams).then((resp) => {
        const binaryData = [];
        binaryData.push(resp);
        //获取blob链接
        let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
        console.log(pdfUrl);
        window.open(pdfUrl);
      });
    },
    // 字符串转对象
    stringToObject(data) {
      return JSON.parse(data);
    },
    submitForm() { },
    // 点击tabs
    tabClick(label) {
      switch (label) {
        case '1':
          this.getTransDetail();
          break;
        case '2':
          this.getTrackTrajectory();
          break;
        case '5':
          this.getPrintRecord();
          break;
        case '6':
          // 获取订单异动数据
          this.getOrderChangeData();
          break;
        default:
          break;
      }
    },
    // 查看温度记录打印数据
    viewDevicePrintData(row) {
      this.printRecord.params = JSON.parse(row.printParams);
      this.printRecord.data = JSON.parse(row.printData);
      this.printRecordReturnDataViewShow = true;
      console.log(this.printRecord);
    }
  }
};
</script>
<style lang="scss" scoped>
.cost__card {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;

  ::v-deep .el-card__header {
    background: #21292508;
  }
}
.cost__card__estimate {
  display: flex;
  gap: 15px;
  font-size: 18px;
  align-items: baseline;
  .cost__card__estimate__main {
    display: flex;
    align-items: baseline;
    gap: 2px;
    color: #ff2a2a;
    .cost__card__estimate__main__label {
      font-size: 18px;
    }
    .cost__card__estimate__main__value {
      font-size: 22px;
    }
  }
}
.cost__box {
  display: flex;
  gap: 20px;
  margin-top: 10px;
  .cost__item {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .cost__item__label {
      font-size: 12px;
      color: #999999;
    }
  }
}

.cost__card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card__header__num {
  display: flex;
  align-items: baseline;
  gap: 2px;
  color: #5670fe;
}

.card__header__num__label {
  font-size: 12px;
}

.card__header__num__value {
  font-size: 18px;
}

::v-deep {
  thead th {
    border-right: none !important;
  }

  .el-drawer__header {
    margin-bottom: 20px;
  }

  .orderDetails {
    .el-card__body {
      padding-bottom: 15px;
    }
  }

  .titleLayout {
    margin-bottom: 15px;
    font-size: 15px;
  }
}

.mb16 {
  margin-bottom: 16px;
}

.p-16 {
  padding: 16px;
}

.orderDetails {
  .box-top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    >div {
      :nth-child(1) {
        color: #999999;
      }

      :nth-child(2) {
        color: #666666;
      }
    }
  }

  .heading {
    color: #999999;
  }

  .heading-value {
    color: #707070;
  }

  .el-card__body {
    padding-bottom: 15px;
  }

  .box__flex__2 {
    display: flex;
    align-items: center;
    gap: 50px;
    font-size: 14px;

    >div {
      :nth-child(1) {
        color: #999999;
      }

      :nth-child(2) {
        color: #666666;
      }
    }
  }
}

.titleLayout {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .verticalBar {
    display: inline-block;
    background-color: #5670fe;
    width: 3px;
    height: 1em;
    margin-right: 8px;
  }

  .title {
    color: #5670fe;
  }
}

.box-orderInformation {

  //display: grid;
  //grid-template-columns: 1fr 1fr;
  //gap: 16px;
  .d-box-content {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    font-size: 14px;

    .d-box-column {
      display: flex;
      flex-direction: column;
      margin-left: 20px;
      gap: 8px;
      flex: 1;

      .d-box-info {
        display: flex;
        //justify-content: space-between;

        :nth-child(1) {
          color: #999999;
          width: 100px;
          flex-shrink: 0;
          flex-grow: 0;
        }

        :nth-child(2) {
          color: #333333;
        }
      }
    }

    .d-box-column:first-child {
      border-right: 1px solid #e6ebf5;
    }
  }
}

.box {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}

.item-line {
  margin-bottom: 10px;
  display: flex;
  align-items: center;

  .item-label {
    color: #aaaaaa;
    width: 80px;
    text-align: right;
    margin-right: 15px;
    flex-shrink: 0;
    flex-wrap: 0;
  }

  .item-content {
    color: #333333;

    .img-list {
      display: flex;

      .img {
        width: 128px;
        height: 128px;
        border: 1px dashed #d9d9d9;
        margin-top: 0;
        margin-left: 10px;

        img {
          width: 108px;
          height: 108px;
          margin: 10px;
        }
      }
    }
  }
}</style>
