<template>
    <div class="app-container">
        <!--搜索-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item label="集散网点名称" prop="branchName">
                    <el-input v-model="queryParams.branchName" clearable placeholder="请输入集散网点名称" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="集散网点地址" prop="areas">
                    <el-cascader v-model="queryParams.areas" :options="sysAreasThird" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择省市区" style="min-width: 300px" @change="handleQuery" @visible-change="visibleChangeSearch" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <div>
                    <el-button v-hasPermi="['collect:branch:add']" icon="el-icon-plus" type="primary" @click="handleAdd">新增 </el-button>
                    <el-button v-hasPermi="['collect:branch:remove']" :disabled="multiple" icon="el-icon-delete" type="danger" @click="handleDelete">删除 </el-button>
                    <el-button :disabled="single" icon="el-icon-printer" type="warning" @click="printLabel">打印标签 </el-button>
                </div>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="DistributionBranch" @queryTable="getList"></right-toolbar>
            </div>

            <column-table key="DistributionBranch" v-loading="loading" v-model:columns="columns" :data="branchList" :show-check-box="true" border element-loading-text="加载中..." @selection-change="handleSelectionChange">
                <template #branchClass="{ row }">
                    <span>{{ branchClassFormat(row) }}</span>
                </template>
                <template #opt="scope">
                    <el-button v-hasPermi="['transport:car:add']" icon="el-icon-magic-stick" link size="small" type="primary" @click="relationDriver(scope.row)">关联司机</el-button>
                    <el-button v-hasPermi="['transport:car:add']" icon="el-icon-user" link size="small" type="primary" @click="relationUser(scope.row)">指派员工</el-button>
                    <el-dropdown>
                        <el-button icon="el-icon-arrow-down" link size="small">更多</el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item v-hasPermi="['collect:branch:edit']" icon="el-icon-edit" size="small" style="color: #67c23a" @click.native="handleUpdate(scope.row)">修改</el-dropdown-item>
                                <el-dropdown-item v-hasPermi="['collect:branch:delete']" icon="el-icon-delete" size="small" style="color: #f56c6c" @click.native="handleDelete(scope.row)">删除</el-dropdown-item>
                                <el-dropdown-item icon="el-icon-printer" size="small" style="color: #e6a23c" @click.native="printBoxTag(scope.row)">打印标签</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
        <!-- 添加或修改服务网点对话框 -->
        <el-dialog v-model="open" v-dialogDrag :title="title" append-to-body width="650px">
            <el-form ref="form" :model="form" :rules="rules" label-width="110px">
                <el-form-item label="集散网点名称" prop="branchName">
                    <el-input v-model="form.branchName" maxlength="20" placeholder="请输入集散网点名称" show-word-limit />
                </el-form-item>
                <el-form-item label="服务类型" prop="branchClass">
                    <el-select v-model="form.branchClass" clearable placeholder="请选择服务类型" style="width: 100%">
                        <el-option v-for="(item, idx) in fourplCarOptions" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="集散网点地址" prop="areas">
                    <el-cascader v-model="form.areas" :options="sysAreas" clearable filterable placeholder="请选择省市区" style="width: 100%" @change="addressChange" />
                </el-form-item>
                <el-form-item label="详细地址" prop="address">
                    <el-input v-model="form.address" maxlength="200" placeholder="请输入详细地址" show-word-limit />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="form.sort" :max="9999" :min="1" :precision="0" :step="1" controls-position="right" placeholder="请输入排序" style="width: 100%" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="60" placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</template>
        </el-dialog>
        <driver-select v-if="driverOpen" v-model:show="driverOpen" :driverData="driverData" :type="'flow_driver'" :values="values" @changeShow="changeShow" @onConfirm="binding"></driver-select>
        <user-select v-if="userOpen" v-model:show="userOpen" v-model:userData="userData" :type="'flow_user'" :values="uservalues" @changeShow="changeShow" @onConfirm="appointUser"></user-select>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import driverSelect from '@/components/driverSelect';
import userSelect from '@/components/userSelect';
import serviceNetwork from '@/api/logisticsConfiguration/serviceNetwork.js';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'DistributionBranch',
    components: {
        SearchButton,
        driverSelect,
        userSelect,
        ColumnTable,
        RightToolbar
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            sysAreas: [], //省市区数据
            statusDict: [],
            // 服务网点表格数据
            branchList: [],
            columns: [
                { title: '集散网点名称', key: 'branchName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '集散网点编码', key: 'branchCode', align: 'center', minWidth: '120px', columnShow: true },
                { title: '服务类型', key: 'branchClass', align: 'center', minWidth: '80px', columnShow: true },
                { title: '集散网点地址', key: 'complateAddress', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收区域个数', key: 'lanAreaNum', align: 'center', minWidth: '120px', columnShow: true },
                { title: '地配区域个数', key: 'landAreaNum', align: 'center', minWidth: '120px', columnShow: true },
                { title: '司机', key: 'lineDriverName', align: 'center', minWidth: '250px', columnShow: true ,showOverflowTooltip: true},
                { title: '备注', key: 'remark', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '300px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                branchName: null,
                provinceId: null,
                cityId: null,
                countyId: null
            },
            // 表单参数
            form: {
                serviceCategory: [],
                agreeToTermsCarrier: []
            },
            // 表单校验
            rules: {
                branchName: [{ required: true, message: '请输入集散网点名称', trigger: 'blur' }],
                branchClass: [{ required: true, message: '请选择服务类型', trigger: 'blur' }],
                address: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { max: 200, message: '请输入200个字符以内', trigger: 'blur' }
                ],
                areas: [{ required: true, message: '请选择省市区', trigger: 'blur' }]
            },
            // 搜索关键字
            searchKeyword: '',
            typeOptions: [],
            treeTypeLists: [],
            defaultProps: {
                children: 'children',
                label: function (data, node) {
                    return data.typeName + '(' + data.children.length + ')';
                },
                hasChildren: 'hasChildren'
            },
            statusOptions: [],
            userConfig: {},
            userOpen: false,
            fourplCarOptions: [],
            branchCode: null,
            driverOpen: false,
            values: {},
            uservalues: {},
            driverData: [],
            userData: [],
            // 三级省市区
            sysAreasThird: []
        };
    },
    watch: {
        // 根据名称筛选部门树
        searchKeyword(val) {
            this.$refs.tree.filter(val);
        }
    },
    async created() {
        //服务类型
        this.fourplCarOptions = await this.getDictList('fourpl_service_type');
        this.getList();
    },
    methods: {
        /**
         * 获取集散网点地址省市区
         */
        visibleChangeSearch() {
            this.sysAreasThird = this.getSysAreasThird;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        },
        // 指派员工
        appointUser(data) {
            let params = [];
            params = data.userList.map((item) => {
                // return {userId: item.id, userName: item.name, userPhone: item.phone, status: item.status,branchCode:data.data.branchCode,id:data.data.id};
                return { userId: item.id, branchCode: data.data.branchCode };
            });
            serviceNetwork.saveBranchEmp(params).then((response) => {
                if (response.code == 200) {
                    this.msgSuccess('指派员工成功');
                    this.userOpen = false;
                    this.getList();
                }
            });
        },
        // 绑定司机
        binding(data) {
            let params = [];
            params = data.driverList.map((item) => {
                return { branchCode: data.data, driverCode: item.driverCode };
            });
            operationConfiguration.bindLineDeiver(params).then((response) => {
                if (response.code == 200) {
                    this.msgSuccess('绑定司机成功');
                    this.driverOpen = false;
                    this.getList();
                }
            });
        },
        changeShow(value) {
            this.driverOpen = value;
            this.userOpen = value;
        },
        // 显示关联司机
        relationDriver(row) {
            this.driverData = [];
            this.values = {
                minNum: 1, // 最少绑定一个
                title: '关联司机',
                open: true,
                data: row.branchCode,
                type: 1
            };
            operationConfiguration.getLineDriverList({ branchCode: row.branchCode }).then((response) => {
                if (response?.data?.records) {
                    this.driverData = response.data.records.map((item) => {
                        return item.driver;
                    });
                }
                this.driverOpen = true;
            });
        },
        addressChange(value) {
            this.form.provinceId = value[0];
            this.form.cityId = value[1];
            this.form.countyId = value[2];
            this.form.townId = value[3];
        },
        /** 类型字典转换 */
        branchClassFormat(val) {
            return this.selectDictLabel(this.fourplCarOptions, val.branchClass);
        },
        /** 查询服务网点列表 */
        getList() {
            this.loading = true;
            this.branchList = [];
            const { areas } = this.queryParams;
            if (areas) {
                this.queryParams.provinceId = areas[0];
                this.queryParams.cityId = areas[1];
                this.queryParams.countyId = areas[2];
            }
            let params = { ...this.queryParams };
            delete params.areas;
            serviceNetwork.listBranch(params).then((response) => {
                if (response.code === 200 && response.data) {
                    this.branchList = response.data.records || [];
                    this.total = response.data.total || 0;
                }
                this.loading = false;
            });
        },
        //显示指派员工
        relationUser(row) {
            this.userData = [];
            serviceNetwork.getBranchEmpList({ branchCode: row.branchCode }).then((response) => {
                if (response.code == 200 && response.data) {
                    this.userData = response.data.map((item) => {
                        return { id: item.user.id, name: item.user.name, phone: item.user.phone, status: false };
                    });
                }
                this.uservalues = {
                    title: '指派员工',
                    roleType: '2',
                    statusFlag: false,
                    data: row
                };
                this.userOpen = true;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                branchName: null,
                provinceId: null,
                cityId: null,
                sysCompanyAddress: null,
                countyId: null,
                townId: null,
                address: null,
                header: null,
                areas: [],
                headerPhone: null,
                remark: null,
                branchClass: '0',
                sort: 0,
                branchType: '2'
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryParams = {
                current: 1,
                size: 10,
                branchName: null,
                provinceId: null,
                cityId: null,
                countyId: null
            };
            this.handleQuery();
        },
        resetForm(formName) {
            this.$refs[formName] ? this.$refs[formName].resetFields() : '';
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.names = selection.map((item) => item.branchName);
            this.branchCode = selection.map((item) => item.branchCode);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        //标签打印
        printLabel() {
            this.pdfLabelView('0eaa1f6d67b7486a925326c6499fddca', this.branchCode);
        },
        /**
         * 打印箱签
         * @param row
         */
        printBoxTag(row) {
            this.pdfLabelView('0eaa1f6d67b7486a925326c6499fddca', row.branchCode);
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.sysAreas = this.getSysAreas; //地址赋值
            this.reset();
            this.open = true;
            this.title = '添加服务网点';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.sysAreas = this.getSysAreas; //地址赋值
            this.reset();
            const id = row.id || this.ids;
            serviceNetwork.getBranch({ id }).then((response) => {
                this.form = { ...response.data };
                this.form.areas = [this.form.provinceId, this.form.cityId, this.form.countyId, this.form.townId];
                this.open = true;
                this.title = '修改服务网点';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        const params = { ...this.form };
                        serviceNetwork.saveBranch(params).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        const params = { ...this.form };
                        serviceNetwork.saveBranch(params).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids.join(',');
            const names = row.branchName || this.names;
            this.$confirm('是否确认删除服务网点"' + names + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return serviceNetwork.delBranch({ ids: ids });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'collect/branch/export',
                {
                    ...this.queryParams
                },
                `collect_branch.xlsx`
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
</style>
