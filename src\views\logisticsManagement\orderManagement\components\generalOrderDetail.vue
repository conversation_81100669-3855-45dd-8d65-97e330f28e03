<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="数据加载中" style="background-color: #f5f7fd">
    <el-form ref="form" class="p10" label-width="110px" size="small">
      <!-- 发件人信息 -->
      <div style="display: flex; width: 100%">
        <el-card class="mb10" shadow="never" style="width: 50%; position: relative">
          <CardHeader title="发件人信息" />

          <div class="box_" style="margin-left: 10px">
            <el-form-item  label="发件网点：" prop="sendWebsiteName">
              {{ orderInfo.sendWebsiteName }}
            </el-form-item>
            <el-form-item  label="发件人电话：" prop="sendUserPhone">
              {{ orderInfo.sendUserPhone }}
            </el-form-item>
            <el-form-item  :label="'发件联系人\n（托运人）'" class="shippingMethod" prop="sendUser">
              {{ orderInfo.sendUser }}
            </el-form-item>
            <el-form-item  label="详细地址：" prop="sendAddress">
              {{ orderInfo.sendAddress }}
            </el-form-item>
            <el-form-item  label="货主：" prop="companyName">
              {{ orderInfo.companyName }}
            </el-form-item>
            <el-form-item  label="发件公司：" prop="sendCompany">
              {{ orderInfo.sendCompany }}
            </el-form-item>
          </div>
        </el-card>
        <!-- 收件人信息 -->
        <el-card class="mb10" shadow="never" style="width: 50%; margin-left: 10px; position: relative">
          <CardHeader title="收件人信息" />
          <div class="box_" style="margin-left: 10px">
            <el-form-item  label="目的网点：" prop="receiverWebsiteName">
              {{ orderInfo.receiverWebsiteName }}
            </el-form-item>
            <el-form-item  label="收件人电话：" prop="receiverUserPhone">
              {{ orderInfo.receiverUserPhone }}
            </el-form-item>
            <el-form-item  label="收件人名称：" prop="receiverUser">
              {{ orderInfo.receiverUser }}
            </el-form-item>

            <el-form-item  label="详细地址：" prop="receiverAddress">
              {{ orderInfo.receiverAddress }}
            </el-form-item>

            <el-form-item  label="收件公司：" prop="receiverCompany">
              {{ orderInfo.receiverCompany }}
            </el-form-item>
            <el-form-item  label="中转目的地：" prop="transferPlaceCountyName">
              {{ orderInfo.transferPlaceCountyName }}
            </el-form-item>
          </div>
        </el-card>
      </div>
      <!-- /订单信息   -->
      <el-card class="mb10" shadow="never">
        <CardHeader title="服务信息" />
        <div class="box1">
          <el-form-item  label="运输方式：" prop="transportMethod">
            {{ getDictLabelFormat(transportMethodOptions, orderInfo.transportMethod) }}
          </el-form-item>
          <el-form-item :label="'发货方式：\n（揽收方式）'" class="shippingMethod" clearable prop="orderType">
            {{ getDictLabelFormat(orderTypeOptions, orderInfo.orderType) }}
          </el-form-item>
          <el-form-item  label="交货方式：" prop="handleMethod">
            {{ getDictLabelFormat(handleMethodOptions, orderInfo.handleMethod) }}
          </el-form-item>
        </div>
      </el-card>

      <!-- /货品信息   -->
      <el-card class="mb10 box-mb5" shadow="never">
        <CardHeader title="货品信息" />
        <div class="box">
          <el-form-item v-if="orderInfo.goodsName" label="货品名称：" prop="goodsName">
            {{ orderInfo.goodsName }}
          </el-form-item>
          <el-form-item v-if="orderInfo.goodsPackages" label="件数：" prop="goodsPackages">
            {{ orderInfo.goodsPackages }}
          </el-form-item>
          <el-form-item v-if="orderInfo.goodPackingType" label="包装：" prop="goodPackingType"> {{ getDictLabelFormat(goodPackingTypeOptions, orderInfo.goodPackingType) }}{{ orderInfo.goodPacking }} </el-form-item>
          <el-form-item v-if="orderInfo.productWeight" label="重量：" prop="productWeight">
            {{ orderInfo.productWeight }}
          </el-form-item>
          <el-form-item v-if="orderInfo.productVolume" label="体积：" prop="productVolume">
            {{ orderInfo.productVolume }}
          </el-form-item>
          <el-form-item v-if="orderInfo.accompanyOrderFlag" label="是否有随货单：" prop="accompanyOrderFlag">
            {{ getDictLabelFormat(isAccompanyingBill, orderInfo.accompanyOrderFlag) }}
          </el-form-item>
          <el-form-item v-if="orderInfo.remark" label="备注：" prop="remark">
            {{ orderInfo.remark }}
          </el-form-item>
        </div>
      </el-card>
      <!-- 费用信息   -->
      <el-card class="mb10 box-mb5" shadow="never">
        <CardHeader title="费用信息" />
        <div class="box">
          <el-form-item v-if="orderInfo.paymentMethod" label="付款方式：" prop="paymentMethod">
            {{ getDictLabelFormat(paymentMethodOptions, orderInfo.paymentMethod) }}
          </el-form-item>
          <el-form-item v-if="orderInfo.freightCost" class="regStyle" label="运费：" prop="freightCost">
            <span class="font-cost">{{ orderInfo.freightCost }}</span
            >元
          </el-form-item>
          <el-form-item v-if="orderInfo.collectionGoodsPayment" label="代收货款：" prop="collectionGoodsPayment"> {{ orderInfo.collectionGoodsPayment }}元 </el-form-item>
          <el-form-item v-if="orderInfo.advancePayment" label="垫付费：" prop="advancePayment"> {{ orderInfo.advancePayment }}元 </el-form-item>
          <el-form-item v-if="orderInfo.distributionCost" :label="'送货费：\n（配送费）'" class="shippingMethod regStyle" prop="distributionCost" style="margin-bottom: 0"> {{ orderInfo.distributionCost }}元 </el-form-item>

          <el-form-item v-if="orderInfo.pickUpCost" :label="'接货费：\n（揽收费）'" class="shippingMethod regStyle" prop="pickUpCost" style="margin-bottom: 0"> {{ orderInfo.pickUpCost }}元 </el-form-item>
          <el-form-item v-if="orderInfo.forkliftTruckCost" label="叉/吊车款：" prop="forkliftTruckCost"> {{ orderInfo.forkliftTruckCost }}元 </el-form-item>
          <div class="box_2fr">
            <el-form-item v-if="orderInfo.receiptFlag" label="是否有签回单：" prop="receiptFlag">
              {{ getDictLabelFormat(isAccompanyingBill, orderInfo.receiptFlag) }}
            </el-form-item>
            <el-form-item v-if="orderInfo.receiptCost" label="回单费：" prop="receiptCost" style="margin-left: 5px"> {{ orderInfo.receiptCost }}元 </el-form-item>
          </div>

          <el-form-item v-if="orderInfo.insuredPriceRate" label="保价费率：" prop="insuredPriceRate">
            {{ getDictLabelFormat(insuredPriceRateOptions, orderInfo.insuredPriceRate) }}
          </el-form-item>
          <el-form-item v-if="orderInfo.insuredAmount" label="保价金额：" prop="insuredAmount"> {{ orderInfo.insuredAmount }}元 </el-form-item>
          <el-form-item v-if="orderInfo.insuredPrice" label="保价费：" prop="insuredPrice"> {{ orderInfo.insuredPrice }}元 </el-form-item>
          <el-form-item v-if="orderInfo.brokerCost" label="经纪人费：" prop="brokerCost"> {{ orderInfo.brokerCost }}元 </el-form-item>
          <el-form-item v-if="orderInfo.presentCost" label="现付费：" prop="presentCost"> {{ orderInfo.presentCost }}元 </el-form-item>
          <el-form-item v-if="orderInfo.arrivalCost" label="到付费：" prop="arrivalCost"> {{ orderInfo.arrivalCost }}元 </el-form-item>
          <el-form-item v-if="orderInfo.totalCost" label="应收总金额：" prop="totalCost">
            <span class="font-cost">{{ orderInfo.totalCost }}</span
            >元
          </el-form-item>
          <el-form-item v-if="orderInfo.costRemark" label="备注：" prop="costRemark">
            {{ orderInfo.costRemark }}
          </el-form-item>
        </div>
      </el-card>

      <!-- /付款账户   -->
      <el-card v-if="orderInfo.unitName" class="mb10 box-mb5" shadow="never">
        <CardHeader title="付款账户" />
        <div class="box">
          <el-form-item v-if="orderInfo.unitName" label="单位名称：" prop="unitName">
            {{ orderInfo.unitName }}
          </el-form-item>
          <el-form-item v-if="orderInfo.identificationCode" label="纳税人识别号：" prop="identificationCode">
            {{ orderInfo.identificationCode }}
          </el-form-item>
          <el-form-item v-if="orderInfo.unitAddress" label="单位地址：" prop="unitAddress">
            {{ orderInfo.unitAddress }}
          </el-form-item>
          <el-form-item v-if="orderInfo.contactPhone" label="联系电话：" prop="contactPhone">
            {{ orderInfo.contactPhone }}
          </el-form-item>
          <el-form-item v-if="orderInfo.openingBankNo" label="银行账户：" prop="openingBankNo">
            {{ orderInfo.openingBankNo }}
          </el-form-item>
          <el-form-item v-if="orderInfo.openingBank" label="开户行：" prop="openingBank">
            {{ orderInfo.openingBank }}
          </el-form-item>
          <el-form-item v-if="orderInfo.accountRemark" label="备注：" prop="accountRemark">
            {{ orderInfo.accountRemark }}
          </el-form-item>
        </div>
      </el-card>
      <!-- /签收信息   -->
      <el-card v-if="orderInfo.SignFileList && orderInfo.SignFileList.length > 0" class="mb10 box-mb5" shadow="never">
        <CardHeader title="签收信息" />
        <div>
          <el-descriptions :column="3">
            <el-descriptions-item label="客户签名">
              <template v-if="signPicList && signPicList.length > 0">
                <el-tooltip v-for="img in signPicList" :key="img.id" class="item" content="点击查看大图" effect="dark" placement="top">
                  <el-image :preview-src-list="[img.fileUrl]" :src="img.fileUrl" style="width: 100px; height: 100px; margin-right: 8px" />
                </el-tooltip>
              </template>
              <div v-else class="no-pictures">
                <span>暂无客户签名信息</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="回执单">
              <template v-if="receiptPicList && receiptPicList.length > 0">
                <el-tooltip v-for="img in receiptPicList" class="item" content="点击查看大图" effect="dark" placement="right">
                  <el-image :preview-src-list="[img.fileUrl]" :src="img.fileUrl" style="width: 100px; height: 100px" />
                </el-tooltip>
              </template>
              <div v-else class="no-pictures">
                <span>暂无回执单信息</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="随货单">
              <template v-if="accompanyingBillOfLadingList && accompanyingBillOfLadingList.length > 0">
                <el-tooltip v-for="img in accompanyingBillOfLadingList" class="item" content="点击查看大图" effect="dark" placement="right">
                  <el-image :preview-src-list="[img.fileUrl]" :src="img.fileUrl" style="width: 100px; height: 100px" />
                </el-tooltip>
              </template>
              <div v-else class="no-pictures">
                <span>暂无随货单信息</span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import CardHeader from '@/components/CardHeader';
// import { getGeneralGoodInfo } from '@/api/fourpl/generalOrder';

export default {
  name: 'generalOrderDetail',
  props: {
    orderId: {
      required: true,
      type: String
    }
  },
  components: {
    CardHeader
  },
  data() {
    return {
      fullscreenLoading: false, // 全屏加载
      orderInfo: {},
      orderStatusOptions: [], // 订单状态
      orderTypeOptions: [], // 货物揽收方式
      paymentMethodOptions: [], // 付款方式
      transportMethodOptions: [], // 运输方式
      handleMethodOptions: [], // 交货方式
      goodPackingTypeOptions: [], // goodPackingTypeOptions
      isAccompanyingBill: [
        {
          dictLabel: '是',
          dictValue: '1'
        },
        {
          dictLabel: '否',
          dictValue: '0'
        }
      ], // 是否有随货单
      insuredPriceRateOptions: [], // 保价费率
      signPicList: [], // 签名
      receiptPicList: [], // 回执单
      accompanyingBillOfLadingList: [] // 随货单
    };
  },
  created() {
    // 获取订单状态
    this.getDicts('fourpl_order_status').then((response) => {
      this.orderStatusOptions = response.data;
    });
    // 货物揽收方式
    this.getDicts('fourpl_mail_service').then((response) => {
      this.orderTypeOptions = response.data;
    });
    /** 付款方式 */
    this.getDicts('fourpl_payment_method').then((response) => {
      this.paymentMethodOptions = response.data;
    });
    /** 运输方式*/
    this.getDicts('fourpl_general_order_transport_method').then((response) => {
      this.transportMethodOptions = response.data;
    });
    /** 交货方式*/
    this.getDicts('fourpl_general_order_handle_method').then((response) => {
      this.handleMethodOptions = response.data;
    });
    /** 包装类型*/
    this.getDicts('fourpl_general_order_good_packing_type').then((response) => {
      this.goodPackingTypeOptions = response.data;
    });
    /** 保价费率*/
    this.getDicts('fourpl_partner_insured_price_rate').then((response) => {
      this.insuredPriceRateOptions = response.data;
    });

    this.getGeneralGoodInfo();
  },
  methods: {
    // 获取订单信息
    getGeneralGoodInfo() {
      this.fullscreenLoading = true;
      getGeneralGoodInfo(this.orderId)
        .then((response) => {
          this.orderInfo = response.data;
          this.signPicList = this.orderInfo.SignFileList.filter((item) => item.fileType == '2' && item.fileUrl); // 签名
          this.receiptPicList = this.orderInfo.SignFileList.filter((item) => item.fileType == '1' && item.fileUrl); // 回执单
          this.accompanyingBillOfLadingList = this.orderInfo.SignFileList.filter((item) => item.fileType == '3' && item.fileUrl); // 随货单
          this.fullscreenLoading = false;
        })
        .catch((e) => {
          this.fullscreenLoading = false;
        });
    },
    /** 字典转换 */
    getDictLabelFormat(data, value) {
      return this.selectDictLabel(data, value);
    }
  }
};
</script>

<style lang="scss" scoped>
.p10{
  padding: 10px;
}
.box {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}
.box1 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto;
}
.box_2fr {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}

.shippingMethod {
  ::v-deep .el-form-item__label {
    line-height: 15px;
  }
}
.regStyle {
  ::v-deep .el-form-item__label {
    padding-left: 20px;
  }
}
::v-deep {
  .el-form-item--small.el-form-item {
    margin-bottom: 5px !important;
  }
  .el-form-item__label {
    color: #999999;
  }
  .el-form-item__content {
    font-size: 14px;
    color: #333333;
  }
  .box_ {
    .el-form-item {
      display: flex;
      justify-content: start;
      .el-form-item__label {
        text-align: left !important;
        margin-right: auto;
      }
      .el-form-item__content {
        text-align: right;
        margin-left: 10px !important;
        margin-right: 10px;
      }
    }
  }
  .el-drawer__header {
    margin-bottom: 20px;
  }
}
.font-cost {
  font-size: 20px;
  color: red;
  font-weight: bold;
}
</style>
