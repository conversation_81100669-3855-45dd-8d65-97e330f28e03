<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-18 10:06:52
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-22 11:17:18
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\views\assist\purchasingManagement\component\fineSingle.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="app-container" v-if="open">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog title="订单详情" v-model="open" width="85%" :before-close="beforClose" :show-close="!loading" append-to-body
            destroy-on-close>
            <el-button type="primary" @click="alginOrder" class="alignBtn" v-if="detailRow.status === '3'">复制单据</el-button>
            <el-collapse v-model="activeNames" v-loading="loading">
                <el-collapse-item title="基本信息" name="1">
                    <template #title>
                        <span class="col_title">基本信息</span>
                    </template>
                    <el-descriptions :column="3" border>
                        <el-descriptions-item label="供应商" label-align="left" align="center" label-class-name="my-label"
                            class-name="my-content">{{ list.essentialInformation.supplier.enterpriseName || '--' }}</el-descriptions-item>
                        <el-descriptions-item label="供应商代表" label-align="left" align="center">{{ list.essentialInformation.supplierRepresentative.delegateName|| '--'  }}</el-descriptions-item>
                        <el-descriptions-item label="结算方式" label-align="left" align="center">{{ formDict(settlementMethod,list.essentialInformation.settlementMethod) }}</el-descriptions-item>
                        <el-descriptions-item label="付款期限" label-align="left" align="center">
                            {{ list.essentialInformation.termsPayment ? moment(list.essentialInformation.termsPayment).format('YYYY-MM-DD') : '--'}}
                        </el-descriptions-item>
                        <!-- <el-descriptions-item label="发票类型" label-align="left" align="center">
                            {{ formDict(InvoiceTypeList,list.essentialInformation.invoiceType) }}</el-descriptions-item> -->
                        <el-descriptions-item label="物流方式" label-align="left"
                            align="center">{{ formDict(logisticsMethodsList,list.essentialInformation.logisticsMode) }}</el-descriptions-item>
                        <el-descriptions-item label="库号" label-align="left"
                            align="center">{{ list.essentialInformation.warehouseNumber.warehouseNumber }}</el-descriptions-item>
                        <el-descriptions-item label="经手人" label-align="left"
                            align="center">{{ list.essentialInformation.handledBy.name }}</el-descriptions-item>
                        <el-descriptions-item label="制单人" label-align="left"
                            align="center">{{ list.essentialInformation.preparedBy.name }}</el-descriptions-item>
                        <el-descriptions-item label="自营扣率" label-align="left"
                            align="center">{{ formDict(deductionRateList,list.essentialInformation.selfRate) }}</el-descriptions-item>
                        <el-descriptions-item label="折扣金额" label-align="left"
                            align="center">{{ list.essentialInformation.discountAmount.toFixed(2) }}</el-descriptions-item>
                        <el-descriptions-item label="是否生成合同" label-align="left"
                            align="center">{{ list.essentialInformation.isGenerateContract == '1' ? '是': '否' }}</el-descriptions-item>
                        <el-descriptions-item label="备注" label-align="left"
                            align="left">{{ list.essentialInformation.remark|| '--' }}</el-descriptions-item>
                    </el-descriptions>
                </el-collapse-item>
                <el-collapse-item title="细单信息" name="2">
                    <template #title>
                        <span class="col_title">细单信息</span>
                    </template>
                    <el-table v-loading="loading" :data="list.detailedOrderInformation" border style="margin-top: 30px;"
                        size="small">
                        <el-table-column min-width="80" align="center" label="序号" fixed="left">
                            <template #default="scope">
                                {{ scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column label="商品名称" align="center" prop="tradeName" :show-overflow-tooltip="true"
                            min-width="140" fixed="left" />
                        <el-table-column label="数量" align="center" :show-overflow-tooltip="true" min-width="140"
                            fixed="right">
                            <template #default="scope">
                                <div>
                                    <p style="color: red;font-size: 16px;">
                                        {{ scope.row.quantity }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="单价" align="center" :show-overflow-tooltip="true" min-width="140"
                            fixed="right">
                            <template #default="scope">
                                <div>
                                    <p style="color: red;font-size: 16px;">
                                        {{ scope.row.unitPrice }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="细单合计金额" prop="amountMoney" :show-overflow-tooltip="true" align="center"
                            min-width="100" fixed="right">
                            <template #default="scope">
                                <span style="color: red;font-size: 16px;">{{
                                    (Number(scope.row.quantity || 0) * Number(scope.row.unitPrice || 0)).toFixed(2)
                                }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="到货数量" prop="arrivalQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="合格收货数量" prop="receivingQuantity" :show-overflow-tooltip="true"
                            align="center" min-width="140" />
                        <el-table-column label="入库数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="商品编码" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="商品自编码" prop="commoditySelfCode" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <!-- <el-table-column label="类型" prop="commodityType"
                            :formatter="(row) => formDict(productType, row.commodityType)" :show-overflow-tooltip="true"
                            align="center" min-width="140" /> -->
                        <el-table-column label="生产厂家" prop="manufacture.enterpriseName" :show-overflow-tooltip="true"
                            align="center" min-width="140" />
                        <el-table-column label="产地" prop="originPlace" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="规格" prop="packageSpecification" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="税率" prop="taxRate" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="件装量" prop="ratio" :show-overflow-tooltip="true" align="center"
                            min-width="140" />

                        <el-table-column label="件数" prop="pieceNumber" :show-overflow-tooltip="true" align="center"
                            min-width="140">
                            <template #default="scope">
                                <span>{{ String(Math.floor(((scope.row.quantity ? Number(scope.row.quantity || 0) : 0) /
                                    (scope.row.ratio ? Number(scope.row.ratio || 0) : 1)))) + (scope.row.completeUnit ||
                                        '箱')
                                    + (((scope.row.quantity ? Number(scope.row.quantity || 0) : 0) % (scope.row.ratio ?
                                        Number(scope.row.ratio || 0) : 1)) ? (((scope.row.quantity ? Number(scope.row.quantity
                                            || 0) : 0) % (scope.row.ratio ? Number(scope.row.ratio || 0) : 1)) +
                                            (scope.row.basicUnit)) : '') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="基本单位" prop="basicUnit" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="整件单位" prop="completeUnit" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="剂型" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="生产日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                            min-width="140"
                            :formatter="row => row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'" />
                        <!-- TODO -->
                        <el-table-column label="过期日期" prop="noticeContent" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="有效期" prop="validityTime" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="贮藏温区" prop="storageTemperature" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="库存余量" prop="availableInventory" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="上次购价" prop="lastDifferencePrice" :show-overflow-tooltip="true"
                            align="center" min-width="140" />
                        <el-table-column label="上次供应商" prop="lastSupplier" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                    </el-table>
                </el-collapse-item>
                <el-collapse-item title="入库记录" name="3">
                    <template #title>
                        <span class="col_title">入库记录</span>
                    </template>
                    <el-table :data="list.WarehousingList" border>
                        <el-table-column label="自编码" prop="commodity.commoditySelfCode" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <!-- // TODO -->
                        <el-table-column label="入库单编号" prop="commodityType" :show-overflow-tooltip="true" align="center"
                            :formatter="(row) => row.nodeName" min-width="120" />
                        <el-table-column label="入库日期" prop="intoTime" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="(row) => row.intoTime ? moment(row.intoTime).format('YYYY-MM-DD') : '--'" />
                        <!-- // TODO -->
                        <el-table-column label="入库数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="批号" prop="intoNo" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <!-- // TODO -->
                        <el-table-column label="单据状态" prop="nodeName" align="center" min-width="120" />
                    </el-table>
                </el-collapse-item>
                <el-collapse-item title="合计信息" name="4">
                    <template #title>
                        <span class="col_title">合计信息</span>
                    </template>
                    <div class="total">
                        <p><span>单据数量：</span><span>{{ list.essentialInformation.totalQuantity || '--' }}</span></p>
                        <p><span>单据金额：</span><span>{{ list.essentialInformation.totalAmount || '--'  }}</span></p>
                        <p><span>折后总金额：</span><span>{{ list.essentialInformation.totalAmountAfterDiscount || '--'  }}</span></p>
                    </div>
                </el-collapse-item>
            </el-collapse>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button @click="beforClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
 
<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, watch, defineExpose, onMounted } from 'vue'
import BasicInformation from './basicInformation.vue';
import FineSingle from './fineSingle.vue';
import WarehousingRecords from './warehousingRecords.vue'
import purchasingManagement from '@/api/erp/purchasingManagement'
import moment from 'moment'
const { proxy } = getCurrentInstance();
const loading = ref(false);
const activeNames = ref(['1', '2', '3', '4'])
const InvoiceTypeList = ref([])
const settlementMethod = ref([])
const logisticsMethodsList = ref([])
const deductionRateList = ref([])
const list = ref({
    essentialInformation: {},
    detailedOrderInformation: [],
    WarehousingList: []
})
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    },
    rules: {

    },
});

const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    beforClose: {
        type: Function,
        default: () => { }
    },
    title: {
        type: String,
        default: ''
    },
    modalType: {
        type: String,
        default: ""
    },
    getList: {
        type: Function,
        default: () => { }
    },
    detailRow: {
        type: Object,
        default: () => { { } }
    }

})

const { open, beforClose, title, modalType, getList, detailRow } = toRefs(props)
const { queryParams, rules } = toRefs(data);
const formDict = (data, val) => {
    if(!data?.length || !val) return
    return proxy.selectDictLabel(data, val)
}
/**
 * @description: 获取订单详情
 * @return {*}
 */

const getDetail = async () => {
    loading.value = true
    try {
        let res = await purchasingManagement.getIdOrder({ id: detailRow.value.id })
        if (res.code == 200) {
            list.value.essentialInformation = {
                supplier: res.data?.erpPurchaseOrderDTO?.supplier,
                supplierRepresentative: res.data?.erpPurchaseOrderDTO?.supplierRepresentative,
                settlementMethod: res.data?.erpPurchaseOrderDTO?.settlementMethod,
                termsPayment: res.data?.erpPurchaseOrderDTO?.termsPayment,
                invoiceType: res.data?.erpPurchaseOrderDTO?.invoiceType,
                logisticsMode: res.data?.erpPurchaseOrderDTO?.logisticsMode,
                warehouseNumber: res.data?.erpPurchaseOrderDTO?.warehouseNumber,
                handledBy: res.data?.erpPurchaseOrderDTO?.handledBy,
                preparedBy: res.data?.erpPurchaseOrderDTO?.preparedBy,
                selfRate: res.data?.erpPurchaseOrderDTO?.selfRate,
                remark: res.data?.erpPurchaseOrderDTO?.remark,
                discountAmount: res.data?.erpPurchaseOrderDTO?.discountAmount,
                isGenerateContract: res.data?.erpPurchaseOrderDTO?.isGenerateContract,
                id: res.data?.erpPurchaseOrderDTO?.id,
                totalQuantity: res.data?.erpPurchaseOrderDTO?.totalQuantity,
                totalAmount: res.data?.erpPurchaseOrderDTO?.totalAmount,
                totalAmountAfterDiscount: res.data?.erpPurchaseOrderDTO?.totalAmountAfterDiscount,
            }
            list.value.detailedOrderInformation = res.data?.orderFormDTOS
        }
        // 获取入库记录
        let WarehousingRecords = await purchasingManagement.erpPurchaseWarehousingRecord({ purchaseId: detailRow.value.id })
        if (WarehousingRecords.code == 200) {
            list.value.WarehousingList = WarehousingRecords.data?.records
        }
        loading.value = false
    } catch (error) {
        proxy.msgError(error)
    } finally {
        loading.value = false
    }

}

const alginOrder = () => {
    purchasingManagement.fastCopy({id:detailRow.value.id}).then(res=>{
        if(res.code === 200){
            proxy.msgSuccess('操作成功')
            beforClose.value()
            getList.value()
        }else{
            proxy.msgError(res.msg)
        }
    })
   
}


async function dict() {
    InvoiceTypeList.value = await proxy.getDictList('erp_Invoice_type')  // 发票类型
    settlementMethod.value = await proxy.getDictList('erp_settlement_method') // 结算方式
    logisticsMethodsList.value = await proxy.getDictList('erp_logistics_methods') // 物流方式
    deductionRateList.value = await proxy.getDictList('serp_elfoperated_deduction_rate') // 自营扣率
}
onMounted(() => {
    dict()
    getDetail()
})

</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.total {
    display: flex;
    margin: 10px 20px;

    p {
        margin-right: 50px;

        & span:nth-of-type(1) {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        & span:nth-of-type(2) {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}

.alignBtn {
    position: absolute;
    right: 50px;
    top: 15px
}

::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
    font-weight: bold;
    color: #505050;
    font-size: 13px;
    max-width: 50px;
}
::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-center {
    width: 25%;
}
</style>