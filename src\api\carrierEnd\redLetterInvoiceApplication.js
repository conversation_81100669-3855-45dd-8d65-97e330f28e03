import request from '@/utils/request';

export default {
    // 红冲申请-列表(承运端)
    getListCarrierList: (params) => {
        return request.get('/tms/redRush/apply/carrierList', params);
    },
    // 红冲申请-列表(货主端)
    getListCompanyList: (params) => {
        return request.get('/tms/redRush/apply/companyList', params);
    },
    // 红冲申请撤销
    cancel: (params) => {
        return request.delete('/tms/redRush/apply/revoke', params);
    },
    // 红冲申请-审批记录
    getRecordList: (params) => {
        return request.get('/audit/task/getRecordList', params);
    },
    // 红冲申请-手动开票
    manualInvoice: (params) => {
        return request.get('/tms/redRush/apply/open-red-invoice', params);
    }
};
