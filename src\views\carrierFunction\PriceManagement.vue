<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryParams" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form" @submit.native.prevent>
                <el-form-item label="价格本名称" prop="priceBookName" style="width: 250px">
                    <el-input v-model="queryParams.priceBookName" clearable placeholder="请输入价格本名称" @change="handleQuery" @clear="handleQuery" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="费用类型" prop="costType" style="width: 250px">
                    <el-select v-model="queryParams.costType" clearable placeholder="请选择费用类型" @change="handleQuery">
                        <el-option v-for="item in costTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="价格本类型" prop="type" style="width: 250px">
                    <el-select v-model="queryParams.type" clearable placeholder="请选择价格本类型" @change="handleQuery">
                        <el-option v-for="item in priceBookTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="价格本状态" prop="releaseStatus">
                    <el-select v-model="queryParams.releaseStatus" clearable placeholder="请选择价格本状态" @change="handleQuery">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="激活状态" prop="status">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择激活状态" @change="handleQuery">
                        <el-option label="启用" value="1"></el-option>
                        <el-option label="禁用" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery('queryParams')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button type="primary" @click="newPriceVisible = true">新建价格本</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="ownerPriceTable" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="orderList" :showIndex="true" element-loading-text="加载中..." :max-height="600">
                <template #status="{ row }">
                    <el-switch
                        v-if="row.releaseStatus !== '0' && row.releaseStatus !== '1' && row.releaseStatus !== '4'"
                        v-model="row.status"
                        :before-change="() => handleStatusChange(row)"
                        active-color="#13ce66"
                        active-text="启用"
                        active-value="1"
                        inactive-text="禁用"
                        inactive-value="0"
                        inline-prompt
                        size="small"
                    ></el-switch>
                    <span v-else></span>
                </template>
                <template #releaseTime="{ row }">
                    <span>{{ row.releaseTime }}</span>
                </template>
                <template #logOffTime="{ row }">
                    <span>{{ row.logOffTime }}</span>
                </template>
                <template #type="{ row }">
                    <span>{{ formatDictionaryData('priceBookTypeList', row.type) }}</span>
                </template>
                <template #releaseStatus="{ row }">
                    <span>{{ formatDictionaryData('statusList', row.releaseStatus) }}</span>
                </template>
                <template #costType="{ row }">
                    <span>{{ formatDictionaryData('costTypeList', row.costType) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="row.releaseStatus === '2'" icon="el-icon-check" link size="small" type="success" @click="openPostNotification(row, '3')">发布</el-button>
                    <el-button v-if="row.releaseStatus === '3'" icon="el-icon-close" link size="small" type="danger" @click="openPostNotification(row, '4')">注销</el-button>
                    <el-button v-if="row.releaseStatus === '0'" icon="el-icon-edit" link size="small" type="primary" @click="openAddPrice(row)">添加价格</el-button>
                    <el-button v-if="row.releaseStatus === '2'" icon="el-icon-edit" link size="small" type="warning" @click="openAndImproveThePrice(row)">完善价格</el-button>
                    <el-button v-if="row.releaseStatus === '4' || row.releaseStatus === '3'" icon="el-icon-copy-document" link size="small" type="primary" @click="openPostNotification(row, 'copy')">复制</el-button>
                    <el-button v-if="row.releaseStatus === '0' || row.releaseStatus === '1' || row.releaseStatus === '2'" icon="el-icon-delete" link size="small" type="danger" @click="openPostNotification(row, 'delete')">删除</el-button>
                    <el-button v-if="row.releaseStatus !== '0' && row.releaseStatus !== '1'" icon="el-icon-info-filled" link size="small" type="primary" @click="openToViewThePriceBook(row)">查看价格本</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
        </el-card>

        <!--  /新增价格本 抽屉   -->
        <el-drawer v-if="newPriceVisible" v-model="newPriceVisible" :title="newPriceTitle" size="35vw" @close="hideNewPrice">
            <div v-loading="newPriceLoading" :element-loading-text="newPriceLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="newPriceForm" :model="newPriceForm" :rules="newPriceFormRules" class="newPriceForm" label-width="auto">
                        <el-form-item label="价格本类型" prop="type">
                            <el-select v-model="newPriceForm.type" clearable placeholder="请选择价格本类型" style="width: 100%" @change="setToResetThePriceOfThisName">
                                <el-option v-for="item in priceBookTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="newPriceForm.type == '2'" label="客户名称" prop="companyId">
                            <el-select v-model="newPriceForm.companyId" clearable filterable placeholder="请选择客户" style="width: 100%">
                                <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="newPriceForm.type == '4'" label="承运商名称" prop="companyId">
                            <el-select v-model="newPriceForm.companyId" clearable filterable placeholder="请选择承运商" style="width: 100%">
                                <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="费用类型" prop="costType">
                            <el-select v-model="newPriceForm.costType" clearable placeholder="请选择费用类型" style="width: 100%">
                                <el-option v-for="item in costTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="价格本名称" prop="priceBookName">
                            <el-input v-model="newPriceForm.priceBookName" :formatter="formatThePriceBookName" clearable placeholder="请输入价格本名称" style="width: 100%"></el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="newPriceForm.remark" clearable placeholder="请输入备注" style="width: 100%" type="textarea"></el-input>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideNewPrice">取消</el-button>
                    <el-button type="primary" @click="submitTheNewPriceBook">确定</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  添加价格明细 抽屉  -->
        <el-drawer v-if="addPriceDetailsVisible" v-model="addPriceDetailsVisible" :title="addPriceDetailsTitle" size="35vw" @close="hideAddPriceDetails">
            <div v-loading="addPriceDetailsLoading" :element-loading-text="addPriceDetailsLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never" style="overflow: inherit">
                    <el-form ref="addPriceDetailsForm" :model="addPriceDetailsForm" :rules="addPriceDetailsFormRules" class="addPriceDetailsForm" label-width="auto" size="default">
                        <el-form-item label="价格本名称" prop="priceBookName">
                            <el-input v-model="addPriceDetailsForm.priceBookName" clearable disabled></el-input>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="addPriceDetailsForm.transType" clearable placeholder="请选择运输类型" @change="setPriceTypes">
                                <el-option v-for="item in valuationTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="addPriceDetailsForm.transType === '2'" label="车辆类型" prop="carTypes">
                            <el-tree-select v-model="addPriceDetailsForm.carTypes" :data="typeOptions" :render-after-expand="false" check-on-click-node check-strictly multiple show-checkbox style="width: 100%"></el-tree-select>
                        </el-form-item>
                        <el-form-item v-if="addPriceDetailsForm.costType !== '2'" label="网点" prop="originNetworks">
                            <el-select v-model="addPriceDetailsForm.originNetworks" clearable multiple placeholder="请选择网点" popper-class="select__buttons" style="width: 100%">
                                <div class="box__multiple">
                                    <el-button v-if="branchList.length" :type="addPriceDetailsForm.originNetworks.length === branchList.length ? '' : 'primary'" @click="handleSelectAll('addPriceDetailsForm', 'originNetworks')">{{
                                        addPriceDetailsForm.originNetworks.length === branchList.length ? '取消全选' : '全选'
                                    }}</el-button>
                                    <el-button @click="handleSelectInvert('addPriceDetailsForm', 'originNetworks')">反选</el-button>
                                </div>
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="addPriceDetailsForm.costType === '2'" label="起始网点" prop="originNetworks">
                            <el-select v-model="addPriceDetailsForm.originNetworks" clearable multiple placeholder="请选择起始网点" popper-class="select__buttons" style="width: 100%">
                                <div class="box__multiple">
                                    <el-button v-if="branchList.length" :type="addPriceDetailsForm.originNetworks.length === branchList.length ? '' : 'primary'" @click="handleSelectAll('addPriceDetailsForm', 'originNetworks')">{{
                                        addPriceDetailsForm.originNetworks.length === branchList.length ? '取消全选' : '全选'
                                    }}</el-button>
                                    <el-button @click="handleSelectInvert('addPriceDetailsForm', 'originNetworks')">反选</el-button>
                                </div>
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="addPriceDetailsForm.costType === '2'" label="目的网点" prop="targetNetworks">
                            <el-select v-model="addPriceDetailsForm.targetNetworks" clearable multiple placeholder="请选择目的网点" popper-class="select__buttons" style="width: 100%">
                                <div class="box__multiple">
                                    <el-button v-if="branchList.length" :type="addPriceDetailsForm.targetNetworks.length === branchList.length ? '' : 'primary'" @click="handleSelectAll('addPriceDetailsForm', 'targetNetworks')">{{
                                        addPriceDetailsForm.targetNetworks.length === branchList.length ? '取消全选' : '全选'
                                    }}</el-button>
                                    <el-button @click="handleSelectInvert('addPriceDetailsForm', 'targetNetworks')">反选</el-button>
                                </div>
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productTypes">
                            <el-select v-model="addPriceDetailsForm.productTypes" clearable multiple placeholder="请选择产品分类">
                                <el-option v-for="item in goodsTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温区类型" prop="temperatureTypes">
                            <el-select v-model="addPriceDetailsForm.temperatureTypes" clearable multiple placeholder="请选择温区类型">
                                <el-option v-for="item in temperatureTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="addPriceDetailsForm.transType" label="计算公式" prop="priceBookFormulaName">
                            <el-input v-model="addPriceDetailsForm.priceBookFormulaName" placeholder="请选择计算公式" readonly @focus="handleOpenFormulaSelection('addPriceDetailsForm')"></el-input>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideAddPriceDetails">取消</el-button>
                    <el-button type="primary" @click="submitAddPrice">一键生成</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  公式选择-->
        <el-dialog v-model="showCalculationFormulaDialog" :close-on-click-modal="true" :modal-append-to-body="false" :title="calculationFormulaTitle" class="calculationFormula" width="80vw" @close="hideCalculationFormulaDialog">
            <div v-loading="calculationFormulaLoading" :element-loading-text="calculationFormulaLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="calculationFormulaForm" :inline="true" :model="calculationFormulaForm" class="seache-form" @submit.native.prevent>
                        <el-form-item label="公式编号" prop="code">
                            <el-input v-model="calculationFormulaForm.code" clearable placeholder="请输入公式编号" @clear="getTheFormulaSelectsTheData" @keyup.enter.native="getTheFormulaSelectsTheData"></el-input>
                        </el-form-item>
                        <el-form-item label="公式名称">
                            <el-input v-model="calculationFormulaForm.name" clearable placeholder="请输入公式名称" @clear="getTheFormulaSelectsTheData" @keyup.enter.native="getTheFormulaSelectsTheData"></el-input>
                        </el-form-item>
                        <el-form-item label="运输类型">
                            <el-select v-model="calculationFormulaForm.transType" clearable placeholder="请选择运输类型" style="width: 100%" @change="setPriceTypes">
                                <el-option v-for="item in valuationTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="el-icon-search" type="primary" @click="getTheFormulaSelectsTheData">搜索</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
                <el-card shadow="never">
                    <el-table ref="calculationFormulaList" :data="calculationFormulaList" border fit highlight-current-row max-height="350" stripe style="width: 100%" @current-change="handleCurrentChange">
                        <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
                        <el-table-column align="center" label="公式编号" prop="code" width="180"></el-table-column>
                        <el-table-column label="公式名称" prop="name" width="150"></el-table-column>
                        <el-table-column :formatter="productTypeFormat" label="运输类型" prop="transType" width="150"></el-table-column>
                        <el-table-column label="默认公式" minWidth="120" prop="defaultFormula">
                            <template #default="scope">
                                <span>{{ formatConditions(scope.row.defaultFormula) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="公式说明" prop="description" width="100">
                            <template #default="scope">
                                <el-popover placement="top" width="400px">
                                    <pre>{{ scope.row.description }}</pre>
                                    <template #reference>
                                        <el-link>查看</el-link>
                                    </template>
                                </el-popover>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideCalculationFormulaDialog">取消</el-button>
                    <el-button type="primary" @click="handleClickDeterminesTheSelectionFormula">确定</el-button>
                </div>
            </div>
        </el-dialog>

        <!--  /查看价格本 抽屉  -->
        <el-drawer v-if="viewPriceBookVisible" v-model="viewPriceBookVisible" :title="viewPriceBookTitle" size="80vw" @close="hideViewPriceBook">
            <div v-loading="viewPriceBookLoading" :element-loading-text="viewPriceBookLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card v-show="showSearchViewPriceBookForm" :body-style="{ padding: '10px' }" class="mb10" shadow="never" style="overflow: inherit">
                    <el-form ref="viewPriceBookForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="viewPriceBookForm" class="seache-form" @submit.native.prevent>
                        <el-form-item label="价格本" prop="priceBookId">
                            <el-select v-model="viewPriceBookForm.priceBookId" clearable disabled placeholder="请选择价格本" style="width: 100%">
                                <el-option v-for="item in orderList" :key="item.id" :label="item.priceBookName" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="viewPriceBookForm.transType" clearable placeholder="请选择运输类型" style="width: 100%" @change="priceDataList('see')">
                                <el-option v-for="item in valuationTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="viewPriceBookForm.transType === '2'" v-show="isShowAll" label="车辆类型" prop="carType">
                            <el-tree-select v-model="viewPriceBookForm.carType" :data="typeOptions" :render-after-expand="false" clearable @change="priceDataList('see')"></el-tree-select>
                        </el-form-item>
                        <el-form-item v-if="viewPriceBookForm.costType !== '2'" v-show="isShowAll" label="网点" prop="branchCode">
                            <el-select v-model="viewPriceBookForm.branchCode" clearable placeholder="请选择网点" style="width: 100%" @change="handleNetworkChange($event, viewPriceBookForm.costType)">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="viewPriceBookForm.costType === '1'" v-show="isShowAll" label="地配区域名称" prop="areaCode">
                            <el-select v-model="viewPriceBookForm.areaCode" clearable placeholder="请选择区域" style="width: 100%" @change="priceDataList('see')">
                                <el-option v-for="item in areaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="viewPriceBookForm.costType === '3'" v-show="isShowAll" label="地配区域名称" prop="areaCode">
                            <el-select v-model="viewPriceBookForm.areaCode" clearable placeholder="请选择区域" style="width: 100%" @change="priceDataList('see')">
                                <el-option v-for="item in areaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="viewPriceBookForm.costType === '2'" v-show="isShowAll" label="起始网点" prop="branchCode">
                            <el-select v-model="viewPriceBookForm.branchCode" clearable placeholder="请选择起始网点" style="width: 100%" @change="priceDataList('see')">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="viewPriceBookForm.costType === '2'" v-show="isShowAll" label="目的网点" prop="areaCode">
                            <el-select v-model="viewPriceBookForm.areaCode" clearable placeholder="请选择目的网点" style="width: 100%" @change="priceDataList('see')">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productType">
                            <el-select v-model="viewPriceBookForm.productType" clearable placeholder="请选择产品分类" style="width: 100%" @change="priceDataList('see')">
                                <el-option v-for="item in goodsTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温区类型" prop="temperatureType">
                            <el-select v-model="viewPriceBookForm.temperatureType" clearable placeholder="请选择温区类型" style="width: 100%" @change="priceDataList('see')">
                                <el-option v-for="item in temperatureTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <search-button :is-show-all="isShowAll" @handleQuery="priceDataList('see')" @resetQuery="resetViewPriceBookForm('viewPriceBookForm')" @showAllClick="showAllClick" />
                    </el-form>
                </el-card>
                <!-- / 表格内容 -->
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <div style="margin-bottom: 10px">
                        <el-button v-if="viewPriceBookForm.releaseStatus !== '3' && viewPriceBookForm.releaseStatus !== '4'" type="primary" @click="openSingleAdd('add')">添加单项价格</el-button>
                        <el-button v-if="viewPriceBookForm.releaseStatus !== '3' && viewPriceBookForm.releaseStatus !== '4'" type="primary" @click="openBatchAddSettings">批量添加</el-button>
                        <el-button v-if="viewPriceBookForm.releaseStatus !== '3' && viewPriceBookForm.releaseStatus !== '4' && networkTransportationPriceData.length" type="primary" @click="bulkModification('networkTransportationPriceTotal')">批量修改</el-button>
                        <el-button v-if="viewPriceBookForm.releaseStatus !== '3' && viewPriceBookForm.releaseStatus !== '4' && networkTransportationPriceData.length" :disabled="multiple" type="primary" @click="bulkModificationIsSelected">批量修改选中</el-button>
                        <el-button v-if="isRevoked" type="warning" @click="undoModifications">撤销</el-button>
                        <el-button v-if="viewPriceBookForm.releaseStatus === '2'" :disabled="multiple" type="danger" @click="handleBatchDeletion">批量删除选中</el-button>
                        <el-button type="primary" @click="exportPriceBook">导出价格本</el-button>
                        <right-toolbar v-model:columns="networkTransportationPriceColumns" v-model:show-search="showSearchViewPriceBookForm" table-i-d="networkTransportationPriceData" @queryTable="priceDataList('see')" />
                    </div>
                    <column-table v-loading="loading" :columns="networkTransportationPriceColumns" :data="networkTransportationPriceData" :showCheckBox="true" :showIndex="true" :max-height="600" @selection-change="handleSelectionChangeCheckThePriceBook">
                        <template #transType="{ row }">
                            <span>{{ formatDictionaryData('valuationTypeList', row.transType) }}</span>
                        </template>
                        <template #productType="{ row }">
                            <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                        </template>
                        <template #temperatureType="{ row }">
                            <span>{{ formatDictionaryData('temperatureTypeList', row.temperatureType) }}</span>
                        </template>
                        <template #carType="{ row }">
                            <span v-if="row.carType">{{ formatVehicleTypeData(row.carType) }}</span>
                        </template>
                        <template #opt="{ row }">
                            <el-button v-if="viewPriceBookForm.releaseStatus !== '3' && viewPriceBookForm.releaseStatus !== '4'" icon="el-icon-edit" link size="small" type="primary" @click="openSingleAdd('edit', row)">保存</el-button>
                            <el-button v-if="viewPriceBookForm.releaseStatus === '2'" icon="el-icon-delete" link size="small" type="danger" @click="handleIndividualDeletion(row)">删除</el-button>
                        </template>
                    </column-table>
                    <pagination v-show="networkTransportationPriceTotal > 0" v-model:limit="viewPriceBookForm.size" v-model:page="viewPriceBookForm.current" :total="networkTransportationPriceTotal" @pagination="priceDataList('see')" />
                </el-card>
            </div>
        </el-drawer>

        <!--  /单个添加 抽屉   -->
        <el-drawer v-if="singleAddVisible" v-model="singleAddVisible" :title="singleAddTitle" size="35vw" @close="hideSingleAdd">
            <div v-loading="singleAddLoading" :element-loading-text="singleAddLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never" style="overflow: inherit">
                    <el-form ref="singleAddForm" :model="singleAddForm" :rules="singleAddFormRules" class="singleAddForm" label-width="auto">
                        <el-form-item label="价格本名称" prop="priceBookId">
                            <el-select v-model="singleAddForm.priceBookId" clearable disabled placeholder="请选择价格本" style="width: 100%">
                                <el-option v-for="item in orderList" :key="item.id" :label="item.priceBookName" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="singleAddForm.transType" :disabled="singleAddForm.isEdit" clearable placeholder="请选择运输类型" style="width: 100%">
                                <el-option v-for="item in valuationTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="singleAddForm.transType === '2'" label="车辆类型" prop="carType">
                            <el-tree-select v-model="singleAddForm.carType" :data="typeOptions" :disabled="singleAddForm.isEdit" :render-after-expand="false" style="width: 100%"></el-tree-select>
                        </el-form-item>
                        <el-form-item v-if="singleAddForm.costType !== '2'" label="网点" prop="branchCode">
                            <el-select v-model="singleAddForm.branchCode" :disabled="singleAddForm.isEdit" clearable placeholder="请选择网点" style="width: 100%" @change="handleNetworkChange($event, singleAddForm.costType)">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="singleAddForm.costType === '1'" label="地配区域名称" prop="areaCode">
                            <el-select v-model="singleAddForm.areaCode" :disabled="singleAddForm.isEdit" clearable placeholder="请选择区域" style="width: 100%">
                                <el-option v-for="item in areaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="singleAddForm.costType === '3'" label="地配区域名称" prop="areaCode">
                            <el-select v-model="singleAddForm.areaCode" :disabled="singleAddForm.isEdit" clearable placeholder="请选择区域" style="width: 100%">
                                <el-option v-for="item in areaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="singleAddForm.costType === '2'" label="起始网点" prop="branchCode">
                            <el-select v-model="singleAddForm.branchCode" :disabled="singleAddForm.isEdit" clearable placeholder="请选择起始网点" style="width: 100%">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="singleAddForm.costType === '2'" label="目的网点" prop="areaCode">
                            <el-select v-model="singleAddForm.areaCode" :disabled="singleAddForm.isEdit" clearable placeholder="请选择目的网点" style="width: 100%">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productType">
                            <el-select v-model="singleAddForm.productType" :disabled="singleAddForm.isEdit" clearable placeholder="请选择产品分类" style="width: 100%">
                                <el-option v-for="item in goodsTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温区类型" prop="temperatureType">
                            <el-select v-model="singleAddForm.temperatureType" :disabled="singleAddForm.isEdit" clearable placeholder="请选择温区类型" style="width: 100%">
                                <el-option v-for="item in temperatureTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="单件费用" prop="standardCost">
                            <el-input-number v-model="singleAddForm.standardCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入单件费用" style="min-width: 200px"></el-input-number>
                            <span class="numeric_units">元/件</span>
                        </el-form-item>
                        <el-form-item label="每单费用" prop="perOrderCost">
                            <el-input-number v-model="singleAddForm.perOrderCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入每单费用" style="min-width: 200px"></el-input-number>
                            <span class="numeric_units">元/单</span>
                        </el-form-item>
                        <el-form-item label="重量费用" prop="weightCost">
                            <el-input-number v-model="singleAddForm.weightCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入重量费用" style="min-width: 200px"></el-input-number>
                            <span class="numeric_units">元/千克</span>
                        </el-form-item>
                        <el-form-item label="容积费用" prop="volumeCost">
                            <el-input-number v-model="singleAddForm.volumeCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入容积费用" style="min-width: 200px"></el-input-number>
                            <span class="numeric_units">元/方</span>
                        </el-form-item>
                        <el-form-item label="路程费用" prop="distanceCost">
                            <el-input-number v-model="singleAddForm.distanceCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入路程费用" style="min-width: 200px"></el-input-number>
                            <span class="numeric_units">元/公里</span>
                        </el-form-item>
                        <el-form-item label="预计所需时间" prop="timeNeeded">
                            <el-input-number v-model="singleAddForm.timeNeeded" :min="0" :precision="1" :step="0.5" controls-position="right" placeholder="请输入预计所需时间" style="min-width: 200px"></el-input-number>
                            <span class="numeric_units">小时</span>
                        </el-form-item>
                        <el-form-item v-if="singleAddForm.transType" label="计算公式" prop="priceBookFormulaName">
                            <el-input v-model="singleAddForm.priceBookFormulaName" placeholder="请选择计算公式" readonly @focus="handleOpenFormulaSelection('singleAddForm')"></el-input>
                        </el-form-item>
                        <el-form-item v-if="!singleAddForm.isEdit" label="备注" prop="remark">
                            <el-input v-model="singleAddForm.remark" :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入备注" type="textarea"></el-input>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideSingleAdd">取消</el-button>
                    <el-button v-if="singleAddForm.isEdit" type="primary" @click="submitSingleModification">保存</el-button>
                    <el-button v-else type="primary" @click="submitAndAddPriceBook">确定</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  /批量添加设置 抽屉   -->
        <el-drawer v-model="batchAddSettingsVisible" :title="batchAddSettingsTitle" size="30vw" @close="hideBatchAddSettings">
            <div v-loading="batchAddSettingsLoading" :element-loading-text="batchAddSettingsLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never" style="overflow: inherit">
                    <el-form ref="batchAddSettingsForm" :model="batchAddSettingsForm" :rules="batchAddSettingsFormRules" class="batchAddSettingsForm" label-width="auto">
                        <el-form-item label="价格本" prop="priceBookId">
                            <el-select v-model="batchAddSettingsForm.priceBookId" clearable disabled placeholder="请选择价格本" style="width: 100%">
                                <el-option v-for="item in orderList" :key="item.id" :label="item.priceBookName" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="batchAddSettingsForm.transType" clearable placeholder="请选择运输类型" style="width: 100%">
                                <el-option v-for="item in valuationTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddSettingsForm.transType === '2'" label="车辆类型" prop="carTypes">
                            <el-tree-select v-model="batchAddSettingsForm.carTypes" :data="typeOptions" :render-after-expand="false" check-on-click-node check-strictly multiple show-checkbox style="width: 100%"></el-tree-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddSettingsForm.costType !== '2'" label="网点" prop="originNetworks">
                            <el-select v-model="batchAddSettingsForm.originNetworks" clearable multiple placeholder="请选择网点" popper-class="select__buttons" style="width: 100%">
                                <div class="box__multiple">
                                    <el-button v-if="branchList.length" :type="batchAddSettingsForm.originNetworks.length === branchList.length ? '' : 'primary'" @click="handleSelectAll('batchAddSettingsForm', 'originNetworks')">{{
                                        batchAddSettingsForm.originNetworks.length === branchList.length ? '取消全选' : '全选'
                                    }}</el-button>
                                    <el-button @click="handleSelectInvert('batchAddSettingsForm', 'originNetworks')">反选</el-button>
                                </div>
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddSettingsForm.costType === '2'" label="起始网点" prop="originNetworks">
                            <el-select v-model="batchAddSettingsForm.originNetworks" clearable multiple placeholder="请选择起始网点" popper-class="select__buttons" style="width: 100%">
                                <div class="box__multiple">
                                    <el-button v-if="branchList.length" :type="batchAddSettingsForm.originNetworks.length === branchList.length ? '' : 'primary'" @click="handleSelectAll('batchAddSettingsForm', 'originNetworks')">{{
                                        batchAddSettingsForm.originNetworks.length === branchList.length ? '取消全选' : '全选'
                                    }}</el-button>
                                    <el-button @click="handleSelectInvert('batchAddSettingsForm', 'originNetworks')">反选</el-button>
                                </div>
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddSettingsForm.costType === '2'" label="目的网点" prop="targetNetworks">
                            <el-select v-model="batchAddSettingsForm.targetNetworks" clearable multiple placeholder="请选择目的网点" popper-class="select__buttons" style="width: 100%">
                                <div class="box__multiple">
                                    <el-button v-if="branchList.length" :type="batchAddSettingsForm.targetNetworks.length === branchList.length ? '' : 'primary'" @click="handleSelectAll('batchAddSettingsForm', 'targetNetworks')">{{
                                        batchAddSettingsForm.targetNetworks.length === branchList.length ? '取消全选' : '全选'
                                    }}</el-button>
                                    <el-button @click="handleSelectInvert('batchAddSettingsForm', 'targetNetworks')">反选</el-button>
                                </div>
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productTypes">
                            <el-select v-model="batchAddSettingsForm.productTypes" clearable multiple placeholder="请选择产品分类" style="width: 100%">
                                <el-option v-for="item in goodsTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温区类型" prop="temperatureTypes">
                            <el-select v-model="batchAddSettingsForm.temperatureTypes" clearable multiple placeholder="请选择温区类型" style="width: 100%">
                                <el-option v-for="item in temperatureTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddSettingsForm.transType" label="计算公式" prop="priceBookFormulaName">
                            <el-input v-model="batchAddSettingsForm.priceBookFormulaName" placeholder="请选择计算公式" readonly @focus="handleOpenFormulaSelection('batchAddSettingsForm')"></el-input>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideBatchAddSettings">取消</el-button>
                    <el-button type="primary" @click="submitBatchAdd">一键添加</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  /批量添加价格 抽屉   -->
        <el-drawer v-if="batchAddTransportationPriceVisible" v-model="batchAddTransportationPriceVisible" :title="batchAddTransportationPriceTitle" size="80vw" @close="hideBatchAddTransportationPrice">
            <div v-loading="batchAddTransportationPriceLoading" :element-loading-text="batchAddTransportationPriceLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card v-show="showBatchAddTransportationPriceForm" :body-style="{ padding: '10px' }" class="mb10" shadow="never" style="overflow: inherit">
                    <el-form ref="batchAddTransportationPriceForm" :inline="true" :model="batchAddTransportationPriceForm" :rules="batchAddTransportationPriceFormRules" class="seache-form" @submit.native.prevent>
                        <el-form-item label="价格本" prop="priceBookId">
                            <el-select v-model="batchAddTransportationPriceForm.priceBookId" clearable disabled placeholder="请选择价格本" style="width: 100%" @change="priceDataList('batch')">
                                <el-option v-for="item in orderList" :key="item.id" :label="item.priceBookName" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="batchAddTransportationPriceForm.transType" clearable placeholder="请选择运输类型" style="width: 100%" @change="priceDataList('batch', true)">
                                <el-option v-for="item in valuationTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddTransportationPriceForm.transType === '2'" v-show="isShowAll" label="车辆类型" prop="carType">
                            <el-tree-select v-model="batchAddTransportationPriceForm.carType" :data="typeOptions" :render-after-expand="false" clearable @change="priceDataList('batch')"></el-tree-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddTransportationPriceForm.costType !== '2'" v-show="isShowAll" label="网点" prop="branchCode">
                            <el-select v-model="batchAddTransportationPriceForm.branchCode" clearable placeholder="请选择网点" style="width: 100%" @change="handleNetworkChange($event, batchAddTransportationPriceForm.costType)">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddTransportationPriceForm.costType === '1'" v-show="isShowAll" label="地配区域名称" prop="areaCode">
                            <el-select v-model="batchAddTransportationPriceForm.areaCode" clearable placeholder="请选择区域" style="width: 100%" @change="priceDataList('batch')">
                                <el-option v-for="item in areaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddTransportationPriceForm.costType === '3'" v-show="isShowAll" label="地配区域名称" prop="areaCode">
                            <el-select v-model="batchAddTransportationPriceForm.areaCode" clearable placeholder="请选择区域" style="width: 100%" @change="priceDataList('batch')">
                                <el-option v-for="item in areaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddTransportationPriceForm.costType === '2'" v-show="isShowAll" label="起始网点" prop="branchCode">
                            <el-select v-model="batchAddTransportationPriceForm.branchCode" clearable placeholder="请选择起始网点" style="width: 100%" @change="priceDataList('batch')">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="batchAddTransportationPriceForm.costType === '2'" v-show="isShowAll" label="目的网点" prop="areaCode">
                            <el-select v-model="batchAddTransportationPriceForm.areaCode" clearable placeholder="请选择目的网点" style="width: 100%" @change="priceDataList('batch')">
                                <el-option v-for="item in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productType">
                            <el-select v-model="batchAddTransportationPriceForm.productType" clearable placeholder="请选择产品分类" style="width: 100%" @change="priceDataList('batch')">
                                <el-option v-for="item in goodsTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温区类型" prop="temperatureType">
                            <el-select v-model="batchAddTransportationPriceForm.temperatureType" clearable placeholder="请选择温区类型" style="width: 100%" @change="priceDataList('batch')">
                                <el-option v-for="item in temperatureTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="计算公式" prop="priceBookFormulaId">
                            <el-select v-model="batchAddTransportationPriceForm.priceBookFormulaId" filterable clearable placeholder="请选择计算公式" style="width: 100%" @change="priceDataList('batch', 'priceBookFormulaId')">
                                <el-option v-for="item in priceBookFormulaIdList" :key="item.priceBookFormulaId" :label="item.priceBookFormulaName" :value="item.priceBookFormulaId"></el-option>
                            </el-select>
                        </el-form-item>
                        <search-button :is-show-all="isShowAll" @handleQuery="priceDataList('batch')" @resetQuery="resetBatchAddTransportationPriceForm('batchAddTransportationPriceForm')" @showAllClick="showAllClick" />
                    </el-form>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <div style="margin-bottom: 10px">
                        <!--            <el-button  type="primary" @click="submitBatchModifyPrice">保存</el-button>-->
                        <el-button v-if="batchAddTransportationPriceData.length" type="primary" @click="submitBatchModifyPrice">提交</el-button>
                        <el-button v-if="batchAddTransportationPriceData.length" type="primary" @click="bulkModification('batchAddTransportationPriceTotal')">批量修改</el-button>
                        <el-button :disabled="multipleBulkModification" type="primary" @click="bulkModificationIsSelected">批量修改选中</el-button>
                        <el-button v-if="isRevoked" type="primary" @click="undoModifications">撤销</el-button>
                        <el-button type="success" @click="handleClickImportExpenses">导入费用</el-button>
                        <right-toolbar v-model:columns="batchModificationColumns" v-model:show-search="showBatchAddTransportationPriceForm" table-i-d="batchAddTransportationPriceTable" @queryTable="priceDataList('batch')" />
                    </div>
                    <column-table v-loading="loading" :columns="batchModificationColumns" :data="batchAddTransportationPriceData" :showCheckBox="true" :showIndex="true" :max-height="600" @selection-change="handleSelectionChangeAddPricesInBulk">
                        <template #transType="{ row }">
                            <span>{{ formatDictionaryData('valuationTypeList', row.transType) }}</span>
                        </template>
                        <template #pricingMethod="{ row }">
                            <span>{{ formatDictionaryData('costTypeList', row.costType) }}</span>
                        </template>
                        <template #productType="{ row }">
                            <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                        </template>
                        <template #temperatureType="{ row }">
                            <span>{{ formatDictionaryData('temperatureTypeList', row.temperatureType) }}</span>
                        </template>
                        <template #carType="{ row }">
                            <span v-if="row.carType">{{ formatVehicleTypeData(row.carType) }}</span>
                        </template>
                        <template #standardCost="{ row }">
                            <el-input-number v-model="row.standardCost" :min="0" :step="0.01" controls-position="right" style="width: 120px"></el-input-number>
                        </template>
                        <template #perOrderCost="{ row }">
                            <el-input-number v-model="row.perOrderCost" :min="0" :step="0.01" controls-position="right" style="width: 120px"></el-input-number>
                        </template>
                        <template #weightCost="{ row }">
                            <el-input-number v-model="row.weightCost" :min="0" :step="0.01" controls-position="right" style="width: 120px"></el-input-number>
                        </template>
                        <template #volumeCost="{ row }">
                            <el-input-number v-model="row.volumeCost" :min="0" :step="0.01" controls-position="right" style="width: 120px"></el-input-number>
                        </template>
                        <template #distanceCost="{ row }">
                            <el-input-number v-model="row.distanceCost" :min="0" :step="0.01" controls-position="right" style="width: 120px"></el-input-number>
                        </template>
                        <template #timeNeeded="{ row }">
                            <el-input-number v-model="row.timeNeeded" :min="0" :step="0.5" controls-position="right" style="width: 120px"></el-input-number>
                        </template>
                        <template #priceBookFormulaName="{ row }">
                            <el-input v-model="row.priceBookFormulaName" readonly @focus="handleOpenFormulaSelection('batchAddTransportationPriceForm', row)"></el-input>
                        </template>
                    </column-table>
                    <pagination v-show="batchAddTransportationPriceTotal > 0" v-model:limit="batchAddTransportationPriceForm.size" v-model:page="batchAddTransportationPriceForm.current" :total="batchAddTransportationPriceTotal" @pagination="priceDataList('batch', 'priceBookFormulaId')" />
                </el-card>
            </div>
        </el-drawer>

        <!--  /批量修改 对话框  -->
        <el-dialog v-model="batchModifyVisible" :close-on-click-modal="true" :modal-append-to-body="false" class="dialog__batchModifyPriceForm" title="批量修改" width="450px" @close="handleClickHidesBulkModifications">
            <el-form ref="batchModifyPriceForm" v-loading="batchModifyLoading" :model="batchModifyPriceForm" :rules="batchModifyPriceFormRules" label-width="auto">
                <el-form-item label="运输类型" prop="transType">
                    <el-select v-model="batchModifyPriceForm.transType" clearable disabled placeholder="请选择运输类型" style="width: 100%">
                        <el-option v-for="item in valuationTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="单件费用" prop="standardCost">
                    <el-input-number v-model="batchModifyPriceForm.standardCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入单件费用" style="min-width: 200px"></el-input-number>
                    <span class="numeric_units">元/件</span>
                </el-form-item>
                <el-form-item label="每单费用" prop="perOrderCost">
                    <el-input-number v-model="batchModifyPriceForm.perOrderCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入每单费用" style="min-width: 200px"></el-input-number>
                    <span class="numeric_units">元/单</span>
                </el-form-item>
                <el-form-item label="重量费用" prop="weightCost">
                    <el-input-number v-model="batchModifyPriceForm.weightCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入重量费用" style="min-width: 200px"></el-input-number>
                    <span class="numeric_units">元/千克</span>
                </el-form-item>
                <el-form-item label="容积费用" prop="volumeCost">
                    <el-input-number v-model="batchModifyPriceForm.volumeCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入容积费用" style="min-width: 200px"></el-input-number>
                    <span class="numeric_units">元/方</span>
                </el-form-item>
                <el-form-item label="路程费用" prop="distanceCost">
                    <el-input-number v-model="batchModifyPriceForm.distanceCost" :min="0" :precision="2" :step="0.01" controls-position="right" placeholder="请输入路程费用" style="min-width: 200px"></el-input-number>
                    <span class="numeric_units">元/公里</span>
                </el-form-item>
                <el-form-item label="预计所需时间" prop="timeNeeded">
                    <el-input-number v-model="batchModifyPriceForm.timeNeeded" :min="0" :precision="1" :step="0.5" controls-position="right" placeholder="请输入预计所需时间" style="min-width: 200px"></el-input-number>
                    <span class="numeric_units">小时</span>
                </el-form-item>
                <el-form-item v-if="batchModifyPriceForm.transType" label="计算公式" prop="priceBookFormulaName">
                    <el-input v-model="batchModifyPriceForm.priceBookFormulaName" placeholder="请选择计算公式" readonly @focus="handleOpenFormulaSelection('batchModifyPriceForm')"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                <el-button type="info" @click="handleClickHidesBulkModifications">取消</el-button>
                <el-button type="primary" @click="bulkUpdates">确定</el-button>
            </div>
        </el-dialog>

        <!--  /撤销 对话框  -->
        <el-dialog v-model="undoBulkModificationOfVisible" :title="undoBulkModificationOfTitles" append-to-body center class="dialog__undoBulkModificationOf" width="500px">
            <el-result v-loading="undoBulkModificationOfLoading" :title="undoBulkModificationOfTitle" icon="warning">
                <template slot="extra">
                    <el-button @click="undoBulkModificationOfVisible = false">取消</el-button>
                    <el-button type="primary" @click="undoModifications">确认</el-button>
                </template>
            </el-result>
        </el-dialog>

        <!--  / 批量修改计算公式对话框  -->
        <el-dialog v-model="batchModifyFormulaOfVisible" :title="batchModifyFormulaOfTitles" append-to-body class="dialog__batchModifyFormulaOf" width="500px" @close="handleClickHidesBatchModifyFormulaOf">
            <div v-loading="batchModifyFormulaOfLoading" :element-loading-text="batchModifyFormulaOfLoadingText">
                <el-form ref="batchModifyFormulaOfForm" :model="batchModifyFormulaOfForm" :rules="batchModifyFormulaOfRules" label-width="auto">
                    <el-form-item label="模板下载">
                        <el-button icon="el-icon-download" type="primary" @click="handleDownloadTemplate">费用导入模板【Excel】</el-button>
                    </el-form-item>
                    <el-alert :closable="false" class="mb10" description="请先点击“费用导入模板【Excel】”链接，下载模板，在生成的模板Excel文件中录入费用数据，然后选择该模板文件导入。" show-icon type="info"></el-alert>
                    <el-form-item label="选择导入文件" style="margin-bottom: 0">
                        <el-upload
                            ref="importFile"
                            v-model:file-list="fileList"
                            :action="uploadFileUrl"
                            :auto-upload="false"
                            :before-upload="beforeAvatarUpload"
                            :class="{ 'avatar__uploader__vertical__display': hideUploadTransportPermit, '': !hideUploadTransportPermit }"
                            :data="{
                                id: batchAddTransportationPriceForm.priceBookId
                            }"
                            :headers="headers"
                            :limit="1"
                            :on-error="fileUploadError"
                            :on-progress="fileUploadProgress"
                            :on-remove="handleRemoveFile"
                            :on-success="fileUploadSuccess"
                            :show-file-list="true"
                        >
                            <el-button type="success">选择文件</el-button>
                        </el-upload>
                    </el-form-item>
                    <div v-if="importTemplateInformation.fail" class="box-period">
                        <el-scrollbar>
                            <el-alert v-for="(item, index) in importTemplateInformation.fail" :key="index" :closable="false" :description="'第' + item.rowNum + '行' + item.reason" class="mt10" show-icon type="error"></el-alert>
                        </el-scrollbar>
                    </div>
                </el-form>
            </div>
            <div class="dialog-footer" style="display: flex; justify-content: end; margin-top: 8px">
                <el-button @click="handleClickHidesBatchModifyFormulaOf">取消</el-button>
                <el-button :disabled="importTemplateInformation.fail || fileList.length === 0" type="primary" @click="$refs.importFile.submit()">导入数据</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import priceManagement from '@/api/carrierEnd/priceManagement';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import serviceNetwork from '@/api/logisticsConfiguration/serviceNetwork';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import { selectDictLabel } from '@/utils/dictLabel';
import billingFactorSettings from '@/api/platformFeatures/billingFactorSettings';
import billingParameterSettings from '@/api/platformFeatures/billingParameterSettings';
import { downloadNoData } from '@/utils';
import tool from '@/utils/tool';
import SearchButton from '@/components/searchModule/SearchButton.vue';

/**
 * 转换车辆类型数据
 * @param data
 * @returns {*}
 */
function convertData(data) {
    return data.map((item) => {
        const convertedItem = {
            value: item.typeCode,
            label: item.typeName
        };

        if (item.childList.length > 0) {
            convertedItem.children = convertData(item.childList);
        }

        return convertedItem;
    });
}

export default {
    name: 'PriceManagement',
    components: {
        SearchButton,
        RightToolbar,
        ColumnTable
    },
    data() {
        return {
            showSearch: true,
            queryParams: {
                current: 1,
                size: 10,
                costType: null,
                status: null,
                priceBookType: null,
                releaseStatus: null,
                type: null
            },
            columns: [
                { title: '价格本名称', key: 'priceBookName', align: 'center', minWidth: '260px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '费用类型', key: 'costType', width: '100px', align: 'center', columnShow: true },
                { title: '客户名称', key: 'companyName', width: '200px', align: 'center', columnShow: true },
                { title: '价格本类型', key: 'type', width: '120px', align: 'center', columnShow: true },
                { title: '创建人', key: 'createBy', width: '240px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', width: '170px', align: 'center', columnShow: true },
                { title: '版本', key: 'version', align: 'center', columnShow: true },
                { title: '发布时间', key: 'releaseTime', width: '170px', align: 'center', columnShow: true },
                { title: '注销时间', key: 'logOffTime', width: '170px', align: 'center', columnShow: true },
                { title: '备注', key: 'remark', minWidth: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '价格本状态', key: 'releaseStatus', width: '120px', align: 'center', columnShow: true, fixed: 'right' },
                { title: '激活状态', key: 'status', align: 'center', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '320px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            loading: false,
            orderList: [],
            total: 0,
            costTypeList: [],
            statusList: [],
            newPriceTitle: '新建价格本',
            newPriceVisible: false,
            newPriceLoading: false,
            newPriceLoadingText: '新建中...',
            newPriceForm: {
                costType: null,
                priceBookName: '通用价格本',
                type: null,
                companyId: null,
                remark: ''
            },
            newPriceFormRules: {
                costType: [{ required: true, message: '请选择费用类型', trigger: 'change' }],
                priceBookName: [{ required: true, message: '请输入价格本名称', trigger: 'blur' }],
                type: [{ required: true, message: '请选择价格本类型', trigger: 'change' }],
                companyId: [{ required: true, message: '请选择客户名称', trigger: 'blur' }]
            },
            addPriceDetailsTitle: '添加价格明细',
            addPriceDetailsVisible: false,
            addPriceDetailsLoading: false,
            addPriceDetailsLoadingText: '添加中...',
            addPriceDetailsForm: {
                priceBookName: '',
                transType: '',
                // pricingMethods: [],
                carTypes: [],
                originNetworks: [],
                targetNetworks: [],
                productTypes: [],
                temperatureTypes: [],
                priceBookFormulaName: '',
                priceBookFormulaId: null
            },
            addPriceDetailsFormRules: {
                priceBookName: [{ required: true, message: '请输入价格本名称', trigger: 'blur' }],
                carTypes: [{ required: true, message: '请选择车辆类型', trigger: 'change' }],
                transType: [{ required: true, message: '请选择运输类型', trigger: 'change' }],
                originNetworks: [{ required: true, message: '请选择始发网点', trigger: 'change' }],
                targetNetworks: [{ required: true, message: '请选择目的网点', trigger: 'change' }],
                productTypes: [{ required: true, message: '请选择产品分类', trigger: 'change' }],
                temperatureTypes: [{ required: true, message: '请选择温度类型', trigger: 'change' }],
                priceBookFormulaName: [{ required: true, message: '请选择计算公式', trigger: 'change' }]
            },
            valuationTypeList: [],
            valuationModeList: [],
            branchList: [],
            goodsTypeList: [],
            temperatureTypeList: [],
            viewPriceBookTitle: '揽收价格',
            viewPriceBookVisible: false,
            viewPriceBookLoading: false,
            viewPriceBookLoadingText: '加载中...',
            networkTransportationPriceColumns: [
                { title: '起始网点', key: 'branchName', align: 'center', width: '260px', columnShow: true },
                { title: '目的网点', key: 'areaName', width: '260px', align: 'center', columnShow: true },
                { title: '运输类型', key: 'transType', align: 'center', columnShow: true },
                { title: '产品分类', key: 'productType', align: 'center', columnShow: true },
                { title: '温区类型', key: 'temperatureType', width: '132px', align: 'center', columnShow: true },
                { title: '车辆类型', key: 'carTypeDesc', width: '132px', align: 'center', columnShow: true },
                { title: '单件费用（元/件）', key: 'standardCost', width: '180px', align: 'center', columnShow: true },
                { title: '每单费用（元/单）', key: 'perOrderCost', width: '180px', align: 'center', columnShow: true },
                { title: '重量费用（元/千克）', key: 'weightCost', width: '180px', align: 'center', columnShow: true },
                { title: '容积费用（元/方）', key: 'volumeCost', width: '180px', align: 'center', columnShow: true },
                { title: '路程费用（元/公里）', key: 'distanceCost', width: '180px', align: 'center', columnShow: true },
                { title: '预计所需时间（小时）', key: 'timeNeeded', width: '180px', align: 'center', columnShow: true },
                { title: '计算公式', key: 'priceBookFormulaName', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '130px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            networkTransportationPriceData: [],
            networkTransportationPriceTotal: 0,
            viewPriceBookForm: {
                size: 10,
                current: 1,
                priceBookId: '',
                transType: '',
                carType: null,
                pricingMethod: '',
                branchCode: '',
                areaCode: '',
                productType: '',
                temperatureType: ''
            },
            batchAddSettingsTitle: '批量添加设置',
            batchAddSettingsVisible: false,
            batchAddSettingsLoading: false,
            batchAddSettingsLoadingText: '加载中...',
            batchAddSettingsForm: {
                priceBookId: '',
                transType: '',
                // pricingMethods: [],
                carTypes: [],
                originNetworks: [],
                targetNetworks: [],
                productTypes: [],
                temperatureTypes: [],
                priceBookFormulaName: '',
                priceBookFormulaId: null
            },
            batchAddSettingsFormRules: {
                priceBookId: [{ required: true, message: '请选择价格本', trigger: 'change' }],
                transType: [{ required: true, message: '请选择运输类型', trigger: 'change' }],
                carTypes: [{ required: true, message: '请选择车辆类型', trigger: 'change' }],
                originNetworks: [{ required: true, message: '请选择起始网点', trigger: 'change' }],
                targetNetworks: [{ required: true, message: '请选择目的网点', trigger: 'change' }],
                productTypes: [{ required: true, message: '请选择产品分类', trigger: 'change' }],
                temperatureTypes: [{ required: true, message: '请选择温度类型', trigger: 'change' }],
                priceBookFormulaName: [{ required: true, message: '请选择计算公式', trigger: 'change' }]
            },
            singleAddTitle: '单个添加',
            singleAddVisible: false,
            singleAddLoading: false,
            singleAddLoadingText: '加载中...',
            singleAddForm: {
                priceBookId: '',
                transType: '',
                carType: null,
                pricingMethod: '',
                branchCode: '',
                areaCode: '',
                productType: '',
                temperatureType: '',
                standardCost: undefined,
                perOrderCost: undefined,
                weightCost: undefined,
                volumeCost: undefined,
                distanceCost: undefined,
                timeNeeded: undefined,
                priceBookFormulaName: '',
                priceBookFormulaId: null,
                remark: ''
            },
            singleAddFormRules: {
                priceBookId: [{ required: true, message: '请选择价格本', trigger: 'change' }],
                transType: [{ required: true, message: '请选择运输类型', trigger: 'change' }],
                carType: [{ required: true, message: '请选择车辆类型', trigger: 'change' }],
                branchCode: [{ required: true, message: '请选择起始网点', trigger: 'change' }],
                areaCode: [{ required: true, message: '请选择目的网点', trigger: 'change' }],
                productType: [{ required: true, message: '请选择产品分类', trigger: 'change' }],
                temperatureType: [{ required: true, message: '请选择温区类型', trigger: 'change' }],
                priceBookFormulaName: [{ required: true, message: '请选择计算公式', trigger: 'change' }]
            },
            showBatchAddTransportationPriceForm: true,
            batchAddTransportationPriceTitle: '批量添加运输价格',
            batchAddTransportationPriceVisible: false,
            batchAddTransportationPriceLoading: false,
            batchAddTransportationPriceLoadingText: '加载中...',
            batchAddTransportationPriceForm: {
                size: 10,
                current: 1,
                priceBookName: '',
                // pricingMethods: '',
                valuationModel: '',
                startPlace: '',
                endPlace: '',
                temperatureTypes: '',
                priceBookId: '',
                transType: '',
                carType: null,
                branchCode: '',
                areaCode: '',
                productType: undefined,
                priceBookFormulaId: undefined
            },
            batchAddTransportationPriceFormRules: {},
            batchModificationColumns: [
                { title: '起始网点', key: 'branchName', align: 'center', width: '260px', columnShow: true },
                { title: '目的网点', key: 'areaName', width: '260px', align: 'center', columnShow: true },
                { title: '运输类型', key: 'transType', width: '100px', align: 'center', columnShow: true },
                { title: '产品分类', key: 'productType', align: 'center', columnShow: true },
                { title: '温区类型', key: 'temperatureType', width: '132px', align: 'center', columnShow: true },
                { title: '车辆类型', key: 'carTypeDesc', width: '132px', align: 'center', columnShow: true },
                { title: '单件费用（元/件）', key: 'standardCost', width: '180px', align: 'center', columnShow: true },
                { title: '每单费用（元/单）', key: 'perOrderCost', width: '180px', align: 'center', columnShow: true },
                { title: '重量费用（元/千克）', key: 'weightCost', width: '180px', align: 'center', columnShow: true },
                { title: '容积费用（元/方）', key: 'volumeCost', width: '180px', align: 'center', columnShow: true },
                { title: '路程费用（元/公里）', key: 'distanceCost', width: '180px', align: 'center', columnShow: true },
                { title: '预计所需时间（小时）', key: 'timeNeeded', width: '180px', align: 'center', columnShow: true },
                { title: '计算公式', key: 'priceBookFormulaName', minWidth: '200px', align: 'center', columnShow: true }
            ],
            batchAddTransportationPriceData: [],
            batchAddTransportationPriceTotal: 0,
            standardCostList: [
                { name: '空值（包含0）', value: '1' },
                { name: '非空值', value: '2' }
            ],
            areaList: [],
            batchModifyVisible: false,
            batchModifyLoading: false,
            batchModifyPriceForm: {
                productType: '',
                standardCost: undefined,
                perOrderCost: undefined,
                weightCost: undefined,
                volumeCost: undefined,
                distanceCost: undefined,
                timeNeeded: undefined,
                priceBookFormulaName: '',
                priceBookFormulaId: null
            },
            batchModifyPriceFormRules: {},
            // 是否展示撤销按钮
            isRevoked: false,
            // 存储 批量修改或查看
            editOrViewStatus: '',
            selectData: [],
            // 非单个禁用
            single: true,
            singleBulkModification: true,
            // 非多个禁用
            multiple: true,
            multipleBulkModification: true,
            undoBulkModificationOfTitles: '提示',
            undoBulkModificationOfVisible: false,
            undoBulkModificationOfLoading: false,
            undoBulkModificationOfTitle: '是否撤销批量修改？',
            isSelectedForBatchModification: false,
            // 价格本类型 字典
            priceBookTypeList: [],
            customerList: [],
            showCalculationFormulaDialog: false,
            calculationFormulaTitle: '公式选择',
            calculationFormulaLoading: false,
            calculationFormulaLoadingText: '加载中...',
            calculationFormulaForm: {
                code: '',
                name: '',
                transType: '',
                status: '0',
                publishStatus: '1',
                type: ''
            },
            calculationFormulaList: [],
            // 选中的公式
            theSelectedFormula: {},
            // 选择公式来源
            selectTheFormulaSource: '',
            showSearchViewPriceBookForm: true,
            batchModifyFormulaOfTitles: '价格本导入设置',
            batchModifyFormulaOfVisible: false,
            batchModifyFormulaOfForm: {},
            batchModifyFormulaOfRules: {},
            fileList: [],
            batchModifyFormulaOfLoading: false,
            batchModifyFormulaOfLoadingText: '导入中...',
            importTemplateInformation: {},
            operatorList: [],
            parameterList: [],
            priceList: [],
            // 批量添加价格 修改公式组，临时存储id
            tempAddPriceIdInBulkId: '',
            typeOptions: [],
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            uploadFileUrl: process.env.VUE_APP_API_GAENT + '/cost/priceBookData/import',
            isShowAll: false,
            priceBookFormulaIdList: []
        };
    },
    computed: {
        // 格式化默认公式
        formatConditions() {
            return (val) => {
                if (val && this.operatorList && this.operatorList.length > 0 && this.parameterList && this.parameterList.length > 0 && this.priceList && this.priceList.length > 0) {
                    const dataArr = val.split(/(\+|-|\*|\/|\(|\)|&|\||=|!|>|<)/);
                    dataArr.forEach((item, index) => {
                        if (item.indexOf('@') > -1) {
                            const itemArr = item.split('@');
                            const id = itemArr[0];
                            const type = itemArr[1];
                            if (type === 'Var') {
                                const variableName = this.operatorList.find((item) => item.id === id)?.variableName || '';
                                dataArr[index] = { id, type, value: variableName };
                            } else if (type === 'Param') {
                                const parameterName = this.parameterList.find((item) => item.id === id)?.variableName || '';
                                dataArr[index] = { id, type, value: parameterName };
                            }
                        } else if (item.indexOf('#') > -1) {
                            const otherExpense = this.priceList.find((i) => i.code === item);
                            dataArr[index] = { id: otherExpense.code, type: 'Other', value: otherExpense.name };
                        } else {
                            dataArr[index] = { type: '', value: item };
                        }
                    });
                    // dataArr value 拼接
                    return dataArr.map((item) => item.value).join('');
                } else {
                    return '';
                }
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        formatThePriceBookName() {
            const { type, companyId, costType } = this.newPriceForm;
            let typeName = '';
            let customerIdName = '';
            let companyIdName = '';
            let costTypeName = '';
            let priceBookName = '';

            if (type) {
                typeName = this.priceBookTypeList.find((item) => item.value === type)?.name;
                priceBookName = typeName;
            }
            if (type == '2' && companyId && this.customerList) {
                customerIdName = this.customerList.find((item) => item.companyId == companyId)?.companyName;
            }
            if (type == '4' && companyId && this.customerList) {
                companyIdName = this.customerList.find((item) => item.value == companyId)?.label;
            }
            if (costType) {
                costTypeName = this.costTypeList.find((item) => item.value === costType)?.name.slice(0, 2);
            }
            if (type && costType && type == '1') {
                // costTypeName 插入到 typeName 第二个字后面
                priceBookName = typeName.slice(0, 2) + costTypeName + typeName.slice(2);
            }
            if (type && costType && companyId && type == '2') {
                priceBookName = customerIdName + costTypeName + typeName.slice(2);
            }
            if (type && costType && type == '3') {
                priceBookName = typeName.slice(0, 3) + costTypeName + typeName.slice(3);
            }
            if (type && costType && companyId && type == '4') {
                priceBookName = companyIdName + costTypeName + typeName.slice(3);
            }
            this.newPriceForm.priceBookName = priceBookName + '价格本';
        },
        // 格式化车辆类型数据
        formatVehicleTypeData() {
            return (data) => {
                if (data) {
                    // 从 this.typeOptions 中获取车辆类型数据 第一层找不到，第二层找，依次
                    const type = this.typeOptions.find((item) => item.value === data);
                    if (type) {
                        return type.label;
                    } else {
                        const childType = this.typeOptions.find((item) => item.children && item.children.find((i) => i.value === data));
                        if (childType) {
                            return childType.children.find((i) => i.value === data).label;
                        } else {
                            return '';
                        }
                    }
                } else {
                    return '';
                }
            };
        },
        hideUploadTransportPermit() {
            return this.fileList.length >= 1;
        }
    },
    created() {
        this.getDict();
        // 获取车辆数据
        this.getTreeSelect();
        this.getList();
    },
    methods: {
        beforeAvatarUpload(file) {
            // 限制文件类型excel
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            if (!isExcel) {
                ElMessage.error('上传文件只能是 Excel 格式!');
            }
            return isExcel;
        },
        // 批量修改 - 完善价格本 查看价格本
        bulkModification(type) {
            const { transType, costType } = this.editOrViewStatus === 'batch' ? this.batchAddTransportationPriceForm : this.viewPriceBookForm;
            if (!transType) {
                ElMessage.error('请选择运输类型！');
                return;
            }
            if (this[type]) {
                ElMessageBox.confirm(`可批量修改${this[type]}条，是否继续修改？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        // 打开批量修改弹窗
                        this.batchModifyVisible = true;
                        // 批量修改的运输类型
                        this.batchModifyPriceForm.transType = transType;
                        this.batchModifyPriceForm.costType = costType;
                    })
                    .catch(() => {});
            }
        },
        // 批量修改选中
        bulkModificationIsSelected() {
            const { transType, costType } = this.editOrViewStatus === 'batch' ? this.batchAddTransportationPriceForm : this.viewPriceBookForm;
            // 1、筛选条件必选提示
            if (!transType) {
                ElMessage.error('请选择运输类型！');
                return;
            }
            // 批量修改的运输类型
            this.batchModifyPriceForm.transType = transType;
            this.batchModifyPriceForm.costType = costType;
            // 批量修改选中 打开
            this.isSelectedForBatchModification = true;
            this.batchModifyVisible = true;
        },
        // 批量更新价格本明细价格
        bulkUpdates() {
            this.$refs.batchModifyPriceForm.validate((valid) => {
                if (valid) {
                    // 批量修改选中
                    if (this.isSelectedForBatchModification) {
                        this.batchModifyLoading = true;
                        // batchAddTransportationPriceData 提取 id 和 standardCost
                        const data = this.selectData.map((item) => {
                            return {
                                id: item.id,
                                standardCost: this.batchModifyPriceForm.standardCost,
                                perOrderCost: this.batchModifyPriceForm.perOrderCost,
                                weightCost: this.batchModifyPriceForm.weightCost,
                                volumeCost: this.batchModifyPriceForm.volumeCost,
                                distanceCost: this.batchModifyPriceForm.distanceCost,
                                timeNeeded: this.batchModifyPriceForm.timeNeeded,
                                priceBookFormulaId: this.batchModifyPriceForm.priceBookFormulaId,
                                priceBookFormulaName: this.batchModifyPriceForm.priceBookFormulaName
                            };
                        });
                        // data 数组 去除 undefined
                        const newData = data.filter((item) => item);
                        priceManagement
                            .batchUpdatePriceData(newData)
                            .then((res) => {
                                if (res.code === 200) {
                                    ElMessage.success('批量修改成功！');
                                    this.batchModifyVisible = false;
                                    this.handleClickHidesBulkModifications();
                                    // 批量修改选中 关闭
                                    this.isSelectedForBatchModification = false;
                                    if (this.editOrViewStatus === 'batch') {
                                        this.priceDataList('batch');
                                    } else {
                                        this.priceDataList('see');
                                    }
                                } else {
                                    ElMessage.error(res.msg);
                                }
                            })
                            .finally(() => {
                                this.batchModifyLoading = false;
                            })
                            .catch(() => {});
                    }
                    // 批量修改
                    else {
                        const { priceBookId, transType, carType, branchCode, areaCode, productType, temperatureType } = this.editOrViewStatus === 'batch' ? this.batchAddTransportationPriceForm : this.viewPriceBookForm;
                        const { standardCost, perOrderCost, weightCost, volumeCost, distanceCost, timeNeeded, priceBookFormulaId } = this.batchModifyPriceForm;
                        const query = {
                            priceBookId,
                            transType,
                            carType,
                            branchCode,
                            areaCode,
                            productType,
                            temperatureType
                        };
                        const data = {
                            standardCost,
                            perOrderCost,
                            weightCost,
                            volumeCost,
                            distanceCost,
                            timeNeeded,
                            priceBookFormulaId
                        };

                        this.batchModifyLoading = true;
                        priceManagement
                            .changePrice({ query, data })
                            .then((res) => {
                                if (res.code === 200) {
                                    ElMessage.success('批量修改成功！');
                                    this.batchModifyVisible = false;
                                    this.handleClickHidesBulkModifications();
                                    if (this.editOrViewStatus === 'batch') {
                                        this.priceDataList('batch');
                                    } else {
                                        this.priceDataList('see');
                                    }
                                    // 是否展示撤销按钮
                                    this.getIsRevoke(priceBookId);
                                } else {
                                    ElMessage.error(res.msg);
                                }
                            })
                            .finally(() => {
                                this.batchModifyLoading = false;
                                this.batchModifyPriceForm.priceBookFormulaId = null;
                            })
                            .catch(() => {});
                    }
                }
            });
        },
        exportPriceBook() {
            priceManagement.exportPriceData({ filename: '价格本数据.xls', ...this.viewPriceBookForm }, '', '', 'blob').then((res) => {
                downloadNoData(res, 'application/vnd.ms-excel', '价格本数据.xlsx');
            });
        },
        /**
         * 数据导入失败回调
         */
        fileUploadError() {
            // 提示错误信息
            ElMessage.error('数据导入失败！');
            this.uploadLoading.close();
        },
        /**
         * 导入数据回调
         */
        fileUploadProgress() {
            this.uploadLoading = ElLoading.service({
                lock: true,
                text: '数据导入中...',
                background: 'rgba(0, 0, 0, 0.7)'
            });
        },
        fileUploadSuccess(res) {
            this.uploadLoading.close();
            this.importTemplateInformation = res.data;
            const { fail, success } = res.data;
            if (fail.length === 0) {
                ElMessage.success('成功导入' + success + '条数据！');
                this.handleClickHidesBatchModifyFormulaOf();
                this.priceDataList('batch');
            }
        },
        /**
         * 获取客户和承运商列表
         */
        getAListOfCustomersAndCarriers() {
            this.newPriceLoading = true;
            this.newPriceLoadingText = '加载中...';
            this.customerList = [];
            enterpriseCooperation
                .cooperateSelect({ status: '1' })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.customerList = res.data;
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .finally(() => {
                    this.newPriceLoading = false;
                })
                .catch(() => {});
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.priceBookTypeList = await this.getDictList('cost_price_book_type');
            this.statusList = await this.getDictList('cost_price_book_release_status');
            this.costTypeList = await this.getDictList('cost_price_book_cost_type');
            this.valuationModeList = await this.getDictList('cost_pricing_type');
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.temperatureTypeList = await this.getDictList('fourpl_temperature_type');
            this.valuationTypeList = await this.getDictList('fourpl_product_type');
            this.priceList = await this.getDictList('cost_price_of_this_parameter');
        },
        // 查询是否存在可撤销数据
        getIsRevoke(id) {
            priceManagement
                .getRevokeCount({ id })
                .then((res) => {
                    if (res.code === 200) {
                        this.isRevoked = res.data;
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .catch(() => {});
        },
        getList() {
            this.loading = true;
            priceManagement
                .priceBookList(this.queryParams)
                .then((res) => {
                    if (res.code === 200) {
                        this.orderList = res.data.records || [];
                        this.total = res.data.total || 0;
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .finally(() => {
                    this.loading = false;
                })
                .catch(() => {});
        },
        // 获取 获取计费因子列表 获取计费公式列表 获取价格本变量列表
        getListOfParametersAndVariables() {
            // 获取计费因子列表
            billingFactorSettings
                .billingFactorListByFormula({})
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.operatorList = res.data || [];
                    } else {
                        ElMessage.error('无可用计费因子');
                        return false;
                    }
                })
                .catch(() => {});

            // 获取计费公式列表
            billingParameterSettings
                .billingParameterListByFormulaCopy({})
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.parameterList = res.data || [];
                    } else {
                        ElMessage.error('无可用计费公式');
                        return false;
                    }
                })
                .catch(() => {});
        },
        // 获取网点揽收区域
        async getNetworkTransportationPrice() {
            const res = await serviceNetwork.getBranchList();
            if (res.code === 200 && res.data) {
                this.branchList = res.data || [];
            } else {
                ElMessage.error(res.msg);
            }
        },
        // 获取公式选择数据
        getTheFormulaSelectsTheData() {
            this.calculationFormulaLoading = true;
            this.calculationFormulaLoadingText = '加载中...';
            priceManagement
                .getPriceBookFormulaList({ ...this.calculationFormulaForm })
                .then((res) => {
                    if (res.code === 200) {
                        this.calculationFormulaList = res.data || [];
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .finally(() => {
                    this.calculationFormulaLoading = false;
                })
                .catch(() => {});
        },
        /**
         * 查询车辆类型下拉树结构
         */
        getTreeSelect() {
            priceManagement
                .getCarTypeTree()
                .then((response) => {
                    if (response.code === 200) {
                        // 将 response.data 中每一级节点的 id 转换为 value，typeName 转换为 label，childList 转换为 children
                        this.typeOptions = convertData(response.data);
                    }
                })
                .catch(() => {});
        },
        /**
         * 批量删除价格本明细
         */
        handleBatchDeletion() {
            if (this.selectData.length) {
                // 删除确认
                ElMessageBox.confirm(`是否删除选中的${this.selectData.length}条价格本明细？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        const ids = this.selectData.map((item) => item.id);
                        priceManagement
                            .deletePriceData({ ids: ids.join(',') })
                            .then((res) => {
                                if (res.code === 200) {
                                    ElMessage.success('删除成功！');
                                    this.priceDataList('see');
                                } else {
                                    ElMessage.error(res.msg);
                                }
                            })
                            .catch(() => {});
                    })
                    .catch(() => {});
            } else {
                ElMessage.warning('请选择要删除的价格本明细！');
            }
        },
        // 公式选择 确定事件
        handleClickDeterminesTheSelectionFormula() {
            // 清空选择后 this.theSelectedFormula 变为null，初始为{}，需要判断
            if (this.theSelectedFormula) {
                const { id, name } = this.theSelectedFormula;
                if (!id) {
                    ElMessage.warning('请选择公式！');
                    return;
                }
                // 选择的公式赋值给表单
                // 判断 this.selectTheFormulaSource 来确定是哪个表单
                if (this.selectTheFormulaSource) {
                    this[this.selectTheFormulaSource].priceBookFormulaName = name;
                    this[this.selectTheFormulaSource].priceBookFormulaId = id;
                }
                // 没有来源，说明是完善价格的列表选择公式
                if (!this.selectTheFormulaSource) {
                    this.batchAddTransportationPriceData.forEach((item) => {
                        if (item.id === this.tempAddPriceIdInBulkId) {
                            item.priceBookFormulaName = name;
                            item.priceBookFormulaId = id;
                        }
                    });
                    this.tempAddPriceIdInBulkId = '';
                }
                this.hideCalculationFormulaDialog();
            } else {
                ElMessage.warning('请选择公式！');
            }
        },
        // 隐藏价格本导入设置弹窗
        handleClickHidesBatchModifyFormulaOf() {
            this.batchModifyFormulaOfVisible = false;
            this.$refs.batchModifyFormulaOfForm.resetFields();
            this.fileList = [];
            this.importTemplateInformation = {};
        },
        // 隐藏批量修改价格弹窗
        handleClickHidesBulkModifications() {
            this.batchModifyVisible = false;
            this.$refs.batchModifyPriceForm.resetFields();
            // this.batchModifyPriceForm = {
            //   transType: this.batchModifyPriceForm.transType
            // };
            this.isSelectedForBatchModification = false;
        },
        // 导入费用
        handleClickImportExpenses() {
            // 打开 价格本导入设置 弹窗
            this.batchModifyFormulaOfVisible = true;
        },
        // 公式选择 选中事件
        handleCurrentChange(val) {
            this.theSelectedFormula = val;
        },
        // 费用导入模板【Excel】下载
        handleDownloadTemplate() {
            if (!this.batchAddTransportationPriceForm.priceBookId) {
                ElMessage.warning('请选择价格本！');
                return;
            }
            priceManagement.downloadPriceBookTemplate({ filename: '费用导入模板.xls', ...this.batchAddTransportationPriceForm }, '', '', 'blob').then((res) => {
                downloadNoData(res, 'application/vnd.ms-excel', '费用导入模板.xlsx');
            });
        },
        /**
         * 单个删除价格本明细
         * @param row
         */
        handleIndividualDeletion(row) {
            // 删除确认
            ElMessageBox.confirm(`是否删除该价格本明细？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    priceManagement
                        .deletePriceData({ ids: row.id })
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('删除成功！');
                                this.priceDataList('see');
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {});
        },
        /**
         * 网点选择事件
         * @param e 网点id
         * @param type 1-揽收 3-配送
         */
        handleNetworkChange(e, type) {
            this.batchAddTransportationPriceForm.areaCode = '';
            this.viewPriceBookForm.areaCode = '';
            this.singleAddForm.areaCode = this.singleAddForm.isEdit ? this.singleAddForm.areaCode : '';
            if (e) {
                // 1-揽收 区域接口  3-配送 区域接口
                if (type === '1') {
                    serviceNetwork
                        .getBranchAreaList({ branchCode: e, areaClass: '1' })
                        .then((res) => {
                            if (res.code === 200) {
                                this.areaList = res.data;
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .catch(() => {});
                } else if (type === '3') {
                    serviceNetwork
                        .getBranchAreaList({ branchCode: e, areaClass: '2' })
                        .then((res) => {
                            if (res.code === 200) {
                                this.areaList = res.data;
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .catch(() => {});
                }
            } else {
                this.areaList = [];
            }
        },
        // 打开公式选择弹窗
        handleOpenFormulaSelection(type, row) {
            this.showCalculationFormulaDialog = true;
            // 获取字典数据
            this.getListOfParametersAndVariables();
            if (type && !row) {
                // 获取数据
                if (this[type].transType || this[type].transType) {
                    this.calculationFormulaForm.transType = this[type].transType || this[type].transType;
                    this.calculationFormulaForm.type = this[type].costType;
                    this.getTheFormulaSelectsTheData();
                }
                this.selectTheFormulaSource = type;
            }
            if (type && row) {
                const { transType, id } = row;
                this.calculationFormulaForm.transType = transType;
                this.calculationFormulaForm.type = this[type].costType;
                this.selectTheFormulaSource = '';
                this.tempAddPriceIdInBulkId = id;
                this.getTheFormulaSelectsTheData();
            }
        },
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        handleRemoveFile(file, fileList) {
            this.fileList = fileList;
            this.importTemplateInformation = {};
        },
        /**
         * 网点全选
         * @param type 表单类型
         * @param dotsType 网点类型
         */
        handleSelectAll(type, dotsType) {
            // 如果已经全选，则取消全选
            // 将 branchList 中的所有 branchCode 赋值给 this[type][dotsType]
            if (this[type][dotsType].length === this.branchList.length) {
                this[type][dotsType] = [];
            } else {
                this[type][dotsType] = this.branchList.map((item) => item.branchCode);
            }
        },
        /**
         * 网点反选
         * @param type 表单类型
         * @param dotsType 网点类型
         */
        handleSelectInvert(type, dotsType) {
            // 对this[type][dotsType] 中的网点进行反选
            // 将 branchList 中的所有 branchCode 赋值给 this[type][dotsType]
            this[type][dotsType] = this.branchList.map((item) => item.branchCode).filter((item) => !this[type][dotsType].includes(item));
        },
        // 复选框选中事件 批量修改
        handleSelectionChangeAddPricesInBulk(selection) {
            this.selectData = selection;
            this.singleBulkModification = selection.length !== 1;
            this.multipleBulkModification = !selection.length;
        },
        // 复选框选中事件 查看价格本
        handleSelectionChangeCheckThePriceBook(selection) {
            this.selectData = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        handleStatusChange(row) {
            const { id, status } = row;
            if (id && status) {
                this.loading = true;
                priceManagement
                    .priceBookActive({ id, status: status === '0' ? '1' : '0' })
                    .then((res) => {
                        if (res.code === 200) {
                            ElMessage.success('操作成功！');
                        } else {
                            ElMessage.error(res.msg);
                        }
                    })
                    .catch(() => {
                        // 按钮状态还原
                        row.status = !status;
                    })
                    .finally(() => {
                        this.loading = false;
                        this.getList();
                    });
            }
        },
        // 隐藏 添加价格明细
        hideAddPriceDetails() {
            this.addPriceDetailsVisible = false;
            this.$refs.addPriceDetailsForm.resetFields();
            this.addPriceDetailsForm = {
                priceBookName: '',
                transType: '',
                // pricingMethods: [],
                carTypes: [],
                originNetworks: [],
                targetNetworks: [],
                productTypes: [],
                temperatureTypes: [],
                name: '',
                priceBookFormulaId: null
            };
        },
        hideBatchAddSettings() {
            this.batchAddSettingsVisible = false;
            this.$refs.batchAddSettingsForm.resetFields();
            this.batchAddSettingsForm = { priceBookId: this.batchAddSettingsForm.priceBookId, id: this.batchAddSettingsForm.id, transType: '', carTypes: [], originNetworks: [], targetNetworks: [], productTypes: [], temperatureTypes: [] };
        },
        hideBatchAddTransportationPrice() {
            this.batchAddTransportationPriceVisible = false;
            this.$refs.batchAddTransportationPriceForm.resetFields();
            this.batchAddTransportationPriceForm.size = 10;
            this.batchAddTransportationPriceForm.current = 1;
        },
        // 隐藏公式选择弹窗
        hideCalculationFormulaDialog() {
            this.showCalculationFormulaDialog = false;
            // 清空公式选择
            this.$refs.calculationFormulaList.setCurrentRow();
            this.$refs.calculationFormulaForm.resetFields();
        },
        hideNewPrice() {
            this.newPriceVisible = false;
            this.$refs.newPriceForm.resetFields();
        },
        hideSingleAdd() {
            this.singleAddVisible = false;
            this.$refs.singleAddForm.resetFields();
            // this.singleAddForm priceBookId costType 不重置 其他参数重置
            // 用于单个添加价格
            this.singleAddForm = {
                priceBookId: this.singleAddForm.priceBookId,
                costType: this.singleAddForm.costType
            };
        },
        hideViewPriceBook() {
            this.viewPriceBookVisible = false;
            this.$refs.viewPriceBookForm.resetFields();
            this.viewPriceBookForm.size = 10;
            this.viewPriceBookForm.current = 1;
        },
        /**
         * 转换车辆类型数据结构
         * @param node
         * @returns {{children, id, label}}
         */
        normalizer(node) {
            if (node.children && !node.children.length) {
                delete node.children;
            }
            return {
                id: node.typeCode,
                label: node.typeName,
                children: node.children
            };
        },
        // 打开 添加价格
        openAddPrice(row) {
            this.addPriceDetailsVisible = true;
            const { costType, id, priceBookName } = row;
            this.addPriceDetailsTitle = `添加价格 - ${priceBookName}`;
            this.addPriceDetailsForm.priceBookName = priceBookName;
            this.addPriceDetailsForm.id = id;
            this.addPriceDetailsForm.costType = costType;
            this.getNetworkTransportationPrice();
        },
        // 打开批量添加运输价格 抽屉
        openAndImproveThePrice(row) {
            this.batchAddTransportationPriceVisible = true;
            const { id, priceBookName, costType } = row;
            if (costType === '1') {
                this.batchAddTransportationPriceTitle = `批量添加揽收价格 - ${priceBookName}`;
                // this.batchModificationColumns networkName 的 title 改为 网点 areaName 的 title 改为 地配区域名称
                this.batchModificationColumns[0].title = '网点';
                this.batchModificationColumns[1].title = '地配区域名称';
            } else if (costType === '2') {
                this.batchAddTransportationPriceTitle = `批量添加运输价格 - ${priceBookName}`;
                // this.batchModificationColumns networkName 的 title 改为 起始网点 areaName 的 title 改为 目的网点
                this.batchModificationColumns[0].title = '起始网点';
                this.batchModificationColumns[1].title = '目的网点';
            } else {
                this.batchAddTransportationPriceTitle = `批量添加配送价格 - ${priceBookName}`;
                // this.batchModificationColumns networkName 的 title 改为 网点 areaName 的 title 改为 地配区域名称
                this.batchModificationColumns[0].title = '网点';
                this.batchModificationColumns[1].title = '地配区域名称';
            }
            this.batchAddTransportationPriceForm.priceBookId = id;
            this.batchAddTransportationPriceForm.costType = costType;
            this.getNetworkTransportationPrice();
            // 获取价格本列表数据
            this.priceDataList('batch');
            // 存储类型 用于批量修改提交时判断
            this.editOrViewStatus = 'batch';
            // 是否展示撤销按钮
            this.getIsRevoke(id);
        },
        // 打开 批量添加设置抽屉
        openBatchAddSettings() {
            this.batchAddSettingsVisible = true;
            this.getNetworkTransportationPrice();
        },
        // 打开 发布/注销/复制 价格本 对话框
        openPostNotification(row, type) {
            const { id, priceBookName } = row;
            if (type === '3') {
                ElMessageBox.confirm(`您确定要发布${priceBookName}价格本吗？`, '发布价格本', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        priceManagement.priceBookPublish({ id, releaseStatus: type }).then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('发布成功！');
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        });
                    })
                    .catch(() => {});
            } else if (type === '4') {
                ElMessageBox.confirm(`您确定要注销${priceBookName}价格本吗？`, '注销价格本', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        priceManagement.priceBookPublish({ id, releaseStatus: type }).then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('注销成功！');
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        });
                    })
                    .catch(() => {});
            } else if (type === 'copy') {
                ElMessageBox.confirm(`您确定要复制${priceBookName}价格本吗？`, '复制价格本', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        priceManagement
                            .priceBookCopy({ id })
                            .then((res) => {
                                if (res.code === 200) {
                                    ElMessage.success('复制成功！');
                                    this.getList();
                                } else {
                                    ElMessage.error(res.msg);
                                }
                            })
                            .catch(() => {});
                    })
                    .catch(() => {});
            } else if (type === 'delete') {
                ElMessageBox.confirm(`您确定要删除${priceBookName}价格本吗？`, '删除价格本', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        priceManagement
                            .deletePriceBook({ ids: id })
                            .then((res) => {
                                if (res.code === 200) {
                                    ElMessage.success('删除成功！');
                                    this.getList();
                                } else {
                                    ElMessage.error(res.msg);
                                }
                            })
                            .catch(() => {});
                    })
                    .catch(() => {});
            }
        },
        openSingleAdd(type, row) {
            this.singleAddVisible = true;
            this.singleAddTitle = type === 'add' ? '单个添加' : '修改价格';
            this.getNetworkTransportationPrice();
            this.singleAddForm.isEdit = type !== 'add';
            if (type === 'edit') {
                this.$nextTick(() => {
                    this.singleAddForm = Object.assign({}, this.singleAddForm, row);
                    const { costType, branchCode } = this.singleAddForm;
                    this.handleNetworkChange(branchCode, costType);
                });
            }
        },
        // 打开 查看价格本 抽屉
        openToViewThePriceBook(row) {
            this.viewPriceBookVisible = true;
            const { id, priceBookName, costType, releaseStatus } = row;
            if (costType === '1') {
                this.viewPriceBookTitle = `查看揽收价格 - ${priceBookName}`;
                // this.networkTransportationPriceColumns networkName 的 title 改为 网点 areaName 的 title 改为 地配区域名称
                this.networkTransportationPriceColumns[0].title = '网点';
                this.networkTransportationPriceColumns[1].title = '地配区域名称';
                this.singleAddFormRules.branchCode[0].message = '请选择网点';
                this.singleAddFormRules.areaCode[0].message = '请选择区域';
                this.batchAddSettingsFormRules.originNetworks[0].message = '请选择网点';
                this.batchAddSettingsFormRules.targetNetworks[0].message = '请选择区域';
            } else if (costType === '2') {
                this.viewPriceBookTitle = `查看运输价格 - ${priceBookName}`;
                // this.networkTransportationPriceColumns networkName 的 title 改为 起始网点 areaName 的 title 改为 目的网点
                this.networkTransportationPriceColumns[0].title = '起始网点';
                this.networkTransportationPriceColumns[1].title = '目的网点';
                this.singleAddFormRules.branchCode[0].message = '请选择起始网点';
                this.singleAddFormRules.areaCode[0].message = '请选择目的网点';
                this.batchAddSettingsFormRules.originNetworks[0].message = '请选择起始网点';
                this.batchAddSettingsFormRules.targetNetworks[0].message = '请选择目的网点';
            } else {
                this.viewPriceBookTitle = `查看配送价格 - ${priceBookName}`;
                // this.networkTransportationPriceColumns networkName 的 title 改为 网点 areaName 的 title 改为 地配区域名称
                this.networkTransportationPriceColumns[0].title = '网点';
                this.networkTransportationPriceColumns[1].title = '地配区域名称';
                this.singleAddFormRules.branchCode[0].message = '请选择网点';
                this.singleAddFormRules.areaCode[0].message = '请选择区域';
                this.batchAddSettingsFormRules.originNetworks[0].message = '请选择网点';
                this.batchAddSettingsFormRules.targetNetworks[0].message = '请选择区域';
            }
            this.viewPriceBookForm.priceBookId = id;
            // 用于单个添加价格
            this.singleAddForm.costType = costType;
            this.singleAddForm.priceBookId = id;
            // 用于批量添加价格
            this.batchAddSettingsForm.costType = costType;
            this.batchAddSettingsForm.priceBookId = id;
            this.batchAddSettingsForm.id = id;
            // 用于查看价格本 form
            this.viewPriceBookForm.costType = costType;
            // !后端说暂时不要
            // this.viewPriceBookForm.id = id;
            this.viewPriceBookForm.releaseStatus = releaseStatus;
            // 已发布状态下 查看价格本 抽屉中的 编辑列隐藏
            if (releaseStatus !== '3' && releaseStatus !== '4') {
                // this.networkTransportationPriceColumns key 为 opt 的列 columnShow 为 false
                this.networkTransportationPriceColumns.forEach((item) => {
                    if (item.key === 'opt') {
                        item.columnShow = true;
                    }
                });
            } else {
                this.networkTransportationPriceColumns.forEach((item) => {
                    if (item.key === 'opt') {
                        item.columnShow = false;
                    }
                });
            }

            this.getNetworkTransportationPrice();
            // 获取价格本列表数据
            this.priceDataList('see');
            // 存储类型 用于批量修改提交时判断
            this.editOrViewStatus = 'see';
        },
        // 获取价格本列表数据
        priceDataList(type, noSeachType) {
            // batch 批量 see 查看
            if (type === 'batch') {
                this.batchAddTransportationPriceLoading = true;
                this.batchAddTransportationPriceLoadingText = '加载中...';
                priceManagement
                    .priceDataList(this.batchAddTransportationPriceForm)
                    .then((res) => {
                        if (res.code === 200 && res.data) {
                            this.batchAddTransportationPriceData = res.data.records;
                            this.batchAddTransportationPriceTotal = res.data.total;
                        } else {
                            ElMessage.error(res.msg);
                        }
                    })
                    .finally(() => {
                        this.batchAddTransportationPriceLoading = false;
                    })
                    .catch(() => {});
				const { priceBookFormulaId, ...params } = this.batchAddTransportationPriceForm;
                priceManagement
                    .getFormulaList({ ...params })
                    .then((res) => {
                        if (res.code === 200) {
                            this.priceBookFormulaIdList = res.data || [];
                        } else {
                            ElMessage.error(res.msg);
                        }
                    })
                    .catch(() => {});
                if (noSeachType !== 'priceBookFormulaId') {
                    this.batchAddTransportationPriceForm.priceBookFormulaId = undefined;
                }
            } else if (type === 'see') {
                this.viewPriceBookLoading = true;
                this.viewPriceBookLoadingText = '加载中...';
                priceManagement
                    .priceDataList(this.viewPriceBookForm)
                    .then((res) => {
                        if (res.code === 200 && res.data) {
                            this.networkTransportationPriceData = res.data.records;
                            this.networkTransportationPriceTotal = res.data.total;
                        } else {
                            ElMessage.error(res.msg);
                        }
                    })
                    .finally(() => {
                        this.viewPriceBookLoading = false;
                    })
                    .catch(() => {});
            }
        },
        productTypeFormat(row) {
            return this.selectDictLabel(this.valuationTypeList, row.transType);
        },
        resetBatchAddTransportationPriceForm(form) {
            this.$refs[form].resetFields();
            this.priceDataList('batch');
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleQuery();
        },
        resetViewPriceBookForm(form) {
            this.$refs[form].resetFields();
            this.priceDataList('see');
        },
        // 计价类型 变化
        setPriceTypes() {
            // 重置车辆类型 carTypes
            this.addPriceDetailsForm.carTypes = [];
            // 清除公式选择
            this.addPriceDetailsForm.name = '';
        },
        // 重置价格本名称 清空customerId
        setToResetThePriceOfThisName(val) {
            // this.newPriceForm.companyId 设置为 null 并更新视图
            this.newPriceForm.companyId = null;
            if (val === '2') {
                this.getAListOfCustomersAndCarriers();
            }
            // todo 承运商暂时不做
            // if (val === '4') {
            //   this.getAListOfCustomersAndCarriers('1');
            // }
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        // 生成价格数据
        submitAddPrice() {
            this.$refs.addPriceDetailsForm.validate((valid) => {
                if (valid) {
                    this.addPriceDetailsLoading = true;
                    this.addPriceDetailsLoadingText = '提交中...';
                    if (this.addPriceDetailsForm.transType !== '2') {
                        this.addPriceDetailsForm.carTypes = [];
                    }
                    if (this.addPriceDetailsForm.costType !== '2') {
                        this.addPriceDetailsForm.targetNetworks = [];
                    }
                    this.addPriceDetailsForm.priceBookId = this.addPriceDetailsForm.id;
                    priceManagement
                        .generatePriceData(this.addPriceDetailsForm)
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('生成成功');
                                this.hideAddPriceDetails();
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.addPriceDetailsLoading = false;
                        })
                        .catch(() => {});
                }
            });
        },
        submitAndAddPriceBook() {
            this.$refs.singleAddForm.validate((valid) => {
                if (valid) {
                    this.singleAddLoading = true;
                    this.singleAddLoadingText = '添加中...';
                    if (this.singleAddForm.transType !== '2') {
                        this.singleAddForm.carType = '';
                    }

                    priceManagement
                        .addPriceData(this.singleAddForm)
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('添加成功');
                                this.hideSingleAdd();
                                this.priceDataList('see');
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.singleAddLoading = false;
                        })
                        .catch(() => {});
                }
            });
        },
        // 提交批量添加设置
        submitBatchAdd() {
            this.$refs.batchAddSettingsForm.validate((valid) => {
                if (valid) {
                    this.batchAddSettingsLoading = true;
                    this.batchAddSettingsLoadingText = '提交中...';
                    if (this.batchAddSettingsForm.transType !== '2') {
                        this.batchAddSettingsForm.carTypes = [];
                    }
                    priceManagement
                        .batchAddPriceData(this.batchAddSettingsForm)
                        .then((res) => {
                            if (res.code === 200) {
                                this.batchAddSettingsVisible = false;
                                this.priceDataList('see');
                                this.hideBatchAddSettings();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.batchAddSettingsLoading = false;
                        })
                        .catch(() => {});
                }
            });
        },
        // 批量修改价格数据标准费用
        submitBatchModifyPrice() {
            this.batchAddTransportationPriceLoading = true;
            this.batchAddTransportationPriceLoadingText = '提交中...';
            // batchAddTransportationPriceData 提取 id 和 standardCost
            const data = this.batchAddTransportationPriceData.map((item) => {
                return {
                    id: item.id,
                    standardCost: item.standardCost,
                    perOrderCost: item.perOrderCost,
                    weightCost: item.weightCost,
                    volumeCost: item.volumeCost,
                    distanceCost: item.distanceCost,
                    timeNeeded: item.timeNeeded,
                    priceBookFormulaId: item.priceBookFormulaId,
                    name: item.name
                };
            });
            // data 数组 去除 undefined
            const newData = data.filter((item) => item);
            priceManagement
                .batchUpdatePriceData(newData)
                .then((res) => {
                    if (res.code === 200) {
                        ElMessage.success('修改成功');
                        this.batchAddTransportationPriceVisible = false;
                        this.getList();
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .finally(() => {
                    this.batchAddTransportationPriceLoading = false;
                })
                .catch(() => {});
        },
        // 单个修改
        submitSingleModification() {
            this.$refs.singleAddForm.validate((valid) => {
                if (valid) {
                    // 数组 包含 id 和 standardCost
                    const data = [
                        {
                            id: this.singleAddForm.id,
                            priceBookFormulaId: this.singleAddForm.priceBookFormulaId,
                            name: this.singleAddForm.name,
                            standardCost: this.singleAddForm.standardCost,
                            perOrderCost: this.singleAddForm.perOrderCost,
                            weightCost: this.singleAddForm.weightCost,
                            volumeCost: this.singleAddForm.volumeCost,
                            distanceCost: this.singleAddForm.distanceCost,
                            timeNeeded: this.singleAddForm.timeNeeded,
                            remark: this.singleAddForm.remark
                        }
                    ];
                    priceManagement
                        .batchUpdatePriceData(data)
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('修改成功');
                                this.hideSingleAdd();
                                this.priceDataList('see');
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.singleAddLoading = false;
                        })
                        .catch(() => {});
                }
            });
        },
        // 货主价格本生成价格本
        submitTheNewPriceBook() {
            this.$refs.newPriceForm.validate((valid) => {
                if (valid) {
                    this.newPriceLoading = true;
                    this.newPriceLoadingText = '提交中...';
                    priceManagement
                        .priceBookGenerate(this.newPriceForm)
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('新建成功');
                                this.hideNewPrice();
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.newPriceLoading = false;
                        })
                        .catch(() => {});
                }
            });
        },
        treeChange(node, instanceId) {
            this.$set(this.addPriceDetailsForm, 'typeLevel', parseInt(node.typeLevel) + 1);
        },
        // 撤销批量更新
        undoModifications() {
            ElMessageBox.confirm(`是否撤销批量修改？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.undoBulkModificationOfLoading = true;
                    const { priceBookId } = this.editOrViewStatus === 'batch' ? this.batchAddTransportationPriceForm : this.viewPriceBookForm;
                    priceManagement
                        .revokePrice({ id: priceBookId })
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('撤销成功！');
                                this.undoBulkModificationOfVisible = false;
                                if (this.editOrViewStatus === 'batch') {
                                    this.priceDataList('batch');
                                } else {
                                    this.priceDataList('see');
                                }
                                // 是否展示撤销按钮
                                this.getIsRevoke(priceBookId);
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.undoBulkModificationOfLoading = false;
                        })
                        .catch(() => {});
                })
                .catch(() => {});
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
    .el-radio__input.is-disabled + span.el-radio__label {
        color: #666666;
    }

    .el-input.is-disabled .el-input__inner {
        color: #666666;
    }
    .el-dialog__header {
        padding-bottom: 20px;
    }
    .calculationFormula .el-dialog__body {
        padding: 0;
    }
    .el-alert .el-alert__description,
    .el-alert .el-alert__title {
        margin: 5px 0;
    }
    .el-alert__icon.is-big {
        font-size: 20px;
    }
}

.dialog__publishNotification,
.dialog__batchModifyPriceForm,
.dialog__undoBulkModificationOf,
.dialog__batchModifyFormulaOf {
    ::v-deep {
        .el-dialog__header {
            padding-bottom: 20px;
        }

        .el-result {
            padding: 0;
        }
        .el-dialog__footer {
            padding: 10px 20px;
        }
    }
}
.newPriceForm {
    ::v-deep {
        .el-radio__input.is-disabled + span.el-radio__label {
            color: #666666;
        }
    }
}
.numeric_units {
    margin-left: 10px;
    color: #999;
}
.only-read {
    pointer-events: none;
}
.avatar__uploader__vertical__display {
    ::v-deep {
        .el-upload {
            display: none;
        }
        .el-upload-list__item:first-child {
            margin-top: 0;
            line-height: 30px;
            margin-bottom: 0;
        }
        .el-upload-list {
            margin: 0;
        }
    }
}
.box-period {
    height: 140px;

    ::v-deep {
        .el-scrollbar {
            height: 100%;
        }

        .el-scrollbar__wrap {
            //overflow: scroll;
            width: 102%;
        }
    }
}
.box__multiple {
    position: absolute;
    z-index: 1;
    top: 0;
    display: flex;
    padding: 10px 10px 10px 10px;
    width: 100%;
    background: #ffffff;
    box-shadow: 0 0 0 #c1c1c1, 0 0 6px #ebebeb;
}
.select__buttons {
    .el-select-dropdown__list {
        padding-top: 0 !important;
        .el-select-dropdown__item:nth-of-type(1) {
            margin-top: 55px;
        }
    }
}
::v-deep {
    .vue-treeselect__value-container {
        display: flex;
    }
    .vue-treeselect__placeholder {
        font-size: 13px;
        color: #c0c4cc;
        line-height: 31px;
    }
    .vue-treeselect__multi-value-item-container {
        line-height: 24px;
        height: 24px;
        vertical-align: text-top;
        padding-top: 0;
    }
    .vue-treeselect__multi-value-item {
        padding: 0;
        background-color: #f7f7f7;
        border-color: #efefef;
        color: #b1b1b1;
    }
    .vue-treeselect__input-container {
        height: 24px;
    }
    .el-drawer__header {
        margin-bottom: 20px;
    }
    .vue-treeselect__control {
        height: 32px;
        line-height: 32px;
    }
    .vue-treeselect__value-remove {
        color: #909399;
        margin-right: 5px;
        border: none;
        background-color: #c0c4cc;
        border-radius: 50%;
        text-align: center;
        position: relative;
        cursor: pointer;
        height: 15px;
        width: 16px;
        line-height: 12px;
        display: inline-block;
        transform: scale(0.8);
        top: -2px;
    }
    .vue-treeselect__single-value {
        font-size: 13px;
        color: #666666;
        line-height: 32px;
    }
    .vue-treeselect__multi-value-item:hover .vue-treeselect__value-remove {
        color: #fff;
    }
    .treeselect__wfc .vue-treeselect__control {
        width: fit-content;
    }
}
</style>
