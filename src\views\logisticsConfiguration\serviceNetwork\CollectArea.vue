<template>
    <div class="app-container">
        <!--搜索-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form" @submit.native.prevent>
                <el-form-item label="揽收区域名称" prop="areaName">
                    <el-input v-model="queryParams.areaName" clearable placeholder="请输入揽收区域名称" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="所属网点" prop="branchCode">
                    <el-select v-model="queryParams.branchCode" clearable filterable placeholder="请选择所属网点" @change="handleQuery">
                        <el-option v-for="(item, idx) in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode" />
                    </el-select>
                </el-form-item>
                <!--          <el-form-item label="是否存在揽收司机" prop="isDriver">-->
                <!--            <el-select v-model="queryParams.isDriver" clearable placeholder="请选择是否存在揽收司机" @change="handleQuery">-->
                <!--              <el-option label="是" value="1" />-->
                <!--              <el-option label="否" value="2" />-->
                <!--            </el-select>-->
                <!--          </el-form-item>-->
                <el-form-item label="揽收区域状态" prop="status">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择揽收区域状态" @change="handleQuery">
                        <el-option v-for="(item, idx) in statusOptions" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card shadow="never" :body-style="{ padding: '10px' }">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <div>
                    <el-button v-hasPermi="['collect:lanArea:add']" icon="el-icon-plus" type="primary" @click="handleAdd">新增</el-button>
                    <el-button v-hasPermi="['collect:lanArea:delete']" :disabled="multiple" icon="el-icon-delete" type="danger" @click="handleDelete">批量删除 </el-button>
                </div>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="CollectArea" @queryTable="getList"></right-toolbar>
            </div>
            <column-table :max-height="600" key="CollectArea" v-loading="loading" :columns="columns" :data="areaList" :show-check-box="true" element-loading-text="加载中..." @selection-change="handleSelectionChange">
                <template #driverNum="scope">
                    <el-tag v-if="scope.row.driverNum > 0" type="success">是</el-tag>
                    <el-tag v-else type="danger">否</el-tag>
                </template>
                <template #branchName="{ row }">
                    <span>{{ row?.branch?.branchName }}</span>
                </template>
                <template #status="{ row }">
                    <span>{{ statusFormat(row) }}</span>
                </template>
                <template #opt="scope">
                    <el-button v-hasPermi="['collect:lanArea:edit']" icon="el-icon-magic-stick" link size="small" type="primary" @click="relationDriver(scope.row)">关联司机</el-button>
                    <el-button v-hasPermi="['collect:lanArea:edit']" icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button v-hasPermi="['collect:lanArea:delete']" icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- 添加或修改揽收区域对话框 -->
        <el-dialog v-model="open" v-dialogDrag :title="title" append-to-body width="650px">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="所属网点" prop="branchCode">
                    <el-select v-model="form.branchCode" clearable filterable placeholder="请选择所属网点" size="small" style="width: 100%">
                        <el-option v-for="(item, idx) in branchList" :key="item.branchCode" :label="item.branchName" :value="item.branchCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="揽收区域名称" prop="areaName">
                    <el-input v-model="form.areaName" maxlength="20" placeholder="请输入揽收区域名称" show-word-limit />
                </el-form-item>
                <el-form-item label="揽收区域范围" prop="areaList">
                    <el-cascader v-model="form.areaList" :options="areaOptions" :props="props" clearable filterable placeholder="请选择揽收区域范围" style="width: 100%" />
                </el-form-item>
                <el-form-item label="揽收区域状态">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.name }} </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="60" placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</template>
        </el-dialog>
        <driverSelect v-if="driverOpen" v-model:show="driverOpen" :driverData="driverData" :type="'flow_driver'" :values="values" @changeShow="changeShow" @onConfirm="binding"></driverSelect>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import driverSelect from '@/components/driverSelect';
import serviceNetwork from '@/api/logisticsConfiguration/serviceNetwork.js';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
export default {
    name: 'CollectArea',
    components: { SearchButton, driverSelect, ColumnTable, RightToolbar },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 揽收区域表格数据
            areaList: [],
            columns: [
                { title: '揽收区域名称', key: 'areaName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '区域编码', key: 'areaCode', align: 'center', minWidth: '120px', columnShow: true },
                { title: '所属网点', key: 'branchName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '是否存在揽收司机', key: 'driverNum', align: 'center', minWidth: '150px', columnShow: true },
                { title: '揽收区域状态', key: 'status', align: 'center', minWidth: '120px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '220px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                serviceCode: null,
                areaCode: null,
                areaName: null,
                provinceId: null,
                cityId: null,
                countyId: null,
                status: null,
                isDriver: null
            },
            // 表单参数
            form: {
                branchCode: ''
            },
            // 表单校验
            rules: {
                branchCode: [{ required: true, message: '请选择所属网点', trigger: 'blur' }],
                areaName: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],
                areaList: [{ required: true, type: 'array', message: '请选择区域范围', trigger: 'blur' }]
            },
            // 状态
            statusOptions: [],
            areaOptions: [],
            branchList: [],
            props: { multiple: true },
            driverOpen: false,
            values: {},
            driverData: []
        };
    },
    async created() {
        this.getList();
        /** 状态 */
        this.statusOptions = await this.getDictList('sys_normal_disable');
        this.getBranchAll();
    },
    methods: {
        /** 状态字典转换 */
        statusFormat(val) {
            return this.selectDictLabel(this.statusOptions, val.status);
        },
        // 绑定司机
        binding(data) {
            let params = [];
            params = data.driverList.map((item) => {
                return { driverCode: item.driverCode, areaCode: data.data.areaCode, branchCode: data.data.branchCode };
            });
            operationConfiguration.bindLanDeiver(params).then((response) => {
                if (response.code == 200) {
                    this.msgSuccess('关联司机成功');
                    this.driverOpen = false;
                    this.getList();
                }
            });
        },
        changeShow(value) {
            this.values.open = value;
            this.driverOpen = value;
        },
        // 显示关联司机
        relationDriver(row) {
            this.driverData = [];
            operationConfiguration.getLanDriverList({ branchCode: row.branch.branchCode, areaCode: row.areaCode }).then((response) => {
                if (response.code == 200 && response?.data?.records.length > 0) {
                    this.driverData = response.data.records.map((item) => {
                        return item.driver;
                    });
                }
                this.values = {
                    minNum: 1, // 最少绑定一个
                    title: '关联司机',
                    open: true,
                    data: { areaCode: row.areaCode, branchCode: row.branch.branchCode },
                    type: 1
                };
                this.driverOpen = true;
            });
        },
        // 获取所有承运商所有服务网点
        getBranchAll() {
            serviceNetwork.getBranchList().then((response) => {
                this.branchList = response.data;
            });
        },
        /** 查询揽收区域列表 */
        getList() {
            this.loading = true;
            this.areaList = [];
            let params = { ...this.queryParams };
            params.areaClass = '1';
            serviceNetwork
                .listArea(params)
                .then((response) => {
                    if (response.code === 200 && response.data) {
                        this.areaList = response.data.records || [];
                        this.total = response.data.total || 0;
                    }
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                areaName: null,
                wideList: [],
                areaList: [],
                status: '0',
                remark: null
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        resetForm(formName) {
            this.$refs[formName] ? this.$refs[formName].resetFields() : '';
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.names = selection.map((item) => item.areaname);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.areaOptions = this.getSysAreasThird;
            this.reset();
            this.open = true;
            this.title = '添加揽收区域';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.areaOptions = this.getSysAreasThird;
            this.reset();
            const id = row.id || this.ids;
            this.form.areaList = [];
            serviceNetwork.queryAreaById({ id }).then((response) => {
                this.form = response.data;
                this.form.branchCode = this.form?.branch?.branchCode;
                if (this.form?.wideList) {
                    this.form.areaList = this.form.wideList.map((item) => {
                        return [item.provinceId, item.cityId, item.countyId];
                    });
                }
                this.open = true;
                this.title = '修改揽收区域';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                this.form.wideList = [];
                this.form.areaList.forEach((a) => {
                    this.form.wideList.push({ provinceId: a[0], cityId: a[1], countyId: a[2] });
                });
                if (valid) {
                    if (this.form.id != null) {
                        serviceNetwork.saveArea(this.form).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        serviceNetwork.saveArea({ ...this.form, areaClass: '1' }).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids.join(',');
            const names = row.areaName || this.names;
            this.$confirm('是否确认删除揽收区域"' + names + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return serviceNetwork.delArea({ ids: ids });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        }
    }
};
</script>
<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
</style>
