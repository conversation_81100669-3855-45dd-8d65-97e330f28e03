<template>
    <el-dialog v-model="visible" append-to-body destroy-on-close title="操作记录" width="800px" @close="closeDialog">
        <el-table :data="recordList" style="width: 100%">
            <el-table-column align="center" label="时间" prop="createDate" width="180" />
            <el-table-column align="center" label="操作人" prop="auditUserName" show-overflow-tooltip width="180" />
            <el-table-column align="center" label="审批结果" prop="status" width="100">
                <template #default="{ row }">
                    <el-tag :type="row.status === '1' ? 'success' : 'danger'">
                        {{ row.status === '1' ? '通过' : '驳回' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column align="left" label="审批意见" min-width="200" prop="auditIdea" show-overflow-tooltip />
        </el-table>
    </el-dialog>
</template>

<script>
export default {
    name: 'ApprovalRecord',
    model: {
        prop: 'approvalVisible',
        event: 'update:approvalVisible'
    },
    props: {
        approvalVisible: {
            type: <PERSON>olean,
            default: false
        },
        recordList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            visible: this.approvalVisible
        };
    },
    watch: {
        approvalVisible: {
            immediate: true,
            handler(newVal) {
                this.visible = newVal;
            }
        }
    },
    methods: {
        closeDialog() {
            this.$emit('close');
        }
    },
    emits: ['update:approvalVisible', 'close']
};
</script>

<style scoped></style>
