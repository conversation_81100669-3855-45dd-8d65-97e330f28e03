import request from '@/utils/request';

export default {
    /** s 合作货主 */
    // 列表
    listOwner: function (params) {
        return request.get('/tms/cooperate/company/list', params);
    },
    // 已合作货主下拉框
    cooperateSelect: function (params) {
        return request.get('/tms/cooperate/company/cooperateSelect', params);
    },
    // 查询所有货主下拉框数据
    orgSelect: function (params) {
        return request.get('/sys/org/orgSelect', params);
    },
    // 保存合作货主
    saveOwner: function (data) {
        return request.post('/tms/cooperate/company/save', data);
    },
    // 详情
    queryOwnerById: function (params) {
        return request.get('/tms/cooperate/company/queryById', params);
    },
    // 修改状态
    updateStatus: function (params) {
        return request.get('/tms/cooperate/company/updateStatus', params);
    },
    // 修改货主权限状态
    updateAuthStatus: function (params) {
        return request.get('/tms/cooperate/company/updateAuthStatus', params);
    },
    // 保存修改信息
    saveOwnerChange: function (data) {
        return request.post('/tms/cooperate/companyChange/save', data);
    },
    // 查询签订预付款合同的下拉框
    advanceCompanySelect: function (params) {
        return request.get('/tms/cooperate/company/advanceCompanySelect', params);
    },
    // 查询货主合作信息
    getAdvanceByCompany: function (params) {
        return request.get('/tms/cooperate/company/company', params);
    },
    // 查询当前货主合作信息
    getOwnerByCompany: function (params) {
        return request.get('/tms/cooperate/company/cooperateInfo', params);
    },
    // 收藏/取消收藏
    updateIsCollect: function (params) {
        return request.get('/tms/cooperate/company/updateIsCollect', params);
    },
    // 清除收藏
    cancelCollect: function (params) {
        return request.get('/tms/cooperate/company/cancelCollect', params);
    },
    //查询合作关系修改记录
    queryChangeLog: function (params) {
        return request.get('/tms/cooperate/companyChange/queryByCooperateId', params);
    }

    /** e 合作货主 */
};
