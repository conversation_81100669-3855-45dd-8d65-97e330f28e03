<template>
    <div class="top-right-btn">
        <el-row>
            <el-tooltip :content="showSearch ? '隐藏搜索' : '显示搜索'" class="item" effect="dark" placement="top">
                <el-button v-if="search" circle icon="el-icon-search" @click="toggleSearch()" />
            </el-tooltip>
            <el-tooltip v-if="refreshShow" class="item" content="刷新" effect="dark" placement="top">
                <el-button circle icon="el-icon-refresh" :loading="loading" @click="refresh()" />
            </el-tooltip>
            <el-tooltip class="item" content="显隐列" effect="dark" placement="top">
                <el-button circle icon="el-icon-menu" @click="showColumn()" />
            </el-tooltip>
            <el-tooltip v-if="showColumns" class="item" content="配置显示" effect="dark" placement="top">
                <el-button circle icon="el-icon-setting" @click="serverShowColum()" />
            </el-tooltip>
        </el-row>
        <el-dialog v-model="open" append-to-body class="the_table_header_displays" close-on-click-modal title="表头显示设置" @close="closeDialog">
            <div class="dialog__grid">
                <div class="item__left">
                    <div class="item__left__title">
                        <span>可选择字段</span>
                        <span class="item__left__title__num">（{{ columnsInitial.length }}个）</span>
                    </div>
                    <el-checkbox-group v-model="columnsChecked" @change="handleCheckChange">
                        <el-checkbox v-for="item in columnsInitial" :key="item.key" :checked="item.columnShow" :label="item.key">{{ item.title }}</el-checkbox>
                    </el-checkbox-group>
                </div>
                <div class="item__right">
                    <div class="item__right__title">
                        <span>已展示字段</span>
                        <span class="item__right__title__num">（{{ columnsChecked.length }}个）</span>
                    </div>
                    <div class="box-period">
                        <el-scrollbar>
                            <transition-group class="list" name="drag" tag="ul">
                                <li v-for="(item, index) in columnsChange" :key="item.key" class="item__right__item" draggable="true" @dragenter="dragenter($event, index)" @dragover="dragover($event, index)" @dragstart="dragstart(index)">
                                    <el-icon><el-icon-d-caret /></el-icon>
                                    <span>{{ item.title }}</span>
                                </li>
                            </transition-group>
                        </el-scrollbar>
                    </div>
                </div>
            </div>
            <template #footer class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="warning" @click="treeChangeReset">重置</el-button>
                <el-button type="primary" @click="treeChangeConfirm">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: 'RightToolbar',
    props: {
        columns: {
            default: () => [],
            type: Array
        },
        refreshShow: {
            type: Boolean,
            default: true
        },
        search: {
            type: Boolean,
            default: true
        },
        showColumns: {
            type: Boolean,
            default: false
        },
        showSearch: {
            type: Boolean,
            default: true
        },
        tableID: {
            type: String,
            default: 'dataTable'
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 初始数据
            columnsInitial: [],
            // 选中数据
            columnsChecked: [],
            // 展示数据
            columnsChange: [],
            // 弹出层标题
            title: '显示/隐藏/列顺序',
            // 是否显示弹出层
            open: false,
            treeShowData: [],
            dragIndex: '',
            enterIndex: ''
        };
    },
    created() {
        // 判断是否有缓存 this.tableID + 'Initial'
        let Initial = localStorage.getItem(this.tableID + 'Initial');
        if (Initial) {
            this.columnsInitial = JSON.parse(Initial);
        } else {
            localStorage.setItem(this.tableID + 'Initial', JSON.stringify(this.columns));
        }

        let data = localStorage.getItem(this.tableID + 'Change');
        // 如果存在 data
        if (data) {
            this.$emit('update:columns', JSON.parse(data));
        } else {
            this.$emit('update:columns', this.columns);
        }
    },
    methods: {
        closeDialog() {
            this.open = false;
            let data = localStorage.getItem(this.tableID + 'Change');
            if (data) {
                // this.columnsChecked 等于 JSON.parse(data) 中的 key
                this.columnsChecked = JSON.parse(data).map((item) => item.key);
            } else {
                this.columnsChecked = this.columns.map((item) => item.key);
            }
        },
        dragenter(e, index) {
            e.preventDefault();
            // 避免源对象触发自身的dragenter事件
            if (this.dragIndex !== index) {
                const moving = this.columnsChange[this.dragIndex];
                this.columnsChange.splice(this.dragIndex, 1);
                this.columnsChange.splice(index, 0, moving);
                // 排序变化后目标对象的索引变成源对象的索引
                this.dragIndex = index;
            }
        },
        dragover(e, index) {
            e.preventDefault();
        },
        dragstart(index) {
            this.dragIndex = index;
        },
        /**
         * 修改可选择字段
         * @param val
         */
        handleCheckChange(val) {
            // 从 this.columnsInitial 中筛选出 key 为 val 中的元素 赋值给 this.columnsChange 并设置 columnShow 为 true
            this.columnsChange = this.columnsInitial.filter((item) => val.includes(item.key));
            this.columnsChange.forEach((item) => {
                item.columnShow = true;
            });
        },
        // 刷新
        refresh() {
            this.$emit('queryTable');
        },
        serverShowColum() {
            this.$emit('showColumnOption');
        },
        // 打开显隐列dialog
        showColumn() {
            this.open = true;
            let Initial = localStorage.getItem(this.tableID + 'Initial');
            if (Initial) {
                this.columnsInitial = JSON.parse(Initial);
            } else {
                localStorage.setItem(this.tableID + 'Initial', JSON.stringify(this.columns));
            }
            let data = localStorage.getItem(this.tableID + 'Change');
            if (data) {
                const dataArr = JSON.parse(data);
                this.columnsChange = dataArr;
                // 设置 this.columnsInitial 中 columnShow 为 false
                // 设置 this.columnsInitial 中 key 等于 this.columnsChange 中 key 的 columnShow 为 true
                this.columnsInitial.forEach((item) => {
                    item.columnShow = false;
                    this.columnsChange.forEach((c) => {
                        if (c.key == item.key) {
                            item.columnShow = true;
                        }
                    });
                });
            } else {
                this.columnsChange = JSON.parse(Initial);
                this.columnsInitial.forEach((item) => {
                    item.columnShow = this.columns.some((c) => c.key == item.key);
                });
            }
        },
        // 搜索
        toggleSearch() {
            this.$emit('update:showSearch', !this.showSearch);
        },
        /**
         * 确认修改
         */
        treeChangeConfirm() {
            this.$emit('update:columns', this.columnsChange);
            localStorage.setItem(this.tableID + 'Change', JSON.stringify(this.columnsChange));
            // 判断this.$parent.$parent.setFilters方法是否存在，存在就执行
            this.$parent.$parent.setFilters ? this.$parent.$parent.setFilters() : this.$parent.setFilters ? this.$parent.setFilters() : '';
            this.open = false;
        },
        /**
         * 重置
         */
        treeChangeReset() {
            let data = localStorage.getItem(this.tableID + 'Initial');
            this.$emit('update:columns', JSON.parse(data));
            localStorage.removeItem(this.tableID + 'Change');
            this.columnsChecked = JSON.parse(data).map((item) => item.key);
            this.open = false;
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep {
    .el-dialog__header {
        padding-bottom: 20px;
    }
    .el-dialog__footer {
        padding: 15px 20px;
    }
}
.the_table_header_displays {
    ::v-deep .el-dialog__body {
        padding-top: 20px;
        padding-bottom: 20px;
    }
}
.dialog__footer {
    display: flex;
    justify-content: center;
}
.dialog__grid {
    display: grid;
    grid-template-columns: 2.2fr 1fr;
    grid-gap: 15px;
    .item__left {
        .item__left__title {
            display: flex;
            align-items: center;
            font-size: 14px;
            margin-bottom: 10px;
            .item__left__title__num {
                color: #999;
                font-size: 12px;
            }
        }
    }
    .item__right {
        border-left: 2px solid #e4e7ed;
        padding-left: 15px;
        .item__right__title {
            display: flex;
            align-items: center;
            font-size: 14px;
            margin-bottom: 10px;
            .item__right__title__num {
                color: #999;
                font-size: 12px;
            }
        }
        .item__right__item {
            padding: 5px;
            background: #f5f7fa;
            margin-bottom: 5px;
            cursor: move;
            .item__right__item__label {
                margin-left: 5px;
            }
        }
    }
}
.el-checkbox-group {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
}
.box-period {
    height: 250px;
    ::v-deep {
        .el-scrollbar {
            height: 100%;
        }
        .el-scrollbar__wrap {
            //overflow: scroll;
            width: 110%;
        }
    }
}
.list {
    list-style: none;
    padding: 0;
    margin: 0;
    .drag-move {
        transition: transform 0.3s;
    }
    .list-item {
        cursor: move;
        width: 300px;
        background: #ea6e59;
        border-radius: 4px;
        color: #fff;
        margin-bottom: 6px;
        height: 50px;
        line-height: 50px;
        text-align: center;
    }
}
.top-right-btn {
    position: relative;
    float: right;
}
</style>
