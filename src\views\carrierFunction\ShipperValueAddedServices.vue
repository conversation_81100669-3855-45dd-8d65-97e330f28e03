<template>
    <div class="app-container">
        <!--  搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" @submit.prevent>
                <el-form-item label="货主公司" prop="ownerId">
                    <el-select v-model="queryParams.ownerId" clearable filterable placeholder="请选择货主公司" @change="handleShipperChange">
                        <el-option v-for="item in shipperList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="增值服务" prop="addedServiceConfigId" style="width: 230px">
                    <el-select v-model="queryParams.addedServiceConfigId" clearable filterable placeholder="请选择增值服务" @change="handleQuery">
                        <el-option v-for="item in addServiceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="温区类型" prop="tempType" style="width: 230px">
                    <el-select v-model="queryParams.tempType" clearable filterable placeholder="请选择温区类型" @change="handleQuery">
                        <el-option v-for="item in tempTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="发布状态" prop="publishStatus" v-show="isShowAll">
                    <el-select v-model="queryParams.publishStatus" clearable filterable placeholder="请选择发布状态" @change="handleQuery">
                        <el-option v-for="item in publishTheStatusOfList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="编号" prop="no" v-show="isShowAll">
                    <el-input v-model="queryParams.no" clearable placeholder="请输入编号"></el-input>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery('queryForm')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!-- 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 41px">
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="shipperValueAddedServiceTable" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="orderList" :showIndex="true" :max-height="600">
                <template #tempType="{ row }">
                    {{ selectDictLabel(tempTypeList, row.tempType) }}
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ selectDictLabel(statusList, row.status) }}</span>
                </template>
                <template #publishStatus="{ row }">
                    <span :style="setPublishStatusColor(row.publishStatus)">{{ selectDictLabel(publishTheStatusOfList, row.publishStatus) }}</span>
                </template>
                <template #formulaDesc="{ row }">
                    <el-popover placement="top" width="400" v-if="row.formulaDesc">
                        <pre>{{ row.formulaDesc }}</pre>
                        <template #reference>
                            <el-link icon="el-icon-info-filled" link size="small" type="primary">查看</el-link>
                        </template>
                    </el-popover>
                </template>
                <template #defaultFormula="{ row }">
                    <span>{{ formatDefaultFormula(row) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-close" link size="small" type="danger" @click="handleClickBtnWriteOffFormula(row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import shipperValueAddedServices from '@/api/carrierEnd/shipperValueAddedServices';
import handoverOrderConfiguration from '@/api/carrierEnd/handoverOrderConfiguration';
import valueAddedServices from '@/api/carrierEnd/valueAddedServices';
import { selectDictLabel } from '@/utils/dictLabel';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'ShipperValueAddedServices',
    components: {
        RightToolbar,
        ColumnTable,
        SearchButton
    },
    data() {
        return {
            showSearch: true,
            queryParams: {
                current: 1,
                size: 10,
                addedServiceConfigId: undefined,
                ownerId: undefined,
                tempType: undefined,
                publishStatus: '1',
                no: undefined
            },
            columns: [
                { title: '编号', key: 'no', align: 'center', width: '160px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '货主公司', key: 'ownerName', align: 'center', width: '200px', columnShow: true, fixed: 'left' },
                { title: '服务名称', key: 'name', align: 'center', width: '200px', columnShow: true },
                { title: '温区类型', key: 'tempType', align: 'center', columnShow: true },
                { title: '单项价格', key: 'price', align: 'center', columnShow: true },
                { title: '最低费用', key: 'minCost', align: 'center', columnShow: true },
                { title: '公式状态', key: 'status', align: 'center', columnShow: true },
                { title: '默认公式', key: 'defaultFormula', minWidth: '280px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '公式说明', key: 'formulaDesc', minWidth: '140px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发布状态', key: 'publishStatus', minWidth: '120px', align: 'center', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            loading: false,
            orderList: [],
            total: 0,
            addServiceList: [], // 增值服务列表
            shipperList: [], // 货主公司列表
            tempTypeList: [], // 温区类型列表
            statusList: [], // 货主增值服务公式状态
            publishTheStatusOfList: [], // 发布状态
            isShowAll: false,
        };
    },
    created() {
        this.getDict();
        // 获取路由参数中的增值服务ID
        const { addedServiceConfigId } = this.$route.query;
        if (addedServiceConfigId) {
            this.queryParams.addedServiceConfigId = addedServiceConfigId;
        }
        this.handleQuery();
        this.getShipperList();
        this.getValueAddedServiceList();
    },
    computed: {
        // 设置状态颜色
        setStatusColor() {
            return (status) => {
                return {
                    '0': { color: '#67C23A' }, // 绿色
                    '1': { color: '#F56C6C' } // 红色
                }[status];
            };
        },
        // 设置发布状态颜色
        setPublishStatusColor() {
            return (status) => {
                return {
                    '0': { color: '#F4AC00' }, // 黄色
                    '1': { color: '#67C23A' }, // 绿色
                    '2': { color: '#B1B1B1' } // 灰色
                }[status];
            };
        },
        // 格式化默认公式
        formatDefaultFormula() {
            return (data) => {
                const { addedServiceConfigId, defaultFormula } = data;
                let parameter = '';
                if (addedServiceConfigId && this.addServiceList.length > 0) {
                    // addServiceList 中 取出 addedServiceConfigId 为 e 的对象的 parameter 如果不存在 parameter 对象 则提示
                    parameter = this.addServiceList.find((item) => item.id == addedServiceConfigId)?.parameter;
                }
                if (defaultFormula) {
                    const defaultFormulaList = [];
                    const defaultFormulaArr = defaultFormula.match(/([0-9]+(\.[0-9]+)?|\+|-|\*|\/|\(|\)|&|\||=|!|>|<)|([a-zA-Z]+)/g);
                    if (defaultFormulaArr) {
                        defaultFormulaArr.forEach((item) => {
                            if (item) {
                                if (item.match(/(\d+\.?\d*)/)) {
                                    defaultFormulaList.push({ type: '', value: item, text: item });
                                } else {
                                    if (item === 'price') {
                                        defaultFormulaList.push({ type: 'text', value: item, text: '单项价格' });
                                    } else if (item === 'self') {
                                        defaultFormulaList.push({ type: 'text', value: item, text: parameter || '' });
                                    } else {
                                        defaultFormulaList.push({ type: 'text', value: item, text: item });
                                    }
                                }
                            }
                        });
                    }
                    // defaultFormulaList text 拼接为字符串
                    return defaultFormulaList.map((item) => item.text).join('');
                }
            };
        }
    },
    methods: {
        // 展开折叠
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        // 删除公式
        handleClickBtnWriteOffFormula(row) {
            this.$confirm('确认删除该货主的增值服务？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    // 显示加载中
                    const loading = this.$loading({
                        lock: true,
                        text: '删除中...',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });

                    shipperValueAddedServices
                        .logoutShipperValueAddedService(row.id)
                        .then((res) => {
                            // 关闭加载中
                            loading.close();

                            if (res.code === 200) {
                                // 根据data字段判断是否删除成功
                                if (res.data === true) {
                                    this.$message.success('删除成功');
                                    this.handleQuery();
                                } else {
                                    // 删除失败，显示确认对话框
                                    this.$confirm('还有订单未完结，不能删除该货主的增值服务！', '提示', {
                                        confirmButtonText: '确定',
                                        type: 'error'
                                    });
                                }
                            }
                        })
                        .catch((error) => {
                            // 关闭加载中
                            loading.close();
                            this.$message.error('删除失败');
                        });
                })
                .catch(() => {
                    // 取消操作，不做任何处理
                });
        },
        // 获取增值服务列表
        getValueAddedServiceList() {
            valueAddedServices
                .addedServiceAllList()
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.addServiceList = res.data || [];
                    }
                })
                .catch(() => {});
        },
        /**
         * 获取字典值
         * @returns {Promise<void>}
         */
        async getDict() {
            // 货主增值服务公式状态
            this.statusList = await this.getDictList('fourpl_added_service_status');
            // 发布状态
            this.publishTheStatusOfList = await this.getDictList('cost_price_this_release_status');
            // 温区类型
            this.tempTypeList = await this.getDictList('fourpl_temperature_type');
        },
        // 获取列表数据
        async getList() {
            this.loading = true;
            try {
                const response = await shipperValueAddedServices.getList(this.queryParams);
                this.orderList = response.data.records;
                this.total = response.data.total;
            } catch (error) {
                console.error('获取列表数据失败:', error);
            }
            this.loading = false;
        },
        // 获取货主公司列表
        async getShipperList() {
            try {
                const response = await handoverOrderConfiguration.cooperateSelect();
                this.shipperList = response.data.map((item) => ({
                    id: item.companyId,
                    name: item.companyName
                }));
            } catch (error) {
                console.error('获取货主公司列表失败:', error);
            }
        },
        // 搜索
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        // 重置
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            // 清空URL中的查询参数
            this.$router.replace({
                path: this.$route.path,
                query: {}
            });
            this.handleQuery();
        },
        // 修改货主公司选择处理方法
        handleShipperChange(value) {
            this.queryParams.ownerId = value;
            this.handleQuery();
        }
    }
};
</script>

<style lang="scss" scoped></style>
