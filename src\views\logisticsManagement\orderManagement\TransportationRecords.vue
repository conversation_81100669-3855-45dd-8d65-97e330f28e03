<template>
    <div>
        <div v-if="!isEmpty" v-loading="transportationLoading">
            <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <el-descriptions :border="true" :column="2">
                    <el-descriptions-item label="运单号">{{ transportationRecordData.transOrderNo }}</el-descriptions-item>
                    <el-descriptions-item label="随货同行单号">{{ transportationRecordData.externalOrderNo }}</el-descriptions-item>
                    <el-descriptions-item label="货主公司">{{ transportationRecordData.companyName }}</el-descriptions-item>
                    <el-descriptions-item label="发件公司">{{ transportationRecordData.sendCompany }}</el-descriptions-item>
                    <el-descriptions-item label="收件公司">{{ transportationRecordData.receiverCompany }}</el-descriptions-item>
                    <el-descriptions-item label="收件地址">{{ transportationRecordData.receiverAddress }}</el-descriptions-item>
                    <el-descriptions-item label="收件电话">{{ transportationRecordData.receiverUserPhone }}</el-descriptions-item>
                    <el-descriptions-item label="件数">{{ transportationRecordData.goodsPackages }}件</el-descriptions-item>
                    <el-descriptions-item label="运输温区">{{ transportationRecordData.temperatureDesc }}</el-descriptions-item>
                    <el-descriptions-item label="贮藏温区">{{ transportationRecordData.temperatureDesc }}</el-descriptions-item>
                    <el-descriptions-item v-if="transportationRecordData.temperatureType != '1' && transportationRecordData.temperatureType != '2'" label="使用自有设备">{{ transportationRecordData.isDevice == '1' ? '是' : '否' }}</el-descriptions-item>
                    <el-descriptions-item label="签收审核时间">{{ transportationRecordData.auditTime }}</el-descriptions-item>
                    <el-descriptions-item label="运输人">{{ transportationRecordData.transportName }}</el-descriptions-item>
                    <el-descriptions-item label="运输方式">{{ selectDictLabel(carrierWayDicts, transportationRecordData.carrierWay) }}</el-descriptions-item>
                    <el-descriptions-item label="运输人电话">{{ transportationRecordData.transportPhone }}</el-descriptions-item>
                    <el-descriptions-item v-if="transportationRecordData.carrierWay == '1'" label="运输工具">
                        <div style="display: flex; flex-wrap: wrap; gap: 5px">
                            <el-tag v-for="item in transportationRecordData.tool" :key="item.id" style="margin-right: 5px">{{ item }}</el-tag>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="transportationRecordData.carrierWay == '1'" label="设备号">
                        <div style="display: flex; flex-wrap: wrap; gap: 5px">
                            <el-tag v-for="item in transportationRecordData.deviceNo" :key="item.id" style="margin-right: 5px">{{ item }}</el-tag>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="承运单位">{{ transportationRecordData.flowName }}</el-descriptions-item>
                    <el-descriptions-item label="承运单编号">{{ transportationRecordData.thirdWaybillNo }}</el-descriptions-item>
                    <el-descriptions-item label="启运时间">{{ transportationRecordData.startShipmentTime }}</el-descriptions-item>
                    <el-descriptions-item label="启运温度">{{ transportationRecordData.startShipmentTemp ? transportationRecordData.startShipmentTemp + '℃' : '' }}</el-descriptions-item>
                    <el-descriptions-item label="送达时间">{{ transportationRecordData.deliveryTime }}</el-descriptions-item>
                    <el-descriptions-item label="送达温度">{{ transportationRecordData.deliveryTemp ? transportationRecordData.deliveryTemp + '℃' : '' }}</el-descriptions-item>
                </el-descriptions>
            </el-card>
            <el-card v-if="transportationRecordData.orderDetailList && transportationRecordData.orderDetailList.length > 0" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <card-header title="冷链明细" />
                <el-table :data="transportationRecordData.orderDetailList" :fit="true" border class="coldChainTable" style="width: 100%">
                    <el-table-column align="center" label="序号" prop="index" width="50">
                        <template #default="scope">{{ scope.$index + 1 }}</template>
                    </el-table-column>
                    <el-table-column :show-overflow-tooltip="true" align="center" label="通用名称" prop="name" />
                    <el-table-column :show-overflow-tooltip="true" align="center" label="规格/型号" prop="specifications" />
                    <el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" prop="manufacturer" />
                    <el-table-column :show-overflow-tooltip="true" align="center" label="单位" prop="basicUnit" />
                    <el-table-column align="center" label="数量" prop="quantity" />
                    <el-table-column :show-overflow-tooltip="true" align="center" label="批号/序列号" min-width="100px" prop="batchNumber" />
                    <el-table-column :show-overflow-tooltip="true" align="center" label="使用期限/失效日期" min-width="150px" prop="validityDate" />
                    <el-table-column :show-overflow-tooltip="true" align="center" label="批准文号/注册证号/备案证号" min-width="200px" prop="registrationNumber" />
                    <el-table-column :show-overflow-tooltip="true" align="center" label="上市许可持有人/注册人/备案人" min-width="200px" prop="listPermitHolder" />
                    <el-table-column align="center" label="资料是否齐全" min-width="100px">
                        <template #default="scope">
                            {{ scope.row.completeInformation == 1 ? '是' : '否' }}
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
            <div class="tabs-container">
                <div class="tabs-header">
                    <el-tabs type="border-card">
                        <el-tab-pane class="display__upload" label="随货同行单">
                            <div class="mb10">随货同行单 {{ transportationRecordData.shdFileList.length }}/9</div>
                            <the-file-upload :accept="accept" :actions="actions" :value="transportationRecordData.shdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'shdFileList')" />
                        </el-tab-pane>
                        <el-tab-pane class="display__upload" label="回执单">
                            <div class="mb10">回执单 {{ transportationRecordData.hzdFileList.length }}/9</div>
                            <the-file-upload :accept="accept" :actions="actions" :value="transportationRecordData.hzdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'hzdFileList')" />
                        </el-tab-pane>
                        <el-tab-pane class="display__upload" label="客户签名">
                            <div class="mb10">客户签名 {{ transportationRecordData.khqmFileList.length }}/9</div>
                            <the-file-upload :accept="accept" :actions="actions" :value="transportationRecordData.khqmFileList" list-type="picture-card" @input="receiptUploadChange($event, 'khqmFileList')" />
                        </el-tab-pane>
                        <el-tab-pane class="display__upload" label="运输温度">
                            <div class="mb10">运输温度 {{ transportationRecordData.yswdFileList.length }}/9</div>
                            <the-file-upload :accept="accept" :actions="actions" :value="transportationRecordData.yswdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'yswdFileList')" />
                        </el-tab-pane>
                        <el-tab-pane class="display__upload" label="提货温度">
                            <div class="mb10">提货温度 {{ transportationRecordData.thwdFileList.length }}/9</div>
                            <the-file-upload :accept="accept" :actions="actions" :value="transportationRecordData.thwdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'thwdFileList')" />
                        </el-tab-pane>
                    </el-tabs>
                    <div v-if="parentData.isDownloadAllImages" class="download-btn">
                        <el-button icon="el-icon-download" type="primary" @click="downloadAllFile">下载全部附件</el-button>
                    </div>
                </div>
            </div>

            <el-affix v-if="parentData.isToExamine" position="bottom" style="margin-top: 10px">
                <el-card :body-style="{ padding: '10px' }" shadow="hover">
                    <div style="display: flex; justify-content: end">
                        <el-button type="warning" @click="openReviewAndModifyShow">修 改</el-button>
                        <el-button type="danger" @click="openAuditRefuse">审核驳回</el-button>
                        <el-button type="primary" @click="auditConfirm">审核确认</el-button>
                    </div>
                </el-card>
            </el-affix>

            <!-- 审核驳回 -->
            <el-dialog v-model="auditRefuseShow" title="审核驳回" width="30%" @close="cancelRefuse">
                <el-form class="reject_form">
                    <el-form-item label="驳回原因" prop="reason">
                        <el-input v-model="refuseReason" :rows="3" maxlength="200" placeholder="请输入审核驳回原因" show-word-limit type="textarea"></el-input>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button @click="cancelRefuse">取 消</el-button>
                    <el-button type="primary" @click="auditRefuse">确 定</el-button>
                </template>
            </el-dialog>

            <!-- 修改 -->
            <el-drawer v-model="reviewAndModifyShow" size="60vw" title="修改" @close="closeModifyShow">
                <div style="background-color: #f2f2f2; padding: 10px">
                    <modify-transportation-records :parameters="parameters" @close="closeModifyShow" />
                </div>
            </el-drawer>
        </div>
        <div v-else>
            <el-card :body-style="{ padding: '10px' }" shadow="never">
                <div style="display: flex; justify-content: center; align-items: center; height: 100%">
                    <el-empty description="不存在运输记录"></el-empty>
                </div>
            </el-card>
        </div>
    </div>
</template>
<script>
import ModifyTransportationRecords from '@/views/logisticsManagement/orderManagement/ModifyTransportationRecords.vue';
import sign from '@/api/signAudit/sign';
import transportationRecordsPage from '@/api/carrierEnd/transportationRecordsPage';
import { downloadNoData } from '@/utils';
import CardHeader from '@/components/CardHeader';
import TheFileUpload from '@/components/theFileUpload/TheFileUpload.vue';

export default {
    name: 'TransportationRecords',
    components: { TheFileUpload, ModifyTransportationRecords, CardHeader },
    props: {
        parentData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            transportationRecordData: {},
            transportationLoading: false,
            accept: 'image/*,pdf',
            actions: ['preview', 'download'],
            reviewAndModifyShow: false,
            isEmpty: false,
            auditRefuseShow: false,
            refuseReason: undefined,
            parameters: {},
            carrierWayDicts: [] // 运输方式 字典值
        };
    },
    async created() {
        /** 运输方式 */
        this.carrierWayDicts = await this.getDictList('fourpl_transportation_mode');
        this.transportationRecordData = {};
        console.log(this.parentData);

        if (this.parentData.orderNo) {
            this.getDetail();
        } else if (this.parentData.id) {
            this.getReviewDetails();
        } else {
            this.$message.error('未查询到数据');
        }
    },
    methods: {
        /**
         * 审核确认
         */
        auditConfirm() {
            const { shdFileList, hzdFileList, yswdFileList, thwdFileList, khqmFileList, id, transOrderNo } = this.transportationRecordData;
            if (this.transportationRecordData.isDevice != '1' && (this.transportationRecordData.tempType == '3' || this.transportationRecordData.tempType == '4') && this.verifyRequiredFields(this.transportationRecordData)) return;
            /*if (tempType === '1' || tempType === '2') {
                if (hzdFileList.length === 0) {
                    this.$message.error('请上传回执单');
                    return;
                } else if (khqmFileList.length === 0) {
                    this.$message.error('请上传客户签名');
                    return;
                }
            } else if (tempType === '3' || tempType === '4') {
                if (hzdFileList.length === 0) {
                    this.$message.error('请上传回执单');
                    return;
                } else if (khqmFileList.length === 0) {
                    this.$message.error('请上传客户签名');
                    return;
                } else if (yswdFileList.length === 0) {
                    this.$message.error('请上传运输温度');
                    return;
                } else if (thwdFileList.length === 0) {
                    this.$message.error('请上传提货温度');
                    return;
                }
            }*/
            // 通过以上才执行以下

            let signFileList = [
                ...shdFileList.map((item) => ({ ...item, fileType: '3' })),
                ...hzdFileList.map((item) => ({ ...item, fileType: '1' })),
                ...khqmFileList.map((item) => ({ ...item, fileType: '2' })),
                ...yswdFileList.map((item) => ({ ...item, fileType: '4' })),
                ...thwdFileList.map((item) => ({ ...item, fileType: '5' }))
            ].map((item) => ({
                fileType: item.fileType,
                transOrderNo,
                // yswdFileList thwdFileList 的 fileUrl 用 fileUrl 其他的用 url
                fileUrl: item.fileType === '4' || item.fileType === '5' ? item.fileUrl : item.url,
                fileName: item.name
            }));

            const params = {
                id,
                orderDrug: {
                    transOrderNo
                },
                signFileList
            };
            sign.signAuditConfirm(params).then((res) => {
                if (res.code === 200) {
                    this.$message.success('审核确认成功');
                    this.$emit('close');
                }
            });
        },
        /**
         * 审核驳回
         */
        auditRefuse() {
            if (!this.refuseReason) {
                this.msgError('请输入审核驳回原因');
            } else {
                const params = {
                    id: this.transportationRecordData.id,
                    orderDrug: {
                        transOrderNo: this.transportationRecordData.transOrderNo
                    },
                    refuseNum: this.transportationRecordData.status,
                    reason: this.refuseReason
                };
                sign.signAuditRefuse(params).then((res) => {
                    if (res.code === 200) {
                        this.$message.success('审核驳回成功');
                        this.cancelRefuse();
                        this.$emit('close');
                    }
                });
            }
        },
        /**
         * 取消审核驳回
         */
        cancelRefuse() {
            this.auditRefuseShow = false;
        },
        /**
         * 关闭修改弹窗
         */
        closeModifyShow() {
            this.reviewAndModifyShow = false;
            this.$emit('close');
        },
        /**
         * 下载全部附件
         */
        downloadAllFile() {
            transportationRecordsPage.download({ filename: this.parentData.transOrderNo + '_全部附件.zip', transOrderNo: this.parentData.transOrderNo }, '', '', 'blob').then((res) => {
                downloadNoData(res, 'application/zip', this.parentData.transOrderNo + '_全部附件.zip');
            });
        },
        /**
         * 获取详情 orderNo
         */
        getDetail() {
            this.transportationLoading = true;
            transportationRecordsPage
                .getDetail({ orderNo: this.parentData.orderNo })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        // shdFileList hzdFileList yswdFileList thwdFileList 的 fileUrl 前 加上 服务器地址
                        const { shdFileList, hzdFileList, yswdFileList, thwdFileList, khqmFileList, tool, deviceNo } = res.data;
                        tool && (res.data.tool = tool.split(','));
                        deviceNo && (res.data.deviceNo = deviceNo.split(','));
                        this.transportationRecordData = {
                            ...res.data,
                            shdFileList: shdFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            hzdFileList: hzdFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            yswdFileList: yswdFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            thwdFileList: thwdFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            khqmFileList: khqmFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            })
                        };

                        this.isEmpty = false;
                    } else {
                        this.isEmpty = true;
                    }
                })
                .finally(() => {
                    this.transportationLoading = false;
                });
        },
        /**
         * 获取审核详情 id
         */
        getReviewDetails() {
            this.transportationLoading = true;
            sign.signDetali({
                id: this.parentData.id
            })
                .then((response) => {
                    if (response.code === 200) {
                        // 存储id用于修改
                        this.parameters.modifyId = response.data?.transportRecord?.id;
                        // 将 response.data.orderDrug 和 response.data.transportRecord 合并 赋值给 this.transportationRecordData
                        // response.data.orderDrug tool deviceNo 为字符串，需要转换成数组
                        this.transportationRecordData = {
                            isDevice: response.data?.isDevice || '0',
                            externalOrderNo: response.data?.externalOrderNo,
                            tempType: response.data?.tempType,
                            ...response.data.orderDrug,
                            ...response.data.transportRecord,
                            id: response.data?.id,
                            status: response.data?.status,
                            companyName: response.data?.companyName,
                            auditTime: response.data?.signDate,
                            temperatureDesc: response.data.orderDrug?.temperatureType?.describtion,
                            temperatureType: response.data.orderDrug?.temperatureType?.type,
                            transportName: response.data.createBy?.name,
                            transportPhone: response.data.createBy?.phone,
                            tool: response.data.transportRecord?.tool ? response.data.transportRecord.tool.split(',') : [],
                            deviceNo: response.data.transportRecord?.deviceNo ? response.data.transportRecord.deviceNo.split(',') : [],
                            carrierWay: response.data.carrierWay,
                            flowName: response.data.flowName,
                            flowNo: response.data.flowNo,
                            orderDetailList: response.data.orderDetailList,
                            thirdWaybillNo: response.data.thirdWaybillNo,
                            hzdFileList: response.data.hzFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            shdFileList: response.data.shdFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            yswdFileList: response.data.yswdFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            thwdFileList: response.data.thwdFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            }),
                            khqmFileList: response.data.khqmFileList.map((item) => {
                                return {
                                    ...item,
                                    url: item.fileUrl,
                                    name: item.fileName
                                };
                            })
                        };
                        this.parameters.transOrderNo = this.transportationRecordData.transOrderNo;
                    }
                })
                .finally(() => {
                    this.transportationLoading = false;
                });
        },
        /**
         * 打开审核驳回
         */
        openAuditRefuse() {
            this.auditRefuseShow = true;
        },
        /**
         * 打开修改弹窗
         */
        openReviewAndModifyShow() {
            this.reviewAndModifyShow = true;
            this.parameters.transportationRecordData = this.transportationRecordData;
        },
        /**
         * 上传成功
         * @param response
         * @param type
         */
        receiptUploadChange(data, type) {
            this.transportationRecordData[type] = data;
        },
        /**
         * 验证必填项
         * @param data
         * @returns {boolean}
         */
        verifyRequiredFields(data) {
            if (data.startShipmentTime == '') {
                this.$message.error('请输入启运时间');
                return true;
            }
            if (data.startShipmentTemp == '') {
                this.$message.error('请输入启运温度');
                return true;
            }
            if (data.deliveryTime == '') {
                this.$message.error('请输入送达时间');
                return true;
            }
            if (data.deliveryTemp == '') {
                this.$message.error('请输入送达温度');
                return true;
            }
            // if(data.shdFileList.length == 0){
            // 	this.$message.error('请上传随货同行单');
            // 	return true;
            // }
            // if(data.yswdFileList.length == 0){
            // 	this.$message.error('请上传运输温度');
            // 	return true;
            // }
            // if(data.thwdFileList.length == 0){
            // 	this.$message.error('请上传提货温度');
            // 	return true;
            // }
            return false;
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-upload-list--picture-card .el-upload-list__item-actions:hover span) {
    display: contents !important;
}
.display__upload {
    :deep(.el-upload) {
        display: none !important;
    }
}
:deep(.el-descriptions__label) {
    // 不换行
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.tabs-container {
    margin-bottom: 20px;
    .tabs-header {
        position: relative;
        .download-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            z-index: 10;
        }
    }
}
</style>
