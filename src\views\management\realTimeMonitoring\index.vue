<template>
    <div class="home">
        <div id="map-box"></div>
        <div class="info-box">
            <el-collapse v-model="activeNames" @change="handleChange">
                <el-collapse-item name="1" title="实时监控">
                    <template #title>
                        <el-icon :size="26" class="header-icon" color="#000000" style="margin-left: 20px">
                            <VideoCamera />
                        </el-icon>
                        <span style="padding-left: 20px; font-weight: 600">实时监控</span>
                    </template>
                    <el-form :model="queryParams" label-width="80px">
                        <el-form-item label="设备厂家">
                            <el-select v-model="queryParams.manufacturer" clearable disabled style="width: 200px">
                                <el-option v-for="item in manufacturerList" :key="item.id" :label="item.name" :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="设备编号">
                            <el-input v-model="queryParams.serialNumber" clearable placeholder="请输入设备编号" style="width: 200px" @keyup.enter.native="submitForm" />
                        </el-form-item>
                        <div style="display: flex; justify-content: center">
                            <el-button type="primary" @click="submitForm">确 定</el-button>
                        </div>
                    </el-form>
                </el-collapse-item>
            </el-collapse>
        </div>
        <el-table :data="deviceList" border style="margin-top: 10px">
            <el-table-column align="center" label="设备编号" prop="deviceNo" width="180" />
            <el-table-column align="center" label="时间" prop="lastTime" width="180">
                <template #default="scope">
                    <span>{{ dayTime(scope.row.lastTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="位置" prop="location.location" />
            <el-table-column align="center" label="经度" prop="location.lat" width="180" />
            <el-table-column align="center" label="纬度" prop="location.lng" width="180" />
            <el-table-column align="center" label="设备信息" prop="battery">
                <template #default="scope">
                    <div style="display: flex; justify-content: center">
                        <div class="information">保温箱号：{{ scope.row.incubator }}</div>
                        <div class="information">温度：{{ scope.row.temperature }}℃</div>
                        <div class="information">湿度：{{ scope.row.humidity }}RH%</div>
                        <div class="information">电量：{{ scope.row.battery }}V</div>
                        <div class="information">{{ dayTime(scope.row.lastTime) }}</div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import { getCurrentInstance, ref, shallowRef } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { VideoCamera } from '@element-plus/icons-vue';
import realTimeMonitoringApi from '@/api/management/realTimeMonitoring';
import { ElLoading } from 'element-plus';

const { proxy } = getCurrentInstance();
const map = shallowRef(null);
let AMapObj, marker;
const queryParams = ref({});
const lat = ref(103.724037);
const lng = ref(36.033333);
// 设备厂家
const manufacturerList = ref([]);
function initMap() {
    AMapLoader.load({
        key: 'b70893c02ea4e6682c90cb424131d434', //设置您的key
        version: '2.0',
        plugins: ['AMap.ToolBar', 'AMap.Driving'],
        AMapUI: {
            version: '1.1',
            plugins: []
        },
        Loca: {
            version: '2.0.0'
        }
    })
        .then((AMap) => {
            AMapObj = AMap;
            map.value = new AMap.Map('map-box', {
                viewMode: '3D',
                zoom: 10,
                zooms: [2, 22],
                center: [lat.value, lng.value]
            });
            addMarker();
            AMap.plugin(['AMap.ToolBar', 'AMap.Scale', 'AMap.Geolocation', 'AMap.PlaceSearch', 'AMap.Geocoder', 'AMap.AutoComplete'], () => {
                // 缩放条
                const toolbar = new AMap.ToolBar();
                // 比例尺
                const scale = new AMap.Scale();
                // 定位
                const geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true, // 是否使用高精度定位，默认:true
                    timeout: 10000, // 超过10秒后停止定位，默认：5s
                    position: 'RT', // 定位按钮的停靠位置
                    buttonOffset: new AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
                    zoomToAccuracy: true // 定位成功后是否自动调整地图视野到定位点
                });
                map.value.addControl(geolocation);
                map.value.addControl(toolbar);
                map.value.addControl(scale);
            });
        })
        .catch((e) => {
            console.log(e);
        });
}
// 查询
const deviceList = ref([]);
async function submitForm() {
    // 清除标记点
    marker.remove(map.value);
    let data = {
        // manufacturer: queryParams.value.manufacturer,
        deviceNo: queryParams.value.serialNumber
    };
    // 添加全屏Loading
    ElLoading.service({ fullscreen: true, text: '加载中...' });
    const _proxy = proxy;
    try {
        // 发起请求并等待响应
        const res = await realTimeMonitoringApi.findDevicePosition(data);
        if (res.code === 200) {
            deviceList.value[0] = res.data;
            // 检查location属性是否存在
            const location = res.data?.location;
            if (location) {
                lat.value = location.lat;
                lng.value = location.lng;
            } else {
                // 提示 未获取到设备位置
				_proxy.msgError('未获取到设备位置');
            }

            // 初始化地图（如果尚未初始化）
            initMap();
            // marker = new AMapObj.Marker({
            //   icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            //   position: [lat.value,lng.value],
            //   // position: new AMapObj.LngLat(lat.value, lng.value),
            //   offset: new AMapObj.Pixel(-26.5, -68)
            // });
            // marker.setMap(map.value);
        } else {
            // 处理错误情况
            proxy.msgError('获取设备位置失败');
        }
    } catch (err) {
        // 处理异常情况
        proxy.msgError('获取设备位置失败');
    } finally {
        // 关闭全屏Loading
        ElLoading.service().close();
    }
}
// 实例化点标记
function addMarker() {
    // if (!marker) {
    marker = new AMapObj.Marker({
        icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
        position: [lat.value, lng.value],
        offset: new AMapObj.Pixel(-13, -30)
    });
    marker.setMap(map.value);
    // }
}
// 字典请求
const getDict = async () => {
    manufacturerList.value = await proxy.getDictList('equipment_manufacturer');
    // 暂时默认中集
    queryParams.value.manufacturer = manufacturerList.value[0].code; // 字典查询
};
function dayTime(time) {
    let date = new Date(time);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    let hour = date.getHours();
    let minute = date.getMinutes();
    let second = date.getSeconds();
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
}

initMap();
getDict();
</script>

<style lang="scss" scoped>
.home {
    height: 700px;
    width: 100%;
    padding: 0px;
    margin: 0px;
    position: relative;

    .info-box {
        position: absolute;
        top: 8px;
        left: 8px;
        width: 300px;
        background-color: #fff;
        display: flex;
        flex-direction: column;
    }
}

#map-box {
    height: 100%;
    width: 100%;
    padding: 0px;
    margin: 0px;
}

.information {
    margin-right: 15px;
}
</style>
