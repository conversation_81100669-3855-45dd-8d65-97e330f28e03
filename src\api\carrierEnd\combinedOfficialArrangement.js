import request from '@/utils/request';
export default {
    // 列表
    getBillingFormulaList: function (params) {
        return request.get('/cost/formula/list', params);
    },
    // 新增合算公式
    addFormula: function (params) {
        return request.post('/cost/formula/save', params);
    },
    // 修改合算公式
    editFormula: function (params) {
        return request.post('/cost/formula/edit', params);
    },
    // 修改状态
    editFormulaStatus: function (params) {
        return request.get('/cost/formula/updateStatus', params);
    },
	// 发布公式
	releaseFormula: function (params) {
		return request.get('/cost/formula/published', params);
	},
	// 注销公式
	logoutFormula: function (params) {
		return request.get('/cost/formula/loggedout', params);
	},
	// 修改已发布公式组
	editFormulaPriceBookContract: function (params) {
		return request.post('/cost/formula/update/formulaConditionList', params);
	}
};
