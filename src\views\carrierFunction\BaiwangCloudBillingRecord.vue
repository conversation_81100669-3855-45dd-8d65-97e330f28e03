<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form">
                <el-form-item label="申请人" prop="applyUser" style="width: 230px">
                    <el-input v-model="queryParams.applyUser" clearable placeholder="请输入申请人" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="开票单位名称" prop="invoiceHead">
                    <el-input v-model="queryParams.invoiceHead" clearable placeholder="请输入开票单位名称" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="开票主体" prop="signCompany" style="width: 240px">
                    <el-select v-model="queryParams.signCompany" clearable filterable placeholder="请选择开票主体" @change="handleQuery">
                        <el-option v-for="dict in companyList" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="项目名称" prop="projectName">
                    <el-input v-model="queryParams.projectName" clearable placeholder="请输入项目名称" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item class="date-screening" label="开票日期" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="开票员" prop="invoiceUser" style="width: 240px">
                    <el-input v-model="queryParams.invoiceUser" clearable placeholder="请输入开票员" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery('queryForm')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 16px">
                <el-button :disabled="dataList.length == 0" icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :tableID="'BaiwangCloudBillingRecord'" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="BaiwangCloudBillingRecord" ref="ColumnTable" v-loading="loading" :columns="columns" :data="dataList" :show-summary="true">
                <template #invoiceHead="{ row }">
                    <span>{{ row.invoiceHead || '--' }}</span>
                </template>
                <template #openInvoiceType="{ row }">
                    <span>{{ row.openInvoiceType ? selectDictLabel(openInvoiceTypeDictionary, row.openInvoiceType) : '--' }}</span>
                </template>
                <template #invoiceType="{ row }">
                    <span>{{ row.invoiceType ? selectDictLabel(invoiceTypeDictionary, row.invoiceType) : '' }}</span>
                </template>
                <template #signCompany="{ row }">
                    <span>{{ row.signCompany ? selectDictLabel(companyList, row.signCompany) : '' }}</span>
                </template>
            </column-table>
            <div class="flex justify-end items-center">
                <span style="margin-right: 10px; color: red; font-size: 15px"
                    >开票金额合计:<b>{{ Number(invoiceAmount).toFixed(2) }}</b
                    >元</span
                >
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>

<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import dataQuery from '@/api/carrierEnd/dataQuery.js';
import moment from 'moment';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'BaiwangCloudBillingRecord',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar
    },
    data() {
        return {
            showSearch: true,
            isShowAll: false,
            loading: false,
            queryParams: {
                current: 1,
                size: 10,
                applyUser: null, // 申请人
                invoiceHead: null, // 开票单位名称
                projectName: null, // 项目名称
                invoiceUser: null, // 开票员
                queryTime: [] // 开票日期
            },
            dataList: [],
            columns: [
                { title: '申请人', width: '180px', align: 'left', key: 'applyUser', columnShow: true, showOverflowTooltip: true },
                // { title: '货主', minWidth: '180px', align: 'center', key: 'companyName', columnShow: true },
                { title: '开票单位名称', minWidth: '220px', align: 'left', key: 'invoiceHead', columnShow: true, showOverflowTooltip: true },
                { title: '开票主体', minWidth: '220px', align: 'left', key: 'signCompany', columnShow: true, showOverflowTooltip: true },
                { title: '项目名称', minWidth: '140px', align: 'center', key: 'projectName', columnShow: true },
                { title: '开票业务类型', width: '110px', align: 'center', key: 'openInvoiceType', columnShow: true },
                { title: '发票类型', width: '110px', align: 'center', key: 'invoiceType', columnShow: true },
                { title: '发票种类', width: '110px', align: 'center', key: 'recordType', columnShow: true },
                { title: '开票金额', width: '110px', align: 'center', key: 'invoiceAmount', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '发票号码', width: '170px', align: 'center', key: 'invoiceNo', columnShow: true },
                { title: '开票日期', width: '120px', align: 'center', key: 'invoiceDate', columnShow: true },
                { title: '开票员', width: '120px', align: 'center', key: 'invoiceUser', columnShow: true, showOverflowTooltip: true }
            ],
            shortcuts:setDatePickerShortcuts(),
            invoiceAmount: 0,
            invoiceTypeDictionary: [], // 发票类型
            openInvoiceTypeDictionary: [], // 开票业务类型
            companyList: [] //开票主体
        };
    },
    created() {
        this.getDict();
        this.handleQuery();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            this.invoiceTypeDictionary = await this.getDictList('collaborating_shipper_invoice_type'); // 发票类型
            this.openInvoiceTypeDictionary = await this.getDictList('invoicing_business_type'); // 开票业务类型
            this.companyList = await this.getDictList('signing_company'); // 开票主体
        },
        /**
         * 切换查询显示
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 重置查询
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryParams = {
                current: 1,
                size: 10,
                applyUser: null, // 申请人
                invoiceHead: null, // 开票单位名称
                projectName: null, // 项目名称
                invoiceUser: null, // 开票员
                queryTime: [] // 开票日期
            };
            this.handleQuery();
        },
        /**
         * 查询
         * @param e
         */
        handleQuery(e) {
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                this.queryParams.startInvoiceDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endInvoiceDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.startInvoiceDate = null;
                this.queryParams.endInvoiceDate = null;
            }
            this.queryParams.current = 1;
            this.getList();
            this.getInvoiceRecordTotalMoney();
        },
        /**
         * 请求API
         */
        getList() {
            this.loading = true;
            const { queryTime, ...params } = this.queryParams;
            dataQuery
                .listInvoiceRecord(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.loading = false;
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    } else {
                        this.loading = false;
                    }
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        /**
         * 获取开票记录总金额
         */
        getInvoiceRecordTotalMoney() {
            this.invoiceAmount = 0;
            const { current, size, queryTime, ...params } = this.queryParams;
            dataQuery.getInvoiceRecordTotalMoney(params).then((res) => {
                if (res.code === 200) {
                    this.invoiceAmount = res.data;
                }
            });
        },
        /**
         * 导出
         */
        handleExport() {
            const { current, size, queryTime, ...params } = this.queryParams;
            dataQuery.exporInvoiceRecord({ filename: '百望云开票记录列表.xls', ...params }, '', '', 'blob').then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '百望云开票记录列表.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                    this.msgSuccess('百望云开票记录列表导出任务已生成！');
                } else {
                    this.msgError('导出异常请联系管理员');
                }
            });
        }
    }
};
</script>

<style scoped></style>
