<template>
<el-dialog :modelValue='show' center append-to-body :title='title' width='40%' draggable @close="cancelClick">
  <div class="box-content">
    <h4>为维护寄递渠道安全，防止安全责任事故发生，有效促进双方全面履行寄递合同，依据《邮政法》《快递市场管理办法》等相关法律法规，甲、乙双方就寄递物品安全保障达成本协议,以共同遵守:</h4>
    <p>一、本协议所指违禁品是指在运输过程中可能造成危害人身财产安全，或者污染环境及其它快件风险的物品，包括但不限于国家邮政局颁布的《禁寄物品指导目录及处理办法》所列范畴。</p>
    <p>二、乙方在寄递物品前应仔细阅读《禁寄物品指导目录及处理办法》和快递详情单背面条款，并在详情单相应位置填写寄件人和寄递物品的详细信息。</p>
    <p>三、乙方保证委托甲方递送的物品符合国家关干禁止寄递或者限制寄递物品的规定，不属干《禁寄物品指导目录及外理办法》所列范畴，没有危害国家安全、公共安全和公民、法人、其他组织的合法权益。</p>
    <p>四、甲方在揽收环节发现之方寄递的物品属干违禁品的，甲方应停止揽收活动。乙方对不能确定是否安全的，应当出具相关部门的安全证明书，否则，甲方有权拒绝揽收。</p>
    <p>五、乙方应配合田方的工作人员对所寄递的物品进行开箱验视，此验视包括在乙方所在地和田方所在地的验视，但田方应保证验视后物品的完整性。外包装破损不得寄递。</p>
    <p>六、甲方需要乙方提供安全证明书的，乙方应予配合。如乙方不能提供又拒绝开箱验视的，甲方有权拒绝揽收。</p>
    <p>七、乙方明知是违禁品，却故意隐瞒和欺骗甲方，并利用甲方的善意进行寄递，给田方及第三人的财产和人身安全造成损害的，乙方应当赔偿损失:产生的其他相关法律责任由国家相关部门归责。</p>
    <p>八、甲方在中转和派送环节发现乙方寄递的物品属干不需要没收或销塑的违禁品的;应妥善处理，并及时通知乙方，且有权拒绝退还快递费;对于依法需要没收或销毁的物品，甲方可上报给监管部门处理。</p>
    <p>九、乙方签署本协议、使用快递详情单即表明知道并同意所负的安全责任。甲、乙双方先前已经签署《快递服务合同》的，与本协议有异议的条款，以本协议条款为准。</p>
  </div>
  <div style='display: flex;justify-content: center;'>
    <el-button size='small' @click='cancelClick'>{{cancelText}}</el-button>
    <el-button size='small' type='primary' :disabled="buttonDisabled" @click='confirmClick()'>{{buttonText}}</el-button>
  </div>
</el-dialog>
</template>

<script>
export default {
  name: "ProtocolPopup",
  props: {
    show:{
      required: true,
      type: Boolean,
      default:false
    },
    title:{
      type: String,
      default:''
    },
    buttonDelayTime:{
      type: Number,
      default:5
    },
    cancelText:{
      type: String,
      default:'取消'
    },
    confirmText:{
      type: String,
      default:'同意'
    },
  },
  data(){
    return {
      buttonText:'同意',
      buttonDisabled:false,
      timer:null,
      countDown:0,
    };
  },
  created(){
    this.buttonShow();
  },
  beforeDestroy(){
    clearTimeout(this.timer);
  },
  methods:{
    buttonShow(){
      let that = this;
      if(this.show && this.buttonDelayTime > 0){
        this.countDown = this.buttonDelayTime;
        this.buttonText = this.countDown + 's'
        this.buttonDisabled = true;
        this.timer = setInterval(function() {
          // 反复执行的代码
          if(that.countDown > 1){
            that.countDown = parseInt(that.countDown) - 1;
            that.buttonText = that.countDown + 's'
          }else{
            that.buttonText = that.confirmText;
            that.buttonDisabled = false;
            clearTimeout(that.timer);
          }
        }, 1000);
      }
    },
    /**
     * 确认
     */
    confirmClick(){
      this.$emit('update:show', false);
      this.$emit('confirm');
    },

    /**
     * 取消
     */
    cancelClick(){
      this.$emit('update:show', false);
      clearTimeout(this.timer);
      this.$emit('cancel');
    }
  },
}
</script>

<style scoped lang="scss">
.box-content{
  height: 500px;
  overflow-y: scroll;
  user-select:none;
  h4{
    line-height: 2;
  }
  p{
    line-height: 2;
  }
}
</style>
