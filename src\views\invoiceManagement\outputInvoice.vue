<template>
  <div class="inputSearch">
    <outputSearch ref="searchRef" @handleQuery="handleQuery"/>
    <el-card body-style="padding-top:0;padding-bottom:0;" class="box-card last-card" style="margin-top: 10px">
      <template #header>
        <div class="card-header">
          <span><el-button class="button" type="primary" @click="newAddTable()">
              手动录入</el-button><el-button class="button" type="primary">同步接口</el-button></span>
        </div>
      </template>
      <div class="item" style="margin-top:10px">
        <DragTableColumn v-loading="loadingFlag" v-model:queryParams="searchRef.searchForm" :columns="columns"
                         :getList="handleQuery" :row-style="functionIndex.tableRowStyles" :tableData="tableList"
                         className="invoiceManagement_outputInvoicez">
          <template v-slot:operate="{ scopeData }">
            <el-button :disabled="scopeData.row.source == 1" link type="primary"
                       @click="editTable(scopeData.row, '编辑')"><img src="@/assets/icons/update.png"
                                                                      style="margin: 0px 5px 0 0"/>编辑
            </el-button>
            <el-button :disabled="scopeData.row.source == 1" link type="danger" @click="deltable(scopeData.row)"><img
                src="@/assets/icons/delete.png" style="margin: 0px 5px 0 0"/>删除
            </el-button>

            <el-button link type="primary" @click="editTable(scopeData.row, '查看')"><img
                src="@/assets/icons/detail.png"
                style="margin: 2px 5px 0 0"/>详情
            </el-button>
            <el-button link type="success" @click="logFn(scopeData.row.id)"><img src="@/assets/icons/review.png"
                                                                                 style="margin: 0px 2px 0 0"/>操作日志
            </el-button>
          </template>
        </DragTableColumn>
        <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                       :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
                       layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                       @size-change="handleQuery"
                       @current-change="handleQuery"/>
      </div>

    </el-card>
    <el-dialog v-model="dialogVisible" :before-close="handleClose" :title="data.title" width="80%">
      <div v-loading="logflag">
        <outputDialog ref="formRef" :UUID="UUID"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button v-if="formRef?.formFlag ? false : true" type="primary" @click="submitFn()">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible3" title="操作记录" width="30%">
      <div v-loading="logFnFlag">
        <logQuery ref="logQueryRef"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible3 = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import outputSearch from './components/outputSearch.vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import outputDialog from './components/outputDialog.vue'
import {outPut} from "@/api/model/invoice";
import {functionIndex} from "@/views/salesManagement/functionIndex";
import {uuid} from "vue-uuid";
import {backApi, manageApi} from "@/api/model/salesManagement";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const searchRef = ref(null)
const childRef = ref(null)
const data = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const emit = defineEmits([])
const props = defineProps({})
const dialogVisible = ref(false)
const logQueryRef = ref(null)
const dialogVisible3 = ref(false)
const formRef = ref(null)
const loadingFlag = ref(false)
const logflag = ref(false)
const logFnFlag = ref(false)
const logFn = async (id) => {
  logFnFlag.value = true
  dialogVisible3.value = true;
  if (logQueryRef.value) {
    logQueryRef.value.data.list = [];
  }
  const logList = await manageApi.logList({masterId: id})
  if (logList.code == 200) {
    logQueryRef.value.timeFns([], logList.data.records);
  } else {
    ElMessage.error('加载失败')
  }
  logFnFlag.value = false
}
const handleClose = () => {
  if (data.title == '查看详情') {
    dialogVisible.value = false
    emptyFn()
    UUID.value = null
  } else {
    ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    })
        .then(() => {
          dialogVisible.value = false
          emptyFn()
          UUID.value = null
        })
        .catch(() => {
        });
  }
}
const changeTime = (time) => {
  if (time) {
    let newTime = new Date(time)
    newTime = newTime.setDate(newTime.getDate() + 1);
    newTime = functionIndex.transformTimestampSearch(newTime)
    return newTime
  } else {
    return null
  }
}
const handleQuery = () => {
  loadingFlag.value = true
  outPut.getList({
    size: data.pageSize,
    current: data.pageNum,
    customerName: searchRef.value?.searchForm.n1,
    taxpayerNo: searchRef.value?.searchForm.n2,
    invoiceNo: searchRef.value?.searchForm.n3,
    source: searchRef.value?.searchForm.n4,
    invoiceCode: searchRef.value?.searchForm.n5,
    beginCreateDate: searchRef.value?.searchForm.n6[0],
    endCreateDate: changeTime(searchRef.value?.searchForm.n6[1]),
    beginInvoicingDate: searchRef.value?.searchForm.n7[0],
    endInvoicingDate: changeTime(searchRef.value?.searchForm.n7[1]),
  }).then((res) => {
    if (res.code == 200) {
      data.total = res.data.total
      tableList.value = res.data.records
    } else {
      ElMessage.error(res.msg)
    }
    loadingFlag.value = false
  })
}
const luoSave = (id, uuid, nums) => {
  let num = nums ? nums : 1
  backApi.saveFile({
    uuid: uuid,
    busId: id
  }).then((res) => {
    if (res.code == 200) {
      UUID.value = null
      ElMessage.success('提交成功')
    } else {
      if (num <= 5) {
        num++
        setTimeout(() => {
          luoSave(id, uuid, num)
        }, 3000)
      }
    }
  })
}
const submitFn = async () => {
  if (!formRef.value.creatform) return;
  await formRef.value.creatform.validate((valid) => {
    if (valid) {
      let fileArr = []
      formRef.value.backForm.n5.forEach(item => {
        fileArr.push({
          name: item.name,
          resFileUrl: item.resFileUrl ? item.resFileUrl : item.url
        })
      })
      outPut.saveList({
        id: formRef.value.backForm.id ? formRef.value.backForm.id : null,
        invoiceNo: formRef.value.backForm.n1,
        invoiceCode: formRef.value.backForm.n2,
        customer: {
          id: formRef.value.backForm.n8
        },
        invoiceType: formRef.value.backForm.n9,
        customerName: formRef.value.backForm.n6,
        taxpayerNo: formRef.value.backForm.n7,
        invoicingDate: formRef.value.backForm.n3,
        invoicingAmount: formRef.value.backForm.n4,
        source: 0,
        files: JSON.stringify(fileArr)
      }).then(res => {
        if (res.code == 200) {
          if (formRef.value.delFilesUrl.length > 0) {
            console.log(formRef.value.delFilesUrl, formRef.value.delFilesUrl.join(','))
            outPut.delFile({
              fileUrls: formRef.value.delFilesUrl.toString()
            })
          }
          console.log(formRef.value.backForm.n5)
          if (formRef.value.backForm.n5.some(item => item.newFile == true)) {
            luoSave(res.data.id, UUID.value, 1)
          }
          const newarr = []
          formRef.value.tableData.forEach(item => {
            newarr.push({
              salesInvoice: {
                id: item.id
              },
              salesInvoiceReceipt: {
                id: formRef.value.backForm.id ? formRef.value.backForm.id : res.data.id
              },
              //开票金额
              invoicingAmount: item.amount
            })
          })
          outPut.relevancy(newarr).then(res2 => {
            if (res2.code == 200) {
              dialogVisible.value = false
              emptyFn()
              handleQuery()
              ElMessage.success('信息保存成功，并且已与开票申请关联')
            } else {
              ElMessage.error('信息保存成功但关联失败，错误信息: ' + res.msg)
            }
          })
        } else {
          ElMessage.error(res.msg)
        }
      })
    }
  })
}
const emptyFn = () => {
  formRef.value.tableData = []
  for (let key in formRef.value.backForm) {
    if (key == 'n5' || key == 'search') {
      formRef.value.backForm[key] = []
    } else if (key == 'n4') {
      formRef.value.backForm[key] = 0
    } else {
      formRef.value.backForm[key] = ''
    }
  }
  formRef.value.delFilesUrl = []
  formRef.value.creatform?.clearValidate()
}
const editTable = (row, title) => {
  dialogVisible.value = true
  UUID.value = uuid.v1()
  setTimeout(async () => {
    logflag.value = true
    if (title == '查看') {
      formRef.value.formFlag = true
      data.title = '查看详情'
    } else {
      formRef.value.formFlag = false
      data.title = '编辑销项发票'
    }
    formRef.value.tableData = []
    const res1 = await outPut.detailList({id: row.id})
    const res2 = await outPut.detaiInvoices({'salesInvoiceReceipt.id': row.id})
    if (res1.code == 200 && res2.code == 200) {
      formRef.value.backForm.id = res1.data.id ? res1.data.id : null
      formRef.value.backForm.n6 = res1.data.customerName ? res1.data.customerName : ''
      formRef.value.backForm.n9 = res1.data.invoiceType ? res1.data.invoiceType : ''
      formRef.value.backForm.n7 = res1.data.taxpayerNo ? res1.data.taxpayerNo : ''
      formRef.value.backForm.n8 = res1.data.customer.id ? res1.data.customer.id : null
      formRef.value.backForm.n1 = res1.data.invoiceNo ? res1.data.invoiceNo : ''
      formRef.value.backForm.n2 = res1.data.invoiceCode ? res1.data.invoiceCode : ""
      formRef.value.backForm.n3 = res1.data.invoicingDate ? res1.data.invoicingDate : ''
      formRef.value.backForm.n4 = res1.data.invoicingAmount ? res1.data.invoicingAmount : 0
      formRef.value.backForm.n5 = res1.data.files ? JSON.parse(res1.data.files) : []
      res2.data.records.forEach(item => {
        formRef.value.typeDetail = item.salesInvoice.billingType || 0
        item.salesInvoice.oldList = true
        item.salesInvoice.delId = item.id
        formRef.value.tableData.push(item.salesInvoice)
        formRef.value.tableData[formRef.value.tableData.length - 1].invoiceType = item.salesInvoiceReceipt.invoiceType
        if (item.invoicingAmount) {
          formRef.value.tableData[formRef.value.tableData.length - 1].amount = item.invoicingAmount
        }
      })
    } else {
      ElMessage.error('加载失败')
    }
    logflag.value = false
  })
}
const deltable = (row) => {
  ElMessageBox.confirm("确认删除此订单吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        outPut.delList({
          ids: row.id
        }).then(res => {
          if (res.code == 200) {
            handleQuery()
            ElMessage.success('信息删除成功')
          } else {
            ElMessage.error('删除失败，错误信息：' + res.msg)
          }
        })
      })
      .catch(() => {
      });
};
const UUID = ref(null)
const newAddTable = () => {
  setTimeout(() => {
    formRef.value.formFlag = false
    emptyFn()
  }, 100)
  UUID.value = uuid.v1()
  data.title = '创建销项发票'
  dialogVisible.value = true
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  handleQuery()
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})
const columns = ref([
  {label: '发票号码', prop: 'invoiceNo'},
  {label: '发票代码', prop: 'invoiceCode'},
  {label: '发票客户', prop: 'customerName'},
  {label: '纳税人识别号', prop: 'taxpayerNo'},
  {label: '开票日期', prop: 'invoicingDate', type: 'date'},
  {label: '开票金额(含税)', prop: 'invoicingAmount'},
  {
    label: '来源', prop: 'source', type: 'status', filters: [
      {
        value: "0",
        name: "手动录入"
      },
      {
        value: "1",
        name: "同步接口"
      }
    ], searchKey: "n4"
  },
  {label: '创建人', prop: 'createBy.name'},
  {label: '创建日期', prop: 'createDate', type: 'date'},
  {label: '操作', prop: 'operate', minWidth: "300px", type: 'operate', fixed: 'right'},
])
const tableList = ref([
  {id: 1}
])
</script>
<style lang='scss' scoped>
.inputSearch {
  padding: 10px;
}

.item {
  margin-bottom: 18px;
  margin-top: -10px;
}
</style>
