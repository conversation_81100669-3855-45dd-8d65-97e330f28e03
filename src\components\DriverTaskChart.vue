<template>
    <div class="driver-task">
        <div class="section-header">司机任务</div>
        <div class="filter-bar">
            <div class="time-tabs">
                <el-radio-group v-model="queryType" size="small">
                    <el-radio-button label="1">今日</el-radio-button>
                    <el-radio-button label="2">本周</el-radio-button>
                    <el-radio-button label="3">本月</el-radio-button>
                    <el-radio-button label="4">全年</el-radio-button>
                </el-radio-group>
            </div>
            <div class="date-picker">
                <el-date-picker v-model="dateRange" end-placeholder="结束月份" range-separator="至" size="small" start-placeholder="开始月份" style="width: 180px" type="monthrange" value-format="YYYY-MM-DD" />
            </div>
        </div>
        <!-- 图表内容 -->
        <div ref="chartContainer" class="driver-task-chart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import {onMounted, onUnmounted, ref, watch} from 'vue';
import home from '@/api/home/<USER>';

export default {
    name: 'DriverTaskChart',
    setup() {
        const chartContainer = ref(null);
        const queryType = ref('3');
        const dateRange = ref([]);
        let chart = null;

        // 初始化图表配置
        const initChartOption = () => {
            return {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['待运输', '运输中', '已完成', '已取消'],
                    bottom: 5,
                    icon: 'rect',
                    itemWidth: 10,
                    itemHeight: 10
                },
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '35px',
                    top: '20px',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['揽收任务', '干线任务', '配送任务']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '待运输',
                        type: 'bar',
                        itemStyle: {
                            color: '#FFD26B'
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: [0, 0, 0]
                    },
                    {
                        name: '运输中',
                        type: 'bar',
                        itemStyle: {
                            color: '#7BA8FD'
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: []
                    },
                    {
                        name: '已完成',
                        type: 'bar',
                        itemStyle: {
                            color: '#45F269'
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: []
                    },
                    {
                        name: '已取消',
                        type: 'bar',
                        itemStyle: {
                            color: '#FD6F6F'
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: []
                    }
                ]
            };
        };

        // 初始化图表
        const initChart = () => {
            if (chart) {
                chart.dispose();
            }
            chart = echarts.init(chartContainer.value);
            chart.setOption(initChartOption());
        };
        // 添加获取日期范围的方法
        const getDateRange = (type) => {
            const now = new Date();
            const start = new Date();
            const end = new Date();

            switch (type) {
                case '1': // 今日
                    return [now.toISOString().split('T')[0], now.toISOString().split('T')[0]];
                case '2': // 本周
                    start.setDate(now.getDate() - now.getDay() + 1);
                    end.setDate(now.getDate() + (7 - now.getDay()));
                    break;
                case '3': // 本月
                    start.setDate(1);
                    end.setMonth(now.getMonth() + 1, 0);
                    break;
                case '4': // 全年
                    start.setMonth(0, 1);
                    end.setMonth(11, 31);
                    break;
            }
            return [start.toISOString().split('T')[0], end.toISOString().split('T')[0]];
        };

        // 修改更新图表数据的方法
        const updateChartData = async () => {
            let params = {};

            if (dateRange.value && dateRange.value.length === 2) {
                queryType.value = '';
                // 获取结束月份的最后一天
                const endDate = new Date(dateRange.value[1]);
                endDate.setMonth(endDate.getMonth() + 1, 0);
                const formattedEndDate = endDate.toISOString().split('T')[0];

                params = {
                    queryType: '1',
                    startQueryDate: dateRange.value[0],
                    endQueryDate: formattedEndDate
                };
            } else {
                params = {
                    queryType: queryType.value,
                    ...Object.fromEntries(getDateRange(queryType.value).map((date, index) => [index === 0 ? 'startQueryDate' : 'endQueryDate', date]))
                };
            }

            try {
                const response = await home.getDriverTaskNum(params);
                const list = response.data;

                // 检查数据是否为空
                if (!list || list.length === 0) {
                    return;
                }

                // 更新图表数据
                const option = chart.getOption();
                list.forEach((item) => {
                    const seriesIndex = parseInt(item.dataType) - 1;
                    if (seriesIndex >= 0 && seriesIndex < option.series.length) {
                        option.series[seriesIndex].data = item.datas;
                    }
                });

                chart.setOption(option);
            } catch (error) {
                console.error('获取订单趋势数据失败:', error);
            }
        };

        // 修改 watch 逻辑部分
        watch(
            queryType,
            (newVal, oldVal) => {
                if (newVal && newVal !== oldVal) {
                    // 添加对比，避免重复触发
                    dateRange.value = []; // 这个操作会触发 dateRange 的 watch
                    updateChartData(); // 直接调用一次就够了
                }
            },
            { flush: 'post' }
        ); // 添加 flush: 'post' 确保在 DOM 更新后执行

        watch(
            dateRange,
            (newVal, oldVal) => {
                // 避免初始化和重复触发
                if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
                    if (newVal && newVal.length === 2) {
                        updateChartData();
                    }
                }
            },
            { flush: 'post' }
        );

        onMounted(() => {
            initChart();
            updateChartData();

            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                chart?.resize();
            });
        });

        onUnmounted(() => {
            chart?.dispose();
            window.removeEventListener('resize', chart?.resize);
        });

        return {
            chartContainer,
            queryType,
            dateRange
        };
    }
};
</script>

<style lang="scss" scoped>
.driver-task {
    height: 100%;
    padding: 10px 10px 0 10px;

    .section-header {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
    }

    .filter-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
.driver-task-chart {
    height: 300px;
}
</style>
