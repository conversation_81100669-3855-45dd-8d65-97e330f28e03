import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/erp/sale/orderTemplate/list', params)
  },
  delete: function (ids) {
    return http.delete('/erp/sale/orderTemplate/delete', ids)
  },
  save: function (params) {
    return http.post('/erp/sale/orderTemplate/save', params)
  },
  details: function (id) {
    return http.get('/erp/sale/orderTemplate/queryById', id)
  },
  clientType: function (params) {
    return http.get("/erp/customer/erpCustomers/list", params);
  },
  getUser: function (id) {
    return http.get('/sys/user/list', id)
  },
  searchRepeat: function (params) {
    return http.post('/erp/sale/orderTemplate/repeatQuery', params)
  },
}