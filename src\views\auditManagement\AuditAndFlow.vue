<template>
    <div class="app-container">
        <!-- 审核主流程配置 -->
        <el-card class="box-card">
            <template #header>
                <div class="card-header">
                    <span>系统审核主流程配置</span>
                    <div>
                        <el-button v-hasPermi="['audit:flow:add']" class="button" type="primary" size="mini" @click="addBtn()">新增 </el-button>
                        <el-button circle icon="el-icon-refresh" size="mini" @click="getList" />
                    </div>
                </div>
            </template>
            <div>
                <el-table v-loading="loading1" :data="data.flowData" highlight-current-row border style="width: 100%; margin-bottom: 12px">
                    <el-table-column label="流程名称" prop="name" />
                    <el-table-column label="对应表单名称" prop="forms" :formatter="getFormIdFormat" />
                    <el-table-column label="审批机构类型" prop="orgType" :formatter="getOrgTypeFormat" />
                    <el-table-column label="备注" prop="remark" />
                    <el-table-column label="操作" width="200px">
                        <template #default="scope">
                            <el-button v-hasPermi="['audit:orgFlow:bindOrg']" plain size="mini" style="padding: 5px 5px" text type="success" @click="selectACarrier(scope.row)">绑定组织 </el-button>
                            <el-button v-hasPermi="['audit:flow:edit']" plain size="mini" style="padding: 5px 5px" text type="primary" @click="editBtn(scope.row)">编辑 </el-button>
                            <el-button v-hasPermi="['audit:flow:del']" plain size="mini" style="padding: 5px 5px" text type="danger" @click="delFlow(scope.row)">删除 </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <pagination v-show="data.total > 0" v-model:limit="data.pageSize" v-model:page="data.pageNum" :total="data.total" @pagination="getList" />
        </el-card>
        <!-- 对话框Form表单 主流程配置-->
        <el-dialog v-model="mainFlowVisible" title="主流程配置" width="500px">
            <el-form ref="baseForm" :label-position="labelPosition" :model="form" :rules="rules" label-width="auto" style="max-width: 90%; margin-top: 10px">
                <el-form-item label="审批机构类型" prop="orgType">
                    <el-select v-model="form.orgType" :reserve-keyword="false" allow-create default-first-option filterable placeholder="请选择审批机构类型" size="default" style="width: 100%">
                        <el-option v-for="(item, index) in orgTypeOptions" :key="index" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="表单名称" prop="formId">
                    <el-select v-model="form.formId" class="m-2" :disabled="form.id" placeholder="请选择对应表单名称" size="default" style="width: 100%" @change="formIdChange">
                        <el-option v-for="(item, index) in formTypes" :key="index" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="流程名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入流程名称" maxlength="60" show-word-limit />
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="form.remark" :rows="5" placeholder="请输入备注" type="textarea" maxlength="500" show-word-limit />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="mainFlowVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveSubmit()"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
        <!--    组织选择-->
        <el-dialog v-model="dialogCarrierSelectsVisible" title="绑定组织" width="800px">
            <div style="text-align: center; width: 100%">
                <el-transfer style="text-align: left; display: inline-block" filterable filter-placeholder="请输入组织关键字" :titles="['待绑定组织', '已绑定组织']" v-model="selectCarrierData.orgIds" :data="listOrgs"> </el-transfer>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogCarrierSelectsVisible = false">取消</el-button>
                    <el-button type="primary" @click="selectCarrierSave()">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { flows } from '@/api/auditManagement/auditAndFlow.js';
import { ElLoading } from 'element-plus';
export default {
    name: 'AuditAndFlow',
    data() {
        return {
            mainFlowVisible: false, // 主流程配置
            dialogCarrierSelectsVisible: false, // 组织选择
            labelPosition: 'right',
            loading1: false,
            loading2: false,
            // 数据
            data: {
                pageSize: 10, //每页条数
                pageNum: 1, //流程 当前页码
                total: 0 //总数据数量
            },
            // 表单
            form: {
                name: undefined,
                remark: undefined,
                orgType: undefined,
                formId: undefined
            },

            listOrgs: [], // 组织列表
            formTypes: [], // 表单名称下拉
            orgTypeOptions: [], // 机构类型
            bindCarriers: [], // 绑定组织列表
            selectCarrierData: {
                flowId: null,
                orgIds: null
            }, // 绑定组织数据
            rules: {
                // 步骤新增
                name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
                formId: [{ required: true, message: '请选择对应表单', trigger: 'blur' }],
                orgType: [{ required: true, message: '请选择审批机构类型', trigger: 'blur' }]
            },
            carrierType: '1' // 1-组织
        };
    },
    methods: {
        /** 机构类型 */
        getOrgTypeFormat(row) {
            return this.selectDictLabel(this.orgTypeOptions, row.orgType);
        },
        /** 表单 */
        getFormIdFormat(row) {
            return this.selectDictLabel(this.formTypes, row.formId);
        },
        /**
         * 承运商选择
         */
        async selectACarrier(row) {
            this.listOrgs = [];
            this.bindOrgs = [];
            const loading = ElLoading.service({
                lock: true,
                text: '正在请求中，请稍后...',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            flows
                .getBindOrgList({
                    orgType: row.orgType,
                    id: row.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.listOrgs = res.data.noBindOrgList.map((item) => {
                            item.key = item.id;
                            item.label = item.name;
                            return item;
                        });
                        this.bindOrgs = res.data.bindOrgList.map((item) => {
                            item.key = item.id;
                            item.label = item.name;
                            item.disabled = true;
                            this.listOrgs.push(item);
                            return item;
                        });
                        this.selectCarrierData = {
                            flowId: row.id,
                            orgIds: this.bindOrgs.map((item) => item.id)
                        };
                        this.dialogCarrierSelectsVisible = true;
                    }
                })
                .catch((err) => {})
                .finally(() => {
                    loading.close();
                });
        },
        /**
         * 保存绑定组织
         */
        selectCarrierSave() {
            let orgIds = [];
            this.selectCarrierData.orgIds.forEach((item) => {
                let index = this.bindOrgs.findIndex((b) => b.id == item);
                if (index === -1) {
                    orgIds.push(item);
                }
            });
            if (orgIds.length == 0) {
                this.msgError('请选择新绑定组织');
                return false;
            }
            flows.saveTheCarrier({ flowId: this.selectCarrierData.flowId, orgIds }).then((res) => {
                if (res.code == 200) {
                    this.msgSuccess('绑定成功');
                    this.getList();
                    this.dialogCarrierSelectsVisible = false;
                } else {
                    this.msgError(res.msg);
                }
            });
        },
        /**
         * 主流程删除
         */
        delFlow(row) {
            let that = this;
            this.$confirm('确认删除此主流程配置?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    flows.delFLow({ ids: row.id }).then((res) => {
                        if (res.code == 200) {
                            that.msgSuccess('删除成功');
                            that.getList();
                        } else {
                            that.msgError(res.msg);
                        }
                    });
                })
                .catch(() => {});
        },
        addBtn() {
            this.reset();
            this.mainFlowVisible = true;
        },
        /**
         * 编辑流程
         */
        editBtn(row) {
            this.mainFlowVisible = true;
            this.form = { ...row };
            this.form.carrierIds = row.carrierIds.toString();
        },
        /**
         * 编辑流程提交
         */
        async saveSubmit() {
            await this.$refs['baseForm'].validate((valid) => {
                if (valid) {
                    const params = {
                        orgType: this.form.orgType,
                        formName: this.form.formName,
                        formId: this.form.formId,
                        name: this.form.name,
                        remark: this.form.remark
                    };
                    if (this.form.id) params.id = this.form.id;
                    flows
                        .saveFlow(params)
                        .then((res) => {
                            if (res.code == 200) {
                                this.msgSuccess(this.form.id ? '编辑成功' : '添加成功');
                                this.getList();
                                this.mainFlowVisible = false;
                                this.reset();
                            } else {
                                this.msgError(res.msg);
                            }
                        })
                        .catch(() => {
                            this.msgError('添加失败，请稍后重试');
                        });
                }
            });
        },
        formIdChange(e) {
            const item = this.formTypes.find((item) => item.value == e);
            if (item) {
                this.form.formName = item.name;
            }
        },
        /**
         * 重置表单
         */
        reset() {
            this.form = {
                name: undefined,
                remark: undefined,
                roleId: undefined,
                formId: undefined,
                carrierIds: undefined
            };
        },
        /**
         * 获取流程列表数据
         */
        getList() {
            this.loading1 = true;
            flows
                .flowData({
                    current: this.data.pageNum,
                    size: this.data.pageSize
                })
                .then((res) => {
                    this.loading1 = false;
                    if (res.code == 200) {
                        this.data.flowData = res.data.records;
                        this.data.total = res.data.total;
                    }
                })
                .catch((e) => {
                    this.loading1 = false;
                });
        }
    },
    async beforeMount() {
        this.getList();
        /*流程表单*/
        this.formTypes = await this.getDictList('audit_form');
        // 机构类型
        this.orgTypeOptions = await this.getDictList('fourpl_org_type');
    }
};
</script>

<style scoped lang="scss">
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
