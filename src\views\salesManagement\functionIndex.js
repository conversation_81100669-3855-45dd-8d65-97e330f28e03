export const functionIndex = {
    //时间转换
    transformTimestamp: (timestamp) => {
        let a = new Date(timestamp).getTime();
        const date = new Date(a);
        if (date == 'Invalid Date') return '--'
        const Y = date.getFullYear() + "-";
        const M =
            (date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1) + "-";
        const D =
            (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
        const h =
            (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
        const m =
            date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
        // const s = date.getSeconds(); // 秒
        const dateString = Y + M + D;
        // console.log('dateString', dateString); // > dateString 2021-07-06 14:23
        return dateString;
    },
    transformTimestampSearch: (timestamp) => {
        let a = new Date(timestamp).getTime();
        const date = new Date(a);
        if (date == 'Invalid Date') return '--'
        const Y = date.getFullYear() + "-";
        const M =
            (date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1) + "-";
        const D =
            (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
        const h =
            (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
        const m =
            (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) + ':';
        const s =
            date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
        // const s = date.getSeconds(); // 秒
        const dateString = Y + M + D + h + m + s;
        // console.log('dateString', dateString); // > dateString 2021-07-06 14:23
        return dateString;
    },
    tableRowStyle: ({row}) => {
        if (row.status == "0") {
            return {
                color: '#e6a23c'
            }
        } else if (row.status == "1") {
            return {
                color: '#409eff'
            }
        } else if (row.status == "2") {
            return {
                color: '#67c23a'
            }
        } else if (row.status == "4") {
            return {
                color: '#ff4800'
            }
        }
    },
    tableRowStyles: ({row}) => {
        if (row.auditStatus == "0") {
            return {
                color: '#e6a23c'
            }
        } else if (row.auditStatus == "1") {
            return {
                color: '#409eff'
            }
        } else if (row.auditStatus == "2") {
            return {
                color: '#67c23a'
            }
        } else if (row.auditStatus == "4") {
            return {
                color: '#ff4800'
            }
        } else if (row.auditStatus == "7") {
            return {
                color: '#ff4800'
            }
        }
    }
}
