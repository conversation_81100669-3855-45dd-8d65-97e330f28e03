<template>
    <el-dialog v-model="dialogVisible" :before-close="handleClose" title="收款单" width="60%">
        <div class="receipt-container">
            <!-- 左侧可提交收款单 -->
            <div v-if="hasAvailableReceipts" class="receipt-section">
                <h3 class="section-title">可以提交的收款单：</h3>
                <div class="receipt-list">
                    <div v-for="receipt in availableReceipts" :key="receipt.id">
                        <div class="receipt-info success-text">
                            <span class="circle-icon success-bg">
                                <el-icon><Check /></el-icon>
                            </span>
                            收款单号：{{ receipt.number }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧不可提交收款单 -->
            <div :class="{ 'full-width': !hasAvailableReceipts }" class="receipt-section">
                <h3 class="section-title">
                    不能提交的收款单：
                    <span class="error-message">(收款单已经进入审批环节)</span>
                </h3>
                <div class="receipt-list">
                    <div v-for="receipt in unavailableReceipts" :key="receipt.id">
                        <div class="receipt-info error-text">
                            <span class="circle-icon error-bg">
                                <el-icon><Close /></el-icon>
                            </span>
                            收款单号：{{ receipt.number }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button v-if="hasAvailableReceipts" type="primary" @click="handleSubmit">只提交正常的收款单</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import { Check, Close } from '@element-plus/icons-vue'

export default {
    name: 'SelectReceipt',
    components: {
        Check,
        Close
    },
    props: {
        availableReceipts: {
            type: Array,
            default: () => []
        },
        modelValue: {
            type: Boolean,
            default: false
        },
        unavailableReceipts: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit('update:modelValue', value);
            }
        },
        hasAvailableReceipts() {
            return this.availableReceipts && this.availableReceipts.length > 0;
        }
    },
    methods: {
        handleClose() {
            this.dialogVisible = false;
        },
        handleSubmit() {
            // 提交所有可用的收款单
            this.$emit(
                'submit',
                this.availableReceipts.map((receipt) => receipt.id)
            );
            this.handleClose();
        }
    },
    emits: ['update:modelValue', 'submit']
};
</script>

<style scoped>
.receipt-container {
    display: flex;
    gap: 20px;
    max-height: 60vh;
    overflow: hidden;
}

.receipt-section {
    flex: 1;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.receipt-section.full-width {
    flex: 1 1 100%;
}

.section-title {
    margin: 0 0 10px 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.receipt-list {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding-right: 8px;
}

.receipt-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    background: #FFFFFF;
}

.success-text {
    color: #67c23a;
}

.error-text {
    color: #f56c6c;
}

.circle-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.success-bg {
    background-color: #67c23a;
    color: #ffffff;
}

.error-bg {
    background-color: #f56c6c;
    color: #ffffff;
}

.circle-icon .el-icon {
    font-size: 12px;
}

.error-message {
    font-size: 12px;
    color: #f56c6c;
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 10px;
}

/* 自定义滚动条样式 */
.receipt-list::-webkit-scrollbar {
    width: 6px;
}

.receipt-list::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
}

.receipt-list::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
}
</style>
