<template>
    <div>
        <el-drawer v-model="open" :title="title" append-to-body size="1100px" @close="confirmOrderCancel">
            <div class="p10 orderDetails" style="background-color: #f2f2f2">
                <el-form ref="form" :model="form" :rules="ruleForm" label-width="130px" size="small">
                    <el-card class="mb10" shadow="never">
                        <div class="box-top">
                            <div>
                                <span>订单号：</span>
                                <span>{{ form.orderNo }}</span>
                            </div>
                            <div v-if="form.transOrderNo">
                                <span>运单号：</span>
                                <span>{{ form.transOrderNo }}</span>
                            </div>
                        </div>
                    </el-card>
                    <el-card class="mb10" shadow="never">
                        <div class="boxOrder">
                            <el-form-item label="揽收方式" prop="orderTypeDesc">
                                {{ form.orderTypeDesc }}
                            </el-form-item>
                            <el-form-item label="付款方式" prop="paymentMethodDesc">
                                {{ form.paymentMethodDesc }}
                            </el-form-item>
                            <el-form-item label="预存款余额">
                                <div v-if="ownerAccountInfo && ownerAccountInfo?.advanceSettlementFlag == '1' && ownerAccountInfo?.advancePaymentInfo"
                                    style="display: flex; justify-content: space-between; align-items: center; width: 100%; background: #f2f2f2">
                                    <div style="flex-shrink: 0; margin-right: 10px">
                                        <span style="color: #ea0008; font-weight: bold">{{
                                            ownerAccountInfo?.advancePaymentInfo?.balance || 0 }}</span><span
                                            style="color: #ea0008">元</span>
                                    </div>
                                    <div
                                        v-if="Number(ownerAccountInfo?.advancePaymentInfo?.balance) <= Number(ownerAccountInfo?.advancePaymentInfo?.balanceWarn)">
                                        <span style="color: #ea0008">预存款金额为{{
                                            ownerAccountInfo.advancePaymentInfo.balance || 0 }}元，请及时缴纳预存款!</span>
                                    </div>
                                </div>
                                <div v-else>无预存款</div>
                            </el-form-item>
                            <el-form-item v-if="form.orderType == '2' && form.sendBranchName" label="网点"
                                prop="sendBranch">
                                {{ form.sendBranchName }}
                            </el-form-item>
                            <el-form-item v-if="form.carrierWayDesc" label="运输方式" prop="carrierWay">
                                {{ form.carrierWayDesc }}
                            </el-form-item>
                        </div>
                    </el-card>

                    <!-- /订单信息   -->
                    <el-card class="mb10" shadow="never">
                        <template #header>
                            <card-header size="mini" title="订单信息" />
                        </template>
                        <div class="box box-info">
                            <div>
                                <el-form-item label="发件地址" prop="sendTown">
                                    <span>{{ form?.sendTown?.province || '' }}{{ form?.sendTown?.city || '' }}{{
                                        form?.sendTown?.county || ''
                                        }}{{ form?.sendTown?.town || '' }}</span>
                                </el-form-item>
                            </div>
                            <div>
                                <el-form-item label="收件地址" prop="shippingAddress">
                                    <span>{{ form?.receiverTown?.province || '' }}{{ form?.receiverTown?.city || '' }}{{
                                        form?.receiverTown?.county || '' }}{{ form?.receiverTown?.town || '' }}</span>
                                </el-form-item>
                            </div>
                            <el-form-item label="发件详细地址" prop="sendAddress">
                                <el-tooltip :content="form.sendAddress" class="item" effect="dark" placement="top">
                                    <span class="hiddenText">{{ form.sendAddress }}</span>
                                </el-tooltip>
                            </el-form-item>
                            <el-form-item label="收件详细地址" prop="receiverAddress">
                                <el-input v-model="form.receiverAddress" maxlength="200" placeholder="请输入详细地址"
                                    show-word-limit></el-input>
                            </el-form-item>
                            <el-form-item label="发件联系人" prop="sendUser">
                                {{ form.sendUser }}
                            </el-form-item>
                            <el-form-item label="收件人名称" prop="receiverUser">
                                {{ form.receiverUser }}
                            </el-form-item>
                            <el-form-item label="发件人电话" prop="sendUserPhone">
                                {{ form.sendUserPhone }}
                            </el-form-item>
                            <el-form-item label="收件人电话" prop="receiverUserPhone">
                                {{ form.receiverUserPhone }}
                            </el-form-item>
                            <el-form-item label="发件公司" prop="sendCompany">
                                {{ form.sendCompany }}
                            </el-form-item>
                            <el-form-item label="收件公司" prop="receiverCompany">
                                {{ form.receiverCompany }}
                            </el-form-item>
                        </div>
                    </el-card>

                    <!-- /货品信息   -->
                    <el-card class="mb10" shadow="never">
                        <template #header>
                            <card-header size="mini" title="货品信息" />
                        </template>
                        <div class="boxOrder box-info">
                            <el-form-item label="产品分类" prop="productClassDesc">
                                {{ form.productClassDesc }}
                            </el-form-item>
                            <el-form-item v-if="form.temperatureTypeDesc" label="温层类型" prop="temperatureTypeDesc">
                                {{ form.temperatureTypeDesc }}
                            </el-form-item>
                            <el-form-item v-if="form.productTypeDesc" label="运输类型" prop="productTypeDesc">
                                {{ form.productTypeDesc }}
                            </el-form-item>
                            <el-form-item v-if="form.productType == 2" label="车辆类型" prop="carTyp">
                                {{ form.carTypeName }}
                            </el-form-item>
                            <el-form-item v-if="form.productType == 2" label="公里数" prop="kilometre">
                                {{ form.kilometre }}
                            </el-form-item>
                            <el-form-item label="件数" prop="goodsPackages">
                                <el-input v-model="form.goodsPackages" placeholder="请输入件数" text="number"></el-input>
                            </el-form-item>
                            <el-form-item v-if="form.productPropertiesDesc" label="货品属性" prop="productProperties">
                                {{ form.productPropertiesDesc }}
                            </el-form-item>
                            <el-form-item v-if="form.productWeight" label="重量" prop="productWeight"> {{
                                form.productWeight }} kg
                            </el-form-item>
                            <el-form-item v-if="form.externalOrderNo" label="随货同行单号" prop="externalOrderNo">
                                {{ form.externalOrderNo }}
                            </el-form-item>
                            <el-form-item v-if="form.productVolume" label="容积" prop="productVolume"> {{
                                form.productVolume }} 升
                            </el-form-item>
                            <el-form-item v-if="form.remark" label="备注" prop="remark">
                                {{ form.remark }}
                            </el-form-item>
                            <el-form-item v-if="form.goodsAmount" label="货值金额" prop="goodsAmount">
                                {{ form.goodsAmount }}
                            </el-form-item>
                            <el-form-item v-if="form.detailDesc" label="详情说明" prop="detailDesc">
                                {{ form.detailDesc }}
                            </el-form-item>
                        </div>
                    </el-card>

                    <!-- /冷链明细   -->
                    <el-card v-if="orderDetailCheck" class="mb10" shadow="never">
                        <template #header>
                            <card-header size="mini" title="冷链明细" />
                        </template>

                        <el-table :data="coldChainProductData" :fit="true" class="coldChainTable" style="width: 100%">
                            <el-table-column align="center" label="序号" prop="index" width="50">
                                <template #default="scope">{{ scope.$index + 1 }}</template>
                            </el-table-column>
                            <el-table-column align="center" label="通用名称" min-width="180" prop="name">
                                <template #default="scope">
                                    <el-input v-model="scope.row.name" maxlength="60" placeholder="请输入通用名称"
                                        show-word-limit size="small"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="规格/型号" min-width="180" prop="specifications">
                                <template #default="scope">
                                    <el-input v-model="scope.row.specifications" maxlength="60" placeholder="请输入规格/型号"
                                        show-word-limit size="small"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="生产厂家" min-width="180" prop="manufacturer">
                                <template #default="scope">
                                    <el-input v-model="scope.row.manufacturer" maxlength="60" placeholder="请输入生产厂家"
                                        show-word-limit size="small"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="单位" min-width="180" prop="basicUnit">
                                <template #default="scope">
                                    <el-input v-model="scope.row.basicUnit" maxlength="10" placeholder="请输入单位"
                                        show-word-limit size="small"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="数量" min-width="180" prop="quantity">
                                <template #default="scope">
                                    <el-input-number v-model="scope.row.quantity" :min="1" size="small"
                                        type="number"></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="批号/序列号" min-width="180" prop="batchNumber">
                                <template #default="scope">
                                    <el-input v-model="scope.row.batchNumber" maxlength="60" placeholder="请输入批号/序列号"
                                        show-word-limit size="small"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="使用期限/失效日期" min-width="210" prop="validityDate">
                                <template #default="scope">
                                    <el-date-picker v-model="scope.row.validityDate" placeholder="请输入使用期限/失效日期"
                                        size="small" style="width: 100%" type="date"
                                        value-format="YYYY-MM-DD"></el-date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="批准文号/注册证号/备案证号" min-width="210"
                                prop="registrationNumber">
                                <template #default="scope">
                                    <el-input v-model="scope.row.registrationNumber" maxlength="60"
                                        placeholder="请输入批准文号/注册证号/备案证号" show-word-limit size="small"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="上市许可持有人/注册人/备案人" min-width="210"
                                prop="listPermitHolder">
                                <template #default="scope">
                                    <el-input v-model="scope.row.listPermitHolder" maxlength="60"
                                        placeholder="请输入上市许可持有人/注册人/备案人" show-word-limit size="small"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="资料是否齐全" min-width="160" prop="completeInformation">
                                <template #default="scope">
                                    <el-radio-group v-model="scope.row.completeInformation" size="small">
                                        <el-radio label="0">否</el-radio>
                                        <el-radio label="1">是</el-radio>
                                    </el-radio-group>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" fixed="right" label="操作" width="60">
                                <template #default="scope">
                                    <el-button size="mini" type="text" @click="removeColdChainItems(scope.$index)">删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div style="display: flex; justify-content: center; margin-top: 15px">
                            <el-button icon="el-icon-plus" type="text" @click="addColdChainProducts">添加冷链明细 </el-button>
                        </div>
                    </el-card>

                    <!-- 增值服务 -->
                    <el-card v-if="addedServiceDicts && addedServiceDicts.length > 0" class="mb10" shadow="never"
                        style="overflow: inherit">
                        <template #header>
                            <card-header size="mini" title="增值服务" />
                        </template>
                        <div>
                            <el-checkbox-group v-model="addServiceList" class="box-valueAddedServiceButton">
                                <el-link v-for="(service, index) in addedServiceDicts" :key="index" :underline="false"
                                    :value="service.id" @click.stop="addServiceListClick(index)">
                                    <el-checkbox :label="service" @change="addServiceListCheckBox($event, index)"
                                        @click.stop.native><br /></el-checkbox>
                                    <span>{{ service.name }} </span>
                                    <span v-if="service.isNeedInput == '1'" style="margin-left: 10px">{{
                                        service.inputValue }} {{
                                        service.unit }} </span>
                                    <span v-if="service.cost" style="margin-left: 10px">¥{{ service.cost }} </span>
                                </el-link>
                            </el-checkbox-group>
                        </div>
                    </el-card>

                    <!-- /阅读条款  -->
                    <el-card class="mb10" shadow="never">
                        <div class="box-footer" style="align-items: end">
                            <div style="display: flex; flex-direction: row; align-items: center; color: #bbbbbb">
                                <div>预估费用</div>
                                <div style="margin: 0 0 15px 15px; font-size: 40px; color: #ea0008">¥{{ form.orderCost
                                    && form.orderCost !=
                                    0 ? form.orderCost : '--' }}</div>
                                <el-button v-if="costDataList.length > 0" icon="el-icon-view"
                                    style="margin-left: 10px; margin-bottom: 15px" type="primary"
                                    @click="costCreakdownClick">明细</el-button>
                            </div>
                            <el-form-item style="margin-bottom: 20px">
                                <!--                    <el-button type="info" @click="resetForm('form')">全部清空</el-button>-->
                                <el-button type="info" @click="confirmOrderCancel">取消</el-button>
                                <el-button :disabled="quicklyPlaceOrderConfirmDisabled" type="primary"
                                    @click="confirmOrder()">确认并提交</el-button>
                            </el-form-item>
                        </div>
                    </el-card>
                </el-form>
            </div>
        </el-drawer>
        <el-dialog v-model="costCreakdownShow" :show-close="false" append-to-body class="icon-dialog"
            modal-append-to-body title="费用明细" width="700px" @close="costCreakdownShow = false">
            <div v-for="(item, index) in costDataList">
                <div v-if="item.data.length > 0" :key="index" style="margin-top: 10px">
                    <div style="display: flex; font-size: 13px; color: #333333">
                        <div>{{ item.name }}</div>
                        <div style="color: #ea0008; margin-left: 20px">¥{{ item.cost }}</div>
                    </div>
                    <div style="display: flex; padding: 15px 0; color: #999999; font-size: 11px">
                        <div v-for="(val, i) in item.data" :key="i" style="display: flex; width: 33.33%">
                            <div>{{ val.name }}</div>
                            <div style="margin-left: 10px">¥{{ val.cost }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="formula" style="padding-bottom: 15px">计算公式：{{ formula }}</div>
        </el-dialog>
        <el-dialog v-model="addedServiceShow" :show-close="false" append-to-body class="icon-dialog"
            modal-append-to-body title="增值服务" width="600px">
            <el-form ref="formAddedService" :model="formService" :rules="serviceRules" label-width="auto">
                <el-form-item :label="formService.name" class="mb0" prop="inputValue">
                    <el-input-number v-model="formService.inputValue" :min="0"
                        :precision="formService.unit == '元' ? 2 : 0" placeholder="请输入值"
                        style="width: 200px !important"></el-input-number>
                    <span style="margin-left: 5px">{{ formService.unit }}</span>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelService">关闭</el-button>
                <el-button type="primary" @click="submitFormService">确 定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import CardHeader from '@/components/CardHeader';
import orderManagement from '@/api/logisticsManagement/orderManagement';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import { ElLoading, ElMessageBox } from 'element-plus';
import { coldChainDetailVerification, verifyThatTheAddressesAreConsistent } from '@/utils';

export default {
    name: 'ConfirmAnOrder',
    props: {
        title: {
            type: String,
            default: ''
        },
        orderInfo: {
            type: Object,
            default: {}
        }
    },
    components: {
        CardHeader
    },
    watch: {
        'form.goodsPackages'(newVal, oldVal) {
            if (newVal && oldVal && newVal != oldVal) {
                if (this.isClearingInterface) {
                    this.getOrderCost();
                }
            }
        },
        'receiverAddressParams.searchValue'() {
            this.getReceiverAddressBooks(true);
        },
        'sendAddressParams.searchValue'() {
            this.getSendAddressBooks(true);
        }
    },
    data() {
        return {
            open: false,
            form: {},
            //表单验证
            ruleForm: {
                receiverAddress: [
                    { required: true, message: '请输入收件地址', trigger: 'blur' },
                    { max: 200, message: '详细地址不能超过200个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (verifyThatTheAddressesAreConsistent(value, this.receiverAddressText)) {
                                callback();
                            } else {
                                callback(new Error('收件详细地址与收件地址不符'));
                            }
                        },
                        trigger: ['blur', 'change']
                    }
                ],
                goodsPackages: [
                    { required: true, message: '请输入件数', trigger: 'blur' },
                    { pattern: /^(\d)+$/, message: '件数只能输入正整数', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value < 1) {
                                callback(new Error('件数必须大于等于1'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ]
            },
            coldChainProductData: [],
            orderDetailCheck: false,
            addedServiceDicts: [], // 增值服务
            addServiceList: [], //新增订单增值服务
            addedServiceShow: false, // 是否显示增值服务弹窗
            formService: {}, // 增值服务修改项
            serviceRules: {
                inputValue: [
                    {
                        required: true,
                        message: '请输入值',
                        trigger: 'blur'
                    }
                ]
            },
            costDataList: [], // 结算费用展示
            costCreakdownShow: false, // 费用明细弹窗
            formula: null, //计算公式
            quicklyPlaceOrderConfirmDisabled: false, //快速下单确认按钮禁用
            ownerAccountInfo: {},
            isClearingInterface: false,
            orderDetail: {}, // 原数据
            fieldData: {
                receiverAddress: '收件详细地址',
                goodsPackages: '件数',
                orderDetailList: '冷链明细',
                addServiceList: '增值服务'
            },
            changeDetailsList: [],
            temperatureType: '' // 添加温区类型变量
        };
    },
    async created() {
        this.open = true;
        this.form = { ...this.orderInfo };
        Object.assign(this.orderDetail, JSON.parse(JSON.stringify({ ...this.orderInfo })));
        this.receiverAddressText = [];
        if (this.orderInfo?.receiverTown?.province) this.receiverAddressText.push(this.orderInfo.receiverTown.province);
        if (this.orderInfo?.receiverTown?.city) this.receiverAddressText.push(this.orderInfo.receiverTown.city);
        if (this.orderInfo?.receiverTown?.county) this.receiverAddressText.push(this.orderInfo.receiverTown.county);
        this.getAdvanceByCompany(); // 货主公司账号信息
        this.coldChainProductData = JSON.parse(JSON.stringify(this.orderInfo?.orderDetailList || []));
        if (this.coldChainProductData.length > 0) {
            this.orderDetailCheck = true;
        }
        // 设置温区类型
        this.temperatureType = this.orderInfo?.temperatureType?.type;
        if (this.orderInfo.addServiceList && this.orderInfo.addServiceList.length > 0) {
            this.form.addedServices = this.orderInfo.addServiceList.map((item) => {
                return { ...item, id: item.addServiceId };
            });
        }
        // 获取增值服务列表
        await this.getAllAvailableAddService();
        /** 产品分类 */
        let timer = setTimeout(() => {
            this.orderDetail.addServiceList = JSON.parse(JSON.stringify(this.addServiceList));
            this.orderDetail.orderDetailList = JSON.parse(JSON.stringify(this.coldChainProductData));
            this.isClearingInterface = true;
            this.getOrderCost();
            clearTimeout(timer);
        }, 800);
    },
    methods: {
        //获取货主公司账号信息
        getAdvanceByCompany() {
            this.ownerAccountInfo = {};
            if (!this.form.companyId) return;
            enterpriseCooperation
                .getAdvanceByCompany({ companyId: this.form.companyId })
                .then((res) => {
                    if (res.code == 200 && res.data) {
                        this.ownerAccountInfo = res.data;
                        if (this.ownerAccountInfo?.advanceSettlementFlag == '1' && this.ownerAccountInfo?.advancePaymentInfo && Number(this.ownerAccountInfo?.advancePaymentInfo?.balance) <= Number(this.ownerAccountInfo?.advancePaymentInfo?.balanceWarn)) {
                            if (this.ownerAccountInfo?.advancePaymentInfo?.orderAuth == '1') {
                                this.msgError('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，余额下限为' + this.ownerAccountInfo.advancePaymentInfo.balanceWarn + '元，已低于下限，暂时无法确认订单，请提醒货主公司及时充值');
                            } else {
                                this.msgError('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，余额下限为' + this.ownerAccountInfo.advancePaymentInfo.balanceWarn + '元，已低于下限，请提醒货主公司及时充值');
                            }
                        }
                    }
                })
                .catch((e) => { });
        },
        checkAddedServices(value) {
            let flag = true;
            if (value && value.length > 0) {
                value.forEach((item) => {
                    let i = this.addedServiceDicts.findIndex((a) => a.id == item.id);
                    if (i > -1) {
                        if (item.inputValue == '' && this.addedServiceDicts[i].isNeedInput == '1') {
                            this.msgError('请输入' + item.name + '的值');
                            flag = false;
                            return false;
                        }
                    }
                });
            }
            return flag;
        },
        // 确认订单
        confirmOrder() {
            if (!this.checkAddedServices(this.form.addedServices)) {
                return;
            }
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.orderDetailCheck) {
                        if (this.coldChainProductData.length === 0) {
                            this.msgError('冷链订单货品明细不能为空');
                            return;
                        } else {
                            const msg = coldChainDetailVerification(this.coldChainProductData);
                            if (msg != null) {
                                this.msgError(msg);
                                return;
                            }
                        }
                    } else {
                        this.coldChainProductData = [];
                    }
                    if (Object.values(this.ownerAccountInfo).length != 0 && this.ownerAccountInfo?.advancePaymentInfo && this.form.paymentMethod == '7') {
                        if (Number(this.ownerAccountInfo?.advancePaymentInfo?.balance) <= Number(this.ownerAccountInfo?.advancePaymentInfo?.balanceWarn)) {
                            if (this.ownerAccountInfo?.advancePaymentInfo?.orderAuth == '1') {
                                this.msgWarning('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，余额下限为' + this.ownerAccountInfo.advancePaymentInfo.balanceWarn + '元，已低于下限，请提醒货主公司及时充值');
                            } else {
                                this.msgWarning('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，余额下限为' + this.ownerAccountInfo.advancePaymentInfo.balanceWarn + '元，已低于下限，请提醒货主公司及时充值');
                            }
                        } else if (Number(this.form.orderCost) > Number(this.ownerAccountInfo?.advancePaymentInfo?.balance)) {
                            this.msgWarning('此货主公司预存款余额为' + this.ownerAccountInfo.advancePaymentInfo.balance + '元，已低于总费用，请提醒货主公司及时充值');
                        }
                    }
                    const loading = ElLoading.service({
                        lock: true,
                        text: 'Loading...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    this.form.orderClass = '2';
                    this.form.status = '3';
                    this.form.orderDetailList = this.coldChainProductData;
                    delete this.form.addedServices;
                    this.form.addServiceList = this.addServiceList;
                    orderManagement
                        .confirmOrder(this.form)
                        .then((res) => {
                            if (res.code === 200) {
                                this.msgSuccess('确认订单成功');
                                this.getChangDetail();
                                // 重置表单
                                // this.resetForm('form');
                                this.quicklyPlaceOrderConfirmDisabled = false;
                                this.open = false;
                                this.$emit('close');
                            } else {
                                this.quicklyPlaceOrderConfirmDisabled = false;
                                // this.msgError(res.message);
                            }
                            loading.close();
                        })
                        .catch((e) => {
                            loading.close();
                        });
                }
            });
        },
        // 对比订单发生的变化并写入
        getChangDetail() {
            this.changeDetailsList = [];
            for (let key in this.fieldData) {
                let oldValue = null;
                let newValue = null;
                if (key == 'orderDetailList' || key == 'addServiceList') {
                    if (key == 'orderDetailList' && (this.orderDetail[key]?.length > 0 || this.form[key]?.length > 0)) {
                        oldValue = this.orderDetail[key] || [];
                        newValue = this.form[key] || [];
                    } else if (key == 'addServiceList' && (this.orderDetail[key]?.length > 0 || this.form[key]?.length > 0)) {
                        oldValue = this.orderDetail[key].map((item) => {
                            return {
                                inputValue: item.inputValue,
                                id: item.id
                            };
                        });
                        newValue = this.form[key].map((item) => {
                            return {
                                inputValue: item.inputValue,
                                id: item.id
                            };
                        });
                    }
                    if (JSON.stringify(oldValue) != JSON.stringify(newValue)) {
                        this.changeDetailsList.push({
                            name: this.fieldData[key], // 异动属性名
                            properties: key, // 异动属性名
                            oldValue: JSON.stringify(oldValue).toString(), // 旧值
                            value: JSON.stringify(newValue).toString() // 当前值
                        });
                    }
                } else {
                    if (!this.form[key] && !this.orderDetail[key]) {
                        continue;
                    }
                    if (this.orderDetail[key] != this.form[key]) {
                        oldValue = this.orderDetail[key];
                        newValue = this.form[key];
                        if (Array.isArray(this.orderDetail[key])) {
                            oldValue = this.orderDetail[key].join(',');
                        }
                        if (this.orderDetail[key] == 'undefined' || this.orderDetail[key] == undefined || this.orderDetail[key] == 'null' || this.orderDetail[key] == null) {
                            oldValue = '';
                        }
                        if (Array.isArray(this.form[key])) {
                            newValue = this.form[key].join(',');
                        }
                        if (this.form[key] == 'undefined' || this.form[key] == undefined || this.form[key] == 'null' || this.form[key] == null) {
                            newValue = '';
                        }
                        this.changeDetailsList.push({
                            name: this.fieldData[key], // 异动属性名
                            properties: key, // 异动属性名
                            oldValue: oldValue, // 旧值
                            value: newValue // 当前值
                        });
                    }
                }
            }
            if (this.changeDetailsList.length == 0) {
                return;
            }
            orderManagement
                .saveTheTransaction({
                    orderId: this.orderDetail.id,
                    changeDetails: this.changeDetailsList
                })
                .then((res) => {
                    if (res.code != 200) {
                    }
                })
                .catch((e) => { });
        },
        // 重置表单
        resetForm(formName) {
            this.coldChainProductData = [];
            this.addServiceList = [];
            this.$refs[formName].resetFields();
        },
        // 显示明细
        costCreakdownClick() {
            this.costCreakdownShow = true;
        },
        // 下单价格实时计算
        getOrderCost() {
            this.orderBranchData = {};
            this.form.collectBranchCode = null;
            this.form.collectAreaCode = null;
            this.form.transportStartBranchCode = null;
            this.form.transportEndBranchCode = null;
            this.form.deliveryBranchCode = null;
            this.form.deliveryAreaCode = null;
            this.costDataList = [];
            this.form.costData = [];
            this.form.orderCost = 0.0;
            if (!this.isClearingInterface) {
                return false;
            }
            this.formula = null;
            if (!this.form.pickupAddress || this.form.pickupAddress.length == 0 || this.form.pickupAddress.length == 1) {
                return false;
            }
            if (!this.form.shippingAddress || this.form.shippingAddress.length == 0 || this.form.shippingAddress.length == 1) {
                return false;
            }
            let params = {
                sendCountyId: this.form.pickupAddress[2],
                receiverCountyId: this.form.shippingAddress[2]
            };
            this.quicklyPlaceOrderConfirmDisabled = true;
            orderManagement
                .getOrderCost(params)
                .then((res) => {
                    if (res.code == 200) {
                        this.orderBranchData = res.data;
                        this.form.collectBranchCode = res.data.sendBranchCode; // 揽收网点编号
                        this.form.collectAreaCode = res.data.sendAreaCode; // 揽收区域编号
                        this.form.transportStartBranchCode = res.data.sendBranchCode; // 运输网点-起始网点
                        this.form.transportEndBranchCode = res.data.receiverBranchCode; //  运输网点-到达网点编号
                        this.form.deliveryBranchCode = res.data.receiverBranchCode; //  配送网点编号
                        this.form.deliveryAreaCode = res.data.receiverAreaCode; //  配送区域编号
                        this.getCostCalculation();
                    }
                    this.quicklyPlaceOrderConfirmDisabled = false;
                })
                .catch((e) => {
                    this.quicklyPlaceOrderConfirmDisabled = false;
                });
        },
        // 订单确认取消
        confirmOrderCancel() {
            this.open = false;
            this.$emit('close');
        },
        // 保存增值服务信息
        submitFormService() {
            this.$refs['formAddedService'].validate((valid) => {
                if (valid) {
                    this.addedServiceShow = false;
                    let formServices = [];
                    this.addServiceList.forEach((val) => {
                        let obj = {};
                        obj.id = val.id;
                        obj.name = val.name;
                        obj.inputValue = val.inputValue;
                        formServices.push(obj);
                    });
                    this.form.addedServices = formServices;
                    this.getAddedServiceCalculate(this.formService.id, this.formService.inputValue, this.formService.name);
                }
            });
        },
        // 获取增值服务费用
        getAddedServiceCalculate(id, inputValue, name) {
            this.getCostCalculation();
            orderManagement
                .calculateAddedService({ id, inputValue })
                .then((res) => {
                    if (res.code == 200) {
                        let i = this.addedServiceDicts.findIndex((a) => a.id == res.data.id);
                        if (i > -1) {
                            this.addedServiceDicts[i].cost = res.data.result;
                        }
                    }
                })
                .catch((e) => { });
        },
        // 关闭增值服务弹窗
        cancelService() {
            this.addedServiceShow = false;
            let index = this.addServiceList.findIndex((a) => a.id == this.formService.id);
            if (index > -1) {
                this.addServiceList.splice(index, 1);
            }
            let i = this.addedServiceDicts.findIndex((a) => a.id == this.formService.id);
            if (i > -1) {
                this.addedServiceDicts[i].inputValue = null;
                this.addedServiceDicts[i].cost = null;
            }
            this.formService = {};
            this.$forceUpdate();
            this.getCostCalculation();
        },
        // 删除冷链货品
        removeColdChainItems(index) {
            this.$confirm('确定删除该该明细？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.coldChainProductData.splice(index, 1);
                })
                .catch(() => {
                    this.msgError('已取消删除');
                });
        },
        // 新增冷链货品
        addColdChainProducts() {
            this.coldChainProductData.push({
                name: undefined, // 通用名称
                specifications: undefined, // 规格/型号
                basicUnit: undefined, // 单位
                quantity: 1, // 数量
                registrationNumber: undefined, // 批准文号/注册证号/备案证号
                listPermitHolder: undefined, // 上市许可持有人/注册人/备案人
                manufacturer: undefined, // 生产厂家
                batchNumber: undefined, // 批号/序列号
                validityDate: undefined, // 使用期限/失效日期
                completeInformation: '1' // 资料是否齐全
            });
        },
        addServiceListCheckBox(e, index) {
            if (e == false) {
                let y = this.form.addedServices.findIndex((c) => c.id == this.addedServiceDicts[index].id);
                if (y > -1) {
                    this.form.addedServices.splice(y, 1);
                }
                this.addedServiceDicts[index].inputValue = '';
                this.addedServiceDicts[index].cost = null;
                if (this.addedServiceDicts[index].id == this.formService.id) {
                    this.formService = {};
                    this.$forceUpdate();
                }
                this.getCostCalculation();
                return false;
            }
            if (this.addedServiceDicts[index].isNeedInput == '1') {
                this.addedServiceShow = true;
                this.formService = this.addedServiceDicts[index];
                this.formService.inputValue = this.addedServiceDicts[index].defaultValue || 0;
            } else {
                let formServices = [];
                this.addServiceList.forEach((val) => {
                    let obj = {};
                    obj.id = val.id;
                    obj.name = val.name;
                    obj.inputValue = val.inputValue || '';
                    formServices.push(obj);
                });
                this.form.addedServices = formServices;
                this.getAddedServiceCalculate(this.addedServiceDicts[index].id, this.addedServiceDicts[index].inputValue, this.addedServiceDicts[index].name);
            }
        },
        addServiceListClick(index) {
            let i = this.addServiceList.findIndex((a) => a.id == this.addedServiceDicts[index].id);
            if (i > -1) {
                if (this.addedServiceDicts[index].isNeedInput == '1') {
                    this.addedServiceShow = true;
                    this.formService = this.addedServiceDicts[index];
                    this.formService.inputValue = this.addedServiceDicts[index].inputValue || this.addedServiceDicts[index].defaultValue || 0;
                }
            } else {
                this.addServiceList.push(this.addedServiceDicts[index]);
                this.addServiceListCheckBox(true, index);
            }
        },
        async getAllAvailableAddService() {
            this.addServiceList = [];
            // 如果没有货主公司或温层类型,直接返回
            if (!this.form.companyId || !this.temperatureType) {
                this.addedServiceDicts = [];
                return;
            }

            const Organization = this.$TOOL.data.get('Organization');
            const current = this.$TOOL.data.get('orgKey');
            await orderManagement
                .getOrderAddedService({
                    ownerId: this.form.companyId,
                    carrierId: Organization[current].id,
                    tempType: this.temperatureType // 添加温区类型参数
                })
                .then((response) => {
                    this.addedServiceDicts = [];
                    if (response.code == 200) {
                        this.addedServiceDicts = response.data;
                        this.addedServiceDicts.forEach((item, index) => {
                            if (this.form.addedServices.length > 0) {
                                let i = this.form.addedServices.findIndex((a) => a.id == item.id);
                                if (i > -1) {
                                    this.addedServiceDicts[index].inputValue = this.form.addedServices[i].inputValue;
                                    this.addServiceList.push(this.addedServiceDicts[index]);
                                    this.getAddedServiceCalculate(this.addedServiceDicts[index].id, this.form.addedServices[i].inputValue, this.addedServiceDicts[index].name);
                                }
                            } else {
                                if (item.isDefault === '1') {
                                    this.addedServiceDicts[index].inputValue = item.defaultValue;
                                    this.addServiceList.push(item);
                                    this.getAddedServiceCalculate(item.id, item.defaultValue, item.name);
                                }
                            }
                        });
                        if (this.addServiceList.length > 0) {
                            let formServices = [];
                            this.addServiceList.forEach((val) => {
                                let obj = {};
                                obj.id = val.id;
                                obj.name = val.name;
                                obj.inputValue = val.inputValue || '';
                                formServices.push(obj);
                            });
                            this.form.addedServices = formServices;
                        }
                    } else {
                        this.addServiceList = [];
                        this.form.addedServices = [];
                    }
                });
        },
        // 获取数据订单
        getCostCalculation() {
            if (!this.isClearingInterface) {
                return false;
            }
            let params = {
                companyId: this.form.companyId, // 客户ID
                orderType: this.form.orderType, // 订单类型
                orderClass: '2', // 订单类型2-货主订单 4-承运商订单
                collectBranchCode: this.orderBranchData.sendBranchCode, // 揽收网点编号
                collectAreaCode: this.orderBranchData.sendAreaCode, // 揽收区域编号
                transportStartBranchCode: this.orderBranchData.sendBranchCode, // 运输网点-起始网点
                transportEndBranchCode: this.orderBranchData.receiverBranchCode, //  运输网点-到达网点编号
                deliveryBranchCode: this.orderBranchData.receiverBranchCode, //  配送网点编号
                deliveryAreaCode: this.orderBranchData.receiverAreaCode, //  配送区域编号
                transType: this.form.productType, // 运输类型 1-零担 2-整车
                temperatureType: this.temperatureType, // 温区类型 是大类型
                goodsPackages: this.form.goodsPackages, // 件数
                productType: this.form.productClass, // 产品分类
                productWeight: this.form.productWeight || 0, // 重量
                productVolume: this.form.productVolume || 0, // 货物体积
                // carType: this.form.carType, // 车辆类型
                // kilometre: this.form.kilometre, // 公里数
                type: '1', // 计算方式  1-实时计算 2-计算并新增订单费用 3-计算并修改订单费用
                addedServiceList: this.addServiceList.map((item) => {
                    return {
                        id: item.id,
                        inputValue: item.inputValue
                    };
                }), // 增值服务
                receiverTownId: this.form.shippingAddress[3]
            };
            if (this.form.productType == '2') {
                params.carType = this.form.carType; // 车辆类型
                params.kilometre = this.form.kilometre; // 公里数
            } else {
                params.carType = ''; // 车辆类型
                params.kilometre = '';
            }
            let requiredField = ['collectBranchCode', 'companyId', 'temperatureType', 'goodsPackages', 'productType', 'transType'];
            if (this.checkRequiredData(params, requiredField)) {
                this.costDataList = [];
                this.form.costData = [];
                this.form.orderCost = 0.0;
                this.formula = null;
                this.quicklyPlaceOrderConfirmDisabled = true;
                orderManagement
                    .getCostCalculation(params)
                    .then((res) => {
                        if (res.code == 200) {
                            this.form.costData = res.data.costData;
                            this.form.orderCost = Number(res.data.allPrice);
                            this.formula = res.data.describe;
                            this.getCostDataList(this.form.costData);
                            // res.data.costData中costType等于5的cost如果大于0，代表存在超区费用，增加超区服务费提醒(提醒内容为：您的收件地址超出服务范围，将收取XX元超区服务费)，提醒信息点击确认后可继续操作
                            let costData = res.data.costData;
                            let costDataIndex = costData.findIndex((item) => item.costType == '5');
                            if (costDataIndex > -1 && costData[costDataIndex].cost > 0) {
                                ElMessageBox.alert('您的收件地址超出服务范围，将收取<span style="color: red;">' + costData[costDataIndex].cost + '</span>元超区服务费', '提示', {
                                    confirmButtonText: '确定',
                                    dangerouslyUseHTMLString: true,
                                    closeOnClickModal: true
                                });
                            }
                        } else {
                            // 费用计算失败时，将订单费用设置为0
                            this.form.orderCost = '0';
                            this.form.costData = [];
                            this.formula = null;
                            this.costDataList = [];
                        }
                        this.quicklyPlaceOrderConfirmDisabled = false;
                    })
                    .catch((e) => {
                        this.quicklyPlaceOrderConfirmDisabled = false;
                    });
            }
        },
        // 检验必填项
        checkRequiredData(obj, requiredField) {
            let flag = true;
            for (let key of requiredField) {
                if (obj[key] == undefined || obj[key] == '') {
                    flag = false;
                    return flag;
                }
                if ((key = 'goodsPackages' && obj[key] <= 0)) {
                    flag = false;
                    return flag;
                }
            }
            return flag;
        },
        // 封装展示数据
        getCostDataList(data) {
            this.costDataList = [];
            let basicCost = 0,
                serviceCost = 0;
            let basicCostList = [],
                serviceCostList = [];
            data.forEach((item) => {
                if (item.type == '1') {
                    basicCost = (Number(basicCost) + Number(Number(item.cost).toFixed(2))).toFixed(2);
                    basicCostList.push({ ...item, cost: Number(Number(item.cost).toFixed(2)) });
                } else {
                    serviceCost = (Number(serviceCost) + Number(Number(item.cost).toFixed(2))).toFixed(2);
                    serviceCostList.push({ ...item, cost: Number(Number(item.cost).toFixed(2)) });
                }
            });
            if (data.length > 0) {
                this.costDataList = [
                    { name: '基础费用', cost: basicCost, data: basicCostList },
                    { name: '增值服务费用', cost: serviceCost, data: serviceCostList }
                ];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-card__header {
        padding: 10px 20px 0 20px;
    }

    .el-card__body {
        padding: 10px 20px;
    }
}

.boxOrder {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    justify-items: stretch;
    align-items: stretch;

    .el-form-item {
        margin-bottom: 0px;
        align-items: flex-start;
    }
}

.orderDetails {
    .box-top {
        >div {
            :nth-child(1) {
                color: #999999;
            }

            :nth-child(2) {
                color: #666666;
            }
        }
    }

    .box {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto;
        justify-items: stretch;
        align-items: stretch;
    }

    .box-info .el-form-item--small.el-form-item {
        margin-bottom: 10px;
    }
}

.orderDetails {
    .box-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-left: 10px;
    }

    .heading {
        color: #999999;
    }

    .heading-value {
        color: #707070;
    }

    .el-card__body {
        padding-bottom: 15px;
    }

    .el-form-item {
        border-bottom: 10px !important;
    }
}

.box-footer {
    padding-left: 8px;
    display: flex;
    justify-content: space-between;

    .el-form-item__content {
        margin-left: 0 !important;
    }
}

.box-valueAddedServiceButton {
    display: flex;
    flex-wrap: wrap;
    gap: 0 24px;
}
</style>
