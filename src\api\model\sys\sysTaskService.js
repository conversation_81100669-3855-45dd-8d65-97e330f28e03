import http from "@/utils/request"

/*
 *@description: 系统任务
 *@author: 路正宁
 *@date: 2023-03-17 12:03:01
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/task/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/task/delete',
       {ids: ids}
    )
  },

  queryById: function (id) {
    return http.get(
       '/task/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/task/list',
       params
    )
  },

  exportTemplate: function () {
    return http.get(
       '/task/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/task/export',
       params,
       'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
       '/task/import',
       data
    )
  }
}
