import request from '@/utils/request';
export default {
    // 列表
    paymentList: function (params) {
        return request.get('/cost/payment/list', params);
    },
    // 导出
    paymentExport: function (params, config, resDetail, responseType) {
        return request.get('/cost/payment/export', params, config, resDetail, responseType);
    },
    // 账单明细
    statementDetail: function (params) {
        return request.get('/cost/payment/detail', params);
    },
    // 批量核销
    carrierCheck: function (params) {
        return request.post('/cost/payment/verificat', params);
    },
    //批量撤销
    revokePaymentDoc: function (params) {
        return request.post('/cost/payment/revocation', params);
    },
	// 货主列表
	getCustomerList: function (params) {
		return request.post('/cost/payment/queryCompanySelect', params);
	},
	// 坏账登记
	badDebtRegister: function (params) {
		return request.post('/cost/payment/badDebtRegist', params);
	},
	// 坏账记录
	getBadDebtRegistrationList: function (params) {
		return request.get('/cost/payment/badDept/list', params);
	},
	// (到付)根据公司名称查询开票信息
	getInvoiceCompany: function (params) {
		return request.get('/tms/payment/invoiceApply/getInvoiceCompany', params);
	},
    // 支付申请-前置校验
    paymentApplyPreCheck: function (params) {
        return request.get('/tms/advancepayment/apply/payApply/check', params);
    },
    // 账单明细-仓储服务费
    getStorageBillDetails: function (paymentOrderNo) {
        return request.get(`/cost/payment/getWmsServiceFee/${paymentOrderNo}`);
    },
    // 账单明细-仓储年费
    getStorageYearFee: function (paymentOrderNo) {
        return request.get(`/cost/payment/getWmsYearFee/${paymentOrderNo}`);
    },
    // 账单明细-仓储保费
    getStorageInsuranceFee: function (paymentOrderNo) {
        return request.get(`/cost/payment/getWmsPremiumFee/${paymentOrderNo}`);
    },
    // 费用统计
    getCostStatistics: function (params) {
        return request.get('/cost/payment/cost/stats', params);
    }
};
