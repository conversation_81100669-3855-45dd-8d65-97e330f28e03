<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" @submit.prevent>
                <el-form-item label="增值服务" prop="addServiceId">
                    <el-select v-model="queryParams.addServiceId" clearable filterable placeholder="请选择" @change="handleQuery">
                        <el-option v-for="item in addServiceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="温区类型" prop="tempType">
                    <el-select v-model="queryParams.tempType" clearable filterable placeholder="请选择温区类型" @change="handleQuery">
                        <el-option v-for="item in tempTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="编号" prop="no">
                    <el-input v-model="queryParams.no" clearable placeholder="请输入编号"></el-input>
                </el-form-item>
                <el-form-item class="last-form-item">
                    <el-button :loading="loading" icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
                    <el-button :loading="loading" icon="el-icon-refresh" type="info" @click="resetQuery('queryForm')">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button type="primary" @click="openValueAddedService('add')">新增</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :loading="loading" table-i-d="valueAddedServiceCarrierTable" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="orderList" :max-height="800" :showIndex="true">
                <template #tempType="{ row }">
                    {{ selectDictLabel(tempTypeList, row.tempType) }}
                </template>
                <template #status="{ row }">
                    <el-switch
                        v-if="row.releaseStatus !== '0' && row.releaseStatus !== '1' && row.releaseStatus !== '4'"
                        v-model="row.status"
                        active-color="#13ce66"
                        active-text="启用"
                        active-value="0"
                        inactive-text="禁用"
                        inactive-value="1"
                        inline-prompt
                        size="small"
                        @change="handleSwitchChange(row)"
                    />
                    <span v-else></span>
                </template>
                <template #publishStatus="{ row }">
                    <div v-html="formatPublishStatus(row.publishStatus)"></div>
                </template>
                <template #remark="{ row }">
                    <el-popover v-if="row.remark" placement="top" width="400">
                        <pre>{{ row.remark }}</pre>
                        <template #reference>
                            <el-link icon="el-icon-info-filled" link size="small" type="primary">查看</el-link>
                        </template>
                    </el-popover>
                </template>
                <template #defaultFormula="{ row }">
                    <span>{{ formatDefaultFormula(row) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="row.publishStatus === '1'" icon="el-icon-plus" link size="small" type="primary" @click="handleAddShippers(row)">添加货主</el-button>
                    <el-button v-if="row.publishStatus === '1'" icon="el-icon-view" link size="small" type="primary" @click="handleViewShippers(row)">查看货主</el-button>
                    <el-button v-if="row.publishStatus === '0'" icon="el-icon-check" link size="small" type="success" @click="handleClickBtnPublishTheFormula(row)">发布</el-button>
                    <el-button v-if="row.publishStatus === '1'" icon="el-icon-close" link size="small" type="danger" @click="handleClickBtnWriteOffFormula(row)">注销</el-button>
                    <el-button v-if="row.publishStatus === '0'" icon="el-icon-edit" link size="small" type="warning" @click="openValueAddedService('edit', row)">修改</el-button>
                    <!-- 已发布的修改 -->
                    <el-button v-if="row.publishStatus === '1'" icon="el-icon-edit" link size="small" type="warning" @click="openValueAddedService('edit', row)">修改已发布</el-button>
                    <el-button v-if="row.publishStatus !== '1'" icon="el-icon-delete" link size="small" type="danger" @click="submitDelete(row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- /新增增值服务 抽屉  -->
        <el-drawer v-if="newValueAddedServicesVisible" v-model="newValueAddedServicesVisible" :title="newValueAddedServicesTitle" size="880px" @close="hideNewValueAddedServices">
            <div v-loading="newValueAddedServicesLoading" :element-loading-text="newValueAddedServicesLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="billingFormulaForm" :model="billingFormulaForm" :rules="billingFormulaFormRules" class="billingFormulaForm" label-width="auto">
                        <div style="display: flex; gap: 10px;">
                            <el-form-item label="增值服务" prop="addServiceIdTemp" style="flex: 1;">
                                <el-select
                                    v-model="billingFormulaForm.addServiceIdTemp"
                                    :disabled="isDisabled"
                                    clearable
                                    filterable
                                    placeholder="请选择增值服务"
                                    style="width: 100%;"
                                    value-key="id"
                                    @change="handleAddServiceIdChange"
                                >
                                    <el-option v-for="item in addServiceList" :key="item.id" :label="item.name" :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="温区类型" prop="tempType" style="flex: 1;">
                                <el-select
                                    v-model="billingFormulaForm.tempType"
                                    :disabled="isDisabled"
                                    clearable
                                    filterable
                                    placeholder="请选择温区类型"
                                    style="width: 100%;"
                                >
                                    <el-option v-for="item in tempTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <el-form-item label="单项价格" prop="price">
                            <el-input v-model="billingFormulaForm.price" :disabled="isDisabled" oninput="value=value.replace(/^\.+|[^\d.]/g,'')" placeholder="请输入单项价格"></el-input>
                        </el-form-item>
                        <el-form-item v-if="billingFormulaForm.isNeedInput == 1" label="输入单位" prop="unit">
                            <el-select v-model="billingFormulaForm.unit" :disabled="isDisabled" clearable placeholder="请选择单位">
                                <el-option v-for="item in unitList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="最低费用" prop="minCost">
                            <el-input v-model="billingFormulaForm.minCost" :disabled="isDisabled" oninput="value=value.replace(/^\.+|[^\d.]/g,'')" placeholder="请输入最低费用"></el-input>
                        </el-form-item>
                        <el-form-item label="是否默认服务" prop="isDefault">
                            <el-switch v-model="billingFormulaForm.isDefault" :disabled="isDisabled" active-text="是" active-value="1" inactive-text="否" inactive-value="0" @change="isDefaultChange" />
                        </el-form-item>
                        <el-form-item v-if="billingFormulaForm.isDefault == '1'" label="默认值" prop="defaultValue">
                            <el-input v-model="billingFormulaForm.defaultValue" :disabled="isDisabled" oninput="value=value.replace(/^\.+|[^\d.]/g,'')" placeholder="请输入默认值"></el-input>
                        </el-form-item>
                        <el-form-item label="公式描述" prop="remark">
                            <pre>{{ billingFormulaForm.remark }}</pre>
                        </el-form-item>
                        <el-form-item label="参数选择">
                            <div style="display: flex">
                                <el-button v-if="parameter && billingFormulaForm.isNeedInput == 1" @click="setParameterSelection('self', 'text', parameter)">{{ parameter }}</el-button>
                                <el-button @click="setParameterSelection('price', 'text', '单项价格')">单项价格</el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="运算符号">
                            <div class="box__grid__parameter">
                                <el-button v-for="item in operatorSymbolList" :key="item.code" @click="setParameterSelection(item.code, '', item.code)">{{ item.name }}</el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="运算数字">
                            <div class="box__numberButton">
                                <el-button v-for="(item, index) in 10" :key="index" @click="setParameterSelection(index, '', index)">{{ index }}</el-button>
                                <el-button @click="setParameterSelection('.', '', '.')">.</el-button>
                            </div>
                        </el-form-item>
                        <el-radio-group v-model="billingFormulaForm.index" class="billingFormulaForm__radio" @change="handleIndexChange">
                            <el-radio :disabled="isDisabled" border label="-1">
                                <el-form-item class="mb0" label="默认公式" style="padding: 15px">
                                    <div class="tag__box">
                                        <div class="tag__m">
                                            <span v-for="(tag, index) in billingFormulaForm.defaultFormulaList" :key="index">
                                                {{ tag.text }}
                                            </span>
                                            <span v-if="billingFormulaForm.defaultFormulaList && billingFormulaForm.defaultFormulaList.length > 0 && !isDisabled" class="btn__revoke" @click="revokeFormula('defaultFormulaList')">撤销</span>
                                        </div>
                                        <el-link v-if="!isDisabled" @click="resetTheFormulaOrCondition('defaultFormulaList')">重置</el-link>
                                    </div>
                                </el-form-item>
                            </el-radio>
                            <el-radio v-for="(item, index) in billingFormulaForm.addedServiceFormulaList" :key="index" :label="index" border class="list">
                                <div class="list-item" draggable="true" @dragenter="dragenter($event, index)" @dragover="dragover($event, index)" @dragstart="dragstart(index)">
                                    <div class="list__item__sort"></div>
                                    <div class="list__item__serialNumber">{{ index + 1 }}</div>
                                    <el-switch v-model="item.status" active-color="#13ce66" active-text="启用" active-value="0" inactive-text="禁用" inactive-value="1" inline-prompt @change="item.status == '1' ? (item.status = '1') : (item.status = '0')"></el-switch>
                                    <el-radio-group v-model="item.conditionType" class="billingFormulaForm__radio__condition" @change="(e) => changeConditionType(e, index)">
                                        <el-radio border label="1">
                                            <el-form-item class="mb0" label="计算条件">
                                                <div class="tag__box">
                                                    <div class="tag__m">
                                                        <span v-for="(tag, index) in item.conditionsList" :key="index">
                                                            {{ tag.text }}
                                                        </span>
                                                        <span v-if="(item.conditionsList && item.conditionsList.length > 0 && !item.id) || !isDisabled" class="btn__revoke" @click="revokeFormula('conditionsList', index)">撤销</span>
                                                    </div>
                                                    <el-link v-if="!item.id || !isDisabled" @click="resetTheFormulaOrCondition('conditionsList', index)">重置</el-link>
                                                </div>
                                            </el-form-item>
                                        </el-radio>
                                        <el-radio border label="2">
                                            <el-form-item class="mb0" label="计算公式">
                                                <div class="tag__box">
                                                    <div class="tag__m">
                                                        <span v-for="(tag, index) in item.formulaList" :key="index">
                                                            {{ tag.text }}
                                                        </span>
                                                        <span v-if="(item.formulaList && item.formulaList.length > 0 && !item.id) || !isDisabled" class="btn__revoke" @click="revokeFormula('formulaList', index)">撤销</span>
                                                    </div>
                                                    <el-link v-if="!item.id || !isDisabled" @click="resetTheFormulaOrCondition('formulaList', index)">重置</el-link>
                                                </div>
                                            </el-form-item>
                                        </el-radio>
                                    </el-radio-group>
                                </div>
                                <div style="padding-right: 15px">
                                    <el-link @click="deleteCondition(index)">删除</el-link>
                                </div>
                            </el-radio>
                        </el-radio-group>
                        <div class="drawer__add">
                            <div class="add__tip"><span>调整位置：鼠标变为</span><i class="el-icon-rank"></i><span>时上下拖拽</span></div>
                            <el-button icon="el-icon-plus" type="primary" @click="addCondition">新增</el-button>
                        </div>
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideNewValueAddedServices">取消</el-button>
                    <el-button v-if="newValueAddedServicesModify && !isDisabled" type="primary" @click="handleClickAddedModifications('edit')">修改</el-button>
                    <el-button v-else-if="newValueAddedServicesModify && isDisabled" type="primary" @click="handleClickAddedModifications('publish')">保存</el-button>
                    <el-button v-else type="primary" @click="handleClickAddedModifications('add')">确定</el-button>
                </div>
            </div>
        </el-drawer>

        <!-- 发布对象选择弹窗 -->
        <el-dialog v-model="publishDialogVisible" :close-on-click-modal="false" :title="dialogTitle" width="850px" @close="handlePublishDialogClose">
            <div v-loading="dialogLoading">
                <el-transfer
                    v-model="selectedPublishTargets"
                    :data="publishTargetsList"
                    :props="{
                        key: 'companyId',
                        label: 'companyName',
                        disabled: 'disabled'
                    }"
                    :titles="['待选择', '已选择']"
                    filter-placeholder="请输入关键词搜索"
                    filterable
                >
                    <template v-if="isAddShipper && existingShipperIds.length > 0" #right-footer>
                        <div class="transfer-footer">
                            <span class="text-muted">已有货主不可移除，只能添加新货主</span>
                        </div>
                    </template>
                </el-transfer>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="publishDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleConfirmPublish">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import valueAddedServices from '@/api/carrierEnd/valueAddedServices';
import { selectDictLabel } from '@/utils/dictLabel';
import handoverOrderConfiguration from '@/api/carrierEnd/handoverOrderConfiguration';

export default {
    name: 'ValueAddedServices',
    components: {
        RightToolbar,
        ColumnTable
    },
    data() {
        return {
            showSearch: true,
            queryParams: {
                current: 1,
                size: 10,
                addServiceId: undefined,
                tempType: undefined,
                no: undefined
            },
            columns: [
                { title: '编号', key: 'no', align: 'center', width: '160px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '服务名称', key: 'addServiceName', align: 'center', width: '200px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '温区类型', key: 'tempType', align: 'center', width: '100px', columnShow: true },
                { title: '单项价格', key: 'price', align: 'center', width: '100px', columnShow: true },
                { title: '最低费用', key: 'minCost', align: 'center', width: '100px', columnShow: true },
                { title: '公式状态', key: 'status', align: 'center', width: '100px', columnShow: true },
                { title: '默认公式', key: 'defaultFormula', minWidth: '280px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '公式说明', key: 'remark', minWidth: '140px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发布状态', key: 'publishStatus', minWidth: '120px', align: 'center', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '360px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            loading: false,
            orderList: [],
            total: 0,
            newValueAddedServicesVisible: false,
            newValueAddedServicesTitle: '新增增值服务',
            newValueAddedServicesLoading: false,
            newValueAddedServicesLoadingText: '新增中...',
            billingFormulaForm: {
                addServiceId: '',
                addServiceIdTemp: null,
                goodsType: '',
                isNeedInput: '',
                price: null,
                unit: '',
                minCost: '',
                remark: '',
                index: '-1',
                defaultFormulaList: [],
                addedServiceFormulaList: [],
                isDefault: '0', // 是否默认 0-否 1-是
                defaultValue: null, // 默认值
                tempType: '' // 温区类型
            },
            billingFormulaFormRules: {
                addServiceIdTemp: [{
                    required: true,
                    message: '请选择增值服务',
                    trigger: 'change',
                    validator: (rule, value, callback) => {
                        if (!value || !value.id) {
                            callback(new Error('请选择增值服务'));
                        } else {
                            callback();
                        }
                    }
                }],
                goodsType: [{ required: true, message: '请选择货物类型', trigger: 'blur' }],
                price: [
                    { required: true, message: '请输入单项价格', trigger: 'blur' },
                    /*单项价格不能为空*/
                    {
                        validator: (rule, value, callback) => {
                            if (value === 0) {
                                callback(new Error('单项价格不能为0'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change'
                    }
                ],
                minCost: [
                    { required: true, message: '请输入最低费用', trigger: 'blur' }
                    // {
                    //     validator: (rule, value, callback) => {
                    //         if (value === 0) {
                    //             callback(new Error('最低费用不能为0'));
                    //         } else {
                    //             callback();
                    //         }
                    //     },
                    //     trigger: 'change'
                    // }
                ],
                index: [{ required: true, message: '请输入排序', trigger: 'blur' }],
                formulaList: [{ required: true, message: '请选择计算公式', trigger: 'blur' }],
                isDefault: [{ required: true, message: '请选择是否默认服务', trigger: 'blur' }],
                defaultValue: [{ required: true, message: '请输入默认值', trigger: 'blur' }],
                tempType: [{ required: true, message: '请选择温区类型', trigger: 'change' }]
            },
            newValueAddedServicesModify: false,
            unitList: [],
            tempTypeList: [],
            addServiceList: [],
            parameter: null,
            operatorSymbolList: [],
            // 临时id 用于 修改时 新增的计算公式
            TempId: null,
            // 源对象的下标
            dragIndex: '',
            // 目标对象的下标
            enterIndex: '',
            timeout: null,
            publishTheStatusOfList: [],
            isDisabled: false,
            // 发布对象相关数据
            publishDialogVisible: false,
            selectedPublishTargets: [],
            publishTargetsList: [],
            currentPublishRow: null, // 当前要发布的行数据
            dialogTitle: '选择发布对象', // 弹窗标题
            isAddShipper: false, // 是否是添加货主操作
            dialogLoading: false, // 弹窗加载状态
            existingShipperIds: [], // 已存在的货主ID列表
        };
    },
    computed: {
        // 格式化默认公式
        formatDefaultFormula() {
            return (data) => {
                const { addServiceId, defaultFormula } = data;
                let parameter = '';
                if (addServiceId && this.addServiceList.length > 0) {
                    // addServiceList 中 取出 id 为 e 的对象的 parameter 如果不存在 parameter 对象 则提示
                    parameter = this.addServiceList.find((item) => item.id == addServiceId)?.parameter;
                }
                if (defaultFormula) {
                    const defaultFormulaList = [];
                    const defaultFormulaArr = defaultFormula.match(/([0-9]+(\.[0-9]+)?|\+|-|\*|\/|\(|\)|&|\||=|!|>|<)|([a-zA-Z]+)/g);
                    if (defaultFormulaArr) {
                        defaultFormulaArr.forEach((item) => {
                            if (item) {
                                if (item.match(/(\d+\.?\d*)/)) {
                                    defaultFormulaList.push({ type: '', value: item, text: item });
                                } else {
                                    if (item === 'price') {
                                        defaultFormulaList.push({ type: 'text', value: item, text: '单项价格' });
                                    } else if (item === 'self') {
                                        defaultFormulaList.push({ type: 'text', value: item, text: parameter || '' });
                                    } else {
                                        defaultFormulaList.push({ type: 'text', value: item, text: item });
                                    }
                                }
                            }
                        });
                    }
                    // defaultFormulaList text 拼接为字符串
                    return defaultFormulaList.map((item) => item.text).join('');
                }
            };
        },
        formatPublishStatus() {
            return (value) => {
                const publishStatusText = selectDictLabel(this.publishTheStatusOfList, value);
                if (value === '0') {
                    return `<span style="color: #F4AC00">${publishStatusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #1ACD7E">${publishStatusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #B1B1B1">${publishStatusText}</span>`;
                } else {
                    return `<span style="color: #B1B1B1">${publishStatusText}</span>`;
                }
            };
        }
    },
    watch: {
        'billingFormulaForm.addedServiceFormulaList': {
            handler() {
                this.setFormulaDescription();
            },
            deep: true
        },
        'billingFormulaForm.defaultFormulaList': {
            handler() {
                this.setFormulaDescription();
            },
            deep: true
        }
    },
    created() {
        this.getDict();
        this.handleQuery();
        this.getValueAddedServiceList();
    },
    methods: {
        addCondition() {
            // this.billingFormulaForm.addedServiceFormulaList 新增 一条计算公式
            this.billingFormulaForm.addedServiceFormulaList.splice(this.billingFormulaForm.addedServiceFormulaList.length + 1, 0, {
                conditions: '',
                conditionsList: [],
                formula: '',
                formulaList: [],
                conditionType: '1',
                status: '0'
            });
            this.billingFormulaForm.index = this.billingFormulaForm.addedServiceFormulaList.length - 1;
            this.billingFormulaForm.addedServiceFormulaList.forEach((item, i) => {
                if (i != this.billingFormulaForm.index) {
                    item.conditionType = '';
                }
            });
        },
        changeConditionType(e, index) {
            this.billingFormulaForm.index = index;
            this.billingFormulaForm.addedServiceFormulaList[index].conditionType = e;
            // 设置 不是 this.billingFormulaForm.addedServiceFormulaList[index].conditionType 为空
            this.billingFormulaForm.addedServiceFormulaList.forEach((item, i) => {
                if (i != index) {
                    item.conditionType = '';
                }
            });
            // 更新视图
            this.$forceUpdate();
        },
        deleteCondition(index) {
            // 判断 this.billingFormulaForm.addedServiceFormulaList[index]
            const { id } = this.billingFormulaForm.addedServiceFormulaList[index];
            // if id不存在 或者 this.isDisabled 为false
            if (!id || !this.isDisabled) {
                this.billingFormulaForm.addedServiceFormulaList.splice(index, 1);
            } else {
                this.$message.warning('该条计算公式已经保存，不能删除');
            }
        },
        dragenter(e, index) {
            e.preventDefault();
            this.enterIndex = index;
            if (this.timeout !== null) {
                clearTimeout(this.timeout);
            }
            // 拖拽事件的防抖
            this.timeout = setTimeout(() => {
                if (this.dragIndex !== index) {
                    const source = this.billingFormulaForm.addedServiceFormulaList[this.dragIndex];
                    this.billingFormulaForm.addedServiceFormulaList.splice(this.dragIndex, 1);
                    this.billingFormulaForm.addedServiceFormulaList.splice(index, 0, source);
                    // 排序变化后目标对象的索引变成源对象的索引
                    this.dragIndex = index;
                    //选中框设置
                    this.billingFormulaForm.index = index;
                    this.billingFormulaForm.addedServiceFormulaList.forEach((item, i) => {
                        if (i != this.billingFormulaForm.index) {
                            item.conditionType = '';
                        }
                    });
                    this.billingFormulaForm.addedServiceFormulaList[this.billingFormulaForm.index].conditionType = '1';
                }
            }, 100);
        },
        dragover(e) {
            e.preventDefault();
        },
        dragstart(index) {
            this.dragIndex = index;
        },
        getDetail(id) {
            valueAddedServices
                .addedServiceDetailCarrier({ id })
                .then((res) => {
                    if (res.code === 200) {
                        const { addServiceId, addedServiceFormulaList, defaultFormula, publishStatus } = res.data;
                        // 将 tempType 转为字符串
                        if (res.data.tempType !== null && res.data.tempType !== undefined) {
                            res.data.tempType = res.data.tempType.toString();
                        }

                        // addServiceList 中 取出 id 为 e 的对象的 parameter 如果不存在 parameter 对象 则提示
                        this.parameter = this.addServiceList.find((item) => item.id === addServiceId)?.parameter;
                        res.data.formulaList = {};

                        if (defaultFormula) {
                            const defaultFormulaList = [];
                            const defaultFormulaArr = defaultFormula.match(/([0-9]+(\.[0-9]+)?|\+|-|\*|\/|\(|\)|&|\||=|!|>|<)|([a-zA-Z]+)/g);
                            if (defaultFormulaArr) {
                                defaultFormulaArr.forEach((item) => {
                                    if (item) {
                                        if (item.match(/(\d+\.?\d*)/)) {
                                            defaultFormulaList.push({ type: '', value: item, text: item });
                                        } else {
                                            if (item === 'price') {
                                                defaultFormulaList.push({ type: 'text', value: item, text: '单项价格' });
                                            } else if (item === 'self') {
                                                defaultFormulaList.push({ type: 'text', value: item, text: this.parameter || '' });
                                            } else {
                                                defaultFormulaList.push({ type: 'text', value: item, text: item });
                                            }
                                        }
                                    }
                                });
                            }
                            res.data.defaultFormulaList = defaultFormulaList;
                        }

                        if (addedServiceFormulaList) {
                            addedServiceFormulaList.forEach((item, index) => {
                                const { formula, conditions } = item;
                                const formulaList = [];
                                const conditionsList = [];
                                const formulaArr = formula.match(/([0-9]+(\.[0-9]+)?|\+|-|\*|\/|\(|\)|&|\||=|!|>|<)|([a-zA-Z]+)/g);
                                const conditionsArr = conditions.match(/([0-9]+(\.[0-9]+)?|\+|-|\*|\/|\(|\)|&|\||=|!|>|<)|([a-zA-Z]+)/g);
                                if (formulaArr) {
                                    formulaArr.forEach((item) => {
                                        if (item) {
                                            if (item.match(/(\d+\.?\d*)/)) {
                                                formulaList.push({ type: '', value: item, text: item });
                                            } else {
                                                if (item === 'price') {
                                                    formulaList.push({ type: 'text', value: item, text: '单项价格' });
                                                } else if (item === 'self') {
                                                    formulaList.push({ type: 'text', value: item, text: this.parameter || '' });
                                                } else {
                                                    formulaList.push({ type: 'text', value: item, text: item });
                                                }
                                            }
                                        }
                                    });
                                }
                                if (conditionsArr) {
                                    conditionsArr.forEach((item) => {
                                        if (item) {
                                            if (item.match(/(\d+\.?\d*)/)) {
                                                conditionsList.push({ type: '', value: item, text: item });
                                            } else {
                                                if (item === 'price') {
                                                    conditionsList.push({ type: 'text', value: item, text: '单项价格' });
                                                } else if (item === 'self') {
                                                    conditionsList.push({ type: 'text', value: item, text: this.parameter || '' });
                                                } else {
                                                    conditionsList.push({ type: 'text', value: item, text: item });
                                                }
                                            }
                                        }
                                    });
                                }
                                // res.data.addedServiceFormulaList[index] 新增 formulaList conditionsList
                                res.data.addedServiceFormulaList[index].formulaList = formulaList;
                                res.data.addedServiceFormulaList[index].conditionsList = conditionsList;
                            });
                        }

                        const isNeedInput = this.addServiceList.find((item) => item.id == addServiceId)?.isNeedInput;
                        const name = this.addServiceList.find((item) => item.id === addServiceId)?.name;
                        this.billingFormulaForm = { ...this.billingFormulaForm, ...res.data, addServiceIdTemp: { id: addServiceId, name }, isNeedInput };

                        // publishStatus 表示已发布
                        publishStatus == '1' ? (this.isDisabled = true) : (this.isDisabled = false);

                        // 选中默认公式
                        if (publishStatus == '1') {
                            this.billingFormulaForm.index = '0' * 1;
                            // 如果 this.billingFormulaForm.addedServiceFormulaList 为空
                            if (this.billingFormulaForm.addedServiceFormulaList.length > 0) {
                                this.billingFormulaForm.addedServiceFormulaList[this.billingFormulaForm.index].conditionType = '1';
                            } else {
                                this.billingFormulaForm.index = '-1';
                            }
                        } else {
                            this.billingFormulaForm.index = '-1';
                        }
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.billingFormulaLoading = false;
                });
        },
        /**
         * 获取字典值
         * @returns {Promise<void>}
         */
        async getDict() {
            this.tempTypeList = await this.getDictList('fourpl_temperature_type');
            this.unitList = await this.getDictList('fourpl_added_service_unit');
            this.operatorSymbolList = await this.getDictList('cost_operational_symbol');
            this.publishTheStatusOfList = await this.getDictList('cost_price_this_release_status');
        },
        // 获取已有货主列表
        async getExistingShippers(id) {
            this.dialogLoading = true;
            try {
                const res = await valueAddedServices.queryShippersByAddedService(id);
                if (res.code === 200 && res.data) {
                    // 保存已有货主ID列表 - 直接使用返回的数组
                    this.existingShipperIds = res.data;
                    // 将已有货主添加到已选择列表
                    this.selectedPublishTargets = [...this.existingShipperIds];
                }
            } catch (error) {
                console.error('获取已有货主列表失败:', error);
                this.$message.error('获取已有货主列表失败');
            } finally {
                this.dialogLoading = false;
            }
        },
        getList() {
            this.loading = true;
            valueAddedServices
                .addedServiceListCarrier(this.queryParams)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.orderList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        // 获取货主列表
        async getPublishTargets() {
            this.dialogLoading = true;
            try {
                const res = await handoverOrderConfiguration.cooperateSelect({ status: 1 });
                if (res.code === 200) {
                    this.publishTargetsList = res.data || [];

                    // 如果是添加货主操作，需要禁用已有货主的移除功能
                    if (this.isAddShipper && this.existingShipperIds.length > 0) {
                        // 创建一个新的数组，避免修改原始数据
                        this.publishTargetsList = this.publishTargetsList.map(item => {
                            // 如果是已有货主，标记为不可移除
                            if (this.existingShipperIds.includes(item.companyId)) {
                                return { ...item, disabled: true };
                            }
                            return item;
                        });
                    }
                }
            } catch (error) {
                console.error('获取货主列表失败:', error);
                this.$message.error('获取货主列表失败');
            } finally {
                this.dialogLoading = false;
            }
        },
        getValueAddedServiceList() {
            valueAddedServices
                .addedServiceAllList()
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.addServiceList = res.data || [];
                    }
                })
                .catch(() => {});
        },
        handleAddServiceIdChange(e) {
            const { id, unit, isNeedInput } = e;
            this.billingFormulaForm.addServiceId = id;
            this.billingFormulaForm.unit = unit;
            this.billingFormulaForm.isNeedInput = isNeedInput;
            // addServiceList 中 取出 id 为 e 的对象 的 parameter
            if (id) {
                this.parameter = this.addServiceList.find((item) => item.id === id).parameter;
            }
        },
        // 点击添加货主按钮
        async handleAddShippers(row) {
            this.currentPublishRow = row;
            this.dialogTitle = '添加货主';
            this.isAddShipper = true;
            // 获取已有货主列表
            await this.getExistingShippers(row.id);
            // 获取货主列表
            await this.getPublishTargets();
            this.publishDialogVisible = true;
        },
        // 确定新增增值服务
        handleClickAddedModifications(type) {
            // billingFormulaForm.defaultFormulaList 不能为空
            if (this.billingFormulaForm.defaultFormulaList.length === 0) {
                this.$message.error('请添加默认公式');
                return;
            }
            // this.billingFormulaForm 的 price 和 minCost转为数值类型
            this.billingFormulaForm.price = Number(this.billingFormulaForm.price);
            this.billingFormulaForm.minCost = Number(this.billingFormulaForm.minCost);
            if (this.billingFormulaForm.addedServiceFormulaList.length > 0) {
                let flag = false;
                this.billingFormulaForm.addedServiceFormulaList.forEach((item) => {
                    if (item.conditionsList.length === 0 || item.formulaList.length === 0) {
                        flag = true;
                    }
                });
                if (flag) {
                    this.$message.error('条件和公式不能为空');
                    return false;
                }
            }
            this.$refs.billingFormulaForm.validate((valid) => {
                if (valid) {
                    this.newValueAddedServicesLoading = true;
                    if (type === 'add') {
                        this.newValueAddedServicesLoadingText = '新增中...';
                    } else if (type === 'edit' || type === 'publish') {
                        this.newValueAddedServicesLoadingText = '修改中...';
                    }

                    // 对默认公式进行处理
                    let defaultFormula = '';
                    this.billingFormulaForm.defaultFormulaList.forEach((item) => {
                        defaultFormula += item.value;
                    });

                    // 对新增公式组 处理
                    this.billingFormulaForm.addedServiceFormulaList.forEach((item) => {
                        item.formula = '';
                        item.conditions = '';
                        item.formulaList.forEach((item2) => {
                            item.formula += item2.value;
                        });
                        item.conditionsList.forEach((item2) => {
                            item.conditions += item2.value;
                        });
                        if (type === 'edit') {
                            // 修改时 新增公式组的 id 为空时，需要添加 addedServicesId
                            if (!item.id) {
                                item.addedServicesId = this.TempId;
                            }
                        }
                        item.sort = this.billingFormulaForm.addedServiceFormulaList.indexOf(item);
                    });

                    // 新增公式组
                    if (type === 'add') {
                        valueAddedServices
                            .addAddedServiceCarrier({ ...this.billingFormulaForm, defaultFormula })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('新增成功');
                                    this.hideNewValueAddedServices();
                                    this.getList();
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .catch(() => {})
                            .finally(() => {
                                this.newValueAddedServicesLoading = false;
                            });
                    }
                    if (type === 'edit') {
                        valueAddedServices
                            .updateAddedServiceCarrier({ ...this.billingFormulaForm, defaultFormula })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('修改成功');
                                    this.hideNewValueAddedServices();
                                    this.getList();
                                    // 修改完成后，清空 TempId
                                    this.TempId = '';
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .finally(() => {
                                this.newValueAddedServicesLoading = false;
                            });
                    }
                    if (type === 'publish') {
                        valueAddedServices
                            .editAddedServiceFormula({ ...this.billingFormulaForm })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('修改成功');
                                    this.hideNewValueAddedServices();
                                    this.getList();
                                    // 修改完成后，清空 TempId
                                    this.TempId = '';
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .catch(() => {})
                            .finally(() => {
                                this.newValueAddedServicesLoading = false;
                            });
                    }
                }
            });
        },
        // 点击发布按钮
        async handleClickBtnPublishTheFormula(row) {
            this.currentPublishRow = row;
            this.dialogTitle = '选择发布对象';
            this.isAddShipper = false;
            // 获取货主列表
            await this.getPublishTargets();
            this.publishDialogVisible = true;
        },
        // 注销公式
        handleClickBtnWriteOffFormula(row) {
            const { id, addServiceName } = row;
            if (id) {
                this.$confirm('是否注销公式：' + addServiceName, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        this.loading = true;
                        valueAddedServices
                            .logoutAddedService({ id })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功注销公式！');
                                    this.getList();
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .catch(() => {})
                            .finally(() => {
                                this.loading = false;
                            });
                    })
                    .catch(() => {});
            } else {
                this.$message.error('公式不存在！');
            }
        },
        // 确定修改增值服务
        handleClickEdit() {
            this.$refs.billingFormulaForm.validate((valid) => {
                if (valid) {
                    // billingFormulaForm.defaultFormulaList 不能为空
                    if (this.billingFormulaForm.defaultFormulaList.length === 0) {
                        this.$message.error('请添加默认公式');
                        return;
                    }
                    if (this.billingFormulaForm.addedServiceFormulaList && this.billingFormulaForm.addedServiceFormulaList.length > 0) {
                        let flag = false;
                        this.billingFormulaForm.addedServiceFormulaList.forEach((item) => {
                            if (item.conditionsList.length === 0 || item.formulaList.length === 0) {
                                flag = true;
                            }
                        });
                        if (flag) {
                            this.$message.error('条件和公式不能为空');
                            return false;
                        }
                    }

                    this.newValueAddedServicesLoading = true;
                    this.newValueAddedServicesLoadingText = '修改中...';

                    // 对默认公式进行处理
                    let defaultFormula = '';
                    this.billingFormulaForm.defaultFormulaList.forEach((item) => {
                        if (item.type === 'text') {
                            defaultFormula += item.value;
                        } else {
                            defaultFormula += item.value;
                        }
                    });

                    // 对新增公式组 处理
                    this.billingFormulaForm.addedServiceFormulaList.forEach((item) => {
                        // 先清空 formula 和 conditions
                        item.formula = '';
                        item.conditions = '';
                        item.formulaList.forEach((item2) => {
                            if (item2.type === 'text') {
                                item.formula += item2.value;
                            } else {
                                item.formula += item2.value;
                            }
                        });
                        item.conditionsList.forEach((item2) => {
                            if (item2.type === 'text') {
                                item.conditions += item2.value;
                            } else {
                                item.conditions += item2.value;
                            }
                        });
                        // 修改时 新增公式组的 id 为空时，需要添加 addedServicesId
                        if (!item.id) {
                            item.addedServicesId = this.TempId;
                        }
                    });

                    valueAddedServices
                        .addAddedServiceCarrier({ ...this.billingFormulaForm, defaultFormula })
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('修改增值服务成功');
                                this.hideNewValueAddedServices();
                                this.getList();
                                // 修改完成后，清空 TempId
                                this.TempId = '';
                            } else {
                                this.$message.error(res.msg);
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.newValueAddedServicesLoading = false;
                        });
                }
            });
        },
        // 确认发布或添加货主
        handleConfirmPublish() {
            if (this.selectedPublishTargets.length === 0) {
                this.$message.warning('请选择发布对象');
                return;
            }

            const { id, addServiceName } = this.currentPublishRow;
            if (!id) {
                this.$message.error('公式不存在！');
                return;
            }

            if (this.isAddShipper) {
                // 添加货主操作
                // 过滤出新添加的货主ID
                const newShipperIds = this.selectedPublishTargets.filter(
                    id => !this.existingShipperIds.includes(id)
                );

                if (newShipperIds.length === 0) {
                    this.$message.warning('请选择新的货主');
                    return;
                }

                this.$confirm(`确认为公式：${addServiceName} 添加选中的货主？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        this.loading = true;
                        valueAddedServices
                            .addShipper({
                                addedServiceId: id,
                                ownerIds: this.selectedPublishTargets // 提交所有选中的货主ID，包括已有的和新增的
                            })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功添加货主！');
                                    this.getList();
                                    this.publishDialogVisible = false;
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .catch(() => {})
                            .finally(() => {
                                this.loading = false;
                            });
                    })
                    .catch(() => {});
            } else {
                // 发布操作
                this.$confirm(`确认发布公式：${addServiceName}？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        this.loading = true;
                        valueAddedServices
                            .releaseAddedService({
                                id,
                                ownerIds: this.selectedPublishTargets
                            })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功发布公式！');
                                    this.getList();
                                    this.publishDialogVisible = false;
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .catch(() => {})
                            .finally(() => {
                                this.loading = false;
                            });
                    })
                    .catch(() => {});
            }
        },
        handleIndexChange(e) {
            this.billingFormulaForm.index = e;
            this.billingFormulaForm.addedServiceFormulaList.forEach((item) => {
                item.conditionType = '';
            });
        },
        // 关闭发布对象选择弹窗
        handlePublishDialogClose() {
            this.selectedPublishTargets = [];
            this.existingShipperIds = [];
            this.currentPublishRow = null;
            this.isAddShipper = false;
            this.dialogLoading = false;
        },
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        // 调整公式状态
        handleSwitchChange(row) {
            const { status, id } = row;
            if (status && id) {
                this.loading = true;
                const params = {
                    id,
                    status
                };
                valueAddedServices
                    .updateCarrierAddedServiceStatus(params)
                    .then((res) => {
                        if (res.code === 200) {
                            this.$message.success('成功修改公式状态！');
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .catch(() => {})
                    .finally(() => {
                        this.loading = false;
                        this.getList();
                    });
            }
        },
        // 查看货主
        handleViewShippers(row) {
            // 构造目标路由对象
            const route = {
                path: '/shipperValueAddedServices',
                query: {
                    addedServiceConfigId: row.addServiceId
                }
            };

            // 先删除已存在的相同路径的标签页
            if (this.$store.state.tagsView?.visitedViews) {
                const existingViews = this.$store.state.tagsView.visitedViews.filter(
                    v => v.path === '/shipperValueAddedServices'
                );
                existingViews.forEach(view => {
                    this.$store.commit('tagsView/DEL_VISITED_VIEW', view);
                });
            }

            // 然后进行路由跳转
            this.$router.push(route);
        },
        // 关闭新增增值服务弹窗
        hideNewValueAddedServices() {
            this.newValueAddedServicesVisible = false;
            this.$refs.billingFormulaForm.resetFields();
            this.billingFormulaForm = {
                addServiceId: '',
                addServiceIdTemp: null,
                goodsType: '',
                isNeedInput: '',
                price: null,
                unit: '',
                minCost: '',
                remark: '',
                index: '-1',
                defaultFormulaList: [],
                addedServiceFormulaList: [],
                isDefault: '0', // 是否默认 0-否 1-是
                defaultValue: null, // 默认值
                tempType: '' // 温区类型
            };
            // 取消已发布状态
            this.isDisabled = false;
            this.$nextTick(() => {
                // 加上延时避免 mounted 方法比页面加载早执行 或者 对img进行块级化设置宽高进行 提前站位
                setTimeout(() => {
                    // 描述文字清除
                    this.billingFormulaForm.remark = '';
                }, 1000);
            });
        },
        isDefaultChange(e) {
            if (e != '1') {
                this.billingFormulaForm.defaultValue = null;
            } else {
                this.billingFormulaForm.defaultValue = 0;
            }
        },
        // 打开新增增值服务弹窗
        openValueAddedService(type, row) {
            this.newValueAddedServicesVisible = true;
            if (type === 'add') {
                this.newValueAddedServicesTitle = '新增增值服务';
                this.newValueAddedServicesLoadingText = '新增中...';
                this.newValueAddedServicesModify = false;
            } else if (type === 'edit') {
                this.newValueAddedServicesTitle = '修改增值服务';
                this.newValueAddedServicesLoadingText = '修改中...';
                const { id } = row;
                // 查询增值服务详情
                this.getDetail(id);
                // 临时存储 id
                this.TempId = id;
                // 打开修改状态
                this.newValueAddedServicesModify = true;
            }
        },
        resetQuery(formName) {
            // 先重置所有查询参数
            this.queryParams = {
                current: 1,
                size: 10,
                addServiceId: undefined,
                tempType: undefined,
                no: undefined
            };
            // 然后重置表单
            this.$refs[formName].resetFields();
            // 最后进行查询
            this.getList();
        },
        resetTheFormulaOrCondition(type, i) {
            if (type === 'defaultFormulaList') {
                this.billingFormulaForm.defaultFormulaList = [];
            } else {
                this.billingFormulaForm.addedServiceFormulaList[i][type] = [];
            }
            this.$forceUpdate();
        },
        // 撤销操作
        revokeFormula(type, i) {
            if (type === 'defaultFormulaList') {
                this.billingFormulaForm[type].pop();
            } else {
                this.billingFormulaForm.addedServiceFormulaList[i][type].pop();
            }
            this.$forceUpdate();
        },
        // 描述文字传参
        setFormulaDescription() {
            // this.billingFormulaForm 拷贝
            const data = JSON.parse(JSON.stringify(this.billingFormulaForm));
            // 对默认公式进行处理
            let defaultFormula = '';
            data.defaultFormulaList.forEach((item) => {
                defaultFormula += item.value;
            });

            // 对新增公式组 处理
            data.addedServiceFormulaList.forEach((item) => {
                item.formula = '';
                item.conditions = '';
                item.formulaList.forEach((item2) => {
                    item.formula += item2.value;
                });
                item.conditionsList.forEach((item2) => {
                    item.conditions += item2.value;
                });
                item.sort = data.addedServiceFormulaList.indexOf(item);
            });
            valueAddedServices
                .translateCarrierPriceBookFormula({ ...data, defaultFormula, name: data.addServiceIdTemp.name, parameter: this.parameter })
                .then((res) => {
                    if (res.code === 200) {
                        this.billingFormulaForm.remark = res.data;
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .catch(() => {});
        },
        setParameterSelection(value, type, text) {
            if (this.billingFormulaForm.index === '-1') {
                this.billingFormulaForm.defaultFormulaList.push({
                    value,
                    type,
                    text
                });
            } else {
                const { conditionType, conditionsList, formulaList, id } = this.billingFormulaForm.addedServiceFormulaList[this.billingFormulaForm.index];
                // 已发布 id存在 提示
                if (id && this.isDisabled) {
                    this.$message.warning('已发布的公式不可编辑！可修改公式状态与新增公式');
                } else {
                    if (conditionType === '1') {
                        conditionsList.push({ value, type, text });
                    } else if (conditionType === '2') {
                        formulaList.push({ type, value, text });
                    }
                }
            }
            this.$forceUpdate();
        },
        submitDelete(row) {
            const { id, addServiceName } = row;
            this.$confirm('是否确认删除增值服务：' + addServiceName, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    if (id) {
                        const loading = this.$loading({
                            lock: true,
                            text: '删除中...',
                            spinner: 'el-icon-loading'
                        });
                        valueAddedServices
                            .deleteAddedServiceCarrier({ ids: id })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success(res.data);
                                    this.getList();
                                } else {
                                    this.$message.error(res.message);
                                }
                            })
                            .catch(() => {})
                            .finally(() => {
                                loading.close();
                            });
                    }
                })
                .catch(() => {});
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
:deep(.el-result) {
    padding: 0;
}
:deep(.el-input.is-disabled .el-input__inner),
:deep(.el-textarea.is-disabled .el-textarea__inner),
:deep(.el-radio__input.is-disabled + span.el-radio__label) {
    color: #666666;
    background-color: #f5f7fa;
}
.billingFormulaForm__radio {
    :deep(.el-radio.is-bordered) {
        height: auto;
        width: 100%;
        display: flex;
        align-items: center;
        padding: 15px;

        > :nth-child(1) {
            display: none;
        }
    }
    > :deep(.el-radio.is-bordered) {
        padding: 0;
    }
}
.billingFormulaForm__radio,
.billingFormulaForm__radio__condition {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    align-items: stretch;

    :deep(.el-radio.is-bordered + .el-radio.is-bordered) {
        margin-left: 0;
    }

    :deep(.el-radio__label) {
        display: flex;
        padding-left: 0;
        align-items: center;
        gap: 20px;
        flex: 1;

        > :nth-child(1) {
            flex: 1;
        }
        .el-form-item--default {
            margin-bottom: 0;
        }
    }
}
.billingFormulaForm__radio__condition {
    :deep(.el-form-item__label) {
        cursor: move;
    }
}
.tag__box {
    display: flex;
    grid-gap: 10px;
    flex: 1;
}
.tag__m {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex: 1;
    padding: 0 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
}
.box__numberButton {
    display: flex;
    flex-wrap: wrap;
    grid-gap: 10px;
    :deep(.el-button + .el-button) {
        margin-left: 0;
    }
}
.box__grid__parameter {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    button {
        margin-left: 0;
    }
}
.btn__revoke {
    margin-left: auto;
    color: #5670fe;
    cursor: pointer;
}
.list {
    .list-item {
        cursor: move;
        transition: transform 0.3s;
        display: flex;
        gap: 15px;
        padding: 15px 0 15px 15px;
        position: relative;
        align-items: center;
        .list__item__sort {
            width: 0;
            height: 0;
            border-top: 30px solid #666666;
            border-right: 30px solid transparent;
            position: absolute;
            top: 0;
            left: 0;
        }
        .list__item__serialNumber {
            font-size: 12px;
            text-align: center;
            color: #ffffff;
            position: absolute;
            top: 3px;
            left: 5px;
        }
    }
}
:deep(.is-bordered.is-checked .list-item .list__item__sort) {
    border-top: 30px solid #5670fe;
}
.drawer__add {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    margin-top: 10px;
    .add__tip {
        font-size: 12px;
        color: #b1b1b1;
        display: flex;
        gap: 3px;
        align-items: baseline;
    }
}
pre {
    color: #666666;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 发布对象选择弹窗样式 */
:deep(.el-transfer) {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-transfer-panel {
        width: 300px;
    }

    .el-transfer-panel__body {
        height: 400px;
    }

    .el-transfer-panel__list.is-filterable {
        height: 350px;
    }
    // 搜索框宽度自适应
    .el-transfer-panel__filter {
        width: 100%;
        margin: 0;
        padding: 8px 12px;

        .el-input {
            width: 100%;

            .el-input__wrapper {
                width: 100%;
            }
        }
    }

    // 禁用项样式
    .el-checkbox.is-disabled + span {
        color: #333333 !important;
        cursor: not-allowed;
    }

    .el-transfer-panel__item.is-disabled {
        background-color: #f0f0f0 !important;
        opacity: 1 !important;
        width: 100%;
        display: block;
    }

    // 确保禁用项的背景色拉通
    .el-transfer-panel__item.is-disabled .el-checkbox__label {
        width: 100%;
        display: block;
    }

    // 确保禁用项的文字颜色为黑色
    .el-transfer-panel__item.is-disabled .el-checkbox__label span {
        color: #333333 !important;
    }

    // 覆盖Element UI的默认样式
    .el-transfer-panel__item.is-disabled .el-checkbox__input.is-disabled + .el-checkbox__label {
        color: #333333 !important;
    }
}

.dialog-footer {
    text-align: right;
    margin-top: 20px;
}

.transfer-footer {
    padding: 8px 12px;
    text-align: center;
    background-color: #f5f7fa;
    border-top: 1px solid #dcdfe6;
}

.text-muted {
    color: #909399;
    font-size: 12px;
}

/* 全局禁用项样式 */
:deep(.el-transfer-panel__item.is-disabled) {
    background-color: #f0f0f0 !important;
    opacity: 1 !important;
}

:deep(.el-transfer-panel__item.is-disabled .el-checkbox__input.is-disabled + .el-checkbox__label) {
    color: #333333 !important;
}
</style>
