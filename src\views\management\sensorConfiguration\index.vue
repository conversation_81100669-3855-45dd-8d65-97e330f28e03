<!--  传感器设置 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form">
                <el-form-item label="设备编号" prop="serialNumber" style="width: 250px">
                    <el-input v-model="queryParams.serialNumber" clearable placeholder="请输入设备编号" />
                </el-form-item>
                <el-form-item class="deviceStatus" label="设备状态" style="width: 250px">
                    <el-select v-model="queryParams.deviceStatus" clearable placeholder="请选择设备状态">
                        <el-option v-for="item in deviceList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作状态" prop="operateStatus" style="width: 250px">
                    <el-select v-model="queryParams.operateStatus" clearable placeholder="请选择操作状态">
                        <el-option v-for="item in operateList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="设备厂家" prop="manufacturer">
                    <el-input v-model="queryParams.manufacturer" clearable placeholder="请输入设备厂家" />
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="getList" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" :icon="SetUp" size="mini" type="warning" @click="batch">批量修改</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" :icon="SetUp" size="mini" type="danger" @click="whole">全部修改</el-button>
                    </el-col>
                </el-row>
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table v-loading="loading" :data="abnormalprintList" border style="margin-top: 15px" @selection-change="handleSelectionChange">
                <el-table-column align="center" type="selection" width="55" />
                <el-table-column :index="idxMethod" align="center" label="序号" type="index" width="55" />
                <el-table-column align="center" label="设备编号" prop="serialNumber" width="120" />
                <el-table-column align="center" label="设备名称" prop="name" width="120" />
                <el-table-column align="center" label="设备厂家" min-width="160" prop="manufacturer" />
                <el-table-column :formatter="(row) => formDict(deviceStatusList, row.deviceStatus)" align="center" label="设备状态" prop="deviceStatus" width="100" />
                <el-table-column :formatter="(row) => formDict(operateList, row.operateStatus)" align="center" label="操作状态" prop="deviceTermial.operateStatus" width="100" />
                <el-table-column align="center" label="数据处理" prop="isPrintProcess">
                    <template #default="scope">
                        <el-switch v-model="scope.row.isPrintProcess" size="small" @change="smsEnableUp(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="短信开关" prop="smsEnable">
                    <template #default="scope">
                        <el-switch v-model="scope.row.smsEnable" size="small" @change="smsEnableUp(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column align="center" label="创建时间" min-width="150" prop="updateDate">
                    <template #default="scope">
                        <span>{{ moment(scope.row.updateDate).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="备注" min-width="160" prop="remark" show-overflow-tooltip/>
                <el-table-column align="center" label="操作" prop="originalRecordList" fixed="right" width="100" show-overflow-tooltip>
                    <template #default="scope">
                        <el-button icon="el-icon-edit" link size="small" type="warning" @click="showJSON(scope.row)">设置</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
        <!-- 批量 -->
        <el-dialog v-model="dialogVisible" :before-close="handleClose" :title="title" width="30%">
            <div style="text-align: center">
                <div>
                    <span>设备短信发送开关</span>
                    <el-switch v-model="messageOpen" active-text="开" inactive-text="关" inline-prompt size="large" style="margin-left: 20px" />
                </div>
                <div>
                    <span>设备打印数据处理</span>
                    <el-switch v-model="printOpen" active-text="开" inactive-text="关" inline-prompt size="large" style="margin-left: 20px" />
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="batchModify">确定</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 全部 -->
        <el-dialog v-model="wholeOpen" :before-close="handleClose" :title="title" width="30%">
            <div style="text-align: center">
                <div>
                    <span>设备短信发送开关</span>
                    <el-switch v-model="messageOpen" active-text="开" inactive-text="关" inline-prompt size="large" style="margin-left: 20px" />
                </div>
                <div>
                    <span>设备打印数据处理</span>
                    <el-switch v-model="printOpen" active-text="开" inactive-text="关" inline-prompt size="large" style="margin-left: 20px" />
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="wholeOpen = false">取消</el-button>
                    <el-button type="primary" @click="modifyAll">确定</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 设置 -->
        <el-drawer v-show="open" v-model="open" :title="title" append-to-body size="650" style="padding: 0 30px">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
                <el-form-item class="define" label="设备编号" prop="serialNumber">
                    <el-input v-model="form.serialNumber" disabled placeholder="请输入设备编号" />
                </el-form-item>
                <el-form-item class="define" label="设备名称" prop="name">
                    <el-input v-model="form.name" disabled placeholder="请输入设备名称" />
                </el-form-item>
                <el-form-item class="define" label="设备状态" prop="deviceStatus">
                    <el-select v-model="form.deviceStatus" disabled style="width: 500px">
                        <el-option v-for="item in deviceList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item class="define" label="操作状态" prop="operateStatus">
                    <el-select v-model="form.operateStatus" disabled style="width: 500px">
                        <el-option v-for="item in operateList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item class="define" label="设备短信发送开关" prop="smsEnable">
                    <el-switch v-model="form.smsEnable" active-text="开" inactive-text="关" inline-prompt size="large" style="margin-left: 20px" />
                </el-form-item>
                <el-form-item class="define" label="设备打印数据处理" prop="isPrintProcess">
                    <el-switch v-model="form.isPrintProcess" active-text="开" inactive-text="关" inline-prompt size="large" style="margin-left: 20px" />
                </el-form-item>
                <el-form-item class="define" label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" />
                </el-form-item>
            </el-form>
            <div class="drawer-footer">
                <el-button @click="open = false; form = {};">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-drawer>
    </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { SetUp } from '@element-plus/icons-vue';
import sensorConfigurationApi from '@/api/management/sensorConfiguration';
import moment from 'moment';
import SearchButton from '@/components/searchModule/SearchButton.vue';
const { proxy } = getCurrentInstance();

// 表单验证
const rules = reactive({
    isPrintProcess: [{ required: true, message: '请选择是否打印时处理', trigger: 'change' }],
    smsEnable: [{ required: true, message: '请选择是否启用短信', trigger: 'change' }]
});
// 显示搜索条件
const showSearch = ref(true);
// 查询参数
const queryParams = ref({
    current: 1,
    size: 10,
    total: 0
});

// 表格多选
const chooseList = ref([]);
const handleSelectionChange = (key) => {
    chooseList.value = key;
};
const isShowAll = ref(false);
const title = ref('');
const dialogVisible = ref(false);
/** 批量操作 */
function batch() {
    if (chooseList.value.length > 0) {
        dialogVisible.value = true;
        title.value = '批量修改';
    } else {
        proxy.msgError('请选择需要修改的数据！');
    }
}
//查询展开收缩
function showAllClick() {
    isShowAll.value = !isShowAll.value;
}
// 确定批量修改
const newFilArr = ref([]);
function batchModify() {
    let ids = '';
    newFilArr.value = [];
    chooseList.value.filter((item) => {
        newFilArr.value.push(item.serialNumber);
    });
    ids = newFilArr.value.join(',');
    sensorConfigurationApi
        .updateDataProcess({
            smsEnable: messageOpen.value,
            isPrintProcess: printOpen.value,
            deviceCodes: ids
        })
        .then((res) => {
            if (res.code == 200) {
                proxy.msgSuccess('全部修改成功');
                getList();
                dialogVisible.value = false;
                messageOpen.value = false;
                printOpen.value = false;
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

const wholeOpen = ref(false);
/** 全部 */
function whole() {
    proxy
        .$confirm('是否确认全部修改数据?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            wholeOpen.value = true;
            title.value = '全部修改';
        });
}
// 确定全部修改
function modifyAll() {
    sensorConfigurationApi
        .updateAllDataProcess({
            smsEnable: messageOpen.value,
            isPrintProcess: printOpen.value
        })
        .then((res) => {
            if (res.code == 200) {
                proxy.msgSuccess('全部修改成功');
                getList();
                wholeOpen.value = false;
                messageOpen.value = false;
                printOpen.value = false;
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

// 设备状态
const deviceList = ref([
    { code: 1, name: '启用' },
    { code: 2, name: '停用' },
    { code: 3, name: '待验证' }
]);

// 短信开关
const messageOpen = ref(false);
// 设备打印
const printOpen = ref(false);
//字典回显
const formDict = (data, val) => {
    return data && val ? proxy.selectDictLabel(data, val) : '--';
};
const operateList = ref([]);
// 设备状态
const deviceStatusList = ref([]);
// 字典请求
const getDict = async () => {
    operateList.value = await proxy.getDictList('operate_type');
    deviceStatusList.value = await proxy.getDictList('sensor_device_type');
};
getDict();

// 查询设备列表数据
const abnormalprintList = ref([]);
function getList() {
    sensorConfigurationApi.configList(queryParams.value).then((res) => {
        if (res.code == 200) {
            abnormalprintList.value = res.data.records;
            queryParams.value.total = res.data.total;
        }
    });
}
getList();

// 重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    getList();
}

// 设置
const form = ref({});
const open = ref(false);
function showJSON(row) {
    form.value = row;
    open.value = true;
    title.value = '设备设置';
}

// 列表修改
function smsEnableUp(val) {
    sensorConfigurationApi
        .updateDataProcess({
            isPrintProcess: val.isPrintProcess,
            smsEnable: val.smsEnable,
            deviceCodes: val.serialNumber
        })
        .then((res) => {
            console.log(res);
            if (res.code == 200) {
                proxy.msgSuccess('设置成功');
                getList();
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

// 设置
function submitForm() {
    proxy.$refs['formRef'].validate((valid) => {
        if (valid) {
            sensorConfigurationApi
                .updateDataProcess({
                    isPrintProcess: form.value.isPrintProcess,
                    smsEnable: form.value.smsEnable,
                    remark: form.value.remark,
                    deviceCodes: form.value.serialNumber
                })
                .then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        proxy.msgSuccess('设置成功');
                        getList();
                        open.value = false;
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        }
    });
}
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.deviceata {
    border: 2px solid #f5f7fa;
    padding: 20px;
    width: 100%;
}

.drawer-footer {
    position: absolute;
    bottom: 20px;
    left: 300px;
}
</style>
