<template>
    <div class="app-container">
        <!-- s 搜索项-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form" @submit.native.prevent>
                <el-form-item label="订单号" prop="orderNo" style="width: 280px">
                    <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="运单号" prop="transOrderNo" style="width: 280px">
                    <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单号" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="请选择订单状态" @change="handleQuery">
                        <el-option v-for="dict in statusDicts" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件公司" prop="sendCompany" style="width: 280px">
                    <el-input v-model="queryParams.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件地址" prop="sendAddress">
                    <el-cascader v-model="queryParams.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择发件地址" @change="handleQuery" @visible-change="visibleChange" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件公司" prop="receiverCompany" style="width: 280px">
                    <el-input v-model="queryParams.receiverCompany" clearable placeholder="请输入收件公司" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件地址" prop="receiverAddress">
                    <el-cascader v-model="queryParams.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择收件地址" @change="handleQuery" @visible-change="visibleChange" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="产品分类" prop="productClass">
                    <el-select v-model="queryParams.productClass" clearable filterable placeholder="请选择产品分类" @change="handleQuery">
                        <el-option v-for="dict in fourplProductClassDicts" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运输类型" prop="productType">
                    <el-select v-model="queryParams.productType" clearable filterable placeholder="请选择运输类型" @change="handleQuery">
                        <el-option v-for="dict in productTypeDicts" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="承运商" prop="carrierId">
                    <el-select v-model="queryParams.carrierId" clearable filterable placeholder="请选择承运商" style="width: 100%" @change="changeCarrierQuery">
                        <el-option v-for="item in carrierList" :key="item.carrierId" :label="item.carrierName" :value="item.carrierId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="温层类型" prop="temperatureTypeTemp" style="width: 280px">
                    <el-select v-model="queryParams.temperatureTypeTemp" :disabled="!queryParams.carrierId" :placeholder="queryParams.carrierId ? '请选择温层类型' : '请选择承运商'" clearable collapse-tags collapse-tags-tooltip filterable multiple @change="handleQuery">
                        <el-option v-for="(dict, index) in temperatureTypeDicts" :key="index" :label="dict.describtion" :value="dict.id" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="付款方式" prop="paymentMethod">
                    <el-select v-model="queryParams.paymentMethod" clearable filterable placeholder="请选择付款方式" @change="handleQuery">
                        <el-option v-for="(dict, index) in fourplPaymentMethodOptions" :key="index" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="揽收方式" prop="orderType">
                    <el-select v-model="queryParams.orderType" clearable placeholder="请选择揽收方式" @change="handleQuery">
                        <el-option v-for="dict in collectionMethod" :key="dict.value" :label="dict.name" :value="dict.value * 1" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="件数" prop="goodsPackages">
                    <div>
                        <el-select v-model="queryParams.prePackageOption" style="width: 45%">
                            <el-option v-for="(item, index) in prePackageOptions" :key="index" :label="item.name" :value="item.value"> </el-option>
                        </el-select>
                        <el-input v-model="queryParams.goodsPackages" clearable placeholder="请输入件数" style="width: 55%" @keyup.enter.native="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="货值金额" prop="goodsAmount">
                    <div style="width: 220px">
                        <el-select v-model="queryParams.preAmountOption" style="width: 45%">
                            <el-option v-for="(item, index) in prePackageOptions" :key="index" :label="item.name" :value="item.value"> </el-option>
                        </el-select>
                        <el-input v-model="queryParams.goodsAmount" clearable placeholder="请输入货值金额" style="width: 55%" @keyup.enter.native="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item label="下单时间" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!-- e 搜索项-->

        <!-- s 列表项-->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 16px">
                <el-button v-hasPermi="['fourPL:order:add']" icon="el-icon-plus" size="mini" type="primary" @click="openTheNewOrderSlider">下单</el-button>
                <el-button v-hasPermi="['fourpl:order:batchConfirm']" :disabled="multiple" icon="el-icon-check" size="mini" type="success" @click="batchConfirmationOrder">批量提交</el-button>
                <el-button icon="el-icon-download" size="mini" type="warning" @click="handleExportOrder">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="OwnerOrderList" @queryTable="getList" />
            </div>
            <column-table :max-height="600" key="OwnerOrderList" ref="ColumnTable" v-loading="loading" :columns="columns" :data="orderList" :show-check-box="true" @select="handleSelectionChange" @select-all="selectAll" @row-dblclick="(row) => getOrderInfo(row)">
                <template #status="{ row }">
                    <span v-if="row.status == 1" style="color: #5670fe">{{ statusFormater(row) }}</span>
                    <span v-else-if="row.status == 2" style="color: #ff2a2a">{{ statusFormater(row) }}</span>
                    <span v-else-if="row.status == 3" style="color: #ff2a2a">{{ statusFormater(row) }}</span>
                    <span v-else-if="row.status == 4" style="color: #1acd7e">{{ statusFormater(row) }}</span>
                    <span v-else-if="row.status == 5" style="color: #b1b1b1">{{ statusFormater(row) }}</span>
                    <span v-else>{{ statusFormater(row) }}</span>
                </template>
                <template #sendAddress="{ row }">
                    <span>{{ row?.sendTown?.province || '' }}{{ row?.sendTown?.city || '' }}{{ row?.sendTown?.county || '' }}{{ row?.sendTown?.town || '' }}{{ row?.sendAddress || '' }}</span>
                </template>
                <template #receiverAddress="{ row }">
                    <span>{{ row?.receiverTown?.province || '' }}{{ row?.receiverTown?.city || '' }}{{ row?.receiverTown?.county || '' }}{{ row?.receiverTown?.town || '' }}{{ row?.receiverAddress || '' }}</span>
                </template>
                <template #orderType="{ row }">
                    <span>{{ dictionaryFormatting(collectionMethod, row.orderType) }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ dictionaryFormatting(productTypeDicts, row.productType) }}</span>
                </template>
                <template #paymentMethod="{ row }">
                    <span>{{ fourplPaymentMethodFormat(row) }}</span>
                </template>
                <template #productClass="{ row }">
                    <span>{{ fourplProductClassFormat(row) }}</span>
                </template>
                <template #paymentStatus="{ row }">
                    <span>{{ fourplOrderPaymentStatusFormat(row) }}</span>
                </template>
                <template #temperatureType="{ row }">
                    <span>{{ row.temperatureType.describtion || '' }}</span>
                </template>
                <template #createDate="{ row }">
                    <span>{{ timeFormatting(row.createDate) }}</span>
                </template>
                <template #cancelType="{ row }">
                    <span>{{ row.cancelType == '0' ? row.cancelReason : fourplCancelAnOrderTypeFormat(row) }}</span>
                </template>
                <template #cancelUser="{ row }">
                    <span>{{ row?.cancelUser?.name || '' }}</span>
                </template>
                <template #opt="{ row }" style="width: 300px">
                    <el-button v-if="row.status == '2'" v-hasPermi="['fourPL:order:edit']" icon="el-icon-edit" link size="small" type="warning" @click="editOrderClick(row)">修改订单</el-button>
                    <!--        <el-button v-if="row.orderType == '2' && row.authStatus == '1' && row.status == '2'" icon="el-icon-check" link type="primary" @click="batchConfirmOrder(row)">确认订单</el-button>-->
                    <el-button v-if="row.status == '3' || row.status == '4' || row.status == '6'" icon="el-icon-printer" link size="small" type="primary" @click="printBoxLabels(row)">打印箱签</el-button>
                    <el-dropdown size="small" style="height: 23px">
                        <el-button icon="el-icon-arrow-down" link size="small">更多</el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item icon="el-icon-info-filled" style="color: #5670fe" @click.native="getOrderInfo(row)">订单详情</el-dropdown-item>
                                <el-dropdown-item v-if="row.status == '1'" v-hasPermi="['fourPL:order:edit']" icon="el-icon-edit" style="color: #67c23a" @click.native="setOrder(row)">审核订单</el-dropdown-item>
                                <el-dropdown-item v-if="row.status == '1' || row.status == '2' || row.status == '3'" icon="el-icon-close" style="color: #747474" @click.native="cancelAnOrderClick(row)">取消订单</el-dropdown-item>
                                <el-dropdown-item style="color: #f4ac00" @click.native="collectOrder(row.id, row.collectFlag)"
                                    ><el-icon><StarFilled v-if="row.collectFlag === '1'" /><Star v-else /></el-icon>收藏订单</el-dropdown-item
                                >
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
        </el-card>
        <!-- e 列表项-->

        <!--s 修改订单、确认订单、审核订单 滑块-->
        <el-drawer v-model="addOrderSwitch" :title="orderSliderTitle" direction="rtl" size="70%" @close="onClose">
            <ModifyOrder v-if="addOrderSwitch" :order-info="orderInfo" :type="editOrderType" @close="onClose"></ModifyOrder>
        </el-drawer>
        <!--订单详情-->
        <el-drawer v-model="infoOpen" direction="rtl" size="50%" title="订单详情" @close="infoOpen = false">
            <div style="background-color: #f5f7fd; padding: 10px">
                <order-detail-trans v-if="infoOpen" :order-info="orderInfo" source="2"></order-detail-trans>
            </div>
        </el-drawer>

        <!--s 违禁品协议组件  -->
        <!--  <ContrabandAgreement ref="contrabandAgreement" @protocolStatus="agreeToTheAgreement" />-->
        <!--e 违禁品协议组件  -->

        <!--    打印箱签滑块-->
        <el-drawer v-model="printBox" direction="rtl" size="50%" title="打印箱签">
            <print-box-tag v-if="printBox" :orderInfo="orderInfo" @callbackMethod="closePrintBoxLabel"></print-box-tag>
        </el-drawer>
        <!--    批量确认提醒-->
        <el-dialog v-model="batchConfirmationOrderShow" :show-close="false" append-to-body class="icon-dialog" modal-append-to-body title="批量确认" width="400px">
            <div v-for="(item, index) in receivingModeList" :key="index" style="margin-top: 10px">
                <div style="display: flex; justify-content: space-around; font-size: 16px; color: #333333">
                    <div>{{ item.name }}</div>
                    <div style="color: #ea0008; margin-left: 20px">{{ item.num }}单</div>
                </div>
            </div>
            <template #footer>
                <el-button @click="cancelBatchConfirmationOrder">关闭</el-button>
				<el-button type="primary" @click="submitBatchConfirmationOrder">确 定</el-button>
			</template>
        </el-dialog>
        <el-dialog v-model="cancelAnOrderOpen" append-to-body dialogDrag title="取消订单" width="50%" @close="cancelAnOrderOpen = false">
            <el-form key="cancelAnOrder" ref="formCancelAnOrder" :model="formCancelAnOrder" :rules="cancelAnOrderRules" style="width: 100%">
                <el-form-item label="订单编号:" label-width="110px" prop="orderNo">
                    {{ formCancelAnOrder.orderNo }}
                </el-form-item>
                <el-form-item label-width="40px" prop="cancelType">
                    <el-radio-group v-model="formCancelAnOrder.cancelType" style="width: 100%">
                        <div style="display: flex; flex-direction: column; width: 100%">
                            <el-radio v-for="(item, index) in fourplOrderChangeTypeOptions" :key="index" :label="item.value" border>
                                <div style="display: flex; align-items: center; flex-grow: 1">
                                    <div style="margin-right: 10px">{{ item.name }}</div>
                                    <el-form-item v-if="formCancelAnOrder.cancelType == '0' && item.value == '0'" label-width="0px" prop="cancelReason" style="width: 100%">
                                        <el-input v-model="formCancelAnOrder.cancelReason" maxlength="50" placeholder="请输入其他取消原因" show-word-limit type="text" />
                                    </el-form-item>
                                </div>
                            </el-radio>
                        </div>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelAnOrderOpen = false">取 消</el-button>
                <el-button type="primary" @click="cancelOrder">确认取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar';
import PrintBoxTag from '@/views/orderComponents/PrintBoxTag';
import OrderDetailTrans from '@/views/orderComponents/OrderDetailTrans';
import ModifyOrder from '@/views/shipperEnd/ownersOrderList/ModifyOrder.vue';
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration.js'; // 其他配置
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
import { ElLoading } from 'element-plus';
import { Star, StarFilled } from '@element-plus/icons-vue';
import moment from 'moment';
import { setDatePickerShortcuts } from '@/utils/config-store';
export default {
    name: 'OwnerOrderList',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar,
        PrintBoxTag,
        OrderDetailTrans,
        ModifyOrder,
        Star,
        StarFilled
    },
    data() {
        return {
            // 订单信息 数据
            form: {
                orderSource: 1, //订单来源
                orderType: '2', //取件方式
                pickupAddress: '', //取件地址
                shippingAddress: '', //收件地址
                sendAddress: '', //取件地址 详细地址
                receiverAddress: '', //收件地址 详细地址
                sendUser: '', //取件联系人
                receiverUser: '', //收件人名称
                sendUserPhone: '', //取件人电话
                receiverUserPhone: '', //收件人电话
                remark: '', //备注
                sendCompany: '', //发货人公司
                receiverCompany: '', //收货人公司
                productType: '', //产品分类
                temperatureType: '', //温层类型
                productClass: '', //货品名称
                vehicleSelection: null, //车型选择
                goodsPackages: '', //件数
                productProperties: null, //货品属性
                productWeight: null, //重量
                externalOrderNo: '', //随货同行单号
                productVolume: null, //体积
                paymentMethod: '', //付款方式
                addedServices: [], //附加服务
                agreeToTermsCarrier: [], //阅读并同意
                carrierId: '', //承运商
                orderCost: 0.0, // 预估费用
                costData: [], // 费用明细
                kilometre: 0 // 公里数
            },
            addOrderSwitch: false, //新增订单开关
            // 搜索项
            queryParams: {
                current: 1,
                size: 10,
                orderNo: null,
                transOrderNo: null,
                belongUserName: null,
                orderType: null,
                sendCompany: null,
                receiverCompany: null,
                status: null,
                queryTime: [],
                sysCompanyName: null,
                sysCompanyPhone: null,
                sysCompanyDept: null,
                temperatureTypeTemp: null,
                preAmountOption: null,
                goodsAmount: null
            },
            //订单列表
            orderList: [],
            total: 0, // 总条数
            loading: false,
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', width: '120px', fixed: 'left', columnShow: true, hideFilter: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '120px', columnShow: true },
                // { title: '订单所属', key: 'companyName', align: 'center', width: '180px', columnShow: true },
                { title: '揽收方式', key: 'orderType', align: 'center', width: '120px', columnShow: true },
                { title: '订单状态', key: 'status', align: 'center', width: '100px', columnShow: true },

                { title: '下单时间', key: 'createDate', width: '200px', align: 'center', columnShow: true, sortable: true },
                { title: '发件人', key: 'sendUser', width: '90px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件人电话', key: 'sendUserPhone', width: '120px', align: 'center', columnShow: true },
                { title: '发件公司', key: 'sendCompany', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendAddress', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件人', key: 'receiverUser', width: '90px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件人电话', key: 'receiverUserPhone', width: '120px', align: 'center', columnShow: true },
                { title: '收件公司', key: 'receiverCompany', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '件数', key: 'goodsPackages', width: '80px', align: 'center', columnShow: true },
                { title: '公里数', key: 'kilometre', align: 'center', columnShow: true },
                { title: '运输类型', key: 'productType', width: '100px', align: 'center', columnShow: true },
                { title: '产品分类', key: 'productClass', width: '200px', align: 'center', columnShow: true },
                { title: '保价费（元）', key: 'insureFee', width: '110px', align: 'center', columnShow: true },
                { title: '温层类型', key: 'temperatureType', width: '200px', align: 'center', columnShow: true },
                { title: '付款方式', key: 'paymentMethod', width: '100px', align: 'center', columnShow: true },
                { title: '付款状态', key: 'paymentStatus', width: '100px', align: 'center', columnShow: true },
                { title: '货值金额', key: 'goodsAmount', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '详情说明', key: 'detailDesc', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '取消原因', key: 'cancelType', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '取消操作人', key: 'cancelUser', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '200px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            statusDicts: [], //订单状态字典
            productAttributeDictionary: [], // 商品属性 字典
            productTypeDicts: [], // 运输类型 字典
            addedServiceDicts: [], // 增值服务
            sysAreas: [], //省市区数据

            belongUserId: '95', //订单所属人ID（租户/货主ID）
            createUserId: '5', //下单人ID
            orderSliderTitle: '', //订单详情弹窗标题
            // 显示搜索条件
            showSearch: true,
            temperatureTypeDicts: [], //温层类型字典
            orderDetailCheck: false, //冷链详情开关
            infoOpen: false,
            orderInfo: {
                addedServices: [],
                fileList: [],
                orderDetailList: []
            },
            title: '',
            quicklyPlaceOrderConfirmDisabled: false, //快速下单确认按钮禁用
            typeOptions: [],
            //违禁品协议组件
            agreementView: false,
            fourplPaymentMethodOptions: [], // 付款方式
            carrierList: [], //承运商列表
            fourplProductClassDicts: [], // 产品分类
            collectionMethod: [],
            // 结算费用展示
            costDataList: [],
            // 费用明细弹窗
            costCreakdownShow: false,
            // 是否显示增值服务弹窗
            addedServiceShow: false,
            // 增值服务修改项
            formService: {},
            serviceRules: {
                inputValue: [
                    {
                        required: true,
                        message: '请输入值',
                        trigger: 'blur'
                    }
                ]
            },
            printBox: false,
            selectData: [], // 选中的值
            multiple: true,
            batchConfirmationOrderShow: false, // 批量确认提示
            receivingModeList: [], // 揽收方式
            fourplOrderPaymentStatusDicts: [], // 付款状态
            prePackageOptions: [],
            shortcuts:setDatePickerShortcuts(),
            editOrderType: null, // 修改订单类型 1-修改订单， 2-审核订单 ，3-确认订单
            cancelAnOrderOpen: false, // 取消订单弹窗展示
            formCancelAnOrder: {}, // 取消订单参数
            fourplOrderChangeTypeOptions: [], // 取消原因
            cancelAnOrderRules: {
                cancelType: [{ required: true, message: '请选择取消原因', trigger: 'blur' }],
                cancelReason: [
                    {
                        validator: (rule, value, callback) => {
                            if (this.formCancelAnOrder.type == '0' && (value == '' || value == undefined)) {
                                callback(new Error('请输入其他取消原因'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ]
            },
            ownerAccountInfo: {}, // 货主账户信息
            ownerPaymentMethodOptions: [],
            isShowAll: false
        };
    },
    computed: {
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        },
        // 字典翻译
        dictionaryFormatting() {
            return (data, value) => {
                return this.selectDictLabel(data, value);
            };
        }
    },
    async created() {
        /** 产品分类 */
        this.fourplProductClassDicts = await this.getDictList('fourpl_product_class');
        // 订单状态字典
        this.statusDicts = await this.getDictList('fourpl_order_status');
        // 运输类型4PL
        this.productTypeDicts = await this.getDictList('fourpl_product_type');
        // 货品属性
        this.productAttributeDictionary = await this.getDictList('fourpl_product_attributes');
        /** 付款方式 */
        let fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
        this.fourplPaymentMethodOptions = fourplPaymentMethodOptions.filter((item) => item.value != '4' && item.value != '5' && item.value != '6');
        /** 揽收方式 */
        this.collectionMethod = await this.getDictList('fourpl_mail_service');
        this.receivingModeList = JSON.parse(JSON.stringify(this.collectionMethod));
        /** 付款状态 */
        this.fourplOrderPaymentStatusDicts = await this.getDictList('fourpl_order_payment_status');
        /** 付款状态订单件数搜索条件 */
        this.prePackageOptions = await this.getDictList('fourpl_order_equals');
        orderManagement.carrierSelectList().then((res) => {
            if (res.code === 200 && res.data) {
                this.carrierList = res.data;
                // res.data承运商时默认选中只有一个，显示承运商信息
                if (res.data.length == 1 && res.data[0].carrierId) {
                    this.queryParams.carrierId = res.data[0].carrierId;
                    // 获取温层类型字典
                    this.getTemperatureType();
                }
                // 默认设置当天
                let now = moment(new Date()).format('YYYY-MM-DD');
                this.queryParams.queryTime = [now, now];
                this.handleQuery();
            } else {
                this.msgError('未获得承运商');
            }
        });
    },
    methods: {
        // 展开或者合上
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        },
        // 获取温层类型
        getTemperatureType() {
            otherConfiguration.getTemperatureTypeList({ carrierId: this.queryParams.carrierId, status: '0' }).then((res) => {
                if (res.code === 200 && res?.data?.records.length > 0) {
                    this.temperatureTypeDicts = res.data.records;
                }
            });
        },
        // 取消订单展示
        async cancelAnOrderClick(row) {
            if (!this.fourplOrderChangeTypeOptions || this.fourplOrderChangeTypeOptions.length == 0) {
                /** 异动类型4PL */
                this.fourplOrderChangeTypeOptions = await this.getDictList('fourpl_order_change_type');
            }
            this.formCancelAnOrder = {
                ...row,
                cancelReason: null,
                cancelType: '0'
            };
            this.cancelAnOrderOpen = true;
        },
        // 取消订单
        cancelOrder() {
            this.$refs.formCancelAnOrder.validate((result) => {
                if (result) {
                    if (this.formCancelAnOrder.cancelType != '0') {
                        this.formCancelAnOrder.cancelReason = this.selectDictLabel(this.fourplOrderChangeTypeOptions, this.formCancelAnOrder.cancelType);
                    }
                    orderManagement.cancelOrder(this.formCancelAnOrder).then((res) => {
                        if (res.code == 200) {
                            this.msgSuccess('取消成功');
                            this.$refs['formCancelAnOrder'].resetFields();
                            this.cancelAnOrderOpen = false;
                            this.getList();
                        }
                    });
                }
            });
        },
        /** 异动类型4PL字典转换 */
        fourplCancelAnOrderTypeFormat(val) {
            return this.selectDictLabel(this.fourplOrderChangeTypeOptions, val.cancelType);
        },

        //修改订单显示窗口
        editOrderClick(row) {
            // const { id } = row;
            this.orderInfo = row;
            this.orderSliderTitle = '修改订单';
            this.addOrderSwitch = true;
            this.editOrderType = '1'; // 1-修改订单， 2-审核订单 ，3-确认订单
        },
        changeCarrierQuery(e) {
            this.queryParams.temperatureTypeTemp = null;
            this.getTemperatureType();
            this.handleQuery();
        },
        /** 付款状态4PL字典转换 */
        fourplOrderPaymentStatusFormat(val) {
            return this.selectDictLabel(this.fourplOrderPaymentStatusDicts, val.paymentStatus);
        },
        /** 付款方式4PL字典转换 */
        fourplPaymentMethodFormat(val) {
            return this.selectDictLabel(this.fourplPaymentMethodOptions, val.paymentMethod);
        },
        //批量确认订单
        batchConfirmationOrder() {
            if (this.selectData.length == 0) {
                this.msgError('请选择需要确认的订单');
                return;
            }
            this.receivingModeList.forEach((item, index) => {
                let num = 0;
                this.selectData.forEach((order) => {
                    if (order.orderType == item.value + '') {
                        num = num + 1;
                    }
                });
                this.receivingModeList[index].num = num;
            });
            this.batchConfirmationOrderShow = true;
        },
        // 确认批量操作
        submitBatchConfirmationOrder() {
            this.loading = true;
            orderManagement
                .batchSubmitOrderDrug({ orderList: this.selectData })
                .then((response) => {
                    if (response.code == 200) {
                        this.msgSuccess('批量确认订单成功');
                        this.getList();
                        this.selectData = [];
                        this.multiple = true;
                        this.batchConfirmationOrderShow = false;
                    }
                    this.loading = false;
                })
                .catch((e) => {
                    this.loading = false;
                });
        },
        // 取消批量确认
        cancelBatchConfirmationOrder() {
            this.batchConfirmationOrderShow = false;
        },
        /** 导出按钮操作 */
        handleExportOrder() {
            this.fullLoading = true;
            let params = { ...this.queryParams };
            params.queryType = '0'; // 0-货主 1-承天商
            params.tempId = params.temperatureTypeTemp.toString();
            delete params.queryTime;
            delete params.sendAddress;
            delete params.receiverAddress;
            delete params.temperatureTypeTemp;
            orderManagement
                .newExportOrderData({ filename: '订单列表.xls', ...params }, '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '订单列表.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                        this.msgSuccess('订单导出任务已生成！');
                    } else {
                        this.msgError('导出异常请联系管理员');
                    }
                    this.fullLoading = false;
                })
                .catch((e) => {
                    this.fullLoading = false;
                });
        },

        //确认订单显示窗口
        batchConfirmOrder(row) {
            const { id } = row;
            this.orderSliderTitle = '确认订单';
            orderManagement.queryOrderDrugById({ id }).then((res) => {
                if (res.code === 200) {
                    const loading = ElLoading.service({
                        lock: true,
                        text: '生成订单信息中...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    setTimeout(() => {
                        loading.close();
                        // 打开弹窗
                        this.$nextTick(() => {
                            // res.data赋值给form 不是替换
                            // this.form.agreeToTermsCarrier = [];

                            Object.assign(this.form, res.data);
                            // 数据回显
                            this.form.pickupAddress = [res.data.sendProvinceId, res.data.sendCityId, res.data.sendCountyId, res.data.sendTown.id] || [];
                            this.form.shippingAddress = [res.data.receiverProvinceId, res.data.receiverCityId, res.data.receiverCountyId, res.data.receiverTown.id] || [];

                            this.form.productTypeDesc = this.dictionaryFormatting(this.productTypeDicts, this.form.productType);
                            this.form.temperatureTypeDesc = this.form.temperatureType.describtion;
                            this.form.productClassDesc = this.fourplProductClassFormat({ productClass: this.form.productClass });
                            this.form.orderTypeDesc = this.dictionaryFormatting(this.collectionMethod, this.form.orderType);
                            this.form.paymentMethodDesc = this.dictionaryFormatting(this.fourplPaymentMethodOptions, this.form.paymentMethod);
                            this.form.productPropertiesDesc = this.selectDictLabels(this.productAttributeDictionary, this.form.productProperties);
                            this.form.costData = [];
                            this.form.addedServices = res.data.addedServices;
                            this.addOrderSwitch = true;
                        });
                    }, 500);
                }
            });
        },
        collectOrder(id, type) {
            if (type === '1') {
                this.msgError('订单已收藏');
            } else {
                orderManagement.collectOrder({ id, collectFlag: '1' }).then((res) => {
                    if (res.code === 200) {
                        this.msgSuccess('收藏成功');
                        this.getList();
                    } else {
                        this.msgSuccess('收藏失败');
                    }
                });
                this.getList();
            }
        },
        /** 产品分类 */
        fourplProductClassFormat(val) {
            return this.selectDictLabel(this.fourplProductClassDicts, val.productClass);
        },
        // 获取订单列表
        getList() {
            this.loading = true;
            let params = { ...this.queryParams };
            params.queryType = '0'; // 0-货主 1-承天商
            params.tempId = params.temperatureTypeTemp.toString();
            delete params.queryTime;
            delete params.sendAddress;
            delete params.receiverAddress;
            delete params.temperatureTypeTemp;
            orderManagement.listOrderDrug(params).then((res) => {
                if (res.code === 200) {
                    this.loading = false;
                    this.orderList = res.data.records || [];
                    this.total = res.data.total || 0;
                } else {
                    this.loading = false;
                    this.$message.error(res.msg);
                }
            });
        },
        getOrderInfo(row) {
            orderManagement.queryOrderDrugById({ id: row.id }).then((response) => {
                if (response.code == 200) {
                    this.title = '订单详情';
                    this.infoOpen = true;
					this.orderInfo = { ...response.data, orderId: row.id };
                }
            });
        },
        handleQuery() {
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = null;
                this.queryParams.endCreateDate = null;
            }
            if (this.queryParams.sendAddress) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = this.queryParams.sendAddress;
                this.queryParams.sendProvinceId = sendProvinceId || null;
                this.queryParams.sendCityId = sendCityId || null;
                this.queryParams.sendCountyId = sendCountyId || null;
                this.queryParams['sendTown.id'] = sendTownId || null;
            } else {
                this.queryParams.sendProvinceId = null;
                this.queryParams.sendCityId = null;
                this.queryParams.sendCountyId = null;
                this.queryParams['sendTown.id'] = null;
            }
            if (this.queryParams.receiverAddress) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = this.queryParams.receiverAddress;
                this.queryParams.receiverProvinceId = receiverProvinceId || null;
                this.queryParams.receiverCityId = receiverCityId || null;
                this.queryParams.receiverCountyId = receiverCountyId || null;
                this.queryParams['receiverTown.id'] = receiverTownId || null;
            } else {
                this.queryParams.receiverProvinceId = null;
                this.queryParams.receiverCityId = null;
                this.queryParams.receiverCountyId = null;
                this.queryParams['receiverTown.id'] = null;
            }

            this.queryParams.current = 1;
            this.getList();
        },
        // 全选
        selectAll(selection) {
            this.selectData = [];
            try {
                selection.forEach((item) => {
                    if (item.status == 1) {
                        this.selectData.push(item);
                    } else {
                        this.msgError('当前页订单有非暂存状态的订单，无法进行全选操作！');
                        this.$refs.ColumnTable.clearSelection();
                        throw Error();
                    }
                });
            } catch (e) {}
            this.multiple = !this.selectData.length;
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.selectData = [];
            try {
                selection.forEach((item) => {
                    if (item.status == 1) {
                        this.selectData.push(item);
                    } else {
                        this.msgError('【暂存单】状态的订单才能批量确认！');
                        this.$refs.ColumnTable.toggleRowSelection(item, false);
                        throw Error();
                    }
                });
            } catch (e) {}
            this.multiple = !this.selectData.length;
        },
        // 关闭滑块
        onClose() {
            // 清空form表单
            this.addOrderSwitch = false;
            this.orderInfo = {};
            this.getList();
        },
        // 新建订单 滑块
        openTheNewOrderSlider() {
            this.$router.push({
                name: 'OwnerOrders'
            });
        },

        // 重置表单
        resetForm(formName) {
            this.form.id = null;
            this.$refs[formName].resetFields();
        },
        resetQuery() {
            this.resetForm('queryForm');
            let now = moment(new Date()).format('YYYY-MM-DD');
            this.queryParams.queryTime = [now, now];
            this.queryParams.prePackageOption = null;
            this.queryParams.preAmountOption = null;
            this.handleQuery();
        },
        // 审核订单
        setOrder(row) {
            this.orderSliderTitle = '审核订单';
            this.addOrderSwitch = true;
            this.orderInfo = row;
            this.editOrderType = '2'; // 1-修改订单， 2-审核订单 ，3-确认订单
        },
        statusFormater(row) {
            return this.selectDictLabel(this.statusDicts, row.status);
        },
        //关闭打印箱签
        closePrintBoxLabel() {
            this.printBox = false;
            this.getList();
        },
        // 打印箱签显示窗口
        printBoxLabels(row) {
            this.orderInfo = row;
            this.printBox = true;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .el-radio:last-child {
        margin-right: 30px;
    }

    .el-radio {
        margin-bottom: 10px;

        .el-radio__label {
            .el-input__wrapper {
                background: none;
                box-shadow: none;
            }
        }
    }
}
</style>
