import http from "@/utils/request";
export default {
    // 查询出库登记列表数据
    findOutboundList: function (params) {
        return http.get('/staple/outbreg/findOutboundList', params)
    },
    // 查询出库登记详情数据
    findRecordDetail: function (params) {
        return http.get('/staple/outbreg/findRecordDetail', params)
    },
    // 保存出库登记数据
    saveOutRecord: function (data) {
        return http.post('/staple/outbreg/saveOutRecord', data);
    },
    // 删除出库登记
    deleteRecord: function (params) {
        return http.delete("/staple/outbreg/deleteRecord", params);
    },
    // 删除出库登记详情
    deleteRecordDetail: function (params) {
        return http.delete("/staple/outbreg/deleteRecordDetail", params);
    },
    // 删除出库单
    deleteRecordOutCode: function (params) {
        return http.delete("/staple/outbreg/deleteRecordOutCode", params);
    },
    // 查询货主下的收货公司
    findAddressBookByCompany: function (params) {
        return http.get('/staple/outbreg/findAddressBookByCompany', params)
    },

};
