import {disappApi} from "@/api/model/commodity/disappear";

let data = JSON.parse(localStorage.getItem('dictionary_type')) || {}
export const typeList = async (num) => {
	if (!localStorage.getItem("dictionary_type")) {
		await disappApi.typeDrug({dictType: "product_basic_unit", size: 1000}).then((res) => {
			data.basicType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "product_zjdw", size: 1000}).then((res) => {
			data.wholeType = res.data.records;
		});
		await disappApi.natureDrug({"item.id": 8, size: 1000}).then((res) => {
			data.saveType = res.data.records;
		});
		await disappApi.natureDrug({"item.id": 10, size: 1000}).then((res) => {
			data.GSPType = res.data.records;
		})
		await disappApi.natureDrug({"item.id": 9, size: 1000}).then((res) => {
			data.ratifyType = res.data.records;
		});
		await disappApi.natureDrug({"item.id": 5, size: 1000}).then((res) => {
			data.recipeType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "yh_type", size: 1000}).then((res) => {
			data.maintainType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "product_wq", size: 1000}).then((res) => {
			data.storeType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "product_wqfw", size: 1000}).then((res) => {
			data.warmType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "product_ybfl", size: 1000}).then((res) => {
			data.healthType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "commodity_file_type", size: 1000}).then((res) => {
			data.fileType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "Instrument_file1", size: 1000}).then((res) => {
			data.InstrumentFileType1 = res.data.records;
		});
		await disappApi.typeDrug({dictType: "Instrument_file2", size: 1000}).then((res) => {
			data.InstrumentFileType2 = res.data.records;
		});
		await disappApi.typeDrug({dictType: "drugFile_type", size: 1000}).then((res) => {
			data.drugFileType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "disappear_filetupe", size: 1000}).then((res) => {
			data.disappearFiletype = res.data.records;
		});
		await disappApi.typeDrug({dictType: "food_filetype", size: 1000}).then((res) => {
			data.foodFileType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "product_man_type", size: 1000}).then((res) => {
			data.manageType = res.data.records;
		});
		await disappApi.typeDrug({dictType: "product_classify_code", size: 1000}).then((res) => {
			data.goodsType = res.data.records;
		});
		await disappApi
			.typeDrug({dictType: "wms_apparatus_quality_type", size: 1000})
			.then((res) => {
				data.conType = res.data.records;
			});
		localStorage.setItem('dictionary_type', JSON.stringify(data))
		return data
	} else {
		return JSON.parse(localStorage.getItem('dictionary_type'))
	}
}
