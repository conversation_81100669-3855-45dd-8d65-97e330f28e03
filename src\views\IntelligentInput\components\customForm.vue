<template>
  <div>
    <div v-if="formRow" style="overflow: auto;height: 600px; padding-right: 10px">
      <div v-if="props.formRow.split(',').indexOf('1') != -1">
        <span class="titleForm">基本信息</span>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="140px">
          <el-form-item label="企业名称" prop="enterpriseName">
            <el-input v-model="form.enterpriseName" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入企业名称" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('enterpriseName', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item :key="form.credentialType"
                        :prop="form.credentialType != '2' ? 'socialCreditCode' : undefined"
                        label="统一社会信用代码">
            <el-input v-model="form.socialCreditCode" :disabled="modalType == 'detail'"
                      clearable maxlength="100" placeholder="请输入统一社会信用代码" show-word-limit
                      style="width: 300px"
                      @blur="handleSocialCreditCodeBlur"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('socialCreditCode', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="资质类别" prop="credentialType">
            <el-select ref="credentialTypeRef" v-model="form.credentialType" :disabled="true"
                       placeholder="请选择资质类别" style="width: 300px"
                       @change="handleChange_credentialType" @focus="handleSelectFcous">
              <el-option v-for="item in qualificationList" :key="item.value" :label="item.name"
                         :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="业务类型" prop="tradeType">
            <el-select v-model="form.tradeType" :disabled="modalType == 'detail'" placeholder="请选择业务类型"
                       style="width: 300px">
              <el-option v-for="item in tradeType" :key="item.value" :label="item.name"
                         :value="item.value"/>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="拼音码" prop="pinyinCode" v-if="modalType == 'detail'">
              <el-input v-model="form.pinyinCode" placeholder="请输入拼音码"
                  :disabled="modalType == 'detail'" maxlength="100" show-word-limit clearable />
          </el-form-item> -->
          <el-form-item :rules="{
                                    pattern: form.credentialType === '1' ? /^KPF\d{5}(-\d{2})?$/ : form.credentialType === '2' ? /^KYL\d{5}(-\d{2})?$/ : form.credentialType === '3' ? /^KLS\d{5}(-\d{2})?$/ : null, message: form.credentialType === '1' ? '请输入KPFxxxxx或者KPFxxxxx-xx,其中xx为纯数字' : form.credentialType === '2' ? '请输入KYLxxxxx或者KYLxxxxx-xx,其中xx为纯数字' : form.credentialType === '3' ? '请输入KLSxxxxx或者KLSxxxxx-xx,其中xx为纯数字' : '', trigger: 'blur'
                                }" label="自编码" prop="selfCoding">
            <el-input v-model="form.selfCoding" clearable placeholder="请输入自编码" style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('selfCoding', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="营业期限" prop="businessTerm">
            <el-date-picker v-model="form.businessTerm" :disabled="modalType == 'detail'"
                            :disabled-date="disabledDate"
                            placeholder="请选择营业期限" style="width: 300px"
                            type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('businessTerm', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="住所" prop="residence">
            <el-input v-model="form.residence" :disabled="modalType == 'detail'" clearable maxlength="100"
                      placeholder="请输入住所" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('residence', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <!-- TODO -->
          <el-form-item label="经营范围" prop="businessScope">
            <el-input v-model="form.businessScope" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入经营范围" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('businessScope', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="发证机关" prop="issuingOffice ">
            <el-input v-model="form.issuingOffice" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入发证机关" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('issuingOffice', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input v-model="form.contactNumber" :disabled="modalType == 'detail'" clearable
                      placeholder="请输入联系电话"
                      style="width: 300px" type="number"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('contactNumber', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="经营方式" prop="natureBusiness">
            <el-select v-model="form.natureBusiness" :disabled="modalType == 'detail'" placeholder="请选择经营方式"
                       style="width: 300px"
            >
              <el-option v-for="item in modeOfList" :key="item.value" :label="item.name"
                         :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="经营类型" prop="businessType">
            <el-select v-model="form.businessType" :disabled="modalType == 'detail'" placeholder="请选择经营类型"
                       style="width: 300px">
              <el-option v-for="item in businessList" :key="item.value" :label="item.name"
                         :value="item.value"/>
            </el-select>
          </el-form-item>

          <el-form-item label="开户银行" prop="bankDeposit">
            <el-input v-model="form.bankDeposit" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入开户银行" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('bankDeposit', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="开户许可证号" prop="accountOpeningLicenseCode">
            <el-input v-model="form.accountOpeningLicenseCode" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入开户许可证号" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('accountOpeningLicenseCode', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="银行账号" prop="accountNumber">
            <el-input v-model="form.accountNumber" :disabled="modalType == 'detail'" clearable
                      placeholder="请输入银行账号"
                      style="width: 300px" type="number"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('accountNumber', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="发证日期" prop="issuingTime">
            <el-date-picker v-model="form.issuingTime" :disabled="modalType == 'detail'" placeholder="请选择发证日期"
                            style="width: 300px"
                            type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('issuingTime', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="开票名称" prop="billName">
            <el-input v-model="form.billName" :disabled="modalType == 'detail'"
                      clearable maxlength="100" placeholder="请输入开票名称" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('billName', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="开票地址" prop="billAddress">
            <el-input v-model="form.billAddress" :disabled="modalType == 'detail'"
                      clearable maxlength="100" placeholder="请输入开票地址" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('billAddress', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="所在区域" prop="provinces">
            <el-cascader ref="deliveryAddress" v-model="form.provinces" :disabled="modalType == 'detail'"
                         :options="sysAreasThird" clearable
                         filterable placeholder="请选择所在区域" style="width: 300px"
                         @change="addressChange"/>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" :disabled="modalType == 'detail'" :rows="1"
                      clearable maxlength="100" placeholder="请输入备注" show-word-limit style="width: 300px"
                      type="textarea"/>
          </el-form-item>

          <el-form-item v-if="form.credentialType == '1' || form.credentialType == '3'" label="经营范围配置"
                        prop="apparatusFirst">
            <el-checkbox v-model="form.apparatusFirst" label="1" name="type"
                         style="width: 300px">一类器械
            </el-checkbox>
            <el-input v-model="form.apparatusScopeName"
                      :disabled="!form.apparatusFirst" clearable
                      placeholder="请选择经营范围"
                      readonly style="width: 300px"
                      @click="() => handleLicenseScope(form, 'apparatusFirstScopes')"/>
            <el-checkbox v-model="form.sterilized" label="1" name="type"
                         style="width: 300px">消杀
            </el-checkbox>
            <el-input key="disinfectionAndSterilization" v-model="form.sterilizedScopeName"
                      :disabled="!form.sterilized" placeholder="请选择经营范围"
                      readonly style="width: 300px"
                      @click="() => handleLicenseScope(form, 'sterilizedScopes')">
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div
          v-if="props.formRow.split(',').indexOf('2') != -1||props.formRow.split(',').indexOf('7') != -1||props.formRow.split(',').indexOf('8') != -1||props.formRow.split(',').indexOf('9') != -1">
        <span class="titleForm">质量信息{{ echoMess() }}</span>
        <el-form v-if="form.credentialType !== '2'" ref="modalTableListRef"
                 :model="licensedata" :rules="tabKey == '1' ? modalTableListRules : modalTableListRules_" label-width="140px"
                 style="margin-top:20px"
        >
          <el-form-item label="许可证编号" prop="licenseNo">
            <el-input v-model="licensedata.licenseNo" :disabled="modalType == 'detail'" clearable maxlength="100"
                      placeholder="请输入许可证编号" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenseNo', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="注册地址" prop="licenseAddress">
            <el-input v-model="licensedata.licenseAddress" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入注册地址" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenseAddress', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="仓库地址" prop="warehouseAddress">
            <el-input v-model="licensedata.warehouseAddress" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入仓库地址" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('warehouseAddress', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>

          <el-form-item key="licenceScopesName" label="许可证范围" prop="licenceScopesName">
            <el-input key="licenceScopesName2``" v-model="licensedata.licenceScopesName" clearable
                      placeholder="请选择许可证范围" readonly style="width: 300px"
                      @click="() => handleLicenseScope(licensedata)"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenceScopesName', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="发证日期" prop="licenseStartTime">
            <el-date-picker v-model="licensedata.licenseStartTime" :disabled="modalType == 'detail'"
                            :disabled-date="disabledDateAfter" placeholder="请选择发证日期"
                            style="width: 300px"
                            type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenseStartTime', true, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="有效期" prop="licenseValidity">
            <el-date-picker v-model="licensedata.licenseValidity" :disabled="modalType == 'detail'"
                            :disabled-date="disabledDate" placeholder="请选择有效期"
                            style="width: 300px"
                            type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenseValidity', true, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="发证机关" prop="licenseOffice">
            <el-input v-model="licensedata.licenseOffice" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入发证机关" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenseOffice', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="法人" prop="licenseLegalPerson">
            <el-input v-model="licensedata.licenseLegalPerson" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入法人" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenseLegalPerson', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="企业负责人" prop="licenseDirector">
            <el-input v-model="licensedata.licenseDirector" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入企业负责人" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('licenseDirector', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="质量负责人" prop="qualityDirector">
            <el-input v-model="licensedata.qualityDirector" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输质量负责人" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('qualityDirector', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
        </el-form>
        <el-form v-if="form.credentialType == '2'" ref="modalTableListRef_2" :model="form" :rules="rules"
                 label-width="140px" style="margin-top: 20px;">
          <el-form-item label="执业许可证名称" prop="zylicenseName">
            <el-input v-model="form.zylicenseName" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入执业许可证名称" show-word-limit style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseName', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="许可证地址" prop="zylicenseAddress">
            <el-input v-model="form.zylicenseAddress" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入许可证地址" show-word-limit style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseAddress', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="经营性质" prop="business">
            <el-select v-model="form.business" :disabled="modalType == 'detail'" clearable
                       placeholder="请选择经营性质" style="width:300px">
              <el-option v-for="item in businessNatureList" :key="item.value" :label="item.name"
                         :value="item.value"/>
            </el-select>
          </el-form-item>

          <el-form-item label="法人" prop="zylicenseLegalPerson">
            <el-input v-model="form.zylicenseLegalPerson" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入法人" show-word-limit style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseLegalPerson', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="负责人" prop="zylicenseDirector">
            <el-input v-model="form.zylicenseDirector" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入负责人" show-word-limit style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseDirector', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="发证日期" prop="zylicenseStartTime">
            <el-date-picker v-model="form.zylicenseStartTime" :disabled="modalType == 'detail'"
                            placeholder="请选择发证日期"
                            style="width:300px" type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseStartTime', true, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="有效期" prop="zylicenseValidity">
            <el-date-picker v-model="form.zylicenseValidity" :disabled="modalType == 'detail'"
                            :disabled-date="disabledDate"
                            clearable placeholder="请选择有效期" style="width:300px"
                            type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseValidity', true, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="登记号码" prop="zylicenseNo">
            <el-input v-model="form.zylicenseNo" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入登记号码" show-word-limit style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseNo', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="发证机关" prop="zylicenseOffice">
            <el-input v-model="form.zylicenseOffice" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入发证机关" show-word-limit style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('zylicenseOffice', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="诊疗科目" prop="zylicenseProject">
            <el-tree-select v-model="form.zylicenseProject" :data="zylicenseProjectOption"
                            :render-after-expand="false" class="selectTree" clearable multiple show-checkbox
                            style="width:300px"/>
          </el-form-item>
          <el-form-item label=" ">
            <el-tree-select v-model="form.zylicenseRange" :data="massRangeOption" :popper-append-to-body="false"
                            :render-after-expand="false" class="selectTree" clearable multiple
                            show-checkbox style="width:300px"/>
          </el-form-item>

          <!-- zylicenseProject:[{id:id,ids:ids}]  -->
        </el-form>
      </div>
      <div v-if="props.formRow.split(',').indexOf('3') != -1">
        <span class="titleForm">采购授权委托书</span>
        <el-form ref="entrust_formRef" :model="entrust" :rules="entrust_rules" label-width="140px"
                 style="margin-top: 20px">
          <el-form-item label="类型" prop="status">
            <el-radio-group v-model="entrust.status">
              <el-radio v-for="item in drugTypeList" :key="item.value" :label="item.value">{{
                  item.name
                }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="委托人姓名" prop="delegateName">
            <el-input v-model="entrust.delegateName" :disabled="modalType == 'detail'" clearable maxlength="100"
                      placeholder="请输入委托人姓名" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('delegateName', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="身份证号" prop="certificateCard">
            <el-input v-model="entrust.certificateCard" :disabled="modalType == 'detail'" clearable maxlength="100"
                      placeholder="请输入身份证号" show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('certificateCard', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="有效期" prop="effectiveTime">
            <el-date-picker v-model="entrust.effectiveTime" :disabled="modalType == 'detail'"
                            :disabled-date="disabledDate" placeholder="请选择有效期"
                            style="width: 300px" type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('effectiveTime', true, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="授权日期" prop="empowerTime">
            <el-date-picker v-model="entrust.empowerTime" :disabled="modalType == 'detail'" placeholder="请选择授权日期"
                            style="width: 300px" type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('empowerTime', true, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="区域限制" prop="addressList">
            <el-select v-model="entrust.addressList" clearable filterable multiple placeholder="请选择省份"
                       style="width: 300px">
              <el-option v-for="item in sysAreas" :key="item.value" :disabled="modalType == 'detail'"
                         :label="item.label"
                         :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="委托商品范围" prop="isEntrustAll">
            <el-radio-group v-model="entrust.isEntrustAll">
              <el-radio label="1">所有{{ formDict(drugTypeList, entrust.status) }}药品</el-radio>
              <el-radio label="2">部分商品</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="委托人身份证" prop="delegateCard">
            <el-upload ref="delegateCardRef" v-model:file-list="entrust.delegateCard" :action="uploadUrl"
                       :before-upload="(file) => beforeFile(file)"
                       :data="{ fileType: 'image', fjType: '委托人身份证', zhType: '客户' }"
                       :disabled="modalType == 'detail'" :headers='headers'
                       :limit="1" :on-exceed="(files) => handleExceed(files, delegateCardRef)"
                       :on-preview="handlePictureCardPreview"
                       :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 6)"
                       class="upload-demo"
                       drag
                       list-type="picture-card">
              <el-icon>
                <Plus/>
              </el-icon>
            </el-upload>
          </el-form-item>
        </el-form>
        <div v-show="entrust.isEntrustAll == '2'">
          <el-input v-model="modalentrustOut.commonValue" :disabled="modalType == 'detail'"
                    clearable
                    placeholder="请输入商品名或自编码" style="width:60%"/>
          <el-button style="margin-left:10px" type="primary" @click="handleSearchShop_entrust">搜索商品</el-button>
          <el-button :disabled="!chooseList_entrust_side_table_list.length" style="margin-left:10px" type="danger"
                     @click="handleDelte_entrust_out">删除
          </el-button>
        </div>
        <!-- 表格数据 -->
        <el-table v-show="entrust.isEntrustAll == '2'" :data="entrust_side_table_list" border
                  style="margin-top: 30px;" @selection-change="handleSelectionChange_side_table_list">
          <el-table-column align="center" min-width="55" type="selection"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="自编码" min-width="120"
                           prop="commoditySelfCode"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="商品名" min-width="120"
                           prop="tradeName"/>
          <el-table-column :formatter="(row) => commodityTypeDict[row.commodityType]" :show-overflow-tooltip="true"
                           align="center" label="分类"
                           min-width="120" prop="commodityType"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="剂型" min-width="120"
                           prop="dosageForm"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="规格" min-width="120"
                           prop="packageSpecification"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="基本单位" min-width="140"
                           prop="basicUnit"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" min-width="120"
                           prop="manufacture.enterpriseName"/>
          <el-table-column align="center" label="生产地址" min-width="120" prop="originPlace"/>

        </el-table>
      </div>
      <div v-if="props.formRow.split(',').indexOf('4') != -1">
        <span class="titleForm">质量保证协议书</span>
        <el-form ref="warranty_formRef" :model="warranty" :rules="warranty_rules" label-width="140px"
                 style="margin-top: 20px">
          <!-- <div class="box"> -->
          <el-form-item label="有效期" prop="qualityDate">
            <el-date-picker v-model="warranty.qualityDate" :disabled="modalType == 'detail'"
                            :disabled-date="disabledDate"
                            placeholder="请选择有效期" style="width: 300px;" type="date"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('qualityDate', true, 4)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <!-- <el-form-item label="编号" prop="warranty_code">
                  <el-input v-model="warranty.businessLicenseCode" placeholder="请输入编号"
                      :disabled="modalType == 'detail'" maxlength="100" show-word-limit clearable />
              </el-form-item> -->
          <!-- </div> -->
        </el-form>
      </div>
      <div v-if="props.formRow.split(',').indexOf('5') != -1">
        <span class="titleForm">GSP证书</span>
        <el-form ref="formRef" label-width="140px">
          <el-form-item label="证书编号">
            <el-input v-model="modalTableList_GMPRef.gmpCertificateNo" clearable maxlength="100"
                      placeholder="请输入证书编号"
                      show-word-limit style="width: 300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon style="font-size: 20px;margin-left: 7px" @click="iconBtn('gmpCertificateNo', false, 5)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="证书地址">
            <el-input v-model="modalTableList_GMPRef.gmpCertificateAddress" clearable maxlength="100"
                      placeholder="请输入证书地址"
                      show-word-limit style="width: 300px" @blur="handleSocialCreditCodeBlur"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon style="font-size: 20px;margin-left: 7px" @click="iconBtn('gmpCertificateAddress', false, 5)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="证书有效期">
            <el-date-picker v-model="modalTableList_GMPRef.gmpExpiredTime" :disabled-date="disabledDateAfter"
                            placeholder="请选择证书有效期" style="width: 300px" type="date"/>

            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon style="font-size: 20px;margin-left: 7px" @click="iconBtn('gmpExpiredTime', true, 5)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="证书授权范围">
            <el-select v-model="modalTableList_GMPRef.gmpCertificateScope"
                       filterable multiple placeholder="请选择证书授权范围"
                       style="width:300px">
              <el-option v-for=" (item)  in  licenseScopeList " :key="item.id"
                         :label="item.valueName" :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <el-empty
          v-if="props.formRow.split(',').indexOf('6') != -1 && props.formRow.split(',').length==1"
          description="暂无对应表单"/>
    </div>
    <div v-if="!formRow" style="padding: 20px">
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item name="1" title="基本信息">
          <template #title>
            <span class="col_title">基本信息</span>
          </template>
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto"
                   style="margin-top: 0px;padding-right: 20px;">
            <div class="box_2">
              <el-form-item label="企业名称" prop="enterpriseName">
                <el-input v-model="form.enterpriseName" :disabled="modalType == 'detail'" clearable
                          maxlength="100" placeholder="请输入企业名称" show-word-limit/>
              </el-form-item>
              <el-form-item :key="form.credentialType"
                            :prop="form.credentialType != '2' ? 'socialCreditCode' : undefined"
                            label="统一社会信用代码">
                <el-input v-model="form.socialCreditCode" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入统一社会信用代码" show-word-limit
                          @blur="handleSocialCreditCodeBlur"/>
              </el-form-item>
            </div>
            <div class="box">
              <el-form-item label="资质类别" prop="credentialType">
                <el-select ref="credentialTypeRef" v-model="form.credentialType" :disabled="true"
                           placeholder="请选择资质类别" style="width:100%"
                           @change="handleChange_credentialType" @focus="handleSelectFcous">
                  <el-option v-for="item in qualificationList" :key="item.value" :label="item.name"
                             :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="业务类型" prop="tradeType">
                <el-select v-model="form.tradeType" :disabled="modalType == 'detail'" placeholder="请选择业务类型"
                           style="width:100%">
                  <el-option v-for="item in tradeType" :key="item.value" :label="item.name"
                             :value="item.value"/>
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="拼音码" prop="pinyinCode" v-if="modalType == 'detail'">
                  <el-input v-model="form.pinyinCode" placeholder="请输入拼音码"
                      :disabled="modalType == 'detail'" maxlength="100" show-word-limit clearable />
              </el-form-item> -->
              <el-form-item :rules="{
                                    pattern: form.credentialType === '1' ? /^KPF\d{5}(-\d{2})?$/ : form.credentialType === '2' ? /^KYL\d{5}(-\d{2})?$/ : form.credentialType === '3' ? /^KLS\d{5}(-\d{2})?$/ : null, message: form.credentialType === '1' ? '请输入KPFxxxxx或者KPFxxxxx-xx,其中xx为纯数字' : form.credentialType === '2' ? '请输入KYLxxxxx或者KYLxxxxx-xx,其中xx为纯数字' : form.credentialType === '3' ? '请输入KLSxxxxx或者KLSxxxxx-xx,其中xx为纯数字' : '', trigger: 'blur'
                                }" label="自编码" prop="selfCoding">
                <el-input v-model="form.selfCoding" clearable placeholder="请输入自编码"/>
              </el-form-item>
              <el-form-item label="营业期限" prop="businessTerm">
                <el-date-picker v-model="form.businessTerm" :disabled="modalType == 'detail'"
                                :disabled-date="disabledDate"
                                placeholder="请选择营业期限" style="width: 100%;"
                                type="date"/>
              </el-form-item>
              <el-form-item label="住所" prop="residence">
                <el-input v-model="form.residence" :disabled="modalType == 'detail'" clearable
                          maxlength="100" placeholder="请输入住所" show-word-limit/>
              </el-form-item>
              <!-- TODO -->
              <el-form-item label="经营范围" prop="businessScope">
                <el-input v-model="form.businessScope" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入经营范围" show-word-limit/>
              </el-form-item>
              <el-form-item label="发证机关" prop="issuingOffice ">
                <el-input v-model="form.issuingOffice" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入发证机关" show-word-limit/>
              </el-form-item>
              <el-form-item label="联系电话" prop="contactNumber">
                <el-input v-model="form.contactNumber" :disabled="modalType == 'detail'" clearable
                          placeholder="请输入联系电话" type="number"/>
              </el-form-item>
              <el-form-item label="经营方式" prop="natureBusiness">
                <el-select v-model="form.natureBusiness" :disabled="modalType == 'detail'" placeholder="请选择经营方式"
                           style="width:100%">
                  <el-option v-for="item in modeOfList" :key="item.value" :label="item.name"
                             :value="item.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="经营类型" prop="businessType">
                <el-select v-model="form.businessType" :disabled="modalType == 'detail'" placeholder="请选择经营类型"
                           style="width:100%">
                  <el-option v-for="item in businessList" :key="item.value" :label="item.name"
                             :value="item.value"/>
                </el-select>
              </el-form-item>

              <el-form-item label="开户银行" prop="bankDeposit">
                <el-input v-model="form.bankDeposit" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入开户银行" show-word-limit/>
              </el-form-item>
              <el-form-item label="开户许可证号" prop="accountOpeningLicenseCode">
                <el-input v-model="form.accountOpeningLicenseCode" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入开户许可证号" show-word-limit/>
              </el-form-item>
              <el-form-item label="银行账号" prop="accountNumber">
                <el-input v-model="form.accountNumber" :disabled="modalType == 'detail'" clearable
                          placeholder="请输入银行账号" type="number"/>
              </el-form-item>
              <el-form-item label="发证日期" prop="issuingTime">
                <el-date-picker v-model="form.issuingTime" :disabled="modalType == 'detail'"
                                placeholder="请选择发证日期"
                                style="width: 100%;" type="date"/>
              </el-form-item>
              <el-form-item label="开票名称" prop="billName">
                <el-input v-model="form.billName" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入开票名称" show-word-limit/>
              </el-form-item>
              <el-form-item label="开票地址" prop="billAddress">
                <el-input v-model="form.billAddress" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入开票地址" show-word-limit/>
              </el-form-item>
              <el-form-item label="所在区域" prop="provinces">
                <el-cascader ref="deliveryAddress" v-model="form.provinces" :disabled="modalType == 'detail'"
                             :options="sysAreasThird"
                             clearable filterable placeholder="请选择所在区域"
                             style="width: 100%" @change="addressChange"/>
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" :disabled="modalType == 'detail'" :rows="1"
                          clearable maxlength="100" placeholder="请输入备注" show-word-limit
                          type="textarea"/>
              </el-form-item>

            </div>
            <div style="display:flex">
              <el-form-item v-if="form.credentialType == '1' || form.credentialType == '3'" label="经营范围配置"
                            prop="apparatusFirst">
                <div class="checkBox">
                  <el-checkbox v-model="form.apparatusFirst" label="1"
                               name="type">一类器械
                  </el-checkbox>
                </div>
                <div class="checkBoxRight">
                  <el-input v-model="form.apparatusScopeName"
                            :disabled="!form.apparatusFirst"
                            clearable
                            placeholder="请选择经营范围" readonly style="width:100%"
                            @click="() => handleLicenseScope(form, 'apparatusFirstScopes')"/>
                </div>
                <div class="checkBox">
                  <el-checkbox v-model="form.sterilized" label="1"
                               name="type">消杀
                  </el-checkbox>
                </div>
                <div class="checkBoxRight">
                  <el-input key="disinfectionAndSterilization" v-model="form.sterilizedScopeName"
                            :disabled="!form.sterilized"
                            placeholder="请选择经营范围" readonly
                            style="width:100%" @click="() => handleLicenseScope(form, 'sterilizedScopes')">
                  </el-input>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </el-collapse-item>
        <el-collapse-item class="step2" name="2" title="质量信息" @click="handleChange_msg">
          <template #title>
            <span class="col_title">质量信息</span>
          </template>
          <div v-if="form.credentialType !== '2'" class="btn_cmt">
            <el-button v-if="chooseTypeFn('2')" :type="tabKey == '1' ? 'primary' : 'default'"
                       @click="() => handleTabClick('1')">
              <el-icon>
                <Plus/>
              </el-icon>
              <span>药品</span></el-button>

            <el-button v-if="chooseTypeFn('7')" :type="tabKey == '4' ? 'primary' : 'default'"
                       @click="() => handleTabClick('4')">
              <el-icon>
                <Plus/>
              </el-icon>
              <span>二类</span></el-button>
            <el-button v-if="chooseTypeFn('8')" :type="tabKey == '2' ? 'primary' : 'default'"
                       @click="() => handleTabClick('2')">
              <el-icon>
                <Plus/>
              </el-icon>
              <span>三类</span></el-button>
            <el-button v-if="chooseTypeFn('9')" :class="form.credentialType !== '2' ? 'last-btn' : null"
                       :type="tabKey == '5' ? 'primary' : 'default'"
                       @click="() => handleTabClick('5')">
              <el-icon>
                <Plus/>
              </el-icon>
              <span>食品</span></el-button>
          </div>
          <el-form v-if="form.credentialType !== '2'" ref="modalTableListRef"
                   :model="licensedata"
                   :rules="tabKey == '1' ? modalTableListRules : modalTableListRules_" label-width="auto"
                   style="margin-top: 0px;padding-right: 20px;">
            <div class="box">
              <el-form-item label="许可证编号" prop="licenseNo">
                <el-input v-model="licensedata.licenseNo" :disabled="modalType == 'detail'" clearable
                          maxlength="100" placeholder="请输入许可证编号" show-word-limit/>
              </el-form-item>
              <el-form-item label="注册地址" prop="licenseAddress">
                <el-input v-model="licensedata.licenseAddress" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入注册地址" show-word-limit/>
              </el-form-item>
              <el-form-item label="仓库地址" prop="warehouseAddress">
                <el-input v-model="licensedata.warehouseAddress" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入仓库地址" show-word-limit/>
              </el-form-item>

              <el-form-item key="licenceScopesName" label="许可证范围" prop="licenceScopesName">
                <el-input key="licenceScopesName2``" v-model="licensedata.licenceScopesName"
                          clearable placeholder="请选择许可证范围" readonly
                          @click="() => handleLicenseScope(licensedata)"/>
              </el-form-item>

              <el-form-item label="发证日期" prop="licenseStartTime">
                <el-date-picker v-model="licensedata.licenseStartTime" :disabled="modalType == 'detail'"
                                :disabled-date="disabledDateAfter"
                                placeholder="请选择发证日期" style="width: 100%;"
                                type="date"/>
              </el-form-item>

              <el-form-item label="有效期" prop="licenseValidity">
                <el-date-picker v-model="licensedata.licenseValidity" :disabled="modalType == 'detail'"
                                :disabled-date="disabledDate"
                                placeholder="请选择有效期" style="width: 100%;"
                                type="date"/>
              </el-form-item>
              <el-form-item label="发证机关" prop="licenseOffice">
                <el-input v-model="licensedata.licenseOffice" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入发证机关" show-word-limit/>
              </el-form-item>
              <el-form-item label="法人" prop="licenseLegalPerson">
                <el-input v-model="licensedata.licenseLegalPerson" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入法人" show-word-limit/>
              </el-form-item>

              <el-form-item label="企业负责人" prop="licenseDirector">
                <el-input v-model="licensedata.licenseDirector" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入企业负责人" show-word-limit/>
              </el-form-item>
              <el-form-item label="质量负责人" prop="qualityDirector">
                <el-input v-model="licensedata.qualityDirector" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输质量负责人" show-word-limit/>
              </el-form-item>
            </div>

            <el-form-item label="许可证图片" prop="licenseImg">
              <el-upload ref="licenseImgRef" v-model:file-list="licensedata.licenseImg" :action="uploadUrl"
                         :before-upload="(file) => beforeFile(file)"
                         :data="{ fileType: 'image', fjType: '质量信息', zhType: '客户' }"
                         :disabled="true" :headers='headers'
                         :on-exceed="(files) => handleExceed(files, licenseImgRef)"
                         :on-preview="handlePictureCardPreview"
                         :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 1)"
                         class="upload-demo" drag
                         list-type="picture-card"
                         multiple>
                <el-icon>
                  <Plus/>
                </el-icon>
              </el-upload>

            </el-form-item>
          </el-form>
          <el-form v-if="form.credentialType == '2'" ref="modalTableListRef_2" :model="form" :rules="rules"
                   label-width="auto" style="margin-top: 0px;padding-right: 20px;">
            <div class="box">

              <el-form-item label="执业许可证名称" prop="zylicenseName">
                <el-input v-model="form.zylicenseName" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入执业许可证名称" show-word-limit/>
              </el-form-item>
              <el-form-item label="许可证地址" prop="zylicenseAddress">
                <el-input v-model="form.zylicenseAddress" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入许可证地址" show-word-limit/>
              </el-form-item>
              <el-form-item label="经营性质" prop="business">
                <el-select v-model="form.business" :disabled="modalType == 'detail'" clearable
                           placeholder="请选择经营性质" style="width:100%">
                  <el-option v-for="item in businessNatureList" :key="item.value" :label="item.name"
                             :value="item.value"/>
                </el-select>
              </el-form-item>

              <el-form-item label="法人" prop="zylicenseLegalPerson">
                <el-input v-model="form.zylicenseLegalPerson" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入法人" show-word-limit/>
              </el-form-item>
              <el-form-item label="负责人" prop="zylicenseDirector">
                <el-input v-model="form.zylicenseDirector" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入负责人" show-word-limit/>
              </el-form-item>
              <el-form-item label="发证日期" prop="zylicenseStartTime">
                <el-date-picker v-model="form.zylicenseStartTime" :disabled="modalType == 'detail'"
                                placeholder="请选择发证日期"
                                style="width: 100%;" type="date"/>
              </el-form-item>
              <el-form-item label="有效期" prop="zylicenseValidity">
                <el-date-picker v-model="form.zylicenseValidity" :disabled="modalType == 'detail'"
                                :disabled-date="disabledDate"
                                clearable placeholder="请选择有效期" style="width: 100%;"
                                type="date"/>
              </el-form-item>
              <el-form-item label="登记号码" prop="zylicenseNo">
                <el-input v-model="form.zylicenseNo" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入登记号码" show-word-limit/>
              </el-form-item>
              <el-form-item label="发证机关" prop="zylicenseOffice">
                <el-input v-model="form.zylicenseOffice" :disabled="modalType == 'detail'"
                          clearable maxlength="100" placeholder="请输入发证机关" show-word-limit/>
              </el-form-item>
              <el-form-item label="诊疗科目" prop="zylicenseProject" style="width:200%">
                <div style="width: 50%;">
                  <el-tree-select v-model="form.zylicenseProject" :data="zylicenseProjectOption"
                                  :render-after-expand="false" class="selectTree" clearable multiple show-checkbox
                                  style="width:100%"/>
                </div>
                <div style="width: 50%;">
                  <el-tree-select v-model="form.zylicenseRange" :data="massRangeOption" :popper-append-to-body="false"
                                  :render-after-expand="false" class="selectTree" clearable multiple
                                  show-checkbox style="width:100%"/>
                </div>

              </el-form-item>

              <!-- zylicenseProject:[{id:id,ids:ids}]  -->
            </div>
          </el-form>
        </el-collapse-item>
        <el-collapse-item name="3" title="采购授权委托书" @click="handleChange_msg">
          <template #title>
            <span class="col_title">采购授权委托书</span>
          </template>
          <!--          <div style="margin-bottom: 20px;">-->
          <!--            <el-button :disabled="modalType == 'detail'" type="primary"-->
          <!--                       @click="() => handleAdd_entrust()">新增-->
          <!--            </el-button>-->
          <!--            <el-button :disabled="modalType == 'detail' || !chooseList_entrust.length" type="danger"-->
          <!--                       @click="handleDelete_entrust">删除-->
          <!--            </el-button>-->
          <!--          </div>-->
          <el-table ref="multipleTableRef_entrust" :data="modalTableList_entrust"
                    border @selection-change="handleSelectionChange_entrust">
            <!--            <el-table-column align="center" min-width="55" type="selection"/>-->
            <el-table-column align="center" label="序号" min-width="80">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="委托人姓名" min-width="100"
                             prop="delegateName">
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="身份证号" min-width="100"
                             prop="certificateCard">
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="有效期" min-width="100">
              <template #default="scope">
                <p>{{
                    (scope.row.effectiveTime ?
                        moment(scope.row.effectiveTime).format('YYYY-MM-DD') :
                        undefined) || '--'
                  }}</p>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="授权日期" min-width="100">
              <template #default="scope">
                <p>{{
                    (scope.row.empowerTime ?
                        moment(scope.row.empowerTime).format('YYYY-MM-DD') :
                        undefined) || '--'
                  }}</p>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="区域限制" min-width="100"
                             prop="addressList">
              <template #default="scope">
                <p>{{
                    filterAddress(scope.row.addressList) || '--'
                  }}</p>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="委托书" min-width="100">
              <template #default="scope">
                <el-button link type="primary"
                           @click="() => handlePictureCardPreview(scope.row.remark[0].url)">{{
                    scope.row.remark[0].name
                    || '--'
                  }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" fixed="right" label="操作"
                             min-width="100">
              <template #default="scope">
                <el-button :disabled="modalType == 'detail'" icon="el-icon-edit" link type="primary"
                           @click="handleAdd_entrust(scope.row)">查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
        <el-collapse-item name="4" title="质量保证协议书" @click="handleChange_msg">
          <template #title>
            <span class="col_title">质量保证协议书</span>
          </template>
          <!--          <div style="margin-bottom: 20px;">-->
          <!--            <el-button :disabled="modalType == 'detail'" type="primary"-->
          <!--                       @click="() => handleAdd_warranty()">新增-->
          <!--            </el-button>-->
          <!--            <el-button :disabled="modalType == 'detail' || !chooseList_warranty.length" type="danger"-->
          <!--                       @click="handleDelete_warranty">删除-->
          <!--            </el-button>-->
          <!--          </div>-->
          <el-table ref="multipleTableRef_warranty" :data="modalTableList_warranty"
                    border @selection-change="handleSelectionChange_warranty">
            <!--            <el-table-column align="center" min-width="55" type="selection"/>-->
            <el-table-column align="center" label="序号" min-width="80">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="协议编号" align="center" :show-overflow-tooltip="true"
                min-width="100"></el-table-column> -->
            <el-table-column :show-overflow-tooltip="true" align="center" label="有效期" min-width="100">
              <template #default="scope">
                <p>{{
                    (scope.row.qualityDate ?
                        moment(scope.row.qualityDate).format('YYYY-MM-DD') :
                        undefined) || '--'
                  }}</p>
              </template>
            </el-table-column>
            <!-- <el-table-column label="类型" align="center" min-width="100"
                :show-overflow-tooltip="true"></el-table-column> -->
            <el-table-column :show-overflow-tooltip="true" align="center" label="上传图片" min-width="100">
              <template #default="scope">
                <el-button link type="primary"
                           @click="() => handlePictureCardPreview(scope.row.qualityCode[0].url)">{{
                    scope.row.qualityCode[0].name
                    || '--'
                  }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="操作" min-width="100">
              <template #default="scope">
                <el-button :disabled="modalType == 'detail'" icon="el-icon-edit" link
                           type="primary"
                           @click="handleAdd_warranty(scope.row)">查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
        <el-collapse-item name="5" title="GSP证书" @click="handleChange_msg">
          <template #title>
            <span class="col_title">GSP证书</span>
          </template>
          <div>
            <div style="margin-bottom: 20px;">
              <!--              <el-button :disabled="modalType == 'detail'" type="primary"-->
              <!--                         @click="handleAdd_gsp">新增-->
              <!--              </el-button>-->
              <!--              <el-button :disabled="modalType == 'detail' || !chooseList_GMP.length" type="danger"-->
              <!--                         @click="handleDelete_gsp">删除-->
              <!--              </el-button>-->
            </div>
            <el-table ref="multipleTableRef_GMP" :data="modalTableList_GMP"
                      border @selection-change="handleSelectionChange_GMP">
              <el-table-column v-if="!ocrFlag" align="center" min-width="55" type="selection"/>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书编号" min-width="100">
                <template #default="scope">
                  <div v-clickOutside="() => handleClickOutside(scope, 'isShowgmpCertificateNo')">
                    <p v-show="!scope.row.isShowgmpCertificateNo"
                       style="height: 42px;line-height:42px"
                       @click="handleInputEdit(scope, 'gmpCertificateNo')">
                      {{ scope.row.gmpCertificateNo || '请输入证书编号' }}</p>
                    <el-input v-show="scope.row.isShowgmpCertificateNo" v-model="scope.row.gmpCertificateNo"
                              clearable maxlength="100" placeholder="请输入证书编号"
                              show-word-limit/>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书地址" min-width="100">
                <template #default="scope">
                  <div
                      v-clickOutside="() => handleClickOutside(scope, 'isShowgmpCertificateAddress')">
                    <p v-show="!scope.row.isShowgmpCertificateAddress"
                       style="height: 42px;line-height:42px"
                       @click="handleInputEdit(scope, 'gmpCertificateAddress')">
                      {{ scope.row.gmpCertificateAddress || '请输入证书地址' }}</p>
                    <el-input v-show="scope.row.isShowgmpCertificateAddress" v-model="scope.row.gmpCertificateAddress"
                              clearable maxlength="100"
                              placeholder="请输入证书地址" show-word-limit/>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书图片" min-width="100">
                <template #default="scope">
                  <div style="display:flex;justify-content: center;">
                    <p
                       style="color:#2A76F8;cursor: pointer;width:150px;line-height:42px;white-space: nowrap;overflow:hidden;text-overflow: ellipsis;"
                       @click="() => handlePictureCardPreview(scope.row.gmpCertificatePicture[0].url)">
                      {{
                        scope.row.gmpCertificatePicture[0].name
                      }}
                    </p>
                    <!--                    <el-upload v-if="modalType !== 'detail'" ref="gmpCertificatePictureRef"-->
                    <!--                               v-model:file-list="scope.row.gmpCertificatePicture" :action="uploadUrl"-->
                    <!--                               :before-upload="(file) => beforeFile(file)"-->
                    <!--                               :data="{ fileType: 'image', fjType: 'GSP证书', zhType: '客户管理' }"-->
                    <!--                               :headers='headers'-->
                    <!--                               :limit="1" :on-exceed="(files) => handleExceed(files, gmpCertificatePictureRef)"-->
                    <!--                               :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, scope.$index, 5)"-->
                    <!--                               :show-file-list="false"-->
                    <!--                               accept=".jpg,.jpeg,.png,.gif.JPG,.JPEG,.PNG"-->
                    <!--                               class="upload-demo">-->
                    <!--                      <template #trigger>-->

                    <!--                        <el-button type="primary">-->
                    <!--                          <el-icon style="margin-right:5px;">-->
                    <!--                            <UploadFilled/>-->
                    <!--                          </el-icon>-->
                    <!--                          上传-->
                    <!--                        </el-button>-->
                    <!--                      </template>-->
                    <!--                    </el-upload>-->
                  </div>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书有效期" min-width="100">
                <template #default="scope">
                  <div v-clickOutside="() => handleClickOutside(scope, 'isShowgmpExpiredTime')">
                    <p v-show="!scope.row.isShowgmpExpiredTime"
                       style="height: 42px;line-height:42px"
                       @click="handleInputEdit(scope, 'gmpExpiredTime')">
                      {{
                        (scope.row.gmpExpiredTime ?
                            moment(scope.row.gmpExpiredTime).format('YYYY-MM-DD') :
                            undefined) || '请选择有效期'
                      }}</p>
                    <el-date-picker v-show="scope.row.isShowgmpExpiredTime"
                                    v-model="scope.row.gmpExpiredTime" :disabled-date="disabledDate"
                                    :style="[{ width: '100%' }, { display: scope.row.isShowgmpExpiredTime ? 'block' : 'none' }]"
                                    clearable
                                    placeholder="请选择有效期" type="date"/>
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="证书授权范围" min-width="100">
                <template #default="scope">
                  <div
                      v-clickOutside="($event) => handleClickOutside(scope, 'isShowgmpCertificateScope', $event)">
                    <p v-show="!scope.row.isShowgmpCertificateScope"
                       style="height: 42px;line-height: 42px;"
                       @click="handleInputEdit(scope, 'gmpCertificateScope')">
                      {{
                        filterArr(licenseScopeList, 'id', scope.row.gmpCertificateScope) ||
                        '请选择证书授权范围'
                      }} </p>
                    <div v-show="scope.row.isShowgmpCertificateScope">
                      <el-select v-model="scope.row.gmpCertificateScope"
                                 :popper-append-to-body="false" multiple placeholder="请选择证书授权范围"
                                 popper-class="selectClass" style="width:100%">
                        <el-option v-for=" item  in  licenseScopeList " :key="item.id"
                                   :label="item.valueName" :value="item.id"/>
                      </el-select>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item name="6" title="附件上传" @click="handleChange_msg">
          <template #title>
            <span class="col_title">附件上传</span>
          </template>
          <!--          <div style="margin-bottom: 20px;">-->
          <!--            <el-button :disabled="modalType == 'detail'" type="primary"-->
          <!--                       @click="handleGetFileList">获取附件列表-->
          <!--            </el-button>-->
          <!--          </div>-->
          <el-table ref="multipleTableRef_file" v-loading="file_loading" :data="modalTableList_file" border>
            <el-table-column align="center" label="序号" width="50">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="类型" width="150">
              <template #default="scope">
                <span>{{ formDict(manufacturerType, scope.row.smallType) }}</span>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="文件类型" min-width="250">
              <template #default="scope">
                <span>{{ formDict(directoryFileName, scope.row.categoryName) }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="文件名称" width="400">
              <template #default="scope">
                <div>
                  <div v-if="scope.row.fileList">
                                            <span v-for="(item, index) in scope.row.fileList" :key="item.uid"
                                                  class="fileName_t"><span
                                                @click="() => handlePictureCardPreview(scope.row.fileList[index].url)">{{
                                                item.name
                                              }}</span></span>
                  </div>
                  <span v-else>请上传</span>
                </div>

              </template>
            </el-table-column>
            <!--            <el-table-column align="center" label="文件上传" width="130">-->
            <!--              <template #default="scope">-->
            <!--                <el-upload v-model:file-list="scope.row.fileList" :action="uploadUrl"-->
            <!--                           :before-upload="(file) => beforeFile(file)"-->
            <!--                           :data="{ fileType: 'image', fjType: formDict(directoryFileName, scope.row.categoryName), zhType: '客户管理', smallClass: scope.row.smallType }"-->
            <!--                           :headers='headers'-->
            <!--                           :limit="scope.row.isMultiPage === '1' ? 999 : 1"-->
            <!--                           :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, scope.$index, 4)"-->
            <!--                           :show-file-list="false" accept=".jpg,.jpeg,.png,.gif.JPG,.JPEG,.PNG"-->
            <!--                           class="upload-demo">-->
            <!--                  <template #trigger>-->
            <!--                    <el-button type="primary">-->
            <!--                      <el-icon style="margin-right:5px;">-->
            <!--                        <UploadFilled/>-->
            <!--                      </el-icon>-->
            <!--                      上传-->
            <!--                    </el-button>-->
            <!--                  </template>-->
            <!--                </el-upload>-->
            <!--              </template>-->
            <!--            </el-table-column>-->
            <el-table-column :show-overflow-tooltip="true" align="center" label="是否必传" width="100">
              <template #default="scope">
                                    <span>{{
                                        scope.row.isUpload === '1' ? '是' : scope.row.isUpload === '0' ? '否' : '--'
                                      }}</span>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="是否多张" width="100">
              <template #default="scope">
                                    <span>{{
                                        scope.row.isMultiPage === '1' ? '是' : scope.row.isMultiPage === '0' ? '否' :
                                            '--'
                                      }}</span>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" align="center" label="备注" min-width="300">
              <template #default="scope">
                <div v-clickOutside="() => handleClickOutside(scope, 'isShowremark')">
                  <p v-show="!scope.row.isShowremark" style="height: 42px;line-height:42px"
                     @click="handleInputEdit(scope, 'remark')">
                    {{ scope.row.remark || '请输入备注' }}</p>
                  <el-input v-show="scope.row.isShowremark" v-model="scope.row.remark"
                            clearable maxlength="100" placeholder="请输入备注" show-word-limit/>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </div>
    <el-dialog v-if="warrantyVisible" v-model="warrantyVisible" :before-close="() => handleImgWarrantyClose()"
               :close-on-click-modal="false"
               :title="warranty_title" width="50%">
      <el-form ref="warranty_formRef" :disabled="true" :model="warranty" :rules="warranty_rules" label-width="90px">
        <!-- <div class="box"> -->
        <el-form-item label="有效期" prop="qualityDate">
          <el-date-picker v-model="warranty.qualityDate" :disabled="modalType == 'detail'" :disabled-date="disabledDate"
                          placeholder="请选择有效期" style="width: 50%;" type="date"/>
        </el-form-item>
        <!-- <el-form-item label="编号" prop="warranty_code">
                <el-input v-model="warranty.businessLicenseCode" placeholder="请输入编号"
                    :disabled="modalType == 'detail'" maxlength="100" show-word-limit clearable />
            </el-form-item> -->
        <!-- </div> -->
        <el-form-item label="上传图片" prop="qualityCode">
          <el-upload ref="qualityCodeRef" v-model:file-list="warranty.qualityCode" :action="uploadUrl"
                     :before-upload="(file) => beforeFile(file)"
                     :data="{ fileType: 'image', fjType: '质保协议', zhType: '客户' }" :disabled="modalType == 'detail'"
                     :headers='headers'
                     :limit="1" :on-exceed="(files) => handleExceed(files, qualityCodeRef)"
                     :on-preview="handlePictureCardPreview"
                     :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 3)"
                     class="upload-demo"
                     drag
                     list-type="picture-card">
            <el-icon>
              <Plus/>
            </el-icon>
          </el-upload>

        </el-form-item>
      </el-form>
      <template #footer>
        <p style="text-align: right">
          <el-button @click="() => handleImgWarrantyClose()">取消</el-button>
          <!--          <el-button type="primary" @click="warrantySubmit">-->
          <!--            确定-->
          <!--          </el-button>-->
        </p>
      </template>
    </el-dialog>
    <el-dialog v-model="entrustVisible" :before-close="() => handleImgEntrustClose()" title="查看"
               width="65%">
      <el-form ref="entrust_formRef" :disabled="true" :model="entrust" :rules="entrust_rules" label-width="130px">
        <div class="box_2">
          <el-form-item label="类型" prop="status">
            <el-radio-group v-model="entrust.status">
              <el-radio v-for="item in drugTypeList" :key="item.value" :label="item.value">{{
                  item.name
                }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="委托人姓名" prop="delegateName">
            <el-input v-model="entrust.delegateName" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入委托人姓名" show-word-limit/>
          </el-form-item>
          <el-form-item label="身份证号" prop="certificateCard">
            <el-input v-model="entrust.certificateCard" :disabled="modalType == 'detail'" clearable
                      maxlength="100" placeholder="请输入身份证号" show-word-limit/>
          </el-form-item>
          <el-form-item label="有效期" prop="effectiveTime">
            <el-date-picker v-model="entrust.effectiveTime" :disabled="modalType == 'detail'"
                            :disabled-date="disabledDate"
                            placeholder="请选择有效期" style="width: 100%;" type="date"/>
          </el-form-item>
          <el-form-item label="授权日期" prop="empowerTime">
            <el-date-picker v-model="entrust.empowerTime" :disabled="modalType == 'detail'" placeholder="请选择授权日期"
                            style="width: 100%;" type="date"/>
          </el-form-item>
          <el-form-item label="区域限制" prop="addressList">
            <el-select v-model="entrust.addressList" clearable filterable multiple placeholder="请选择省份"
                       style="width: 100%">
              <el-option v-for="item in sysAreas" :key="item.value" :disabled="modalType == 'detail'"
                         :label="item.label"
                         :value="item.value"/>
            </el-select>
          </el-form-item>

        </div>
        <el-form-item label="委托商品范围" prop="isEntrustAll">
          <el-radio-group v-model="entrust.isEntrustAll">
            <el-radio label="1">所有{{ formDict(drugTypeList, entrust.status) }}药品</el-radio>
            <el-radio label="2">部分商品</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="box_2">
          <el-form-item label="委托书" prop="remark">
            <el-upload ref="remarkRef" v-model:file-list="entrust.remark" :action="uploadUrl"
                       :before-upload="(file) => beforeFile(file)"
                       :data="{ fileType: 'image', fjType: '委托书', zhType: '客户' }" :disabled="modalType == 'detail'"
                       :headers='headers'
                       :limit="1" :on-exceed="(files) => handleExceed(files, remarkRef)"
                       :on-preview="handlePictureCardPreview"
                       :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 2)"
                       class="upload-demo"
                       drag
                       list-type="picture-card">
              <el-icon>
                <Plus/>
              </el-icon>
            </el-upload>


          </el-form-item>
          <el-form-item label="委托人身份证" prop="delegateCard">
            <el-upload ref="delegateCardRef" v-model:file-list="entrust.delegateCard" :action="uploadUrl"
                       :before-upload="(file) => beforeFile(file)"
                       :data="{ fileType: 'image', fjType: '委托人身份证', zhType: '客户' }"
                       :disabled="modalType == 'detail'" :headers='headers'
                       :limit="1" :on-exceed="(files) => handleExceed(files, delegateCardRef)"
                       :on-preview="handlePictureCardPreview"
                       :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 6)"
                       class="upload-demo"
                       drag
                       list-type="picture-card">
              <el-icon>
                <Plus/>
              </el-icon>
            </el-upload>


          </el-form-item>
        </div>
      </el-form>
      <!--      <div v-show="entrust.isEntrustAll == '2'">-->
      <!--        <el-input v-model="modalentrustOut.commonValue" :disabled="modalType == 'detail'"-->
      <!--                  clearable-->
      <!--                  placeholder="请输入商品名或自编码" style="width:60%"/>-->
      <!--        <el-button style="margin-left:10px" type="primary" @click="handleSearchShop_entrust">搜索商品</el-button>-->
      <!--        <el-button :disabled="!chooseList_entrust_side_table_list.length" style="margin-left:10px" type="danger"-->
      <!--                   @click="handleDelte_entrust_out">删除-->
      <!--        </el-button>-->
      <!--      </div>-->
      <!-- 表格数据 -->
      <el-table v-show="entrust.isEntrustAll == '2'" :data="entrust_side_table_list" border
                style="margin-top: 30px;" @selection-change="handleSelectionChange_side_table_list">
        <el-table-column align="center" min-width="55" type="selection"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="自编码" min-width="120"
                         prop="commoditySelfCode"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="商品名" min-width="120"
                         prop="tradeName"/>
        <el-table-column :formatter="(row) => commodityTypeDict[row.commodityType]" :show-overflow-tooltip="true"
                         align="center" label="分类"
                         min-width="120" prop="commodityType"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="剂型" min-width="120"
                         prop="dosageForm"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="规格" min-width="120"
                         prop="packageSpecification"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="基本单位" min-width="140"
                         prop="basicUnit"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" min-width="120"
                         prop="manufacture.enterpriseName"/>
        <el-table-column align="center" label="生产地址" min-width="120" prop="originPlace"/>

      </el-table>
      <template #footer>
        <p style="text-align: right">
          <el-button @click="() => handleImgEntrustClose()">取消</el-button>
          <!--          <el-button type="primary" @click="entrustSubmit">-->
          <!--            确定-->
          <!--          </el-button>-->
        </p>
      </template>
    </el-dialog>
    <el-dialog v-model="entrustVisible_out" :before-close="() => entrustVisible_out = false" title="请选择委托商品"
               width="50%">
      <!-- 表格数据 -->
      <el-table ref="entrust_out_table_listRef" v-loading="entrust_out_table_list_loading"
                :data="entrust_out_table_list" border
                row-key="id" style="margin-top: 30px;"
                @selection-change="handleSelectionChange_out_table_list">
        <el-table-column :reserve-selection="true" align="center" min-width="55" type="selection"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="自编码" min-width="120"
                         prop="commoditySelfCode"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="商品名" min-width="120"
                         prop="tradeName"/>
        <el-table-column :formatter="(row) => commodityTypeDict[row.commodityType]" :show-overflow-tooltip="true"
                         align="center" label="分类"
                         min-width="120" prop="commodityType"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="剂型" min-width="120"
                         prop="dosageForm"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="规格" min-width="120"
                         prop="packageSpecification"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="基本单位" min-width="140"
                         prop="basicUnit"/>
        <el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" min-width="120"
                         prop="manufacture.enterpriseName"/>
        <el-table-column align="center" label="生产地址" min-width="120" prop="originPlace"/>
        <!-- <el-table-column label="操作" align="center" class-name="small-padding" min-width="300" fixed="right">
            <template #default="scope">
                <el-button link type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
        </el-table-column> -->

      </el-table>
      <div style="display:flex;justify-content:end;margin-top:20px">
        <!-- <el-pagination v-model:current-page="modalentrust_out.current" v-model:page-size="modalentrust_out.size"
            :page-sizes="[10, 20, 30, 50]" :small="false"  :background="true"
            layout="total, sizes, prev, pager, next, jumper"  :total="modalentrust_out_Total" @size-change="handleSearchShop_entrust"
            @current-change="handleSearchShop_entrust" /> -->
        <pagination v-show="modalentrust_out_Total > 0" v-model:limit="modalentrustOut.size"
                    v-model:page="modalentrustOut.current" :total="modalentrust_out_Total"
                    @pagination="handleSearchShop_entrust"/>
      </div>
      <template #footer>
        <p v-show="!entrust_out_table_list_loading" style="text-align: right">
          <el-button @click="entrustVisible_out = false">取消</el-button>
          <el-button type="primary" @click="entrustSubmit_out">
            确定
          </el-button>
        </p>
      </template>
    </el-dialog>
    <el-dialog v-model="prohibitionAndLiftingVisible" :before-close="prohibitionAndLiftingClose"
               :title="prohibitionAndLiftingTitle"
               width="30%">
      <el-form :model="form" label-width="100px">
        <el-form-item label="企业名称：">
          <span>{{ customLabelValue.enterpriseName }}</span>
        </el-form-item>
        <el-form-item label="自编码：">
          <span>{{ customLabelValue.selfCoding }}</span>
        </el-form-item>
        <el-form-item label="原因：">
          <el-input v-model="idea" placeholder="请填写原因" type="textarea"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <p style="text-align: right">
          <el-button @click="prohibitionAndLiftingVisible = false">取消</el-button>
          <el-button type="primary" @click="prohibitionAndLiftingSubmit">
            确定
          </el-button>
        </p>
      </template>
    </el-dialog>
    <el-dialog v-if="licenseScopeVisible" v-model="licenseScopeVisible"
               :before-close="() => licenseScopeVisible = false" style="padding:0 30px"
               title="许可证范围" width="60%">
      <el-table v-loading="lisloading" :data="licenseScopeListData" :select-on-indeterminate="true"
                :tree-props="{ children: 'children' }"
                border row-key="id" style="width: 100%;margin-bottom: 20px">
        <el-table-column align=center label="选择" min-width="140">
          <template #default="scope">
            <el-checkbox v-model="checkedKeys" :label="scope.row.id"
                         @change="(isChoose) => handleCheckChange(isChoose, scope.row)">&nbsp;
            </el-checkbox>
          </template>
        </el-table-column>
        <!-- <el-table-column type="selection" width="55" :reserve-selection="true"
            :selectable="selectable"></el-table-column> -->
        <!-- <el-table-column type="selection" width="55" align="center"> </el-table-column> -->
        <el-table-column align="center" label="质量名称" min-width="250" prop="massName">
        </el-table-column>
        <el-table-column align="center" label="质量编号" min-width="180" prop="massNo">
        </el-table-column>
        <el-table-column align="center" label="属性分类" prop="massType">
          <template #default="scope">
            {{ scope.row.isStop == 1 ? "新范围" : "旧范围" }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="isStop">
          <template #default="scope">
            {{ scope.row.isStop == 1 ? "正常" : "停用" }}
          </template>
        </el-table-column>
        <el-table-column :formatter="row => row.remark ? row.remark : '--'" align="center" label="备注"
                         min-width="150px"
                         prop="remark"/>
      </el-table>
      <template #footer>
        <p style="text-align: right">
          <el-button @click="licenseScopeVisible = false">取消</el-button>
          <el-button v-if="modalType !== 'detail'" type="primary" @click="lisSubmit">
            确定
          </el-button>
        </p>
      </template>
    </el-dialog>
    <viewImg v-if="uploadVisible" :beforeClose="() => uploadVisible = false" :src="uploadViewImgUrl"
             :visible="uploadVisible"/>
  </div>

  <!--    <template #footer>-->
  <!--      <div class="dialog-footer" v-if="!modalLoding">-->
  <!--        <el-button @click="submitForm(3)">取 消</el-button>-->
  <!--        <el-button type="" @click="submitForm(1)"-->
  <!--                   v-if="modalType != 'detail' && modalType != 'edit'">保存草稿</el-button>-->
  <!--        <el-button type="primary" @click="submitForm(2)" v-if="modalType != 'detail'">提交审核</el-button>-->
  <!--      </div>-->
  <!--    </template>-->
</template>

<script setup>

import {getCurrentInstance, onMounted, reactive, ref, toRefs, watch} from 'vue'
import {Aim, Plus} from '@element-plus/icons-vue'
import moment from 'moment'
import {uuid} from 'vue-uuid';
import custom from '@/api/erp/custom'
import {ElMessageBox, genFileId} from 'element-plus'
import manufacturerManagement from '@/api/erp/manufacturerManagement'
import tool from '@/utils/tool';
import detail from '../../assist/cooperationQualification/custom/detail.vue'

const {proxy} = getCurrentInstance();
//  const { sys_normal_disable } = proxy.useDict("sys_normal_disable");
const typeDict = {
  '1': '药品',
  '2': '三类',
  '3': '一类',
  '4': '二类',
  '5': '食品',
  '6': '消杀',
  '7': '其他'
}
const titleDict = {
  add: "新增",
  edit: "编辑",
  detail: "详情",
  draft: "草稿"
}
const commodityTypeDict = {
  '1': '药品',
  '2': '器械',
  '3': '消杀',
  '4': '食品'
}
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
  Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
  ContentType: 'multipart/form-data',
  clientType:'pc',
}
const detailOpen = ref(false)
const detailData = ref(null)
const lisloading = ref(false)
const licenseScopeVisible = ref(false)
const licenseScopeListData = ref([])
const fileUrl = process.env.VUE_APP_API_fileUrl
const reviewVisible = ref(false)
const licenseScopeList = ref([])
const modalType = ref("")
const prohibitionAndLiftingVisible = ref(false)
const prohibitionAndLiftingTitle = ref("")
const list = ref([]);
const open = ref(false);
const loading = ref(false);
const showSearch = ref(false)
const total = ref(0);
const title = ref("");
const activeNames = ref(['1'])
const tabKey = ref('1')
const modalTableList = ref([])
const licensedata = ref({licenseImg: [], licenceScopes: [], type: '1'})
const modalTableList_file = ref([])
const chooseList = ref([])
const chooseList_entrust = ref([])
const modalTableList_entrust = ref([]) // 委托书列表
const modalTableList_warranty = ref([]) // 质保协议列表数据
const chooseList_warranty = ref([]) // 质保协议选中数据
const warrantyVisible = ref(false)
const warranty_title = ref("")
const warranty = ref({})
const entrustVisible = ref(false)
const entrust_title = ref("")
const entrust = ref({
  status: '1'
})
const entrust_side_table_list = ref([])
const entrust_out_table_list = ref([])
const entrustVisible_out = ref(false)
const chooseList_entrust_side_table_list = ref([])
const chooseList_entrust_out_table_list = ref([])
const sysAreas = ref([])
const sysAreasThird = ref([])
const delFlagList = ref([])
const reviewStatus = ref([])
const qualificationList = ref([])
const modeOfList = ref([])
const drugTypeList = ref([])
const businessList = ref([])
const uploadVisible = ref(false)
const uploadViewImgUrl = ref("")
const batchTypeList = ref([])
const retailTypeList = ref([])
const orgTypeList = ref([])
const fileSelectList = ref([])
const modalentrust_out_Total = ref(0)
const modalLoding = ref(false)
const warranty_copy = ref({})
const entrust_copy = ref({})
const customLabelValue = ref({})
const idea = ref("")
const fileListVisible = ref(false)
const fileListData = ref([])
const fileDraLoading = ref(false)
const fileListDataTotal = ref(0)
const fileListChooseList = ref([])
const entrust_out_table_list_loading = ref(false)
const shopType = ref([])
const businessNatureList = ref([])
const busScopes = ref([])
const deliveryAddress = ref(null)
// gsp
const modalTableList_GMP = ref([])
const gmpCertificatePictureRef = ref(null)
const chooseList_GMP = ref([])
const deleteGmpList = ref([])
const data = reactive({
  form: {
    credentialType: "1",
    zylicenseProject: [],
    zylicenseRange: []
  },
  modalTableList_GMPRef: {},
  queryParams: {
    current: 1,
    size: 10,
  },
  modalentrustOut: {
    current: 1,
    size: 10,
  },
  fileListDataPage: {
    current: 1,
    size: 10,
  },
  rules: {
    enterpriseName: [{required: true, message: "企业名称不能为空", trigger: "blur"}],
    contactNumber: [{pattern: /^1[3456789]\d{9}$/, message: '请输入正确的联系电话', trigger: 'blur'},],
    selfCoding: [{required: true, message: "自编码不能为空", trigger: "blur"},],
    credentialType: [{required: true, message: "资质类别不能为空", trigger: "change"}],
    socialCreditCode: [{required: true, message: "统一社会信用代码不能为空", trigger: "blur"}],
    // businessTerm: [{ required: true, message: "营业期限不能为空", trigger: "change" }],
    // residence: [{ required: true, message: "住所不能为空", trigger: "blur" }],
    zylicenseName: [{required: true, message: "执业许可证名称不能为空", trigger: "blur"}],
    zylicenseAddress: [{required: true, message: "许可证地址不能为空", trigger: "blur"}],
    business: [{required: true, message: "经营性质不能为空", trigger: "blur"}],
    zylicenseValidity: [{required: true, message: "有效期不能为空", trigger: "change"}],
    tradeType: [{required: true, message: "业务类型不能为空", trigger: "change"}],
    provinces: [{required: true, message: "所在区域不能为空", trigger: "change"}],
    zylicenseNo: [{required: true, message: "登记号码不能为空", trigger: "blur"}],
    zylicenseLegalPerson: [{required: true, message: "法人不能为空", trigger: "blur"}],
    zylicenseDirector: [{required: true, message: "负责人不能为空", trigger: "blur"}],
    // zylicenseStartTime: [{ required: true, message: "发证日期不能为空", trigger: "blur" }],
    zylicenseOffice: [{required: true, message: "发证机关不能为空", trigger: "blur"}],
    zylicenseProject: [{required: true, message: "诊疗科目不能为空", trigger: "change"}],
    // remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
  },
  entrust_rules: {
    status: [{required: true, message: "类型不能为空", trigger: "change"}],
    delegateName: [{required: true, message: "委托人姓名不能为空", trigger: "blur"}],
    certificateCard: [{required: true, message: "身份证号不能为空", trigger: "blur"},
      {pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur'}],
    effectiveTime: [{required: true, message: "有效期不能为空", trigger: "change"}],
    empowerTime: [{required: true, message: "授权日期不能为空", trigger: "change"}],
    // addressList: [{ required: true, message: "区域限制不能为空", trigger: "change" }],
    isEntrustAll: [{required: true, message: "委托商品范围不能为空", trigger: "change"}],
    remark: [{required: true, message: "委托书不能为空", trigger: "change"}],
    // delegateCard: [{ required: true, message: "委托人身份证不能为空", trigger: "change" }],
  },
  warranty_rules: {
    qualityDate: [{required: true, message: "有效期不能为空", trigger: "change"}],
    qualityCode: [{required: true, message: "上传图片不能为空", trigger: "change"}],
  },
  modalTableListRules: {
    licenseNo: [{required: true, message: "许可证编号不能为空", trigger: "blur"}],
    licenseAddress: [{required: true, message: "注册地址不能为空", trigger: "blur"}],
    licenceScopesName: [{required: true, message: "许可证范围不能为空", trigger: 'blur'}],
    licenseStartTime: [{required: true, message: "发证日期不能为空", trigger: "change"}],
    licenseValidity: [{required: true, message: "有效期不能为空", trigger: "change"}],
    licenseOffice: [{required: true, message: "发证机关不能为空", trigger: "blur"}],
    licenseDirector: [{required: true, message: "企业负责人不能为空", trigger: "blur"}],
    qualityDirector: [{required: true, message: "质量负责人不能为空", trigger: "blur"}],
    licenseImg: [{required: true, message: "许可证图片不能为空", trigger: "change"}],
    licenseLegalPerson: [{required: true, message: "法人不能为空", trigger: "blur"}],
    warehouseAddress: [{required: true, message: "仓库地址不能为空", trigger: "blur"}],
  },
  modalTableListRules_: {
    licenseNo: [{required: true, message: "许可证编号不能为空", trigger: "blur"}],
    // licenseAddress: [{ required: true, message: "注册地址不能为空", trigger: "blur" }],
    licenceScopesName: [{required: true, message: "许可证范围不能为空", trigger: 'blur'}],
    licenseStartTime: [{required: true, message: "发证日期不能为空", trigger: "change"}],
    licenseValidity: [{required: true, message: "有效期不能为空", trigger: "change"}],
    licenseOffice: [{required: true, message: "发证机关不能为空", trigger: "blur"}],
    // licenseDirector: [{ required: true, message: "企业负责人不能为空", trigger: "blur" }],
    // qualityDirector: [{ required: true, message: "质量负责人不能为空", trigger: "blur" }],
    licenseImg: [{required: true, message: "许可证图片不能为空", trigger: "change"}],
    // licenseLegalPerson: [{ required: true, message: "法人不能为空", trigger: "blur" }],
    // warehouseAddress: [{ required: true, message: "仓库地址不能为空", trigger: "blur" }],
  }
});
const remarkRef = ref(null)
const delegateCardRef = ref(null)
const qualityCodeRef = ref(null)
const file_loading = ref(false)
const licenseImgRef = ref(null)
const zylicenseProjectOption = ref([])
const massRangeOption = ref([])
const credentialType_oldValue = ref(undefined)
const credentialTypeRef = ref(null)
const chooseLicenseScope = ref([])
const licenseScopeRow = ref({})
const checkedKeys = ref([])
const erpCustomersDelegateList = ref([])
const erpCustomersDelegateCommodityList = ref([])
const erpCustomersQualityList = ref([])
const entrust_out_table_listRef = ref(null)
const reviewRow = ref({})
const uploadVisibleFile = ref(false)
const uploadViewImgUrlFile = ref('')
const zylicenceDTOSCopy = ref({})
const rangeType = ref(undefined)
const manufacturerType = ref([])
const directoryFileName = ref([])
const tradeType = ref([])
const isClearFileWaring = ref(true)
const props = defineProps({
  formRow: {
    default: null
  },
  ocrFlag: {
    default: false
  },
  smallTypeForm: {
    default: null
  },
  formList: {
    default: null
  }
})
const {formRow, ocrFlag, smallTypeForm} = toRefs(props)
const {
  queryParams,
  form,
  rules,
  modalentrustOut,
  entrust_rules,
  warranty_rules,
  fileListDataPage,
  modalTableListRules,
  modalTableListRules_,
  modalTableList_GMPRef
} = toRefs(data);
watch(() => form.value.zylicenseProject, (newValue, oldValue) => {
  custom.getRangeId({tssIds: form.value.zylicenseProject?.toString()}).then(res => {
    if (res.code == 200) {
      const data = res.data
      data.forEach(x => {
        x.massRangeSetIds && x.massRangeSetIds?.split(",").forEach(v => {
          form.value.zylicenseRange.push(v)
          form.value.zylicenseRange = [...new Set(form.value.zylicenseRange)]
        })

      })
    }
  })
})
const tableRowStyle = ({row, rowIndex}) => {
  if (row.status == '6' && !row.isEnable) {  // 草稿
    return {
      color: '#e6a23c'
    }
  } else if (row.status == '1' && !row.isEnable) {  // 待审核
    return {
      color: '#409eff'
    }
  } else if (row.status == '2' && !row.isEnable) {  //审核中
    return {
      color: '#67c23a'
    }
  } else if (row.status == '4' && !row.isEnable) {  //已驳回
    return {
      color: '#ff4800'
    }
  } else if (row.isEnable) {   // 禁用
    return {
      color: '#f56c6c'
    }
  }
}

watch(() => modalTableList.value, (newValue, oldValue) => {
  if (!open.value || isClearFileWaring) return
  if (modalTableList_file.value?.length) {
    ElMessageBox.confirm("修改会清空附件列表？", '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(res => {
      modalTableList_file.value = []
    }).catch(() => {

    })
  }
}, {deep: true})
// 获取附件列表
const handleGetFileList = async () => {
  isClearFileWaring.value = true
  file_loading.value = true
  const smallType = []
  const ids = []
  if (form.value.credentialType !== '2') {
    await isaddItem()
    modalTableList.value?.forEach(item => {
      ids?.push(typeDict[item.type] + form.value.credentialType)
    })
    manufacturerType.value?.forEach(item => {
      if (ids.includes(item.code)) {
        smallType.push(item.value)
      }
    })
  } else {
    smallType.push('9')
  }


  manufacturerManagement.getFileList({
    bigType: '7',
    smallType: smallTypeForm.value,
    size: 1000,
    isFile: 1
  }).then(res => {
    if (res.code == 200) {
      modalTableList_file.value = res?.data?.records

    } else {
      proxy.msgError(res.msg)
    }
    file_loading.value = false
  }).finally(() => {
    file_loading.value = false
  })
  isClearFileWaring.value = false
  // modalTableList_file.value = []
}
const handleDeleteFile = (scopeIndex, index) => {
  modalTableList_file.value[scopeIndex].fileList.splice(index, 1)
  proxy.msgSuccess('删除成功！')
}
// gsp方法  start
const handleAdd_gsp = () => {
  modalTableList_GMP.value.push({uuid: uuid.v1(), gmpCertificatePicture: [], isAdd: true})
}
const handleDelete_gsp = () => {
  proxy.$confirm('是否确认删除选中数据项?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    const ids = []
    chooseList_GMP.value.forEach(item => {
      if (!item.isAdd) {
        ids.push(item.id)
      }
    })
    if (ids?.length) {
      deleteGmpList.value = [...deleteGmpList.value, ...ids]
      modalTableList_GMP.value = modalTableList_GMP.value.filter((item) => !(chooseList_GMP.value.some((ele) => ele.uuid === item.uuid)));
      proxy.msgSuccess('删除成功')
    } else {
      modalTableList_GMP.value = modalTableList_GMP.value.filter((item) => !(chooseList_GMP.value.some((ele) => ele.uuid === item.uuid)));
      proxy.msgSuccess('删除成功')
    }

  }).catch(() => {

  })
}
const handleSelectionChange_GMP = (value) => {
  chooseList_GMP.value = value

}
// end
const addressChange = () => {
  form.value.provinceName = deliveryAddress.value.getCheckedNodes()[0].text
}
const lisSubmit = () => {
  checkedKeys.value = [...new Set(checkedKeys.value)]
  if (rangeType.value) {
    licenseScopeRow.value[rangeType.value] = []
    checkedKeys.value.forEach(x => {
      if (rangeType.value === 'apparatusFirstScopes') {
        // licenseScopeRow.value.apparatusFirstScopes = []
        licenseScopeRow.value.apparatusFirstScopes.push({
          massRangeSet: {
            id: x
          }
        })
      } else {
        // licenseScopeRow.value.sterilizedScopes = []
        licenseScopeRow.value.sterilizedScopes.push({
          massRangeSet: {
            id: x
          }
        })

      }

    })
  } else {
    licenseScopeRow.value.licenceScopes = []
    checkedKeys.value.forEach(x => {
      licenseScopeRow.value.licenceScopes.push({
        massRangeSet: {
          id: x
        }
      })
    })
  }

  const arr = treeToArray(licenseScopeListData.value)
  const chooseName = []
  arr.forEach(item => {
    if (checkedKeys.value.includes(item.id)) {
      chooseName.push(item.massName)
    }
  })
  if (rangeType.value == 'apparatusFirstScopes') {
    form.value.apparatusScopeName = chooseName.toString()
  } else if (rangeType.value == 'sterilizedScopes') {
    console.log(chooseName);
    form.value.sterilizedScopeName = chooseName.toString()
  } else {
    licenseScopeRow.value.licenceScopesName = chooseName.toString()
  }

  licenseScopeVisible.value = false
}
const handleSocialCreditCodeBlur = () => {
  if (form.value.credentialType != '2') {
    if (form.value.socialCreditCode ?? false) {
      custom.checkLicenseCode({code: form.value.socialCreditCode, id: form.value?.id}).then(res => {
        if (res.data) {
          proxy.msgError('统一社会信用代码已经存在！请重新输入')
          form.value.socialCreditCode = undefined
        }
      })
    }
  }
}
const viewSelection = (row, isChoose) => {
  const forTreeChoose = (arr) => {
    arr.forEach(v => {
      checkedKeys.value.push(v.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (v.children?.length) {
        forTreeChoose(v.children)
      }
    })
  }
  const forTreeChoose_no = (arr) => {
    arr.forEach(v => {
      checkedKeys.value = [...checkedKeys.value].filter(x => x != v.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (v.children?.length) {
        forTreeChoose_no(v.children)
      }
    })
  }
  row.forEach(x => {
    if (isChoose) {
      checkedKeys.value.push(x.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (x.children?.length) {
        forTreeChoose(x.children)

      }
    } else {
      // indeterminate
      checkedKeys.value = [...checkedKeys.value].filter(s => s != x.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (x.children?.length) {
        forTreeChoose_no(x.children)

      }
    }

  })
}
const handleCheckChange = (isChoose, row) => {
  viewSelection([row], isChoose)
}

function treeToArray(tree) {
  return tree.reduce((res, item) => {
    const {children, ...i} = item
    return res.concat(i, children && children.length ? treeToArray(children) : [])
  }, [])
}

const handleLicenseScope = (scope, key) => {
  let isopen = false
  if (modalTableList_entrust.value?.length) {
    proxy.$confirm('更新会清空委托书列表,确认操作吗?', '提示', {
      type: 'warning',
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    }).then(() => {
      modalTableList_entrust.value = []
      isopen = true

    }).catch(() => {

    })
  }
  if (!isopen && modalTableList_entrust.value?.length) return
  let shopKey = undefined
  rangeType.value = undefined
  chooseLicenseScope.value = []
  licenseScopeListData.value = []
  checkedKeys.value = []
  licenseScopeRow.value = scope
  licenseScopeVisible.value = true

  if (key === 'apparatusFirstScopes') {
    shopKey = shopType.value.filter(item => item.name === typeDict['3'])?.[0]?.id
  } else if (key === 'sterilizedScopes') {
    shopKey = shopType.value.filter(item => item.name === typeDict['6'])?.[0]?.id
  } else {
    shopKey = shopType.value.filter(item => item.name === typeDict[scope.type])?.[0]?.id
  }
  if (shopKey) {
    lisloading.value = true
    custom.getWightConfig({dictid: shopKey}).then(res => {
      if (res.code == 200) {
        lisloading.value = false
        res.data.forEach(v => {
          if (v.length) {
            licenseScopeListData.value.push(v[0])
          }

        })
        if (key) {
          rangeType.value = key
          console.log(licenseScopeRow.value, key);
          licenseScopeRow.value[key]?.forEach(v => {
            checkedKeys.value.push(v.massRangeSet.id)
          })
        } else {
          licenseScopeRow.value.licenceScopes?.forEach(v => {
            checkedKeys.value.push(v.massRangeSet.id)
          })
        }


      }
    }).finally(() => {
      lisloading.value = false
    })
  } else {
    lisloading.value = false
  }
}
/**
 * @description: 点击操作日志
 * @return {*}
 */
const handleLog = (row) => {
  reviewVisible.value = true
  reviewRow.value = row
}
/**
 * @description: 关闭操作日志
 * @return {*}
 */
const beforeClose_review = () => {
  reviewVisible.value = false
}
const filterAddress = (addressList) => {
  const addressArr = []
  sysAreas.value?.forEach(x => {
    addressList?.forEach(v => {
      if (x?.value == v) {
        addressArr?.push(x.label)
      }
    })
  })
  return addressArr?.toString()
}
const handleExceed = (files, ref) => {
  ref.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  ref.handleStart(file)
  ref.submit()
}
const handleImgWarrantyClose = () => {
  if (warranty_title.value = "修改" && warranty.value.id) {
    modalTableList_warranty.value.forEach(item => {
      item = warranty_copy.value
    })
  }
  warranty_copy.value = {}
  warranty.value = {}
  warrantyVisible.value = false
}
const handleExportFile = () => {
  const chooseArr = []
  fileListChooseList.value.forEach(v => {
    chooseArr.push(v.fileUrl)
  })
  manufacturerManagement.exportFile({fileNames: chooseArr?.toString()}).then(res => {
    proxy.download(res, "application/zip")
  })
}
const handleDetailFileListChoose = (key) => {
  fileListChooseList.value = key
}
const handleFileList = () => {
  fileListVisible.value = true
  fileDraLoading.value = true
  manufacturerManagement.getFileLists({
    commonId: form.value.id,
    commonType: '12', ...fileListDataPage.value
  }).then(res => {
    if (res.code == 200) {
      fileListData.value = res.data?.records
      fileListDataTotal.value = res.data?.total
    }
    fileDraLoading.value = false
  })
}
const handleImgEntrustClose = () => {
  if (entrust_title.value = "修改" && entrust.value.id) {
    modalTableList_entrust.value.forEach(v => {
      // console.log(v,entrust.value,entrust_copy.value);
      if (v.id == entrust.value.id) {
        v = entrust_copy.value
      }
    })
  }
  entrustVisible.value = false
}
const handlePictureCardPreview = (uploadFile) => {
  uploadViewImgUrl.value = uploadFile?.url || uploadFile
  uploadVisible.value = true
}
const handleUploadView = (url) => {
  uploadViewImgUrl.value = url
  uploadVisible.value = true
}
const handleUploadViewFile = (url) => {
  uploadVisibleFile.value = true
  uploadViewImgUrlFile.value = url
}
const handleClickOutside = (scope, key, e) => {
  if (e?.target?.parentNode?.parentNode?.parentNode?.parentNode.className.includes('selectClass') || e?.target?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode.className.includes('selectClass')) {
    return
  }
  scope.row[key] = false
}
const filterArr = (option, key, value) => {
  if (!option?.length || !key || !value?.length) return
  const res = option.filter(v => value.includes(v[key]) === true)
  const NameArr = []
  res?.forEach(item => {
    NameArr.push(item?.valueName)
  })
  return NameArr?.toString()
}
const handleSelectFcous = () => {
  credentialType_oldValue.value = form.value.credentialType
}
const handleChange_credentialType = value => {
  form.value.credentialType = credentialType_oldValue.value
  proxy.$confirm('切换会清空委托书列表和附件列表，确认切换吗？', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    credentialTypeRef.value.blur()
    proxy.$refs["formRef"].resetFields()
    form.value.credentialType = value
    form.value.supplierCoding = undefined
    modalTableList_file.value = []
    modalTableList_entrust.value = []
    modalTableList.value = []
    licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
    if (value == "2") {
      fileSelectList.value = orgTypeList.value
    } else {
      if (value == "1") {
        fileSelectList.value = batchTypeList.value
      }
      if (value == "3") {
        fileSelectList.value = retailTypeList.value
      }
    }
    getSupplierCoding()
  }).catch(() => {

  })
}
const getSupplierCoding = async () => {
  const res = await custom.getSelfCodeByType({type: form.value.credentialType})
  if (res.code === 200) {
    form.value.selfCoding = res.data
  } else {
    proxy.msgError('自编码获取失败')
  }

}
const handleChange_msg = () => {
  // if (!form.value.credentialType) {
  //     activeNames.value = ['1']
  // }

}
// 获取所有经营范围
const getbusScopes = async () => {
  isClearFileWaring.value = true
  await isaddItem()
  console.log(modalTableList.value, '-----------委托书');
  const busScopesArr = []
  if (form.value?.credentialType !== '2') {
    form.value.apparatusFirstScopes?.forEach(item => {
      busScopesArr.push(item?.massRangeSet?.id)
    })
    form.value?.sterilizedScopes?.forEach(item => {
      busScopesArr.push(item?.massRangeSet?.id)
    })
    modalTableList.value?.forEach(item => {
      item?.licenceScopes?.forEach(value => {
        busScopesArr.push(value?.massRangeSet?.id)
      })
    })
  }
  busScopes.value = busScopesArr
  isClearFileWaring.value = false
}
// 委托书新增
const handleAdd_entrust = (row) => {
  getbusScopes()
  entrust.value = {}
  entrust_side_table_list.value = []
  if (row) {
    // 修改
    entrust_title.value = "修改"
    // modalTableList_entrust.value
    entrust_copy.value = JSON.parse(JSON.stringify(row))
    entrust.value = {...entrust_copy.value}
    entrust_side_table_list.value = entrust.value.delegateCommodityDTOS || []
  } else {
    // 新增
    entrust_title.value = "新增"
  }
  entrustVisible.value = true
}
// 委托书新增提交
const entrustSubmit = () => {
  proxy.$refs["entrust_formRef"].validate(valid => {
    if (valid) {
      entrust.value.effectiveTime = moment(entrust.value.effectiveTime).format("YYYY-MM-DD")
      entrust.value.empowerTime = moment(entrust.value.empowerTime).format("YYYY-MM-DD")
      // entrust.value.addressList = entrust.value.addressList[0]
      // entrust.value.remark = JSON.stringify(entrust.value.remark?.[0])
      if (entrust.value.isEntrustAll == '2') {
        entrust.value.delegateCommodityDTOS = entrust_side_table_list.value || []
      } else {
        entrust.value.delegateCommodityDTOS = []
      }

      if (entrust_title.value = "修改" && entrust.value.id) {
        modalTableList_entrust.value.forEach((v, i) => {
          if (v.id == entrust.value.id) {
            modalTableList_entrust.value[i] = entrust.value
          }
        })
      } else {
        entrust.value.id = uuid.v1()
        entrust.value.isAdd = true
        modalTableList_entrust.value = [...modalTableList_entrust.value, entrust.value]
      }
      // customRef1.value.modalTableList_entrust=[...newValue]
      emit('cusSaveFn', entrust.value.id)
      entrustVisible.value = false
      entrust.value = {}
      entrust_side_table_list.value = []
    }
  })


}
// 质保协议新增提交
const warrantySubmit = () => {
  proxy.$refs["warranty_formRef"].validate(valid => {
    if (valid) {
      if (warranty_title.value = "修改" && warranty.value.id) {
        modalTableList_warranty.value.forEach((item, index) => {
          modalTableList_warranty.value[index] = warranty.value
        })
      } else {
        warranty.value.id = uuid.v1()
        warranty.value.isAdd = true
        modalTableList_warranty.value = [...modalTableList_warranty.value, warranty.value]
      }
      emit('cusSaveFn', warranty.value.id)
      warranty.value = {}
      warrantyVisible.value = false


    }
  })
}
// 质保协议新增
const handleAdd_warranty = (row) => {
  warranty.value = {}
  if (row) {
    // 修改
    warranty_title.value = "修改"
    warranty_copy.value = JSON.parse(JSON.stringify(row))
    warranty.value = {...warranty_copy.value}
  } else {
    // 新增
    warranty_title.value = "新增"

  }
  warrantyVisible.value = true
}
// 质保协议删除
const handleDelete_warranty = () => {
  proxy.$confirm('是否确认删除选中数据项?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    const ids = []
    chooseList_warranty.value.forEach(item => {
      if (!item.isAdd) {
        ids.push(item.id)
      }
    })
    if (ids?.length) {
      // 质保协议删除
      erpCustomersQualityList.value = [...erpCustomersQualityList.value, ...ids]
      modalTableList_warranty.value = modalTableList_warranty.value.filter((item) => !(chooseList_warranty.value.some((ele) => ele.id === item.id)));
      proxy.msgSuccess('删除成功')
    } else {
      modalTableList_warranty.value = modalTableList_warranty.value.filter((item) => !(chooseList_warranty.value.some((ele) => ele.id === item.id)));
      proxy.msgSuccess('删除成功')
    }

  }).catch(() => {

  })
}
const handleSelectionChange_warranty = (key) => {
  chooseList_warranty.value = key
}
//委托书选择商品提交
const entrustSubmit_out = () => {
  // entrust_side_table_list.value = [...chooseList_entrust_out_table_list.value]
  const ids = []
  entrust_side_table_list.value?.forEach(x => ids.push(x.id))
  chooseList_entrust_out_table_list.value?.forEach(v => {
    if (ids.length && ids?.includes(v.id)) {
      proxy.msgError('检测到商品名为' + v.tradeName + '已添加，已将该条数据过滤！')
    } else {
      entrust_side_table_list.value = [...entrust_side_table_list.value, v]
    }

  })

  entrustVisible_out.value = false
}
const handleSelectionChange_side_table_list = key => {
  chooseList_entrust_side_table_list.value = key
}
const handleSelectionChange_out_table_list = key => {
  chooseList_entrust_out_table_list.value = key
}
// 委托书搜索商品
const handleSearchShop_entrust = () => {
  entrustVisible_out.value = true
  entrust_out_table_list_loading.value = true
  custom.shopList_({
    ...modalentrustOut.value,
    specialMedicineControl: entrust.value.status == '1' ? '0' : entrust.value.status == '2' ? '1' : undefined,
    busScopes: busScopes.value
  }).then(res => {
    if (res.code == 200) {
      entrust_out_table_list.value = res.data?.records
      // entrust_out_table_list.value.forEach(v => {
      //     if (entrust_side_table_list.value && entrust_side_table_list.value.length) {
      //         entrust_side_table_list.value.forEach(x => {
      //             if (v.id === x.id) {
      //                 entrust_out_table_listRef.value.toggleRowSelection(v, true)

      //             }

      //         })
      //     }

      // })

      modalentrust_out_Total.value = res.data?.total
      entrust_out_table_list_loading.value = false
    }
  }).finally(() => {
    entrust_out_table_list_loading.value = false
  })


}
// 委托书删除搜索商品
const handleDelte_entrust_out = () => {
  proxy.$confirm('是否确认删除选中数据项?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    const ids = []
    chooseList_entrust_side_table_list.value.forEach(item => {
      if (!item.isAdd) {
        ids.push(item.id)
      }
    })
    if (ids?.length) {
      // 委托商品删除
      erpCustomersDelegateCommodityList.value = [...erpCustomersDelegateCommodityList.value, ...ids]
      entrust_side_table_list.value = entrust_side_table_list.value.filter((item) => !(chooseList_entrust_side_table_list.value.some((ele) => ele.id === item.id))) || [];
      proxy.msgSuccess('删除成功')
    } else {
      entrust_side_table_list.value = entrust_side_table_list.value.filter((item) => !(chooseList_entrust_side_table_list.value.some((ele) => ele.id === item.id))) || [];
      proxy.msgSuccess('删除成功')
    }

  }).catch(() => {

  })

}

// 委托书删除
const handleDelete_entrust = () => {
  proxy.$confirm('是否确认删除选中数据项?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    const ids = []
    chooseList_entrust.value.forEach(item => {
      if (!item.isAdd) {
        ids.push(item.id)
      }
    })
    if (ids?.length) {
      // 委托书删除
      erpCustomersDelegateList.value = [...erpCustomersDelegateList.value, ...ids]
      modalTableList_entrust.value = modalTableList_entrust.value.filter((item) => !(chooseList_entrust.value.some((ele) => ele.id === item.id)));
      proxy.msgSuccess('删除成功')

    } else {
      modalTableList_entrust.value = modalTableList_entrust.value.filter((item) => !(chooseList_entrust.value.some((ele) => ele.id === item.id)));
      proxy.msgSuccess('删除成功')
    }

  }).catch(() => {

  })
}
// 委托书table多选
const handleSelectionChange_entrust = (key) => {
  chooseList_entrust.value = key
}
const disabledDate = (time) => {
  return time.getTime() < Date.now()
}

// 禁用or解禁提交
const prohibitionAndLiftingSubmit = () => {
  const params = {
    idea: idea.value,
    status: customLabelValue.value.isEnable ? '0' : '1',
    id: customLabelValue.value.id
  }
  custom.delFlag(params).then(res => {
    if (res.code == 200) {
      proxy.msgSuccess(customLabelValue.value.isEnable ? '解禁成功' : '禁用成功')
      getList()
      customLabelValue.value = {}
    }
  }).finally(() => {
  })
  prohibitionAndLiftingVisible.value = false
}
// 关闭禁用or解禁
const prohibitionAndLiftingClose = () => {
  prohibitionAndLiftingVisible.value = false
}

// 禁用
const handleDisabled = (row, type) => {
  prohibitionAndLiftingVisible.value = true
  idea.value = undefined
  customLabelValue.value = row
  if (type == '1') {
    prohibitionAndLiftingTitle.value = '设置禁用'
  } else {
    prohibitionAndLiftingTitle.value = '设置解禁'
  }

}
// 查看详情modal
const handleDetail = (row, type) => {
  detailOpen.value = true
  detailData.value = row
  modalType.value = type
}
const handleEdit = (row, type) => {
  modalLoding.value = true
  open.value = true
  modalType.value = type
  title.value = titleDict[type]
  getLicenseScopeListOption()
  getRanges()
  getTerats()
  custom.detail({id: row.id}).then(res => {
    if (res.code != 200) return proxy.msgError(res.msg)
    form.value = res.data?.customersDTO
    form.value.apparatusFirst = form.value.apparatusFirst == '1' ? true : false
    form.value.sterilized = form.value.sterilized == '1' ? true : false
    modalTableList_entrust.value = res.data?.delegateDTOS
    modalTableList_entrust.value?.forEach(item => {
      item.remark = item?.remark ? [JSON.parse(item.remark)] : []
      item.delegateCard = item?.delegateCard ? [JSON.parse(item.delegateCard)] : []
      item.addressList = item.addressList && item.addressList?.split(",")
      item.isEntrustAll = item.isEntrustAll === true ? '1' : '2'
    })
    modalTableList_file.value = res.data?.fileDTOS
    modalTableList_file.value?.forEach(v => {
      v.fileList = [{name: v?.fileName, url: v?.fileUrl}]
    })
    modalTableList.value = res.data?.licenceDTOS
    modalTableList.value?.forEach(item => {
      item.type = Object.entries(typeDict).find(([key, val]) => val === item.type)[0]
      item.licenseImg = item.licenseImg ? JSON.parse(item.licenseImg) : []
    })
    licensedata.value = modalTableList.value?.[0]
    tabKey.value = modalTableList.value?.[0]?.type
    modalTableList_warranty.value = res.data?.qualityDTOS
    modalTableList_warranty.value?.forEach(item => {
      item.qualityCode = item.qualityCode ? [JSON.parse(item.qualityCode)] : []
    })
    if (res.data?.zylicenceDTOS) {
      zylicenceDTOSCopy.value = JSON.parse(JSON.stringify(res.data?.zylicenceDTOS))
      form.value = {...res.data?.zylicenceDTOS, ...form.value}
      form.value.zylicenseRange = form.value?.zylicenseRange && form.value?.zylicenseRange?.split(",")
      form.value.zylicenseRange = [...new Set(form.value.zylicenseRange)]
      form.value.zylicenseProject = form.value?.zylicenseProject && form.value?.zylicenseProject?.split(",")
      form.value.zylicenseProject = [...new Set(form.value.zylicenseProject)]
    }
    modalTableList_GMP.value = res.data?.gmpCertificateDTOList || []
    modalTableList_GMP.value?.forEach(item => {
      item.gmpCertificatePicture = item.gmpCertificatePicture ? [JSON.parse(item.gmpCertificatePicture)] : []
      item.gmpCertificateScope = item.gmpCertificateScope ? item.gmpCertificateScope?.split(',') : []
    })
    modalLoding.value = false
  }).finally(() => {
    modalLoding.value = false
  })
}
const handleInputEdit = (scope, type) => {
  if (modalType.value == 'detail') return
  scope.row[`isShow${type}`] = true
}
const handleUploadSuccess = (res, file, fileList, index, type) => {
  if (res.code == 200) {
    if (type == 1) {
      const fileList = [...licensedata.value.licenseImg, res.data]
      licensedata.value.licenseImg = fileList.filter(item => !item.response)
    }
    if (type == 2) {
      entrust.value.remark = [res.data]
    }
    if (type == 3) {
      warranty.value.qualityCode = [res.data]
    }
    if (type == 4) {
      const fileList = [...modalTableList_file.value[index].fileList, res.data]
      modalTableList_file.value[index].fileList = fileList.filter(item => !item.response)
    }
    if (type == 5) {
      modalTableList_GMP.value[index].gmpCertificatePicture = [res.data]
    }
    if (type == 6) {
      entrust.value.delegateCard = [res.data]
    }
  } else {
    proxy.msgError(res.msg)
  }
}
const echoMess = () => {
  if (props.formRow.split(',').indexOf('2') != -1 && form.value.credentialType !== '2') {
    return '-药品'
  } else if (props.formRow.split(',').indexOf('7') != -1 && form.value.credentialType !== '2') {
    return '-二类'
  } else if (props.formRow.split(',').indexOf('8') != -1 && form.value.credentialType !== '2') {
    return '-三类'
  } else if (props.formRow.split(',').indexOf('9') != -1 && form.value.credentialType !== '2') {
    return '-消杀'
  } else {
    return ''
  }
}
const handleSelectionChange = (key) => {
  chooseList.value = key
}
const handleChange = (value) => {
  activeNames.value = value
}
// 储存数据  回显数据
const saveLicendata = async (value) => {
  await isaddItem()
  const data = modalTableList.value?.filter(item => item.type === value)
  if (data?.length) {
    licensedata.value = data[0]

  } else {
    licensedata.value = {licenseImg: [], licenceScopes: [], type: value}
  }
  tabKey.value = value
}
// 判断数组是否有该类型数据  有则回显  无则新增
const isLicenseData = (value) => {
  const cloneTabKey = JSON.parse(JSON.stringify(tabKey.value))
  if (regData(value)) {
    saveLicendata(value)
  } else {
    proxy.$refs["modalTableListRef"].validate((valid) => {
      if (valid) {
        saveLicendata(value)
      } else {
        tabKey.value = cloneTabKey
      }
    })
  }
}

const beforeFile = (file) => {
  if (file.size > 2097152) {
    proxy.msgError("文件不能大于2M");
    return false;
  }
}
// 检验该类型是否为空数据
const regData = (key) => {
  let index = 0
  const data = licensedata.value
  const regKey = key === '1' ? ["licenseNo", "licenseAddress", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg", "licenseLegalPerson", "warehouseAddress"] : ["licenseNo", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseImg"]
  regKey.forEach(item => {
    if (Array.isArray(data[item]) ? data[item]?.length : data[item]) {
      index++
    }

  })
  if (index === regKey.length || index === 0) {
    return true
  }
  return false
}

const handleTabClick = (value) => {
  isLicenseData(value)
}

/** 查询列表 */
function getList() {
  loading.value = true
  const params = {
    ...queryParams.value,
  }
  if (params?.createDate?.length) {
    params.beginCreateDate = moment(params?.createDate[0]).format('YYYY-MM-DD 00:00:00')
    params.endCreateDate = moment(params?.createDate[1]).format('YYYY-MM-DD 23:59:59')
    delete params.createDate
  }
  if (params?.updateDate?.length) {
    params.beginUpdateDate = moment(params?.updateDate[0]).format('YYYY-MM-DD 00:00:00')
    params.endUpdateDate = moment(params?.updateDate[1]).format('YYYY-MM-DD 23:59:59')
    delete params.updateDate
  }
  custom.getList(params).then(res => {
    if (res.code == 200) {
      list.value = res.data.records
      total.value = res.data.total
      loading.value = false
    }
  })
}

const formDict = (data, val) => {
  if (data.length && val) {
    return proxy.selectDictLabel(data, val)
  }

}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

const getLicenseScopeListOption = async () => {
  let res = await manufacturerManagement.getLicenseScopeList({'item.id': '6', isStop: '1', current: 1, size: 999})
  licenseScopeList.value = res?.data?.records
}
const handleAdd = async (row, type) => {
  open.value = true
  modalType.value = type
  title.value = titleDict[type]
  getLicenseScopeListOption()
  getRanges()
  getTerats()
  getSupplierCoding()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

const deleteData = async () => {
  if (modalType.value != 'add') {
    if (erpCustomersDelegateList.value?.length) {
      await custom.erpCustomersDelegate({ids: [...erpCustomersDelegateList.value]?.toString()})
    }
    if (erpCustomersDelegateCommodityList.value?.length) {
      await custom.erpCustomersDelegateCommodity({ids: [...erpCustomersDelegateCommodityList.value]?.toString()})
    }
    if (erpCustomersQualityList.value?.length) {
      await custom.erpCustomersQuality({ids: [...erpCustomersQualityList.value]?.toString()})
    }
    if (deleteGmpList.value?.length) {
      await custom.deleteGmp({ids: [...deleteGmpList.value]?.toString()})
    }

  }
}
const isaddItem = async () => {
  let tem = false
  const setArr = async () => {
    modalTableList.value?.forEach((item, index) => {
      if (item.type === licensedata.value.type) {
        tem = true
        modalTableList.value[index] = licensedata.value
      }
    })
  }
  await setArr()
  if (!tem) {
    modalTableList.value = [...modalTableList.value, licensedata.value]
  }
  return modalTableList.value
}
const deleteNoModalListData = async () => {
  const modalTableListCopy = [...new Set(modalTableList.value.map(JSON.stringify))].map(JSON.parse)
  return modalTableListCopy
}

async function submitForm(type) {
  let fileIndex = 0
  if (type == 3) {
    if (modalType.value != 'detail') {
      ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            open.value = false
            form.value = {
              credentialType: '1'
            }

            modalTableList.value = []
            licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
            modalTableList_entrust.value = []
            modalTableList_warranty.value = []
            modalTableList_file.value = []
            modalTableList_GMP.value = []
            tabKey.value = '1'
            activeNames.value = '1'
            modalLoding.value = false

            zylicenceDTOSCopy.value = {}
            const query = proxy.$route?.query
            if (query && query.type == 'abnormalTask') {
              proxy.$router.push('/abnormalTask')
            }
          })
          .catch(() => {
            // catch error
          });
    } else {
      open.value = false
      form.value = {
        credentialType: '1'
      }
      modalTableList.value = []
      licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
      modalTableList_entrust.value = []
      modalTableList_warranty.value = []
      modalTableList_file.value = []
      modalTableList_GMP.value = []
      tabKey.value = '1'
      activeNames.value = '1'
      modalLoding.value = false

      zylicenceDTOSCopy.value = {}
    }

    return
  }
  proxy.$refs["formRef"].validate(async (valid, fields) => {
    if (!valid) {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          proxy.$refs["formRef"].scrollToField(propName)
        }
      })
      if (activeNames.value.indexOf('1') == -1) {
        activeNames.value.push('1')
      }
    }
    if (form.value.credentialType !== '2') {
      modalTableList.value = await isaddItem()
      modalTableList.value = await deleteNoModalListData()
    }
    const customersDTO = JSON.parse(JSON.stringify(form.value))
    const params = {}
    const licenceDTOS = modalTableList.value?.length ? JSON.parse(JSON.stringify(modalTableList.value)) : []
    const delegateDTOS = modalTableList_entrust.value?.length ? JSON.parse(JSON.stringify(modalTableList_entrust.value)) : []
    const qualityDTOS = modalTableList_warranty.value?.length ? JSON.parse(JSON.stringify(modalTableList_warranty.value)) : []
    let fileDTOS = modalTableList_file.value?.length ? JSON.parse(JSON.stringify(modalTableList_file.value)) : []
    const gmpCertificateDTOS = modalTableList_GMP.value?.length ? JSON.parse(JSON.stringify(modalTableList_GMP.value)) : []
    const {
      zylicenseName,
      zylicenseAddress,
      business,
      zylicenseLegalPerson,
      zylicenseDirector,
      zylicenseStartTime,
      zylicenseValidity,
      zylicenseNo,
      zylicenseOffice,
      zylicenseProject,
      zylicenseRange
    } = form.value
    const zylicenceDTOS = {
      id: form.value?.credentialType == '2' && zylicenceDTOSCopy.value.id,
      zylicenseName,
      zylicenseAddress,
      business,
      zylicenseLegalPerson,
      zylicenseDirector,
      zylicenseStartTime: zylicenseStartTime ? moment(zylicenseStartTime)?.format("YYYY-MM-DD") : undefined,
      zylicenseValidity: moment(zylicenseValidity)?.format("YYYY-MM-DD"),
      zylicenseNo,
      zylicenseOffice,
      zylicenseProject: zylicenseProject?.toString(),
      zylicenseRange: zylicenseRange?.toString()
    }
    const isRegData = () => {
      // if (!licenceDTOS?.length && type == 2 && customersDTO?.credentialType != '2') {
      //     proxy.msgError(`请确认最少填写一条质量信息！`)
      //     return
      // }
      if (!delegateDTOS?.length && type == 2) {
        proxy.msgError(`请确认最少填写一条委托信息！`)

      }
      // if (!qualityDTOS?.length && type == 2) {
      //     proxy.msgError(`请确认最少填写一条质保协议！`)
      //     return
      // }


    }
    const dataDel = () => {
      customersDTO.provinces = customersDTO?.provinces
      customersDTO.issuingTime = customersDTO?.issuingTime ? moment(customersDTO?.issuingTime)?.format("YYYY-MM-DD") : undefined
      customersDTO.businessTerm = customersDTO?.businessTerm ? moment(customersDTO?.businessTerm)?.format("YYYY-MM-DD") : undefined
      customersDTO.apparatusFirst = customersDTO.apparatusFirst == true ? '1' : '0'
      customersDTO.sterilized = customersDTO.sterilized == true ? '1' : '0'
      customersDTO.source = '2'
      if (!customersDTO.apparatusFirst) {
        delete customersDTO.apparatusFirstScopes
        delete customersDTO.apparatusScopeName
      }
      if (!customersDTO.sterilized) {
        delete customersDTO.sterilizedScopes
        delete customersDTO.sterilizedScopeName
      }
      const deleteKey = ['isShowbusiness', 'isShowlicenseAddress', 'isShowlicenseDirector', 'isShowlicenseLegalPerson', 'isShowlicenseNo', 'isShowlicenseOffice', 'isShowlicenseScope', 'isShowlicenseStartTime', 'isShowlicenseValidity', 'isShowqualityDirector', 'isShowzylicenseAddress', 'isShowzylicenseDirector', 'isShowzylicenseLegalPerson', 'isShowzylicenseName', 'isShowzylicenseNo', 'isShowzylicenseOffice']

      licenceDTOS?.length && licenceDTOS?.forEach(item => {
        item.type = item.type && typeDict[item.type]
        item.licenseStartTime = item.licenseStartTime ? moment(item.licenseStartTime)?.format("YYYY-MM-DD") : undefined
        item.licenseValidity = item.licenseValidity ? moment(item.licenseValidity)?.format("YYYY-MM-DD") : undefined
        item.licenseImg = item.licenseImg?.length ? JSON.stringify(item.licenseImg) : undefined
        deleteKey?.forEach(x => {
          delete item[x]
        })
      })
      delegateDTOS?.length && delegateDTOS?.forEach(item => {
        item.remark = item.remark && JSON.stringify(item.remark?.[0])
        item.delegateCard = item.delegateCard && JSON.stringify(item.delegateCard?.[0])
        item.addressList = item?.addressList?.toString()
        item.isEntrustAll = item.isEntrustAll === '1' ? true : item.isEntrustAll === '2' ? false : undefined
        if (item.isAdd) {
          delete item.id
        }
        if (item?.delegateCommodityDTOS?.length) {
          item?.delegateCommodityDTOS?.forEach((x, i) => {
            item.delegateCommodityDTOS[i] = {
              commodityId: x?.id,
              commodityType: x?.commodityType
            }
          })
        }
      })
      qualityDTOS?.length && qualityDTOS?.forEach(item => {
        item.qualityDate = item?.qualityDate ? moment(item?.qualityDate)?.format("YYYY-MM-DD") : undefined
        item.qualityCode = item?.qualityCode && JSON.stringify(item?.qualityCode?.[0])
        if (item.isAdd) {
          delete item.id
        }

      })

      for (var item of fileDTOS) {
        item.fileType =
            item.cardName = item.categoryName
        item.fileName = item.file
        if (item.isUpload == '1' && !item?.fileList?.length) {
          break
        } else {
          fileIndex++
        }
      }
      const fileListArr = []
      const fileName_ = []
      const fileUrl_ = []
      fileDTOS.forEach(item => {
        item.fileList?.forEach(val => {
          fileName_.push(val?.name)
          fileUrl_.push(val?.url)
        })
        fileListArr.push({
          smallType: item.smallType,
          categoryName: item.categoryName,
          isMultiPage: item.isMultiPage,
          fileType: item.smallType,
          cardName: item.categoryName,
          fileName: fileName_.toString(),
          fileUrl: fileUrl_.toString(),
          isUpload: item.isUpload,
          isPage: item.isMultiPage,
          fileNameUrl: JSON.stringify(item.fileList),
          remark: item.remark
        })
      })
      fileDTOS?.length && fileDTOS?.forEach(item => {
        item.fileUrl = item.fileList?.length && item.fileList[0]?.url
        item.fileName = item.fileList?.length && item.fileList[0]?.name
      })
      fileDTOS = fileListArr
      gmpCertificateDTOS?.forEach(item => {
        item.gmpExpiredTime = item.gmpExpiredTime ? moment(item.gmpExpiredTime).format("YYYY-MM-DD") : undefined
        item.gmpCertificatePicture = item.gmpCertificatePicture?.length && JSON.stringify(item.gmpCertificatePicture?.[0])
        item.gmpCertificateScope = item.gmpCertificateScope?.length && item.gmpCertificateScope?.toString()
      })
    }
    const delType = () => {
      params.customersDTO = customersDTO
      params.delegateDTOS = delegateDTOS
      params.qualityDTOS = qualityDTOS
      params.fileDTOS = fileDTOS
      params.gmpCertificateDTOList = gmpCertificateDTOS
      if (form.value?.credentialType != '2') {
        params.licenceDTOS = licenceDTOS
      } else {
        params.zylicenceDTOS = zylicenceDTOS
      }
      if (type == 1) {
        params.operate = 'save'
      }
      if (type == 2) {
        params.operate = 'submit'
      }
      if (Object?.entries(titleDict)?.find(([key, val]) => val === title?.value)?.[0] == 'edit' && type == 1) {
        params.operate = 'update'
      }

      if (Object?.entries(titleDict)?.find(([key, val]) => val === title?.value)?.[0] == 'draft' && type == 1) {
        params.operate = 'update'
      }
    }
    const submitFun = () => {
      // if (params.operate !== 'submit' && !fileDTOS?.length) return proxy.msgError('请填写附件信息！')
      if (fileIndex !== fileDTOS?.length) return proxy.msgError('请上传必传附件！')
      modalLoding.value = true
      custom.save(params).then(res => {
        if (res.code == 200) {
          proxy.msgSuccess('操作成功')
          open.value = false
          form.value = {
            credentialType: '1'
          }
          modalTableList.value = []
          licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
          modalTableList_entrust.value = []
          modalTableList_warranty.value = []
          modalTableList_file.value = []
          modalTableList_GMP.value = []
          tabKey.value = '1'
          activeNames.value = '1'
          modalLoding.value = false
          emit('closeAll')
          const query = proxy.$route?.query
          if (query && query.type == 'abnormalTask') {
            proxy.$router.push('/abnormalTask')
          }
        } else {
          proxy.msgError(res.msg)
          modalLoding.value = false
        }
      }).finally(() => {
        modalLoding.value = false
      })
    }
    const funTotal = () => {
      new Promise((res, rej) => {
        res()
      }).then(() => isRegData()).then(() => dataDel()).then(() => delType()).then(() => {
        if (modalType.value == 'edit') {
          deleteData()
        }
        submitFun()
      })
    }
    if (valid) {
      if (type === 2 && customersDTO?.credentialType == '2') {
        proxy.$refs["modalTableListRef_2"].validate(async (reg, fields) => {
          if (reg) {
            funTotal()
          } else {
            Object.keys(fields).forEach((key, i) => {
              const propName = fields[key][0].field
              if (i == 0) {
                proxy.$refs["modalTableListRef_2"].scrollToField(propName)
              }
            })
            if (activeNames.value.indexOf('2') == -1) {
              activeNames.value.push('2')
            }
          }
        })
      } else if (type === 2 && customersDTO?.credentialType !== '2') {
        proxy.$refs["modalTableListRef"].validate(async (reg, fields) => {
          if (reg) {
            funTotal()
          } else {
            Object.keys(fields).forEach((key, i) => {
              const propName = fields[key][0].field
              if (i == 0) {
                proxy.$refs["modalTableListRef"].scrollToField(propName)
              }
            })
            if (activeNames.value.indexOf('2') == -1) {
              activeNames.value.push('2')
            }
          }
        })

      } else {
        funTotal()
      }

    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$confirm('是否确认删除改数据项?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    custom.delete({ids: row.id}).then(res => {
      if (res.code == 200) {
        proxy.msgSuccess('删除成功')
        getList()
      } else {
        proxy.msgError(res.msg)
      }
    })
  }).catch(() => {
  });
}

async function dict() {
  delFlagList.value = await proxy.getDictList('custom_isEnable')
  reviewStatus.value = await proxy.getDictList('erp_review_status')
  qualificationList.value = await proxy.getDictList('erp_qualification_category')
  modeOfList.value = await proxy.getDictList("erp_mode_of_operation")
  drugTypeList.value = await proxy.getDictList("erp_drug_type")
  businessList.value = await proxy.getDictList("erp_business_type")
  batchTypeList.value = await proxy.getDictList("batch_type")  // 文件批发
  retailTypeList.value = await proxy.getDictList("retail_type")  // 文件零售
  orgTypeList.value = await proxy.getDictList("org_type")  // 文件零售
  fileSelectList.value = batchTypeList.value
  shopType.value = await proxy.getDictList('product_type_config')
  businessNatureList.value = await proxy.getDictList('medical_treatment_business_nature')
  manufacturerType.value = await proxy.getDictList('customer_type')
  directoryFileName.value = await proxy.getDictList('directory_file_name')
  tradeType.value = await proxy.getDictList('trade_type')

}

sysAreasThird.value = proxy.getSysAreasThird
const recursion = (option, key) => {
  option.forEach(v => {
    if (v[key]) {
      v.label = v[key]
      v.value = v.id
      if (modalType.value == 'detail') {
        v.disabled = true
      }
    }
    if (v.children?.length) {
      recursion(v.children, key)
    }
  })
}
const getRanges = async () => {
  let res = await custom.getRange()
  if (res.code == 200) {
    massRangeOption.value = res.data
    recursion(massRangeOption.value, 'massName')
  }
}

const getTerats = async () => {
  let res = await custom.getTerat()
  if (res.code == 200) {
    zylicenseProjectOption.value = res.data
    recursion(zylicenseProjectOption.value, 'treatmentName')
  }
}
const emit = defineEmits(['iconBtn', 'closeAll', 'cusSaveFn'])
const iconBtn = (ind, timeFlag, type) => {
  emit('iconBtn', {
    ind: ind,
    time: timeFlag,
    type: type
  })
}
const chooseTypeFn = (value) => {
  let ind = false
  props.formList.forEach(item => {
    if (item.formSign.split(',').indexOf(value) != -1) {
      ind = true
    }
  })
  if (ind) {
    return true
  } else {
    return false
  }
}

onMounted(() => {
  if (ocrFlag.value) {
    handleGetFileList()
  }
  const query = proxy.$route?.query
  getLicenseScopeListOption()
  getSupplierCoding()
  getRanges()
  getTerats()
  if (query && query.id && query.type == 'abnormalTask') {
    modalLoding.value = true
    open.value = true
    modalType.value = 'edit'
    title.value = titleDict['edit']
    custom.detail({id: query.id}).then(res => {
      if (res.code != 200) return proxy.msgError(res.msg)
      form.value = res.data?.customersDTO
      form.value.apparatusFirst = form.value.apparatusFirst == '1' ? true : false
      form.value.sterilized = form.value.sterilized == '1' ? true : false
      modalTableList_entrust.value = res.data?.delegateDTOS
      modalTableList_entrust.value?.forEach(item => {
        item.remark = item?.remark ? [JSON.parse(item.remark)] : []
        item.delegateCard = item?.delegateCard ? [JSON.parse(item.delegateCard)] : []
        item.addressList = item.addressList && item.addressList?.split(",")
      })
      modalTableList_file.value = res.data?.fileDTOS
      modalTableList_file.value?.forEach(v => {
        v.fileList = [{name: v?.fileName, url: v?.fileUrl}]
      })
      modalTableList.value = res.data?.licenceDTOS
      modalTableList.value?.forEach(item => {
        item.type = Object.entries(typeDict).find(([key, val]) => val === item.type)[0]
        item.licenseImg = item.licenseImg ? JSON.parse(item.licenseImg) : []
      })
      licensedata.value = modalTableList.value[0]
      tabKey.value = modalTableList.value[0]?.type
      modalTableList_warranty.value = res.data?.qualityDTOS
      modalTableList_warranty.value?.forEach(item => {
        item.qualityCode = item.qualityCode ? [JSON.parse(item.qualityCode)] : []
      })
      if (res.data?.zylicenceDTOS) {
        zylicenceDTOSCopy.value = JSON.parse(JSON.stringify(res.data?.zylicenceDTOS))
        form.value = {...res.data?.zylicenceDTOS, ...form.value}
        form.value.zylicenseRange = form.value?.zylicenseRange && form.value?.zylicenseRange?.split(",")
        form.value.zylicenseRange = [...new Set(form.value.zylicenseRange)]
        form.value.zylicenseProject = form.value?.zylicenseProject && form.value?.zylicenseProject?.split(",")
        form.value.zylicenseProject = [...new Set(form.value.zylicenseProject)]
      }
      modalTableList_GMP.value = res.data?.gmpCertificateDTOList || []
      modalTableList_GMP.value?.forEach(item => {
        item.gmpCertificatePicture = item.gmpCertificatePicture ? [JSON.parse(item.gmpCertificatePicture)] : []
        item.gmpCertificateScope = item.gmpCertificateScope ? item.gmpCertificateScope?.split(',') : []
      })
      modalLoding.value = false
    }).finally(() => {
      modalLoding.value = false
    })
  }
  dict()
  getList();
  sysAreas.value = proxy.getSysAreasThird.map(v => {
    return {value: v.value, label: v.label}
  })
  if (smallTypeForm.value == 1 || smallTypeForm.value == 2 || smallTypeForm.value == 3 || smallTypeForm.value == 4) {
    form.value.credentialType = '1'
  } else if (smallTypeForm.value == 5 || smallTypeForm.value == 6 || smallTypeForm.value == 7 || smallTypeForm.value == 8) {
    form.value.credentialType = '3'
  } else {
    form.value.credentialType = '2'
  }
  sysAreas.value = [{label: '全国', value: '1', id: '1'}, ...sysAreas.value]
})

defineExpose({
  submitForm,
  form,
  modalTableList,
  licensedata,
  entrustSubmit,
  tabKey,
  modalTableList_entrust,
  handleAdd_entrust,
  entrustVisible,
  handleImgEntrustClose,
  entrust,
  warrantySubmit,
  modalTableList_warranty,
  handleAdd_warranty,
  warrantyVisible,
  handleImgWarrantyClose,
  modalTableList_GMPRef,
  modalTableList_GMP,
  modalTableList_file,
  warranty
})
const columns = ref([
  {
    label: '序号',
    prop: 'commodityCommonName',
    type: "sort",
    fixed: 'left',
  },
  {
    label: '资质类别',
    prop: 'credentialType',
    type: 'status',
    filters: qualificationList
  },
  {
    label: '企业名称',
    prop: 'enterpriseName'
  },
  {
    label: '拼音码',
    prop: 'pinyinCode',
  },
  {
    label: '自编码',
    prop: 'selfCoding',
  },
  {
    label: '统一社会信用代码',
    prop: 'socialCreditCode',
  },
  {
    label: '审核状态',
    prop: 'status',
    type: 'status',
    filters: reviewStatus
  },
  {
    label: '禁用状态',
    prop: 'isEnable',
    type: 'status',
    filters: delFlagList
  },
  {
    label: '创建日期',
    prop: 'createDate',
    type: 'date'
  },
  {
    label: '修改日期',
    prop: 'updateDate',
    type: 'date'
  },
  {
    label: '操作',
    prop: 'operate',
    type: 'operate',
    minWidth: 300,
    fixed: 'right'
  },
])
</script>
<style lang="scss" scoped>
.titleForm {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
}
.col_title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 8px;

  &::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 20px;
    background-color: #2878ff;
    border-radius: 2px;
    position: absolute;
    top: 15px;
    left: 0;
  }
}

.box {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  // grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: start;
}

.box_2 {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}

.box_1 {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}

.el-tabs {
  display: flex;
  // align-items: center;
}

.el-tabs__nav-wrap::after {
  width: 0 !important;
}

::v-deep .Botm {
  .el-card__body {
    padding-bottom: 0px
  }
}

::v-deep .rules {
  position: relative;

  .cell::after {
    content: "*";
    color: red;
    display: inline-block;
    position: absolute;
    top: 30%;
    left: 70px;
  }
}

::v-deep .rulesRemark {
  position: relative;

  .cell::after {
    content: "*";
    color: hwb(17 0% 0%);
    display: inline-block;
    position: absolute;
    top: 30%;
    left: calc(50% - 30px)
  }
}

::v-deep .serchBtn {
  .el-form-item__content {
    display: flex;
    justify-content: end;
  }
}

.box_date {
  width: 220px;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep input[type="number"] {
  -moz-appearance: textfield;
  /* 此处写不写都可以 */
}


.checkBox {
  margin-right: 30px;
  margin-left: 30px
}

.checkBoxRight {
  width: 640px
}

::v-deep .el-upload-dragger {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

::v-deep .step2 {
  .el-button {
    // border-bottom: none;
    border-right: none;
    border-radius: 4px 0 0 4px;

  }

  .el-button + .el-button {
    margin-left: 0;
    border-radius: 0 0 0 0;
    // border-left: none;
  }
}

.btn_cmt {
  margin-bottom: 20px;
}

.last-btn {
  border: 1px solid #dcdfe6 !important;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
  display: contents !important;
}

.fileName_t {
  display: flex;
  width: 100%;
  color: #2a76f8;
  cursor: pointer;
  align-items: center;
  margin-top: 4px;

  span:nth-of-type(2) {
    display: none;
    margin-left: 10px;

    ::v-deep .el-icon {
      // margin-top:10px;
      font-size: 12px;
      color: red
    }
  }
}

.fileName_t:hover .fileName_t_icon {
  display: block;
}
</style>
