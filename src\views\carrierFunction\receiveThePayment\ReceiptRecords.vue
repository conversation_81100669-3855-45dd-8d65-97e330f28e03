<template>
    <div v-loading="fullLoading" :element-loading-text="fullLoadingText" class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form" @submit.prevent>
                <el-form-item label="到款单位/人" prop="arriveUnit" style="width: 230px">
                    <el-input v-model="queryParams.arriveUnit" clearable placeholder="请输入到款单位/人" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="到款日期" prop="queryTime" style="width: 310px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item label="认款状态" prop="status" style="width: 230px">
                    <el-select v-model="queryParams.status" clearable filterable placeholder="请选择认款状态" style="width: 100%;" @change="handleQuery">
                        <el-option v-for="item in statusOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="到款渠道" prop="arriveSource" style="width: 310px">
                    <el-select v-model="queryParams.arriveSource" clearable filterable placeholder="请选择到款渠道" style="width: 100%;" @change="handleQuery">
                        <el-option v-for="item in arriveSourceOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="货主公司" prop="payCompany" style="width: 310px">
                    <el-input v-model="queryParams.payCompany" clearable placeholder="请输入货主公司" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收款公司" prop="receiverCompany" style="width: 310px">
                    <el-select v-model="queryParams.receiverCompany" clearable filterable placeholder="请选择收款公司" style="width: 100%;" @change="handleQuery">
                        <el-option v-for="item in receiverCompanyOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="结算公司" prop="settleCompany" style="width: 310px">
                    <el-input v-model="queryParams.settleCompany" clearable placeholder="请输入结算公司" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="认款日期" prop="claimDate" style="width: 310px">
                    <el-date-picker v-model="queryParams.claimDate" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <div class="flex justify-around">
                <el-statistic :precision="2" :value="statisticalData.totalArriveMoney" :value-style="{ color: '#3cccca', fontWeight: 'bold' }" group-separator="," title="合计到款金额："></el-statistic>
                <el-statistic :precision="2" :value="statisticalData.totalClaimMoney" :value-style="{ color: '#ac86ff', fontWeight: 'bold' }" group-separator="," title="确认金额："></el-statistic>
                <el-statistic :precision="2" :value="statisticalData.unrecognizedAmount" :value-style="{ color: '#feb119', fontWeight: 'bold' }" group-separator="," title="未确认金额："></el-statistic>
                <el-statistic :precision="2" :value="statisticalData.totalAccountMoney" :value-style="{ color: '#53c2ff', fontWeight: 'bold' }" group-separator="," title="入账金额："></el-statistic>
                <el-statistic :precision="2" :value="statisticalData.totalChargeMoney" :value-style="{ color: '#fbb1a1', fontWeight: 'bold' }" group-separator="," title="手续费："></el-statistic>
            </div>
        </el-card>
        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 16px">
                <div style="display: inline-flex; align-items: center">
                    <el-button icon="el-icon-plus" size="mini" type="primary" @click="handleAdd">上传到款记录</el-button>
                    <el-button v-hasPermi="['tms:arrive:moneyRecord:export']" :disabled="dataList.length == 0" icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                </div>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :tableID="'ReceiptRecords'" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="ReceiptRecords" ref="ColumnTable" v-loading="loading" :max-height="600" :columns="columns" :data="dataList">
                <template #arriveDate="{ row }">
                    <span>{{ row.arriveDate ? formatDate(row.arriveDate) : '--' }}</span>
                </template>
                <template #status="{ row }">
                    <span>{{ row.status ? selectDictLabel(statusOptions, row.status) : '--' }}</span>
                </template>
                <template #receiverCompany="{ row }">
                    <span>{{ row.receiverCompany ? selectDictLabel(receiverCompanyOptions, row.receiverCompany) : '--' }}</span>
                </template>
                <template #arriveSource="{ row }">
                    <span>{{ selectDictLabel(arriveSourceOptions, row.arriveSource)}}</span>
                </template>
                <template #chargeAmount="{ row }">
                    <span>{{ row.chargeAmount?row.chargeAmount:'--'}}</span>
                </template>
                <template #accountAmount="{ row }">
                    <span>{{ row.accountAmount?row.accountAmount:'--'}}</span>
                </template>

                <template #opt="{ row }">
                    <el-button v-if="row.status == '1'" v-hasPermi="['tms:arrive:moneyRecord:del']" icon="el-icon-delete" link size="small" type="danger" @click="deleteHandle(row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
        <!--    上传到款记录-->
        <el-dialog v-if="uploadMoneyRecordToOpen" v-model="uploadMoneyRecordToOpen" append-to-body dialogDrag title="到款记录" width="600px" @close="uploadMoneyRecordClose">
            <div class="text-base" style="width: 80%; margin: auto; padding: 10px 0">
                <div class="flex justify-between">
                    <span v-hasPermi="['tms:arrive:moneyRecord:downloadTemplate']">模板链接</span>
                    <span v-hasPermi="['tms:arrive:moneyRecord:uploadDataFile']">上传文件</span>
                </div>
                <el-divider style="margin: 10px 0"></el-divider>
                <div class="flex justify-between">
                    <div v-hasPermi="['tms:arrive:moneyRecord:downloadTemplate']" class="text-main-500 flex items-center">下载<el-button link type="primary" @click="downloadTemplate">到款记录上传模板.xls</el-button></div>
                    <el-upload
                        ref="upload"
                        v-hasPermi="['tms:arrive:moneyRecord:uploadDataFile']"
                        v-model:file-list="fileList"
                        :action="uploadFileUrl"
                        :auto-upload="true"
                        :before-upload="beforeUpload"
                        :headers="headers"
                        :limit="1"
                        :on-success="(res) => handleUploadSuccess(res)"
                        :show-file-list="false"
                        accept=".xls, .xlsx"
                        class="upload-demo"
                    >
                        <template #trigger>
                            <el-button type="primary">
                                <el-icon style="margin-right: 5px">
                                    <UploadFilled />
                                </el-icon>
                                上传
                            </el-button>
                        </template>
                    </el-upload>
                </div>
                <div v-if="uploadReturnData?.analysis?.successData?.length > 0">
                    <p v-if="uploadReturnData?.analysis?.successMsg" class="text-green-500" v-html="uploadReturnData.analysis.successMsg"></p>
                </div>
                <div v-if="uploadReturnData?.analysis?.errorData?.length > 0">
                    <p v-if="uploadReturnData?.analysis?.failureMsg" class="text-red-500" v-html="uploadReturnData.analysis.failureMsg"></p>
                </div>
            </div>
            <template #footer>
                <el-button @click="uploadMoneyRecordClose">取 消</el-button>
                <el-button :disabled="uploadDisabled" type="primary" @click="uploadMoneyRecordConfirm">确认</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import receiptRecords from '@/api/carrierEnd/receiveThePayment/receiptRecords';
import { ElMessageBox, ElMessage } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { downloadNoData } from '@/utils';
import moment from 'moment';
import tool from '@/utils/tool';
import { setDatePickerShortcuts } from '@/utils/config-store';
export default {
    name: 'ReceiptRecords',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar,
        UploadFilled
    },
    data() {
        return {
            fullLoading: false, // 全屏加载
            fullLoadingText: '正在导出数据，请稍等...',
            showSearch: true, // 显示搜索
            isShowAll: false, // 显示全部
            queryParams: {
                current: 1,
                size: 10,
                arriveUnit: undefined, // 到款单位/人
                queryTime: [], // 到款日期
                status: null, // 认款状态
                receiverCompany: undefined, // 收款公司
                claimDate: [], // 认款日期
                arriveSource:undefined, // 到款渠道
                payCompany:undefined, // 货主公司
                settleCompany:undefined, // 结算公司
            },
            shortcuts: setDatePickerShortcuts(),
            dataList: [], // 列表数据
            loading: false, // 加载状态
            columns: [
                { title: '到款日期', key: 'arriveDate', align: 'center', width: '110px', columnShow: true },
                { title: '到款单位/人', key: 'arriveUnit', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '到款金额', key: 'arriveAmount', align: 'center', width: '100px', columnShow: true },
                { title: '到款渠道', key: 'arriveSource', align: 'center', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '手续费', key: 'chargeAmount', align: 'center', width: '100px', columnShow: true },
                { title: '入账金额', key: 'accountAmount', align: 'center', width: '100px', columnShow: true },
                { title: '收款公司', key: 'receiverCompany', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '认款状态', key: 'status', align: 'center', width: '80px', columnShow: true },
                { title: '认款日期', key: 'claimDate', align: 'center', width: '110px', columnShow: true },
                { title: '认款金额', key: 'claimAmount', align: 'center', width: '100px', columnShow: true },
                { title: '货主公司', key: 'payCompany', align: 'center', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settleCompany', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '待认款金额', key: 'stayAmount', align: 'center', width: '100px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', fixed: 'right', width: '80px', hideFilter: true, columnShow: true,showOverflowTooltip: true }
            ], // 表格列
            total: 0, // 总条数
            statusOptions: [], // 认款状态
            receiverCompanyOptions: [], // 收款公司
            arriveSourceOptions:[], //  到款渠道
            statisticalData: {
                totalArriveMoney: 0, // 合计到款金额
                totalClaimMoney: 0 ,// 确认金额
				unrecognizedAmount: 0 ,// 未确认金额
                totalAccountMoney:0, //    入账金额
                totalChargeMoney:0 // 手续费
            }, // 统计数据
            uploadMoneyRecordToOpen: false, // 上传到款记录
            //文件上传定义
            uploadFileUrl: process.env.VUE_APP_API_GAENT + '/tms/arrive/moneyRecord/uploadDataFile', // 上传的图片服务器地址
            fileList: [], // 文件列表
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            }, // 上传文件请求头
            // 大小限制(MB)
            fileSize: 5,
            fileType: ['xls', 'xlsx'], // 上传的文件类型
            uploadReturnData: {}, // 上传返回数据
            uploadDisabled: false // 上传按钮禁用
        };
    },
    computed: {
        /**
         * 格式化日期
         * @returns {function(*=): *}
         */
        formatDate() {
            return function (date) {
                return moment(date).format('YYYY-MM-DD');
            };
        }
    },
    created() {
        this.getDict();
        this.handleQuery();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            // 认款状态
            this.statusOptions = await this.getDictList('fourpl_subscription_status');
            // 收款公司
            this.receiverCompanyOptions = await this.getDictList('signing_company');
            // 到款渠道
            this.arriveSourceOptions = await this.getDictList('fourpl_arrive_source');

        },
        getTotalMoney() {
            this.statisticalData = {
                totalArriveMoney: 0,
                totalClaimMoney: 0,
				unrecognizedAmount: 0,
                totalAccountMoney:0, //    入账金额
                totalChargeMoney:0 // 手续费
            };
            const { queryTime, claimDate,current,size, ...params } = this.queryParams;
            receiptRecords.getTotalMoney(params).then((res) => {
                if (res.code == 200) {
                    this.statisticalData = res.data;
                    this.statisticalData.unrecognizedAmount = parseFloat(this.statisticalData.totalArriveMoney) - parseFloat(this.statisticalData.totalClaimMoney);
                }
            });
        },
        /**
         * 获取列表数据
         */
        getList() {
            this.loading = true;
            this.dataList = [];
            const { queryTime, claimDate, ...params } = this.queryParams;
            receiptRecords
                .getMoneyRecordList(params)
                .then((res) => {
                    if (res.code == 200) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {
                    this.dataList = [];
                    this.total = 0;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 上传到款记录 -弹出
         */
        handleAdd() {
            this.uploadMoneyRecordToOpen = true;
        },
        /**
         * 下载模板
         */
        downloadTemplate() {
            receiptRecords.downloadTemplate('', '', '', 'blob').then((res) => {
                downloadNoData(res, 'application/vnd.ms-excel', '到款记录上传模板.xls');
            });
        },
        uploadMoneyRecordClose() {
            this.uploadMoneyRecordToOpen = false;
            this.uploadReturnData = {};
            this.fileList = [];
        },
        /**
         * 上传到款记录 -确认
         */
        uploadMoneyRecordConfirm() {
            if (this.uploadReturnData?.analysis?.successData?.length == 0) {
                this.msgError('没有可以上传的数据');
                return;
            }
            this.uploadDisabled = true;
            receiptRecords
                .batchImport({ dataList: this.uploadReturnData.analysis.successData })
                .then((res) => {
                    if (res.code == 200) {
                        this.msgSuccess('上传成功');
                        this.uploadMoneyRecordClose();
                        this.getList();
                        this.getTotalMoney();
                    }
                })
                .finally(() => {
                    this.uploadDisabled = false;
                });
        },
        /**
         * 上传到款记录 -上传成功
         * @param res
         */
        handleUploadSuccess(res) {
            if (res.code == 200) {
                this.uploadReturnData = res.data;
            }
        },
        // 验证文件格式与大小
        beforeUpload(file) {
            // 校检文件类型
            if (this.fileType) {
                let fileExtension = '';
                if (file.name.lastIndexOf('.') > -1) {
                    fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
                }
                const isTypeOk = this.fileType.some((type) => {
                    if (file.type.indexOf(type) > -1) return true;
                    if (fileExtension && fileExtension.indexOf(type) > -1) return true;
                    return false;
                });
                if (!isTypeOk) {
                    this.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`);
                    return false;
                }
            }
            // 校检文件大小
            if (this.fileSize) {
                const isLt = file.size / 1024 / 1024 < this.fileSize;
                if (!isLt) {
                    this.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
                    return false;
                }
            }
            return true;
        },
        /**
         * 删除到款记录
         * @param row
         */
        deleteHandle(row) {
            ElMessageBox.confirm('确认删除此项吗?', '提示', {
                type: 'warning',
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            })
                .then(() => {
                    receiptRecords
                        .delMoneyRecord({ ids: row.id })
                        .then((res) => {
                            if (res.code === 200) {
                                this.msgSuccess('删除成功！');
                                this.getList();
                            }
                        })
                        .catch(() => {
                            this.msgError('删除失败！');
                        });
                })
                .catch(() => {
                    ElMessage({
                        message: '删除取消!',
                        type: 'info'
                    });
                });
        },
        /**
         * 切换查询显示
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 重置查询
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryParams = {
                current: 1,
                size: 10,
                arriveUnit: undefined, // 到款单位/人
                queryTime: [], // 到款日期
                status: null, // 认款状态
                receiverCompany: undefined, // 收款公司
                claimDate: [], // 认款日期
                arriveSource:undefined, // 到款渠道
                payCompany:undefined, // 货主公司
                settleCompany:undefined, // 结算公司
            };
            this.handleQuery();
        },
        /**
         * 查询
         */
        handleQuery() {
            const { queryTime, claimDate } = this.queryParams;
            this.queryParams.startArriveDate = undefined;
            this.queryParams.endArriveDate = undefined;
            if (queryTime != undefined && queryTime.length != 0 && queryTime[0] != 'Invalid Date') {
                this.queryParams.startArriveDate = queryTime[0] + ' 00:00:00';
                this.queryParams.endArriveDate = queryTime[1] + ' 23:59:59';
            }
            this.queryParams.startClaimDate = undefined;
            this.queryParams.endClaimDate = undefined;
            if (claimDate != undefined && claimDate.length != 0 && claimDate[0] != 'Invalid Date') {
                this.queryParams.startClaimDate = claimDate[0] + ' 00:00:00';
                this.queryParams.endClaimDate = claimDate[1] + ' 23:59:59';
            }
            this.queryParams.current = 1;
            this.getList();
            this.getTotalMoney();
        },
        /** 导出按钮操作 */
        handleExport() {
            this.fullLoading = true;
            const { queryTime, claimDate, current, size, ...params } = this.queryParams;
            receiptRecords
                .exportList({ filename: '到款记录.xls', ...params }, { responseType: 'blob' }, '')
                .then((res) => {
                    downloadNoData(res, 'application/vnd.ms-excel', '到款记录.xlsx');
                })
                .catch(() => {})
                .finally(() => {
                    this.fullLoading = false;
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-statistic {
    display: flex;
    align-items: baseline;
    margin-right: 20px;
    gap: 5px;
}
</style>
