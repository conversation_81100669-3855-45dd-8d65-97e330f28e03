<template>
    <div>
        <!-- 表头 -->
        <el-card class="box-card Botm">
            <TopTitle title="筛选">
            </TopTitle>
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item label="诊疗范围" prop="type">
                    <el-tree-select v-model="queryParams.diagnosis" :data="diaSelectList" :render-after-expand="false"
                        @node-click="getCheckedNodes" placeholder="请选择诊疗范围"
                        :props="{ value: 'treatmentName', label: 'treatmentName' }" class="form_225" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchQuality">搜索</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <div>
            <el-card style="margin:10px;">
                <TopTitle title="查询列表">
                    <el-button type="primary" @click="handlerCreat(creatform)" style="float: right;">新增</el-button>
                </TopTitle>
                <!--表单区域 -->
                <el-table class="el-table" border :data="diagnosisList1">
                    <!-- <el-table-column label="ID" align="center" prop="id" /> -->
                    <el-table-column label="诊疗科目" align="left" prop="treatmentName" />
                    <el-table-column label="经营范围" align="left" prop="massRangeSetNames" :formatter="classFormat"
                        :show-overflow-tooltip="true" />
                    <el-table-column label="处方类型" align="left" prop="prescriptionTypeValues"
                        :show-overflow-tooltip="true" />
                    <el-table-column label="药品剂型" align="left" prop="dosageFormValues" :show-overflow-tooltip="true" />
                    <el-table-column label="创建时间" align="left" prop="createDate">
                        <template #default="scope">
                            {{ formatDate(scope.row.createDate) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="修改时间" align="left" prop="updateDate">
                        <template #default="scope">
                            {{ formatDate(scope.row.updateDate) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <el-button link type="primary" @click="handleEdit(scope.row)"><img
                                    src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                            <el-button link type="danger" @click="handleDelete(scope.row)"><img
                                    src="@/assets/icons/delete.png" style="margin-right:5px" />删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div style="float: right;">
                    <pagination :total="queryParams.total" v-model:page="queryParams.pageNum"
                        v-model:limit="queryParams.pageSize" @pagination="getList" />
                </div>
            </el-card>
            <el-dialog v-model="dialogFormVisible" :title="!state.boxFlag ? '新增配置' : '修改配置'" style="width:550px ;"
                :close-on-press-escape="false" :close-on-click-modal="false">
                <!-- 创建 -->
                <div v-show="!state.boxFlag">
                    <el-form :model="form" label-position="right" ref="creatform" :rules="creatRules">
                        <el-form-item label="诊疗范围" prop="diagnosis">
                            <el-tree-select v-model="form.diagnosis" :data="diaSelectList" :render-after-expand="false"
                                @node-click="getCheckedNodes" placeholder="请选择诊疗范围"
                                :props="{ value: 'treatmentName', label: 'treatmentName' }" />
                        </el-form-item>
                        <!-- <el-form-item label="诊疗范围" prop="diagnosis">
                            <el-tree-select v-model="value" :data="data2" multiple :render-after-expand="false"
                                show-checkbox />
                        </el-form-item> -->
                        <el-form-item label="处方类型" prop="pretype">
                            <el-select v-model="form.pretype" placeholder="请选择处方类型" class="el-select" multiple
                                value-key="id">
                                <template #empty>
                                    <div>
                                        <p style="text-align: center;color: #635f5e;margin:40px 0;">暂无数据</p>
                                    </div>
                                    <p style="text-align: center">
                                        <el-button size="small" style="margin: 0px 0 15px 0" type="primary"
                                            @click="getList2">
                                            返回
                                        </el-button>
                                    </p>
                                </template>
                                <el-input v-model="drugList2" v-show="flag" clearable placeholder="请输入处方类型"
                                    @keydown.enter="handlerDrug2" @clear="handlerDrug2" style="width:90%;margin-left:10px;">
                                    <template v-slot:suffix>
                                        <el-icon @click="handlerDrug2">
                                            <Search />
                                        </el-icon>
                                    </template>
                                </el-input>
                                <el-option :label="item.valueName" :value="item.id" v-for=" item  in  diaSelectList2"
                                    :key="item.id" @click.native="handlerId2(item)" />
                            </el-select>
                        </el-form-item>
                        <!-- <el-button type="primary" v-if="flag" size="small" @click="">返回</el-button> -->
                        <el-form-item label="药品剂型" prop="managetype">
                            <el-select v-model="form.managetype" placeholder="请选择药品剂型" class="el-select" multiple>
                                <template #empty>
                                    <div>
                                        <p style="text-align: center;color: #635f5e;margin:40px 0;">暂无数据</p>
                                    </div>
                                    <p style="text-align: center">
                                        <el-button size="small" style="margin: 0px 0 15px 0" type="primary"
                                            @click="getList3">
                                            返回
                                        </el-button>
                                    </p>
                                </template>
                              <el-input v-model="drugList" clearable placeholder="请输入药品剂型"
                                        style="width:90%;margin-left:10px;"
                                        @clear="handlerDrug" @keydown.enter="handlerDrug">
                                    <template v-slot:suffix>
                                        <el-icon @click="handlerDrug">
                                            <Search />
                                        </el-icon>
                                    </template>
                                </el-input>
                                <el-option :label="item.valueName" :value="item.id" v-for=" item  in  diaSelectList3"
                                    :key="item.id" @click.native="handlerId3(item)" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="经营范围" prop="commodity">
                            <el-tree-select style="width:100%" v-model="form.commodity" placeholder="请选择经营范围"
                                :data="treedata" multiple :render-after-expand="false" show-checkbox check-on-click-node
                                :default-checked-keys="treeArr" node-key="id" @check="currentChecked"
                                :props="{ value: 'id', label: 'massName' }" />
                        </el-form-item>
                    </el-form>
                    <div class="footer">
                        <el-button type="primary" @click="creat(creatform)">确定</el-button>
                        <el-button @click="dialogFormVisible = false">取消</el-button>
                    </div>
                </div>
                <!-- 编辑 -->
                <div v-show="state.boxFlag">
                    <el-form :model="editForm" label-position="right" ref="editform" :rules="creatRules2">
                        <el-form-item label="诊疗范围" prop="treatmentName">
                            <el-tree-select v-model="editForm.treatmentName" :data="diaSelectList"
                                :render-after-expand="false" @node-click="getCheckedNodes" placeholder="请选择诊疗范围"
                                :props="{ value: 'treatmentName', label: 'treatmentName' }" />
                        </el-form-item>
                        <el-form-item label="处方类型" prop="prescriptionTypeIds">
                            <el-select v-model="editForm.prescriptionTypeIds" placeholder="请选择处方类型" class="el-select"
                                multiple filterable allow-create :reserve-keyword="false" value-key="id">
                                <template #empty>
                                    <div>
                                        <p style="text-align: center;color: #635f5e;margin:40px 0;">暂无数据</p>
                                    </div>
                                    <p style="text-align: center">
                                        <el-button size="small" style="margin: 0px 0 15px 0" type="primary"
                                            @click="getList2">
                                            返回
                                        </el-button>
                                    </p>
                                </template>
                                <el-input v-model="drugList2" v-show="flag" clearable placeholder="请输入处方类型"
                                    @keydown.enter="handlerDrug2" @clear="handlerDrug2" style="width:90%;margin-left:10px;">
                                    <template v-slot:suffix>
                                        <el-icon @click="handlerDrug2">
                                            <Search />
                                        </el-icon>
                                    </template>
                                </el-input>
                                <el-option :label="item.valueName" :value="item.id" v-for=" item  in  diaSelectList2"
                                    :key="item.id" @click.native="handlerId2(item)" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="药品剂型" prop="dosageFormIds">
                            <el-select v-model="editForm.dosageFormIds" placeholder="请选择药品剂型" class="el-select" multiple>
                                <template #empty>
                                    <div>
                                        <p style="text-align: center;color: #635f5e;margin:40px 0;">暂无数据</p>
                                    </div>
                                    <p style="text-align: center">
                                        <el-button size="small" style="margin: 0px 0 15px 0" type="primary"
                                            @click="getList3">
                                            返回
                                        </el-button>
                                    </p>
                                </template>
                                <el-input v-model="drugList" clearable placeholder="请输入药品剂型" @keydown.enter="handlerDrug"
                                    @clear="handlerDrug" style="width:90%;margin-left:10px;">
                                    <template v-slot:suffix>
                                        <el-icon @click="handlerDrug">
                                            <Search />
                                        </el-icon>
                                    </template>
                                </el-input>
                                <el-option :label="item.valueName" :value="item.id" v-for=" item  in  diaSelectList3"
                                    :key="item.id" @click.native="handlerId3(item)" />
                                <!-- <el-pagination v-model:current-page="current2" v-model:page-size="size2" :total="total2"
                                    layout="->,total, prev, pager, next, jumper" @current-change="getList3" /> -->
                            </el-select>
                        </el-form-item>
                        <el-form-item label="经营范围" prop="massRangeSetNames">
                            <el-tree-select class="treeselect" v-model="editForm.massRangeSetNames" placeholder="请选择经营范围"
                                :data="treedata" multiple :render-after-expand="false" show-checkbox check-on-click-node
                                :default-checked-keys="treeArr" node-key="massName" @check="currentChecked2"
                                :props="{ value: 'massName', label: 'massName' }" />
                        </el-form-item>
                    </el-form>
                    <div class="footer">
                        <el-button type="primary" @click="edit(editform)">确定</el-button>
                        <el-button @click="dialogFormVisible = false">取消</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, watchEffect, computed, getCurrentInstance, watch } from 'vue';
// import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import TopTitle from '@/components/topTitle'
import diagnosisList from "@/api/erp/diagnosisList";
import { massRange } from "@/api/model/assist/massRange/index";
import { Search } from "@element-plus/icons-vue";
import erpBaseCommonValuesService from '@/api/erp/assist/erpBaseCommonValuesService';
import { ElMessage } from "element-plus";
const { proxy } = getCurrentInstance();
const value = ref()
const dialogFormVisible = ref(false);
const flag = ref(true)
const ids = ref();
const treeselect = ref()
const creatform = ref()
const editform = ref()
const diaSelectList = ref([])
const diaSelectList2 = ref([])
const diaSelectList3 = ref([])
const drugList = ref('')
const drugList2 = ref('')
const treatmentScopeSetid = ref('')
const prescriptionTypeIds = ref('')
const dosageFormIds = ref('')
const massRangeSetIds = ref('')
const editTypevalue = ref()
const editMassRangeid = ref()
const treedata = ref([]);
const current2 = ref(1)
const size2 = ref(10)
const total2 = ref(0)
const editForm = reactive({})
const editDosagevalue = ref()
const editTreatmentScopeSetid = ref()
const diagnosisList1 = ref([]);
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 10,
    type: null,
    commodityClass: null,
    diagnosis: ''
});
const pretypeValue = ref('')
const pretypeId = ref('')
const managetypeValue = ref('')
const managetypeId = ref('')
const commodityId = ref('')
const form = reactive({
    diagnosis: '',
    pretype: '',
    managetype: '',
    commodity: ''
})
const form2 = reactive({
    diagnosis: '',
    pretype: '',
    managetype: '',
    commodity: ''
})
const state = reactive({
    pageNo: 1,
    pageSize: 10,
    dataCount: 0,
    boxFlag: false
});
const creatRules = reactive({
    diagnosis: [{ required: true, message: "请选择诊疗范围", trigger: "blur" }],
    pretype: [{ required: true, message: "请选择处方类型", trigger: "blur" }],
    managetype: [{ required: true, message: "请选择药品剂型", trigger: "blur" }],
    commodity: [{ required: true, message: "请选择经营范围", trigger: "blur" }],
});
const creatRules2 = reactive({
    treatmentName: [{ required: true, message: "请选择诊疗范围", trigger: "blur" }],
    prescriptionTypeIds: [{ required: true, message: "请选择处方类型", trigger: "blur" }],
    dosageFormIds: [{ required: true, message: "请选择药品剂型", trigger: "blur" }],
    massRangeSetNames: [{ required: true, message: "请选择经营范围", trigger: "blur" }],
});
//药品剂型搜索
const handlerDrug = () => {
    erpBaseCommonValuesService.list({
        "item.id": 6,
        valueName: drugList.value,
    }).then(res => {
        if (res.code == 200) {
            diaSelectList3.value = res.data.records
        }
    })
}
//处方剂型搜索
const handlerDrug2 = () => {
    erpBaseCommonValuesService.list({
        "item.id": 5,
        valueName: drugList2.value,
    }).then(res => {
        if (res.code == 200) {
            diaSelectList2.value = res.data.records
        }
    })
}
// 格式化日期
function formatDate(d) {
    var date = new Date(d);
    var YY = date.getFullYear() + "-";
    var MM =
        (date.getMonth() + 1 < 10
            ? "0" + (date.getMonth() + 1)
            : date.getMonth() + 1) + "-";
    var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    return YY + MM + DD;
}
//保存id
function handlerId(items) {
    treatmentScopeSetid.value = items.id
}
function getCheckedNodes(node, e) {
    if (!node.children) {
        form.diagnosis = node.treatmentName
        treatmentScopeSetid.value = node.id
    }
}
function handlerId2(items) {
    var str = ""
    for (var i = 0; i < form.pretype.length; i++) {
        str += form.pretype[i] + ",";
    }
    if (str.length > 0) {
        str = str.substr(0, str.length - 1);
        // form.pretype=str
        pretypeId.value = str
    }
}
function handlerId3(items) {
    var str = ""
    for (var i = 0; i < form.managetype.length; i++) {
        str += form.managetype[i] + ",";
    }
    if (str.length > 0) {
        str = str.substr(0, str.length - 1);
        managetypeId.value = str
    }
}
// 监听
watch(
    () => form.pretype,
    (newValue) => {
        let newarr = []
        diaSelectList2.value.forEach(
            (item) => {
                newValue.forEach(items => {
                    if (items == item.id) {
                        newarr.push(item)
                    }
                })
            }
        );
        var str = "";
        for (var i = 0; i < newarr.length; i++) {
            str += newarr[i].valueName + ",";
        }
        //去掉最后一个逗号(如果不需要去掉，就不用写)
        if (str.length > 0) {
            str = str.substr(0, str.length - 1);
            pretypeValue.value = str
        }
    }
);
watch(
    () => form.managetype,
    (newValue) => {
        let newarr = []
        diaSelectList3.value.forEach(
            (item) => {
                newValue.forEach(items => {
                    if (items == item.id) {
                        newarr.push(item)
                    }
                })
            }
        );
        var str = "";
        for (var i = 0; i < newarr.length; i++) {
            str += newarr[i].valueName + ",";
        }
        //去掉最后一个逗号(如果不需要去掉，就不用写)
        if (str.length > 0) {
            str = str.substr(0, str.length - 1);
            managetypeValue.value = str
        }
    }
);

function currentChecked(nodeObj, SelectedObj) {
    console.log(SelectedObj.checkedKeys);
    form.commodity = SelectedObj.checkedKeys
    // form.commodity.push(SelectedObj.checkedKeys)
    // form.commodity.push(SelectedObj.checkedKeys.join(","))
    // ...{SelectedObj.checkedKeys}
    console.log(form.commodity);
    var str = "";
    for (var i = 0; i < SelectedObj.checkedNodes.length; i++) {
        str += SelectedObj.checkedNodes[i].id + ",";
    }
    //去掉最后一个逗号(如果不需要去掉，就不用写)
    if (str.length > 0) {
        str = str.substr(0, str.length - 1);
        massRangeSetIds.value = str
    }
    var str = "";
    for (var i = 0; i < SelectedObj.checkedNodes.length; i++) {
        str += SelectedObj.checkedNodes[i].massName + ",";
    }
    //去掉最后一个逗号(如果不需要去掉，就不用写)
    if (str.length > 0) {
        str = str.substr(0, str.length - 1);
        commodityId.value = str
    }
}
//搜索
function searchQuality() {
    var params = {
        treatmentName: queryParams.diagnosis
    }
    diagnosisList.list(params).then(res => {
        console.log(res);
        if (res.code == 200) {
            diagnosisList1.value = res.data.records
        }
    })
}

// 新增按钮
function handlerCreat(formEl) {
    drugList.value = ''
    drugList2.value = ''
    dialogFormVisible.value = true
    state.boxFlag = false
    formEl.resetFields()
}
// 新增诊疗范围配置列表
const creat = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var params = {
                treatmentScopeSet: {
                    id: treatmentScopeSetid.value
                },
                treatmentName: form.diagnosis,
                prescriptionTypeIds: pretypeId.value,
                prescriptionTypeValues: pretypeValue.value,
                dosageFormIds: managetypeId.value,
                dosageFormValues: managetypeValue.value,
                massRangeSetIds: massRangeSetIds.value,
                massRangeSetNames: commodityId.value
            }
            diagnosisList.save(params).then(res => {

                if (res.code == 200) {
                    diagnosisList1.value.push(res.data)
                    ElMessage({
                        message: "保存成功",
                        type: "success",
                    });
                    dialogFormVisible.value = false
                    getList()
                } else {
                    proxy.msgError(res.msg)
                    // dialogFormVisible.value = false
                }
            })
        }
    });
};


// 编辑按钮
function handleEdit(row) {
    drugList.value = ''
    drugList2.value = ''
    dialogFormVisible.value = true
    state.boxFlag = true
    ids.value = row.id
    editForm.value = { ...row };
    editForm.treatmentName = row.treatmentName;
    editForm.prescriptionTypeIds = row.prescriptionTypeIds.split(",");
    editForm.dosageFormIds = row.dosageFormIds.split(",");
    editForm.massRangeSetNames = row.massRangeSetNames.split(",");
}
//编辑请求
const edit = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {

            //诊疗范围参数
            let depth1 = (node, nodeList = []) => {
                node.forEach((item) => {
                    if (item.children) {
                        depth1(item.children, nodeList)
                    } else {
                        nodeList.push(item)
                    }
                })
                return nodeList
            }
            var arr = depth1(diaSelectList._rawValue)
            let arrlist = () => {
                arr.forEach(
                    (item) => {
                        item.treatmentName
                        if (editForm.treatmentName == item.treatmentName) {
                            editTreatmentScopeSetid.value = item.id
                        }
                    },

                );
            }
            arrlist()
            // 处方类型参数
            let depth2 = () => {
                var newarr = []
                diaSelectList2.value.forEach(
                    (item) => {
                        editForm.prescriptionTypeIds.forEach(items => {
                            if (items == item.id) {
                                newarr.push(item)
                            }
                        })
                    }
                );
                return newarr
            }
            var typelist = depth2()
            var str = "";
            for (var i = 0; i < typelist.length; i++) {
                str += typelist[i].valueName + ",";
            }
            //去掉最后一个逗号(如果不需要去掉，就不用写)
            if (str.length > 0) {
                str = str.substr(0, str.length - 1);
                editTypevalue.value = str
            }
            //药品剂型参数
            let depth3 = () => {
                var newarr = []
                diaSelectList3.value.forEach(
                    (item) => {
                        editForm.dosageFormIds.forEach(items => {
                            if (items == item.id) {
                                newarr.push(item)
                            }
                        })
                    }
                );
                return newarr
            }
            var typelist2 = depth3()
            var str = "";
            for (var i = 0; i < typelist2.length; i++) {
                str += typelist2[i].valueName + ",";
            }
            //去掉最后一个逗号(如果不需要去掉，就不用写)
            if (str.length > 0) {
                str = str.substr(0, str.length - 1);
                // console.log(str);
                editDosagevalue.value = str
            }
            // 经营范围参数
            let depth4 = (node, nodeList = []) => {
                node.forEach((item) => {
                    if (item.children) {
                        nodeList.push(item)
                        depth4(item.children, nodeList)
                    } else {
                        nodeList.push(item)
                    }
                })
                return nodeList
            }
            // console.log(treedata._rawValue);
            var arr2 = depth4(treedata._rawValue)
            // console.log( 'name',editForm.massRangeSetNames);
            // console.log('arr2',arr2);
            // let newarr = []
            // diaSelectList3.value.forEach(
            //     (item) => {
            //         newValue.forEach(items => {
            //             if (items == item.id) {
            //                 newarr.push(item)
            //             }
            //         })
            //     }
            // );
            let arrlist4 = () => {
                var editarr = []
                arr2.forEach(
                    (item) => {
                        editForm.massRangeSetNames.forEach(items => {
                            if (items == item.massName) {
                                editarr.push(item.id)
                            }
                        })

                    },
                );
                editMassRangeid.value = editarr.join(",")
            }
            arrlist4()
            // console.log(editMassRangeid.value);
            var params = {
                id: ids.value,
                treatmentScopeSet: {
                    id: editTreatmentScopeSetid.value
                },
                treatmentName: editForm.treatmentName,
                prescriptionTypeIds: editForm.prescriptionTypeIds.join(","),
                prescriptionTypeValues: editTypevalue.value,
                dosageFormIds: editForm.dosageFormIds.join(","),
                dosageFormValues: editDosagevalue.value,
                massRangeSetIds: editMassRangeid.value,
                massRangeSetNames: editForm.massRangeSetNames.join(",")
            }
            diagnosisList.save(params).then(res => {
                if (res.code == 200) {
                    // diagnosisList1.value.push(res.data)
                    ElMessage({
                        message: "修改成功",
                        type: "success",
                    });
                    dialogFormVisible.value = false
                    getList()
                }
            })
        }
    });
};

//删除
function handleDelete(row) {
    proxy.$confirm('是否确认删除此诊疗范围配置?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        diagnosisList.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
// 重置
function resetQuery(formEl) {
    queryParams.diagnosis = ''
    getList()
}
//诊疗范围配置列表
function getList() {
    diagnosisList.list({
        size: queryParams.pageSize,
        current: queryParams.pageNum,
    }).then(res => {
        if (res.code == 200) {
            diagnosisList1.value = res.data.records
            queryParams.total = res.data.total
        }
    })
}
getList()
//获取诊疗范围
const getData = () => {
    diagnosisList
        .treeData({})
        .then((res) => {
            if (res.code == 200) {
                diaSelectList.value = res.data
            }
        });
};
getData()
//获取处方类型
function getList2() {
    drugList2.value = ''
    erpBaseCommonValuesService.list({
        "item.id": 5,
        current: 1,
        size: 100000,
    }).then(res => {
        if (res.code == 200) {
            diaSelectList2.value = res.data.records
        }
    })
}
getList2()
//获取药品剂型
function getList3() {
    drugList.value = ''
    erpBaseCommonValuesService.list({
        "item.id": 6,
        current: 1,
        size: 100000,
    }).then(res => {
        if (res.code == 200) {
            diaSelectList3.value = res.data.records
        }
    })
}

getList3()
// 获取质量树表
const getData2 = () => {
    massRange
        .treeData()
        .then((res) => {
            if (res.code == 200) {
                treedata.value = res.data
            }
        });
};
getData2()
function handleSelectionChange() { }
/**
 * 仓库
 */
// const store = useStore();
/**
 * 路由对象
 */
const route = useRoute();
/**
 * 路由实例
 */
const router = useRouter();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const data = reactive({})
onBeforeMount(() => {
    //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
    //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
    ...toRefs(data)
})

</script>
<style scoped lang='scss'>
.el-table {
    margin-top: 20px;
}

.treeselect {
    width: 100%;
    // height:500px;
}

.form-input {
    height: 300px;
}

.scopeClass {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 232px;
    border: 1px solid #ddd;
    display: inline-block;
    width: 100%;
    border-radius: 0 0 6px 6px;
}

.commonTopBox {
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
    background-color: #fff;
    margin: 20px;
    -webkit-box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
    border-radius: 7px;
}

.el-select {
    width: 100%;
}

.footer {
    margin: 0px 0px 10px 370px;
}

.top {
    margin: 20px;
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
    background-color: #fff;
    -webkit-box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
    border-radius: 7px;
}

.topSpan {
    width: 45px;
    height: 23px;
    font-size: 22px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    display: block;
    line-height: 30px;
    margin-bottom: 20px;
}

::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.searchSpan {
    width: 138px;
    height: 23px;
    font-size: 22px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    // display: block;
    line-height: 30px;
    // margin-right:100px;
}
</style>
