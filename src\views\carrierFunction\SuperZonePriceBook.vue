<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryParams" :inline="true" :model="queryParams" class="seache-form" @submit.native.prevent>
                <el-form-item label="价格本名称" prop="name" style="width: 250px">
                    <el-input v-model="queryParams.name" clearable placeholder="请输入价格本名称" @change="handleQuery" @clear="handleQuery" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="价格本类型" prop="type" style="width: 250px">
                    <el-select v-model="queryParams.type" clearable placeholder="请选择价格本类型" @change="handleQuery">
                        <el-option v-for="item in priceBookTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="发布状态" prop="publishStatus">
                    <el-select v-model="queryParams.publishStatus" clearable placeholder="请选择发布状态" @change="handleQuery">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="激活状态" prop="status">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择激活状态" @change="handleQuery">
                        <el-option label="启用" value="1"></el-option>
                        <el-option label="禁用" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery('queryParams')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button type="primary" @click="newPriceVisible = true">添加价格本</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="superZonePriceBookTable" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="dataList" :showIndex="true" element-loading-text="加载中...">
                <template #status="{ row }">
                    <el-switch
                        v-if="row.publishStatus !== '0' && row.publishStatus !== '1' && row.publishStatus !== '4'"
                        v-model="row.status"
                        :before-change="() => handleStatusChange(row)"
                        active-color="#13ce66"
                        active-text="启用"
                        active-value="1"
                        inactive-text="禁用"
                        inactive-value="0"
                        inline-prompt
                        size="small"
                    ></el-switch>
                    <span v-else></span>
                </template>
                <template #createBy="{ row }">
                    <span>{{ row.createBy.name }}</span>
                </template>
                <template #type="{ row }">
                    <span>{{ formatDictionaryData('priceBookTypeList', row.type) }}</span>
                </template>
                <template #publishStatus="{ row }">
                    <span>{{ formatDictionaryData('statusList', row.publishStatus) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="row.publishStatus === '2'" icon="el-icon-check" link size="small" type="success" @click="openPostNotification(row, '3')">发布</el-button>
                    <el-button v-if="row.publishStatus === '3'" icon="el-icon-close" link size="small" type="danger" @click="openPostNotification(row, '4')">注销</el-button>
                    <el-button v-if="row.publishStatus !== '4'" icon="el-icon-plus" link size="small" type="success" @click="openAddPrice(row)">添加价格</el-button>
                    <el-button v-if="row.publishStatus === '0' || row.publishStatus === '1' || row.publishStatus === '2'" icon="el-icon-delete" link size="small" type="danger" @click="openPostNotification(row, 'delete')">删除</el-button>
                    <el-button v-if="row.publishStatus !== '0' && row.publishStatus !== '1'" icon="el-icon-info-filled" link size="small" type="primary" @click="openToViewThePriceBook(row)">查看价格本</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
        </el-card>

        <!--  /新增价格本 抽屉   -->
        <el-drawer v-if="newPriceVisible" v-model="newPriceVisible" size="35vw" title="新建价格本" @close="hideNewPrice">
            <div v-loading="newPriceLoading" :element-loading-text="newPriceLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="newPriceForm" :model="newPriceForm" :rules="newPriceFormRules" class="newPriceForm" label-width="auto">
                        <el-form-item label="价格本类型" prop="type">
                            <el-select v-model="newPriceForm.type" clearable placeholder="请选择价格本类型" style="width: 100%" @change="setToResetThePriceOfThisName">
                                <el-option v-for="item in priceBookTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="newPriceForm.type == '2'" label="客户名称" prop="companyId">
                            <el-select v-model="newPriceForm.companyId" clearable filterable placeholder="请选择客户" style="width: 100%">
                                <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="newPriceForm.type == '4'" label="承运商名称" prop="companyId">
                            <el-select v-model="newPriceForm.companyId" clearable filterable placeholder="请选择承运商" style="width: 100%">
                                <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="价格本名称" prop="name">
                            <el-input v-model="newPriceForm.name" :formatter="formatThePriceBookName" clearable placeholder="请输入价格本名称" style="width: 100%"></el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="newPriceForm.remark" clearable placeholder="请输入备注" style="width: 100%" type="textarea"></el-input>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideNewPrice">取消</el-button>
                    <el-button type="primary" @click="submitTheNewPriceBook">确定</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  添加价格明细 抽屉  -->
        <el-drawer v-if="addPriceDetailsVisible" v-model="addPriceDetailsVisible" :title="addPriceDetailsTitle" size="75vw" @close="hideAddPriceDetails">
            <div v-loading="addPriceDetailsLoading" :element-loading-text="addPriceDetailsLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="addPriceDetailsForm" :model="addPriceDetailsForm" :rules="addPriceDetailsFormRules" label-width="auto">
                        <el-form-item label="价格本名称" prop="name">
                            <span style="font-weight: bold">{{ addPriceDetailsForm.name }}</span>
                        </el-form-item>
                        <div v-for="(item, index) in addPriceDetailsForm.superAreaInformationList" :key="index" class="addPriceDetailsForm__line">
                            <el-form-item :prop="`superAreaInformationList.${index}.area`" :rules="{ required: true, message: '请选择地区', trigger: 'change' }" label="省市区/县" style="width: 100%">
                                <el-cascader
                                    v-model="item.area"
                                    :options="sysAreas"
                                    :props="props"
                                    class="w-full"
                                    clearable
                                    collapse-tags
                                    collapse-tags-tooltip
                                    max-collapse-tags="3"
                                    placeholder="请选择地区"
                                    @change="
                                        (e) => {
                                            verifyIfTheRegionIsDuplicated(e, index);
                                        }
                                    "
                                    @visible-change="visibleChange"
                                />
                            </el-form-item>
                            <el-form-item :prop="`superAreaInformationList.${index}.superAreaMoney`" :rules="{ required: true, message: '请输入超区金额', trigger: 'blur' }" label="超区金额">
                                <el-input-number v-model="item.superAreaMoney" :max="99999999.99" :min="0" :precision="2" class="number__unit__element" clearable controls-position="right" placeholder="请输入" style="width: 200px"></el-input-number>
                            </el-form-item>
                            <el-form-item :prop="`superAreaInformationList.${index}.superAreaDistance`" :rules="{ required: true, message: '请输入超区距离', trigger: 'blur' }" label="超区距离">
                                <el-input-number v-model="item.superAreaDistance" :max="999999.99" :min="0" :precision="2" class="number__unit__kilometre" clearable controls-position="right" placeholder="请输入" style="width: 200px"></el-input-number>
                            </el-form-item>
                            <el-button v-if="index > 0" link style="padding-bottom: 20px; margin-left: 5px" type="danger" @click="addPriceDetailsForm.superAreaInformationList.splice(index, 1)">移除</el-button>
                            <span v-else style="width: 81px"></span>
                        </div>
                        <el-button icon="el-icon-plus" type="primary" @click="continueToAddPriceDetails">继续添加</el-button>
                    </el-form>
                </el-card>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideAddPriceDetails">取消</el-button>
                    <el-button type="primary" @click="submitAddPrice">一键生成</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  /查看价格本 抽屉  -->
        <el-drawer v-if="viewPriceBookVisible" v-model="viewPriceBookVisible" :title="viewPriceBookTitle" size="70vw" @close="hideViewPriceBook">
            <div v-loading="viewPriceBookLoading" :element-loading-text="viewPriceBookLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card v-show="isShowPriceBookForm" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="viewPriceBookForm" :inline="true" :model="viewPriceBookForm" class="seache-form" label-width="auto">
                        <el-form-item label="省市区/县" prop="area" style="width: 400px">
                            <el-cascader v-model="viewPriceBookForm.area" :options="sysAreas" :props="{ checkStrictly: true }" clearable placeholder="请选择地区" style="width: 100%" @change="handleQueryPriceBook" @visible-change="visibleChange" />
                        </el-form-item>
                        <search-button :is-show-all-switch="false" @handleQuery="handleQueryPriceBook" @resetQuery="resetQuery('viewPriceBookForm')"></search-button>
                    </el-form>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <div style="margin-bottom: 10px">
                        <el-button v-if="priceBookList.length > 0" type="primary" @click="submitPriceBook">提交</el-button>
                        <el-button type="success" @click="handleClickImportExpenses">导入费用</el-button>
                        <right-toolbar v-model:columns="columnsPriceBook" v-model:show-search="isShowPriceBookForm" table-i-d="priceBookTable" @queryTable="getPriceBookList" />
                    </div>
                    <column-table :columns="columnsPriceBook" :data="priceBookList" :showIndex="true">
                        <template #distance="{ row }">
                            <el-input-number
                                v-if="row._editing && row._editKey === 'distance'"
                                v-model="row.distance"
                                :max="999999.99"
                                :min="0"
                                :precision="2"
                                class="number__unit__kilometre"
                                clearable
                                controls-position="right"
                                placeholder="请输入"
                                style="width: 200px"
                            ></el-input-number>
                            <div v-else @dblclick="editCell(row, 'distance')">{{ row.distance }}</div>
                        </template>
                        <template #cost="{ row }">
                            <el-input-number v-if="row._editing && row._editKey === 'cost'" v-model="row.cost" :max="99999999.99" :min="0" :precision="2" class="number__unit__element" clearable controls-position="right" placeholder="请输入" style="width: 200px"></el-input-number>
                            <div v-else @dblclick="editCell(row, 'cost')">{{ row.cost }}</div>
                        </template>
                        <template #opt="{ row }">
                            <el-button icon="el-icon-delete" link size="small" type="danger" @click="onClickDelete(row)">删除</el-button>
                        </template>
                    </column-table>
                    <pagination v-show="priceBookTotal > 0" v-model:limit="viewPriceBookForm.size" v-model:page="viewPriceBookForm.current" :total="priceBookTotal" class="mb0" @pagination="getPriceBookList" />
                </el-card>
            </div>
        </el-drawer>

        <!--  / 批量修改计算公式对话框  -->
        <el-dialog v-model="batchModifyFormulaOfVisible" append-to-body class="dialog__batchModifyFormulaOf" title="价格本导入设置" width="500px" @close="handleClickHidesBatchModifyFormulaOf">
            <el-form>
                <el-form-item label="模板下载">
                    <el-button icon="el-icon-download" type="primary" @click="handleDownloadTemplate">费用导入模板【Excel】</el-button>
                </el-form-item>
                <el-alert :closable="false" class="mb10" description="请先点击“费用导入模板【Excel】”链接，下载模板，在生成的模板Excel文件中录入费用数据，然后选择该模板文件导入。" show-icon type="info"></el-alert>
                <el-form-item label="选择导入文件" style="margin-bottom: 0">
                    <el-upload
                        ref="importFile"
                        v-model:file-list="fileList"
                        :action="uploadFileUrl"
                        :auto-upload="false"
                        :before-upload="beforeAvatarUpload"
                        :class="{ 'avatar__uploader__vertical__display': hideUploadTransportPermit, '': !hideUploadTransportPermit }"
                        :data="{
                            id: viewPriceBookForm.exceedCountyId
                        }"
                        :headers="headers"
                        :limit="1"
                        :on-error="fileUploadError"
                        :on-progress="fileUploadProgress"
                        :on-remove="handleRemoveFile"
                        :on-success="fileUploadSuccess"
                        :show-file-list="true"
                    >
                        <el-button type="success">选择文件</el-button>
                    </el-upload>
                </el-form-item>
                <div v-if="importTemplateInformation.fail" class="box-period">
                    <el-scrollbar>
                        <el-alert v-for="(item, index) in importTemplateInformation.fail" :key="index" :closable="false" :description="'第' + item.rowNum + '行' + item.reason" class="mt10" show-icon type="error"></el-alert>
                    </el-scrollbar>
                </div>
            </el-form>
            <div class="dialog-footer" style="display: flex; justify-content: end; margin-top: 8px">
                <el-button @click="handleClickHidesBatchModifyFormulaOf">取消</el-button>
                <el-button :disabled="importTemplateInformation.fail || fileList.length === 0" type="primary" @click="$refs.importFile.submit()">导入数据</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import superZonePriceBook from '@/api/carrierEnd/superZonePriceBook';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import { selectDictLabel } from '@/utils/dictLabel';
import { downloadNoData } from '@/utils';
import tool from '@/utils/tool';

export default {
    name: 'SuperZonePriceBook',
    components: {
        SearchButton,
        RightToolbar,
        ColumnTable
    },
    data() {
        return {
            showSearch: true,
            queryParams: {
                current: 1,
                size: 10,
                name: undefined,
                type: undefined,
                publishStatus: undefined,
                status: undefined
            },
            columns: [
                { title: '价格本名称', key: 'name', align: 'center', minWidth: '260px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '客户名称', key: 'companyName', width: '200px', align: 'center', columnShow: true },
                { title: '价格本类型', key: 'type', width: '120px', align: 'center', columnShow: true },
                { title: '创建人', key: 'createBy', width: '240px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', width: '170px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '版本', key: 'version', align: 'center', columnShow: true },
                { title: '发布时间', key: 'publishTime', width: '170px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '注销时间', key: 'logoutTime', width: '170px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '备注', key: 'remark', minWidth: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发布状态', key: 'publishStatus', width: '120px', align: 'center', columnShow: true, fixed: 'right' },
                { title: '激活状态', key: 'status', align: 'center', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '280px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            loading: false,
            total: 0,
            isShowAll: false,
            priceBookTypeList: [],
            statusList: [],
            dataList: [],
            newPriceVisible: false,
            newPriceLoading: false,
            newPriceLoadingText: '新建中...',
            newPriceForm: {
                name: '通用价格本',
                type: undefined,
                companyId: undefined,
                remark: ''
            },
            newPriceFormRules: {
                name: [{ required: true, message: '请输入价格本名称', trigger: 'blur' }],
                type: [{ required: true, message: '请选择价格本类型', trigger: 'change' }],
                companyId: [{ required: true, message: '请选择客户名称', trigger: 'blur' }]
            },
            customerList: [],
            addPriceDetailsVisible: false,
            addPriceDetailsTitle: '添加价格明细',
            addPriceDetailsLoading: false,
            addPriceDetailsLoadingText: '添加中...',
            addPriceDetailsForm: {
                name: undefined,
                superAreaInformationList: [
                    {
                        area: [],
                        superAreaMoney: undefined,
                        superAreaDistance: undefined
                    }
                ]
            },
            addPriceDetailsFormRules: {
                name: [{ required: true, message: '请输入价格本名称', trigger: 'blur' }]
            },
            sysAreas: [],
            viewPriceBookVisible: false,
            viewPriceBookTitle: '价格本详情',
            viewPriceBookForm: {
                current: 1,
                size: 10,
                area: []
            },
            viewPriceBookLoading: false,
            viewPriceBookLoadingText: '加载中...',
            isShowPriceBookForm: true,
            columnsPriceBook: [
                { title: '省市区/县', key: 'address', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '超区距离（公里）', key: 'distance', width: '230px', align: 'center', columnShow: true },
                { title: '超区金额(元)', key: 'cost', width: '230px', align: 'center', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '80px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            priceBookList: [],
            priceBookTotal: 0,
            batchModifyFormulaOfVisible: false,
            importTemplateInformation: {},
            fileList: [],
            uploadFileUrl: process.env.VUE_APP_API_GAENT + '/cost/exceedCounty/priceBook/data/import',
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            uploadLoading: null,
            props: {
                multiple: true
            }
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        formatThePriceBookName() {
            const { type, companyId } = this.newPriceForm;
            let typeName = '';
            let customerIdName = '';
            let companyIdName = '';
            let costTypeName = '超区费用';
            let name = '';

            if (type) {
                typeName = this.priceBookTypeList.find((item) => item.value === type)?.name;
                name = typeName;
            }
            if (type == '2' && companyId && this.customerList) {
                customerIdName = this.customerList.find((item) => item.companyId == companyId)?.companyName;
            }
            if (type == '4' && companyId && this.customerList) {
                companyIdName = this.customerList.find((item) => item.value == companyId)?.label;
            }
            if (type && type == '1') {
                // costTypeName 插入到 typeName 第二个字后面
                name = typeName.slice(0, 2) + costTypeName + typeName.slice(2);
            }
            if (type && companyId && type == '2') {
                name = customerIdName + costTypeName + typeName.slice(2);
            }
            if (type && type == '3') {
                name = typeName.slice(0, 3) + costTypeName + typeName.slice(3);
            }
            if (type && companyId && type == '4') {
                name = companyIdName + costTypeName + typeName.slice(3);
            }
            this.newPriceForm.name = name + '价格本';
        },
        hideUploadTransportPermit() {
            return this.fileList.length >= 1;
        }
    },
    created() {
        this.getDict();
        this.handleQuery();
        this.sysAreas = this.getSysAreas;
    },
    methods: {
        beforeAvatarUpload(file) {
            // 限制文件类型excel
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            if (!isExcel) {
                ElMessage.error('上传文件只能是 Excel 格式!');
            }
            return isExcel;
        },
        /**
         * 验证是否存在重复的地区
         * @returns {boolean}
         */
        checkAreaDuplicated() {
            let areaList = [];
            for (let item of this.addPriceDetailsForm.superAreaInformationList) {
                let areaStr = JSON.stringify(item.area);
                if (areaList.includes(areaStr)) {
                    return false;
                }
                areaList.push(areaStr);
            }
            return true;
        },
        /**
         * 继续添加价格明细
         */
        continueToAddPriceDetails() {
            // 存在 el-cascader 每次push会 非常卡顿 优化
            const { area, superAreaMoney, superAreaDistance } = this.addPriceDetailsForm.superAreaInformationList[this.addPriceDetailsForm.superAreaInformationList.length - 1];
            if (area.length && superAreaMoney && superAreaDistance) {
                this.addPriceDetailsForm.superAreaInformationList.push({
                    area: [],
                    superAreaMoney: undefined,
                    superAreaDistance: undefined
                });
            } else {
                ElMessage.warning('请先完善上一条数据');
            }
        },
        /**
         * 编辑单元格
         * @param row
         * @param key
         */
        editCell(row, key) {
            row._editing = true;
            row._editKey = key;
        },
        /**
         * 数据导入失败回调
         */
        fileUploadError() {
            // 提示错误信息
            ElMessage.error('数据导入失败！');
            this.uploadLoading.close();
        },
        /**
         * 导入数据回调
         */
        fileUploadProgress() {
            this.uploadLoading = ElLoading.service({
                lock: true,
                text: '数据导入中...',
                background: 'rgba(0, 0, 0, 0.7)'
            });
        },
        fileUploadSuccess(res) {
            this.uploadLoading.close();
            this.importTemplateInformation = res.data;
            const { fail, success } = res.data;
            if (fail.length === 0) {
                ElMessage.success('成功导入' + success + '条数据！');
                this.handleClickHidesBatchModifyFormulaOf();
                this.priceDataList('batch');
            }
        },
        /**
         * 获取客户和承运商列表
         */
        getAListOfCustomersAndCarriers() {
            this.newPriceLoading = true;
            this.newPriceLoadingText = '加载中...';
            this.customerList = [];
            enterpriseCooperation
                .cooperateSelect({ status: '1' })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.customerList = res.data;
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .finally(() => {
                    this.newPriceLoading = false;
                })
                .catch(() => {});
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.priceBookTypeList = await this.getDictList('cost_price_book_type');
            this.statusList = await this.getDictList('cost_price_book_release_status');
        },
        getList() {
            this.loading = true;
            superZonePriceBook
                .list(this.queryParams)
                .then((res) => {
                    if (res.code === 200) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .finally(() => {
                    this.loading = false;
                })
                .catch(() => {});
        },
        /**
         * 获取价格本列表
         */
        getPriceBookList() {
            this.viewPriceBookLoading = true;
            this.viewPriceBookLoadingText = '加载中...';
            // 从 this.viewPriceBookForm 中去除 area
            const { area, ...params } = this.viewPriceBookForm;
            superZonePriceBook
                .getPriceBookList(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.priceBookList = res.data.records || [];
                        this.priceBookTotal = res.data.total || 0;
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .finally(() => {
                    this.viewPriceBookLoading = false;
                })
                .catch(() => {});
        },
        /**
         * 隐藏价格本导入设置弹窗
         */
        handleClickHidesBatchModifyFormulaOf() {
            this.batchModifyFormulaOfVisible = false;
            this.$refs.batchModifyFormulaOfForm.resetFields();
            this.fileList = [];
            this.importTemplateInformation = {};
        },
        /**
         * 导入费用
         */
        handleClickImportExpenses() {
            // 打开 价格本导入设置 弹窗
            this.batchModifyFormulaOfVisible = true;
        },
        /**
         * 费用导入模板【Excel】下载
         */
        handleDownloadTemplate() {
            if (!this.viewPriceBookForm.exceedCountyId) {
                ElMessage.warning('请选择价格本！');
                return;
            }
            superZonePriceBook.downloadTemplate({ filename: '费用导入模板.xls', ...this.viewPriceBookForm }, '', '', 'blob').then((res) => {
                downloadNoData(res, 'application/vnd.ms-excel', '费用导入模板.xlsx');
            });
        },
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        /**
         * 重置 查询价格本
         */
        handleQueryPriceBook() {
            this.viewPriceBookForm.current = 1;
            let areaFields = ['provinceId', 'cityId', 'countyId', 'townId'];

            if (this.viewPriceBookForm.area !== null) {
                areaFields.forEach((field, index) => {
                    this.viewPriceBookForm[field] = this.viewPriceBookForm.area[index];
                });
            } else {
                areaFields.forEach((field, index) => {
                    this.viewPriceBookForm[field] = undefined;
                });
            }

            this.getPriceBookList();
        },
        handleRemoveFile(file, fileList) {
            this.fileList = fileList;
            this.importTemplateInformation = {};
        },
        handleStatusChange(row) {
            const { id, status } = row;
            if (id && status) {
                this.loading = true;
                superZonePriceBook
                    .updateStatus({ id, status: status === '0' ? '1' : '0' })
                    .then((res) => {
                        if (res.code === 200) {
                            ElMessage.success('操作成功！');
                        } else {
                            ElMessage.error(res.msg);
                        }
                    })
                    .catch(() => {
                        // 按钮状态还原
                        row.status = !status;
                    })
                    .finally(() => {
                        this.loading = false;
                        this.getList();
                    });
            }
        },
        /**
         * 隐藏 添加价格
         */
        hideAddPriceDetails() {
            this.addPriceDetailsVisible = false;
            this.$refs.addPriceDetailsForm.resetFields();
            this.addPriceDetailsForm.superAreaInformationList = [
                {
                    area: [],
                    superAreaMoney: undefined,
                    superAreaDistance: undefined
                }
            ];
        },
        /**
         * 隐藏新增价格本抽屉
         */
        hideNewPrice() {
            this.newPriceVisible = false;
            this.$refs.newPriceForm.resetFields();
        },
        /**
         * 隐藏 查看价格本
         */
        hideViewPriceBook() {
            this.viewPriceBookVisible = false;
        },
        /**
         * 点击删除明细
         * @param row
         */
        onClickDelete(row) {
            // 检查id是否存在
            if (!row.id) {
                ElMessage.error('明细ID不存在，无法删除！');
                return;
            }

            // 确认删除操作
            ElMessageBox.confirm('确定删除该条明细吗？', '删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    superZonePriceBook
                        .deleteDetail({ ids: row.id }) // 使用数组确保传递的是数组格式
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('删除成功！');
                                this.getPriceBookList(); // 假设这是刷新列表的方法
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {});
        },
        openAddPrice(row) {
            this.addPriceDetailsVisible = true;
            const { id, name } = row;
            this.addPriceDetailsTitle = `添加价格 - ${name}`;
            this.addPriceDetailsForm.name = name;
            this.addPriceDetailsForm.id = id;
        },
        /**
         * 打开 发布/注销/复制 价格本 对话框
         * @param row
         * @param type
         */
        openPostNotification(row, type) {
            const { id, name } = row;
            if (type === '3') {
                ElMessageBox.confirm(`您确定要发布<span style="color: red;">${name}</span>吗？`, '发布价格本', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                })
                    .then(() => {
                        superZonePriceBook.updatePublishStatus({ id, publishStatus: type }).then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('发布成功！');
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        });
                    })
                    .catch(() => {});
            } else if (type === '4') {
                ElMessageBox.confirm(`您确定要注销<span style="color: red;">${name}</span>吗？`, '注销价格本', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                })
                    .then(() => {
                        superZonePriceBook.updatePublishStatus({ id, publishStatus: type }).then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('注销成功！');
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        });
                    })
                    .catch(() => {});
            } else if (type === 'delete') {
                ElMessageBox.confirm(`您确定要删除<span style="color: red;">${name}</span>吗？`, '删除价格本', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                })
                    .then(() => {
                        superZonePriceBook
                            .delete({ ids: id })
                            .then((res) => {
                                if (res.code === 200) {
                                    ElMessage.success('删除成功！');
                                    this.getList();
                                } else {
                                    ElMessage.error(res.msg);
                                }
                            })
                            .catch(() => {});
                    })
                    .catch(() => {});
            }
        },
        /**
         * 查看价格本
         * @param row
         */
        openToViewThePriceBook(row) {
            this.viewPriceBookVisible = true;
            const { id, name } = row;
            this.viewPriceBookTitle = `查看价格本 - ${name}`;
            this.viewPriceBookForm.exceedCountyId = id;
            this.handleQueryPriceBook();
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            if (formName === 'queryParams') {
                this.handleQuery();
            }
            if (formName === 'viewPriceBookForm') {
                this.handleQueryPriceBook();
            }
        },
        /**
         * 重置价格本名称 清空customerId
         * @param val
         */
        setToResetThePriceOfThisName(val) {
            // this.newPriceForm.companyId 设置为 null 并更新视图
            this.newPriceForm.companyId = undefined;
            if (val === '2') {
                this.getAListOfCustomersAndCarriers();
            }
            // todo 承运商暂时不做
            // if (val === '4') {
            //   this.getAListOfCustomersAndCarriers('1');
            // }
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 一键生产价格
         */
        submitAddPrice() {
            this.$refs.addPriceDetailsForm.validate((valid) => {
                if (valid) {
                    if (!this.checkAreaDuplicated()) {
                        ElMessage.error('同一地区不能重复添加');
                        return;
                    }
                    let transformedData = this.addPriceDetailsForm.superAreaInformationList.map((item) => {
                        return {
                            exceedCountyId: this.addPriceDetailsForm.id,
                            areaList: item.area.map((item) => {
                                return {
                                    provinceId: item[0],
                                    cityId: item[1],
                                    countyId: item[2],
                                    townId: item[3]
                                };
                            }),
                            cost: item.superAreaMoney,
                            distance: item.superAreaDistance
                        };
                    });
                    this.addPriceDetailsLoading = true;
                    this.addPriceDetailsLoadingText = '提交中...';
                    superZonePriceBook
                        .addDetail(transformedData)
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('添加成功');
                                this.hideAddPriceDetails();
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.addPriceDetailsLoading = false;
                        })
                        .catch(() => {});
                }
            });
        },
        /**
         * 提交价格本
         */
        submitPriceBook() {
            // 判断是否 修改 没有修改 提示
            const isEdit = this.priceBookList.some((item) => {
                return item._editing;
            });
            if (!isEdit) {
                ElMessage.warning('请先修改价格本！');
                return;
            }
            // 将修改的数据 _editing true 提交 否则不提交
            const transformedData = this.priceBookList
                .map((item) => {
                    if (item._editing) {
                        return {
                            id: item.id,
                            cost: item.cost,
                            distance: item.distance
                        };
                    }
                })
                .filter((item) => item);
            superZonePriceBook
                .batchUpdatePriceBook(transformedData)
                .then((res) => {
                    if (res.code === 200) {
                        ElMessage.success('提交成功');
                        this.handleQueryPriceBook();
                    } else {
                        ElMessage.error(res.msg);
                    }
                })
                .catch(() => {});
        },
        /**
         * 货主价格本生成价格本
         */
        submitTheNewPriceBook() {
            this.$refs.newPriceForm.validate((valid) => {
                if (valid) {
                    this.newPriceLoading = true;
                    this.newPriceLoadingText = '提交中...';
                    superZonePriceBook
                        .add(this.newPriceForm)
                        .then((res) => {
                            if (res.code === 200) {
                                ElMessage.success('新建成功');
                                this.hideNewPrice();
                                this.getList();
                            } else {
                                ElMessage.error(res.msg);
                            }
                        })
                        .finally(() => {
                            this.newPriceLoading = false;
                        })
                        .catch(() => {});
                }
            });
        },
        /**
         * 验证所选地区是否重复
         */
        verifyIfTheRegionIsDuplicated(area, index) {
            const areaList = area.map((item) => {
                return {
                    provinceId: item[0],
                    cityId: item[1],
                    countyId: item[2],
                    townId: item[3]
                };
            });
            superZonePriceBook
                .checkRepeat({
                    exceedCountyId: this.addPriceDetailsForm.id,
                    areaList
                })
                .then((res) => {
                    if (res.code === 500) {
                        this.addPriceDetailsForm.superAreaInformationList[index].area = [];
                    }
                });
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
    .el-radio__input.is-disabled + span.el-radio__label {
        color: #666666;
    }

    .el-input.is-disabled .el-input__inner {
        color: #666666;
        -webkit-text-fill-color: #666666;
    }
    .el-dialog__header {
        padding-bottom: 20px;
    }
    .calculationFormula .el-dialog__body {
        padding: 0;
    }
    .el-alert .el-alert__description,
    .el-alert .el-alert__title {
        margin: 5px 0;
    }
    .el-alert__icon.is-big {
        font-size: 20px;
    }
    .custom__message__box .el-message-box__content {
        color: red; /* Change the text color to red */
    }
}
.addPriceDetailsForm__line {
    display: flex;
}
.number__unit__element {
    position: relative;
    ::v-deep .el-input__inner {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
.number__unit__kilometre {
    position: relative;
    ::v-deep .el-input__inner {
        text-align: left;
    }
    &::after {
        content: '公里';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
.dialog__batchModifyFormulaOf {
    ::v-deep {
        .el-dialog__header {
            padding-bottom: 20px;
        }

        .el-result {
            padding: 0;
        }
        .el-dialog__footer {
            padding: 10px 20px;
        }
    }
}
</style>
