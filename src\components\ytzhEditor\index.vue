<template>
  <div>
    <div style="border: 1px solid #ccc; margin-top: 10px">
      <Toolbar
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
        style="border-bottom: 1px solid #ccc"
      />
      <Editor
        :defaultConfig="editorConfig"
        :mode="mode"
        v-model="valueHtml"
        style="height: 500px; overflow-y: hidden"
        @onCreated="handleCreated"
        @onChange="handleChange"
        @onDestroyed="handleDestroyed"
        @onFocus="handleFocus"
        @onBlur="handleBlur"
        @customAlert="customAlert"
        @customPaste="customPaste"
      />
    </div>
    <div>
        <h1 style="margin-top:10px">实时预览</h1>
    </div>
     <div style="margin-top: 10px;width:100%;margin: 0 auto;" v-html="valueHtml">
    </div>
  </div>
</template>

<script>
import { getCurrentInstance } from "vue";
import "@wangeditor/editor/dist/css/style.css";
import { onBeforeUnmount, ref, shallowRef, onMounted } from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";

export default {
  components: { Editor, Toolbar },
  setup() {
    var viewHtmlVisible=ref(false)
    // 编辑器实例，必须用 shallowRef，重要！
    const editorRef = shallowRef();
    const appContext = getCurrentInstance().appContext;
    // 内容 HTML
    const valueHtml = ref("");

    // 模拟 ajax 异步获取内容
    onMounted(() => {
      //   setTimeout(() => {
      //     valueHtml.value = "<p>模拟 Ajax 异步设置内容</p>";
      //   }, 1500);
    });
    //总图片列表

    const ImageAllFileList = [];
    const VideoAllFileList = [];
    var InitImageAllFileList = [];
    var InitVideoAllFileList = [];
    const toolbarConfig = {};
    const editorConfig = { placeholder: "请输入内容...", MENU_CONF: {} };
    //图片上传////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    editorConfig.MENU_CONF["uploadImage"] = {
      server: process.env.VUE_APP_API_UPLOAD,
      // form-data fieldName ，默认值 'wangeditor-uploaded-image'
      fieldName: "file",
      // 单个文件的最大体积限制，默认为 2M
      maxFileSize: 1 * 1024 * 1024, // 1M
      // 最多可上传几个文件，默认为 100
      maxNumberOfFiles: 10,
      // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
      allowedFileTypes: ["image/*"],
      // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
      meta: {
        fileType: "image",
      },
      // 将 meta 拼接到 url 参数中，默认 false
      metaWithUrl: false,
      // 自定义增加 http  header
      headers: {
        Authorization:
          "Bearer " + appContext.config.globalProperties.$TOOL.cookie.get("TOKEN"),
        clientType:'pc',
      },
      // 上传之前触发
      onBeforeUpload(file) {
        return file;
      },
      // 自定义插入图片
      customInsert(res, insertFn) {
        insertFn(res.data.url, "", res.data.url);
      },
      // 单个文件上传失败
      onFailed(file, res) {
        appContext.config.globalProperties.$Response.errorNotice(
          res,
          `${file.name} 上传出错`
        );
      },
      // 上传错误，或者触发 timeout 超时
      onError(file, err, res) {
        //校验文件大小
        if (file.size / 1024 / 1024 > 1) {
          appContext.config.globalProperties.$message.error(
            `${file.name} 上传出错，文件大小不能超过1M，请压缩后上传`
          );
        } else {
          appContext.config.globalProperties.$message.error(`${file.name} 上传出错`);
        }
      },
      // 跨域是否传递 cookie ，默认为 false
      withCredentials: true,
      // 超时时间，默认为 10 秒
      timeout: 5 * 1000, // 5 秒
    };
    // 插入图片
    editorConfig.MENU_CONF["insertImage"] = {
      onInsertedImage(imageNode) {
        if (imageNode == null) return;
        ImageAllFileList.push(imageNode);
      },
    };


    editorConfig.MENU_CONF["uploadVideo"] = {
      server: process.env.VUE_APP_API_UPLOAD,
      // form-data fieldName ，默认值 'wangeditor-uploaded-video'
      fieldName: "file",

      // 单个文件的最大体积限制，默认为 10M
      maxFileSize: 50 * 1024 * 1024, // 5M

      // 最多可上传几个文件，默认为 5
      maxNumberOfFiles: 3,

      // 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
      allowedFileTypes: ["video/*"],

      // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
      meta: {
        fileType: "video",
      },

      // 将 meta 拼接到 url 参数中，默认 false
      metaWithUrl: false,

      // 自定义增加 http  header
      headers: {
        Authorization:
          "Bearer " + appContext.config.globalProperties.$TOOL.cookie.get("TOKEN"),
        clientType:'pc',
      },

      // 跨域是否传递 cookie ，默认为 false
      withCredentials: true,

      // 超时时间，默认为 30 秒
      timeout: 30 * 1000, // 15 秒
      customInsert(res, insertFn) {
        insertFn(res.data.url, "", res.data.url);
      },

    };
    //视频上传////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 自定义转换视频
    function customParseVideoSrc(src) {
      return `<iframe src="${src}" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true" style='margin: 0px auto;display: flex;'> </iframe>`;
    };
    // 插入视频
    editorConfig.MENU_CONF["insertVideo"] = {
      parseVideoSrc: customParseVideoSrc,
      onInsertedVideo(videoNode) {
        if (videoNode == null) return;
        VideoAllFileList.push(videoNode);
      },
    };
    // 组件销毁时，也及时销毁编辑器，重要！
    onBeforeUnmount(() => {
      const editor = editorRef.value;
      if (editor == null) return;

      editor.destroy();
    });

    // 编辑器回调函数
    const handleCreated = (editor) => {
      console.log("created", editor);
      editorRef.value = editor; // 记录 editor 实例，重要！
    };



    //编辑时触发
    const handleChange = (editor) => {
      console.log("change:", editor.getHtml());
    };
    const handleDestroyed = (editor) => {
      console.log("destroyed", editor);
    };
    const handleFocus = (editor) => {
      console.log("focus", editor);
    };
    const handleBlur = (editor) => {
      console.log("blur", editor);
    };
    const customAlert = (info, type) => {
      alert(`【自定义提示】${type} - ${info}`);
    };
    const customPaste = (editor, event, callback) => {
      console.log("ClipboardEvent 粘贴事件对象", event);

      // 自定义插入内容
      //editor.insertText("asdfasdf");

      // 返回值（注意，vue 事件的返回值，不能用 return）
      callback(true); // 返回 false ，阻止默认粘贴行为
      // callback(true) // 返回 true ，继续默认的粘贴行为
    };
    /*
     * 插入文本
     * @author: 路正宁
     * @date: 2023-04-19 09:42:59
     */
     const setHtml = (data) => {
      const editor = editorRef.value;
      if (editor == null) return;
      valueHtml.value = data;
      setTimeout(() => {
        InitImageAllFileList=editor.getElemsByType("image");
        InitVideoAllFileList=editor.getElemsByType("video");
      }, 100);
    };
    /*
     * 获取HTML文本
     * @author: 路正宁
     * @date: 2023-04-19 09:43:35
     */
    const getHtml = () => {
      const editor = editorRef.value;
      if (editor == null) return null;
      return editor.getHtml();
    };

    /*
     * 禁用编辑器
     * @author: 路正宁
     * @date: 2023-04-19 09:43:54
     */
    const disable = () => {
      const editor = editorRef.value;
      if (editor == null) return;
      editor.disable();
    };
    /*
     * 删除已删除的文件
     * @author: 路正宁
     * @date: 2023-04-19 10:05:23
     */
     const deletedImageFile = async () => {
      const editor = editorRef.value;
      if (editor == null) return;
      for (var i = 0; i < InitImageAllFileList.length; i++){
        ImageAllFileList.push(InitImageAllFileList[i]);
      }
      //当前图片列表
      var imgList = editor.getElemsByType("image");
      //遍历所有插入的图片列表
      for (var i = 0; i < ImageAllFileList.length; i++) {
        var isExist = false;
        //遍历编辑器中的图片列表
        for (var j = 0; j < imgList.length; j++) {
          //判断编辑器中是否存在该文件
          if (ImageAllFileList[i].src == imgList[j].src) {
            isExist = true;
            break;
          }
        }
        //如果编辑器中不存在该图片则删除
        if (isExist == false) {
          if (
            appContext.config.globalProperties.$ObjectUtils.isNotEmpty(
              ImageAllFileList[i].src
            )
          ) {
            var res = await appContext.config.globalProperties.$API.common.deleteFile.get(
              ImageAllFileList[i].src
            );
            if (res.code != 200) {
              appContext.config.globalProperties.$Response.errorNotice(
                res,
                "文件处理异常"
              );
            }
          }
        }
      }
    };
    const deletedVideoFile = async () => {
      const editor = editorRef.value;
      if (editor == null) return;
      for (var a = 0; a < InitVideoAllFileList.length; a++){
        VideoAllFileList.push(InitVideoAllFileList[a]);
      }
      //当前图片列表
      var imgList = editor.getElemsByType("video");
      //遍历所有插入的图片列表
      for (var i = 0; i < VideoAllFileList.length; i++) {
        var isExist = false;
        //遍历编辑器中的图片列表
        for (var j = 0; j < imgList.length; j++) {
          //判断编辑器中是否存在该文件
          if (VideoAllFileList[i].src == imgList[j].src) {
            isExist = true;
            break;
          }
        }
        //如果编辑器中不存在该图片则删除
        if (isExist == false) {
          if (
            appContext.config.globalProperties.$ObjectUtils.isNotEmpty(
                VideoAllFileList[i].src
            )
          ) {
            var res = await appContext.config.globalProperties.$API.common.deleteFile.get(
                VideoAllFileList[i].src
            );
            if (res.code != 200) {
              appContext.config.globalProperties.$Response.errorNotice(
                res,
                "文件处理异常"
              );
            }
          }
        }
      }
    };

    return {
      editorRef,
      mode: "default",
      valueHtml,
      toolbarConfig,
      editorConfig,
      handleCreated,
      handleChange,
      handleDestroyed,
      handleFocus,
      handleBlur,
      customAlert,
      customPaste,
      setHtml,
      getHtml,
      disable,
      deletedImageFile,
      deletedVideoFile,
      appContext,
      InitImageAllFileList:[],
      InitVideoAllFileList:[],
      ImageAllFileList,
      VideoAllFileList,
      viewHtmlVisible,
    };
  },
};
</script>
<style>

</style>
