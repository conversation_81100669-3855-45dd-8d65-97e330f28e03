import request from '@/utils/request';
export default {
    // 列表
    listLanTask: function (params) {
        return request.get('/tms/lanTask/list', params);
    },
    // 领取
    receiveTask: function (params) {
        return request.get('/tms/lanTask/grab', params);
    },
    // 派单
    updateLanTask: function (params) {
        return request.get('/tms/lanTask/dispatch', params);
    },
    // 获取司机列表
    getDriverList: function (params) {
        return request.get('/tms/driver/lanDriver/pcDriverSelect', params);
    },
    // 批量改派
	transferLanTask: function (params) {
        return request.post('/tms/lanTask/reassignment', params);
    },
	// 强制完结
	forceEndLanTask: function (params) {
        return request.post('/tms/lanTask/forceEnd', params);
    },
};
