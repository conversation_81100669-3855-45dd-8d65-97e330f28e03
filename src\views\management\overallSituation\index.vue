<!--  传感器设置 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" icon="el-icon-plus" size="mini" type="primary" @click="handleAdd">新增</el-button>
                    </el-col>
                </el-row>
            </div>
            <el-table v-loading="loading" :data="esOperationRecordList" border>
                <el-table-column :index="idxMethod" align="center" label="序号" type="index" width="55" />
                <el-table-column align="center" label="名称" prop="name" width="160" />
                <el-table-column align="center" label="配置标识" prop="attribute" width="200" />
                <el-table-column align="center" label="参数时长" prop="storage" width="200">
                    <template #default="scope">
                        {{ MMtoDayHourMin(scope.row.storage) }}
                    </template>
                </el-table-column>
                <el-table-column :formatter="(row) => formDict(globalSettingsList, row.status)" align="center" label="状态" prop="status" width="60" />
                <el-table-column align="center" label="说明" min-width="200" prop="remark" show-overflow-tooltip/>
                <el-table-column align="center" label="操作" width="80" show-overflow-tooltip fixed="right">
                    <template #default="scope">
                        <el-button icon="el-icon-edit" link size="small" type="warning" @click="modify(scope.row)">修改</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
        <!-- 修改 -->
        <el-drawer v-model="drawerOpen" :title="title">
            <div style="padding: 10px 20px">
                <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="form" :rules="rules" label-width="150px">
                    <div>
                        <el-form-item label="名称" prop="name">
                            <el-input v-model="form.name" placeholder="请输入名称" style="width: 350px" />
                        </el-form-item>
                        <el-form-item class="define" label="参数时长（分钟）" prop="storage">
                            <el-input-number v-model="form.storage" :min="0" style="width: 350px" @change="handleChange" />
                        </el-form-item>
                        <el-form-item label="配置标识" prop="attribute">
                            <el-input v-model="form.attribute" :disabled="disabled" placeholder="请输入配置标识" style="width: 350px" />
                        </el-form-item>
                        <el-form-item class="define" label="开启状态" prop="status">
                            <el-select v-model="form.status" clearable placeholder="请选择设备状态" style="width: 350px">
                                <el-option v-for="dict in globalSettingsList" :key="dict.id" :label="dict.name" :value="dict.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item class="define" label="说明" prop="remark">
                            <el-input v-model="form.remark" :rows="2" maxlength="200" placeholder="请输入说明" style="width: 350px" type="textarea" />
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <div class="drawer-footer">
				<el-button @click=" drawerOpen = false; form = {};">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-drawer>
    </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import overallSituationApi from '@/api/management/overallSituation';
const { proxy } = getCurrentInstance();
// import moment from 'moment';
const esOperationRecordList = ref([]);

const showSearch = ref(false);
const title = ref('');
const drawerOpen = ref(false);
const disabled = ref(false);
// 修改
const form = ref({
    key: true
});

// 表单验证
const rules = reactive({
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    attribute: [{ required: true, message: '请输入配置标识', trigger: 'blur' }],
    storage: [{ required: true, message: '请输入参数时长', trigger: 'blur' }],
    status: [{ required: true, message: '请开启状态', trigger: 'change' }]
});
//分钟数转天时分
function MMtoDayHourMin(min) {
    var day = parseInt(min / 1440);
    var hour = parseInt((min / 60) % 24);
    var minute = parseInt(min % 60);
    return day + '天' + hour + '时' + minute + '分';
}

function modify(row) {
    form.value = {
        deFlag: row.deFlag,
        id: row.id,
        name: row.name,
        remark: row.remark,
        storage: row.storage,
        status: row.status,
        attribute: row.attribute
    };
    showSearch.value = true;
    drawerOpen.value = true;
    disabled.value = true;
    title.value = '数据处理设置';
}
// 确定修改
function submitForm() {
    proxy.$refs['queryForm'].validate((valid) => {
        if (valid) {
            overallSituationApi.globalConfigSave(form.value).then((res) => {
                if (res.code == 200) {
                    showSearch.value = false;
                    drawerOpen.value = false;
                    getList();
                }
            });
        }
    });
}

// 新增
function handleAdd() {
    form.value = {
        name: '',
        remark: '',
        storage: '',
        attribute: '',
        status: 0
    };
    showSearch.value = true;
    drawerOpen.value = true;
    disabled.value = false;
    title.value = '新增数据处理设置';
}

// 数据处理设置参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
});

const operateList = ref([]);
// 全局设置状态
const globalSettingsList = ref([]);
// 字典请求
const getDict = async () => {
    operateList.value = await proxy.getDictList('operate_type');
    globalSettingsList.value = await proxy.getDictList('global_settings_status');
};
getDict();

//字典回显
const formDict = (data, val) => {
    return data && val ? proxy.selectDictLabel(data, val) : '--';
};
// 查询设备列表数据
function getList() {
    overallSituationApi.globalConfigList(queryParams.value).then((res) => {
        if (res.code == 200) {
            esOperationRecordList.value = res.data.records;
            queryParams.value.total = res.data.total;
        }
    });
}
getList();
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.drawer-footer {
    position: absolute;
    bottom: 20px;
    left: 250px;
}
</style>
