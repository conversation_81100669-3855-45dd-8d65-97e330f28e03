<template>
  <div>
    <el-card class="box-card" style="margin-bottom: 10px">
      <h4 class="el-dialog__title">选择出库单据</h4>
      <el-form ref="creatform" :inline="true" :model="searchForm" :rules="creatRules" label-width="90px"
               style="margin-top: 20px">
        <el-form-item label="供应商" prop="n1">
          <el-select v-model="searchForm.n1" clearable filterable placeholder="请选择供应商" style="width: 150px">
            <template #empty>
              <p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
                无数据
              </p>
              <p style="text-align: center">
                <el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
                  data.supplier.value =
                    '';
                  supplierList();
                }
                  ">
                  返回
                </el-button>
              </p>
            </template>
            <el-input v-model="data.supplier.value" placeholder="请输入供应商名称" @keydown.enter="supplierList"/>
            <el-option v-for="(item, index) in data.supplier.type" :key="index" :label="item.enterpriseName"
                       :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.n2" clearable placeholder="请输入商品名称" style="width: 150px"/>
        </el-form-item>
        <el-form-item label="经手人" prop="n3">
          <el-select v-model="searchForm.n3" clearable filterable placeholder="请输入经手人" style="width: 150px">
            <template #empty>
              <p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
                无数据
              </p>
              <p style="text-align: center">
                <el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
                  data.handle.value =
                    '';
                  handleList();
                }
                  ">
                  返回
                </el-button>
              </p>
            </template>
            <el-input v-model="data.handle.value" placeholder="请输入客户名称Enter键搜索" @keydown.enter="handleList"/>
            <el-option v-for="(item, index) in data.handle.type" :key="index" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="单据编号">
          <el-input v-model="searchForm.n4" clearable placeholder="请输入单据编号" style="width: 150px"/>
        </el-form-item>
        <el-form-item>
          <label id="el-id-2128-46" class="el-form-item__label" for="el-id-2128-55" style="width: 90px;">
            <el-button type="primary" @click="handleQuery(creatform)">查询</el-button>
          </label>
        </el-form-item>
      </el-form>
      <el-button :disabled="!multipleSelection.length" style="margin-bottom: 15px" type="primary" @click="addFn">
        添加
      </el-button>
      <el-table ref="multipleTableRef" v-loading="receiptsFlag" :border="true" :data="receiptsList"
                :header-cell-style="{ 'text-align': 'center' }" :row-style="({ row }) => {
          return row.redFlag ? {
            color: 'red'
          } : null
        }" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column :selectable="selectable" align="center" fixed="left" type="selection" width="55"/>
        <el-table-column align="center" label="单据编号" property="purchaseOrder" width="170px"/>
        <el-table-column :sort-method="dateSortMethod" align="center" label="单据创建日期" property="orderTime" sortable
                         width="130px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.orderTime) }}
          </template>
        </el-table-column>
        <el-table-column :sort-method="dateSortMethod" align="center" label="出库日期" property="outTime" sortable
                         width="100px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.outTime) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品名称" property="commodity.tradeName" width="130px"/>
        <el-table-column align="center" label="自编码" property="commodity.commoditySelfCode" width="100px"/>
        <el-table-column align="center" label="规格" property="commodity.packageSpecification" width="100px"/>
        <el-table-column align="center" label="生产厂家" property="commodity.manufactureName" width="150px"/>
        <el-table-column align="center" label="产地" property="commodity.producingArea" width="100px"/>
        <el-table-column align="center" label="供应商" property="suppier" width="100px"/>
        <el-table-column align="center" label="生产日期" property="produceDate" width="100px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="批号" property="batchNumber" width="150px"/>
        <el-table-column align="center" label="有效期" property="expirevalidate" width="100px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.expirevalidate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" property="basicUnit" width="100px"/>
        <el-table-column align="center" label="商品编号" property="commodity.commodityCode" width="100px"/>
        <el-table-column align="center" label="单价" property="unitPrice" width="100px">
          <template #default="scope">
            {{ Number(scope.row.unitPrice).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="出库数量" property="outQuantity" width="100px"/>
        <el-table-column align="center" fixed="right" label="可开票数量" property="canQuantity" width="100px"/>
        <el-table-column align="center" fixed="right" label="开票数量" property="" width="150px">
          <template #default="scope">
            <el-input-number v-model="scope.row.num" :max="scope.row.canQuantity" :min="0" :precision="0" :step="1"
                             size="small" @change="numChange(scope.$index)"/>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="开票金额" property="allPrice" width="100px">
          <template #default="scope">
            {{ Number(scope.row.allPrice).toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                     :disabled="false" :page-sizes="[50, 100, 200, 500]" :small="false" :total="data.total"
                     layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                     @size-change="handleQuery(creatform)"
                     @current-change="handleQuery(creatform)"/>
    </el-card>
    <el-card class="box-card" style="margin-bottom: 10px">
      <h4 class="el-dialog__title">待开票记录</h4>
      <el-button :disabled="multipleSelection2.length == 0" style="margin: 15px 0" type="primary" @click="delFn()">
        删除
      </el-button>
      <el-table ref="multipleTableRef2" :border="true" :data="chooseData"
                :header-cell-style="{ 'text-align': 'center' }"
                style="width: 100%" @selection-change="handleSelectionChange2">
        <el-table-column align="center" fixed="left" type="selection" width="55"/>
        <el-table-column align="center" label="单据编号" property="purchaseOrder" width="170px"/>
        <el-table-column :sort-method="dateSortMethod" align="center" label="单据创建日期" property="" sortable
                         width="130px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.orderTime) }}
          </template>
        </el-table-column>
        <el-table-column :sort-method="dateSortMethod" align="center" label="出库日期" property="outTime" sortable
                         width="100px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.outTime) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品名称" property="commodity.tradeName" width="130px"/>
        <el-table-column align="center" label="自编码" property="commodity.commoditySelfCode" width="100px"/>
        <el-table-column align="center" label="规格" property="commodity.packageSpecification" width="100px"/>
        <el-table-column align="center" label="生产厂家" property="commodity.manufactureName" width="150px"/>
        <el-table-column align="center" label="产地" property="commodity.packageSpecification" width="100px"/>
        <el-table-column align="center" label="供应商" property="suppier" width="100px"/>
        <el-table-column align="center" label="生产日期" property="produceDate" width="100px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="批号" property="batchNumber" width="150px"/>
        <el-table-column align="center" label="有效期" property="expirevalidate" width="100px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.expirevalidate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" property="basicUnit" width="100px"/>
        <el-table-column align="center" label="商品编号" property="commodity.commodityCode" width="100px"/>
        <el-table-column align="center" label="单价" property="unitPrice" width="100px">
          <template #default="scope">
            {{ scope.row.unitPrice?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="出库数量" property="outQuantity" width="100px"/>
        <el-table-column align="center" fixed="right" label="开票数量" property="num" width="150px"/>
        <el-table-column align="center" fixed="right" label="开票金额" property="allPrice" width="100px">
          <template #default="scope">
            {{ scope.row.allPrice?.toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>
      <p class="textStyle" style="margin: 15px 0 0px 0">合计开票金额: <span style="color:red">{{
          formData.n2.toFixed(2)
        }}元</span></p>
    </el-card>
    <el-card class="box-card" style="margin-bottom: 10px">
      <h4 class="el-dialog__title" style="margin-bottom: 10px">发票信息</h4>
      <el-form :disabled="!chooseData.length" :inline="true" label-width="70px" style="margin-top: 20px">
        <el-form-item label="发票号码">
          <el-select v-model="searchForm.n5" clearable filterable multiple placeholder="请选择发票号码"
                     style="width: 240px">
            <el-option v-for="item in invoiceType" :key="item.id" :label="item.invoiceNo" :value="item.invoiceNo"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <label id="el-id-2128-46" class="el-form-item__label" for="el-id-2128-55" style="width: 55px;">
            <el-button type="primary" @click="messAdd()">添加</el-button>
          </label>
        </el-form-item>
      </el-form>
      <el-table v-loading="invoiceFlag" :data="invoiceList" style="width: 100%">
        <el-table-column label="发票号码" property="invoiceNo"/>
        <el-table-column label="发票代码" property="invoiceCode"/>
        <el-table-column label=发票类型 property="type">
          <template #default="scope">
            {{ echo(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column label="发票供应商" property="invoiceSupplier"/>
        <el-table-column label="纳税人识别号" property="taxpayerNo"/>
        <el-table-column label="发票日期" property="invoicingDate">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.invoicingDate) }}
          </template>
        </el-table-column>
        <el-table-column label="发票金额" property="">
          <template #default="scope">
            {{ scope.row.invoicingAmount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="不含税金额" property="excludingTax">
          <template #default="scope">
            {{ Number(scope.row.excludingTax).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="税额" property="totalTax">
          <template #default="scope">
            {{ Number(scope.row.totalTax).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="可抵扣税额" property="effectiveTax">
          <template #default="scope">
            {{ Number(scope.row.effectiveTax).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="附件" property="">
          <template #default="scope">
            <p v-for="(item, index) in invoiceList[scope.$index].fileDtos" :key="index"
               style="color: rgb(34, 130, 255);cursor: pointer;" @click="checkImg(item)">{{
                item.name
              }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" property="">
          <template #default="scope">
            <el-button text type="primary" @click="delInvoice(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <p class="textStyle" style="margin: 15px 0 0px 0">合计发票金额: <span style="color:red">{{
          formData.n3.toFixed(2)
        }}元</span></p>
      <el-form label-width="50px" style="margin-top: 20px">
        <el-form-item label="备注">
          <el-input v-model="formData.n1" :rows="3" clearable placeholder="请输入备注" type="textarea"/>
        </el-form-item>
      </el-form>
    </el-card>
    <p style="color:red;margin: 10px 0 0 10px">点击“提交申请”将发起进项发票关联审批流程
    </p>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect, getCurrentInstance} from 'vue';
import {backApi, manageApi} from "@/api/model/salesManagement";
import {ElMessage, ElMessageBox} from "element-plus";
import {inputInvoice, relevanceInput} from "@/api/model/invoice";
import {functionIndex} from "../../commodity/functionIndex";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const {proxy} = getCurrentInstance();
const invoiceType = ref([])
const creatform = ref(); //验证表单/流程新增
const creatRules = reactive({
  n1: [{required: true, message: "请选择供应商", trigger: "blur"}],
  n3: [{required: true, message: "请选择经手人", trigger: "blur"}],
});
const invoiceFlag = ref(false);
const dialogVisible = ref(false)
const chooseData = ref([])
const invoiceList = ref([])
const searchForm = ref({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
})
const formData = ref({
  n1: "",
  n2: 0,
  n3: 0,
  n4: "",
  n5: ""
})
const close = () => {
  data.checkFlag = false
}
const checkImg = (row) => {
  data.imgUrl = []
  const fileName = row.name.split(".")
  data.imgUrl = []
  if (fileName[fileName.length - 1] == "pdf") {
    var s = window.location.toString();
    var s1 = s.substr(7, s.length);
    var s2 = s1.indexOf("/");
    s = s.substr(0, 8 + s2);
    window.open(s + row.url)
  } else {
    data.imgUrl.push(row.url)
    data.checkFlag = true
  }
}
const dateSortMethod = (a, b) => {
  const dateA = new Date(a.date);
  const dateB = new Date(b.date);
  if (dateA < dateB) {
    return -1;
  } else if (dateA > dateB) {
    return 1;
  } else {
    return 0;
  }
}
const messAdd = async () => {
  const message = searchForm.value.n5.join(',')
  let postArr = message.split(',' || '，')
  let arr1 = []
  let arr2 = []
  let arr3 = []
  let arr4 = []
  let res = null
  for (const item of postArr) {
    if (invoiceList.value.length > 0 && invoiceList.value.find(items => items.invoiceNo == item)) {
      arr2.push(item)
    } else {
      if (item) {
        invoiceFlag.value = true
        res = await relevanceInput.detailsInput({
          invoiceNo: item
        })
        invoiceFlag.value = false
        if (res.code == 200) {
          if (res.data) {
            if (res.data.erpPurchaseInvoiceReceiptDto.invoiceSupplierId == chooseData.value[0].supplierId) {
              invoiceList.value.push(res.data.erpPurchaseInvoiceReceiptDto)
              invoiceList.value[invoiceList.value.length - 1].fileDtos = []
              invoiceList.value[invoiceList.value.length - 1].newList = true
              res.data.fileDtos.forEach(item => {
                invoiceList.value[invoiceList.value.length - 1].fileDtos.push({
                  name: item.fileName,
                  url: item.fileUrl
                })
              })
              arr1.push(item)
            } else {
              arr4.push(item)
            }
          } else {
            arr3.push(item)
          }
        }
      }
    }
  }
  if (arr1.length > 0) {
    ElMessage.success(arr1.join('、') + ' 号码添加成功')
  }
  if (arr2.length > 0) {
    ElMessage.error(arr2.join('、') + ' 号码已经添加,不能重复添加')
  }
  if (arr3.length > 0) {
    ElMessage.warning(arr3.join('、') + ' 号码搜索失败')
  }
  if (arr4.length > 0) {
    ElMessage.warning(arr4.join('、') + ' 号码与待开票记录关联供应商不一致')
  }
  formData.value.n3 = 0
  invoiceList.value.forEach(item => {
    formData.value.n3 += item.invoicingAmount
  })
}
const data = reactive({
  clientType: {
    value: '',
    type: []
  },
  imgUrl: [],
  checkFlag: false,
  getEditInList: [],
  handle: {
    value: '',
    type: []
  },
  supplier: {
    value: '',
    type: []
  },
  pageNum: 1,
  pageSize: 50,
  total: 0
})
const multipleSelection = ref([])
const multipleTableRef = ref()
const multipleSelection2 = ref([])
const receiptsList = ref()
const receiptsFlag = ref(false)
const formFlag = ref(false)
const multipleTableRef2 = ref()
const emit = defineEmits([])
const props = defineProps({})
const tableData = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}
const handleSelectionChange2 = (val) => {
  multipleSelection2.value = val
}
const numChange = (ind) => {
  receiptsList.value[ind].allPrice = receiptsList.value[ind].num * receiptsList.value[ind].unitPrice
}
const delInvoice = (row) => {
  ElMessageBox.confirm("确认删除此项吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        console.log(row)
        if (row.oldId) {
          relevanceInput.delInvoice({
            ids: row.oldId
          }).then((res) => {
            if (res.code == 200) {
              invoiceList.value = invoiceList.value.filter(item => item.id != row.id)
              ElMessage.success('删除成功')
            } else {
              ElMessage.error(res.msg)
            }
          })
        } else {
          invoiceList.value = invoiceList.value.filter(item => item.id != row.id)
          ElMessage.success('删除成功')
        }
        formData.value.n3 = 0
        invoiceList.value.forEach(item => {
          formData.value.n3 += item.invoicingAmount
        })
      })
      .catch(() => {
      });
}
const selectable = (row) => {
  return row.canQuantity > 0
}
const addFn = () => {
  if (multipleSelection.value.length > 0) {
    let flag = false
    let flag2 = false
    let flag3 = false
    formData.value.n2 = 0
    multipleSelection.value.forEach(item => {
      if (!chooseData.value.find(items => items.id == item.id)) {
        if (!chooseData.value.find(items => items.suppier !== item.suppier || items.creator !== item.creator)) {
          if (item.allPrice > 0) {
            item.newList = true
            item.redFlag = false
            chooseData.value.push(JSON.parse(JSON.stringify(item)))
          } else {
            item.redFlag = true
            flag3 = true
          }
        } else {
          flag2 = true
          item.redFlag = true
        }
      } else {
        item.redFlag = true
        flag = true
      }
    })
    formData.value.n2 = 0
    chooseData.value.forEach(item => {
      item.checkFlag = false
      formData.value.n2 += item.allPrice
    })
    if (flag) {
      ElMessage.warning('添加成功，重复的入库记录已过滤')
    }
    if (flag2) {
      ElMessage.warning('添加成功，供应商或经手人不一致的入库记录已过滤')
    }
    if (flag3) {
      ElMessage.warning('添加成功，开票金额为 0 的已过滤')
    }
    if (!flag && !flag2 && !flag3) {
      ElMessage.success('添加成功')
    }
  } else {
    ElMessage.error('请先选择')
  }
}
const invoiceTypes = ref([])
const echo = (str) => {
  let find = invoiceTypes.value.find(item => item.value == str)?.name
  return find ? find : ""
}
const delFn = () => {
  ElMessageBox.confirm("确认删除此项吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        let ids = []
        chooseData.value.forEach(item => {
          console.log(item.newList, 1111)
          if (!item.newList) {
            ids.push(item.id)
          }
        })
        if (ids.length > 0) {
          relevanceInput.delGoods({
            ids: ids.join(','),
          }).then(res => {
            if (res.code == 200) {
              let newarr = chooseData.value.filter(
                  (item) =>
                      !multipleSelection2.value.some(
                          (subItem) => subItem.id === item.id
                      )
              );
              chooseData.value = newarr;
              formData.value.n2 = 0
              if (chooseData.value.length) {
                chooseData.value.forEach(item => {
                  formData.value.n2 += item.allPrice
                })
              }
              ElMessage.success('删除成功')
            } else {
              ElMessage.error(res.msg)
            }
          })
        } else {
          let newarr = chooseData.value.filter(
              (item) =>
                  !multipleSelection2.value.some(
                      (subItem) => subItem.id === item.id
                  )
          );
          chooseData.value = newarr;
          formData.value.n2 = 0
          if (chooseData.value.length) {
            chooseData.value.forEach(item => {
              formData.value.n2 += item.allPrice
            })
          }
          ElMessage.success('删除成功')
        }

      })
      .catch(() => {
      });
}
const clientList = () => {
  manageApi
      .clientType({
        current: 1,
        size: 1000,
        enterpriseName: data.clientType.value,
      })
      .then((res) => {
        data.clientType.type = res.data.records;
      });
};

const handleList = () => {
  backApi
      .handleList({
        size: 1000,
        name: data.handle.value
      })
      .then((res) => {
        if (res.code == 200) {
          data.handle.type = res.data.records;
        }
      });
};
const supplierList = () => {
  relevanceInput.supplierList({
    size: 1000,
    enterpriseName: data.supplier.value,
    customLabel: 2,
    // status: 3  审核通过用户
  }).then((res) => {
    if (res.code == 200) {
      data.supplier.type = res.data.records;
    }
  })
}
const handleQuery = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      receiptsFlag.value = true
      relevanceInput.retreat({
        supplierId: searchForm.value.n1,
        tradeName: searchForm.value.n2,
        creatorId: searchForm.value.n3,
        orderCode: searchForm.value.n4,
        size: data.pageSize,
        current: data.pageNum,
        searchType: 'purchaseInvoice'
      }).then(res => {
        if (res.code == 200) {
          receiptsList.value = res.data.records
          receiptsList.value.forEach(receipt => {
            receipt.num = receipt.canQuantity
            receipt.allPrice = receipt.num * receipt.unitPrice
          })
          if (chooseData.value.length > 0) {
            if (!(chooseData.value[0].supplierId == searchForm.value.n1 && chooseData.value[0].creatorId == searchForm.value.n3)) {
              chooseData.value = []
            }
          }
          data.total = res.data.total
          formData.value.n4 = searchForm.value.n1
          formData.value.n5 = searchForm.value.n3
        }
        receiptsFlag.value = false
      })
    }
  });
}
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  clientList()
  handleList()
  supplierList()
  inputInvoice.getList({size: 1000}).then(res => {
    invoiceType.value = res.data.records
  })
  let typeLo = JSON.parse(window.localStorage.getItem('In_Invioce'))
  if (typeLo) {
    invoiceTypes.value = typeLo
  } else {
    invoiceTypes.value = await proxy.getDictList("In_Invioce")
  }
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  chooseData,
  formData,
  invoiceList,
  searchForm,
  receiptsList
})

</script>
<style lang='scss' scoped>
.el-select-dropdown__list {
  .el-input {
    width: 90%;
    margin-left: 5%;
    margin-top: 5px;
    margin-bottom: 15px;
  }

  .el-pagination {
    margin-right: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.textStyle {
  font-size: 15px;
  color: #000;
  font-weight: bolder;
}

.messTable {
  width: 100%;
  background-color: #eaedf3;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 1px 1px 0 1px;

  tr {
    margin-bottom: 1px;
    display: flex;

    td {
      background-color: white;
      line-height: 40px;
    }

    td:nth-child(1) {
      flex: 1;
      padding: 0 10px;
      font-weight: bold;
      color: #505050;
      background: #f7f7f7;
    }

    td:nth-child(2) {
      color: #606266;
      padding: 0 10px;
      flex: 2
    }
  }
}
</style>
