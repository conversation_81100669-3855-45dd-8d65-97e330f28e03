<template>

	<el-main style="padding:0 20px;">
		<el-descriptions :column="1" border size="small">
			<el-descriptions-item label="日志名称">{{data.appName}}</el-descriptions-item>
			<el-descriptions-item label="操作人">{{data.operator}}</el-descriptions-item>
			<el-descriptions-item label="日志等级">{{data.logGrade}}</el-descriptions-item>
			<el-descriptions-item label="请求接口">{{data.url}}</el-descriptions-item>
			<el-descriptions-item label="请求方法">{{data.method}}</el-descriptions-item>
			<el-descriptions-item label="状态代码">{{data.httpCode}}</el-descriptions-item>
			<el-descriptions-item label="日志时间">{{data.createDate}}</el-descriptions-item>
			<el-descriptions-item label="客户机IP">{{data.clientIp}}</el-descriptions-item>
		</el-descriptions>
		<el-collapse v-model="activeNames" style="margin-top: 20px;">
			<el-collapse-item title="请求体" name="1">
				<div class="code">
					{{data.requestBody}}
				</div>
			</el-collapse-item>
			<el-collapse-item title="响应体" name="2">
				<div class="code">
					{{data.responseBody}}
				</div>
			</el-collapse-item>
			<el-collapse-item title="错误信息" name="3">
				<div class="code">
					{{data.pointMessage}}
					{{data.pointDetailed}}
				</div>
			</el-collapse-item>
		</el-collapse>
	</el-main>
</template>

<script>
	export default {
		data() {
			return {
				data: {},
				activeNames: ['1'],
				typeMap: {
					'info': "info",
					'warn': "warning",
					'error': "error"
				}
			}
		},
		methods: {
			setData(data){
				this.data = data
			}
		}
	}
</script>

<style scoped>
	.code {background: #848484;padding:15px;color: #fff;font-size: 12px;border-radius: 4px;}
</style>
