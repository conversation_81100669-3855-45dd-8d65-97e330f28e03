<template>
  <div>
    <!-- 截取区域 -->
    <div class="ScreenshotBox">
      <canvas id="canvas-cuts"></canvas>
      <canvas id="canvas-cut" @mousedown="onmousedown" @mousemove="onmousemove" @mouseup="onmouseup"></canvas>
      <div v-show="iconFlag" id="iconBtn">
        <el-icon @click="closeClick">
          <Close/>
        </el-icon>
        <el-icon @click="getImg">
          <Check/>
        </el-icon>
      </div>
    </div>
    <div v-show="false" class="canvas-img">
      <img id="cutImg" :src="cutImgSrc">
    </div>
  </div>
</template>

<script>
import {Check, Close} from '@element-plus/icons-vue'

export default {
  props: "outClick",
  components: {
    Close,
    Check
  },
  data() {
    return {
      images: false,
      // 图像层
      imgSrc: '', // 放置原图像
      ucanvas: null,
      uctx: null,
      img: null,
      // 遮罩层
      canvas: null,
      ctx: null,
      clipArea: null, // 最终坐标点数据
      start: null, // 绘制过程作为判断节点
      // 回显区域
      cutImg: null,
      cutCtx: null,
      cutImgSrc: null,
      // 操作区域
      iconFlag: false
    };
  },

  mounted() {
    //刷新数据列表
  },
  methods: {
    outClick(screenshotImage) {
      let that = this
      that.images = true
      setTimeout(() => {
        // 图像
        that.ucanvas = document.getElementById('canvas-cuts');
        that.ucanvas.width = '900';
        that.ucanvas.height = '600';
        that.uctx = that.ucanvas.getContext('2d');
        that.img = new Image();
        that.img.src = screenshotImage;
        that.img.crossOrigin = '' // 图像跨域处理 （ps:不跨域不用加）
        that.img.onload = function () {
          that.uctx.drawImage(that.img, 0, 0, that.ucanvas.width, that.ucanvas.height);
        };
        // 遮罩层
        that.canvas = document.getElementById('canvas-cut');
        that.canvas.width = '900';
        that.canvas.height = '600';
        that.ctx = that.canvas.getContext('2d');
        that.ctx.fillStyle = 'rgba(0,0,0,0.6)';
        that.ctx.strokeStyle = "rgb(40,119,253)";
        // 回显
        that.cutImg = document.getElementById("cutImg");
      }, 100)
    },
    // 按下
    onmousedown(e) {
      this.start = {
        x: e.offsetX,
        y: e.offsetY
      };
      this.iconFlag = false
    },
    getImg() {
      this.$emit('getImg', this.cutImgSrc);
    },
    // 抬起
    onmouseup(e) {
      let that = this
      if (that.start) {
        that.start = null;
        const element = document.getElementById('iconBtn');
        element.style.left = `${that.clipArea.x + that.clipArea.w - 100}px`
        element.style.top = `${that.clipArea.y + that.clipArea.h + 5}px`;
        this.iconFlag = true
        var url = that.startClip(that.clipArea);
        that.cutImg.src = url;
        //生成base64格式的图
        that.cutImgSrc = url;
      }
    },
    // 移动
    onmousemove(e) {
      if (this.start) {
        this.fill(this.start.x, this.start.y, e.offsetX - this.start.x, e.offsetY - this.start.y) // 调起遮罩层
      }
    },
    // 遮罩层
    fill(x, y, w, h) {
      let that = this
      that.ctx.clearRect(0, 0, that.canvas.width, that.canvas.height);
      that.ctx.beginPath();
      //遮罩层
      that.ctx.globalCompositeOperation = "source-over"; //在目标图像上显示源图像
      that.ctx.fillRect(0, 0, that.canvas.width, that.canvas.height);
      //画框
      that.ctx.globalCompositeOperation = 'destination-out'; //在目标图像之外显示源图像
      that.ctx.fillRect(x, y, w, h);
      //描边
      that.ctx.globalCompositeOperation = "source-over"; //在目标图像上显示源图像
      that.ctx.moveTo(x, y);
      that.ctx.lineTo(x + w, y);
      that.ctx.lineTo(x + w, y + h);
      that.ctx.lineTo(x, y + h);
      that.ctx.lineTo(x, y);
      that.ctx.stroke();
      that.ctx.closePath();
      // 存入最终坐标点数据
      that.clipArea = {
        x,
        y,
        w,
        h
      };
    },
    // 图像转化--截取后的图像
    startClip(area) {
      let that = this
      var canvas = document.createElement("canvas");
      canvas.width = area.w;
      canvas.height = area.h;
      var data = that.uctx.getImageData(area.x, area.y, area.w, area.h);
      var context = canvas.getContext("2d");
      context.putImageData(data, 0, 0);
      return canvas.toDataURL("image/png", 1);
    },
    // 关闭截取
    closeClick() {
      this.images = false
      // 数据重置
      this.imgSrc = '',
          this.ucanvas = null,
          this.uctx = null,
          this.img = null,
          this.canvas = null,
          this.ctx = null,
          this.clipArea = null,
          this.start = null,
          this.cutImg = null,
          this.cutCtx = null,
          this.cutImgSrc = null
      this.$emit('destroyComponent', false)
    }
  }
};
</script>

<style lang="scss" scoped>
.ScreenshotBox {
  width: 100%;
  height: 100%;
  position: relative;

  #canvas-cut {
    position: absolute;
    z-index: 909;
    left: 0
  }
}

#iconBtn {
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 998;
  border-radius: 3px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  justify-items: center;
  width: 100px;
  height: 35px;
  font-size: 24px;
}
</style>
