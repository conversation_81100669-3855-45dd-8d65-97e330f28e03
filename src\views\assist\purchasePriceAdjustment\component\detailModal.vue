<template>
    <div class="app-container" v-if="open">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog title="采购调价详情" v-model="open" width="85%" :before-close="beforClose">
            <div v-loading="loading">
                <h4 class="titleH4">
                    申请编号:<span style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;">
                        {{
                            detailData.applyNo ||
                            '--' }}</span>
                    申请日期: <span style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;">
                        {{
                            detailData.applyDate ?
                            moment(detailData.applyDate).format('YYYY-MM-DD') : '--' }}</span>
                    申请人:<span style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;">
                        {{
                            detailData.createBy &&
                            detailData.createBy.name || '--' }}</span>
                    审核状态: <span
                        style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;">{{
                            formDict(reviewStatus,
                                detailData.auditStatus) }}</span> </h4>
                <h3 class="el-dialog__title" style="margin-bottom: 10px">调整明细</h3>
                <el-table :data="list" border style="margin-top: 20px;" row-key="id">
                    <el-table-column type="expand">
					<template #default="props">
						<div style="display: flex;justify-content: end">
							<div m="4"
								 style="display: flex;width: 87%; justify-content:flex-end;align-items: center">
								<!-- <div style="color: red;font-size: 13px;line-height: 25px">
									<p>注意事项：</p>
									<p>1、调整数量总和不能大于所选出库（入库）记录当前的可调数量；</p>
									<p>2、调整数量不能录入小数；</p>
									<p>3、调整后单价不能大于所选出库（入库）记录当前的单价。</p>s
								</div> -->
								<el-table :border="true" :data="props.row.adjustPriceDTOs" size="small" style="width: 55%">
									<el-table-column :value-on-clear="0" align="center" label="调整数量" prop="adjustQuantity"></el-table-column>
									<el-table-column :value-on-clear="0" align="center" label="调整后单价" prop="adjustUnitPrice" :formatter="row=>row.adjustUnitPrice ? row.adjustUnitPrice.toFixed(2) : 0.00"></el-table-column>
                                    <el-table-column :value-on-clear="0" align="center" label="调价金额" >
                                        <template #default="scope">
                                                   <span>{{ (Number(scope.row.adjustQuantity ?scope.row.adjustQuantity : 0 )* Number(scope.row.adjustUnitPrice ? scope.row.adjustUnitPrice : 0 ) -Number(scope.row.adjustQuantity ? scope.row.adjustQuantity : 0)*Number(props.row.unitPrice ? props.row.unitPrice : 0)).toFixed(2) }}</span>
                                                </template>
                                    </el-table-column>
								</el-table>
							</div>
						</div>
					</template>
				</el-table-column>
                    <el-table-column label="供应商" prop="supplier.enterpriseName" :show-overflow-tooltip="true" align="center"
                        min-width="170" ><template #default="props">
                        <span>{{ form.supplier.enterpriseName || '--' }}</span>
                        </template>	</el-table-column>
                    <el-table-column label="单据编号" prop="purchaseInBound.purchaseOrderCode" :show-overflow-tooltip="true" align="center"
                        min-width="170" />
                    <el-table-column label="单据创建日期" prop="purchaseCreatedDate" :show-overflow-tooltip="true" align="center" min-width="120"
                        :formatter="row => row.purchaseCreatedDate ? moment(row.purchaseCreatedDate).format('YYYY-MM-DD') : '--'" />
                    <el-table-column label="入库日期" :show-overflow-tooltip="true" align="center" min-width="120"
                        :formatter="row => row.purchaseInBound.intoTime ? moment(row.purchaseInBound.intoTime).format('YYYY-MM-DD') : '--'" />
                    <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true" align="center"
                        min-width="120" />
                    <el-table-column label="批号" prop="purchaseInBound.intoNo" :show-overflow-tooltip="true" align="center"
                        min-width="120" />
                    <el-table-column label="仓库" prop="storages" :show-overflow-tooltip="true" align="center"
                        min-width="80" />
                    <el-table-column label="货位" prop="storages" :show-overflow-tooltip="true" align="center"
                        min-width="80" />
                    <el-table-column label="入库数量" prop="purchaseInBound.intoQuantity" :show-overflow-tooltip="true" align="center"
                        min-width="80" />
                    <el-table-column label="单价" prop="unitPrice" :show-overflow-tooltip="true" align="center"
                        min-width="80" />
                    <el-table-column label="金额" prop="money" :show-overflow-tooltip="true" align="center"
                        min-width="80" />
                </el-table>
                <h3 class="el-dialog__title" style="margin-bottom: 0px;margin-top: 10px">其他信息</h3>
                <el-descriptions class="margin-top" title=" " :column="2" border>
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                备注
                            </div>
                        </template>
                            {{ form.adjustExplain || '--' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                附件列表
                            </div>
                        </template>
                        <div class="demo-image__preview" >
                            <el-image class="imgView" :src="item.url"
                                 fit="cover" :preview-src-list="[item.url]"  v-for="item in form.files" :key="item"/>
                        </div>
                    </el-descriptions-item>
                    <!-- <el-descriptions-item>
                        <template #label>
                            <div class="cell-item">
                                调价总金额：
                            </div>
                        </template>
                            {{ form.adjustPrice ? form.adjustPrice.toFixed(2) : '0.00' }}
                    </el-descriptions-item> -->
                </el-descriptions>
            </div>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button @click="beforClose">取消</el-button>
                </div>
            </template>
        </el-dialog>
        <viewImg v-if="uploadVisibleFile" :visible="uploadVisibleFile" :src="uploadViewImgUrlFile"
            :beforeClose="() => uploadVisibleFile = false" />
    </div>
</template>

<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, watch, defineExpose, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from "element-plus";
import moment from 'moment'
import purchasePriceAdjustment from '@/api/erp/purchasePriceAdjustment'
import { Plus, UploadFilled, Search, ArrowDown } from '@element-plus/icons-vue'
import tool from '@/utils/tool';

const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const { proxy } = getCurrentInstance();
const loading = ref(false);
const uploadVisibleFile = ref(false)
const uploadViewImgUrlFile = ref(undefined)
const list = ref([])
const form = ref({})
// const cargoHandling = ref([])
// const paymentProcessing = ref([])
// const logisticsMethods = ref([])
// const thirdPartyIogistics = ref([])
// const reasonForReturn = ref([])
const reviewStatus = ref([])
const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    beforClose: {
        type: Function,
        default: () => { }
    },


    getList: {
        type: Function,
        default: () => { }
    },
    detailData: {
        type: Object,
        default: () => { { } }
    }

})
const { open, beforClose, getList, detailData } = toRefs(props)

const handlePictureCardPreview = (uploadFile) => {
    uploadViewImgUrlFile.value = uploadFile.url
    uploadVisibleFile.value = true
}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}

async function dict() {
    reviewStatus.value = await proxy.getDictList('erp_review_status')

}
const getDetail = () => {
    loading.value = true
    purchasePriceAdjustment.getIdOrder({ id: detailData.value.id }).then(res => {
        if (res.code == 200) {
            loading.value = false
            list.value = res.data?.adjustFormParams

            form.value = res.data?.adjustDTO
            form.value.files =form.value.files ? JSON.parse(form.value.files) : []
            list.value?.forEach((item,index)=>{
                list.value[index] = {...item?.adjustFormDTO,adjustPriceDTOs:item.adjustPriceDTOs}
            })
            console.log(list.value);
        } else {
            proxy.msgError(res.msg)
        }
    })
}
onMounted(async () => {
    await dict()
    getDetail()

})


</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.rowStyle {
    .el-col {
        margin-top: 20px;
        font-size: 15px;

        .rowTitle {
            width: 120px;
            text-align: right;
            display: inline-block;
            font-size: 15px;
            font-weight: bolder;
            color: #000;
        }

        .rowMess {
            color: #4d4d4d;
            font-weight: 600;
        }

        .rowRed {
            color: red;
        }
    }
}

.total {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    margin: 0px 20px;

    p {
        margin-right: 50px;
        margin-top: 20px;

        & span:nth-of-type(1) {
            font-size: 15px;
            font-weight: bold;
            color: #333;
            display: inline-block;
            width: 130px;
            text-align: right;
        }

        .red {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}

.box_date {
    width: 220px;
}

.step {
    margin-bottom: 30px;
}

.step1_search_btn {
    width: 60px;
    margin-left: 50px;
}

h3 {
    color: black;
}

.detailTable {
    width: 100%;
    background-color: #eaedf3;
    font-size: 14px;
    border-radius: 5px;

    tr {
        height: 40px;

        td {
            background-color: white;
        }

        td:nth-child(1) {
            padding: 0 10px;
            font-weight: bold;
            width: 20%;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            width: 80%;
            color: #606266;
            padding: 0 10px;
        }
    }

}

.messTable {
    width: 100%;
    background-color: #eaedf3;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    padding: 1px 1px 0 1px;

    tr {
        margin-bottom: 1px;
        display: flex;

        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2
        }
    }
}

.remark,
.file {
    tr {
        margin-bottom: 1px;
        display: flex;

        // border: 1px solid #eaedf3;
        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2;
            display: flex;
            align-items: center;
        }
    }
}

.remark,
.file {
    border-bottom: 1px solid #eaedf3;
    border-left: 1px solid #eaedf3;
    border-right: 1px solid #eaedf3;
}

.file {
    th:nth-child(2) {
        line-height: 25px;
        color: #2878ff !important;
        cursor: pointer;
        font-weight: 400;
    }
}

.titleH4 {
    margin-bottom: 20px;
    color: #000;
    font-weight: bolder;
    font-size: 15px;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type="number"] {
    -moz-appearance: textfield;
    /* 此处写不写都可以 */
}

::v-deep .el-upload-dragger {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
}

::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
    font-weight: bold;
    color: #505050;
    font-size: 13px;
    width: 120px;
}

::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-center {
    width: 25%;
}
.imgView {
    width:100px;
    height:100px;
    margin-right:20px
}
</style>
