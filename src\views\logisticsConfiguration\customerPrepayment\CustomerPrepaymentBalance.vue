<template>
    <div class="app-container customer-auto-height-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryParams" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.prevent>
                <el-form-item label="客户名称" prop="companyId">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择客户" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in signAdvancePaymentContract" :key="item.id" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="当前余额" prop="balance">
                    <div style="width: 220px">
                        <el-select v-model="queryParams.balanceCompare" style="width: 45%">
                            <el-option v-for="(item, index) in balanceOptions" :key="index" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                        <el-input v-model="queryParams.balance" clearable placeholder="请输入金额" style="width: 55%" @keyup.enter="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="预付金额" prop="advanceAmt">
                    <div style="width: 220px">
                        <el-select v-model="queryParams.advanceAmtCompare" style="width: 45%">
                            <el-option v-for="(item, index) in balanceOptions" :key="index" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                        <el-input v-model="queryParams.advanceAmt" clearable placeholder="请输入预付金额" style="width: 55%" @keyup.enter="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="返利金额" prop="rebateAmt">
                    <div style="width: 220px">
                        <el-select v-model="queryParams.rebateAmtCompare" style="width: 45%">
                            <el-option v-for="(item, index) in balanceOptions" :key="index" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                        <el-input v-model="queryParams.rebateAmt" clearable placeholder="请输入返利金额" style="width: 55%" @keyup.enter="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="使用金额" prop="useAmt">
                    <div style="width: 220px">
                        <el-select v-model="queryParams.useAmtCompare" style="width: 45%">
                            <el-option v-for="(item, index) in balanceOptions" :key="index" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                        <el-input v-model="queryParams.useAmt" clearable placeholder="请输入使用金额" style="width: 55%" @keyup.enter="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="预支额度" prop="advanceLimit">
                    <div style="width: 220px">
                        <el-select v-model="queryParams.advanceLimitCompare" style="width: 45%">
                            <el-option v-for="(item, index) in balanceOptions" :key="index" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                        <el-input v-model="queryParams.advanceLimit" clearable placeholder="请输入预支额度" style="width: 55%" @keyup.enter="handleQuery" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="创建日期" prop="createDate">
                    <el-date-picker v-model="queryParams.createDate" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" style="width: 240px" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="结算日期" prop="latestCostTime">
                    <el-date-picker v-model="queryParams.latestCostTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" style="width: 240px" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery" />
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!-- 数据汇总信息 -->
        <el-card :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <div class="flex justify-around">
                <el-statistic :precision="2" :value="summaryData.totalAdvanceAmt || 0" :value-style="{ color: '#5670FE' }" group-separator="," title="预付金额"></el-statistic>
                <el-statistic :precision="2" :value="summaryData.totalRebateAmt || 0" :value-style="{ color: '#F4AC00' }" group-separator="," title="返利金额"></el-statistic>
                <el-statistic :precision="2" :value="summaryData.totalUseAmt || 0" :value-style="{ color: '#FF6B6B' }" group-separator="," title="使用金额"></el-statistic>
                <el-statistic :precision="2" :value="summaryData.totalBalance || 0" :value-style="{ color: '#1ACD7E' }" group-separator="," title="当前余额"></el-statistic>
            </div>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <div>
                    <el-button type="primary" @click="handleClickCreateANewAccount('add')">新建账户</el-button>
                    <el-button v-if="dataList && dataList.length" :loading="loadingExport" type="primary" @click="handleClickExportAll">全部导出</el-button>
                </div>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="customerAdvancePaymentBalanceTable" @queryTable="getList" />
            </div>
            <column-table ref="customerAdvancePaymentBalanceTable" v-loading="loading" :columns="columns" :data="dataList" :row-class-name="tableRowClassName" :show-index="true" :stripe="false" class="customer-auto-height-table" element-loading-text="加载中..." max-height="null">
                <template #status="{ row }">
                    <span :class="row.status === '0' ? 'text-green-500' : 'text-red-500'">{{ row.status === '0' ? '正常' : '不正常' }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-edit" link size="small" type="warning" @click="handleClickCreateANewAccount('modify', row)">修改</el-button>
                    <el-button icon="el-icon-info-filled" link size="small" type="success" @click="handleClickRechargeEntry(row)">充值录入</el-button>
                    <el-button v-hasPermi="['advance:transaction:entry']" icon="el-icon-edit" link size="small" type="warning" @click="handleClickTransactionEntry(row)">交易录入</el-button>
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickTransactionDetails(row)">交易明细</el-button>
                </template>
            </column-table>
            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
            </div>
        </el-card>

        <!-- / 新增、修改账户 -->
        <el-drawer v-if="customerSAccountVisible" v-model="customerSAccountVisible" :title="customerSAccountFormTitle" size="500px" @close="hideCustomerSAccount">
            <div v-loading="loadingCustomerSAccount" :element-loading-text="loadingCustomerSAccountText" class="p16" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <el-form ref="customerSAccountForm" :model="customerSAccountForm" :rules="customerSAccountFormRules" label-width="auto">
                        <el-form-item label="客户名称" prop="companyId">
                            <el-select v-model="customerSAccountForm.companyId" :disabled="isModify" clearable filterable placeholder="请选择客户" style="width: 100%">
                                <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="余额提醒下限" prop="balanceWarn">
                            <el-input-number v-model="customerSAccountForm.balanceWarn" :max="**********" :min="-**********" :step="0.01" clearable placeholder="请输入余额提醒下限" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="余额不足时下单权限" prop="orderAuth">
                            <el-radio-group v-model="customerSAccountForm.orderAuth">
                                <el-radio label="1">禁用</el-radio>
                                <el-radio label="0">不禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="预支额度" prop="advanceLimit">
                            <el-input-number v-model="customerSAccountForm.advanceLimit" :disabled="customerSAccountForm.orderAuth !== '1'" :max="**********" :min="0" :step="1" clearable placeholder="请输入预支额度" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="customerSAccountForm.remark" :rows="2" clearable maxlength="60" placeholder="请输入备注" show-word-limit type="textarea"></el-input>
                        </el-form-item>
                    </el-form>
                    <div style="display: flex; justify-content: end; margin-top: 10px">
                        <el-button type="info" @click="hideCustomerSAccount">取消</el-button>
                        <el-button v-if="isModify" type="primary" @click="handleClickAddModifyAccount('modify')">修改</el-button>
                        <el-button v-else type="primary" @click="handleClickAddModifyAccount('add')">新增</el-button>
                    </div>
                </el-card>
            </div>
        </el-drawer>

        <!-- / 充值录入 drawer   -->
        <el-drawer v-if="rechargeEntryVisible" v-model="rechargeEntryVisible" size="600px" title="充值录入" @close="hideRechargeEntry">
            <div v-loading="loadingRechargeEntry" :element-loading-text="loadingRechargeEntryText" class="p16" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <el-form ref="rechargeEntryForm" :model="rechargeEntryForm" :rules="rechargeEntryFormRules" label-width="auto">
                        <el-form-item label="客户名称" prop="companyName">
                            <el-input v-model="rechargeEntryForm.companyName" clearable disabled placeholder="请输入客户名称"></el-input>
                        </el-form-item>
                        <el-form-item label="充值金额" prop="amount">
                            <el-input-number v-model="rechargeEntryForm.amount" :max="**********" :min="0" :step="0.01" clearable placeholder="请输入充值金额" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="返利金额" prop="rebateAmt">
                            <el-input-number v-model="rechargeEntryForm.rebateAmt" :max="**********" :min="0" :step="0.01" clearable placeholder="请输入返利金额" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="充值合计金额" prop="totalRechargeAmount">
                            <el-input v-model="totalRechargeAmount" clearable disabled placeholder="请输入充值合计金额"></el-input>
                        </el-form-item>
                        <el-form-item label="充值时间" prop="createDate">
                            <el-date-picker v-model="rechargeEntryForm.createDate" placeholder="选择日期" style="width: 100%" type="date" value-format="YYYY-MM-DD"></el-date-picker>
                        </el-form-item>
                        <el-form-item label="充值方式" prop="paymentType">
                            <el-select v-model="rechargeEntryForm.paymentType" clearable filterable placeholder="请选择充值方式" style="width: 100%">
                                <el-option v-for="item in rechargeMethodList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="上传付款凭证" prop="uploadPaymentVoucher">
                            <el-upload
                                v-model:file-list="uploadPaymentVoucherList"
                                :action="uploadFileUrl"
                                :before-upload="beforeUpload"
                                :class="{ 'avatar__uploader__vertical__display': hideUploadCompanyLogo, 'avatar__uploader__vertical': !hideUploadCompanyLogo }"
                                :headers="headers"
                                :limit="2"
                                :on-preview="handlePicturePaymentVoucher"
                                :on-remove="(file, fileList) => handleRemovePaymentVoucher(file, fileList, 'uploadPaymentVoucherList')"
                                :on-success="(e) => fileUploadSuccess(e, 'uploadPaymentVoucherList')"
                                list-type="picture-card"
                            >
                                <el-icon>
                                    <Plus />
                                </el-icon>
                                <template #tip>
                                    <div class="el__upload__tip">最多2张</div>
                                </template>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="rechargeEntryForm.remark" :rows="2" clearable maxlength="60" placeholder="请输入备注" show-word-limit type="textarea"></el-input>
                        </el-form-item>
                    </el-form>
                    <div style="display: flex; justify-content: end; margin-top: 10px">
                        <el-button type="info" @click="hideRechargeEntry">取消</el-button>
                        <el-button type="primary" @click="handleClickToConfirmRechargeEntry">确定</el-button>
                    </div>
                </el-card>
            </div>
        </el-drawer>

        <!-- / 交易录入 drawer   -->
        <el-drawer v-if="transactionEntryVisible" v-model="transactionEntryVisible" size="600px" title="交易录入" @close="hideTransactionEntry">
            <div v-loading="loadingTransactionEntry" :element-loading-text="loadingTransactionEntryText" class="p16" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <el-form ref="transactionEntryForm" :model="transactionEntryForm" :rules="transactionEntryRules" label-width="auto">
                        <el-form-item label="客户名称" prop="companyName">
                            <el-input v-model="transactionEntryForm.companyName" clearable disabled placeholder="请输入客户名称"></el-input>
                        </el-form-item>
                        <el-form-item label="账户余额" prop="balance">
                            <el-input-number v-model="transactionEntryForm.balance" :max="**********" :min="0" :step="0.01" clearable disabled placeholder="请输入账户余额" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="订单号" prop="orderNo">
                            <el-select v-model="transactionEntryForm.orderNo" :loading="orderSearchLoading" :remote-method="remoteMethod" filterable placeholder="请输入订单号搜索" remote reserve-keyword style="width: 100%">
                                <el-option v-for="item in orderOptions" :key="item" :label="item" :value="item"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="交易类型" prop="dealType">
                            <el-select v-model="transactionEntryForm.dealType" clearable filterable placeholder="请选择交易类型" style="width: 100%">
                                <el-option v-for="item in transactionTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="交易金额" prop="amount">
                            <el-input-number v-model="transactionEntryForm.amount" :max="**********" :min="0" :step="0.01" clearable placeholder="请输入交易金额" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="交易时间" prop="createDate">
                            <el-date-picker v-model="transactionEntryForm.createDate" :default-time="['00:00:00', '23:59:59']" placeholder="选择日期" style="width: 100%" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
                        </el-form-item>

                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="transactionEntryForm.remark" :rows="2" clearable maxlength="60" placeholder="请输入备注" show-word-limit type="textarea"></el-input>
                        </el-form-item>
                    </el-form>
                    <div style="display: flex; justify-content: end; margin-top: 10px">
                        <el-button type="info" @click="hideTransactionEntry">取消</el-button>
                        <el-button type="primary" @click="handleClickToConfirmTransactionEntry">确定</el-button>
                    </div>
                </el-card>
            </div>
        </el-drawer>

        <!-- /图片预览 -->
        <el-image-viewer v-if="dialogVisible" :initial-index="0" :url-list="dialogImageUrl" :z-index="9999" @close="imgClose" />
    </div>
</template>

<script>
import moment from 'moment/moment';
import OrderFeeDetailsWithDetails from '@/views/logisticsConfiguration/customerPrepayment/CustomerPrepaymentBalanceDetail.vue';
import customerPrepayment from '@/api/logisticsConfiguration/customerPrepayment.js';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation.js';
import tool from '@/utils/tool';
import { Close, Delete, Plus } from '@element-plus/icons-vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'CustomerPrepaymentBalance',
    components: { SearchButton, OrderFeeDetailsWithDetails, ColumnTable, RightToolbar, Plus, Close, Delete },
    data() {
        return {
            showSearch: true,
            queryParams: {
                current: 1,
                size: 10,
                companyId: null,
                balanceCompare: undefined,
                balance: null,
                advanceAmt: null,
                advanceAmtCompare: undefined,
                rebateAmt: null,
                rebateAmtCompare: undefined,
                useAmt: null,
                useAmtCompare: undefined,
                advanceLimit: null,
                advanceLimitCompare: undefined,
                createDate: null,
                latestCostTime: null,
                beginCreateDate: undefined,
                endCreateDate: undefined,
                beginLatestCostTime: undefined,
                endLatestCostTime: undefined
            },
            balanceOptions: [
                { name: '大于', value: '>' },
                { name: '小于', value: '<' },
                { name: '等于', value: '=' }
            ],
            columns: [
                { title: '客户预付款帐号', key: 'advanceCode', align: 'center', minWidth: '180px', columnShow: true, fixed: 'left' },
                { title: '客户名称', key: 'companyName', align: 'center', minWidth: '210px', columnShow: true, showOverflowTooltip: true },
                { title: '预付金额（元）', key: 'advanceAmt', align: 'center', minWidth: '120px', columnShow: true },
                { title: '返利金额（元）', key: 'rebateAmt', align: 'center', minWidth: '120px', columnShow: true },
                { title: '使用金额（元）', key: 'useAmt', align: 'center', minWidth: '120px', columnShow: true },
                { title: '当前余额（元）', key: 'balance', align: 'center', minWidth: '120px', columnShow: true },
                { title: '预支额度（元）', key: 'advanceLimit', align: 'center', minWidth: '120px', columnShow: true },
                // { title: '余额提醒下限', key: 'balanceWarn', align: 'center', minWidth: '100px', columnShow: true },
                { title: '创建日期', key: 'createDate', align: 'center', minWidth: '120px', columnShow: true },
                { title: '最新结算时间', key: 'latestCostTime', align: 'center', minWidth: '120px', columnShow: true },
                { title: '状态', key: 'status', align: 'center', minWidth: '80px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '340px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            customerList: [],
            signAdvancePaymentContract: [],
            customerSAccountVisible: false,
            loadingCustomerSAccount: false,
            loadingCustomerSAccountText: '加载中...',
            customerSAccountForm: {
                companyId: null,
                balanceWarn: null,
                orderAuth: '1',
                advanceLimit: 0,
                remark: null,
                id: null
            },
            customerSAccountFormRules: {
                companyId: [{ required: true, message: '请选择客户', trigger: 'blur' }],
                balanceWarn: [{ required: true, message: '请输入余额提醒下限', trigger: 'blur' }],
                orderAuth: [{ required: true, message: '请选择余额不足时下单权限', trigger: 'blur' }],
                advanceLimit: [
                    {
                        validator: (rule, value, callback) => {
                            // 只有当 orderAuth 为 '1'（禁用下单权限）时才验证预支额度
                            if (this.customerSAccountForm.orderAuth === '1') {
                                if (value === null || value === undefined || value === '') {
                                    callback(new Error('请输入预支额度'));
                                } else {
                                    callback();
                                }
                            } else {
                                // 当 orderAuth 为 '0' 时，不验证预支额度
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ]
            },
            rechargeEntryVisible: false,
            loadingRechargeEntry: false,
            loadingRechargeEntryText: '加载中...',
            rechargeEntryForm: {
                id: null,
                companyName: null,
                amount: 0,
                rebateAmt: 0,
                // createDate 默认当前时间
                createDate: moment().format('YYYY-MM-DD'),
                paymentType: null,
                remark: null
            },
            rechargeEntryFormRules: {
                companyName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
                amount: [{ required: true, message: '请输入充值金额', trigger: 'blur' }],
                rebateAmt: [{ required: true, message: '请输入返利金额', trigger: 'blur' }]
            },
            rechargeMethodList: [],
            transactionTypeList: [],
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD, // 上传的图片服务器地址
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 大小限制(MB)
            fileSize: 5,
            fileType: ['png', 'jpg', 'jpeg'],
            uploadPaymentVoucherList: [],
            dialogImageUrl: [],
            dialogVisible: false,
            customerSAccountFormTitle: '新增客户预付款账户',
            isModify: false,
            transactionEntryVisible: false, // 交易录入弹窗
            loadingTransactionEntry: false,
            loadingTransactionEntryText: '加载中...',
            transactionEntryForm: {
                companyName: '',
                advanceCode: '', // 预付款账户
                companyId: '', // 客户id
                orderNo: '', // 订单号
                dealType: '', // -1:扣款 4:退款
                paymentType: '6', // -5:财务调整
                amount: '', // 交易金额
                createDate: moment().format('YYYY-MM-DD HH:mm:ss'), // 交易时间
                remark: '' // 备注
            },
            transactionEntryRules: {
                companyName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
                orderNo: [{ required: true, message: '请选择订单号', trigger: 'blur' }],
                dealType: [{ required: true, message: '请选择交易类型', trigger: 'blur' }],
                amount: [
                    { required: true, message: '请输入交易金额', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (value <= 0) {
                                callback(new Error('交易金额必须大于零'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ]
            },
            orderOptions: [], // 订单搜索数据
            orderSearchLoading: false, // 订单搜索加载状态
            isShowAll: false,
            isExpandStatus: false,
            loadingExport: false,
            shortcuts: setDatePickerShortcuts(),
            summaryData: {
                advanceAmt: 0,
                rebateAmt: 0,
                useAmt: 0,
                balance: 0
            }
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                return this.selectDictLabel(this[dictionaryName], value) || value || '--';
            };
        },
        hideUploadCompanyLogo() {
            return this.uploadPaymentVoucherList.length >= 2;
        },
        totalRechargeAmount() {
            return this.rechargeEntryForm.amount + this.rechargeEntryForm.rebateAmt;
        }
    },
    watch: {
        // 监听 orderAuth 变化，当设置为"不禁用"时重置预支额度
        'customerSAccountForm.orderAuth'(newVal, oldVal) {
            if (newVal === '0' && oldVal === '1') {
                // 当从"禁用"切换到"不禁用"时，重置预支额度为0
                this.customerSAccountForm.advanceLimit = 0;
            }
        }
    },
    async created() {
        // 交易类型 字典
        let rechargeMethodList = await this.getDictList('fourpl_payment_type');
        this.rechargeMethodList = rechargeMethodList.filter((item) => item.value !== '4' && item.value !== '5');
        // 交易类型 字典
        let transactionTypeList = await this.getDictList('fourpl_deal_type');
        this.transactionTypeList = transactionTypeList.filter((item) => item.value != '2' && item.value != '3');
        this.getPaymentList();
        this.getList();
    },
    methods: {
        // 验证文件格式与大小
        beforeUpload(file) {
            // 校检文件类型
            if (this.fileType) {
                let fileExtension = '';
                if (file.name.lastIndexOf('.') > -1) {
                    fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
                }
                const isTypeOk = this.fileType.some((type) => {
                    if (file.type.indexOf(type) > -1) return true;
                    if (fileExtension && fileExtension.indexOf(type) > -1) return true;
                    return false;
                });
                if (!isTypeOk) {
                    this.$message.error(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`);
                    return false;
                }
            }
            // 校检文件大小
            // if (this.fileSize) {
            //     const isLt = file.size / 1024 / 1024 < this.fileSize;
            //     if (!isLt) {
            //         this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
            //         return false;
            //     }
            // }
            return true;
        },
        /**
         * 上传图片成功
         * @param response
         * @param fileName
         */
        fileUploadSuccess(response, fileName) {
            if (response && response.code === 200) {
                this.$message.success('上传成功');
            } else {
                this.$message.error('上传失败');
            }
        },
        formatDate(val) {
            return val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--';
        },
        /**
         *  获取签订预付款合同的货主
         */
        getCustomerList() {
            enterpriseCooperation.advanceCompanySelect().then((res) => {
                if (res.code === 200 && res.data) {
                    this.customerList = res.data || [];
                } else {
                    this.customerList = [];
                }
            });
        },
        getList() {
            this.loading = true;
            const { createDate, latestCostTime, ...restParams } = this.queryParams;
            customerPrepayment
                .listAdvancePayment(restParams)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                        this.getSummaryData();
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         *  查询预付款列表
         */
        getPaymentList() {
            customerPrepayment.companySelect().then((res) => {
                if (res.code === 200 && res.data) {
                    this.signAdvancePaymentContract = res.data || [];
                } else {
                    this.signAdvancePaymentContract = [];
                    this.queryParams.companyId = null;
                }
            });
        },
        /**
         * 新增账户
         * @param type add:新增 modify:修改
         */
        handleClickAddModifyAccount(type) {
            this.$refs.customerSAccountForm.validate((valid) => {
                if (valid) {
                    this.loadingCustomerSAccount = true;
                    if (type === 'add') {
                        this.loadingCustomerSAccountText = '新增中...';
                        // 根据 orderAuth 值决定是否传递 advanceLimit 参数
                        const addParams = { ...this.customerSAccountForm };
                        if (this.customerSAccountForm.orderAuth !== '1') {
                            // 当余额不足时下单权限为"不禁用"时，不传递 advanceLimit 参数
                            delete addParams.advanceLimit;
                        }
                        customerPrepayment
                            .saveAdvancePayment(addParams)
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('新增成功');
                                    this.hideCustomerSAccount();
                                    this.getList();
                                }
                            })
                            .finally(() => {
                                this.loadingCustomerSAccount = false;
                            });
                    } else if (type === 'modify') {
                        const params = {
                            id: this.customerSAccountForm.id,
                            balanceWarn: this.customerSAccountForm.balanceWarn,
                            orderAuth: this.customerSAccountForm.orderAuth,
                            remark: this.customerSAccountForm.remark
                        };
                        // 只有当 orderAuth 为 '1'（禁用下单权限）时才传递 advanceLimit 参数
                        if (this.customerSAccountForm.orderAuth === '1') {
                            params.advanceLimit = this.customerSAccountForm.advanceLimit;
                        }
                        this.loadingCustomerSAccountText = '修改中...';
                        customerPrepayment
                            .saveAdvancePayment(params)
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('修改成功');
                                    this.hideCustomerSAccount();
                                    this.getList();
                                }
                            })
                            .finally(() => {
                                this.loadingCustomerSAccount = false;
                            });
                    }
                }
            });
        },
        /**
         * 显示新增、修改账户 drawer
         * @param type add:新增 modify:修改
         * @param row 当前行数据
         */
        handleClickCreateANewAccount(type, row) {
            // 获取签订预付款合同的货主
            this.getCustomerList();
            if (type === 'add') {
                this.customerSAccountFormTitle = '新增账户';
                this.isModify = false;
                this.customerSAccountVisible = true;
            } else if (type === 'modify') {
                this.customerSAccountFormTitle = '修改账户';
                this.isModify = true;
                this.customerSAccountVisible = true;
                this.$nextTick(() => {
                    this.customerSAccountForm = Object.assign({}, row);
                });
            }
        },
        /**
         * 全部导出
         */
        handleClickExportAll() {
            if (this.loadingExport) return;
            this.loadingExport = true;
            const { createDate, latestCostTime, ...restQueryParams } = this.queryParams;
            const exportParams = {
                ...restQueryParams,
                filename: '预付款列表.xls'
            };
            customerPrepayment
                .advancePaymentExport(exportParams, '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '预付款列表.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                    } else {
                        this.$message.error('导出异常请联系管理员');
                    }
                })
                .finally(() => {
                    this.loadingExport = false;
                });
        },
        /**
         * 打开充值 drawer
         * @param data 当前行数据
         */
        handleClickRechargeEntry(data) {
            const { id, companyName } = data;
            // id companyName 赋值给 this.rechargeEntryForm 对应字段
            this.rechargeEntryForm.id = id;
            this.rechargeEntryForm.companyName = companyName;
            this.rechargeEntryVisible = true;
        },
        /**
         * 确定充值录入
         */
        handleClickToConfirmRechargeEntry() {
            this.$refs.rechargeEntryForm.validate((valid) => {
                if (valid) {
                    this.loadingRechargeEntry = true;
                    this.loadingRechargeEntryText = '充值中...';
                    let file = [];
                    if (this.uploadPaymentVoucherList.length > 0) {
                        this.uploadPaymentVoucherList.forEach((item) => {
                            file.push({
                                fileName: item?.response?.data?.fileName,
                                fileUrl: item?.response?.data?.fileUrl
                            });
                        });
                    }
                    customerPrepayment
                        .recharge({ ...this.rechargeEntryForm, file })
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('充值成功');
                                this.hideRechargeEntry();
                                this.getList();
                            }
                        })
                        .finally(() => {
                            this.loadingRechargeEntry = false;
                        });
                }
            });
        },
        /**
         * 交易录入
         */
        handleClickToConfirmTransactionEntry() {
            this.$refs.transactionEntryForm.validate((valid) => {
                if (valid) {
                    this.loadingTransactionEntry = true;
                    this.loadingTransactionEntryText = '交易中...';
                    customerPrepayment
                        .transaction(this.transactionEntryForm)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('交易成功');
                                this.hideTransactionEntry();
                                this.getList();
                            }
                        })
                        .finally(() => {
                            this.loadingTransactionEntry = false;
                        });
                }
            });
        },
        /**
         * 打开交易明细 drawer
         */
        handleClickTransactionDetails(data) {
            const { id } = data;
            const params = { advanceId: id };
            this.$router.push({ name: 'CustomerPrepaymentBalanceDetail', query: params });
        },
        /**
         * 交易录入弹窗
         */
        handleClickTransactionEntry(row) {
            this.transactionEntryVisible = true;
            this.transactionEntryForm = {
                companyName: row.companyName,
                balance: row.balance,
                advanceCode: row.id, // 预付款账户id
                companyId: row.companyId, // 客户id
                orderNo: '', // 订单号
                dealType: '', // -1:扣款 4:退款
                paymentType: '6', // -5:财务调整
                amount: '', // 交易金额
                createDate: moment().format('YYYY-MM-DD HH:mm:ss'), // 交易时间
                remark: '' // 备注
            };
        },
        // 预览上传图片
        handlePicturePaymentVoucher(file) {
            this.dialogImageUrl = [];
            this.dialogImageUrl.push(file.url);
            this.dialogVisible = true;
        },
        /**
         * 获取汇总数据
         */
        async getSummaryData() {
            try {
                // 使用与列表接口相同的查询参数获取统计数据
                const params = { ...this.queryParams };
                // 移除分页参数
                delete params.current;
                delete params.size;

                // 处理日期范围参数
                if (params.createDate && params.createDate.length === 2) {
                    params.beginCreateDate = params.createDate[0];
                    params.endCreateDate = params.createDate[1];
                    delete params.createDate;
                }

                if (params.latestCostTime && params.latestCostTime.length === 2) {
                    params.beginLatestCostTime = params.latestCostTime[0];
                    params.endLatestCostTime = params.latestCostTime[1];
                    delete params.latestCostTime;
                }

                // 调用统计接口
                const res = await customerPrepayment.statAdvancePayment(params);
                this.summaryData = res.data || {};
            } catch (error) {
                console.error('获取统计数据失败:', error);
                this.summaryData = {
                    totalAdvanceAmt: 0,
                    totalRebateAmt: 0,
                    totalUseAmt: 0,
                    totalBalance: 0
                };
            }
        },

        handleQuery() {
            this.queryParams.current = 1;
            const { createDate, latestCostTime } = this.queryParams;
            if (createDate && createDate.length) {
                this.queryParams.beginCreateDate = createDate[0] ? createDate[0] + ' 00:00:00' : '';
                this.queryParams.endCreateDate = createDate[1] ? createDate[1] + ' 23:59:59' : '';
            } else {
                this.queryParams.beginCreateDate = undefined;
                this.queryParams.endCreateDate = undefined;
            }

            if (latestCostTime && latestCostTime.length) {
                this.queryParams.beginLatestCostTime = latestCostTime[0] ? latestCostTime[0] + ' 00:00:00' : '';
                this.queryParams.endLatestCostTime = latestCostTime[1] ? latestCostTime[1] + ' 23:59:59' : '';
            } else {
                this.queryParams.beginLatestCostTime = undefined;
                this.queryParams.endLatestCostTime = undefined;
            }
            this.getList();
        },
        /**
         * 删除上传图片
         * @param file
         * @param fileList
         * @param fileName
         */
        handleRemovePaymentVoucher(file, fileList, fileName) {
            this[fileName] = fileList;
        },
        /**
         * 隐藏新增、修改账户 drawer
         */
        hideCustomerSAccount() {
            this.customerSAccountVisible = false;
            this.$refs.customerSAccountForm.resetFields();
        },
        /**
         * 隐藏充值 drawer
         */
        hideRechargeEntry() {
            this.rechargeEntryVisible = false;
            this.$refs.rechargeEntryForm.resetFields();
            this.uploadPaymentVoucherList = [];
        },
        /**
         * 关闭交易录入弹窗
         */
        hideTransactionEntry() {
            this.transactionEntryVisible = false;
            this.$refs.transactionEntryForm.resetFields();
        },

        imgClose() {
            this.dialogVisible = false;
        },
        /**
         * 搜索获取订单列表
         */
        remoteMethod(query) {
            if (query !== '') {
                this.orderSearchLoading = true;
                customerPrepayment
                    .searchOrderNo({ orderNo: query })
                    .then((res) => {
                        if (res.code === 200 && res.data) {
                            this.orderOptions = res.data || [];
                            if (this.orderOptions && this.orderOptions.length == 1) {
                                this.transactionEntryForm.orderNo = this.orderOptions[0];
                            }
                        }
                    })
                    .finally(() => {
                        this.orderSearchLoading = false;
                    });
            } else {
                this.orderOptions = [];
            }
        },
        /**
         * 重置查询参数
         */
        resetQuery() {
            this.$refs['queryParams'].resetFields();
            this.queryParams.balanceCompare = undefined;
            this.handleQuery();
        },
        /**
         * 展开/折叠功能
         * @param {number} type - 状态值：0 表示收起，1 表示展开
         */
        setExpandStatus(type) {
            this.isExpandStatus = type === 0; // 当 type 为 0 时，表示收起（即不展开）
        },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 提取余额提醒
         * @param row
         * @param rowIndex
         */
        tableRowClassName({ row, rowIndex }) {
            const { balance, balanceWarn } = row;
            // balance balanceWarn 字段都存在 可以为0
            // balance < balanceWarn 返回 warning-row
            if (balance !== undefined && balanceWarn !== undefined && balance < balanceWarn) {
                return 'warning-row';
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}

.avatar__uploader__vertical__display {
    ::v-deep {
        .el__upload__tip {
            display: none;
        }

        .el-upload--picture-card {
            display: none;
        }
    }
}

.avatar__uploader__vertical {
    display: flex;

    :deep(.el__upload__tip) {
        color: #ff2a2a;
        margin-left: 11px;
        font-size: 12px;
        align-self: center;
    }
}

::v-deep {
    .el-upload-list--picture-card {
        .el-upload-list__item-actions {
            width: var(--el-upload-list-picture-card-size);
            height: var(--el-upload-list-picture-card-size);

            span + span {
                margin-left: 10px;
            }
        }

        .el-icon--close-tip {
            display: none !important;
        }
    }
}
</style>
