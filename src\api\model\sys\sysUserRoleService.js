import http from "@/utils/request"

/*
 *@description: 系统用户角色
 *@author: 路正宁
 *@date: 2023-03-17 12:04:19
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/sys/userRole/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/sys/userRole/delete',
       {ids: ids}
    )
  },

  /*
   * 用户角色配置
   * @author: 路正宁
   * @date: 2023-04-06 10:47:21
  */
  configUserRole: function (userId,roleIds) {
    return http.get(
       '/sys/userRole/configUserRole',
       {userId: userId,
        roleIds:roleIds
      }
    )
  },
  queryById: function (id) {
    return http.get(
       '/sys/userRole/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/sys/userRole/list',
       params
    )
  },

  exportTemplate: function () {
    return http.get(
       '/sys/userRole/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/sys/userRole/export',
       params,
       'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
       '/sys/userRole/import',
       data
    )
  },
  findUserByRoleType: function (params) {
    return http.get(
        '/sys/userRole/list',
        params
    )
  }
}
