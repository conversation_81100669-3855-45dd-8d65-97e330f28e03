import request from '@/utils/request';

export default {
    // 列表
    statementList: function (params) {
        return request.get('/cost/statement/list', params);
    },
    // 对账单列表费用统计
    statementCostStatistics: function (params) {
        return request.get('/cost/statement/cost/stats', params);
    },
    // 费用详情
    statementDetail: function (params) {
        return request.get('/cost/statement/orderCostDetail', params);
    },
    // 生成付款单前置数据查询
    getPaymentData: function (params) {
        return request.post('/cost/statement/frontDataQuery', params);
    },
    // 生成收款单
    createPayment: function (params) {
        return request.post('/cost/statement/genPayment', params);
    },
    // 导出
    export: function (params, config, resDetail, responseType) {
        return request.get('/cost/statement/export', params, config, resDetail, responseType);
    },
    // 货主列表
    getCustomerList: function (params) {
        return request.post('/cost/statement/queryCompanySelect', params);
    },
    // 批量撤销
    cancelCheck: function (params) {
        return request.post('/cost/statement/cancelCheck', params);
    }
};
