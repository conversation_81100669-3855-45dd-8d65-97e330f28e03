<template>
  <div>
    <h4 class="titleH4" style="font-size: 13px;margin-left: 35px;">
      申请单据编号: <span style="font-weight: 400;color: #505050">{{ strs.applyNo }}</span>&emsp;
      申请日期: <span style="font-weight: 400;color: #505050">{{
        functionIndex.transformTimestamp(strs.applyDate)
      }}</span>&emsp;
      申请人: <span style="font-weight: 400;color: #505050">{{ strs.createBy.name }}</span>&emsp;
      类型: <span style="font-weight: 400;color: #505050">{{
        props.strs?.type == '1' ? '采购入库' : "采退出库"
      }}</span></h4>
    <h4 class="el-dialog__title" style="margin-bottom: 10px">发票信息</h4>
    <el-table :data="table1" style="width: 100%">
      <el-table-column align="center" label="发票号码" property="invoiceNo"/>
      <el-table-column align="center" label="发票代码" property="invoiceCode"/>
      <el-table-column align="center" label="发票类型" property="type">
        <template #default="scope">
          {{ echo(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="发票供应商" property="invoiceSupplier"/>
      <el-table-column align="center" label="纳税人识别号" property="taxpayerNo"/>
      <el-table-column label="发票日期" property="invoicingDate">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.invoicingDate) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="发票金额" property="">
        <template #default="scope">
          {{ scope.row.invoicingAmount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="不含税金额" property="excludingTax">
        <template #default="scope">
          {{ Number(scope.row.excludingTax).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="税额" property="totalTax">
        <template #default="scope">
          {{ Number(scope.row.totalTax).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="可抵扣税额" property="effectiveTax">
        <template #default="scope">
          {{ Number(scope.row.effectiveTax).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="附件" property="">
        <template #default="scope">
          <p v-for="(item, index) in table1[scope.$index].fileDtos" :key="index"
             style="color: rgb(34, 130, 255);cursor: pointer;" @click="checkImg(item)">{{
              item.name
            }}</p>
        </template>
      </el-table-column>
    </el-table>
    <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">单据信息</h4>
    <el-table :border="true" :data="table2" :header-cell-style="{ 'text-align': 'center' }" style="width: 100%">
      <el-table-column :show-overflow-tooltip="true" align="center" fixed label="序号" prop="">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" align="center" label="单据编号" property="orderCode"
                       width="170px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="单据创建日期" property="purchaseCreateDate"
                       width="100px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.purchaseCreateDate) }}
        </template>
      </el-table-column>
      <el-table-column :label="props.detailType == '1' ? '入库日期' : '出库日期'" :show-overflow-tooltip="true"
                       align="center"
                       property="intoDate" width="100px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.intoDate) }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="商品名称" property="commodity.tradeName" width="130px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="自编码"
                       property="commodity.commoditySelfCode"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="规格"
                       property="commodity.packageSpecification"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家"
                       property="commodity.manufactureName"
                       width="150px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="产地" property="commodity.producingArea"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="供应商" property="suppier" width="100px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="生产日期" property="" width="100px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.commodity.produceDate) }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" align="center" label="批号" property="batchNumber" width="150px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="有效期" property="validate" width="100px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.validate) }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" align="center" label="单位" property="basicUnit" width="100px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="商品编号" property="commodity.commodityCode"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" align="center" label="单价" property="unitPrice" width="100px"/>
      <el-table-column :label="props.detailType == '1' ? '入库数量' : '出库数量'" :show-overflow-tooltip="true"
                       align="center"
                       property="receivingQuantity" width="100px"/>
      <el-table-column v-if="props.detailType == '1'" align="left" fixed="right" label="发票信息"
                       property="invoicingInfo"
                       width="150px">
        <template #default="scope">
          <div v-for="(item, index) in scope.row.invoicingInfo" :key="index">
            <p v-if="index <= 2 || scope.row.checkFlag">{{
                item
              }}</p>
            <p style="text-align: center">
              <el-button
                  v-if="((index == 3 && !scope.row.checkFlag) || ((index + 1) == scope.row.invoicingInfo.length && scope.row.checkFlag) && scope.row.invoicingInfo.length > 3)"
                  size="small" text type="primary" @click="checkFn(scope.$index)">
                {{ scope.row.checkFlag ? '收起' : '展开' }}
              </el-button>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="props.detailType == '2'" :show-overflow-tooltip="true" align="center" fixed="right"
                       label="开票数量" property="invoicingQuantity" width="100px"/>
      <el-table-column :label="props.detailType == '1' ? '发票金额' : '开票金额'" :show-overflow-tooltip="true"
                       align="center"
                       fixed="right" property="allPrice" width="100px"/>
    </el-table>
    <p class="textStyle" style="margin: 15px 0 0px 0">合计开票金额: <span style="color:red">{{
        price.toFixed(2)
      }}元</span></p>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import {functionIndex} from "@/views/commodity/functionIndex";

const {proxy} = getCurrentInstance();
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const data = reactive({
  checkFlag: false
})
const checkFn = (ind) => {
  table2.value[ind].checkFlag = !table2.value[ind].checkFlag
}
const close = () => {
  data.checkFlag = false
  data.imgUrl = []
}
const emit = defineEmits([])
const props = defineProps({
  table1: {
    default: []
  },
  table2: {
    default: []
  },
  strs: {
    default: {}
  },
  price: {
    default: {}
  },
  detailType: {
    default: 1
  }
})
const echo = (str) => {
  let find = invoiceType.value.find(item => item.value == str)?.name
  return find ? find : ""
}
const checkImg = (row) => {
  data.imgUrl = []
  const fileName = row.name.split(".")
  data.imgUrl = []
  if (fileName[fileName.length - 1] == "pdf") {
    var s = window.location.toString();
    var s1 = s.substr(7, s.length);
    var s2 = s1.indexOf("/");
    s = s.substr(0, 8 + s2);
    window.open(s + row.url)
  } else {
    data.imgUrl.push(row.url)
    data.checkFlag = true
  }
}
const invoiceType = ref()
let {table1, table2, price, strs} = toRefs(props)
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  let typeLo = JSON.parse(window.localStorage.getItem('In_Invioce'))
  if (typeLo) {
    invoiceType.value = typeLo
  } else {
    invoiceType.value = await proxy.getDictList("In_Invioce")
  }
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({})

</script>
<style lang='scss' scoped>
.titleH4 {
  margin-bottom: 20px;
  color: #000;
  font-weight: bolder;
  font-size: 15px;
}

.textStyle {
  font-size: 15px;
  color: #000;
  font-weight: bolder;
}
</style>
