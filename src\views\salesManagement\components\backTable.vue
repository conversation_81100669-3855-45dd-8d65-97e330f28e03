<template>
  <div>
    <DragTableColumn v-loading="props.loadingFlag" v-model:queryParams="searchForm" :columns="columns"
                     :getList="getSearch" :row-style="functionIndex.tableRowStyles" :tableData="props.tableList"
                     className="salesManagement_salesBack">
      <template v-slot:operate="{ scopeData }">
        <el-button :disabled="scopeData.row.auditStatus != '0' &&
          scopeData.row.auditStatus != '7'
          " link type="danger" @click="deltable(scopeData.row)"><img src="@/assets/icons/delete.png"
                                                                     style="margin: 0px 5px 0 0"/>删除
        </el-button>
        <el-button link type="primary" @click="detailsTable(scopeData.row)"><img src="@/assets/icons/detail.png"
                                                                                 style="margin: 2px 5px 0 0"/>详情
        </el-button>
        <el-button link type="success" @click="logFn(scopeData.row)"><img src="@/assets/icons/review.png"
                                                                          style="margin: 0px 2px 0 0"/>操作记录
        </el-button>
      </template>
    </DragTableColumn>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
import {functionIndex} from "@/views/salesManagement/functionIndex";

const {proxy} = getCurrentInstance();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const data = reactive({})
const emit = defineEmits(['detailsTable', 'logFn', 'getTableList', 'delTable'])
const props = defineProps({
  tableList: {
    default: []
  },
  loadingFlag: {
    default: false
  },
  statusType: {
    default: []
  }
})
const searchForm = ref({
  n7: ""
})
const getSearch = () => {
  emit('getTableList')
}
const columns = ref([
  {label: '申请编号', prop: 'applyNo'},
  {label: '申请日期', prop: 'applyDate', type: 'date'},
  {label: '客户名称', prop: 'customerName'},
  {label: '申退数量', prop: 'retreatQuantity'},
  {label: '申退金额', prop: 'retreatAmount'},
  {label: '申请人', prop: 'applyBy.name'},
  {
    label: '审核状态',
    prop: 'auditStatus',
    searchKey: "n7",
    type: "status",
    filters: JSON.parse(localStorage.getItem('salesType')) || props.statusType
  },
  {label: '操作', prop: 'operate', minWidth: "200px", type: 'operate', fixed: 'right'},
])
const detailsTable = (row) => {
  emit('detailsTable', row)
}
const deltable = (row) => {
  emit('delTable', row)
}
const logFn = (row) => {
  emit('logFn', row)
}

onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  searchForm
})

</script>
<style lang='scss' scoped></style>
