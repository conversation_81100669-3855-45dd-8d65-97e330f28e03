<template>
    <div>
        <el-dialog v-model="visible" :title="auditTitle" top="5vh" width="90%" @close="closeVisible">
            <el-form ref="redPunchApprovalForm" :model="redPunchApprovalForm" label-width="auto" style="max-height: 70vh">
                <el-descriptions :column="3" border size="small" title="基本信息">
                    <el-descriptions-item label="红字发票申请编号">
                        <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.applyNo || '--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请日期">
                        <span class="whitespace-nowrap">{{ taskInfo.redRushApply.applyTime || '--'}}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请人">
                        <span class="whitespace-nowrap">{{ taskInfo.redRushApply.applyUser || '--'}}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="红冲原因">
                        <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.reason?selectDictLabel(reasonList,taskInfo.redRushApply.reason):'--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="红字发票费">
                        <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.redInvoiceAmount || '--' }}元</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="审核状态">
                        <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.auditStatus?selectDictLabel(auditStatusOptions,taskInfo.redRushApply.auditStatus):'--' }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="开票状态">
                        <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.redInvoiceFlag?selectDictLabel(redInvoiceFlagList,taskInfo.redRushApply.redInvoiceFlag):'--' }}</span>
                    </el-descriptions-item>
                </el-descriptions>
                <div class="flex mt-10 w-full">
                    <el-descriptions :column="1" border size="small" title="蓝字发票信息" style="width:50%;flex-shrink: 0;">
                        <el-descriptions-item label="发票号码">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.blueInvoiceNo || '--' }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="发票客户">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.invoiceCustomer || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="开票日期">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.blueInvoiceDate || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="开票金额">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.blueInvoiceAmount || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="发票代码">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.blueInvoiceCode || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="纳税人识别号">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.taxNo || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="发票类型">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.invoiceType?selectDictLabel(invoiceTypeOptions,taskInfo.redRushApply.invoiceType):'--' }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="附件">
                            <span v-if="taskInfo?.redRushApply?.blueInvoiceFile" class="whitespace-nowrap" style="color:#2A76F8;cursor: pointer;"  @click="handlePreview(taskInfo.redRushApply.blueInvoiceFile)">点击下载</span>
                            <span v-else>--</span>
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions :column="1" border size="small" title="红字发票信息" style="width: 50%;flex-shrink: 0;">
                        <el-descriptions-item label="发票号码">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.redInvoiceNo || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="发票客户">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.invoiceCustomer || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="开票日期">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.redInvoiceDate || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="开票金额">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.redInvoiceAmount || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="发票代码">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.redInvoiceCode || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="纳税人识别号">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.taxNo || '--'}}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="发票类型">
                            <span class="whitespace-nowrap">{{ taskInfo?.redRushApply?.invoiceType?selectDictLabel(invoiceTypeOptions,taskInfo.redRushApply.invoiceType):'--' }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="附件">
                            <span v-if="taskInfo?.redRushApply?.redInvoiceFile" class="whitespace-nowrap" style="color:#2A76F8;cursor: pointer;" @click="handlePreview(taskInfo.redRushApply.redInvoiceFile)">点击下载</span>
                            <span v-else>--</span>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <el-descriptions v-if="taskInfo?.redRushApply?.businessType!== '1'" class="mt-10" size="small" :title="taskInfo?.redRushApply?.businessType === '2' ?'付款单明细' : '收款单明细'"></el-descriptions>
                <column-table v-if="taskInfo?.redRushApply?.businessType!== '1'" :columns="redPunchApprovalColumns " :data="taskInfo?.redRushApply?.docList|| []" :show-summary="true">
                    <template #companyName="{ row }">
                        {{ setCompanyName(row) }}
                    </template>
                    <template #startDate="{ row }"> {{ row.startDate }} ~ {{ row.endDate }} </template>
                    <template #discountType="{ row }">
                        {{ selectDictLabel(discountTypeList, row.discountType) }}
                    </template>
                    <template #paymentDocType="{ row }">
                        {{ selectDictLabel(paymentDocTypeList, row.paymentDocType) }}
                    </template>
                </column-table>
                <div>
                    <el-form-item label="合计开票金额:" prop="redInvoiceAmount" style="margin-bottom: 0px;">
                        <span style="color: #ff2a2a">{{redPunchApprovalForm.redInvoiceAmount}}</span>
                    </el-form-item>
                    <el-form-item label="详细说明:" prop="detailDesc" style="margin-bottom: 0px;">
                        <el-input v-model="redPunchApprovalForm.detailDesc" :disabled="true" class="w-full" clearable maxlength="100" placeholder="请输入详细说明" show-word-limit type="textarea" />
                    </el-form-item>
                </div>
                <div class="grid items-start" style="grid-template-columns: minmax(0, 1fr) minmax(0, 1fr); padding-bottom: 10px">
                    <div class="flex flex-col">
                        <div v-if="auditInfo.auditStatus === '0' && type === 'examine'">
                            <el-form-item :rules="[{ required: true, message: '请选择审批意见', trigger: 'change' }]" label="审批意见" prop="status">
                                <el-radio-group v-model="redPunchApprovalForm.status">
                                    <el-radio label="1">通过</el-radio>
                                    <el-radio label="2">驳回</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item v-if="redPunchApprovalForm.status === '2'" :rules="[{ required: true, message: '请输入驳回原因', trigger: 'blur' }]" label="驳回原因" prop="auditIdea">
                                <el-input v-model="redPunchApprovalForm.auditIdea" :rows="4" maxlength="500" placeholder="请输入驳回原因" show-word-limit type="textarea"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </el-form>
            <template v-if="auditInfo.auditStatus == '0'" #footer>
                <div v-loading="redPunchApprovalLoading" style="text-align: center">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm()">提 交</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import AuditAndFlowRecords from '@/views/auditManagement/AuditAndFlowRecords.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import { tasks } from '@/api/auditManagement/approvalTask';

export default {
    name: 'RedPunchApproval' ,
    components: {  ColumnTable, AuditAndFlowRecords },
    props: {
        auditInfo: {},
        auditTitle: {
            type: String,
            default: undefined
        },
        redPunchApprovalVisible: {
            type: Boolean,
            default: false
        },
        taskInfo: {},
        type: {
            type: String,
            default: 'details'
        }
    },
    data() {
        return{
            visible: this.redPunchApprovalVisible,
            viewerVisible: false,
            redPunchApprovalForm: {
                status: undefined,
                auditIdea: undefined
            },
            redPunchApprovalLoading: false,
            redPunchApprovalColumns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'paymentDocType', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'billDate', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '纸箱费用', key: 'cartonFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '垫付费用', key: 'advanceFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '租箱费用', key: 'rentalBoxFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '其他费用', key: 'otherFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '费用说明', key: 'feeDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单未收合计', key: 'unpaidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '坏账总金额', key: 'badDebtCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '操作员', key: 'operator', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '调整日期', key: 'adjustTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true }
            ],
            discountTypeList: [],
            paymentDocTypeList: [], // 付款方式
            auditStatusOptions:[],
            invoiceTypeOptions:[], // 发票类型
            reasonList:[], // 红冲原因
            redInvoiceFlagList:[], // 开票状态
        }

    },
    computed: {
        /**
         * 设置公司名称
         */
        setCompanyName() {
            return (row) => {
                const Organization = this.$TOOL.data.get('Organization');
                const orgKey = this.$TOOL.data.get('orgKey');
                const { carrierId, companyId } = row;
                if (carrierId == Organization[orgKey].id) {
                    return row.companyName;
                } else if (companyId == Organization[orgKey].id) {
                    return row.carrierName;
                }
            };
        }
    },
    created() {
        this.redPunchApprovalForm.redInvoiceAmount = this.taskInfo?.redRushApply?.redInvoiceAmount;
        this.redPunchApprovalForm.detailDesc = this.taskInfo?.redRushApply?.detailDesc;
        this.getDict();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            this.discountTypeList = await this.getDictList('discount_type'); // 折扣方式
            this.paymentDocTypeList = await this.getDictList('cost_payment_doc_type'); //结算-收款单类型
            /*审批任务状态*/
            this.auditStatusOptions = await this.getDictList('audit_status');
            // 发票类型
            this.invoiceTypeOptions = await this.getDictList('collaborating_shipper_invoice_type');
            // 红冲原因
            this.reasonList = await this.getDictList('red_reason');
            // 开票状态
            this.redInvoiceFlagList = await this.getDictList('red_invoice_result');

        },
        /**
         * 下载
         * @param data
         */
        handlePreview(fileURL) {
            if (fileURL) {
                window.open(fileURL);
            } else {
                this.$message.error('文件地址不存在');
            }
        },

        /**
         * 关闭弹窗
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:redPunchApprovalVisible', false);
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.redPunchApprovalForm.validate(async (valid) => {
                if (valid) {
                    const params = {
                        taskId: this.auditInfo.id,
                        auditIdea: this.redPunchApprovalForm.auditIdea,
                        status: this.redPunchApprovalForm.status
                    };
                    tasks
                        .auditSubmit(params)
                        .then((res) => {
                            if (res.code == 200) {
                                this.$message.success('操作成功');
                                this.$emit('success');
                                this.closeVisible();
                            }
                        })
                        .finally(() => {
                            this.preDepositTopUpAuditLoading = false;
                        });
                }
            });
        }
    }
};
</script>



<style lang="scss" scoped>

</style>
