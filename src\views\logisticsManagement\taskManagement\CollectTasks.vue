<!--揽收服务-->
<template>
    <div class="app-container customer-auto-height-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="订单号" prop="orderNo" style="width: 240px">
                    <el-input v-model="queryForm.orderNo" clearable placeholder="请输入订单号" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单号" prop="transOrderNo">
                    <el-input v-model="queryForm.transOrderNo" clearable placeholder="请输入运单号" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="货主公司" prop="companyId" style="width: 240px">
                    <el-select v-model="queryForm.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">
                        <el-option v-for="(dict, index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单状态" prop="orderStatus">
                    <el-select v-model="queryForm.orderStatus" clearable placeholder="请选择订单状态" @change="handleQuery">
                        <el-option v-for="dict in statusDicts" :key="dict.code" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="任务状态" prop="status">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择订单状态" @change="handleQuery">
                        <el-option v-for="dict in fourplLanTaskStatusDict" :key="dict.code" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件人" prop="sendUser">
                    <el-input v-model="queryForm.sendUser" clearable placeholder="请输入发件人" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件公司" prop="sendCompany">
                    <el-input v-model="queryForm.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发货地址" prop="sendAddress">
                    <el-cascader v-model="queryForm.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择发货地址" @change="handleQuery" @visible-change="visibleChange" />
                </el-form-item>
                <el-form-item label="创建时间" prop="queryTime" style="width: 310px">
                    <el-date-picker v-model="queryForm.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div class="mb10">
                <el-button icon="el-icon-plus" type="primary" @click="jumpToTheOrderPage">下单</el-button>
                <el-button :disabled="multiple" type="warning" @click="batchChangeDriverShow">批量改派</el-button>
                <el-button :disabled="multiple" type="danger" @click="batchForcedCompletion">批量强制完结</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="taskList" @queryTable="getList"></right-toolbar>
            </div>
            <column-table
                ref="taskList"
                v-loading="loading"
                :columns="columns"
                :data="taskList"
                :defaultSort="{ prop: 'createDate', order: 'descending' }"
                :show-check-box="true"
                class="customer-auto-height-table"
                element-loading-text="加载中..."
                max-height="null"
                @selection-change="handleSelectionChange"
            >
                <template #productType="{ row }">
                    <span>{{ formatDictionaryData('productTypeDict', row.productType) }}</span>
                </template>
                <template #productClass="{ row }">
                    <span>{{ formatDictionaryData('productClassDict', row.productClass) }}</span>
                </template>
                <template #orderStatus="{ row }">
                    <span>{{ formatDictionaryData('statusDicts', row.orderStatus) }}</span>
                </template>
                <template #status="{ row }">
                    <span>{{ formatDictionaryData('fourplLanTaskStatusDict', row.status) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="!row.driverCode" icon="el-icon-plus" link size="small" type="success" @click="dispatch(row)">派单</el-button>
                    <el-button v-if="row.status === '1' || row.status === '2'" icon="el-icon-edit" link size="small" type="warning" @click="showChangeDriver(row)">改派</el-button>
                    <el-button v-if="(row.orderStatus === '4' || row.orderStatus === '5') && row.status !== '5'" icon="el-icon-close" link size="small" type="danger" @click="handleCompulsoryCompletion(row)">强制完结</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <driver-select v-if="driverOpen" :driverData="driverData" :type="'flow_driver'" :values="values" show @changeShow="changeShow" @onConfirm="binding"></driver-select>

        <!-- 改派 批量改派-->
        <el-drawer v-model="changeDriverOpen" :size="batchChangeDriverOpen ? '850px' : '500px'" :title="batchChangeDriverOpen ? '批量改派' : '改派'" @close="handleCloseDriver">
            <div style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="formChangeDriver" :model="formChangeDriver" :rules="rulesChangeDriver" label-width="auto">
                        <div v-if="batchChangeDriverOpen">
                            <el-table :data="selectionRows" border height="250" style="width: 100%; margin-bottom: 20px">
                                <el-table-column align="center" label="订单号" prop="orderNo"></el-table-column>
                                <el-table-column align="center" label="发件公司" prop="sendCompany"></el-table-column>
                                <el-table-column align="center" label="货物数量" prop="goodsPackages"></el-table-column>
                                <el-table-column align="center" label="当前司机" prop="driverName"></el-table-column>
                            </el-table>
                        </div>
                        <div v-else>
                            <el-form-item label="订单号" prop="orderNo">
                                <el-input v-model="formChangeDriver.orderNo" disabled placeholder="" />
                            </el-form-item>
                            <el-form-item label="发件公司" prop="sendCompany">
                                <el-input v-model="formChangeDriver.sendCompany" disabled placeholder="" />
                            </el-form-item>
                            <el-form-item label="货物数量" prop="goodsPackages">
                                <el-input v-model="formChangeDriver.goodsPackages" disabled placeholder="" />
                            </el-form-item>
                            <el-form-item v-if="formChangeDriver.transOrderNo" label="运单号" prop="transOrderNo">
                                <el-input v-model="formChangeDriver.transOrderNo" disabled placeholder="请输入运单号" />
                            </el-form-item>
                        </div>

                        <el-form-item label="转给司机" prop="driver">
                            <el-select v-model="formChangeDriver.driver" clearable filterable placeholder="请选择司机" value-key="driverCode">
                                <el-option v-for="item in driverList" :key="item.driverCode" :label="item.driverName" :value="item" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="转单原因" prop="reason">
                            <el-input v-model="formChangeDriver.reason" maxlength="255" placeholder="请输入转单原因" rows="5" show-word-limit type="textarea" />
                        </el-form-item>
                        <div class="dialog-footer" style="text-align: center">
                            <el-button v-if="batchChangeDriverOpen" type="primary" :loading="batchChangeDriverLoading" @click="batchChangeDriverHandle">保存</el-button>
                            <el-button v-else type="primary" :loading="changeDriverLoading" @click="changeDriver">保存</el-button>
                            <el-button @click="handleCloseDriver">取消</el-button>
                        </div>
                    </el-form>
                </el-card>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import collectTasks from '@/api/carrierEnd/collectTasks';
import { selectDictLabel } from '@/utils/dictLabel';
import DriverSelect from '@/components/driverSelect/index.vue';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import moment from 'moment';

export default {
    name: 'CollectTasks',
    components: {
        DriverSelect,
        RightToolbar,
        SearchButton,
        ColumnTable
    },
    data() {
        return {
            loading: false,
            multiple: true,
            showSearch: true,
            total: 0,
            taskList: [],
            // 查询参数
            queryForm: {
                current: 1,
                size: 10,
                orderNo: undefined,
                companyId: undefined,
                orderStatus: undefined,
                status: undefined,
                productClass: undefined,
                transOrderNo: undefined,
                sendUser: undefined,
                sendCompany: undefined,
                sendAddress: [],
                queryTime: []
            },
            columns: [
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '180px', columnShow: true },
                { title: '订单号', key: 'orderNo', align: 'center', width: '180px', fixed: 'left', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '发件人', key: 'sendUser', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '发件人电话', key: 'sendUserPhone', align: 'center', width: '120px', columnShow: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件人', key: 'receiverUser', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件人电话', key: 'receiverUserPhone', align: 'center', width: '120px', columnShow: true },
                { title: '揽收司机', key: 'driverName', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '订单件数', key: 'goodsPackages', align: 'center', width: '80px', columnShow: true },
                { title: '运输类型', key: 'productType', align: 'center', width: '120px', columnShow: true },
                { title: '产品分类', key: 'productClass', align: 'center', width: '120px', columnShow: true },
                { title: '温层类型', key: 'temperatureDesc', align: 'center', width: '200px', columnShow: true },
                { title: '任务超时', key: 'timeOut', align: 'center', width: '180px', columnShow: true },
                { title: '上门取件时间', key: 'appointmentTime', align: 'center', width: '130px', columnShow: true },
                { title: '是否转单', key: 'isTransfer', align: 'center', width: '100px', columnShow: true },
                { title: '创建时间', key: 'createDate', align: 'center', width: '130px', columnShow: true, sortable: true },
                { title: '订单状态', key: 'orderStatus', align: 'center', width: '80px', columnShow: true, fixed: 'right' },
                { title: '任务状态', key: 'status', align: 'center', width: '80px', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '180px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            fourplLanTaskStatusDict: [],
            driverOpen: false,
            values: {},
            driverData: [],
            orderInfo: {},
            coldChainProductData: [
                {
                    name: '',
                    specifications: '',
                    manufacturer: '',
                    batchNumber: '',
                    quantity: 1,
                    completeInformation: '1'
                }
            ],
            drawer: false,
            // 表单参数
            form: {
                orderSource: 1, //订单来源
                orderType: '1', //取件方式
                pickupAddress: '', //取件地址
                shippingAddress: '', //收件地址
                sendAddress: '', //取件地址 详细地址
                receiverAddress: '', //收件地址 详细地址
                sendUser: '', //取件联系人
                receiverUser: '', //收件人名称
                sendUserPhone: '', //取件人电话
                receiverUserPhone: '', //收件人电话
                remark: '', //备注
                sendCompany: '', //发货人公司
                receiverCompany: '', //收货人公司
                productClass: '', //产品分类
                temperatureType: '', //温层类型
                goodsName: '', //货品名称
                vehicleSelection: '', //车型选择
                goodsPackages: '', //件数
                productProperties: '', //货品属性
                productWeight: 0, //重量
                externalOrderNo: '', //随货同行单号
                productVolume: 0, //体积
                paymentMethod: '', //付款方式
                deliveryTime: '', //送达日期
                appointmentTime: '', //取件时间
                addedServices: [], //附加服务
                agreeToTermsCarrier: [], //阅读并同意
                companyId: '', // 货主
                estimatedCost1: 0.0, // 预估费用
                costData: [], // 费用明细
                kilometre: 0 // 公里数
            },
            addOrderSwitch: false, //新增订单开关
            statusDicts: [], //状态字典
            productAttributeDictionary: [], // 商品属性 字典
            addedServiceDicts: [], // 增值服务
            sysAreas: [], //省市区数据
            belongUserId: '95', //订单所属人ID（租户/货主ID）
            createUserId: '5', //下单人ID
            pickupAddressDefaultAddress: '0', //取件地址默认地址
            shippingAddressDefaultAddress: '0', //收件地址默认地址
            addServiceList: [], //新增订单增值服务
            // 取件地址
            sendAddressParams: {
                total: 0,
                type: 1,
                page: 1,
                size: 10,
                searchValue: ''
            },
            receiverAddressBook: [],
            sendAddressBook: [],
            // 收件地址
            receiverAddressParams: {
                total: 0,
                type: 2,
                page: 1,
                size: 10,
                searchValue: ''
            },
            orderSliderTitle: '', //订单详情弹窗标题
            productClassDicts: [], // 产品类型 字典
            temperatureTypeDicts: [], //温层类型字典
            typeOptions: [],
            orderDetailCheck: false, //冷链详情开关
            companyList: [], // 收件公司
            changeDriverOpen: false, // 改派
            formChangeDriver: {},
            // 表单校验
            rulesChangeDriver: {
                driver: [{ required: true, message: '转给司机不能为空' }]
            },
            driverList: [], // 司机列表
            quicklyPlaceOrderConfirmDisabled: false, //快速下单确认按钮禁用
            infoOpen: false, //订单详情弹窗开关
            collectStatus: null, //揽收状态
            selectionRows: [], //多选
            waybillList: [],
            fourplHandWayOptions: [], // 交接方式
            fourplPaymentMethodOptions: [], // 付款方式
            ownerList: [], // 货主列表
            fourplProductClassDicts: [], // 产品分类
            costCreakdownShow: false, // 费用明细弹窗
            orderBranchData: {}, // 获取网点等信息
            costDataList: [], // 结算费用展示
            addOrderCostSwitch: false, // 确认订单是否触发费用计算
            addedServiceShow: false, // 是否显示增值服务弹窗
            formService: {}, // 增值服务修改项
            serviceRules: {
                inputValue: [
                    {
                        required: true,
                        message: '请输入值',
                        trigger: 'blur'
                    }
                ]
            },
            formula: null, //计算公式
            batchChangeDriverOpen: false, // 批量改派弹窗
            batchChangeDriverLoading: false, // 批量改派保存按钮loading状态
            changeDriverLoading: false, // 改派保存按钮loading状态
            ownerAccountInfo: {}, // 货主账户信息
            ownerPaymentMethodOptions: [],
            productClassDict: [],
            productTypeDict: [],
            isShowAll: false,
            shortcuts: [
                {
                    text: '无',
                    value: () => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: () => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ]
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    watch: {
        'form.companyId'() {
            this.getCostCalculation();
        },
        'form.goodsPackages'() {
            this.getCostCalculation();
        },
        'form.kilometre'() {
            this.getCostCalculation();
        },
        'form.orderType'() {
            this.getCostCalculation();
        },
        'form.paymentMethod'() {
            this.getCostCalculation();
        },
        'form.pickupAddress'(newVal, oldVal) {
            if (newVal.length > 1 && JSON.stringify(newVal) != JSON.stringify(oldVal)) {
                this.getOrderCost();
            }
        },
        'form.productClass'() {
            this.getCostCalculation();
        },
        'form.productType'() {
            this.getCostCalculation();
        },
        'form.productVolume'() {
            this.getCostCalculation();
        },
        'form.productWeight'() {
            this.getCostCalculation();
        },
        'form.shippingAddress'(newVal, oldVal) {
            if (newVal.length > 1 && JSON.stringify(newVal) != JSON.stringify(oldVal)) {
                this.getOrderCost();
            }
        },
        'form.temperatureType'() {
            this.getCostCalculation();
        },
        'form.vehicleSelection'() {
            this.getCostCalculation();
        },
        'receiverAddressParams.searchValue'() {
            this.getReceiverAddressBooks(true);
        },
        'sendAddressParams.searchValue'() {
            this.getSendAddressBooks(true);
        }
    },
    created() {
        this.getDict();
        // 将默认设置调整为当天
        this.queryForm.beginCreateDate = moment().format('YYYY-MM-DD');
        this.queryForm.endCreateDate = moment().format('YYYY-MM-DD');
        this.queryForm.queryTime = [this.queryForm.beginCreateDate, this.queryForm.endCreateDate];
        // 获取货主公司
        this.getCompanySelect();
        this.handleQuery();
    },
    methods: {
        /**
         * 批量改派
         */
        batchChangeDriverHandle() {
            // 防止重复提交
            if (this.batchChangeDriverLoading) {
                return;
            }

            this.$refs.formChangeDriver.validate(async (result) => {
                if (result) {
                    this.batchChangeDriverLoading = true;
                    const { driver, reason } = this.formChangeDriver;
                    const { driverCode: newDriverCode, driverName: newDriverName, carBindId } = driver;
                    const taskCodeList = this.selectionRows.map((item) => item.taskCode);
                    try {
                        const transferResult = await collectTasks.transferLanTask({
                            taskCodeList,
                            newDriverCode,
                            newDriverName,
                            carBindId,
                            reason
                        });
                        if (transferResult.code === 200) {
                            this.msgSuccess('改派成功');
                            this.handleCloseDriver();
                            this.getList();
                        }
                    } catch (error) {
                        this.msgError('改派过程中出现错误，请稍后重试');
                    } finally {
                        this.batchChangeDriverLoading = false;
                    }
                }
            });
        },
        /**
         * 显示批量改派弹窗
         * @returns {boolean}
         */
        batchChangeDriverShow() {
            this.selectionRows.forEach((item) => {
                // item status 为 0 和 1 时才能调整 其他情况 不选中 toggleRowSelection 只有第一次提示 之后不提示
                if (item.status !== '1' && item.status !== '2') {
                    this.$refs.taskList.$refs.ColumnTable.toggleRowSelection(item, false);
                }
            });
            if (this.selectionRows.length === 0) {
                this.msgError('【待揽收】和【已揽收】运单才能改派');
                return false;
            }
            this.resetForm('formChangeDriver');
            this.formChangeDriver = {
                newDriverCode: '',
                reason: ''
            };
            collectTasks
                .getDriverList()
                .then((res) => {
                    if (res.code === 200) {
                        if (res.data.records?.length) {
                            this.driverList = res.data.records;
                            this.changeDriverOpen = true;
                            this.batchChangeDriverOpen = true;
                        } else {
                            this.msgError('暂无司机可改派');
                        }
                    }
                })
                .catch(() => {});
        },
        /**
         * 批量强制完结
         */
        batchForcedCompletion() {
            this.selectionRows.forEach((item) => {
                // item (row.orderStatus === '4' || row.orderStatus === '5') && row.status !== '5' 时才能调整
                // 其他情况 不选中 toggleRowSelection 只有第一次提示 之后不提示
                if ((item.orderStatus !== '4' && item.orderStatus !== '5') || item.status === '5') {
                    this.$refs.taskList.$refs.ColumnTable.toggleRowSelection(item, false);
                }
            });
            if (this.selectionRows.length === 0) {
                this.msgError('【已签收】和【已取消】运单才能强制完结');
                return false;
            }
            this.$confirm('是否确认强制完结该任务？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    collectTasks
                        .forceEndLanTask(this.selectionRows.map((item) => item.id))
                        .then((res) => {
                            if (res.code === 200) {
                                this.msgSuccess('强制完结成功');
                                this.handleQuery();
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {});
        },
        /**
         * 派单
         * @param {Object} data - 派单数据
         */
        binding(data) {
            const { taskCode } = data.data;
            collectTasks
                .updateLanTask({
                    taskCode,
                    driverCode: data.driverList[0].driverCode,
                    driverName: data.driverList[0].driverName
                })
                .then((res) => {
                    if (res.code === 200) {
                        this.msgSuccess('派单成功');
                        this.values.open = false;
                        this.driverOpen = false;
                        this.handleQuery();
                    }
                })
                .catch(() => {});
        },
        /**
         * 改派
         */
        changeDriver() {
            // 防止重复提交
            if (this.changeDriverLoading) {
                return;
            }

            this.$refs.formChangeDriver.validate(async (valid) => {
                if (valid) {
                    this.changeDriverLoading = true;
                    const { driver, reason } = this.formChangeDriver;
                    const { driverCode: newDriverCode, driverName: newDriverName, carBindId } = driver;
                    const taskCodeList = this.formChangeDriver.taskCode.split(',');

                    try {
                        const transferResult = await collectTasks.transferLanTask({
                            taskCodeList,
                            newDriverCode,
                            newDriverName,
                            carBindId,
                            reason
                        });

                        if (transferResult.code === 200) {
                            this.msgSuccess('改派成功');
                            this.handleCloseDriver();
                            this.getList();
                        }
                    } catch (error) {
                        this.msgError('改派过程中出现错误，请稍后重试');
                    } finally {
                        this.changeDriverLoading = false;
                    }
                } else {
                    this.$message.error('表单验证未通过，请检查后重试');
                }
            });
        },
        changeShow(value) {
            this.values.open = value;
            this.driverOpen = value;
        },
        /**
         * 派单
         * @param row
         */
        dispatch(row) {
            // 解构赋值
            const { taskCode, driverCode, driverName } = row;
            // 如果司机姓名和司机编码都存在
            if (driverName && driverCode) {
                // 将司机姓名和司机编码存入driverData数组中
                this.driverData = [{ driverName, driverCode }];
            }
            // 清空driverData数组
            this.driverData = [];
            // 设置values对象的属性
            this.values = {
                minNum: 1, // 最少绑定一个
                maxNum: 1,
                title: '派单',
                open: true,
                data: { ...row, productClass: row.productClass }, // 复制row对象并更新productClass属性
                type: 2,
                taskCode
            };
            // 打开driverOpen组件
            this.driverOpen = true;
        },
        /**
         * 获取货主公司下拉
         */
        getCompanySelect() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.fourplLanTaskStatusDict = await this.getDictList('fourpl_lan_task_status');
            this.productTypeDict = await this.getDictList('fourpl_product_type');
            this.productClassDict = await this.getDictList('fourpl_product_class');
            this.statusDicts = await this.getDictList('fourpl_order_status');
        },
        /**
         * 获取订单列表
         */
        getList() {
            this.loading = true;
            this.taskList = [];
            const { sendAddress, queryTime, ...params } = this.queryForm;
            collectTasks
                .listLanTask(params)
                .then((response) => {
                    this.taskList = response.data.records || [];
                    this.taskList.forEach((o) => (o.isTransfer = o.upTaskCode ? '是' : '否'));
                    this.total = response.data.total;
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 关闭派单弹窗
         */
        handleCloseDriver() {
            this.changeDriverOpen = false;
            this.batchChangeDriverOpen = false;
            // 重置loading状态
            this.batchChangeDriverLoading = false;
            this.changeDriverLoading = false;
        },
        /**
         * 强制完结
         * @param row
         */
        handleCompulsoryCompletion(row) {
            const { id } = row;
            if (id) {
                // 确认框提示
                this.$confirm('是否确认强制完结该任务？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        collectTasks
                            .forceEndLanTask([id])
                            .then((res) => {
                                if (res.code === 200) {
                                    this.msgSuccess('强制完结成功');
                                    this.handleQuery();
                                }
                            })
                            .catch(() => {});
                    })
                    .catch(() => {});
            }
        },
        /**
         * 搜索按钮操作
         */
        handleQuery() {
            this.queryForm.current = 1;

            const { sendAddress, queryTime } = this.queryForm;

            const isValidDateRange = queryTime && queryTime.length === 2 && !queryTime.some((date) => date === 'Invalid Date');
            if (isValidDateRange) {
                this.queryForm.beginCreateDate = queryTime[0] + ' 00:00:00';
                this.queryForm.endCreateDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryForm.beginCreateDate = null;
                this.queryForm.endCreateDate = null;
            }

            if (sendAddress) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = sendAddress;
                this.queryForm.sendProvinceId = sendProvinceId || null;
                this.queryForm.sendCityId = sendCityId || null;
                this.queryForm.sendCountyId = sendCountyId || null;
                this.queryForm.sendTownId = sendTownId || null;
            } else {
                this.queryForm.sendProvinceId = null;
                this.queryForm.sendCityId = null;
                this.queryForm.sendCountyId = null;
                this.queryForm.sendTownId = null;
            }

            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.selectionRows = selection;
            this.multiple = !selection.length;
        },
        /**
         * 跳转到下单页面
         */
        jumpToTheOrderPage() {
            this.$router.push({
                path: '/orderManagement/PlaceAnOrder'
            });
        },
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
        },
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /**
         * 展开或者合上
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 单个改派
         * @param row
         */
        showChangeDriver(row) {
            this.orderInfo = row;
            this.resetForm('formChangeDriver');
            this.formChangeDriver = { ...row, newDriverCode: '', reason: '' };
            collectTasks
                .getDriverList()
                .then((res) => {
                    if (res.code === 200) {
                        if (res.data.records?.length) {
                            this.driverList = res.data.records;
                            this.changeDriverOpen = true;
                        } else {
                            this.msgError('暂无司机可改派');
                        }
                    }
                })
                .catch(() => {});
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-input.is-disabled .el-input__inner) {
    color: #333 !important;
    -webkit-text-fill-color: #333 !important;
}

:deep(.el-drawer__header) {
    .el-drawer__header {
        margin-bottom: 20px;
    }
}
</style>
