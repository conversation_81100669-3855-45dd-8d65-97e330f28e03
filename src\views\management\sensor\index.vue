<!--  1. 传感器管理 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form">
                <el-form-item label="设备编号" prop="serialNumber" style="width: 250px">
                    <el-input v-model="queryParams.serialNumber" clearable placeholder="请输入设备编号" />
                </el-form-item>
                <el-form-item class="define" label="设备状态" prop="deviceStatus" style="width: 250px">
                    <el-select v-model="queryParams.deviceStatus" clearable>
                        <el-option v-for="item in sensorDeviceType" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作状态" placeholder="请选择操作状态" prop="operateStatus" style="width: 250px">
                    <el-select v-model="queryParams.operateStatus" clearable>
                        <el-option v-for="item in operateList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="设备厂家" prop="manufacturer">
                    <el-select v-model="queryParams.manufacturer" clearable>
                        <el-option v-for="item in manufacturerList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="getList" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="display: flex; justify-content: space-between; align-items: center">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" icon="el-icon-plus" type="primary" @click="handleAdd">添加设备</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['device:iceRaft:add']" :icon="Download" type="warning" @click="exportList">导出</el-button>
                    </el-col>
					<el-col :span="1.5">
						<el-button :icon="el-icon-bottom" type="success" @click="downloadTemplate">下载模板</el-button>
					</el-col>
					<el-col :span="1.5">
						<el-upload ref="uploadDeviceExcel"
								   :action="uploadFileUrl" :before-upload="beforeUploadExcel"
								    :headers="headers" :limit="1"
								   :on-success="fileUploadSuccess" :show-file-list="false"
								   :on-error="error" accept=".xls,.xlsx">
							<el-button icon="el-icon-upload" type="warning">上传</el-button>
						</el-upload>
					</el-col>

                </el-row>
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table :data="terminalData" border style="margin-top: 15px" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column :index="idxMethod" align="center" label="序号" type="index" width="50" show-overflow-tooltip/>
                <el-table-column align="center" label="设备编号" prop="serialNumber" width="120" show-overflow-tooltip/>
                <el-table-column align="center" label="设备名称" prop="name" width="200" show-overflow-tooltip/>
                <!-- <el-table-column label="设备厂家" align="center" prop="manufacturer" /> -->
                <el-table-column :formatter="(row) => formDict(manufacturerList, row.manufacturer)" align="center" label="设备厂家" prop="manufacturer" width="200" show-overflow-tooltip/>
                <el-table-column :formatter="(row) => formDict(sensorDeviceType, row.deviceStatus)" align="center" label="设备状态" prop="deviceStatus" width="100" show-overflow-tooltip/>
                <el-table-column :formatter="(row) => formDict(operateList, row.operateStatus)" align="center" label="操作状态" prop="operateStatus" width="100" show-overflow-tooltip/>
                <el-table-column align="center" label="物联网卡号" prop="simNumber" width="200" show-overflow-tooltip/>
                <el-table-column align="center" label="高温阀值℃" prop="highTempThreshold" width="100" show-overflow-tooltip/>
                <el-table-column align="center" label="低温阀值℃" prop="lowTempThreshold" width="100" show-overflow-tooltip/>
                <el-table-column align="center" label="备注" prop="remark" show-overflow-tooltip/>
                <el-table-column :formatter="(row) => formDict(validityList, row.validityStatus)" align="center" label="效期状态" prop="simNumber" width="100" show-overflow-tooltip/>
                <el-table-column align="center" label="效期时间" prop="deviceTerminalCalibration" width="120" show-overflow-tooltip>
                    <template #default="scope">
                        <span v-if="scope.row.deviceTerminalCalibration">
                            {{ scope.row.deviceTerminalCalibration.calibrationEndTime ? moment(scope.row.deviceTerminalCalibration.calibrationEndTime).format('YYYY-MM-DD') : '未校准' }}
                        </span>
                        <span v-else></span>
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed="right" label="操作" width="280" show-overflow-tooltip>
                    <template #default="scope">
                        <el-button icon="el-icon-printer" link size="small" type="primary" @click="printBoxTag(scope.row)">打印标签</el-button>
                        <el-button icon="el-icon-edit" link size="small" type="success" @click="calibrationList(scope.row)">校准</el-button>
                        <el-button icon="el-icon-edit" link size="small" type="warning" @click="modifyingSensors(scope.row)">修改</el-button>
                        <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="details(scope.row)">详情</el-button>
                        <el-button v-if="scope.row.deviceStatus != 0" icon="el-icon-delete" link size="small" type="danger" @click="deleteList(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
        <!-- 添加 -->
        <el-drawer v-show="open" v-model="open" :title="title" append-to-body size="650" style="padding: 0 30px">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item class="define" label="设备厂家" prop="manufacturer">
                    <el-select v-model="form.manufacturer" style="width: 500px">
                        <el-option v-for="item in manufacturerList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item class="define" label="设备名称" prop="name">
                    <el-input v-model="form.name" maxlength="20" placeholder="请输入设备名称" />
                </el-form-item>
                <el-form-item class="define" label="设备状态" prop="deviceStatus">
                    <el-select v-model="form.deviceStatus" style="width: 500px">
                        <el-option v-for="item in sensorDeviceType" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item class="define" label="设备编号" prop="serialNumber">
                    <el-input v-model="form.serialNumber" maxlength="20" placeholder="请输入设备编号" οnkeyup="value=value.replace(/[^\d]/g,0)" />
                </el-form-item>
                <el-form-item class="define" label="操作状态" prop="operateStatus">
                    <el-select v-model="form.operateStatus" style="width: 500px">
                        <el-option v-for="item in operateList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item class="define" label="设备类型" prop="type">
                    <el-select v-model="form.type" style="width: 500px">
                        <el-option v-for="item in sensorTypeList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="设备型号" prop="model" class="define">
					<el-input v-model="form.model" placeholder="请输入设备编号" />
					</el-form-item> -->
                <el-form-item class="define" label="物联网卡号" prop="simNumber">
                    <el-input v-model="form.simNumber" placeholder="请输入物联网卡号" />
                </el-form-item>
                <el-form-item class="define" label="高温阀值" prop="highTempThreshold">
                    <el-input-number v-model="form.highTempThreshold" :precision="1" :step="0.1" controls-position="right" style="width: 500px" />
                </el-form-item>
                <el-form-item class="define" label="低温阀值" prop="lowTempThreshold">
                    <el-input-number v-model="form.lowTempThreshold" :precision="1" :step="0.1" controls-position="right" style="width: 500px" />
                </el-form-item>
                <el-form-item class="define" label="备注" prop="remark">
                    <el-input v-model="form.remark" :autosize="{ minRows: 5, maxRows: 5 }" maxlength="60" placeholder="请输入备注" type="textarea" />
                </el-form-item>
            </el-form>
            <div class="drawer-footer">
                <el-button @click="cancellation">取 消</el-button>
				<el-button type="primary" @click="submitForm">确 定</el-button>
			</div>
        </el-drawer>
        <!-- 详情 -->
        <el-drawer v-show="openDetails" v-model="openDetails" :title="title" append-to-body size="1000" style="padding: 0 30px">
            <el-descriptions :column="3" border>
                <el-descriptions-item label="设备编号">{{ terminalQueryByIdList.serialNumber }}</el-descriptions-item>
                <el-descriptions-item label="经纬度">-</el-descriptions-item>
                <el-descriptions-item label="更新时间">-</el-descriptions-item>
                <el-descriptions-item label="地址">-</el-descriptions-item>
            </el-descriptions>
            <div class="deviceata">
                <div style="display: flex">
                    <!-- <el-tag effect="plain">启用</el-tag> -->
                    <el-tag class="ml-2" effect="plain" style="margin-left: 20px" type="warning">{{ formDict(sensorDeviceType, terminalQueryByIdList.deviceStatus) }}</el-tag>
                    <div style="margin-left: 20px; display: flex">
                        <el-icon color="#666666" size="25">
                            <Edit />
                        </el-icon>
                        <router-link :to="{ path: '/printRecord', query: { deviceNo: terminalQueryByIdList.serialNumber } }">
                            <div>
                                <span style="font-size: 16px; color: #0079fe; line-height: 25px">历史数据</span>
                            </div>
                        </router-link>
                    </div>
                </div>
                <div style="margin-top: 10px; font-size: 14px; color: #666666">绑定资产:<sapn style="margin-left: 20px; color: #333333">-</sapn></div>
                <div style="margin-top: 10px; display: flex; font-size: 14px">
                    <div>
                        <div style="color: #333333; font-weight: 400; text-align: center">-°C</div>
                        <div style="text-align: center; margin-top: 12px; display: flex; align-items: center">
                            <img alt="" src="../../../assets/images/wendu.png" />
                            <span style="color: #666666; margin-left: 6px">温度</span>
                        </div>
                    </div>
                    <div style="margin-left: 64px">
                        <div style="color: #333333; font-weight: 400; text-align: center">-rh%</div>
                        <div style="text-align: center; margin-top: 12px; display: flex; align-items: center">
                            <img alt="" src="../../../assets/images/shidu.png" />
                            <span style="color: #666666; margin-left: 6px">湿度</span>
                        </div>
                    </div>
                    <div style="margin-left: 64px">
                        <div style="color: #333333; font-weight: 400; text-align: center">{{ terminalQueryByIdList.ep }}%</div>
                        <div style="text-align: center; margin-top: 12px; display: flex; align-items: center">
                            <img alt="" src="../../../assets/images/dianya.png" />
                            <div style="color: #666666; margin-left: 6px">电量</div>
                        </div>
                    </div>
                </div>
                <!-- <el-collapse accordion>
					<el-collapse-item name="1">
						<template #title>
							<div style="border-left: 3px solid #4D83FF;width: 0px;height: 16px;"></div>
							<span style="color: #4D83FF;font-size: 16px;margin-left: 10px;">报警设置</span>
						</template>
						<el-table :data="tableData" border style="width: 100%" header-align="center">
							<el-table-column prop="date" label="高低温闻值报警条件" align="center" />
							<el-table-column prop="name" label="接收人员" align="center" />
							<el-table-column prop="address" label="添加人员" align="center" />
						</el-table>
					</el-collapse-item>
				</el-collapse> -->
            </div>
        </el-drawer>
        <!-- 校准 -->
        <el-drawer v-show="calibrationOpen" v-model="calibrationOpen" :title="title" append-to-body size="1200" style="padding: 0 30px">
            <el-card class="box-card Botm">
                <div style="display: flex; justify-content: space-between; align-items: center; font-size: 14px">
                    <div>
                        设备编号：<span>{{ sensorInformation.serialNumber }}</span>
                    </div>
                    <el-button type="primary" @click="verifyRecordList(sensorInformation)">添加校准</el-button>
                </div>
            </el-card>
            <div style="font-size: 14px; margin-top: 10px">
                <span>校准记录</span>
                <el-table :data="calData" border style="width: 100%">
                    <el-table-column label="校准起始时间" prop="calibrationStartTime" />
                    <el-table-column label="校准到期时间" prop="calibrationEndTime" />
                    <el-table-column label="校准温度" prop="calibrationTemp" width="80" />
                    <el-table-column label="操作人" prop="operateBy" />
                    <el-table-column label="校准日期" prop="calibrationTime" />
                    <el-table-column label="校准方" prop="calibrationBy" />
                    <el-table-column label="校准方负责人" prop="calibrationByHead" />
                    <el-table-column label="校准方电话" prop="calibrationByPhone" />
                    <el-table-column label="备注" prop="remark" />
                    <el-table-column align="center" label="操作" width="50">
                        <template #default="scope">
                            <el-button link type="danger" @click="deleteCalibration(scope.row, sensorInformation)">删除 </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="drawer-footer1">
				<el-button @click="calibrationOpen = false">取 消</el-button>
                <!-- <el-button type="primary" @click="submitCalibration">确 定</el-button> -->
            </div>
        </el-drawer>
        <el-dialog v-model="verifyRecordOpen" :title="title" width="30%">
            <div style="text-align: center">
                <el-form ref="verifyForm" :model="verifyList" :rules="rulesing" label-width="120px">
                    <el-form-item label="校准起始时间" prop="calibrationStartTime">
                        <el-date-picker v-model="verifyList.calibrationStartTime" placeholder="请选择校准起始时间" style="width: 100%" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="校准到期时间" prop="calibrationEndTime">
                        <el-date-picker v-model="verifyList.calibrationEndTime" placeholder="请选择校准到期时间" style="width: 100%" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="校准温度" prop="calibrationTemp">
                        <el-input v-model="verifyList.calibrationTemp" placeholder="请输入校准温度" @input="changeInputVal('calibrationTemp')" />
                    </el-form-item>
                    <el-form-item label="校准日期" prop="calibrationTime">
                        <el-date-picker v-model="verifyList.calibrationTime" placeholder="请选择校准日期" style="width: 100%" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="操作人" prop="operateBy">
                        <el-input v-model="verifyList.operateBy" maxlength="20" placeholder="请输入操作人" />
                    </el-form-item>
                    <el-form-item label="校准方" prop="calibrationBy">
                        <el-input v-model="verifyList.calibrationBy" maxlength="20" placeholder="请输入校准方" />
                    </el-form-item>
                    <el-form-item label="校准方负责人" prop="calibrationByHead">
                        <el-input v-model="verifyList.calibrationByHead" maxlength="20" placeholder="请输入校准方负责人" />
                    </el-form-item>
                    <el-form-item label="校准方电话" prop="calibrationByPhone">
                        <el-input v-model="verifyList.calibrationByPhone" placeholder="请输入校准方电话" />
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="verifyList.remark" autosize maxlength="60" placeholder="请输入备注" type="textarea" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click=" verifyRecordOpen = false; verifyList = [];">取消</el-button>
					<el-button type="primary" @click="verifyRecordAdd">确定</el-button>
                </span>
            </template>
        </el-dialog>
		<el-dialog v-model="uploadOpen" title="数据导入" width="60%">
			<div style="text-align: center">
				<div v-if="promptSuccessMsg" style="color:green;" v-html="promptSuccessMsg">
				</div>
				<el-table v-if="uploadData.length>0" :data="uploadData" border style="margin-top: 15px">
					<el-table-column align="center" label="序号" type="index" width="50" />
					<el-table-column align="center" label="设备编号" prop="serialNumber" min-width="120" />
					<el-table-column align="center" label="设备名称" prop="name" min-width="200" />
					<el-table-column :formatter="(row) => formDict(manufacturerList, row.manufacturer)" align="center" label="设备厂家" prop="manufacturer" width="200" />
					<el-table-column :formatter="(row) => formDict(sensorDeviceType, row.deviceStatus)" align="center" label="设备状态" prop="deviceStatus" width="100" />
					<el-table-column :formatter="(row) => formDict(operateList, row.operateStatus)" align="center" label="操作状态" prop="operateStatus" width="100" />
					<el-table-column align="center" label="物联网卡号" prop="simNumber" min-width="100"/>
					<el-table-column align="center" label="高温阀值℃" prop="highTempThreshold" width="100" />
					<el-table-column align="center" label="低温阀值℃" prop="lowTempThreshold" width="100" />
					<el-table-column align="center" label="备注" prop="remark" />
				</el-table>
				<div v-if="errorData.length>0" style="color:red;" class="mt10">
					有{{errorData.length}}条数据校验失败(校验失败数据请重新上传)，失败原因如下：
				</div>
				<el-table v-if="errorData.length>0" :data="errorData" border style="margin-top: 15px">
					<el-table-column align="center" label="序号" type="index" width="50" />
					<el-table-column align="center" label="设备编号" prop="serialNumber" min-width="120" />
					<el-table-column align="center" label="错误信息" prop="errorReason" min-width="300" >
						<template #default="{row}">
							<span style="color:red; ">{{row.errorReason}}</span>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<template #footer>
                <span class="dialog-footer">
                    <el-button v-if="uploadData.length > 0" type="primary" @click="batchImportDevice">确定导入</el-button>
                    <el-button @click=" uploadOpen = false; uploadData = [];errorData=[];">
                        取消
                    </el-button>
                </span>
			</template>
		</el-dialog>
    </div>
</template>
<script setup>
import { verifyPhone } from '@/utils/verificate';
import { ref, reactive, getCurrentInstance } from 'vue';
import { Edit, Download } from '@element-plus/icons-vue';
import qs from 'qs';
import sensorApi from '@/api/management/sensor';
const { proxy } = getCurrentInstance();
import moment from 'moment';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import tool from "@/utils/tool";

// 显示搜索条件
const showSearch = ref(true);
// 查询参数
const queryParams = ref({
    current: 1,
    size: 10,
    total: 0
});
const open = ref(false);
const isShowAll = ref(false);
const title = ref('');
// 上传地址
const uploadFileUrl = ref(process.env.VUE_APP_API_GAENT+'/device/terminal/importDevice');
// token
const headers = ref({
	Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
	clientType: 'pc',
});
// 导入弹窗
const uploadOpen = ref(false);
// 上传数据
const uploadData = ref([]);
// 失败数据
const errorData = ref([]);

// 提示信息
const promptSuccessMsg = ref(null);
/** 新增按钮操作 */
function handleAdd() {
    // reset();
    open.value = true;
    title.value = '添加设备';
}
// 修改设备
function modifyingSensors(row) {
    open.value = true;
    title.value = '修改设备';
    form.value = row;
}

const form = ref({});
// 设备状态
const sensorDeviceType = ref();

const openDetails = ref(false);
const terminalQueryByIdList = ref([]);
// 详情
function details(row) {
    sensorApi
        .terminalQueryById({
            id: row.id
        })
        .then((res) => {
            if (res.code == 200) {
                terminalQueryByIdList.value = res.data;
                console.log(res);
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
    openDetails.value = true;
    title.value = '详情';
}
// 表单验证
const rules = reactive({
    manufacturer: [{ required: true, message: '请选择设备厂家', trigger: 'change' }],
    // name: [
    //     { required: true, message: '请输入设备名称', trigger: 'blur' },
    // ],
    deviceStatus: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
    serialNumber: [
        { required: true, message: '请输入设备编号', trigger: 'blur' },
        { validator: validateNumber, trigger: 'blur' }
    ],
    operateStatus: [{ required: true, message: '请选择操作状态', trigger: 'change' }],
    beginHighTempThreshold: [{ required: true, message: '请输入高温阀值', trigger: 'blur' }],
    endHighTempThreshold: [{ required: true, message: '请输入低温阀值', trigger: 'blur' }],
    type: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
    // model: [
    //     { required: true, message: '请输入设备型号', trigger: 'blur' },
    // ],
    highTempThreshold: [{ required: true, message: '请输入高温阀值', trigger: 'blur' }],
    lowTempThreshold: [{ required: true, message: '请输入低温阀值', trigger: 'blur' }],
    simNumber: [
        // { required: true, message: '请输入物联网卡号', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9]+$/, message: '不能输入特殊字符及空格', trigger: 'blur' }
    ]
});
const rulesing = reactive({
    calibrationStartTime: [{ required: true, message: '请选择校准起始时间', trigger: 'change' }],
    calibrationEndTime: [{ required: true, message: '请选择校准到期时间', trigger: 'change' }],
    calibrationTemp: [
        { required: true, message: '请输入校准温度', trigger: 'change' },
        { validator: verifyNumber, trigger: 'blur' }
    ],
    calibrationTime: [{ required: true, message: '请选择校准日期', trigger: 'change' }],
    operateBy: [{ required: true, message: '请输入操作人', trigger: 'change' }],
    calibrationBy: [{ required: true, message: '请输入校准方', trigger: 'change' }],
    calibrationByHead: [{ required: true, message: '请输入校准方负责人', trigger: 'change' }],
    calibrationByPhone: [
        { required: true, message: '请输入校准方电话', trigger: 'blur' },
        { validator: verifyPhone, trigger: 'blur' }
    ]
});
/**
 * 打印标签
 * @param row
 */
function printBoxTag(row) {
    proxy.pdfLabelView('********************************', row.serialNumber);
}
function changeInputVal(key) {
    let inputVal = this.verifyList[key];
    inputVal = inputVal
        .replace(/[^\d.]/g, '')
        .replace(/\.{2,}/g, '.')
        .replace(/^(0(0)+|\.)/g, '')
        .replace('.', '$#$')
        .replace(/\./g, '')
        .replace('$#$', '.')
        .replace(/^(-)*(\d+)\.((\d){1}).*$/, '$1$2.$3');
    //保留n位小数
    // .replace(/^(-)*(\d+)\.(\d{1,n}).*$/, '$1$2.$3')
    this.verifyList[key] = inputVal;
}
// 温度小数点后一位
function verifyNumber(rule, value, callback) {
	if (value === undefined || value === null || !value.length) {
		// 如果值不存在、为空或者长度为0，则可视为无效输入
		callback(new Error('请输入数字'));
	} else if (value[value.length - 1] === '.') {
		callback(new Error('请输入正确的格式（小数点后不允许有空格）'));
	} else {
		callback();
	}
}

// 时长验证
function validateNumber(rule, value, callback) {
    if (/^([0]|[1-9][0-9]*)$/.test(value) === false) {
        callback(new Error('请输入一个正确的数值'));
    } else {
        callback();
    }
}

function deleteCalibration(row, val) {
    proxy
        .$confirm('是否确认删除校准记录吗?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            sensorApi
                .terminalCalibrationDelete({
                    ids: row.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        calibrationList(val);
                        proxy.msgSuccess('删除成功！');
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        })
        .catch(() => {});
}

// 新增校验记录
function verifyRecordAdd() {
    proxy.$refs['verifyForm'].validate((valid) => {
        if (valid) {
            var data = {
                deviceTerminal: {
                    id: sensorInformation.value.id
                },
                calibrationStartTime: verifyList.value.calibrationStartTime,
                calibrationEndTime: verifyList.value.calibrationEndTime,
                calibrationTemp: verifyList.value.calibrationTemp,
                calibrationTime: verifyList.value.calibrationTime,
                calibrationBy: verifyList.value.calibrationBy,
                calibrationByHead: verifyList.value.calibrationByHead,
                calibrationByPhone: verifyList.value.calibrationByPhone,
                remark: verifyList.value.remark,
                operateBy: verifyList.value.operateBy
            };
            sensorApi
                .saveCheckList(data)
                .then((res) => {
                    if (res.code == 200) {
                        proxy.msgSuccess('保存成功');
                        verifyRecordOpen.value = false;
                        calibrationList(sensorInformation.value);
						verifyList.value = [];
                    }
                })
                .catch((err) => {
					console.log(err)
                    proxy.msgError(err.msg);
                });
        } else {
            proxy.msgError('请检查信息');
        }
    });
}

// 验证记录
const verifyRecordOpen = ref(false);
const verifyList = ref([]);
const verifyData = ref([]);
const calData = ref([]);
function verifyRecordList(val) {
    // verifyData.value = val;
    // sensorApi.queryByIdList({
    //     id: val.id
    // }).then(res => {
    //     if (res.code == 200) {
    //         proxy.msgSuccess('新增成功');
    //     }
    // }).catch((err) => {
    //     proxy.msgError(err.msg);
    // })
    title.value = '新增校验记录';
    verifyList.value.calibrationBy = '药监';
    verifyRecordOpen.value = true;
}

// 校准
const sensorInformation = ref([]);
const calibrationOpen = ref(false);
function calibrationList(val) {
    sensorInformation.value = val;
    title.value = '校准记录';
    calibrationOpen.value = true;
    sensorApi
        .terminalCalibrationList({
            'deviceTerminal.id': val.id
        })
        .then((res) => {
            if (res.code == 200) {
                calData.value = res.data.records;
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

// 取消保存
function cancellation() {
    open.value = false;
    form.value = {};
}

// 保存设备
function submitForm() {
    proxy.$refs['formRef'].validate((valid) => {
        if (valid) {
            sensorApi
                .terminalSave(form.value)
                .then((res) => {
                    if (res.code == 200) {
                        proxy.msgSuccess('新增成功');
                        open.value = false;
                        form.value = {};
                        getList();
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        }
    });
}

//查询展开收缩
function showAllClick() {
    isShowAll.value = !isShowAll.value;
}

// 淡出效果
function fadeOut(element) {
    let opacity = 1;
    let fadeEffect = setInterval(function () {
        if (opacity > 0) {
            opacity -= 0.1;
            element.style.opacity = opacity;
        } else {
            clearInterval(fadeEffect);
            element.style.display = 'none';
        }
    }, 10);
}

// 淡入效果
function fadeIn(element) {
    let opacity = 0;
    element.style.display = 'block';
    let fadeEffect = setInterval(function () {
        if (opacity < 1) {
            opacity += 0.1;
            element.style.opacity = opacity;
        } else {
            clearInterval(fadeEffect);
        }
    }, 15);
}

//删除
function deleteList(row) {
    proxy
        .$confirm('是否确认删除该条数据吗?', '提示', {
            type: 'warning',
            confirmButtonText: '确定',
            cancelButtonText: '取消'
        })
        .then(() => {
            sensorApi.terminalDelete({ ids: row.id }).then((res) => {
                if (res.code === 200) {
                    proxy.msgSuccess('删除成功');
                    getList();
                } else {
                    proxy.msgError('删除失败');
                }
            });
        })
        .catch(() => {});
}

const chooseList = ref([]);
const newFilArr = ref([]);
const handleSelectionChange = (key) => {
    chooseList.value = key;
};
// 导出
function exportList() {
    if (chooseList.value.length > 0) {
        let list = {
            filename: '报警信息记录'
        };
        sensorApi
            .terminalExport(qs.stringify(list, { arrayFormat: 'repeat' }), '', '', 'blob')
            .then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '传感器列表记录.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
					proxy.msgError('导出异常请联系管理员');
                }
            })
            .catch((err) => {
                proxy.msgError(err.msg);
            });
    } else {
        proxy.msgError('请选择需要导出的数据！');
    }
}

/**
 * 导入模板下载
 */
function downloadTemplate(){
	sensorApi
		.downloadTemplate({ filename: '传感器导入模板.xls' }, '', '', 'blob')
		.then((res) => {
			var debug = res;
			if (debug) {
				var elink = document.createElement('a');
				elink.download = '传感器导入模板.xlsx';
				elink.style.display = 'none';
				var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
				elink.href = URL.createObjectURL(blob);
				document.body.appendChild(elink);
				elink.click();
				document.body.removeChild(elink);
			} else {
				proxy.msgError('下载模板异常请联系管理员');
			}
		})
		.catch((err) => {
			proxy.msgError(err.msg);
		});
}
// 上传文件
function beforeUploadExcel(file) {
	// 限制文件类型 xls、xlsx
	const fileType = file.type;
	const fileTypeArr = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
	const isFileType = fileTypeArr.includes(fileType);
	if (!isFileType) {
		proxy.msgError('请上传xls、xlsx格式的文件！');
		return false;
	}
	// return true;
}

/**
 * 上传成功
 * @param res
 */
function fileUploadSuccess(res) {
	if(res.code == 200 && res.data){
		if(res.data.analysis.successData){
			uploadData.value = res.data.analysis.successData||[];
		}
		if(res.data.analysis.errorData){
			errorData.value = res.data.analysis.errorData||[];
		}
		promptSuccessMsg.value = res.data.analysis.successMsg||null;
		uploadOpen.value = true;
		proxy.$refs.uploadDeviceExcel.clearFiles()
	}

}

/**
 * 上传错误
 * @param err
 */
function error(err) {
	proxy.msgError('上传文件未成功！');
}

/**
 * 导入写入数据
 */
function batchImportDevice(){
	sensorApi.batchImportDevice({deviceList:uploadData.value}).then((res) => {
			if (res.code == 200) {
				proxy.msgSuccess('导入成功');
				uploadData.value = [];
				errorData.value = [];
				promptSuccessMsg.value = null;
				uploadOpen.value = false;
				//  清空文件上传列表
				proxy.$refs.uploadDeviceExcel.clearFiles();
			} else {
				proxy.msgError('导入失败！');
			}
		}).catch((err) => {
		proxy.msgError(err.msg);
	});
}
/**
 * 失败数据样式
 * @param row
 * @returns {string}
 */
const rowClassName = ({row}) => {
	return row?'link--danger':'';
}
const terminalData = ref([]);
// 查询列表
function getList() {
    sensorApi
        .terminalList(queryParams.value)
        .then((res) => {
            if (res.code == 200) {
                terminalData.value = res.data.records;
                queryParams.value.total = res.data.total;
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

// 表单重置
function resetQuery() {
    queryParams.value = {
        current: 1,
        size: 10,
        total: 0
    };
    getList();
}

// 报警设置表格
const tableData = ref([{ date: '温度>7C，<3°C时', name: '司机：若有情   18145787878 设备告警人：1    13156565656 设备告警人：2    13156565', address: '甘肃天国医药' }]);
//字典回显
const formDict = (data, val) => {
    return data && val ? proxy.selectDictLabel(data, val) : '--';
};
// 设备状态
const deviceStatusList = ref([]);
const validityList = ref([]);
const operateList = ref([]);
// 设备厂家
const manufacturerList = ref([]);
// 设备类型
const deviceTypeList = ref([]);
const sensorTypeList = ref([]);
// 字典请求
const getDict = async () => {
    sensorDeviceType.value = await proxy.getDictList('sensor_device_type');
    validityList.value = await proxy.getDictList('incubator_validity_status');
    operateList.value = await proxy.getDictList('sensor_operate_type');
    deviceTypeList.value = await proxy.getDictList('device_type');
    deviceStatusList.value = await proxy.getDictList('incubator_status');
    manufacturerList.value = await proxy.getDictList('equipment_manufacturer');
    sensorTypeList.value = await proxy.getDictList('sensor_type');
};
getDict();
getList();
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.deviceata {
    border: 2px solid #f5f7fa;
    padding: 20px;
    width: 100%;
}

.drawer-footer {
    position: absolute;
    bottom: 20px;
    left: 300px;
}

.drawer-footer1 {
    position: absolute;
    bottom: 20px;
    left: 500px;
}
</style>
