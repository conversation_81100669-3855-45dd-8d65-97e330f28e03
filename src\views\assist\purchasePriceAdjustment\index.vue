<template>
    <div class="app-container">
        <el-card class="box-card Botm" style="margin: 10px;">
            <el-form :model="queryParams" ref="queryRef" :inline="true" class="form_130">
                <TopTitle :handleQuery="getList" :resetQuery="resetQuery">
                    <el-form-item label="供应商" prop="querySupplierName">
                        <el-input v-model="queryParams.querySupplierName" placeholder="请输入供应商" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="商品名称" prop="queryCommodityName">
                        <el-input v-model="queryParams.queryCommodityName" placeholder="请输入商品名称" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="采购单据编号" prop="queryPurchaseOrderCode">
                        <el-input v-model="queryParams.queryPurchaseOrderCode" placeholder="请输入采购单据编号" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="经手人" prop="handledBy">
                        <el-select v-model="queryParams.handledBy" filterable placeholder="请选择经手人" clearable
                            class="form_225">
                            <el-option v-for="item in optionList" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="申请人" prop="queryApplyByName">
                        <el-input v-model="queryParams.queryApplyByName" placeholder="请输入申请人" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="申请日期" prop="applyDate">
                        <div class="box_date">
                            <el-date-picker v-model="queryParams.applyDate" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" />
                        </div>
                    </el-form-item>
                    <el-form-item label="审核状态" prop="auditStatus">
                        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" class="form_225">
                            <el-option :label="item.name" :value="item.value" v-for="item in reviewStatus"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                </TopTitle>
            </el-form>
        </el-card>
        <el-card class="box-card" style="margin: 10px;">
            <div style="display:flex;justify-content: space-between;">
                <el-button type="primary" @click="() => handleAdd(undefined, 'add')"
                    style="margin-bottom:10px">创建采购调价申请</el-button>
                <RightToptipBarV2 @handleRefresh="getList"
                    className="purchasePriceAdjustment_ppurchasePriceAdjustmentOrder" />
            </div>
            <div v-loading="loading" style="min-height:200px;">
                <DragTableColumn v-if="reviewStatus.length && outboundStatus.length" :columns="columns" :tableData="list"
                    v-model:queryParams="queryParams" className="purchasePriceAdjustment_ppurchasePriceAdjustmentOrder"
                    :getList="getList" :row-style="tableRowStyle">
                    <template v-slot:operate="{ scopeData }">
                        <el-button link type="primary" @click="handleAdd(scopeData.row, 'detail')"><img
                                src="@/assets/icons/detail.png" style="margin-right:5px" />详情</el-button>
                        <el-button link type="danger" @click="handleDelete(scopeData.row)"
                            :disabled="isDeleteAuthority(scopeData.row.createBy.id, ['1', '4', '6'], scopeData.row.auditStatus)"><img
                                src="@/assets/icons/delete.png" style="margin-right:5px" />删除</el-button>
                        <el-button link type="success" @click="handleReview(scopeData.row)"><img
                                src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                    </template>
                </DragTableColumn>
                <div style="float: right;">
                    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                        v-model:limit="queryParams.size" @pagination="getList" />
                </div>
            </div>
        </el-card>
        <Review :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="() => reviewVisible = false"
            :data="reviewData" />

        <ExitApplication :open="open" v-if="open" :beforClose="() => open = false" :modalType="modalType" :getList="getList"
            :title="title" />
        <DetailModal :open="detailOpen" v-if="detailOpen" :beforClose="() => detailOpen = false" :getList="getList"
            :detailData="detailData" />
    </div>
</template>
 
<script setup >
import { reactive, ref, getCurrentInstance, toRefs, onMounted } from 'vue'
import Review from './component/review.vue';
import ExitApplication from './component/exitApplication.vue';
import moment from 'moment'
import purchasePriceAdjustment from '@/api/erp/purchasePriceAdjustment'
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import isDeleteAuthority from '@/utils/isDeleteAuthority'
import DetailModal from './component/detailModal.vue'

const { proxy } = getCurrentInstance();
const list = ref([]);
const loading = ref(false);
const total = ref(0);
const open = ref(false)
const modalType = ref(null)
const title = ref(undefined)
const reviewVisible = ref(false)
const reviewData = ref({})
const reviewStatus = ref([])
const outboundStatus = ref([])
const detailData = ref({})
const optionList = ref([])
const detailOpen = ref(false)
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    },

});

const { queryParams } = toRefs(data);
const tableRowStyle = ({ row, rowIndex }) => {
    if (row.auditStatus == '6') {  // 草稿
        return {
            color: '#e6a23c'
        }
    } else if (row.auditStatus == '1') {  // 待审核
        return {
            color: '#409eff'
        }
    } else if (row.auditStatus == '2') {  //审核中
        return {
            color: '#67c23a'
        }
    } else if (row.auditStatus == '4') {  //已驳回
        return {
            color: '#ff4800'
        }
    }
}
const handleAdd = (row, type) => {

    // if (row && type == 'edit') {
    //     title.value = "编辑"
    //     purchasingModalRef.value.getDetail(row)
    // }
    if (row && type == 'detail') {
        title.value = "采购调价申请详情"
        detailData.value = row
        detailOpen.value = true
        // purchasingModalRef.value.getDetail(row)
    }
    if (!row && type == 'add') {
        title.value = "创建采购调价申请"
        open.value = true
        modalType.value = type
    }

}
/** 查询角色列表 */
function getList() {
    loading.value = true
    const params = { ...queryParams.value }
    params['handleBy.id'] = params.handledBy
    params.beginApplyDate = params.applyDate?.length ? moment(params.applyDate[0]).format('YYYY-MM-DD 00:00:00') : undefined
    params.endApplyDate = params.applyDate?.length ? moment(params.applyDate[1]).format('YYYY-MM-DD 23:59:59') : undefined
    delete params?.applyDate
    delete params?.handledBy
    purchasePriceAdjustment.getList(params).then(res => {
        if (res.code == 200) {
            list.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}

/**
 * @description: 点击审核记录
 * @return {*}
 */
const handleReview = (row) => {
    reviewVisible.value = true
    reviewData.value = row
}




/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    getList();
}
/**
 * @description: 删除
 * @return {*}
 */
const handleDelete = row => {
    proxy.$confirm('是否确认删除改数据项?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        purchasePriceAdjustment.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                proxy.msgSuccess('删除成功')
                getList()
            } else {
                proxy.msgError(res.msg)
            }
        })
    }).catch(() => { });
}
async function dict() {
    reviewStatus.value = await proxy.getDictList('erp_review_status')
    outboundStatus.value = await proxy.getDictList('s_outbound_status')
}
/**
 * @description: 获取经手人
 * @return {*}
 */

const getUsers = () => {
    warehouseNumberManagement.getUser().then(res => {
        if (res.code == 200) {
            optionList.value = res.data?.records
        }

    })
}
onMounted(() => {
    dict()
    getList();
    getUsers()
})
const columns = ref(
    [
        {
            label: '申请编号',
            prop: 'applyNo',
            minWidth: 180,
        }, {
            label: '申请日期',
            prop: 'applyDate',
            type: 'date'
        }, {
            label: '供应商名称',
            prop: 'supplier.enterpriseName'
        }, {
            label: '申请人',
            prop: 'createBy.name'
        }, {
            label: '审核状态',
            prop: 'auditStatus',
            type: 'status',
            filters: reviewStatus
        },{
            label: '合计调价金额',
            prop: 'adjustPrice'
        }, {
            label: '经手人',
            prop: 'handleBy.name'
        }, {
            label: '操作',
            type: 'operate',
            fixed: 'right',
            minWidth: 200
        }
    ]
)
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

::v-deep .labelStyle {
    .el-form-item__label {
        // margin-left: 10px;
    }
}

::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

::v-deep .serchBtn {
    .el-form-item__content {
        display: flex;
        justify-content: end;
    }
}

.box_date {
    width: 220px;
}
</style>


