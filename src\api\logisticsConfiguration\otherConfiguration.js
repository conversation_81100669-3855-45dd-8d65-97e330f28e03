import request from '@/utils/request'
export default {
    /** s 温层配置 */
    // 温层列表
    listTemperatureType: function (params) {
        return request.get('/tms/config/temperatureType/list', params);
    },
    // 根据Id获取温层配置数据
    getTemperatureTypeById: function (params) {
        return request.get('/tms/config/temperatureType/queryById', params);
    },
    // 新增或修改温层信息后保存
    saveTemperatureType:function(data){
        return request.post( '/tms/config/temperatureType/save', data)
    },
    // 删除温层
    delTemperatureType: function (params) {
        return request.delete("/tms/config/temperatureType/delete", params);
    },
    // 查询承运商所有温层
    getTemperatureTypeList: function (params) {
        return request.get('/tms/config/temperatureType/getTemperatureTypeList', params);
    },
    /** e 温层配置 */
    /** s 支付配置 */
    // 获取承运商支付二维码
    getCarrierCodeFile: function (params) {
        return request.get('/tms/codeFile/carrier', params);
    },
    // 保存
    saveCarrierCodeFile:function(data){
        return request.post( '/tms/codeFile/save', data)
    },
    /** e 支付配置 */
}
