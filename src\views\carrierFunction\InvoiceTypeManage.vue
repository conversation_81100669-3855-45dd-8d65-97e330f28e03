<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="货主名称" prop="owner.id">
                    <el-select v-model="queryForm['owner.id']" clearable placeholder="请选择货主名称" filterable @change="handleQuery">
                        <el-option v-for="item in shipperList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="queryForm['owner.id']" label="抬头名称" prop="invoiceHead">
                    <el-input v-model="queryForm.invoiceHead" clearable placeholder="请输入抬头名称" @clear="handleQuery" @keyup.enter="handleQuery" />
                </el-form-item>
                <search-button v-if="queryForm['owner.id']" :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div :class="queryForm['owner.id'] ? 'mb10' : ''">
                <el-button icon="el-icon-plus" type="primary" @click="onAddInvoiceInformation">添加</el-button>
                <right-toolbar v-if="queryForm['owner.id']" v-model:columns="columns" v-model:show-search="showSearch" table-i-d="InvoiceTypeManageList" @queryTable="getList"></right-toolbar>
            </div>
            <column-table v-if="queryForm['owner.id']" ref="InvoiceTypeManageList" v-loading="loading" :columns="columns" :data="dataList" :max-height="600">
                <template #ownerName="{ row }">
                    {{ row.owner?.name || '' }}
                </template>
                <template #createBy="{ row }">
                    {{ row.createBy.name }}
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-edit" link size="small" type="warning" @click="onEditInvoiceInformation(row)">修改</el-button>
                    <el-button icon="el-icon-delete" link size="small" type="danger" @click="onDeleteInvoiceInformation(row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-if="queryForm['owner.id']" v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- 新增、修改开票信息	-->
        <el-drawer v-model="invoiceInformationVisible" title="开票信息" @close="onCloseInvoiceInformation">
            <div class="p-10" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <el-form ref="invoiceInformationForm" :model="invoiceInformationForm" :rules="invoiceInformationFormRules" label-width="auto">
                        <el-form-item label="货主名称" prop="owner.id">
                            <el-select v-model="invoiceInformationForm['owner.id']" class="w-full" filterable clearable placeholder="请选择货主名称" :disabled="isEdit" @change="handleQuery">
                                <el-option v-for="item in ownerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                            </el-select>
                        </el-form-item>
                        <template v-if="invoiceInformationForm['owner.id']">
                            <el-form-item label="抬头名称" prop="invoiceHead">
                                <el-input v-model="invoiceInformationForm.invoiceHead" clearable placeholder="请输入抬头名称"></el-input>
                            </el-form-item>
                            <el-form-item label="单位税号" prop="taxNo">
                                <el-input v-model="invoiceInformationForm.taxNo" clearable placeholder="请输入单位税号" show-word-limit></el-input>
                            </el-form-item>
                            <el-form-item label="注册地址" prop="address">
                                <el-input v-model="invoiceInformationForm.address" clearable placeholder="请输入注册地址"></el-input>
                            </el-form-item>
                            <el-form-item label="注册电话" prop="phone">
                                <el-input v-model="invoiceInformationForm.phone" clearable placeholder="请输入注册电话"></el-input>
                            </el-form-item>
                            <el-form-item label="开户银行" prop="openBank">
                                <el-input v-model="invoiceInformationForm.openBank" clearable placeholder="请输入开户银行"></el-input>
                            </el-form-item>
                            <el-form-item label="银行账号" prop="bankAccount">
                                <el-input v-model="invoiceInformationForm.bankAccount" clearable placeholder="请输入银行账号" show-word-limit></el-input>
                            </el-form-item>
                        </template>
                    </el-form>
                </el-card>
                <div class="flex justify-end mt-10">
                    <el-button @click="onCloseInvoiceInformation">取消</el-button>
                    <el-button type="primary" @click="onSaveInvoicingInformation">保存</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>
<script>
import invoiceTypeManage from '@/api/carrierEnd/invoiceTypeManage';
import invoiceInformationMaintenance from '@/api/shipperEnd/invoiceInformationMaintenance';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation.js';

export default {
    name: 'InvoiceTypeManage',
    components: { ColumnTable, RightToolbar, SearchButton },
    data() {
        return {
            showSearch: true,
            isEdit: false,
            queryForm: {
                current: 1,
                size: 10,
                invoiceHead: undefined,
                'owner.id': undefined
            },
            columns: [
                { title: '货主名称', key: 'ownerName', align: 'center', width: '200px', fixed: 'left', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '抬头名称', key: 'invoiceHead', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '单位税号', key: 'taxNo', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '注册地址', key: 'address', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '注册电话', key: 'phone', align: 'center', width: '120px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '开户银行', key: 'openBank', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '银行账号', key: 'bankAccount', align: 'center', width: '250px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createBy', align: 'center', width: '220px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', width: '200px', hideFilter: true, columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '130px', fixed: 'right', hideFilter: true, columnShow: true, showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            // 货主列表
            shipperList: [],
            // 合作货主列表（用于抽屉中的货主下拉）
            ownerList: [],
            // 新增、修改开票信息 抽屉状态
            invoiceInformationVisible: false,
            // 新增、修改开票信息 表单数据
            invoiceInformationForm: {
                'owner.id': undefined,
                invoiceHead: undefined,
                taxNo: undefined,
                address: undefined,
                phone: undefined,
                openBank: undefined,
                bankAccount: undefined
            },
            invoiceInformationFormRules: {
                'owner.id': [{ required: true, message: '请选择货主名称', trigger: 'change' }],
                invoiceHead: [{ required: true, message: '请输入抬头名称', trigger: 'blur' }],
                taxNo: [
                    { required: true, message: '请输入单位税号', trigger: 'blur' },
                    { pattern: /^[A-Za-z0-9]{15,20}$/, message: '请输入正确的单位税号', trigger: 'blur' }
                ],
                phone: [{ pattern: /^(1[3456789]\d{9})$|^(0\d{2,3}-?\d{7,8})$/, message: '请输入正确的手机号码或固定电话号码', trigger: 'blur' }],
                bankAccount: [{ pattern: /^[0-9]*$/, message: '请输入正确的银行账号', trigger: 'blur' }]
            }
        };
    },
    created() {
        this.getOwnerSelect();
    },
    methods: {
        /**
         * 获取货主列表（搜索区域使用）
         */
        async getOwnerSelect() {
            try {
                const res = await invoiceTypeManage.getOwnerSelect();
                if (res.code === 200) {
                    this.shipperList = res.data || [];
                } else {
                    this.$message.error('获取货主列表失败');
                    this.shipperList = [];
                }
            } catch (error) {
                console.error('获取货主列表异常:', error);
                this.$message.error('获取货主列表异常');
                this.shipperList = [];
            }
        },
        /**
         * 获取合作货主列表（抽屉中使用）
         */
        async getOwnerList() {
            try {
                const res = await enterpriseCooperation.cooperateSelect();
                if (res.code === 200) {
                    this.ownerList = res.data || [];
                } else {
                    this.$message.error('获取合作货主列表失败');
                    this.ownerList = [];
                }
            } catch (error) {
                console.error('获取合作货主列表异常:', error);
                this.$message.error('获取合作货主列表异常');
                this.ownerList = [];
            }
        },
        /**
         * 获取列表
         */
        async getList() {
            if (!this.queryForm['owner.id']) {
                this.dataList = [];
                this.total = 0;
                return;
            }
            this.loading = true;
            try {
                const { 'owner.id': ownerId, ...params } = this.queryForm;
                const requestParams = {
                    ...params,
                    'owner.id': ownerId
                };
                const res = await invoiceTypeManage.getList(requestParams);
                if (res.code === 200) {
                    this.dataList = res.data.records;
                    this.total = res.data.total;
                } else {
                    this.dataList = [];
                    this.total = 0;
                    this.$message.error('获取列表失败');
                }
            } catch (error) {
                console.error('获取列表异常:', error);
                this.dataList = [];
                this.total = 0;
                this.$message.error('获取列表异常');
            } finally {
                this.loading = false;
            }
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            this.getList();
        },
        /**
         * 重置表单
         * @param {string} formName 表单ref名称
         */
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
        },
        /**
         * 重置查询条件
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryForm = {
                current: 1,
                size: 10,
                invoiceHead: undefined,
                'owner.id': undefined
            };
            this.dataList = [];
            this.total = 0;
        },
        /**
         * 关闭开票信息抽屉
         */
        onCloseInvoiceInformation() {
            this.isEdit = false;
            this.resetForm('invoiceInformationForm');
            this.invoiceInformationForm = {
                'owner.id': undefined,
                invoiceHead: undefined,
                taxNo: undefined,
                address: undefined,
                phone: undefined,
                openBank: undefined,
                bankAccount: undefined
            };
            this.invoiceInformationVisible = false;
        },
        /**
         * 保存开票信息
         */
        onSaveInvoicingInformation() {
            this.$refs.invoiceInformationForm.validate(async (valid) => {
                if (valid) {
                    try {
                        const { 'owner.id': ownerId, ...formData } = this.invoiceInformationForm;
                        const params = {
                            ...formData,
                            owner: {
                                id: ownerId
                            },
                            source: '2' // 2-承运商操作
                        };
                        const res = await invoiceInformationMaintenance.save(params);
                        if (res.code === 200) {
                            this.$message.success(this.invoiceInformationForm.id ? '修改成功' : '添加成功');
                            this.onCloseInvoiceInformation();
                            await this.getOwnerSelect(); // 更新货主列表
                            await this.getList();
                        } else {
                            this.$message.error(res.msg || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存开票信息异常:', error);
                        this.$message.error('保存开票信息异常');
                    }
                }
            });
        },
        /**
         * 添加开票信息
         */
        onAddInvoiceInformation() {
            this.isEdit = false;
            this.invoiceInformationVisible = true;
            this.getOwnerList();
        },
        /**
         * 修改开票信息
         * @param {Object} row 当前行数据
         */
        onEditInvoiceInformation(row) {
            this.isEdit = true;
            this.invoiceInformationVisible = true;
            this.getOwnerList();
            this.$nextTick(() => {
                this.invoiceInformationForm = {
                    ...row,
                    'owner.id': row.owner?.id
                };
            });
        },
        /**
         * 删除开票信息
         * @param {Object} row 当前行数据
         */
        onDeleteInvoiceInformation(row) {
            this.$confirm('是否删除该开票信息？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(async () => {
                    try {
                        const res = await invoiceInformationMaintenance.delete({
                            ids: row.id
                        });
                        if (res.code === 200) {
                            this.$message.success('删除成功');
                            await this.getList();
                        } else {
                            this.$message.error(res.msg || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除开票信息异常:', error);
                        this.$message.error('删除开票信息异常');
                    }
                })
                .catch(() => {});
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #000 !important;
}
</style>
