import http from "@/utils/request"

/*
 *@description: 系统用户
 *@author: 路正宁
 *@date: 2023-03-17 12:05:20
 *@version: V1.0
*/
export default {
    save: function (inputForm) {
        return http.post(
            '/sys/user/save',
            inputForm
        )
    },

    delete: function (ids) {
        return http.delete(
            '/sys/user/delete',
            {ids: ids}
        )
    },
    userSync: function () {
        return http.get(
            '/sync/usercenter/syncUserData',
        )
    },
    queryById: function (id) {
        return http.get(
            '/sys/user/queryById',
            {id: id}
        )
    },

    list: function (params) {
        return http.get(
            '/sys/user/list',
            params
        )
    },

    exportTemplate: function () {
        return http.get(
            '/sys/user/import/template',
            'blob'
        )
    },

    exportExcel: function (params) {
        return http.get(
            '/sys/user/export',
            params,
            'blob'
        )
    },

    importExcel: function (data) {
        return http.post(
            '/sys/user/import',
            data
        )
    },
    /**
     * 用户设置列表
     * @param params
     * @returns {Promise | Promise<unknown>}
     */
    userSettingList: function (params) {
        return http.get(
            '/sys/userSetting/list',
            params
        )
    },
    /**
     * 保存用户设置
     * @param data
     * @returns {Promise | Promise<unknown>}
     */
    userSettingSave: function (data) {
        return http.post(
            '/sys/userSetting/save',
            data
        )
    },
}
