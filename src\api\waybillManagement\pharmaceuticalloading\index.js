import request from '@/utils/request';

export default {
	// 装车记录列表
	assignRecordList: function(params) {
		return request.get('/tms/receipt/assignRecord/list', params);
	},

	// 获取入库完成的货物
	getReceiptGoodsInfo: function(params) {
		return request.get('/tms/receipt/handOrderDetail/getReceiptGoodsInfo', params);
	},
	// 获取司机
	getLoadCarDriverSelect: function(params) {
		return request.get('/tms/driver/lineDriver/getLoadCarDriverSelect', params);
	},
	// 获取承运商所有司机
	getDriverList: function(params) {
		return request.get('/tms/driverInfo/getDriverList', params);
	},
	// 获取集货区List
	getBranchAreaList: function(params) {
		return request.get('/tms/area/getBranchAreaList', params);
	},
	// 获取集货区List
	getAreaList: function(params) {
		return request.get('/tms/receipt/areaGoodsDetail/getAreaList', params);
	},
	// 获取集货区List
	getAreaGoodsList: function(params) {
		return request.get('/tms/receipt/areaGoodsDetail/getAreaGoodsList', params);
	},

	// 确认装车
	assignRecordLoadCar: function(data) {
		return request.post('/tms/receipt/assignRecord/loadCar', data);
	},

	// 保温箱
	// 查询保温箱
	getLoadCarSensorSelect: function(params) {
		return request.get('/tms/receipt/incubatorRecord/loadCar/getIncubatorList', params);
	},
	// 点击保温箱的区县获取运单
	incubatorCountyTransOrderList: function(params) {
		return request.get('/tms/receipt/incubatorRecord/incubator-county-transOrder', params);
	},
	// 点击保温箱的区县获取运单
	incubatorCountyTransCodeList: function(params) {
		return request.get('/tms/receipt/incubatorRecord/incubator-county-transCode', params);
	},
	// 装车-保温箱选择列表-分组查询省市区搜索条件
	getIncubatorAddress: function(params) {
		return request.get('/tms/receipt/incubatorRecord/getIncubatorAddress', params);
	},


	// 	装车详情
	getassignRecordQueryById: function(params) {
		return request.get('/tms/receipt/assignRecord/queryById', params);
	},
	// 装车详情-点击区县获取运单
	getDetailGroupOrder: function(params) {
		return request.get('/tms/receipt/assignRecord/getDetailGroupOrder', params);
	},
	// 装车详情-点击区县的运单获取箱码
	getDetailGroupCode: function(params) {
		return request.get('/tms/receipt/assignRecord/getDetailGroupCode', params);
	},
	// 完成装车&确认发车
	getchangeAssignStatus: function(params) {
		return request.post('/tms/receipt/assignRecord/changeAssignStatus', params);
	},
	// 装车记录完成装车后回退修改
	getassignRecordRollback: function(params) {
		return request.post('/tms/receipt/assignRecord/rollback', params);
	},
	// 装车撤销
	postAssignRevoke: function(params) {
		return request.post('/tms/receipt/assignRecord/assignRevoke', params);
	},
	// 导出装车记录
	assignRecordExport: function (params,config,resDetail,responseType) {
        return request.get('/tms/receipt/assignRecord/export', params,config,resDetail,responseType,1);
    },
	// 三方物流公司
	threePartFlowFlowList: function(params) {
		return request.get('/tms/threePartFlow/flowList', params);
	},
};


