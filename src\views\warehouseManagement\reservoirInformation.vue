<template>
    <div>
        <!-- 搜索 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item prop="wareHouse">
                    <el-select v-model="queryParams.wareHouse" @change="handlerWare2" placeholder="请选择所属仓" clearable
                        @clear="getList">
                        <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="storeHouse">
                    <el-select v-model="queryParams.storeHouse" placeholder="请选择所属库" clearable @change="handleSearch"
                        @clear="getList">
                        <el-option v-for="item in libraryOptions2" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入库名称或简称快速查询" clearable class="form_225"
                        @clear="getList" @keydown.enter="handleSearch">
                        <template v-slot:suffix>
                            <el-icon @click="handleSearch">
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(creatForm)" class="creatSpan">新增</el-button>
            <RightToptipBarV2 @handleRefresh="getList" className="reservoirInformation" style="float:right;margin-top:10px"
                v-if="typeOptions.length && kindOptions.length && classOptions.length" />
            <DragTableColumn :columns="columns" :tableData="tableList" className="reservoirInformation"
                v-model:queryParams="queryParams" :getList="getList">
                <template v-slot:operate="{ scopeData }">
                    <el-button link type="primary" @click="handleEdit(scopeData.row)">
                        <img src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                    <el-button link type="danger" @click="handleDelete(scopeData.row)"><img src="@/assets/icons/delete.png"
                            style="margin-right:5px" />删除</el-button>
                    <el-button link @click="handlerLog(scopeData.row)" style="color:#67c23a"><img
                            src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                </template>
            </DragTableColumn>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增的弹框 -->
        <el-dialog v-model="dialogFormVisible" width="40%" title="新增仓信息" :before-close="() => handlerClose()">
            <el-form :model="dialogform" label-width="100px" :rules="rules" ref="creatForm">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="所属仓" prop="wareHouse">
                            <el-select v-model="dialogform.wareHouse" @change="handlerWare" placeholder="请选择所属仓" clearable
                                style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属库" prop="storeHouse">
                            <el-select v-model="dialogform.storeHouse" placeholder="请选择所属库" clearable style="width:100%;">
                                <el-option v-for="item in libraryOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区编码" prop="code">
                            <el-input v-model="dialogform.code" autocomplete="off" placeholder="请输入库区编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库区名称" prop="name">
                            <el-input v-model="dialogform.name" autocomplete="off" placeholder="请输入库区名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区简称" prop="nameSimplified">
                            <el-input v-model="dialogform.nameSimplified" autocomplete="off" placeholder="请输入库区简称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库区简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="dialogform.nameSimplifiedPinyin" autocomplete="off" placeholder="请输入库区简拼" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区类别" prop="type">
                            <el-select v-model="dialogform.type" placeholder="请选择库区类别" clearable style="width:100%;">
                                <el-option v-for="item in typeOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库区种类" prop="kind">
                            <el-select v-model="dialogform.kind" placeholder="请选择库区种类" clearable style="width:100%;">
                                <el-option v-for="item in kindOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区分类" prop="classification">
                            <el-select v-model="dialogform.classification" placeholder="请选择库区分类" clearable
                                style="width:100%;">
                                <el-option v-for="item in classOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="dialogform.remark" :rows="1" type="textarea" placeholder="请输入备注" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="仓库平面图" prop="plane">
                    <el-upload v-model:file-list="dialogform.plane" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item label="仓库CRT图" prop="crtPicture">
                    <el-upload v-model:file-list="dialogform.crtPicture" :before-upload="beforeUpload" :action="uploadUrl"
                        :headers='headers' :on-preview="handlePictureCardPreview" :on-remove="handleRemove" list-type="picture-card">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
            </el-form>
            <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="dialogImageUrl" />
            </el-dialog>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="creat(creatForm)">确定</el-button>
            </div>
        </el-dialog>
        <!-- 编辑的弹框 -->
        <el-dialog v-model="editdialogFormVisible" width="40%" title="修改仓信息" :before-close="() => handlerClose()">
            <el-form :model="editdialogform" label-width="100px" :rules="rules" ref="editcreatForm">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="所属仓" prop="wareHouse">
                            <el-select v-model="editdialogform.wareHouse" @change="handlerWare3" placeholder="请选择所属仓"
                                clearable style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="所属库" prop="storeHouse">
                            <el-select v-model="editdialogform.storeHouse" placeholder="请选择所属库" clearable
                                style="width:100%;">
                                <el-option v-for="item in libraryOptions3" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区编码" prop="code">
                            <el-input v-model="editdialogform.code" autocomplete="off" placeholder="请输入库区编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库区名称" prop="name">
                            <el-input v-model="editdialogform.name" autocomplete="off" placeholder="请输入库区名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区简称" prop="nameSimplified">
                            <el-input v-model="editdialogform.nameSimplified" autocomplete="off" placeholder="请输入库区简称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库区简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="editdialogform.nameSimplifiedPinyin" autocomplete="off"
                                placeholder="请输入库区简拼" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区类别" prop="type">
                            <el-select v-model="editdialogform.type" placeholder="请选择库区类别" clearable style="width:100%;">
                                <el-option v-for="item in typeOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库区种类" prop="kind">
                            <el-select v-model="editdialogform.kind" placeholder="请选择库区种类" clearable style="width:100%;">
                                <el-option v-for="item in kindOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库区分类" prop="classification">
                            <el-select v-model="editdialogform.classification" placeholder="请选择库区分类" clearable
                                style="width:100%;">
                                <el-option v-for="item in classOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="editdialogform.remark" :rows="1" type="textarea" placeholder="请输入备注" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="仓库平面图" prop="plane">
                    <el-upload v-model:file-list="editdialogform.plane" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item label="仓库CRT图" prop="crtPicture">
                    <el-upload v-model:file-list="editdialogform.crtPicture" :before-upload="beforeUpload"
                        :action="uploadUrl" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                         :headers='headers' list-type="picture-card">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
            </el-form>
            <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="dialogImageUrl" />
            </el-dialog>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="edit(editcreatForm)">确定</el-button>
            </div>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
        <viewImg v-if="uploadVisible" :visible="uploadVisible" :src="uploadViewImgUrl" :beforeClose="() => uploadVisible = false" />
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import informationMaintenance from '@/api/erp/warehouseManagement/informationMaintenance'
import libraryInformation from '@/api/erp/warehouseManagement/libraryInformation'
import reservoirInformation from '@/api/erp/warehouseManagement/reservoirInformation'
import tool from '@/utils/tool';
import logList from './logList.vue'
const reviewRow = ref({})
const { proxy } = getCurrentInstance();
const queryParams = reactive({
    current: 1,
    size: 10,
})
const selected = ref([])
const dialogFormVisible = ref(false)
const reviewVisible = ref(false)
const editcreatForm = ref()
const ids = ref('')
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const editdialogFormVisible = ref(false)
const uploadVisible = ref(false)
const dialogform = reactive([])
const editdialogform = ref([])
const warehouseOptions = ref([])
const libraryOptions = ref([])
const libraryOptions2 = ref([])
const libraryOptions3 = ref([])
const typeOptions = ref([])
const kindOptions = ref([])
const classOptions = ref([])
const tableList = ref([])
const wareId = ref('')
const uploadViewImgUrl = ref('')
const creatForm = ref()
const total = ref(0)
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const rules = reactive({
    wareHouse: [{ required: true, message: '请选择所属仓', trigger: 'blur' },],
    storeHouse: [{ required: true, message: '请选择所属库', trigger: 'blur' },],
    code: [{ required: true, message: '请输入库区编码', trigger: 'blur' },],
    name: [{ required: true, message: '请输入库区名称', trigger: 'blur' },],
    nameSimplified: [{ required: true, message: '请输入库区简称', trigger: 'blur' },],
    nameSimplifiedPinyin: [{ required: true, message: '请输入库区简拼', trigger: 'blur' },],
    type: [{ required: true, message: '请选择库区类别', trigger: 'blur' },],
    kind: [{ required: true, message: '请选择库区种类', trigger: 'blur' },],
    classification: [{ required: true, message: '请选择库区分类', trigger: 'blur' },],
    plane: [{ required: true, message: '请上传仓库平面图', trigger: 'blur' },],
    crtPicture: [{ required: true, message: '请上传仓库CRT图', trigger: 'blur' },],
})
const columns = ref(
    [
        {
            label: '所属仓简称',
            prop: 'wareHouse.nameSimplified',
            // type: "sort",
            // fixed: 'left'
        },
        {
            label: '所属库简称',
            prop: 'storeHouse.nameSimplified'
        }, {
            label: '库区编码',
            prop: 'code'
        }, {
            label: '库区名称',
            prop: 'name'
        }, {
            label: '库区简称',
            prop: 'nameSimplified'
        }
        , {
            label: '库区简拼',
            prop: 'nameSimplifiedPinyin'
        }, {
            label: '库区类别',
            prop: 'type',
            type: 'status',
            filters: typeOptions
        }, {
            label: '库区种类',
            prop: 'kind',
            type: 'status',
            filters: kindOptions
        }, {
            label: '库区分类',
            prop: 'classification',
            type: 'status',
            filters: classOptions
        }, {
            label: '操作',
            prop: 'operate',
            type: 'operate',
            minWidth: 200,
        },
    ]
)
//搜索
const handleSearch = () => {
    getList()
}
//获取仓列表
function getwarehouseList() {
    informationMaintenance.list().then(res => {
        if (res.code == 200) {
            warehouseOptions.value = res.data.records
        }
    })
}
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
const handlerWare = (item) => {
    dialogform.storeHouse = '';
    // editdialogform.storeHouse = ''
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions.value = res.data.records
        }
    })
}
const handlerWare2 = (item) => {
    handleSearch()
    queryParams.storeHouse = '';
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions2.value = res.data.records
        }
    })
}
handlerWare2()
const handlerWare3 = (item) => {
    console.log(111);
    editdialogform.value.storeHouse = ''
    console.log(33);
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions3.value = res.data.records
        }
    })
}
getwarehouseList()
// 新增按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    formEl.resetFields()
}
//获取库区列表
function getList() {
    const { wareHouse, storeHouse, ...rest } = queryParams;
    const queryParamsUpdated = {
        'wareHouse.id': wareHouse,
        'storeHouse.id': storeHouse,
        ...rest
    };
    reservoirInformation.list({ ...queryParamsUpdated }).then(res => {
        if (res.code == 200) {
            tableList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
//新增库区
const creat = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var params = {
                wareHouse: { id: dialogform.wareHouse },
                storeHouse: { id: dialogform.storeHouse },
                code: dialogform.code,
                name: dialogform.name,
                nameSimplified: dialogform.nameSimplified,
                nameSimplifiedPinyin: dialogform.nameSimplifiedPinyin,
                type: dialogform.type,
                kind: dialogform.kind,
                classification: dialogform.classification,
                remark: dialogform.remark,
                plane: dialogform.plane,
                crtPicture: dialogform.crtPicture,
            }
            params.commonFileDTOList = []
            if (params.plane) {
                params.plane.forEach((item) => {
                    params.commonFileDTOList.push({
                        fileType: '1',
                        fileName: item.response.data.name,
                        fileUrl: item.response.data.url
                    })
                })
                delete params.plane
            }
            if (params.crtPicture) {
                params.crtPicture.forEach((item) => {
                    params.commonFileDTOList.push({
                        fileType: '2',
                        fileName: item.response.data.name,
                        fileUrl: item.response.data.url
                    })
                })
                delete params.crtPicture
            }
            console.log(params);
            reservoirInformation.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        dialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "添加失败，请稍后重试",
                        });
                    }
                })
        }
    });
};

// 编辑按钮
const handleEdit = (row) => {
    editdialogFormVisible.value = true
    handlerWare()
    // dict()
    // handlerWare2()
    handlerWare3()
    reservoirInformation.detail({ id: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.value = res.data
            editdialogform.value.wareHouse = res.data.wareHouse.id
            editdialogform.value.storeHouse = res.data.storeHouse.id
            ids.valus = res.data.id
            editdialogform.value.plane = []
            editdialogform.value.crtPicture = []
            res.data.commonFileDTOList.forEach((item) => {
                if (item.fileType == '1') {
                    editdialogform.value.plane.push({
                        url: item.fileUrl,
                        name: item.fileName,
                        id: item.id
                    })
                } else {
                    editdialogform.value.crtPicture.push({
                        url: item.fileUrl,
                        name: item.fileName,
                        id: item.id
                    })
                }
            })
        }
    })
}
// 编辑请求
const edit = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var params = {
                wareHouse: { id: editdialogform.value.wareHouse },
                storeHouse: { id: editdialogform.value.storeHouse },
                code: editdialogform.value.code,
                name: editdialogform.value.name,
                nameSimplified: editdialogform.value.nameSimplified,
                nameSimplifiedPinyin: editdialogform.value.nameSimplifiedPinyin,
                type: editdialogform.value.type,
                kind: editdialogform.value.kind,
                classification: editdialogform.value.classification,
                remark: editdialogform.value.remark,
            }
            params.commonFileDTOList = []
            editdialogform.value.plane.forEach((item) => {
                params.commonFileDTOList.push({
                    fileType: '1',
                    fileName: item.name,
                    id: item.id,
                    fileUrl: item.url
                })
            })
            editdialogform.value.crtPicture.forEach((item) => {
                params.commonFileDTOList.push({
                    fileType: '2',
                    fileName: item.name,
                    id: item.id,
                    fileUrl: item.url
                })
            })
            params.id = ids.valus
            console.log(params);
            reservoirInformation.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "修改成功",
                            type: "success",
                        });
                        editdialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "修改失败，请稍后重试",
                        });
                    }
                })
        }
    }
    );
};

// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
// 删除库区
const handleDelete = (row) => {
    proxy.$confirm('是否确认删除此库区信息?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        reservoirInformation.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
const handlePictureCardPreview = (uploadFile) => {
    uploadViewImgUrl.value = uploadFile?.url
    uploadVisible.value = true
}
const handleRemove = (uploadFile, uploadFiles) => {
    console.log(uploadFile, uploadFiles)
}
const beforeUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
        ElMessage.error('只允许上传图片文件!')
    }
    return isImage;
}
// 字典
async function dict() {
    typeOptions.value = await proxy.getDictList('reservoir_type')
    kindOptions.value = await proxy.getDictList('reservoir_kind')
    classOptions.value = await proxy.getDictList('reservoir_class')
}
dict()
</script>

<style lang="scss" scoped>
.dialog {
    padding: 30px
}
::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
    display: contents !important;
}

::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.dialogP {
    position: relative;
    top: -5px;
    left: 100px
}

.creatSpan {
    margin-bottom: 10px;
}

.dialog-footer {
    display: flex;
    justify-content: end;
}</style>
