<template>
    <div>
        <!-- 查询表头 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item label="创建日期" prop="createDate">
                    <el-date-picker v-model="queryParams.createDate" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" />
                </el-form-item>
                <el-form-item label="状态" prop="statusAble">
                    <el-select v-model="queryParams.statusAble" placeholder="请选择状态">
                        <el-option :label="item.name" :value="item.value" v-for=" item  in  diaSelectList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="任务类型" prop="taskType" clearable>
                    <el-select v-model="queryParams.taskType" placeholder="请选择任务类型">
                        <el-option :label="item.name" :value="item.value" v-for=" item in taskTypeList" :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchQuery">搜索</el-button>
                    <el-button @click="handlerMark">批量标记</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表格内容 -->
        <el-card style="margin:10px;">
            <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange" class="el-table"
                border>
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="序号" align="center" prop="index"  width="80">
                    <template #default="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column label="任务类型" align="left" prop="taskType" min-width="60" 
                :formatter="(row) => formDict(taskTypeList, row.taskType)"/>
                <el-table-column label="任务名称" align="left" prop="taskName" min-width="60" />
                <el-table-column label="来源" align="left" prop="sources" min-width="65" />
                <el-table-column label="自编码" align="left" prop="selfeCode" min-width="70">
                    <template #default="scope">
                        <span @click="handleCellClick(scope.row)" style="color:#1989fa">{{ scope.row.selfeCode }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="名称" align="left" prop="enterName" min-width="100" />
                <el-table-column label="创建日期" align="left" prop="createDate" min-width="60"
                    :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')" />
                <el-table-column label="异常字段" align="left" prop="remark" min-width="110" />
                <el-table-column label="状态" align="left" prop="statusAble" min-width="50">
                    <template #default="scope">
                        {{ scope.row.statusAble == '1' ? '待处理' : '已处理' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" fixed="right" min-width="130">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleQuery(scope.row)"><img src="@/assets/icons/update.png"
                                style="margin-right:5px" />标记状态</el-button>
                        <el-button link type="primary" @click="handlerLog(scope.row)" style="color:#67c23a"><img
                                src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 批量标记状态 -->
        <el-dialog v-model="dialogVisible" title="标记状态" width="30%">
            <div class="dialog-div">
                <p class="dialog-p">当前任务ID:</p>
                <P class="dialog-p">{{ idsvalue }}</P>
                <p class="dialog-p">任务状态：待处理</p>
                <p class="dialog-p">标记为：已处理</p>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="updateStatus">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 标记状态 -->
        <el-dialog v-model="dialogVisible2" title="标记状态" width="30%">
            <div class="dialog-div">
                <p class="dialog-p">当前任务ID: {{ idsvalue2 }}</p>
                <p class="dialog-p">任务状态：{{ statusvalue == '2' ? '待处理' : '已处理' }}</p>
                <p class="dialog-p">标记为：{{ statusvalue == '2' ? '已处理' : '待处理' }}</p>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible2 = false">取消</el-button>
                    <el-button type="primary" @click="updateStatus2">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import abnormalTask from "@/api/erp/abnormalTask/abnormalTask";
const { proxy } = getCurrentInstance();
import { ElMessage } from "element-plus";
import logList from './logList.vue'
import moment from 'moment';
const dialogVisible = ref(false)
const dialogVisible2 = ref(false)
const reviewVisible = ref(false)
const diaSelectList = ref([])
const queryForm = ref()
const idsvalue = ref()
const idsvalue2 = ref()
const statusvalue = ref()
const chooseList = ref([])
const taskTypeList = ref([])
const reviewRow = ref({})
const loading = ref(false);
const queryParams = ref({
    current: 1,
    size: 10,
})
const taskList = ref([])
const total = ref(0)
//字典回显
const formDict = (data, val) => {
    return (data && val) ? proxy.selectDictLabel(data, val) : '--'
}
// 批量标记
const handlerMark = () => {
    dialogVisible.value = true
    var ids = []
    chooseList.value.forEach((item) => {
        ids.push(item.id)
    })
    idsvalue.value = ids.join(',')
}
// 操作日志请求
const handlerLog = (row) => {
    console.log(111);
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
// 批量更改状态
const updateStatus = () => {
    if (idsvalue.value == '') {
        ElMessage({
            message: "请选择异常任务再进行操作",
            type: "error",
        });
    } else {
        abnormalTask.updateStatus({ ids: idsvalue.value }).then(res => {
            if (res.code == 200) {
                dialogVisible.value = false
                ElMessage({
                    message: "标记异常任务成功",
                    type: "success",
                });
                getList()
            }
        })
    }
}
//标记
const handleQuery = (row) => {
    dialogVisible2.value = true
    idsvalue2.value = row.id
    statusvalue.value = row.statusAble == '1' ? '2' : '1'
}
//更改状态
const updateStatus2 = () => {
    abnormalTask.updateStatus({ ids: idsvalue2.value, status: statusvalue.value }).then(res => {
        if (res.code == 200) {
            dialogVisible2.value = false
            ElMessage({
                message: "标记异常任务成功",
                type: "success",
            });
            getList()
        }
    })
}
// 重置
function resetQuery(formEl) {
    formEl.resetFields()
    getList()
}
//搜索
const searchQuery = (row) => {
    getList()
}
const handleCellClick = (row) => {
    if (row.sources == '生产厂家') {
        proxy.$router.push(
            {
                path: '/manufacturer/manufacturerManagement',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources == '供应商') {
        proxy.$router.push(
            {
                path: '/cooperationQualification/supplier',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources == '客户') {
        proxy.$router.push(
            {
                path: '/cooperationQualification/custom',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources == '商品-器械') {
        proxy.$router.push(
            {
                path: '/commodity/instrument',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources == '商品-消杀') {
        proxy.$router.push(
            {
                path: '/commodity/disappear',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources == '商品-食品') {
        proxy.$router.push(
            {
                path: '/commodity/food',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources == '商品-药品') {
        proxy.$router.push(
            {
                path: '/commodity/drug',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources.includes("库存综合查询")){
        proxy.$router.push(
            {
                path: '/inventory/inventoryComprehensiveQuery',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    } else if (row.sources.includes("效期查询")){
        proxy.$router.push(
            {
                path: '/validityQuery',
                query: { id: row.alarmConfig, type: 'abnormalTask' }
            }
        )
    }
}
//异常任务列表
const getList = () => {
    loading.value = true
    // console.log({...queryParams.value});
    const params = {
        ...queryParams.value,
    }
    if(params.taskType == '0'){
        delete params.taskType
    }
    if (params?.createDate?.length) {
        params.beginCreateDate = moment(params?.createDate[0]).format('YYYY-MM-DD')
        params.endCreateDate = moment(params?.createDate[1]).format('YYYY-MM-DD')
        delete params.createDate
    }

    abnormalTask.list(params).then(res => {
        if (res.code == 200) {
            // console.log(res.data.records.sources);
            var aaa = []
            res.data.records.forEach((item) => {
                aaa.push(item.sources)
            })
            console.log(aaa);
            taskList.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}
const handleSelectionChange = key => {
    chooseList.value = key
}
//字典请求
async function dict() {
    diaSelectList.value = await proxy.getDictList('status_able')
    taskTypeList.value = await proxy.getDictList('taskTypeList')
}
dict()
getList()
</script>

<style scoped lang='scss'>
::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.dialog-div {
    margin-left: 40px;
}

.dialog-p {
    line-height: 25px;
    word-wrap: break-word;
}
</style>