<template xmlns="http://www.w3.org/1999/html">
  <div class="order">
    <SearchCom ref="searchRef" @handleQuery="handleQuery"/>
    <el-card body-style="padding-bottom:0" class="box-card">
      <template #header>
        <div class="card-header">
          <span>
            <el-select v-model="chooseFn" class="m-2" clearable placeholder="请选择批量操作项">
              <el-option label="草稿单据批量提交" value="0"/>
              <el-option label="草稿单据批量删除" value="1"/>
            </el-select>
            <el-button :disabled="!multipleSelection.length" class="button" style="margin-left: 13px" type="primary"
                       @click="chooseFnRight()">确认</el-button>
            <el-button class="button" type="primary" @click="newAddTable()">销售下单</el-button></span>
          <span>
            <RightToptipBarV2 className="salesManagement_salesOrder" style="margin-bottom: -18px"
                              @handleRefresh="getSearch"/>
          </span>
        </div>
      </template>
      <div class="item">
        <DragTableColumn v-if="overType && businessType" ref="multipleTableRef" v-loading="loadingFlag"
                         v-model:queryParams="searchForm" :columns="columns" :getList="getSearch"
                         :row-style="functionIndex.tableRowStyle" :tableData="data.tableList"
                         className="salesManagement_salesOrder"
                         @selection-change="handleSelectionChange">
          <template v-slot:operate="{ scopeData }">
            <el-button :disabled="scopeData.row.status != '0' &&
              scopeData.row.status != '4'
              " link type="primary" @click="editTable(scopeData.row, '编辑')"><img src="@/assets/icons/update.png"
                                                                                   style="margin: 0px 5px 0 0"/>编辑
            </el-button>
            <el-button :disabled="scopeData.row.status != '0' &&
              scopeData.row.status != '7'
              " link type="danger" @click="deltable(scopeData.row)"><img src="@/assets/icons/delete.png"
                                                                         style="margin: 0px 5px 0 0"/>删除
            </el-button>
            <el-button link type="primary" @click="detailTable(scopeData.row)"><img src="@/assets/icons/detail.png"
                                                                                    style="margin: 2px 5px 0 0"/>详情
            </el-button>
            <el-button link type="success" @click="logFn(scopeData.row)"><img src="@/assets/icons/review.png"
                                                                              style="margin: 0px 2px 0 0"/>操作记录
            </el-button>

          </template>
        </DragTableColumn>
        <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                       :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
                       layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                       @size-change="handleQuery"
                       @current-change="handleQuery"/>
      </div>
    </el-card>
    <el-dialog v-model="dialogTableVisible" :before-close="data.title == '查看详情' ? null : handleClose"
               :title="data.title"
               width="80%">
      <h4 slot="title" class="stateTitle">
        {{ data.editState == null ? "" : echo1(data.editState) }}
      </h4>
      <orderDialog ref="orderDialogRef" v-model:addGoods="addGoods" v-model:data="data" v-model:footForm="footForm"
                   v-model:formInline1="formInline1" v-model:formInline2="formInline2" @GoodsRule="GoodsRule"
                   @allPrice="allPrice"
                   @clientList="clientList" @delFn="delFn" @delGoods="delGoods" @handPrice="handPrice"
                   @serialList="serialList"/>
      <template #footer>
        <span v-if="data.title != '查看详情'" class="dialog-footer">
          <el-button @click="cancalFn()">
            取消
          </el-button>
          <el-button v-if="data.editStrs.orderHeader.status == 0 ||
            data.editStrs.orderHeader.status == '' ||
            data.editStrs.orderHeader.status == null
            " @click="submitDraft(null)">保存草稿</el-button>
          <el-button type="primary" @click="submitDraft('submit')">
            提交审核
          </el-button>
        </span>
        <span v-else class="dialog-footer">
          <el-button @click="dialogTableVisible = false">
            取消
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible2" title="添加商品" width="80%">
      <el-input v-model="addGoods.value" :prefix-icon="Search" placeholder="请输入商品名称/拼音码/自编码"
                style="width: 300px"/>
      <el-button style="margin-left: 12px" type="primary" @click="GoodsSearch()">搜索
      </el-button>
      <addGoodsTable v-if="goodFlag" v-model:addGoods="addGoods" v-model:loadingFlag2="loadingFlag2"/>
      <el-pagination v-model:current-page="addGoods.pageNum" v-model:page-size="addGoods.pageSize" :background="true"
                     :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="addGoods.total"
                     layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 20px"
                     @size-change="GoodsSearch"
                     @current-change="GoodsSearch"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible2 = false">取消</el-button>
          <el-button type="primary" @click="addRight()">
            添加
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleLog" title="操作记录" width="40%">
      <div v-loading="loadingFlag3">
        <logQuery ref="childLog"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisibleLog = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleFn" :before-close="chooseNum == 2 ? handleClosey : null" title="批量操作提示"
               width="30%">
      <div v-if="chooseNum == 1">
        <p style="margin: 15px 0 35px 0;text-align: center;font-size: 16px">
          当前选中<b>{{ multipleSelection.length }}</b>个，可批量{{ chooseFn == '0' ? '提交' : '删除' }}的草稿数据<b>{{
            rightChooseList.length
          }}</b>个
        </p>
        <p style="color: red">
          若调整单据范围，请点击“取消”
        </p>
        <p style="color: red">
          若确认继续提交，请点击“确定”
        </p>
      </div>
      <div v-if="chooseNum == 2">
        <p style="margin: 15px 0 35px 0;text-align: center;font-size: 16px">
          操作正在进行中，请勿关闭当前对话框
        </p>
        <el-progress :percentage="numHand" :stroke-width="26" :text-inside="true"/>
      </div>
      <div v-if="chooseNum == 3">
        <p style="margin: 15px 0 15px 0;text-align: center;font-size: 16px;line-height: 25px">
          本次批量{{ chooseFn == '0' ? '提交' : '删除' }}草稿单据
          <b>{{
              rightChooseList.length
            }}</b> 个
        </p>
        <p style="margin: 15px 0 35px 0;text-align: center;font-size: 15px;line-height: 25px">
          {{ chooseFn == '0' ? '下单' : '删除' }}成功：<b>{{ resData.suc }}</b>个 <span style="margin-left: 10px">{{
            chooseFn ==
            '0' ? '下单' : '删除'
          }}失败：<b>{{
              resData.err
            }}</b>个</span>
        </p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button v-if="chooseNum != 2" @click="dialogVisibleFn = false">取消</el-button>
          <el-button v-if="rightChooseList.length > 0 && chooseNum != 2" type="primary"
                     @click="chooseNum == 1 ? rightChooseFn() : dialogVisibleFn = false">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <sellDetails ref="childRef" :nums="2" @againFn="againFn"/>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watch, watchEffect,} from "vue";
import {manageApi} from "@/api/model/salesManagement/index";
import {ClickOutside as vClickOutside, ElLoading, ElMessage, ElMessageBox} from 'element-plus'
import {Search} from "@element-plus/icons-vue";
import LogQuery from "@/components/detailsForm/logQuery.vue";
import {switchGoods, switchSend, switchTable} from './switch'
import {addGoodsList, allFormInline, columnsList, dataList} from './orderList'
import orderDialog from './components/orderDialog.vue'
import addGoodsTable from './components/addGoodsTable.vue'
import {functionIndex} from "@/views/salesManagement/functionIndex";
import {regionData} from "element-china-area-data";
import sellDetails from "@/components/detailsForm/sellDetails.vue";
import {uuid} from "vue-uuid";

const orderDialogRef = ref(null)
const {proxy} = getCurrentInstance();
const dialogVisibleLog = ref(false);
const LIU = vClickOutside
/**
 * 仓库
 */
// const store = useStore();
/**
 * 路由对象
 */
const dialogTableVisible = ref(false);

const dialogVisible2 = ref(false);
const loadingFlag = ref(false);
const loadingFlag2 = ref(false);
const loadingFlag3 = ref(false);
const childLog = ref(null);
const chooseFn = ref()
const regionDatas = ref(regionData)
const dialogVisibleFn = ref(false)
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */

const formInline1 = reactive(allFormInline(1));
const formInline2 = reactive(allFormInline(2));
const footForm = reactive({
  num: 0,
  price: 0,
  discount: 0,
});
const data = reactive(dataList());
const addGoods = reactive(addGoodsList());
const submitDraft = (status) => {
  submitDrafts(orderDialogRef.value.creatform1, orderDialogRef.value.creatform2, status)
}
const childRef = ref(null)
const detailTable = (row) => {
  childRef.value.details(row.id, '', true, true);
}
const againFn = (row) => {
  editTable(row, '单据复制')
  allSubmit(null)
}
const submitDrafts = async (formEL1, formEL2, status) => {
  if (!formEL1 || !formEL2) return;
  let flag1 = false;
  let flag2 = false;
  let flag3 = false;
  let flag4 = false;
  let flag5 = false;
  await formEL1.validate((valid, fields) => {
    if (valid) {
      flag1 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          orderDialogRef.value["creatform1"].scrollToField(propName)
        }
      })
      if (orderDialogRef.value.activeNames.indexOf('1') == -1) {
        orderDialogRef.value.activeNames.push('1')
      }
    }
  });
  await formEL2.validate((valid, fields) => {
    if (valid) {
      flag2 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          orderDialogRef.value["creatform2"].scrollToField(propName)
        }
      })
      if (orderDialogRef.value.activeNames.indexOf('4') == -1) {
        orderDialogRef.value.activeNames.push('4')
      }
    }
  });
  if (addGoods.allGoods.length != 0) {
    flag3 = true;
  }
  flag4 = !addGoods.allGoods.some((i) => i.total == 0);

  if (footForm.discount >= 0) {
    flag5 = true;
  }

  if (!flag3) {
    ElMessage.error("请添加商品");
  }
  if (!flag4) {
    ElMessage.error("请输入商品数量或单价");
  }
  if (!flag5) {
    ElMessage.error("折扣金额不能大于总价");
  }

  if (flag1 && flag2 && flag3 && flag4 && flag5) {
    allSubmit(status)
  }
};
const allSubmit = (status) => {
  let newGoodsList = switchGoods(addGoods, data.editState, data.title);
  switchSend(formInline1, formInline2, status, data.title, data.editStrs, newGoodsList, footForm)
      .then((res) => {
        if (res.code == 200) {
          if (status == "submit") {
            ElMessage.success("提交审核成功");
          } else {
            if (data.title == '单据复制') {
              ElMessage.success("单据复制成功");
              childRef.value.dialogTableVisible = false
            } else {
              ElMessage.success("保存成功");
            }
          }
          handleQuery();
          dialogTableVisible.value = false;
        } else {
          if (status == "submit") {
            ElMessage.error(res.msg);
          } else {
            ElMessage.error(res.msg);
          }
        }
      });
}
const handPrice = (scope) => {
  if (typeof addGoods.allGoods[scope.$index].price.str == "object") {
    addGoods.allGoods[scope.$index].price.str = 0;
  }
  if (typeof addGoods.allGoods[scope.$index].num.str == "object") {
    addGoods.allGoods[scope.$index].num.str = 0;
  }
  let comunit = parseInt(addGoods.allGoods[scope.$index].num.str /
      addGoods.allGoods[scope.$index].commodity.ratio)
  let basic = addGoods.allGoods[scope.$index].num.str %
      addGoods.allGoods[scope.$index].commodity.ratio
  // addGoods.allGoods[scope.$index].pieceNumber = comunit + '箱' + basic + '盒'
  if (basic != 0) {
    addGoods.allGoods[scope.$index].pieceNumber =
        comunit.toString()
        + addGoods.allGoods[scope.$index].commodity.completeUnit
        + basic.toString()
        + addGoods.allGoods[scope.$index].commodity.basicUnit
  } else if (basic == 0) {
    addGoods.allGoods[scope.$index].pieceNumber =
        comunit.toString()
        + addGoods.allGoods[scope.$index].commodity.completeUnit
  }

  addGoods.allGoods[scope.$index].total =
      (addGoods.allGoods[scope.$index].price.str *
          addGoods.allGoods[scope.$index].num.str);
  allPrice();
};
const allPrice = () => {
  let num = 0;
  let price = 0;
  let discount = 0;
  addGoods.allGoods.forEach((item) => {
    num += item.num.str;
    price += item.total;
  });
  discount = price - formInline1.n19;
  footForm.num = num;
  footForm.price = price.toFixed(2);
  footForm.discount = discount.toFixed(2);
  if (discount < 0 && footForm.price != 0) {
    ElMessage.error("折扣金额不能大于总金额");
  }
};
watch(
    () => formInline1.n13,
    () => {
      formInline1.n14 = "";
      handleList();
    }
);
watch(
    () => formInline1.n1,
    () => {
      if (formInline1.n1 && formInline1.n14) {
        getMO()
      }
    }
);
watch(
    () => formInline1.n14,
    () => {
      if (formInline1.n1 && formInline1.n14) {
        getMO()
      }
    }
);
watch(() => formInline1.n7, () => {
  if (formInline1.n7 == '0' || formInline1.n7 == '3') {
    formInline1.n8 = ''
  }
})
watch(() => formInline1.n22, () => {
  if (!formInline1.n22) {
    formInline1.n21 = ''
  }
})
watch(
    () => formInline1.n1,
    (newValue) => {
      manageApi.powerType({id: newValue}).then((res) => {
        if (res.code == 200) {
          data.powerType = res.data.delegateDTOS;
          formInline1.n2 = "";
          if (data.powerType.length == 1) {
            formInline1.n2 = data.powerType[0].id;
          }
        }
      });
    }
);
watch(
    () => formInline1.n2,
    () => {
      addGoods.tableData = []
      addGoods.allGoods = []
    }
);
const goodFlag = ref(false)
const getMO = () => {
  manageApi.getMO({
    handleBy: formInline1.n14,
    customer: formInline1.n1
  }).then(res => {
    if (res.code == 200) {
      let flag = false
      for (let key in formInline2) {
        if (formInline2[key] == '' || formInline2[key] == []) {
        } else {
          flag = true
        }
      }
      if (res.data.records.length > 0 && data.title != '编辑') {
        if (flag) {
          ElMessageBox.confirm(
              '订单配置已匹配到有效模板，是否覆盖当前订单配置信息?',
              '提示',
              {
                confirmButtonText: '是，覆盖',
                cancelButtonText: '否，不覆盖',
                type: 'warning',
              }
          )
              .then(() => {
                console.log(res.data.records[0])
                formInline2.n1 = res.data.records[0].stampOption
                formInline2.n2 = res.data.records[0].cargoOwnerOption
                formInline2.n3 = res.data.records[0].packingOption
                formInline2.n4 = res.data.records[0].isConsolidation
                formInline2.n5 = res.data.records[0].receiptReturnOption.split(',')
                formInline2.n6 = res.data.records[0].retainedOption ? '1' : '0'
                formInline2.n7 = res.data.records[0].inspectionReportOption.split(',')
                formInline2.n8 = res.data.records[0].qualityReceiptReturn.split(',')
                formInline2.n9 = res.data.records[0].followGenOption.split(',')
                formInline2.n10 = res.data.records[0].remark
                ElMessage.success('订单配置模板已自动填入')
              })
              .catch(() => {

              })
        } else {
          formInline2.n1 = res.data.records[0].stampOption
          formInline2.n2 = res.data.records[0].cargoOwnerOption
          formInline2.n3 = res.data.records[0].packingOption
          formInline2.n4 = res.data.records[0].isConsolidation
          formInline2.n5 = res.data.records[0].receiptReturnOption.split(',')
          formInline2.n6 = res.data.records[0].retainedOption ? '1' : '0'
          formInline2.n7 = res.data.records[0].inspectionReportOption.split(',')
          formInline2.n8 = res.data.records[0].qualityReceiptReturn.split(',')
          formInline2.n9 = res.data.records[0].followGenOption.split(',')
          formInline2.n10 = res.data.records[0].remark
          ElMessage.success('订单配置模板已自动填入')
        }
      }
    }
  })
}
const addRight = () => {
  addGoods.addList.forEach((items) => {
    items.num = {
      flag: false,
      str: 0,
    };
    items.price = {
      flag: false,
      str: 0,
    };
    items.uuid = uuid.v1();
    items.total = 0;
    items.pieceNumber = parseInt(
        items.num.str /
        parseInt(items.commodity.ratio)
    );
    items.openableQuantity = items.inventory;
    items.validityTime = new Date(items.produceDate);
    items.validityTime.setMonth(
        items.validityTime.getMonth() +
        parseInt(items.commodity.validityTime)
    );
    addGoods.allGoods.push({...items});
  })
  ElMessage.success("添加成功");
  dialogVisible2.value = false;
  goodFlag.value = false
};
const cancalFn = () => {
  ElMessageBox.confirm("信息未保存确认取消吗？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        dialogTableVisible.value = false
      })
      .catch(() => {
        // catch error
      });
}
const overType = ref(null)
const businessType = ref(null)
const statusType = ref(null)
const dict = async () => {
  if (localStorage.getItem('orderTableType')) {
    let obj = JSON.parse(localStorage.getItem('orderTableType'))
    overType.value = obj.overType
    businessType.value = obj.businessType
    statusType.value = obj.statusType
    console.log(obj)
  } else {
    overType.value = await proxy.getDictList("erp_clearingForm");
    businessType.value = await proxy.getDictList("erp_business")
    statusType.value = await proxy.getDictList("status_sales")
    let obj = {
      overType: overType.value,
      businessType: businessType.value,
      statusType: statusType.value
    }
    localStorage.setItem('orderTableType', JSON.stringify(obj))
  }
}
dict()

const columns = ref(columnsList(businessType, overType, statusType))
const searchRef = ref(null);
const searchForm = ref({
  status: "",
  n5: "",
  n7: "",
  n14: "",
  size: data.pageSize,
  current: data.pageNum
});
const getSearch = () => {
  setTimeout(() => {
    console.log(searchForm.value)
    searchRef.value.searchForm.status = searchForm.value.status
    searchRef.value.searchForm.n5 = searchForm.value.n5
    searchRef.value.searchForm.n7 = searchForm.value.n7
    searchRef.value.searchForm.n14 = searchForm.value.n14
    handleQuery()
  })
}
const echo1 = (row) => {
  return statusType.value.find(item => item.value == row.status).name
};
const logFn = async (row) => {
  loadingFlag3.value = true
  dialogVisibleLog.value = true;
  if (childLog.value) {
    childLog.value.data.list = [];
  }
  const auditList = await manageApi.auditList({"salesOrder.id": row.id})
  const logList = await manageApi.logList({masterId: row.id})
  if (auditList.code == 200 && logList.code == 200) {
    childLog.value.timeFns(auditList.data.records, logList.data.records);
  } else {
    ElMessage.error('加载失败')
  }
  loadingFlag3.value = false
};
const GoodsSearch = () => {
  loadingFlag2.value = true;
  manageApi
      .goodsSearch({
        delegateId: formInline1.n2,
        // delegateId: "738220f0-f9dd-11ed-a258-99610d1bc8a9",
        searchName: addGoods.value,
        size: addGoods.pageSize,
        current: addGoods.pageNum,
      })
      .then((res) => {
        if (res.code == 200) {
          console.log(res.data);
          addGoods.total = res.data.total;
          addGoods.tableData = res.data.records;
          addGoods.tableData.forEach((item) => {
            if (!item.inventoryBalance) {
              item.inventoryBalance = 0;
            }
            if (!item.inventory) {
              item.inventory = 0;
            }
          });
          console.log(res.data.records);
        } else {
          ElMessage.error(res.msg);
        }
        loadingFlag2.value = false;
      });
};
const delFn = (selection) => {
  addGoods.delList = selection;
};
const multipleTableRef = ref()
//选中的数据
const multipleSelection = ref([])
const chooseNum = ref(1)
//可提交的草稿数据
const rightChooseList = ref([])
//百分比
const numHand = ref(0)
//提交结果成功失败数据
const resData = ref({
  suc: 0,
  err: 0
})
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}
//初始化弹窗
const chooseFnRight = () => {
  if (chooseFn.value === '0') {
    if (multipleSelection.value.length > 0) {
      rightChooseList.value = []
      chooseNum.value = 1
      numHand.value = 0
      multipleSelection.value.forEach(item => {
        if (item.status == '0') {
          rightChooseList.value.push(item)
        }
      })
      dialogVisibleFn.value = true
    } else {
      ElMessage.error('请先选择')
    }
  } else if (chooseFn.value === '1') {
    if (multipleSelection.value.length > 0) {
      rightChooseList.value = []
      chooseNum.value = 1
      numHand.value = 0
      multipleSelection.value.forEach(item => {
        if (item.status == '0') {
          rightChooseList.value.push(item)
        }
      })
      dialogVisibleFn.value = true
    } else {
      ElMessage.error('请先选择')
    }
  } else {
    ElMessage.warning('请先选择批量操作项')
  }
}
//开始走进度条
const rightChooseFn = () => {
  let arr = []
  chooseNum.value = 2
  rightChooseList.value.forEach(item => {
    arr.push(item.id)
  })
  let str = arr.join(',')
  let ranNum = Math.floor(Math.random() * (70 - 40 + 1)) + 40;
  let handNum = setInterval(() => {
    numHand.value++
    if (numHand.value == ranNum) {
      clearInterval(handNum)
      submitList(str)
    }
  }, 0)
  // dialogVisibleFn.value = false
}

//开始发起请求
const submitList = (str) => {
  console.log(str)
  if (chooseFn.value === '0') {
    manageApi.subAll({
      salesOrderIds: str
    }).then((res) => {
      if (res.code == 200) {
        let handNum = setInterval(() => {
          numHand.value++
          if (numHand.value == 100) {
            clearInterval(handNum)
            redarkFn(res.data)
          }
        })
      } else {
        redarkFn(false)
      }
    })
  } else if (chooseFn.value === '1') {
    manageApi.delManage({
      ids: str
    }).then((res) => {
      if (res.code == 200) {
        let handNum = setInterval(() => {
          numHand.value++
          if (numHand.value == 100) {
            clearInterval(handNum)
            redarkFn(res.data)
          }
        })
      } else {
        redarkFn(false)
      }
    })
  }
}
//提交后的数据分析
const redarkFn = (flag) => {
  if (chooseFn.value === '0') {
    resData.value.suc = 0
    resData.value.err = 0
    if (flag) {
      flag.forEach((item) => {
        if (item.status == 0) {
          resData.value.err++
        } else {
          resData.value.suc++
        }
      })
      multipleTableRef.value?.clearSelection()
      ElMessage.success('提交成功')
      handleQuery()
    } else {
      resData.value.suc = 0
      resData.value.err = rightChooseList.value.length
      ElMessage.error('提交失败')
    }
    chooseNum.value = 3
  } else if (chooseFn.value === '1') {
    resData.value.suc = 0
    resData.value.err = 0
    if (flag) {
      resData.value.suc = rightChooseList.value.length
      resData.value.err = 0
      multipleTableRef.value?.clearSelection()
      ElMessage.success('删除成功')
      handleQuery()
    } else {
      resData.value.suc = 0
      resData.value.err = rightChooseList.value.length
      ElMessage.error('删除失败')
    }
    chooseNum.value = 3
  }
}
const handleList = () => {
  manageApi
      .handleType({
        wnid: formInline1.n13,
      })
      .then((res) => {
        console.log(res)
        if (res.code == 200) {
          data.handle = res.data;
          if (res.data.length == 1) {
            data.myFlag = true;
            formInline1.n14 = res.data[0].handledBy.id;
            return;
          }
          data.myFlag = false;
        }
      });
};
const handleClosey = (done) => {
  ElMessageBox.confirm("任务正在进行中，无法取消", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {

      })
      .catch(() => {
        // catch error
      });
};
const handleClose = (done) => {
  ElMessageBox.confirm("信息未保存确认取消吗", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        done()
        dialogTableVisible.value = false
      })
      .catch(() => {
        // catch error
      });
};
const newAddTable = () => {
  dialogTableVisible.value = true;
  data.editStrs = {
    orderHeader: {
      status: 0,
    },
  };

  data.title = "创建销售订单";
  for (let key in formInline1) {
    formInline1[key] = "";
    if (key == "n19") {
      formInline1[key] = 0;
    } else if (key == "n15") {
      formInline1[key] = null;
    } else if (key == "n20" || key == "n22") {
      formInline1[key] = false;
    }
  }
  for (let key in formInline2) {
    formInline2[key] = "";
    if (key == "n5" || key == "n7" || key == "n8" || key == "n9") {
      formInline2[key] = [];
    }
  }
  footForm.num = 0;
  footForm.price = 0;
  footForm.discount = 0;
  addGoods.tableData = [];
  addGoods.allGoods = [];
  addGoods.delList = [];
  addGoods.value = "";
  formInline1.n16 = JSON.parse(localStorage.getItem("USER_INFO")).content;
  data.editState = null;
  addGoods.total = 0;
  addGoods.pageSize = 10;
  addGoods.pageNum = 1;
  addGoods.shops = "";
  addGoods.addList = null;
};

const clientList = () => {
  manageApi
      .clientType({
        current: data.clientType.pageNum,
        size: 1000,
        enterpriseName: data.clientType.value,
      })
      .then((res) => {
        // 筛选非禁用客户&&审核完成客户
        // let newarr = [];
        // res.data.records.forEach((record) => {
        // 	if (record.customLabel == 2 && record.status == 3) {
        // 		newarr.push(record);
        // 	}
        // });
        // data.clientType.type=newarr
        data.clientType.total = res.data.total;
        data.clientType.type = res.data.records;
      });
};
const serialList = () => {
  manageApi
      .serialType({
        current: data.serial.pageNum,
        size: 1000,
        status: 1,
        isStop: 0,
        warehouseNumber: data.serial.value,
      })
      .then((res) => {
        data.serial.total = res.data.total;
        data.serial.type = res.data.records;
      });
};
const changeTime = (time) => {
  if (time) {
    let newTime = new Date(time)
    newTime = newTime.setDate(newTime.getDate() + 1);
    newTime = functionIndex.transformTimestampSearch(newTime)
    return newTime
  } else {
    return null
  }
}
const handleQuery = () => {
  loadingFlag.value = true;
  manageApi
      .manageSearch({
        size: data.pageSize,
        current: data.pageNum,
        queryStartDocDate: searchRef.value?.searchForm.n1[0],
        queryEndDocDate: searchRef.value ? changeTime(searchRef.value.searchForm.n1[1]) : null,
        queryStartCollectionPeriod: searchRef.value?.searchForm.n2[0],
        queryEndCollectionPeriod: searchRef.value ? changeTime(searchRef.value.searchForm.n2[1]) : null,
        queryCustomerEnterpriseName: searchRef.value?.searchForm.n3,
        queryCommodityTradeName: searchRef.value?.searchForm.n4,
        settlementMethod: searchRef.value?.searchForm.n5,
        status: searchRef.value?.searchForm.status,
        inventoryStatus: searchRef.value?.searchForm.n7,
        queryPreparedByName: searchRef.value?.searchForm.n8,
        queryWarehouseNumber: searchRef.value?.searchForm.n9,
        queryHandledByName: searchRef.value?.searchForm.n10,
        selfRate: searchRef.value?.searchForm.n11,
        docNum: searchRef.value?.searchForm.n12,
        queryAuditorByName: searchRef.value?.searchForm.n13,
        businessType: searchRef.value?.searchForm.n14,
      })
      .then((res) => {
        if (res.code == 200) {
          data.total = res.data.total;
          data.tableList = res.data.records;
        }
        loadingFlag.value = false;
      });
};
const deltable = (row) => {
  ElMessageBox.confirm("确认删除此订单吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        ElLoading.service();
        manageApi.delManage({ids: row.id}).then((res) => {
          if (res.code == 200) {
            ElMessage.success("删除成功");

            handleQuery();
          } else {
            ElMessage.error("删除失败");
          }
          const loadingInstance = ElLoading.service();
          loadingInstance.close();
        });
      })
      .catch(() => {
      });
};
const clearForm = () => {
  setTimeout(() => {
    if (orderDialogRef.value) {
      orderDialogRef.value.creatform1.clearValidate()
      orderDialogRef.value.creatform2.clearValidate()
    } else {
      clearForm()
    }
  })
}
const editTable = (row, title) => {
  data.title = title;
  clearForm()
  if (data.title != '单据复制') {
    ElLoading.service();
  }
  data.editState = row;
  manageApi.detailManage({salesOrderId: row.id}).then((res) => {
    if (res.code == 200) {
      if (res.data.orderHeader.status == 3) {
        manageApi.warehouseList({docNum: res.data.orderHeader.docNum}).then((res) => {
          console.log(res)
          data.warehouseList = res.data.records
        })
      }
      addGoods.allGoods = res.data.orderFormList;
      addGoods.allGoods?.forEach((item) => {
        item.commodity.originPlace = item.originPlace;
        item.commodity.basicUnit = item.basicUnit;
        item.commodity.ratio = item.unitLoading;
        item.supplier = {
          enterpriseName: item.supplierName
        }
        item.commodity.taxRate = item.taxRate;
        item.commodity.storageTemperature = item.storageTemperature;
        item.commodity.batchNumber = item.approvalNumber;
        item.commodity.grugsType = item.grugsType;
        item.commodity.validityTime = item.validityTime;
        item.validityTime = item.expirationTime;
        item.price = {
          flag: false,
          str: item.unitPrice ? item.unitPrice : 0,
        };
        item.num = {
          flag: false,
          str: item.quantity ? item.quantity : 0,
        };
        item.manufacture = item.manufacturer;
        item.batchNumber = item.batchNum;
        item.total = item.amountMoney ? item.amountMoney : 0;
        item.unitPrice = item.costUnitPrice;
      });
      data.editStrs = res.data;
      formInline2.n1 = res.data.orderConfig?.stampOption;
      formInline2.n2 = res.data.orderConfig?.cargoOwnerOption;
      formInline2.n3 = res.data.orderConfig?.packingOption;
      formInline2.n4 = res.data.orderConfig?.isConsolidation;
      formInline2.n5 =
          res.data.orderConfig?.receiptReturnOption.split(",");
      formInline2.n6 = res.data.orderConfig?.retainedOption;
      formInline2.n7 =
          res.data.orderConfig?.inspectionReportOption.split(",");
      formInline2.n8 =
          res.data.orderConfig?.qualityReceiptReturn.split(",");
      formInline2.n9 = res.data.orderConfig?.followGenOption.split(",");
      formInline2.n10 = res.data.orderConfig?.remark;
      if (data.title != '单据复制') {
        dialogTableVisible.value = true;
      }
    }
    if (data.title != '单据复制') {
      const loadingInstance = ElLoading.service();
      loadingInstance.close();
    }
  });
  const SwichList = switchTable(row)
  for (let key in SwichList) {
    formInline1[key] = SwichList[key]
  }
  footForm.price = row.totalAmount;
  footForm.num = row.totalQuantity;
  footForm.discount = row.totalAmountAfterDiscount;
  setTimeout(() => {
    formInline1.n14 = row.handledBy.id;
  }, 500);
};
const delGoods = () => {
  ElMessageBox.confirm("确认删除此项吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        let newarrs = [];
        addGoods.delList.forEach((item) => {
          if (item.id) {
            newarrs.push(item.id);
          }
        });
        if (newarrs.length > 0) {
          manageApi.delGoods({ids: newarrs.toString()}).then((res) => {
            if (res.code == 200) {
              ElMessage({
                type: "success",
                message: "删除成功",
              });
              let newarr = addGoods.allGoods.filter(
                  (item) =>
                      !addGoods.delList.some(
                          (subItem) => subItem.id ? subItem.id === item.id : subItem.uuid === item.uuid
                      )
              );
              addGoods.allGoods = newarr;
              ElMessage.success('删除成功')
            } else {
              ElMessage.error("删除失败");
            }
          });
        } else {
          let newarr = addGoods.allGoods.filter(
              (item) =>
                  !addGoods.delList.some(
                      (subItem) => subItem.uuid === item.uuid
                  )
          );
          ElMessage.success('删除成功')
          addGoods.allGoods = newarr;
        }
      })
      .catch(() => {
      });
};


const GoodsRule = () => {
  if (formInline1.n2.length == 0) {
    ElMessage.error('请先选择客户代表');
  } else {
    goodFlag.value = true
    dialogVisible2.value = true;
  }
}
// const getTableList = () => {
//   loadingFlag.value = true;
//   manageApi
//       .manageList({
//         size: data.pageSize,
//         current: data.pageNum,
//       })
//       .then((res) => {
//         if (res.code == 200) {
//           data.total = res.data.total;
//           data.tableList = res.data.records;
//         }
//         loadingFlag.value = false;
//       });
// };
const zhuanFN = (arr) => {
  arr.forEach(item => {
    item.value = item.label
    item.children ? zhuanFN(item.children) : null
  })
}
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  let citys = JSON.parse(localStorage.getItem('cityData'))
  if (citys) {
    regionDatas.value = citys
  } else {
    zhuanFN(regionDatas.value)
  }
  handleQuery();
  if (localStorage.getItem('orderType')) {
    let arrType = JSON.parse(localStorage.getItem('orderType'))
    for (let key in arrType) {
      data[key] = arrType[key];
    }
  } else {
    data.clearingFormType = await proxy.getDictList("erp_clearingForm");
    data.invoice = await proxy.getDictList("erp_invoice");
    data.logistics = await proxy.getDictList("erp_logistics");
    data.business = await proxy.getDictList("erp_business");
    data.TripartiteLogistics = await proxy.getDictList(
        "erp_TripartiteLogistics"
    );
    data.contract = await proxy.getDictList("erp_contract");
    data.discount = await proxy.getDictList("erp_discount");
    data.stamp = await proxy.getDictList("erp_stamp");
    data.owner = await proxy.getDictList("erp_owner");
    data.encasement = await proxy.getDictList("erp_encasement");
    data.receipts = await proxy.getDictList("erp_receipts");
    data.testing = await proxy.getDictList("erp_testing");
    data.gather = await proxy.getDictList("erp_gather");
    data.return = await proxy.getDictList("erp_return");
    localStorage.setItem("orderType", JSON.stringify(data))
  }
  clientList();
  serialList();
  data.user = JSON.parse(localStorage.getItem("USER_INFO")).content;
});
onMounted(() => {
  const filtered = regionDatas.value.filter((item) => item.value == '甘肃省' || item.value == '青海省' || item.value == '宁夏回族自治区');
  const remaining = regionDatas.value.filter(item => item.value != '甘肃省' && item.value != '青海省' && item.value != '宁夏回族自治区');
  regionDatas.value = filtered.concat(remaining);
  console.log(regionDatas.value)
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  if (!localStorage.getItem('cityData')) {
    localStorage.setItem('cityData', JSON.stringify(regionDatas.value))
  }
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data),
});
</script>
<style lang="scss" scoped>
@import './style/order';
</style>
