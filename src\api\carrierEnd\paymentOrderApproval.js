import request from '@/utils/request';

export default {
    // 获取预存款充值/付款单支付申请详情
    getPaymentOrderApprovalDetail: (params) => {
        return request.get('/tms/advancepayment/apply/getApplyDetail', params);
    },
    // 审批-预付款充值/付款单支付
    approvalPaymentOrder: (params) => {
        return request.post('/tms/advancepayment/apply/approve', params);
    },
    // 删除预付款/付款单申请
    deletePaymentOrder: (params) => {
        return request.delete('/tms/advancepayment/apply/delete', params);
    },
    // 货主下拉接口
    getApplyCompanySelect: (id) => {
        return request.get('/tms/advancepayment/apply/getApplyCompanySelect?carrier.id=' + id);
    },
    // 开票审批详情
    getInvoiceApprovalDetail: (params) => {
        return request.get('/tms/payment/invoiceApply/getApplyDetail', params);
    },
    // 查询未发起支付申请的付款单
    getUnApplyPaymentOrder: (params) => {
        return request.get('/cost/payment/getNoPayApplyList', params);
    }
};
