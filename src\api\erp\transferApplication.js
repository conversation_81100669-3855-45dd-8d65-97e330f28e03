/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-11 10:08:03
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-09 10:17:33
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\manufacturerManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
  getList: function (inputForm) {
    return http.get('/erp/transfer/erpTransfer/list',inputForm)
  },

  getIdOrder: function (inputForm) {
    return http.get('/erp/transfer/erpTransfer/transferDetailById',inputForm)
  },
  
  save: function (data) {
    return http.post('/erp/transfer/erpTransfer/createTransfer',data)
  },
  getReview: function (inputForm) {
    return http.get('/erp/transfer/erpTransferApproval/list',inputForm)
  },
  getOut: function (inputForm) {
    return http.get('/erp/procure/retreat/erpPurchaseRetreatOutbound/list',inputForm)
  },
  delete:function (ids) {
    return http.delete('/erp/transfer/erpTransfer/delete',ids)
  },
  confirm: function (data) {
    return http.post('/erp/transfer/erpTransfer/confirm',data)
  },
  
}
