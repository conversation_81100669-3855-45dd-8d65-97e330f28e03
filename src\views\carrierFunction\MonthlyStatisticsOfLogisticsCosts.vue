<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="货主" prop="companyId" style="width: 230px">
                    <el-select v-model="queryForm.companyId" class="w-full" clearable filterable placeholder="请选择货主" @change="handleChangesCompanyId">
                        <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="月度" prop="billDate" style="width: 250px">
                    <el-date-picker v-model="queryForm.billDate" :shortcuts="monthShortcuts" end-placeholder="结束月份" range-separator="至" start-placeholder="开始月份" type="monthrange" unlink-panels value-format="YYYY-MM" @change="handleChangesCompanyId"> </el-date-picker>
                </el-form-item>
                <el-form-item label="汇款时间" prop="remitTime" style="width: 310px">
                    <el-date-picker v-model="queryForm.remitTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleChangesCompanyId"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="核销日期" prop="reversedTime" style="width: 310px">
                    <el-date-picker v-model="queryForm.reversedTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleChangesCompanyId"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="未收金额" prop="unpaidCost">
                    <div style="width: 194px">
                        <el-select v-model="queryForm.preUnpaidCostOption" clearable style="width: 45%">
                            <el-option v-for="(item, index) in preUnpaidCostOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
                        </el-select>
                        <el-input v-model="queryForm.unpaidCost" class="unpaidCost" clearable placeholder="请输入" style="width: 55%" @keyup.enter="handleChangesCompanyId" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="结算公司" prop="settlementCompanyId" style="width: 250px">
                    <el-select v-model="queryForm.settlementCompanyId" class="w-full" clearable filterable placeholder="请选择结算公司" @change="handleChangesCompanyId">
                        <el-option v-for="item in settlementCompanyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleChangesCompanyId" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <div class="flex justify-around">
                <el-statistic :precision="2" :value="overview.totalReceivableCost" :value-style="{ color: '#5670FE' }" group-separator="," title="应收总金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalPaidCost" :value-style="{ color: '#1ACD7E' }" group-separator="," title="已收总金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalUnpaidCost" :value-style="{ color: '#F4AC00' }" group-separator="," title="未收总金额"></el-statistic>
            </div>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10">
                <el-button :disabled="!dataList || dataList.length === 0" icon="el-icon-download" type="warning" @click="exportAll">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="monthlyStatisticsOfLogisticsCosts" @queryTable="getList" />
            </div>
            <column-table ref="monthlyStatisticsOfLogisticsCosts" v-loading="loading" :columns="columns" :data="dataList" :maxHeight="tableHeight" :show-index="true" :show-summary="true" element-loading-text="加载中...">
                <template #paymentDocType="{ row }">
                    <span>{{ formatDictionaryData('paymentReceiptType', row.paymentDocType) }}</span>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import {selectDictLabel} from '@/utils/dictLabel';
import {downloadNoData} from '@/utils';
import moment from 'moment';
import monthlyStatisticsOfLogisticsCosts from '@/api/carrierEnd/monthlyStatisticsOfLogisticsCosts';
import customerPaymentDocument from '@/api/carrierEnd/customerPaymentDocument';
import settlementCompany from '@/api/carrierEnd/settlementCompany';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'MonthlyStatisticsOfLogisticsCosts',
    components: {
        RightToolbar,
        SearchButton,
        ColumnTable
    },
    data() {
        return {
            queryForm: {
                current: 1,
                size: 10,
                paymentDocType: '3',
                companyId: undefined,
                billDate: [],
                remitTime: [],
                reversedTime: [],
                preUnpaidCostOption: undefined,
                unpaidCost: undefined
            },
            columns: [
                { title: '货主', key: 'companyName', minWidth: '170px', columnShow: true ,showOverflowTooltip: true},
                { title: '结算公司', key: 'settlementCompanyName', minWidth: '170px', columnShow: true ,showOverflowTooltip: true},
                { title: '月度', key: 'billDate', align: 'center', width: '120px', columnShow: true },
                { title: '付款方式', key: 'paymentDocType', align: 'center', width: '120px', columnShow: true },
                { title: '付款期限（天）', key: 'payRemind', align: 'center', width: '120px', columnShow: true },
                { title: '应收金额', key: 'receivableCost', align: 'center', width: '120px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '已收金额', key: 'paidCost', align: 'center', width: '120px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '未收金额', key: 'unpaidCost', align: 'center', width: '120px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '汇款时间', key: 'remitTime', align: 'center', width: '120px', columnShow: true },
                { title: '核销日期', key: 'reversedTime', align: 'center', width: '120px', columnShow: true }
                // { title: '付款申请单号', key: 'paymentApplicationNumber', align: 'center', width: '120px', columnShow: true }
            ],
            showSearch: true,
            loading: false,
            dataList: [],
            total: 0,
            overview: {
                totalReceivableCost: 0,
                totalPaidCost: 0,
                totalUnpaidCost: 0
            },
            isShowAll: false,
            shortcuts: setDatePickerShortcuts(),
            tableHeight: 400,
            paymentReceiptType: [],
            customerList: [],
            preUnpaidCostOptions: [
                {
                    label: '大于',
                    value: '> '
                },
                {
                    label: '等于',
                    value: '= '
                },
                {
                    label: '小于',
                    value: '< '
                }
            ],
            monthShortcuts: [
                {
                    text: '当前月',
                    value: () => {
                        return [new Date(), new Date()];
                    }
                },
                {
                    text: '当前年',
                    value: () => {
                        const end = new Date();
                        const start = new Date(end.getFullYear(), 0, 1);
                        return [start, end];
                    }
                },
                {
                    text: '近半年',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 6);
                        return [start, end];
                    }
                },
                {
                    text: '近一年',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 11);
                        return [start, end];
                    }
                }
            ],
			settlementCompanyList:[]
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    created() {
        this.getDict();
        // billDate 默认上一月
        this.queryForm.billDate = [moment(new Date()).subtract(1, 'months').format('YYYY-MM'), moment(new Date()).subtract(1, 'months').format('YYYY-MM')];
        this.handleChangesCompanyId();
		this.getSettlementCompanyList();
    },
    methods: {
		/**
         * 获取结算公司列表
         */
        async getSettlementCompanyList() {
            const res = await settlementCompany.getSettlementCompanySelectList();
            if (res.code === 200) {
                this.settlementCompanyList = res.data || [];
            } else {
                this.settlementCompanyList = [];
            }
        },
        /**
         * 导出
         */
        exportAll() {
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
					// eslint-disable-next-line no-unused-vars
                    const { remitTime, reversedTime, ...params } = this.queryForm;
                    monthlyStatisticsOfLogisticsCosts
                        .export({ filename: '物流费用月度统计.xls', ...params }, '', '', 'blob')
                        .then((res) => {
                            downloadNoData(res, 'application/vnd.ms-excel', '物流费用月度统计.xlsx');
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.paymentReceiptType = await this.getDictList('cost_payment_doc_type');
        },
        getList() {
            this.loading = true;
			// eslint-disable-next-line no-unused-vars
            const { remitTime, reversedTime, billDate, ...params } = this.queryForm;

            // 获取列表数据
            monthlyStatisticsOfLogisticsCosts
                .getList(params)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.tableHeight = window.innerHeight - this.$refs.searchCard.$el.offsetHeight - 320;
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });

            // 获取统计数据
            monthlyStatisticsOfLogisticsCosts
                .getCostStatistics(params)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        const { totalReceivableCost, totalPaidCost, totalUnpaidCost } = res.data;
                        this.overview.totalReceivableCost = totalReceivableCost * 1 || 0.0;
                        this.overview.totalPaidCost = totalPaidCost * 1 || 0.0;
                        this.overview.totalUnpaidCost = totalUnpaidCost * 1 || 0.0;
                    }
                })
                .catch(() => {
                    this.overview.totalReceivableCost = 0.0;
                    this.overview.totalPaidCost = 0.0;
                    this.overview.totalUnpaidCost = 0.0;
                });
        },
        /**
         * 改变订单类型
         */
        handleChangesCompanyId() {
            this.handleQuery();
			// eslint-disable-next-line no-unused-vars
            const { remitTime, reversedTime, companyId, current, size, billDate, ...params } = this.queryForm;
            customerPaymentDocument
                .getCustomerList({ ...params })
                .then((res) => {
                    if (res.code === 200 && res.data?.length > 0) {
                        this.customerList = res.data;
                    } else {
                        this.customerList = [];
                        this.$set(this.queryForm, 'companyId', undefined);
                    }
                })
                .catch(() => {
                    this.customerList = [];
                    this.queryForm.companyId = undefined;
                })
                .finally(() => {
                    this.getList();
                });
        },
        handleQuery() {
            this.queryForm.current = 1;
            const { remitTime, reversedTime, billDate } = this.queryForm;
            // 验证queryTime是否包含两个合法的日期字符串
            const isValidDateRange = remitTime && remitTime.length === 2 && !remitTime.some((date) => date === 'Invalid Date');
            const isValidReversedTime = reversedTime && reversedTime.length === 2 && !reversedTime.some((date) => date === 'Invalid Date');
            const isValidBillDate = billDate && billDate.length === 2 && !billDate.some((date) => date === 'Invalid Date');

            if (isValidDateRange) {
                this.queryForm.startRemitTime = remitTime[0] + ' 00:00:00';
                this.queryForm.endRemitTime = remitTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startRemitTime = undefined;
                this.queryForm.endRemitTime = undefined;
            }
            if (isValidReversedTime) {
                this.queryForm.startReversedTime = reversedTime[0] + ' 00:00:00';
                this.queryForm.endReversedTime = reversedTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startReversedTime = undefined;
                this.queryForm.endReversedTime = undefined;
            }
            if (isValidBillDate) {
                this.queryForm.beginBillDate = billDate[0];
                this.queryForm.endBillDate = billDate[1];
            } else {
                this.queryForm.beginBillDate = undefined;
                this.queryForm.endBillDate = undefined;
            }
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.queryForm.preUnpaidCostOption = undefined;
            this.handleChangesCompanyId();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    thead th {
        border-right: none !important;
    }
    .el-statistic {
        display: flex;
        align-items: baseline;
        gap: 10px;
    }
}
</style>
