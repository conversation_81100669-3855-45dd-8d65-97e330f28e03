<template>
  <el-form ref="creatform" :model="form" :rules="creatRules" label-width="70px">
		<el-form-item label="审批结果" label-width="100px">
			<el-radio-group v-model="form.status">
				<el-radio :label="30">同意</el-radio>
				<el-radio :label="11">驳回</el-radio>
			</el-radio-group>
		</el-form-item>

    <el-form-item :prop="form.status == 11 ? 'idea' : null" label="审批意见" label-width="100px">
      <el-input v-model="form.idea" :rows="5" placeholder="请输入审批意见" style="width: 60%" type="textarea"/>
		</el-form-item>
	</el-form>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import {ElMessage} from "element-plus";
import http from "@/utils/request";

const creatform = ref();
const creatRules = reactive({
  idea: [{required: true, message: "请输入审批意见", trigger: "blur"}],
});
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const emit = defineEmits(["refresh"]);
const form = ref({
	status: null,
	idea: "",
	ids: "",
});
const formSub = (id) => {
	form.value.ids = id;
	if (form.value.status == null) {
		ElMessage.error("请选择审批结果");
	} else if (form.value.status == 11 && form.value.idea == "") {
		ElMessage.error("请输入审批意见");
	} else {
		let params = form.value;
		http.get("/erp/audit/auditApi/auditInfoProcess", params).then((res) => {
			if (res.code == 200) {
				ElMessage.success("审核成功");
				emit("refresh");
			} else {
				ElMessage.error("审核失败,请重试");
			}
		});
	}
};
onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	formSub,
	form
});
</script>
<style lang="scss" scoped>
.col_title {
	color: #333;
	font-size: 18px;
	font-weight: bold;
	position: relative;
	padding-left: 8px;

	&::after {
		content: "";
		display: inline-block;
		width: 3px;
		height: 20px;
		background-color: #2878ff;
		border-radius: 2px;
		position: absolute;
		top: 15px;
		left: 0;
	}
}

.el-form {
	margin-top: 20px
}
</style>
