import request from '@/utils/request'
import http from "@/utils/request";
export default {
    // 运单跟踪-列表
    transOperatRecordList: function (params) {
        return request.get('/tms/orderDrug/transOrderList', params);
    },
    // 订单列表导出
    exportRecordData: function (data,config) {
        return http.post('/tms/orderDrug/exportTransOrderList',data,config)
    },
    // 运单跟踪-明细
    getOperatRecordList: function (params) {
        return request.get('/tms/trans/transOperatRecord/getOperatRecordList', params);
    },
    // 运单跟踪-明细详情
    getOperatRecordDetail: function (params) {
        return request.get('/tms/trans/transOperatRecord/getOperatRecordDetail', params);
    },
    // 获取保温箱码信息
    getIncubatorQrCode: function (orderNo) {
        return request.get('/tms/receipt/incubatorRecord/getIncubatorQrCode/'+orderNo);
    },

}
