import moment from 'moment';
/**
 * 设置日期选择快捷方式
 * @returns {[{text: string, value: (function(): *[])},{text: string, value: (function(): [*,*])},{text: string, value: (function(): [*,*])},{text: string, value: (function(): [*,*])},{text: string, value: (function(): [*,*])},null,null,null]}
 */
export function setDatePickerShortcuts() {
    return [
        {
            text: '无',
            value: () => {
                return [null, null];
            }
        },
        {
            text: '当天',
            value: () => {
                let now = moment(new Date()).format('YYYY-MM-DD');
                return [now, now];
            }
        },
        {
            text: '7天',
            value: () => {
                let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                let end = moment(new Date()).format('YYYY-MM-DD');
                return [start, end];
            }
        },
        {
            text: '30天',
            value: () => {
                let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                let end = moment(new Date()).format('YYYY-MM-DD');
                return [start, end];
            }
        },
        {
            text: '本月',
            value: () => {
                // 当月1日至今
                let start = moment(new Date()).startOf('month').format('YYYY-MM-DD');
                let end = moment(new Date()).format('YYYY-MM-DD');
                return [start, end];
            }
        },
        {
            text: '本年',
            value: () => {
                // 本年1月1日至今
                let start = moment(new Date()).startOf('year').format('YYYY-MM-DD');
                let end = moment(new Date()).format('YYYY-MM-DD');
                return [start, end];
            }
        }
    ];
}
