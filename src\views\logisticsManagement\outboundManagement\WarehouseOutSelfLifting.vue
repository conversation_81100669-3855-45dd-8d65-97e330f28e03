<template>
<div class="app-container" style="background-color: rgb(245, 247, 253); padding: 10px;" v-loading="loading">
  <el-form ref="form" :model="form" :rules="rules" label-width="auto">
    <el-card class="mb10" shadow="never">
      <el-form-item label="货主公司" prop="companyId">
        <el-select v-model="form.companyId" clearable filterable placeholder="请选择货主公司" size="small"
                   style="width: 100%" @change="companyChange">
          <el-option v-for="(item, idx) in ownerList" :key="item.companyId" :label="item.companyName"
                     :value="item.companyId">
            <span style="float: left">{{ item.companyName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.companyId }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出库日期" prop="outDate">
        <el-date-picker v-model="form.outDate" style="width: 100%;" type="date" placeholder="请选择出库日期"
                        format="YYYY-MM-DD" value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item prop="signFile" label="提货人签名">
        <el-upload
            class="avatar-uploader"
            :action="uploadFileUrl"
            :headers="headers"
            :show-file-list="false"
            :on-remove="(file, fileList)=>{return handUploadRemove(file, fileList,3)}"
            :on-success="(res,file)=>{return handUploadSuccess(res,file,3)}"
            :before-upload="beforeUpload">
          <img v-if="form.signFile" :src="form.signFile" class="avatar">
          <el-icon v-else class="avatar-uploader-icon">
            <Plus/>
          </el-icon>
        </el-upload>
      </el-form-item>
    </el-card>
    <el-card class="mb10" shadow="never" v-for="(item, index) in form.outDataList" :key="index">
      <template #header>
        <div style="display: flex;justify-content: space-between;" class="mb5">
          <card-header :title="`出库信息（${index+1}）`" size="mini" :line="false"/>
          <el-button v-if="form.outDataList.length!=1" style="float: right; padding: 5px 10px;"
                     icon="el-icon-delete" type="danger" @click="delWarehousing(index)">移除
          </el-button>
        </div>
        <div class="border-bottom-1"></div>
      </template>
      <el-form-item label="收货客户" :prop="'outDataList.' + index + '.receiveCompanyId'" :rules="[{ required: true, message: '请选择收货客户' }]">
        <el-select v-model="item.receiveCompanyId" clearable filterable placeholder="请选择收货客户" size="small"
                   style="width: 100%">
          <el-option v-for="(com, idx) in receiveCompanyList" :key="idx" :label="com.company"
                     :value="com.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="件数" :prop="'outDataList.' + index + '.goodsNum'"
                    :rules="[{ required: true, message: '件数不能为空' },{pattern: /^(\d)+$/,message: '件数只能输入正整数',trigger: 'blur'},{ validator: (rule, value, callback)=>goodsNumValidator(rule, value, callback), trigger: 'change' }]">
        <el-input v-model="item.goodsNum" placeholder="请输入件数" :min="1" :max="10000"></el-input>
      </el-form-item>
      <el-form-item label="温区" :prop="'outDataList.' + index + '.tempType'"
                    :rules="[{ required: true, message: '请选择温区' }]">
        <el-select v-model="item.tempType" placeholder="请选择温区" style="width: 100%"
                   @change="checkTemperature($event,index)">
          <el-option v-for="(dict, x) in temperatureTypeDicts" :key="x" :label="dict.name"
                     :value="dict.value+''"/>
        </el-select>
      </el-form-item>

      <el-form-item label="出库单号" :prop="'outDataList.' + index + '.outCodeList'">
        <el-row v-for="(outCode, x) in item.outCodeList" :key="x" :style="x!=(item.outCodeList.length-1)?'margin-bottom: 22px;':''" style="width: 100%;">
          <el-col :span="13">
            <el-form-item :prop="'outDataList.' + index + '.outCodeList.' + x + '.outCode'"
                          :rules="[{ validator: (rule, value, callback)=>outCodeListValidator(rule, value, callback), trigger: 'change' }]">
              <el-input v-model="outCode.outCode" placeholder="请输入出库单号">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="1">
            <el-button type="danger" style="padding: 5px 10px;" v-if="item.outCodeList.length != 1"
                       @click="deleteOutCode(index,x)" del>删除
            </el-button>
            <el-button type="primary" style="padding: 5px 10px;" v-if="x==item.outCodeList.length-1 && item.outCodeList && item.outCodeList.length < 9"
                       @click="addOutCode(index)">添加
            </el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :prop="'outDataList.' + index + '.file'" label="货物照片"
                    :rules="[{ required: true, message: '请上传货物照片' }]">
        <el-upload :action="uploadFileUrl" :before-upload="beforeUpload" :headers="headers" :limit="9"
                   :file-list="item.file"
                   :class="{ 'avatar__uploader__vertical__display': hideUploadCompanyLogo(item.file), 'avatar__uploader__vertical': !hideUploadCompanyLogo(item.file) }"
                   :on-remove="(file, fileList)=>{return handUploadRemove(file, fileList,1,index)}"
                   :on-success="(res,file)=>{return handUploadSuccess(res,file,1,index)}" class="upload-demo"
                   :on-preview="handlePicturePaymentVoucher"
                   list-type="picture-card"
                   multiple
        >
          <el-icon>
            <Plus/>
          </el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item v-if="item.tempType && (item.tempType =='2' || item.tempType =='3')" :prop="'outDataList.' + index + '.outboundFile'" label="冷链交接单"
                    :rules="[{ validator: (rule, value, callback)=>receiptFileValidator(rule, value, callback,index,item.tempType), trigger: 'change' },]"
                    :class="item.tempType && item.tempType =='2'?'is-required':''">
        <el-upload :action="uploadFileUrl" :before-upload="beforeUpload" :headers="headers" :limit="9"
                   :file-list="item.outboundFile"
                   :class="{ 'avatar__uploader__vertical__display': hideUploadCompanyLogo(item.outboundFile), 'avatar__uploader__vertical': !hideUploadCompanyLogo(item.outboundFile) }"
                   :on-remove="(file, fileList)=>{return handUploadRemove(file, fileList,2,index)}"
                   :on-success="(res,file)=>{return handUploadSuccess(res,file,2,index)}" class="upload-demo"
                   :on-preview="handlePicturePaymentVoucher"
                   list-type="picture-card"
                   multiple
        >
          <el-icon>
            <Plus/>
          </el-icon>
        </el-upload>
      </el-form-item>
    </el-card>
    <div style="margin-bottom: 20px;text-align: center;" v-if="form.outDataList && form.outDataList.length < 3" >
      <el-button icon="el-icon-plus" style="width: 80%;padding: 10px 10px;" @click="addWarehousing">继续添加</el-button>
    </div>
  </el-form>
  <div class="dialog-footer">
    <el-button @click="onCancel">取 消</el-button>
    <el-button type="primary" @click="onSubmit" v-loading.fullscreen.lock="fullscreenLoading">保 存</el-button>
  </div>
  <el-image-viewer v-if="dialogVisible" :initial-index="0" @close="dialogVisible=false" :url-list="dialogImageUrl"
                   :z-index="9999"/>
</div>

</template>

<script>
import CardHeader from '@/components/CardHeader';
import outboundManagement from '@/api/logisticsManagement/outboundManagement.js'; // 出库登记
import enterpriseCooperation from "@/api/logisticsConfiguration/enterpriseCooperation"; // 合作配置
import {Plus,Delete,Close} from '@element-plus/icons-vue'
import tool from "@/utils/tool";
import moment from 'moment';
export default {
  name: "WarehouseOutSelfLifting",
  props: {
    recordInfo: {
      required: false,
      type: Object
    }
  },
  components: {
    CardHeader,
    Plus,
    Delete,
    Close,
  },
  data() {
    return {
      form: {
        companyId: null, // 货主
        outDate: null, //出库日期

        signFile: null, // 提货人签名
        outDataList: [{
          outCodeList: [
            {outCode: null}
          ], // 出库单号列表
          receiveCompanyId: null, // 收货客户
          tempType: null, // 温区类型
          goodsNum: 0, // 件数
          file: [], // 货物照片
          outboundFile: [], // 冷链交接单
        }],
      },
      rules: {
        companyId: [{required: true, message: '请选择货主公司', trigger: 'change'}],
        outDate: [{required: true, message: '请选择出库日期', trigger: 'change'}],
        signFile: [{required: true, message: '请上传提货人签名', trigger: 'change'}]
      },
      // 货主列表
      ownerList: [],
      headers: {
        Authorization: "Bearer " + tool.cookie.get("TOKEN"),
        ContentType: "multipart/form-data",
        clientType:'pc',
      },
      //文件上传定义
      uploadFileUrl: process.env.VUE_APP_API_UPLOAD, // 上传的图片服务器地址
      fileType: ['png', 'jpg', 'jpeg'],
      fullscreenLoading: false, // 锁屏
      temperatureTypeDicts: [], //温区类型字典
      receiveCompanyList:[], // 收货客户
      loading:false,
      dialogVisible:false,
      dialogImageUrl:[],
    }
  },
  async created() {
    this.getCompanyCarrierSelect()
    /** 产品类型4PL */
    this.temperatureTypeDicts = await this.getDictList('fourpl_out_wh_temp');
    if (Object.values(this.recordInfo).length != 0) {
      this.getDetail();
    }else{
      this.form.outDate = moment(new Date()).format('YYYY-MM-DD');
    }
  },
  computed: {
    hideUploadCompanyLogo() {
      return (data) => {
        return data.length >= 9;
      };

    },
  },
  methods: {
    // 验证件数大于0
    goodsNumValidator(rule, value, callback, index) {
      if (value < 1) {
        callback(new Error('件数必须大于等于1'))
      } else {
        callback()
      }
    },
    // 验证出库单号
    outCodeListValidator(rule, value, callback, index) {
      let reg = /^[\da-z]{1,8}$/;
      if (!reg.test(value)) {
        callback(new Error('请输入出库单号后1~8位数字或字母'))
      } else {
        callback()
      }
    },
    // 验证冷链交接单
    receiptFileValidator(rule, value, callback, index,tempType) {
      if(tempType && tempType =='2'){
        if(value && value.length > 0){
          callback();
        }else{
          callback(new Error('请上传冷链交接单'));
        }
      }else{
        callback();
      }
    },
    // 添加出库信息
    addWarehousing() {
      this.form.outDataList.push({
        outCodeList: [
          {outCode: null}
        ], // 出库单号列表
        receiveCompanyId: null, // 收货客户
        tempType: null, // 温区类型
        goodsNum: 0, // 件数
        file: [], // 货物照片
        outboundFile: [], // 冷链交接单

      });
    },
    // 删除出库信息
    delWarehousing(index) {
      let item = this.form.outDataList[index];
      if(item.id){
        outboundManagement.deleteRecordDetail({
          recordDetailIds: item.id
        }).then(res =>{
          if(res.code == 200){
            this.form.outDataList.splice(index,1)
          }
        })
      }else{
        this.form.outDataList.splice(index,1)
      }
    },
    // 添加出库单号
    addOutCode(index) {
      this.form.outDataList[index].outCodeList.push({outCode: ''})
    },
    // 删除出库单号
    deleteOutCode(index, i) {
      let item = this.form.outDataList[index].outCodeList[i];
      if(item.id){
        outboundManagement.deleteRecordOutCode({
          recordOutCodeIds: item.id
        }).then(res =>{
          if(res.code == 200){
            this.form.outDataList[index].outCodeList.splice(i,1)
          }
        })
      }else{
        this.form.outDataList[index].outCodeList.splice(i,1)
      }

    },
    // 更改温区选择后的操作
    checkTemperature(data, index) {
      this.form.outDataList[index].tempType = data;
    },
    // 预览上传图片
    handlePicturePaymentVoucher(file) {
      this.dialogImageUrl = [];
      this.dialogImageUrl.push(file.url);
      this.dialogVisible = true;
    },
    getDetail() {
      this.loading = true
      outboundManagement.findRecordDetail({
        recordId: this.recordInfo.id
      }).then((response) => {
        if (response.code === 200 && response.data) {
          let info = response.data.record;
          info.outDate = info.outDate.substr(0,10);
          info.companyId = info.company.id;
          info.outDataList = response.data.detailList.map(item=>{
            item.receiveCompanyId = item.receiveCompany.id;

            item.file = item.file?JSON.parse(item.file):[];
            item.outboundFile = item.outboundFile?JSON.parse(item.outboundFile):[];
            if(!item.outCodeList){
              item.outCodeList = [{outCode: null}]
            }
            return {...item}
          })
          this.form = JSON.parse(JSON.stringify(info));
          this.getReceiveCompanySelect();
        }
        this.loading = false
      }).catch(e => {
        this.loading = false
      })
    },
    // 保存
    onSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.fullscreenLoading = true
          let param = {
            record:{
              ...this.form,
              outDate: this.form.outDate + ' 00:00:00',
              company:{
                id:this.form.companyId,
              },
            },
            detailList:[],

          };
          delete param.record.outDataList;
          delete param.record.companyId;
          delete param.record.companyName;
          this.form.outDataList.forEach((item,index)=>{
            let outCodeList = [];
            item.outCodeList.forEach((code,i)=>{
              if(code.outCode){
                outCodeList.push(code);
              }
            })
            param.detailList.push({
              ...item,
              receiveCompany: {
                id: item.receiveCompanyId
              },
              goodsNum: item.goodsNum,
              tempType: item.tempType,
              outCodeList: outCodeList,
              file: JSON.stringify( item.file),
              outboundFile: JSON.stringify( item.outboundFile)
            })

          })
          console.log(param)
          outboundManagement.saveOutRecord(param ).then(response => {
            if(response.code == 200){
              this.msgSuccess(param.record.id?'修改成功':'登记成功')
              this.$emit('callbackMethod')
            }else{
              this.msgError(param.record.id?'修改失败':'登记失败')
            }
            this.fullscreenLoading = false
          }).catch(e => {
            this.fullscreenLoading = false
          })
        }
      });
    },
    // 取消
    onCancel() {
      this.$emit('callbackMethod')
      this.reset()
    },
    reset() {
      this.form = {
        companyId: null, // 货主
        outDate: null, //出库日期
        receiveCompanyId: null, // 收货客户
        signFile: null, // 提货人签名
        outDataList: [{
          outCodeList: [
            {outCode: null}
          ], // 出库单号列表
          receiveCompanyId: null, // 收货客户
          tempType: null, // 温区类型
          goodsNum: 0, // 件数
          file: [], // 货物照片
          outboundFile: [], // 冷链交接单
        }],
      };
      this.resetForm('form')
    },
    companyChange(e){
      this.form.companyId = e;
      this.receiveCompanyList = [];
      this.getReceiveCompanySelect();
      this.form.outDataList.forEach((item,index)=>{
        this.form.outDataList[index].receiveCompanyId = null;
      })
    },
    // 查询所有货主信息
    getCompanyCarrierSelect() {
      // 获取 查询当前货主的承运商列表
      enterpriseCooperation.cooperateSelect({ status: '1' }).then((res) => {
        if (res.code === 200 && res.data) {
          this.ownerList = res.data
        }
      })
    },
    // 根据货主查询收货客户
    getReceiveCompanySelect(){
      // 获取 查询当前货主的承运商列表
      outboundManagement.findAddressBookByCompany({companyId:this.form.companyId}).then((res) => {
        if (res.code === 200 && res.data) {
          this.receiveCompanyList = res.data.map(item=>{
            return {id:item.id, company:item.company};
          })
        }
      })
    },
    // 上传成功回调
    handUploadSuccess(res, file, type, index) {
      if (res.code == 200) {
        if (type == 1) {
          this.form.outDataList[index].file.push({
            fileUrl: res.data.fileUrl,
            extname:res.data.fileType,
            fileName: res.data.fileName,
            url:res.data.fileUrl,
          })
        }
        if (type == 2) {
          this.form.outDataList[index].outboundFile.push({
            fileUrl: res.data.fileUrl,
            extname:res.data.fileType,
            fileName: res.data.fileName,
            url:res.data.fileUrl,
          })
        }
        if (type == 3) {
          this.form.signFile = res.data.fileUrl;
        }
      }
    },
    //删除图片
    handUploadRemove(file, fileList, type, index) {
      let url = file.url || file.response.data.fileUrl
      if (type == 1) {
        let i = this.form.outDataList[index].file.findIndex(item => item.fileUrl == url)
        if (i > -1) {
          this.form.outDataList[index].file.splice(i,1)
        }
      }
      if (type == 2) {
        let i = this.form.outDataList[index].outboundFile.findIndex(item => item.fileUrl == url)
        if (i > -1) {
          this.form.outDataList[index].outboundFile.splice(i,1)
        }
      }
      if (type == 3) {
        this.form.signFile = null;
      }
    },
    // 验证文件格式与大小
    beforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
        if (!isTypeOk) {
          this.msgError(
              `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
          )
          return false
        }
      }
      return true
    }
  }
}
</script>

<style scoped lang="scss">
.mb5{
  margin-bottom: 5px;
}
.avatar__uploader__vertical__display {
  ::v-deep {
    .el-form-item {
      .el-input__inner{
        --el-input-inner-height:calc(var(--el-input-height, 24px))
      }
    }
    .el__upload__tip {
      display: none;
    }

    .el-upload--picture-card {
      display: none;
    }
  }
}
::v-deep {
  .el-upload-list--picture-card {
    .el-upload-list__item-actions {
      width: var(--el-upload-list-picture-card-size);
      height: var(--el-upload-list-picture-card-size);

      span + span {
        margin-left: 10px;
      }
    }

    .el-icon--close-tip {
      display: none !important;
    }
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 128px;
    text-align: center;
  }

  .avatar {
    width: 128px;
    height: 128px;
    display: block;
  }


}
</style>
