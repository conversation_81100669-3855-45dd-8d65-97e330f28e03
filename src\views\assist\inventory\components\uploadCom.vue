<template>
  <div v-loading="loadingFlag">
    <p style="color: red;margin-left: 10px">需文件提示上传成功后确认保存</p>
    <el-form
        ref="creatform"
        :inline="true"
        label-width="90px"
        style="margin-top: 20px"
    >
      <el-form-item label="上传扫描件">
        <el-upload
            v-model:file-list="fileList"
            :before-upload="(file) =>beforeFile(file)"
            :headers="headers" :limit="0" :on-error="() => errorFile()" :on-preview="handlePreview"
            :on-remove="handleRemove" :on-success="(response) =>successFile(response)"
            :action="process.env.VUE_APP_API_UPLOAD" class="upload-demo" multiple
        >
          <el-button type="primary">请选择上传文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              文件大小不能大于5MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus'
import tool from "@/utils/tool";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const data = reactive({
  imgUrl: [],
  checkFlag: false,
})
const delFile = ref([])
const emit = defineEmits([])
const props = defineProps({})
const loadingFlag = ref(false)
const close = () => {
  data.checkFlag = false;
  data.imgUrl = []
}
const fileList = ref([])
const headers = {
  Authorization: "Bearer " + tool.cookie.get("TOKEN"),
  ContentType: "multipart/form-data",
  clientType:'pc',
};
const handleRemove = (file) => {
  delFile.value.push(file)
}

const handlePreview = (uploadFile) => {
  console.log(uploadFile)
  const fileName = uploadFile.name.split(".")
  data.imgUrl = []
  if (
      fileName[fileName.length - 1] == "gif" ||
      fileName[fileName.length - 1] == "jpeg" ||
      fileName[fileName.length - 1] == "apng" ||
      fileName[fileName.length - 1] == "jpg" ||
      fileName[fileName.length - 1] == "avif" ||
      fileName[fileName.length - 1] == "png" ||
      fileName[fileName.length - 1] == "svg" ||
      fileName[fileName.length - 1] == "webp"
  ) {
    data.checkFlag = true;
    data.imgUrl.push(uploadFile.response ? uploadFile.response.data.url : uploadFile.url)
  }
}
const successFile = () => {
  ElMessage.success("上传成功");
};
const errorFile = () => {
  ElMessage.error("上传失败");
};
const handleExceed = (files, uploadFiles) => {
  ElMessage.warning(
      `The limit is 3, you selected ${files.length} files this time, add up to ${
          files.length + uploadFiles.length
      } totally`
  )
}
const beforeFile = (file) => {
  if (file.size > 5242880) {
    ElMessage.error("文件不能大于5M");
    return false;
  }
}
const beforeRemove = (uploadFile, uploadFiles) => {
  return ElMessageBox.confirm(
      `Cancel the transfer of ${uploadFile.name} ?`
  ).then(
      () => true,
      () => false
  )
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  fileList,
  loadingFlag,
  delFile,
})

</script>
<style lang='scss' scoped>
.el-form--inline {
  overflow: auto;
}
</style>
