.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.formBox {
	width: 100%;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	padding-right: 80px;
}

.item {
	margin-bottom: 18px;
	margin-top: -10px;
}

.el-card {
	margin-top: 10px;
}

.order {
	padding: 0px 10px 10px 10px;
}

.el-pagination {
	margin-top: 40px;
}

.el-select-dropdown__list {
	.el-input {
		width: 90%;
		margin-left: 5%;
		margin-top: 5px;
		margin-bottom: 15px;
	}

	.el-pagination {
		margin-right: 20px;
		margin-top: 10px;
		margin-bottom: 10px;
	}
}

.col_title {
	color: #333;
	font-size: 18px;
	font-weight: bold;
	position: relative;
	padding-left: 8px;

	&::after {
		content: "";
		display: inline-block;
		width: 3px;
		height: 20px;
		background-color: #2878ff;
		border-radius: 2px;
		position: absolute;
		top: 15px;
		left: 0;
	}
}

.stateTitle {
	position: absolute;
	font-size: 15px;
	top: 21px;
	right: 53px;
}

::v-deep .el-checkbox__inner {
	width: 20px !important;
	height: 20px !important;
}

::v-deep .el-checkbox__inner::after {
	width: 7px !important;
	height: 13px !important;
}

::v-deep .el-radio__inner {
	width: 20px !important;
	height: 20px !important;
}

::v-deep .el-radio__inner::after {
	width: 8px !important;
	height: 8px !important;
}
