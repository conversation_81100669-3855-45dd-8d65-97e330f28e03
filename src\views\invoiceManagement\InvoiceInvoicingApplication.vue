<template>
	<div class="ApplicationSearch">
		<ApplicationSearch ref="searchRef" @handleQuery="handleQuery"/>
		<el-card body-style="padding-top:0;padding-bottom:10;" class="box-card last-card" style="margin-top: 10px">
			<template #header>
				<div class="card-header">
					<span>
						<el-button class="button" type="primary" @click="newAddTable()">
							创建申请(销售出库)</el-button>
						<el-button class="button" type="primary" @click="newAddTable2()">
							创建申请(销退入库)</el-button>
					</span>
				</div>
			</template>
			<div class="item" style="margin-top:10px">
				<DragTableColumn v-loading="loadingFlag" v-model:queryParams="searchRef.searchForm" :columns="columns"
								 :getList="handleQuery" :row-style="functionIndex.tableRowStyles" :tableData="tableList"
								 className="invoiceManagement_InvoiceInvoicingApplication">
					<template v-slot:operate="{ scopeData }">
						<el-button :disabled="scopeData.row.auditStatus != 0 && scopeData.row.auditStatus != 7" link
								   type="primary"
								   @click="scopeData.row.billingType == '0' ? editTable(scopeData.row) : editTable2(scopeData.row)">
							<img src="@/assets/icons/update.png" style="margin: 0px 5px 0 0"/>编辑
						</el-button>

						<el-button :disabled="scopeData.row.auditStatus != 0 && scopeData.row.auditStatus != 7" link
								   type="danger"
								   @click="deltable(scopeData.row)"><img src="@/assets/icons/delete.png"
																		 style="margin: 0px 5px 0 0"/>删除
						</el-button>
						<el-button link type="primary"
								   @click="scopeData.row.billingType == '0' ? detailTable(scopeData.row) : detailTable2(scopeData.row)">
							<img src="@/assets/icons/detail.png" style="margin: 2px 5px 0 0"/>详情
						</el-button>
						<el-button link type="success" @click="logFn(scopeData.row)"><img
							src="@/assets/icons/review.png"
							style="margin: 0px 2px 0 0"/>操作日志
						</el-button>
					</template>
				</DragTableColumn>
				<el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
							   :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
							   layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
							   @size-change="handleQuery"
							   @current-change="handleQuery"/>
			</div>
		</el-card>

		<el-dialog v-model="dialogVisible" :before-close="handleClose" title="创建申请" width="80%">
			<div v-loading="editLoading">
				<ApplicationDialog ref="formRef"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleClose()">取消</el-button>
					<el-button type="primary" @click="submitFn()">
						提交申请
					</el-button>
				</span>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisibleOut" :before-close="handleClose2" title="创建申请" width="80%">
			<div v-loading="editLoading2">
				<ApplicationDialog2 ref="formRef2"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleClose2()">取消</el-button>
					<el-button type="primary" @click="submitFn2()">
						提交申请
					</el-button>
				</span>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisible2" title="查看开票详情" width="80%">
			<div v-loading="detailFlag">
				<ApplicationDetails v-if="dialogVisible2" :obj="data.obj" :table1="data.table1" :table2="data.table2"
									:types="data.types"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible2 = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisible3" title="操作记录" width="30%">
			<div v-loading="logFlag">
				<logQuery ref="logQueryRef"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible3 = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import ApplicationSearch from './components/ApplicationSearch.vue'
import {ElMessage, ElMessageBox} from "element-plus";
import ApplicationDialog from './components/ApplicationDialog.vue'
import ApplicationDialog2 from './components/ApplicationDialog2.vue'
import {applocation, outPut} from "@/api/model/invoice";
import ApplicationDetails from './components/ApplicationDetails.vue'
import {functionIndex} from "@/views//salesManagement/functionIndex";
import {manageApi} from "@/api/model/salesManagement";

const {proxy} = getCurrentInstance();

// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const detailFlag = ref(false)
const dialogVisibleOut = ref(false)
const editLoading2 = ref(false)
const formRef2 = ref(null)
const data = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
	table1: [],
	table2: [],
	auditStatus: 10,
	obj: {
		auditStatus: 999
	}
})
const dialogVisible = ref(false)
const formRef = ref(null)
const editLoading = ref(false)
const handleClose = () => {
	ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			dialogVisible.value = false
			emptyFn()
		})
		.catch(() => {
		});
}
const handleClose2 = () => {
	ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			dialogVisibleOut.value = false
			emptyFn2()
		})
		.catch(() => {
		});
}
const logQueryRef = ref(null)
const emit = defineEmits([])
const props = defineProps({})
const searchRef = ref(null)
const loadingFlag = ref(false)
const dialogVisible2 = ref(false)
const dialogVisible3 = ref(false)
const tableList = ref([])
const editTable = (row) => {
	dialogVisible.value = true
	editLoading.value = true
	emptyFn()
	applocation.detailList({
		'salesInvoiceId': row.id
	}).then(res => {
		editLoading.value = false
		if (res.code == 200) {
			res.data.salesInvoiceForms.forEach(item => {
				let adjustPriceList = JSON.parse(JSON.stringify(item.invoiceFormPriceList))
				let invoicingInfo = []
				adjustPriceList?.forEach(items => {
					items.num = items.invoiceQuantity
					items.allPrices = items.totalAmount
					items.adjustUnitPrice = items.invoicePrice
					invoicingInfo.push(items.invoicePrice?.toFixed(2) + ' * ' + items.num + ' = ' + Number(items.allPrices).toFixed(2))
				})
				formRef.value.chooseData.push({
					'docNum': item.salesNo,
					'erpCustomer': {
						id: item.customer.id
					},
					'editId': item.id,
					'customer': item.customerName,
					'commodity': {
						'commonName': item.commodityName,
						'commoditySelfCode': item.commoditySelfCode,
						'packageSpecification': item.commodityPackageSpecification,
						'manufactureName': item.commodityManufactureName,
						'producingArea': item.commodityProducingArea,
						'basicUnit': item.commodityBasicUnit,
					},
					'allPrice': item.totalAmount,
					'produceDate': item.commodityProduceDate,
					'batchNumber': item.batchNo,
					'endValiDate': item.commodityValidityTime,
					'outTime': item.salesOutBound.outTime,
					'createDate': item.salesOrder.docDate,
					'unitPrice': item.unitPrice,
					'outQuantity': item.boundQuantity,
					'supplier': null,
					'invoicingInfo': invoicingInfo,
					'adjustPriceList': adjustPriceList,
					'supplierName': null,
					'checkFlag': false,
					'totalPrice': item.totalPriceTax,
					'amount': item.totalTax,
					'taxRate': item.taxRate,
				})
				formRef.value.formData.id = res.data.salesInvoice.id
				formRef.value.formData.n6 = res.data.salesInvoice.taxpayerNo
				formRef.value.formData.n4 = res.data.salesInvoice.invoiceAmount
				formRef.value.formData.n8 = res.data.salesInvoice.totalTax
				formRef.value.formData.n9 = res.data.salesInvoice.totalPriceTax
				formRef.value.formData.n1 = res.data.salesInvoice.remark
				formRef.value.formData.n3 = res.data.salesInvoice.handleBy.id
				formRef.value.formData.n5 = res.data.salesInvoice.invoiceType
				formRef.value.formData.n2 = res.data.salesInvoice.customer.id
				formRef.value.formData.n7 = res.data.salesInvoice.customer.enterpriseName
			})
		} else {
			ElMessage.error(res.msg)
		}
	})

}
const editTable2 = (row) => {
	dialogVisibleOut.value = true
	editLoading2.value = true
	emptyFn2()
	applocation.detailList({
		'salesInvoiceId': row.id
	}).then(res => {
		editLoading2.value = false
		if (res.code == 200) {
			res.data.salesInvoiceForms.forEach(item => {
				formRef2.value.chooseData.push({
					'docNum': item.salesNo,
					'erpCustomer': {
						id: item.customer?.id
					},
					'editId': item.id,
					'customer': item.customerName,
					'commodity': {
						'commonName': item.commodityName,
						'commoditySelfCode': item.commoditySelfCode,
						'packageSpecification': item.commodityPackageSpecification,
						'manufactureName': item.commodityManufactureName,
						'producingArea': item.commodityProducingArea,
						'basicUnit': item.commodityBasicUnit,
					},
					'allPrice': item.totalAmount,
					'produceDate': item.commodityProduceDate,
					'batchNumber': item.batchNo,
					'endValiDate': item.commodityValidityTime,
					'num': item.invoicedQuantity,
					'inQuantity': item.boundQuantity,
					'inTime': item.salesRetreatInbound.inTime,
					'createDate': item.salesInvoice.applyDate,
					'unitPrice': item.unitPrice,
					'supplier': null,
					'supplierName': null,
					'checkFlag': false,
				})
				formRef2.value.formData.id = res.data.salesInvoice.id
				formRef2.value.formData.n6 = res.data.salesInvoice.taxpayerNo
				formRef2.value.formData.n4 = res.data.salesInvoice.invoiceAmount
				formRef2.value.formData.n1 = res.data.salesInvoice.remark
				formRef2.value.formData.n3 = res.data.salesInvoice.handleBy.id
				formRef2.value.formData.n5 = res.data.salesInvoice.invoiceType
				formRef2.value.formData.n2 = res.data.salesInvoice.customer.id
				formRef2.value.formData.n7 = res.data.salesInvoice.customer.enterpriseName
			})
		} else {
			ElMessage.error(res.msg)
		}
	})

}
const logFlag = ref(false)
const logFn = async (row) => {
	logFlag.value = true
	dialogVisible3.value = true;
	if (logQueryRef.value) {
		logQueryRef.value.data.list = [];
	}
	const auditList = await applocation.auditList({"salesInvoice.id": row.id})
	const logList = await manageApi.logList({masterId: row.id})
	if (auditList.code == 200 && logList.code == 200) {
		logQueryRef.value.timeFns(auditList.data.records, logList.data.records);
	} else {
		ElMessage.error('加载失败')
	}
	logFlag.value = false
}
const detailTable = async (row) => {
	data.table2 = []
	data.table1 = []
	data.types = 0
	dialogVisible2.value = true
	detailFlag.value = true
	data.auditStatus = row.auditStatus
	data.invoiceStatus = row.status
	const res = await applocation.detailList({
		'salesInvoiceId': row.id
	})
	const res2 = await outPut.detaiInvoices({
		'salesInvoice.id': row.id
	})
	if (res.code == 200 && res2.code == 200) {
		detailFlag.value = false
		res2.data.records.forEach(record => {
			data.table1.push(record.salesInvoiceReceipt)
		})
		res.data.salesInvoiceForms.forEach(item => {
			if (!item.invoiceFormPriceList) {
				item.invoiceFormPriceList = []
				item.invoiceFormPriceList.push({
					invoicePrice: item.unitPrice,
					invoiceQuantity: item.invoicedQuantity,
					totalAmount: item.totalAmount
				})
			}
			data.table2.push({
				'docNum': item.salesNo,
				'erpCustomer': {
					id: item.customer.id
				},
				'editId': item.id,
				'customer': item.customerName,
				'commodity': {
					'commonName': item.commodityName,
					'commoditySelfCode': item.commoditySelfCode,
					'packageSpecification': item.commodityPackageSpecification,
					'manufactureName': item.commodityManufactureName,
					'producingArea': item.commodityProducingArea,
					'validityTime': item.commodityValidityTime,
					'basicUnit': item.commodityBasicUnit,
				},
				'checkFlag': false,
				'invoiceFormPriceList': item.invoiceFormPriceList,
				'totalAmount': item.totalAmount,
				'salesOutBound': {
					'outTime': item.salesOutBound.outTime
				},
				'totalPriceTax': item.totalPriceTax,
				'totalTax': item.totalTax,
				'taxRate': item.taxRate,
				'createDate': item.salesOrder.docDate,
				'produceDate': item.commodityProduceDate,
				'batchNumber': item.batchNo,
				'unitPrice': item.unitPrice,
				'outQuantity': item.boundQuantity,
				'supplier': null,
				'supplierName': null,
				'allPrice': (item.unitPrice * item.boundQuantity).toFixed(2)
			})
			data.obj = res.data.salesInvoice
		})
	} else {
		ElMessage.error('获取失败')
	}
}
const detailTable2 = async (row) => {
	data.table2 = []
	data.table1 = []
	data.types = 1
	dialogVisible2.value = true
	detailFlag.value = true
	data.auditStatus = row.auditStatus
	data.invoiceStatus = row.status
	const res = await applocation.detailList({
		'salesInvoiceId': row.id
	})
	const res2 = await outPut.detaiInvoices({
		'salesInvoice.id': row.id
	})
	if (res.code == 200 && res2.code == 200) {
		detailFlag.value = false
		res2.data.records.forEach(record => {
			data.table1.push(record.salesInvoiceReceipt)
		})
		res.data.salesInvoiceForms.forEach(item => {
			data.table2.push({
				'docNum': item.salesNo,
				'editId': item.id,
				'customer': item.customerName,
				'commodity': {
					'commonName': item.commodityName,
					'commoditySelfCode': item.commoditySelfCode,
					'packageSpecification': item.commodityPackageSpecification,
					'manufactureName': item.commodityManufactureName,
					'producingArea': item.commodityProducingArea,
					'validityTime': item.commodityValidityTime,
					'basicUnit': item.commodityBasicUnit,
				},
				'totalAmount': item.totalAmount,
				'salesOutBound': {
					'outTime': item.salesRetreatInbound.inTime
				},
				'createDate': item.salesInvoice.applyDate,
				'produceDate': item.commodityProduceDate,
				'batchNumber': item.batchNo,
				'unitPrice': item.unitPrice,
				'outQuantity': item.boundQuantity,
				'num': item.invoicedQuantity,
				'supplier': null,
				'supplierName': null,
				'allPrice': item.totalAmount
			})
			data.obj = res.data.salesInvoice
		})
	} else {
		ElMessage.error('获取失败')
	}
}
const echoList = (arr) => {
	let newArr = []
	console.log(arr)
	arr.forEach((item) => {
		let priceArr = []
		let allNum = 0
		item.adjustPriceList.forEach((items) => {
			console.log(items)
			allNum += items.num
			priceArr.push({
				salesInvoiceForm: {
					id: item.editId ? item.editId : null
				},
				salesAdjustPrice: {
					id: items.id
				},
				totalAmount: items.allPrices,
				invoiceQuantity: items.num,
				taxRate: items.taxRate,
				totalTax: items.amount,
				invoicePrice: items.adjustUnitPrice
			})
		})
		newArr.push({
			'id': item.editId ? item.editId : null,
			'salesNo': item.docNum,
			'customer': {
				id: item.erpCustomer.id
			},
			'customerName': item.customer,
			'totalTax': item.amount,
			'totalPriceTax': item.totalPrice,
			'taxRate': item.taxRate,
			'commodity': item.commodity,
			'invoicedQuantity': allNum,
			'commodityName': item.commodity.commonName,
			'commoditySelfCode': item.commodity.commoditySelfCode,
			'commodityPackageSpecification': item.commodity.packageSpecification,
			'commodityManufactureName': item.commodity.manufactureName,
			'commodityProducingArea': item.commodity.producingArea,
			'supplier': null,
			'salesOutBound': {
				id: item.id
			},
			'totalAmount': item.allPrice,
			'supplierName': null,
			'commodityProduceDate': item.produceDate,
			'batchNo': item.batchNumber,
			'commodityValidityTime': item.endValiDate,
			'commodityBasicUnit': item.commodity.basicUnit,
			'unitPrice': item.unitPrice,
			'boundQuantity': item.outQuantity,
			// 'invoiceFormPriceList': item.adjustPriceList[0].id ? priceArr : null
			'invoiceFormPriceList': priceArr
		})
	})
	return newArr
}
const echoList2 = (arr) => {
	let newArr = []
	console.log(arr)
	arr.forEach((item) => {
		newArr.push({
			'id': item.editId ? item.editId : null,
			'salesNo': item.docNum,
			'customer': {
				id: item.erpCustomer?.id
			},
			'customerName': item.customer,
			'commodity': item.commodity,
			'commodityName': item.commodity.commonName,
			'commoditySelfCode': item.commodity.commoditySelfCode,
			'commodityPackageSpecification': item.commodity.packageSpecification,
			'commodityManufactureName': item.commodity.manufactureName,
			'commodityProducingArea': item.commodity.producingArea,
			'supplier': null,
			'invoicedQuantity': item.num,
			'boundQuantity': item.inQuantity,
			'salesRetreatInbound': {
				id: item.id
			},
			'totalAmount': item.allPrice,
			'supplierName': null,
			'commodityProduceDate': item.produceDate,
			'batchNo': item.batchNumber,
			'commodityValidityTime': item.endValiDate,
			'commodityBasicUnit': item.commodity.basicUnit,
			'unitPrice': item.unitPrice,
		})
	})
	return newArr
}
const emptyFn = () => {
	setTimeout(() => {
		formRef.value.creatform2.clearValidate()
		formRef.value.chooseData = []
		formRef.value.receiptsList = []
		for (let key in formRef.value.searchForm) {
			formRef.value.searchForm[key] = ''
		}
		for (let key in formRef.value.formData) {
			if (key == 'n4') {
				formRef.value.formData[key] = 0
			} else {
				formRef.value.formData[key] = ''
			}
		}
	})
}
const emptyFn2 = () => {
	setTimeout(() => {
		formRef2.value.creatform2.clearValidate()
		formRef2.value.chooseData = []
		formRef2.value.receiptsList = []
		for (let key in formRef2.value.searchForm) {
			formRef2.value.searchForm[key] = ''
		}
		for (let key in formRef2.value.formData) {
			if (key == 'n4') {
				formRef2.value.formData[key] = 0
			} else {
				formRef2.value.formData[key] = ''
			}
		}
	})
}
const submitFn = async () => {
	if (!formRef.value.creatform2) return;
	await formRef.value.creatform2.validate((valid) => {
		if (valid) {
			if (formRef.value.chooseData.length <= 0) {
				ElMessage.error('请选择出库单据')
			} else {
				let newArr = echoList(formRef.value.chooseData)
				console.log(newArr);
				console.log(formRef.value.formData);
				applocation.saveList({
					'salesInvoice': {
						'id': formRef.value.formData.id ? formRef.value.formData.id : null,
						'taxpayerNo': formRef.value.formData.n6,
						'invoiceAmount': formRef.value.formData.n4.toFixed(2),
						'remark': formRef.value.formData.n1,
						'handleBy': {
							id: formRef.value.formData.n3
						},
						'totalTax': formRef.value.formData.n8,
						'totalPriceTax': formRef.value.formData.n9,
						'invoiceCustomer': formRef.value.formData.n7,
						'invoiceType': formRef.value.formData.n5,
						'billingType': '0',
						'customer': {
							id: formRef.value.formData.n2
						}
					},
					'salesInvoiceForms': newArr,
					'formType': 'submit'
				}).then((res) => {
					if (res.code == 200) {
						ElMessage.success('提交成功')
						dialogVisible.value = false
						emptyFn()
						handleQuery()
					} else {
						ElMessage.error(res.msg)
					}
				})
			}
		}
	});
}
const submitFn2 = async () => {
	if (!formRef2.value.creatform2) return;
	await formRef2.value.creatform2.validate((valid) => {
		if (valid) {
			if (formRef2.value.chooseData.length <= 0) {
				ElMessage.error('请选择入库单据')
			} else {
				let newArr = echoList2(formRef2.value.chooseData)
				applocation.saveList({
					'salesInvoice': {
						'id': formRef2.value.formData.id ? formRef2.value.formData.id : null,
						'taxpayerNo': formRef2.value.formData.n6,
						'invoiceAmount': formRef2.value.formData.n4.toFixed(2),
						'remark': formRef2.value.formData.n1,
						'handleBy': {
							id: formRef2.value.formData.n3
						},
						'invoiceCustomer': formRef2.value.formData.n7,
						'invoiceType': formRef2.value.formData.n5,
						'billingType': '1',
						'customer': {
							id: formRef2.value.formData.n2
						}
					},
					'salesInvoiceForms': newArr,
					'formType': 'submit'
				}).then((res) => {
					if (res.code == 200) {
						ElMessage.success('提交成功')
						dialogVisibleOut.value = false
						emptyFn2()
						handleQuery()
					} else {
						ElMessage.error(res.msg)
					}
				})
			}
		}
	});
}
const deltable = (row) => {
	ElMessageBox.confirm("确认删除此项吗?", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			applocation.delList({
				ids: row.id
			}).then(res => {
				if (res.code == 200) {
					ElMessage.success('删除成功')
					handleQuery()
				} else {
					ElMessage.error(res.msg)
				}
			})
		})
		.catch(() => {
		});
}
const newAddTable = () => {
	emptyFn()
	dialogVisible.value = true
}
const newAddTable2 = () => {
	emptyFn2()
	dialogVisibleOut.value = true
}
const changeTime = (time) => {
	if (time) {
		let newTime = new Date(time)
		newTime = newTime.setDate(newTime.getDate() + 1);
		newTime = functionIndex.transformTimestampSearch(newTime)
		return newTime
	} else {
		return null
	}
}
const handleQuery = () => {
	// tableList
	loadingFlag.value = true
	applocation.getList({
		size: data.pageSize,
		current: data.pageNum,
		invoiceCustomer: searchRef.value?.searchForm.n1,
		salesNo: searchRef.value?.searchForm.n2,
		beginApplyDate: searchRef.value?.searchForm.n3[0],
		endApplyDate: changeTime(searchRef.value?.searchForm.n3[1]),
		applyBy: searchRef.value?.searchForm.n4,
		auditStatus: searchRef.value?.searchForm.n5,
		status: searchRef.value?.searchForm.n6,
		billingType: searchRef.value?.searchForm.n7,
	}).then(res => {
		if (res.code == 200) {
			res.data.records.forEach(record => {
				record.invoiceAmount = record.invoiceAmount.toFixed(2)
			})
			tableList.value = res.data.records
			data.total = res.data.total
		}
		loadingFlag.value = false
	})
}
const statusType = ref()
const echo = (status) => {
	let searchValue = statusType.value.find(item => item.value == status).name
	return searchValue ? searchValue : '未知'
}

onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')

	handleQuery()
})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	...toRefs(data)
})
const dick = async () => {
	statusType.value = await proxy.getDictList("status_sales")
	localStorage.setItem("salesType", JSON.stringify(statusType.value))
}
dick()
const columns = ref([
	{label: '申请单据编号', prop: 'applyNo', minWidth: "150px"},
	{label: '客户', prop: 'customer.enterpriseName'},
	{label: '发票客户', prop: 'invoiceCustomer'},
	{label: '纳税人识别号', prop: 'customer.socialCreditCode'},
	{label: '开票金额(含税)', prop: 'totalPriceTax'},
	{label: '申请人', prop: 'createBy.name'},
	{label: '申请日期', prop: 'createDate', type: "date"},
	{
		label: '审核状态',
		prop: 'auditStatus',
		type: 'status',
		filters: JSON.parse(localStorage.getItem('salesType')) || statusType,
		searchKey: "n5"
	},
	{
		label: '开票状态', prop: 'status', type: 'status', filters: [
			{
				name: '未开票',
				value: '0'
			},
			{
				name: '已开票',
				value: '1'
			},
			{
				name: '开票失败',
				value: '2'
			},
		], searchKey: "n6"
	},
	{
		label: '类型', prop: 'billingType', searchKey: 'n7', type: "status", filters: [
			{
				name: '销售出库',
				value: '0'
			},
			{
				name: '销退入库',
				value: '1'
			}
		],
	},
	{label: '操作', prop: 'operate', minWidth: "300px", type: 'operate', fixed: 'right'},
])
</script>
<style lang='scss' scoped>
.ApplicationSearch {
	padding: 10px;
}
</style>
