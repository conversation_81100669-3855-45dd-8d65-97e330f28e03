<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-06-15 11:24:34
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-16 16:29:11
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\components\viewImg\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<el-image-viewer v-if="visible" :url-list="[src]" @close="beforeClose"/>
   <!-- <div class="zhe" v-if="visible">
			<div class="imgDiv">
				<img :src="src" alt="图片加载失败" />
				<span @click="beforeClose" style="cursor: pointer;"
					><el-icon><Close /></el-icon
				></span>
			</div>
		</div> -->
</template>

<script setup>
import {
	ref,
	reactive,
	toRefs,
	onBeforeMount,
	onMounted,
	watchEffect,
	computed,
	watch,
    defineProps
} from "vue";
import { ArrowDown, Close } from "@element-plus/icons-vue";
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    src:{
        type:String,
        default:''
    },
    beforeClose:{
        type:Function,
        default:()=>{}
    }
})
const {visible,src,beforeClose} = toRefs(props)
</script>

<style lang="scss" scoped>
.zhe {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.6);
	.imgDiv {
		max-width: 70%;
		max-height: 70%;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);

		img {
			width: 100%;
			min-width: 200px;
			height: 100%;
			color: #fff;
		}
		span {
			position: absolute;
			font-size: 25px;
			border-radius: 50%;
			height: 30px;
			width: 30px;
			line-height: 34px;
			text-align: center;
			color: #fff;
			right: -30px;
			top: -5px;
		}
	}
}
</style>