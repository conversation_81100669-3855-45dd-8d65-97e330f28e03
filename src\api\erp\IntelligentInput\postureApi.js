import http from "@/utils/request";
// 系统配置  >  审核流程  api

export const applianceApi = {
    getSmall: function (params) {
        return http.get("/sys/dictValue/getValueByType", params);
    },
    getViewList: function (params) {
        return http.get("/sys/sysQualificationDocumentSet/list", params);
    },
    getORCDan: function (data) {
        return http.post("/erp/ocr/identify/erpOcrIdentify/queryOCRDataByType", data);
    },
    getList: function (data) {
        return http.get("/erp/ocr/identify/erpOcrIdentify/list", data);
    },
    getOrcs: function (data) {
        return http.post("/erp/ocr/identify/erpOcrIdentify/queryIOCRDataByType", data);
    },
    fileUpload: function (data) {
        let config = {headers: {'Content-Type': "multipart/form-data",}}
        return http.post("/file/upload", data, config);
    },
};
