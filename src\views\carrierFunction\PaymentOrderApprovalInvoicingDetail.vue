<template>
    <div>
        <el-dialog v-model="visible" :title="paymentOrderApprovalTitle" top="5vh" width="90%" @close="closeVisible">
            <el-form ref="paymentOrderApprovalForm" :model="paymentOrderApprovalForm" label-width="auto" style="max-height: 70vh">
                <el-descriptions v-if="paymentOrderApprovalStatus === 'approval' || paymentOrderApprovalStatus === 'detail'" :column="4" border title="申请信息">
                    <el-descriptions-item label="开票金额">{{ paymentOrderApprovalForm.invoiceApply.invoiceAmount }}</el-descriptions-item>
                    <el-descriptions-item label="申请公司">{{ paymentOrderApprovalForm.invoiceApply.companyName }}</el-descriptions-item>
                    <el-descriptions-item label="发票抬头">{{ paymentOrderApprovalForm.invoiceApply.invoiceHead }}</el-descriptions-item>
                    <el-descriptions-item label="签约公司">
                        <span class="whitespace-nowrap">{{ formatDictionaryData('companyList', paymentOrderApprovalForm.invoiceApply.signCompany) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="发票类型">{{ formatDictionaryData('invoiceTypeList', paymentOrderApprovalForm.invoiceApply.invoiceType) }}</el-descriptions-item>
                    <el-descriptions-item label="税点(%)">{{ paymentOrderApprovalForm.invoiceApply.taxPoint }}</el-descriptions-item>
                    <el-descriptions-item label="税号">{{ paymentOrderApprovalForm.invoiceApply.taxNo }}</el-descriptions-item>
                    <el-descriptions-item label="地址">{{ paymentOrderApprovalForm.invoiceApply.address }}</el-descriptions-item>
                    <el-descriptions-item label="电话">
                        <span class="whitespace-nowrap">{{ paymentOrderApprovalForm.invoiceApply.phone }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="开户银行">{{ paymentOrderApprovalForm.invoiceApply.openBank }}</el-descriptions-item>
                    <el-descriptions-item label="银行账号">
                        <span class="whitespace-nowrap">{{ paymentOrderApprovalForm.invoiceApply.bankAccount }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="申请时间">
                        <span class="whitespace-nowrap">{{ paymentOrderApprovalForm.invoiceApplyDto.applyTime }}</span>
                    </el-descriptions-item>
                </el-descriptions>
                <el-descriptions class="mt-10" title="付款单明细"></el-descriptions>
                <column-table :columns="paymentOrderApprovalColumns" :data="paymentOrderApprovalForm.docList" :show-summary="true">
                    <template #companyName="{ row }">
                        {{ setCompanyName(row) }}
                    </template>
                    <template #paymentDocType="{ row }">
                        {{ formatDictionaryData('paymentDocTypeOptions', row.paymentDocType) }}
                    </template>
                    <template #startDate="{ row }"> {{ formatDate(row.startDate) }} ~ {{ formatDate(row.endDate) }} </template>
                    <template #discountType="{ row }">
                        {{ formatDictionaryData('discountTypeList', row.discountType) }}
                    </template>
                </column-table>
                <div class="grid items-start" style="grid-template-columns: minmax(0, 1fr) minmax(0, 1fr); padding-bottom: 10px; grid-gap: 10px">
                    <div v-if="paymentOrderApprovalStatus === 'approval'" class="flex flex-col">
                        <el-descriptions :column="2" border title="审批">
                            <el-descriptions-item label="申请人">{{ paymentOrderApprovalForm.invoiceApplyDto.createBy.name }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ formatDate(paymentOrderApprovalForm.invoiceApplyDto.createDate) }}</el-descriptions-item>
                        </el-descriptions>
                        <el-form-item :rules="[{ required: true, message: '请选择审批意见', trigger: 'change' }]" class="mt-10" label="审批意见" prop="approveStatus">
                            <el-radio-group v-model="paymentOrderApprovalForm.approveStatus">
                                <el-radio label="1">通过</el-radio>
                                <el-radio label="2">驳回</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item :rules="paymentOrderApprovalForm.approveStatus === '2' ? [{ required: true, message: '请输入审批备注', trigger: 'blur' }] : []" label="审批备注" prop="approveIdea">
                            <el-input v-model="paymentOrderApprovalForm.approveIdea" :rows="4" maxlength="250" placeholder="请输入审批备注" show-word-limit type="textarea"></el-input>
                        </el-form-item>
                    </div>
                    <div v-else-if="paymentOrderApprovalStatus === 'detail'">
                        <div v-if="paymentOrderApprovalForm.approveList && paymentOrderApprovalForm.approveList.length">
                            <el-descriptions title="审批"></el-descriptions>
                            <el-steps :active="1" :space="60" direction="vertical">
                                <el-step v-for="(item, index) in paymentOrderApprovalForm.approveList" :key="index" :description="`审批人：${item.approveUser}`" :title="`审批时间：${formatDate(item.approveTime)}`">
                                    <template #icon>
                                        <span style="display: inline-block; width: 17px; height: 17px; border-radius: 50%; background: #a8abb2"></span>
                                    </template>
                                </el-step>
                                <el-step :description="`申请时间：${formatDate(paymentOrderApprovalForm.invoiceApplyDto.createDate)}`" :title="`申请人：${paymentOrderApprovalForm.invoiceApplyDto.createBy.name}`">
                                    <template #icon>
                                        <span style="display: inline-block; width: 17px; height: 17px; border-radius: 50%; background: #a8abb2"></span>
                                    </template>
                                </el-step>
                            </el-steps>
                            <div style="margin-left: 20px">
                                <div class="flex mb-10 items-center">
                                    <span>审批意见：</span>
                                    <el-tag v-if="paymentOrderApprovalForm.approveList[0]?.approveStatus === '1'" effect="dark" type="success">已通过</el-tag>
                                    <el-tag v-else effect="dark" type="danger">已驳回</el-tag>
                                </div>
                                <div class="flex">
                                    <span class="whitespace-nowrap">审批备注：</span>
                                    <span class="word-break-break-word">{{ paymentOrderApprovalForm.approveList[paymentOrderApprovalForm.approveList.length - 1]?.approveIdea }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <el-descriptions :column="1" border class="mb-10" title="开票信息">
                            <el-descriptions-item label="开票项目名称">
                                <span>{{ setProjectName(paymentOrderApprovalForm.invoiceApply?.projectName) }}</span>
                                <span class="mx-5">|</span>
                                <span class="text-red-500">{{ paymentOrderApprovalForm.invoiceApply?.projectName }}</span>
                            </el-descriptions-item>
                        </el-descriptions>
                        <div v-if="paymentOrderApprovalStatus === 'detail'">
                            <el-descriptions v-if="paymentOrderApprovalForm.invoiceApply?.projectName === '运输服务费' || paymentOrderApprovalForm.invoiceApply?.projectName === '国内运输费'" :column="2" border class="mt-10" title="特定业务信息">
                                <el-descriptions-item label="运输工具种类">{{ paymentOrderApprovalForm.invoiceApply.transWay }}</el-descriptions-item>
                                <el-descriptions-item label="运输工具牌号">{{ paymentOrderApprovalForm.invoiceApply.transCarNo }}</el-descriptions-item>
                                <el-descriptions-item label="起运地址">{{ paymentOrderApprovalForm.invoiceApply.startAddress }}</el-descriptions-item>
                                <el-descriptions-item label="到达地址">{{ paymentOrderApprovalForm.invoiceApply.endAddress }}</el-descriptions-item>
                                <el-descriptions-item label="运输货物名称" span="2">{{ paymentOrderApprovalForm.invoiceApply.transGooods }}</el-descriptions-item>
                            </el-descriptions>
                            <el-descriptions :column="1" border>
                                <el-descriptions-item label="备注">{{ paymentOrderApprovalForm.remark }}</el-descriptions-item>
                                <el-descriptions-item label="发票备注">{{ paymentOrderApprovalForm.invoiceApply.remark }}</el-descriptions-item>
                            </el-descriptions>
                        </div>
                        <div v-else>
                            <div v-if="paymentOrderApprovalForm.invoiceApply?.projectName === '运输服务费' || paymentOrderApprovalForm.invoiceApply?.projectName === '国内运输费'">
                                <el-descriptions title="特定业务信息"></el-descriptions>
                                <el-form-item :rules="[{ required: true, message: '请输入运输工具种类', trigger: 'blur' }]" label="运输工具种类" prop="invoiceApply.transWay">
                                    <el-input v-model="paymentOrderApprovalForm.invoiceApply.transWay" placeholder="请输入运输工具种类"></el-input>
                                </el-form-item>
                                <el-form-item :rules="[{ required: true, message: '请输入运输工具牌号', trigger: 'blur' }]" label="运输工具牌号" prop="invoiceApply.transCarNo">
                                    <el-input v-model="paymentOrderApprovalForm.invoiceApply.transCarNo" placeholder="请输入运输工具牌号"></el-input>
                                </el-form-item>
                                <el-form-item :rules="[{ required: true, message: '请输入起运地址', trigger: 'blur' }]" label="起运地址" prop="invoiceApply.startAddress">
                                    <el-input v-model="paymentOrderApprovalForm.invoiceApply.startAddress" placeholder="请输入起运地址"></el-input>
                                </el-form-item>
                                <el-form-item :rules="[{ required: true, message: '请输入到达地址', trigger: 'blur' }]" label="到达地址" prop="invoiceApply.endAddress">
                                    <el-input v-model="paymentOrderApprovalForm.invoiceApply.endAddress" placeholder="请输入到达地址"></el-input>
                                </el-form-item>
                                <el-form-item :rules="[{ required: true, message: '请输入运输货物名称', trigger: 'blur' }]" label="运输货物名称" prop="invoiceApply.transGooods">
                                    <el-input v-model="paymentOrderApprovalForm.invoiceApply.transGooods" placeholder="请输入运输货物名称"></el-input>
                                </el-form-item>
                            </div>
                            <el-form-item :rules="[{ required: true, message: '请输入备注', trigger: 'blur' }]" label="备注" prop="remark">
                                <el-input v-model="paymentOrderApprovalForm.remark" maxlength="200" placeholder="请输入备注" show-word-limit type="textarea"></el-input>
                            </el-form-item>
                            <el-form-item label="发票备注" prop="invoiceApply.remark">
                                <el-input v-model="paymentOrderApprovalForm.invoiceApply.remark" maxlength="150" placeholder="请输入发票备注" show-word-limit type="textarea"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </el-form>
            <template v-if="paymentOrderApprovalStatus === 'approval'" #footer>
                <div v-loading="paymentOrderApprovalLoading" style="text-align: center">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm()">提 交</el-button>
                </div>
            </template>
        </el-dialog>
        <el-image-viewer v-if="viewerVisible" v-model="viewerVisible" :url-list="viewerList" @close="viewerVisible = false" />
    </div>
</template>
<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import tool from '@/utils/tool';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import { selectDictLabel } from '@/utils/dictLabel';
import { Close, Delete, Plus } from '@element-plus/icons-vue';
import moment from 'moment';
import InvoiceApproval from '@/api/carrierEnd/InvoiceApproval';

export default {
    name: 'PaymentOrderApproval',
    components: { ColumnTable, Close, Delete, Plus },
    model: {
        prop: 'paymentOrderApprovalVisible',
        event: 'update:paymentOrderApprovalVisible'
    },
    props: {
        /**
         * 开票申请id
         */
        paymentOrderApprovalInvoicingId: {
            type: String,
            default: undefined
        },
        paymentOrderApprovalStatus: {
            type: String,
            default: undefined
        },
        paymentOrderApprovalTitle: {},
        paymentOrderApprovalVisible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            paymentOrderApprovalForm: {
                approveStatus: undefined,
                approveIdea: undefined,
                remark: undefined,
                approveList: [],
                docList: [],
                invoiceApplyDto: {
                    invoiceAmount: undefined,
                    applyTime: undefined,
                    isInvoice: undefined,
                    invoice: {
                        invoiceHead: undefined,
                        taxNo: undefined,
                        address: undefined,
                        phone: undefined,
                        openBank: undefined,
                        bankAccount: undefined
                    },
                    remitFile: [],
                    createBy: {
                        id: undefined,
                        name: undefined
                    },
                    createDate: undefined,
                    remark: undefined
                },
                invoiceApply: {
                    invoiceAmount: undefined,
                    companyName: undefined,
                    invoiceHead: undefined,
                    signCompany: undefined,
                    transWay: undefined,
                    transCarNo: undefined,
                    startAddress: undefined,
                    endAddress: undefined,
                    transGooods: undefined,
                    remark: undefined,
                    invoiceType: undefined,
                    taxPoint: undefined,
                    taxNo: undefined,
                    address: undefined,
                    phone: undefined,
                    openBank: undefined,
                    bankAccount: undefined,
                    projectName: undefined
                }
            },
            paymentOrderApprovalLoading: false,
            visible: this.paymentOrderApprovalVisible,
            viewerVisible: false,
            viewerList: [],
            paymentOrderApprovalColumns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'paymentDocType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'startDate', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '其他服务费', key: 'warehouseFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '纸箱费用', key: 'cartonFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '垫付费用', key: 'advanceFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '租箱费用', key: 'rentalBoxFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '其他费用', key: 'otherFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '费用说明', key: 'feeDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '坏账总金额', key: 'badDebtCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '操作员', key: 'operator', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '调整日期', key: 'adjustTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true }
            ],
            companyList: [],
            discountTypeList: [],
            paymentDocTypeOptions: [],
            invoiceTypeList: []
        };
    },
    computed: {
        /**
         * 格式化日期
         * @returns {function(*): *}
         */
        formatDate() {
            return (value) => {
                return moment(value).format('YYYY-MM-DD');
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置公司名称
         */
        setCompanyName() {
            return (row) => {
                const Organization = this.$TOOL.data.get('Organization');
                const { carrierId, companyId } = row;
                if (carrierId === Organization[0].id) {
                    return row.companyName;
                } else if (companyId === Organization[0].id) {
                    return row.carrierName;
                }
            };
        }
    },
    watch: {
        paymentOrderApprovalVisible: {
            handler(val) {
                this.visible = val;
            },
            immediate: true
        }
    },
    created() {
        this.getDict();
        if (this.paymentOrderApprovalInvoicingId) {
            this.getPaymentOrderApprovalInvoicingDetail(this.paymentOrderApprovalInvoicingId);
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:paymentOrderApprovalVisible', false);
        },
        /**
         * 文件上传成功
         * @param res
         */
        fileUploadSuccess(res) {
            this.paymentOrderApprovalForm.invoiceApplyDto.remitFile.push({
                name: res.data.fileName,
                url: res.data.fileUrl
            });
        },
        /**
         * 提取备注逻辑到单独的方法
         */
        getDefaultRemark(invoiceApply, firstApproval) {
            if (firstApproval.remark) return firstApproval.remark;

            switch (invoiceApply?.signCompany) {
                case '91620103773436199W':
                    return '销方开户行：兴业银行兰州铁路支行  账号：612080100100054750';
                case '91620103MA7CXHAU6L':
                    return '销方开户行：交通银行兰州七里河支行  账号：621060108013000552930';
                default:
                    return firstApproval.remark;
            }
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
            this.discountTypeList = await this.getDictList('discount_type');
            this.paymentDocTypeOptions = await this.getDictList('cost_payment_doc_type');
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
        },

        /**
         * 获取消息类型
         * @param message
         * @returns {string}
         */
        getMessageType(message) {
            return message === '开票成功' || message === '操作成功' ? 'success' : 'error';
        },
        /**
         * 获取开票审批详情
         */
        async getPaymentOrderApprovalInvoicingDetail(id) {
            this.paymentOrderApprovalLoading = true;
            try {
                const res = await paymentOrderApproval.getInvoiceApprovalDetail({ id });
                if (res.code === 200) {
                    const { invoiceApply, approveList } = res.data;
                    const firstApproval = approveList?.[0] || {};
                    const isTransportService = invoiceApply?.projectName === '运输服务费' || invoiceApply?.projectName === '国内运输费';

                    // 使用解构和Object.assign来保持响应式
                    this.paymentOrderApprovalForm = {
                        ...this.paymentOrderApprovalForm, // 保留原有结构
                        ...res.data, // 合并新数据
                        invoiceApply: {
                            ...res.data.invoiceApply,
                            transWay: isTransportService && firstApproval.transWay ? firstApproval.transWay : '公路运输',
                            transCarNo: isTransportService && firstApproval.transCarNo ? firstApproval.transCarNo : '甘A7DB33',
                            startAddress: isTransportService && firstApproval.startAddress ? firstApproval.startAddress : '兰州',
                            endAddress: isTransportService && firstApproval.endAddress ? firstApproval.endAddress : '省内',
                            transGooods: isTransportService && firstApproval.transGooods ? firstApproval.transGooods : '药品',
                        },
						remark: this.getDefaultRemark(invoiceApply, firstApproval)
                    };

                }
            } catch (error) {
                console.error('获取详情失败:', error);
            } finally {
                this.paymentOrderApprovalLoading = false;
            }
        },
        /**
         * 图片预览
         * @param file
         */
        handlePictureCardPreview(file) {
            this.viewerVisible = true;
            this.viewerList = [];
            this.viewerList.push(file.url);
        },
        /**
         * 处理响应
         * @param vm
         * @param res
         */
        handleResponse(vm, res) {
            if (res.code === 200) {
                const message = vm.paymentOrderApprovalForm.approveStatus === '2' ? '操作成功' : res.data.code === 200 ? '开票成功' : res.data.msg;
                vm.$message[this.getMessageType(message)](message);
                vm.$emit('getList');
                vm.closeVisible();
            } else {
                vm.$message.error('提交失败');
            }
        },
        /**
         * 设置项目名称
         * @param projectName
         * @returns {string}
         */
        setProjectName(projectName) {
            if (projectName === '运输服务费' || projectName === '国内运输费') {
                return '运输服务';
            } else if (projectName === '收派服务费' || projectName === '仓储服务费') {
                return '物流辅助服务';
            }
            return projectName || '';
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.paymentOrderApprovalForm.validate(async (valid) => {
                if (valid) {
                    this.paymentOrderApprovalLoading = true;
                    const form = this.paymentOrderApprovalForm;
                    let params = {
                        id: form.invoiceApplyDto.id,
                        approveStatus: form.approveStatus,
                        approveIdea: form.approveIdea,
                        remark: form.remark,
                        invoiceRemark: form.invoiceApply.remark
                    };
                    // 只有在特定条件下才添加额外的运输信息
                    if (['运输服务费', '国内运输费'].includes(form.invoiceApply?.projectName)) {
                        params.transWay = form.invoiceApply.transWay;
                        params.transCarNo = form.invoiceApply.transCarNo;
                        params.startAddress = form.invoiceApply.startAddress;
                        params.endAddress = form.invoiceApply.endAddress;
                        params.transGooods = form.invoiceApply.transGooods;
                    }

                    const res = await InvoiceApproval.submitInvoiceApproval(params);
                    this.handleResponse(this, res);
                    this.paymentOrderApprovalLoading = false;
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__header) {
    margin-bottom: 10px;
}
:deep(.el-descriptions__cell.el-descriptions__label) {
    white-space: nowrap;
}
.number__unit__element {
    position: relative;
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
