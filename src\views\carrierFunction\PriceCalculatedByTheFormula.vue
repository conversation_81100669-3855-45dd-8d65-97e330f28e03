<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.native.prevent>
                <el-form-item v-show="isShowAll" label="公式编码" prop="code">
                    <el-input v-model="queryForm.code" clearable placeholder="请输入公式编码" @clear="handleQuery" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="公式名称" prop="name" style="width: 250px">
                    <el-input v-model="queryForm.name" clearable placeholder="请输入公式名称" @clear="handleQuery" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="费用类型" prop="type" style="width: 250px">
                    <el-select v-model="queryForm.type" clearable placeholder="请选择费用类型" @change="handleQuery">
                        <el-option v-for="item in costTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="运输类型" prop="transType" style="width: 250px">
                    <el-select v-model="queryForm.transType" clearable placeholder="请选择产品类型" @change="handleQuery">
                        <el-option v-for="item in valuationTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="公式状态" prop="status">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择状态" @change="handleQuery">
                        <el-option v-for="item in priceBookActivationList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发布状态" prop="publishStatus">
                    <el-select v-model="queryForm.publishStatus" clearable placeholder="请选择发布状态" @change="handleQuery">
                        <el-option v-for="item in publishTheStatusOfList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button type="primary" @click="handleClickBtnAddedFormulas">新增公式</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="PriceCalculatedByTheFormulaTable" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="orderList" :showIndex="true" :max-height="600">
                <template #status="{ row }">
                    <el-switch
                        v-if="row.releaseStatus !== '0' && row.releaseStatus !== '1' && row.releaseStatus !== '4'"
                        v-model="row.status"
                        active-color="#13ce66"
                        active-text="启用"
                        active-value="0"
                        inactive-text="禁用"
                        inactive-value="1"
                        size="small"
                        inline-prompt
                        @change="handleSwitchChange(row)"
                    />
                    <span v-else></span>
                </template>
                <template #type="{ row }">
                    <span>{{ formatTheBillingClassification(row.type) }}</span>
                </template>
                <template #transType="{ row }">
                    <span>{{ formatTheProductType(row.transType) }}</span>
                </template>
                <template #description="{ row }">
                    <el-popover placement="top" width="400px">
                        <pre>{{ row.description }}</pre>
                        <template #reference>
                            <el-link icon="el-icon-info-filled" link size="small" type="primary">查看</el-link>
                        </template>
                    </el-popover>
                </template>
                <template #publishStatus="{ row }">
                    <div v-html="formatPublishStatus(row.publishStatus)"></div>
                </template>
                <template #defaultFormula="{ row }">
                    <span>{{ formatConditions(row.defaultFormula) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="row.publishStatus === '0'" icon="el-icon-check" link size="small" type="success" @click="handleClickBtnPublishTheFormula(row)">发布</el-button>
                    <el-button v-if="row.publishStatus === '1'" icon="el-icon-close" link size="small" type="danger" @click="handleClickBtnWriteOffFormula(row)">注销</el-button>
                    <el-button v-if="row.publishStatus === '0'" icon="el-icon-edit" link size="small" type="warning" @click="handleClickBtnModifyTheFormula(row)">修改</el-button>
                    <!-- 已发布的修改 -->
                    <el-button v-if="row.publishStatus === '1'" icon="el-icon-edit" link size="small" type="warning" @click="handleClickBtnModifyTheFormula(row)">修改已发布</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!--  / 新增修改公式  -->
        <el-drawer v-if="thePriceIsCalculatedFormulaVisible" v-model="thePriceIsCalculatedFormulaVisible" :title="thePriceIsCalculatedFormulaTitle" size="880px" @close="handleClickCloseThePriceIsCalculatedFormula">
            <div v-loading="thePriceIsCalculatedFormulaLoading" :element-loading-text="thePriceIsCalculatedFormulaLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="thePriceIsCalculatedFormulaForm" :model="thePriceIsCalculatedFormulaForm" :rules="thePriceIsCalculatedFormulaRules" label-width="auto">
                        <el-form-item label="公式名称" prop="name">
                            <el-input v-model="thePriceIsCalculatedFormulaForm.name" :disabled="isDisabled" clearable maxlength="20" placeholder="请输入公式名称" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item label="费用类型" prop="type">
                            <el-select v-model="thePriceIsCalculatedFormulaForm.type" :disabled="isDisabled" clearable placeholder="请选择费用类型">
                                <el-option v-for="item in costTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="运输类型" prop="transType">
                            <el-select v-model="thePriceIsCalculatedFormulaForm.transType" :disabled="isDisabled" clearable placeholder="请选择产品类型">
                                <el-option v-for="item in valuationTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="公式描述" prop="description">
                            <pre>{{ thePriceIsCalculatedFormulaForm.description }}</pre>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="thePriceIsCalculatedFormulaForm.remark" :autosize="{ minRows: 2, maxRows: 4 }" :disabled="isDisabled" maxlength="60" placeholder="请输入备注" show-word-limit type="textarea"></el-input>
                        </el-form-item>
                        <el-form-item label="变量选择">
                            <div class="box__grid__parameter">
                                <el-button v-for="item in operatorList" :key="item.id" @click="setParameterSelection(item.variableName, 'Var', item.id)">{{ item.variableName }}</el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="参数选择">
                            <div class="box__grid__parameter">
                                <el-button v-for="item in parameterList" :key="item.id" @click="setParameterSelection(item.variableName, 'Param', item.id)">
                                    {{ item.variableName }}
                                </el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="价格本变量">
                            <div class="box__grid__parameter">
                                <el-button v-for="item in priceList" :key="item.code" @click="setParameterSelection(item.name, 'Other', item.code)">{{ item.name }}</el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="运算符号">
                            <div class="box__grid__parameter">
                                <el-button v-for="item in operatorSymbolList" :key="item.code" @click="setParameterSelection(item.code, 'os')">{{ item.name }}</el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="运算数字">
                            <div class="box__numberButton">
                                <el-button v-for="(item, index) in 10" :key="index" @click="setParameterSelection(index)">{{ index }}</el-button>
                                <el-button @click="setParameterSelection('.')">.</el-button>
                            </div>
                        </el-form-item>
                        <el-radio-group v-model="thePriceIsCalculatedFormulaForm.index" class="billingFormulaForm__radio" @change="handleIndexChange">
                            <el-radio :disabled="isDisabled" border label="-1">
                                <el-form-item label="默认公式" style="padding: 15px">
                                    <div class="tag__box">
                                        <div class="tag__m">
                                            <span v-for="(tag, index) in thePriceIsCalculatedFormulaForm.defaultFormulaList" :key="index">
                                                {{ tag.value }}
                                            </span>
                                            <span v-if="thePriceIsCalculatedFormulaForm.defaultFormulaList && thePriceIsCalculatedFormulaForm.defaultFormulaList.length > 0 && !isDisabled" class="btn__revoke" @click="revokeFormula('defaultFormulaList')">撤销</span>
                                        </div>
                                        <el-link v-if="!isDisabled" @click="resetTheFormulaOrCondition('defaultFormulaList')">重置</el-link>
                                    </div>
                                </el-form-item>
                            </el-radio>
                            <el-radio v-for="(item, index) in thePriceIsCalculatedFormulaForm.formulaConditionList" :key="index" :label="index" border class="list">
                                <div class="list-item" draggable="true" @dragenter="dragenter($event, index)" @dragover="dragover($event, index)" @dragstart="dragstart(index)">
                                    <div class="list__item__sort"></div>
                                    <div class="list__item__serialNumber">{{ index + 1 }}</div>
                                    <el-switch v-model="item.status" active-color="#13ce66" active-text="启用" active-value="0" class="switch__label" inactive-text="禁用" inactive-value="1" @change="item.status == '1' ? (item.status = '1') : (item.status = '0')"></el-switch>
                                    <el-radio-group v-model="item.conditionType" class="billingFormulaForm__radio__condition" @change="(e) => changeConditionType(e, index)">
                                        <el-radio border label="1">
                                            <el-form-item label="计算条件">
                                                <div class="tag__box">
                                                    <div class="tag__m">
                                                        <span v-for="(tag, index) in item.conditionsList" :key="index">
                                                            {{ tag.value }}
                                                        </span>
                                                        <span v-if="(item.conditionsList && item.conditionsList.length > 0 && !item.id) || !isDisabled" class="btn__revoke" @click="revokeFormula('conditionsList', index)">撤销</span>
                                                    </div>
                                                    <el-link v-if="!item.id || !isDisabled" @click="resetTheFormulaOrCondition('conditionsList', index)">重置</el-link>
                                                </div>
                                            </el-form-item>
                                        </el-radio>
                                        <el-radio border label="2">
                                            <el-form-item label="计算公式">
                                                <div class="tag__box">
                                                    <div class="tag__m">
                                                        <span v-for="(tag, index) in item.formulaList" :key="index">
                                                            {{ tag.value }}
                                                        </span>
                                                        <span v-if="(item.formulaList && item.formulaList.length > 0 && !item.id) || !isDisabled" class="btn__revoke" @click="revokeFormula('formulaList', index)">撤销</span>
                                                    </div>
                                                    <el-link v-if="!item.id || !isDisabled" @click="resetTheFormulaOrCondition('formulaList', index)">重置</el-link>
                                                </div>
                                            </el-form-item>
                                        </el-radio>
                                    </el-radio-group>
                                </div>
                                <div style="padding-right: 15px">
                                    <el-link @click="deleteCondition(index)">删除</el-link>
                                </div>
                            </el-radio>
                        </el-radio-group>
                        <div class="drawer__add">
                            <div class="add__tip"><span>调整位置：鼠标变为</span><i class="el-icon-rank"></i><span>时上下拖拽</span></div>
                            <el-button icon="el-icon-plus" type="primary" @click="addCondition">新增</el-button>
                        </div>
                    </el-form>
                </el-card>
                <div style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="handleClickCloseThePriceIsCalculatedFormula">取消</el-button>
                    <el-button v-if="thePriceIsCalculatedFormulaModify && !isDisabled" type="primary" @click="handleClickAddedModifications('edit')">修改</el-button>
                    <el-button v-else-if="thePriceIsCalculatedFormulaModify && isDisabled" type="primary" @click="handleClickAddedModifications('publish')">保存</el-button>
                    <el-button v-else type="primary" @click="handleClickAddedModifications('add')">新增</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import priceCalculatedByTheFormula from '@/api/carrierEnd/priceCalculatedByTheFormula';
import billingFactorSettings from '@/api/platformFeatures/billingFactorSettings';
import { selectDictLabel } from '@/utils/dictLabel';
import billingParameterSettings from '@/api/platformFeatures/billingParameterSettings';

export default {
    name: 'PriceCalculatedByTheFormula',
    components: {
        SearchButton,
        RightToolbar,
        ColumnTable
    },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                type: '',
                transType: '',
                status: '',
                name: '',
                publishStatus: ''
            },
            valuationTypeList: [],
            loading: false,
            orderList: [],
            total: 0,
            priceBookActivationList: [],
            costTypeList: [],
            publishTheStatusOfList: [
                {
                    name: '未发布',
                    code: '0'
                },
                {
                    name: '未发布',
                    code: '1'
                }
            ],
            columns: [
                { title: '公式编码', key: 'code', align: 'center', minWidth: '200px', columnShow: true, fixed: 'left' },
                { title: '公式名称', key: 'name', minWidth: '150px', align: 'center', columnShow: true,showOverflowTooltip: true },
                { title: '费用类型', key: 'type', minWidth: '120px', align: 'center', columnShow: true },
                { title: '运输类型', key: 'transType', width: '80px', align: 'center', columnShow: true },
                { title: '公式状态', key: 'status', align: 'center', columnShow: true },
                { title: '默认公式', key: 'defaultFormula', minWidth: '280px', align: 'center', columnShow: true ,showOverflowTooltip: true},
                { title: '公式说明', key: 'description', minWidth: '120px', align: 'center', columnShow: true },
                { title: '创建时间', key: 'createDate', minWidth: '120px', align: 'center', columnShow: true, sortable: true },
                { title: '备注', key: 'remark', minWidth: '160px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发布状态', key: 'publishStatus', minWidth: '80px', align: 'center', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '200px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            thePriceIsCalculatedFormulaTitle: '新增价格本计算公式',
            thePriceIsCalculatedFormulaVisible: false,
            thePriceIsCalculatedFormulaModify: false,
            thePriceIsCalculatedFormulaForm: {
                name: '',
                type: '',
                transType: '',
                description: '',
                remark: '',
                index: '-1',
                defaultFormulaList: [],
                formulaConditionList: []
            },
            thePriceIsCalculatedFormulaRules: {
                name: [{ required: true, message: '请输入公式名称', trigger: 'blur' }],
                type: [{ required: true, message: '请选择费用类型', trigger: 'change' }],
                transType: [{ required: true, message: '请选择产品类型', trigger: 'change' }]
            },
            thePriceIsCalculatedFormulaLoading: false,
            thePriceIsCalculatedFormulaLoadingText: '加载中...',
            operatorSymbolList: [],
            // 源对象的下标
            dragIndex: '',
            // 目标对象的下标
            enterIndex: '',
            timeout: null,
            operatorList: [],
            parameterList: [],
            priceList: [],
            isDisabled: false,
            formInline: {},
            isShowAll: false
        };
    },
    computed: {
        // 格式化默认公式
        formatConditions() {
            return (val) => {
                if (val && this.operatorList && this.operatorList.length > 0 &&  this.parameterList && this.parameterList.length > 0 &&  this.priceList && this.priceList.length > 0) {
                    const dataArr = val.split(/(\+|-|\*|\/|\(|\)|&|\||=|!|>|<)/);
                    dataArr.forEach((item, index) => {
                        if (item.indexOf('@') > -1) {
                            const itemArr = item.split('@');
                            const id = itemArr[0];
                            const type = itemArr[1];
                            if (type === 'Var') {
                                const variableName = this.operatorList && this.operatorList.length > 0 ? this.operatorList.find((item) => item.id === id)?.variableName || '' : '';
                                dataArr[index] = { id, type, value: variableName };
                            } else if (type === 'Param') {
                                const parameterName = this.parameterList && this.parameterList.length > 0 ? this.parameterList.find((item) => item.id === id)?.variableName || '' : '';
                                dataArr[index] = { id, type, value: parameterName };
                            }
                        } else if (item.indexOf('#') > -1) {
                            const otherExpense = this.priceList.find((i) => i.code === item);
                            dataArr[index] = { id: otherExpense.code, type: 'Other', value: otherExpense.name };
                        } else {
                            dataArr[index] = { type: '', value: item };
                        }
                    });
                    // dataArr value 拼接
                    return dataArr.map((item) => item.value).join('');
                } else {
                    return val;
                }
            };
        },
        formatPublishStatus() {
            return (value) => {
                const publishStatusText = selectDictLabel(this.publishTheStatusOfList, value);
                if (value === '0') {
                    return `<span style="color: #F4AC00">${publishStatusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #1ACD7E">${publishStatusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #B1B1B1">${publishStatusText}</span>`;
                } else {
                    return `<span style="color: #B1B1B1">${publishStatusText}</span>`;
                }
            };
        },
        formatTheBillingClassification() {
            return (value) => selectDictLabel(this.costTypeList, value);
        },
        formatTheProductType() {
            return (value) => selectDictLabel(this.valuationTypeList, value);
        }
    },
    watch: {
        'thePriceIsCalculatedFormulaForm.defaultFormulaList': {
            handler() {
                this.setFormulaDescription();
            },
            deep: true
        },
        'thePriceIsCalculatedFormulaForm.formulaConditionList': {
            handler() {
                this.setFormulaDescription();
            },
            deep: true
        }
    },
    created() {
        this.getDict();
        this.getList();
        // 获取参数和变量
        this.getListOfParametersAndVariables();
    },
    destroyed() {
        // 每次离开当前界面时，清除定时器
        clearInterval(this.timeout);
        this.timeout = null;
    },
    methods: {
        addCondition() {
            // this.thePriceIsCalculatedFormulaForm.formulaConditionList 新增 一条计算公式
            this.thePriceIsCalculatedFormulaForm.formulaConditionList.splice(this.thePriceIsCalculatedFormulaForm.formulaConditionList.length + 1, 0, {
                conditions: '',
                conditionsList: [],
                formula: '',
                formulaList: [],
                conditionType: '1',
                status: '0'
            });
            this.thePriceIsCalculatedFormulaForm.index = this.thePriceIsCalculatedFormulaForm.formulaConditionList.length - 1;
            this.thePriceIsCalculatedFormulaForm.formulaConditionList.forEach((item, i) => {
                if (i != this.thePriceIsCalculatedFormulaForm.index) {
                    item.conditionType = '';
                }
            });
        },
        changeConditionType(e, index) {
            this.thePriceIsCalculatedFormulaForm.index = index;
            this.thePriceIsCalculatedFormulaForm.formulaConditionList[index].conditionType = e;
            // 设置 不是 this.thePriceIsCalculatedFormulaForm.formulaConditionList[index].conditionType 为空
            this.thePriceIsCalculatedFormulaForm.formulaConditionList.forEach((item, i) => {
                if (i != index) {
                    item.conditionType = '';
                }
            });
            // 更新视图
            this.$forceUpdate();
        },
        // 删除计算公式
        deleteCondition(index) {
            // 判断 this.thePriceIsCalculatedFormulaForm.formulaConditionList[index]
            const { id } = this.thePriceIsCalculatedFormulaForm.formulaConditionList[index];
            // if id不存在 或者 this.isDisabled 为false
            if (!id || !this.isDisabled) {
                this.thePriceIsCalculatedFormulaForm.formulaConditionList.splice(index, 1);
            } else {
                this.$message.warning('该条计算公式已经保存，不能删除');
            }
        },
        dragenter(e, index) {
            e.preventDefault();
            this.enterIndex = index;
            if (this.timeout !== null) {
                clearTimeout(this.timeout);
            }
            // 拖拽事件的防抖
            this.timeout = setTimeout(() => {
                if (this.dragIndex !== index) {
                    const source = this.thePriceIsCalculatedFormulaForm.formulaConditionList[this.dragIndex];
                    this.thePriceIsCalculatedFormulaForm.formulaConditionList.splice(this.dragIndex, 1);
                    this.thePriceIsCalculatedFormulaForm.formulaConditionList.splice(index, 0, source);
                    // 排序变化后目标对象的索引变成源对象的索引
                    this.dragIndex = index;
                    //选中框设置
                    this.thePriceIsCalculatedFormulaForm.index = index;
                    this.thePriceIsCalculatedFormulaForm.formulaConditionList.forEach((item, i) => {
                        if (i != this.thePriceIsCalculatedFormulaForm.index) {
                            item.conditionType = '';
                        }
                    });
                    this.thePriceIsCalculatedFormulaForm.formulaConditionList[this.thePriceIsCalculatedFormulaForm.index].conditionType = '1';
                }
            }, 100);
        },
        dragover(e, index) {
            e.preventDefault();
        },
        dragstart(index) {
            this.dragIndex = index;
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.valuationTypeList = await this.getDictList('fourpl_product_type');
            this.operatorSymbolList = await this.getDictList('cost_operational_symbol');
            this.priceList = await this.getDictList('cost_price_of_this_parameter');
            this.costTypeList = await this.getDictList('cost_price_book_cost_type');
            this.priceBookActivationList = await this.getDictList('cost_price_book_status');
            this.publishTheStatusOfList = await this.getDictList('cost_price_this_release_status');
        },
        getList() {
            this.loading = true;
            priceCalculatedByTheFormula
                .getPriceBookFormulaList(this.queryForm)
                .then((res) => {
                    if (res.code === 200) {
                        this.orderList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 获取参数和变量
         */
		async getListOfParametersAndVariables() {
			try {
				const res1 = await billingFactorSettings.billingFactorListByFormula({});
				if (res1.code === 200 && res1.data) {
					this.operatorList = res1.data || [];
				} else {
					this.$message.error('无可用计费因子');
					return false;
				}

				const res2 = await billingParameterSettings.billingParameterListByFormulaCopy({});
				if (res2.code === 200 && res2.data) {
					this.parameterList = res2.data || [];
				} else {
					this.$message.error('无可用计费公式');
					return false;
				}
			} catch (error) {
				this.$message.error('获取数据失败');
			}
		},
        // 获取详情
        getPriceBookFormulaDetail(id) {
            this.thePriceIsCalculatedFormulaLoading = true;
            this.thePriceIsCalculatedFormulaLoadingText = '获取详情中...';
            priceCalculatedByTheFormula
                .getPriceBookFormulaDetail({ id })
                .then((res) => {
                    if (res.code === 200) {
                        const { defaultFormula, formulaConditionList, publishStatus } = res.data;
                        const defaultFormulaArr = defaultFormula.split(/(\+|-|\*|\/|\(|\)|&|\||=|!|>|<)/);
                        if (defaultFormula) {
                            defaultFormulaArr.forEach((item, index) => {
                                if (item.indexOf('@') > -1) {
                                    const itemArr = item.split('@');
                                    const id = itemArr[0];
                                    const type = itemArr[1];
                                    if (type === 'Var') {
                                        const variableName = this.operatorList.find((item) => item.id === id)?.variableName || '';
                                        defaultFormulaArr[index] = { id, type, value: variableName };
                                    } else if (type === 'Param') {
                                        const paramName = this.parameterList.find((item) => item.id === id)?.variableName || '';
                                        defaultFormulaArr[index] = { id, type, value: paramName };
                                    }
                                } else if (item.indexOf('#') > -1) {
                                    const otherExpense = this.priceList.find((i) => i.code === item);
                                    defaultFormulaArr[index] = { id: otherExpense.code, type: 'Other', value: otherExpense.name };
                                } else {
                                    defaultFormulaArr[index] = { value: item, type: 'os' };
                                }
                            });
                        }
                        if (formulaConditionList) {
                            formulaConditionList.forEach((item) => {
                                const conditionsArr = item.conditions.split(/(\+|-|\*|\/|\(|\)|&|\||=|!|>|<)/);
                                conditionsArr.forEach((item2, index) => {
                                    if (item2.indexOf('@') > -1) {
                                        const itemArr = item2.split('@');
                                        const id = itemArr[0];
                                        const type = itemArr[1];
                                        if (type === 'Var') {
                                            const variableName = this.operatorList.find((item) => item.id === id)?.variableName || '';
                                            conditionsArr[index] = { id, type, value: variableName };
                                        } else if (type === 'Param') {
                                            const paramName = this.parameterList.find((item) => item.id === id)?.variableName || '';
                                            conditionsArr[index] = { id, type, value: paramName };
                                        }
                                    } else if (item2.indexOf('#') > -1) {
                                        const otherExpense = this.priceList.find((i) => i.code === item2);
                                        conditionsArr[index] = { id: otherExpense.code, type: 'Other', value: otherExpense.name };
                                    } else {
                                        conditionsArr[index] = { value: item2, type: 'os' };
                                    }
                                });
                                item.conditionsList = conditionsArr;

                                const formulaArr = item.formula.split(/(\+|-|\*|\/|\(|\)|&|\||=|!|>|<)/);
                                formulaArr.forEach((item2, index) => {
                                    if (item2.indexOf('@') > -1) {
                                        const itemArr = item2.split('@');
                                        const id = itemArr[0];
                                        const type = itemArr[1];
                                        if (type === 'Var') {
                                            const variableName = this.operatorList.find((item) => item.id === id)?.variableName || '';
                                            formulaArr[index] = { id, type, value: variableName };
                                        } else if (type === 'Param') {
                                            const paramName = this.parameterList.find((item) => item.id === id)?.variableName || '';
                                            formulaArr[index] = { id, type, value: paramName };
                                        }
                                    } else if (item2.indexOf('#') > -1) {
                                        const otherExpense = this.priceList.find((i) => i.code === item2);
                                        formulaArr[index] = { id: otherExpense.code, type: 'Other', value: otherExpense.name };
                                    } else {
                                        formulaArr[index] = { value: item2, type: 'os' };
                                    }
                                });
                                item.formulaList = formulaArr;
                            });
                        }

                        // 赋值 回显数据
                        this.thePriceIsCalculatedFormulaForm = {
                            ...res.data,
                            defaultFormulaList: defaultFormulaArr
                        };

                        // publishStatus 表示已发布
                        publishStatus == '1' ? (this.isDisabled = true) : (this.isDisabled = false);

                        // 选中默认公式
                        if (publishStatus == '1') {
                            this.thePriceIsCalculatedFormulaForm.index = '0' * 1;
                            if (this.thePriceIsCalculatedFormulaForm.formulaConditionList.length > 0) {
                                this.thePriceIsCalculatedFormulaForm.formulaConditionList[this.thePriceIsCalculatedFormulaForm.index].conditionType = '1';
                            }
                        } else {
                            this.thePriceIsCalculatedFormulaForm.index = '-1';
                        }
                    }
                })
                .finally(() => {
                    this.thePriceIsCalculatedFormulaLoading = false;
                });
        },
        // 确定新增增值服务
        handleClickAddedModifications(type) {
            // thePriceIsCalculatedFormulaForm.defaultFormulaList 不能为空 提示
            if (this.thePriceIsCalculatedFormulaForm.defaultFormulaList.length === 0) {
                this.$message.error('默认公式不能为空');
                return false;
            }
            // thePriceIsCalculatedFormulaForm.defaultFormulaList.formulaConditionList conditionsList formulaList 不能为空 提示
            if (this.thePriceIsCalculatedFormulaForm.formulaConditionList.length > 0) {
                let flag = false;
                this.thePriceIsCalculatedFormulaForm.formulaConditionList.forEach((item) => {
                    if (item.conditionsList.length === 0 || item.formulaList.length === 0) {
                        flag = true;
                    }
                });
                if (flag) {
                    this.$message.error('条件公式或计算公式不能为空');
                    return false;
                }
            }
            // add 新增 edit 修改 publish 修改已发布
            this.$refs.thePriceIsCalculatedFormulaForm.validate((valid) => {
                if (valid) {
                    this.thePriceIsCalculatedFormulaLoading = true;
                    if (type === 'add') {
                        this.thePriceIsCalculatedFormulaLoadingText = '新增中...';
                    } else if (type === 'edit' || type === 'publish') {
                        this.thePriceIsCalculatedFormulaLoadingText = '修改中...';
                    }

                    // 对默认公式进行处理
                    let defaultFormula = '';
                    this.thePriceIsCalculatedFormulaForm.defaultFormulaList.forEach((item) => {
                        if (item.type === 'Param' || item.type === 'Var') {
                            defaultFormula += item.id + '@' + item.type;
                        } else if (item.type === 'Other') {
                            defaultFormula += item.id;
                        } else {
                            defaultFormula += item.value;
                        }
                    });

                    // 对新增公式组 处理
                    this.thePriceIsCalculatedFormulaForm.formulaConditionList.forEach((item) => {
                        item.formula = '';
                        item.conditions = '';
                        item.formulaList.forEach((item2) => {
                            if (item2.type === 'Param' || item2.type === 'Var') {
                                item.formula += item2.id + '@' + item2.type;
                            } else if (item2.type === 'Other') {
                                item.formula += item2.id;
                            } else {
                                item.formula += item2.value;
                            }
                        });
                        item.conditionsList.forEach((item2) => {
                            if (item2.type === 'Param' || item2.type === 'Var') {
                                item.conditions += item2.id + '@' + item2.type;
                            } else if (item2.type === 'Other') {
                                item.conditions += item2.id;
                            } else {
                                item.conditions += item2.value;
                            }
                        });
                        item.sort = this.thePriceIsCalculatedFormulaForm.formulaConditionList.indexOf(item);
                    });

                    // 新增公式组
                    if (type === 'add') {
                        priceCalculatedByTheFormula
                            .addPriceBookFormula({ ...this.thePriceIsCalculatedFormulaForm, defaultFormula })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功新增价格本计算公式！');
                                    this.handleClickCloseThePriceIsCalculatedFormula();
                                    this.getList();
                                } else {
                                    this.$message.error(res.data);
                                }
                            })
                            .finally(() => {
                                this.thePriceIsCalculatedFormulaLoading = false;
                            });
                    }

                    // 修改公式组
                    if (type === 'edit') {
                        // this.thePriceIsCalculatedFormulaForm 删除 code companyId publishStatus status
                        delete this.thePriceIsCalculatedFormulaForm.code;
                        delete this.thePriceIsCalculatedFormulaForm.companyId;
                        delete this.thePriceIsCalculatedFormulaForm.publishStatus;
                        delete this.thePriceIsCalculatedFormulaForm.status;
                        priceCalculatedByTheFormula
                            .addPriceBookFormula({ ...this.thePriceIsCalculatedFormulaForm, defaultFormula })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功修改价格本计算公式！');
                                    this.handleClickCloseThePriceIsCalculatedFormula();
                                    this.getList();
                                } else {
                                    this.$message.error(res.data);
                                }
                            })
                            .finally(() => {
                                this.thePriceIsCalculatedFormulaLoading = false;
                            });
                    }

                    // 修改已发布公式组
                    if (type === 'publish') {
                        // this.thePriceIsCalculatedFormulaForm 删除 code companyId publishStatus status
                        delete this.thePriceIsCalculatedFormulaForm.code;
                        delete this.thePriceIsCalculatedFormulaForm.companyId;
                        delete this.thePriceIsCalculatedFormulaForm.publishStatus;
                        delete this.thePriceIsCalculatedFormulaForm.status;
                        editFormulaPriceBook({ ...this.thePriceIsCalculatedFormulaForm })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功修改价格本计算公式！');
                                    this.handleClickCloseThePriceIsCalculatedFormula();
                                    this.getList();
                                } else {
                                    this.$message.error(res.data);
                                }
                            })
                            .finally(() => {
                                this.thePriceIsCalculatedFormulaLoading = false;
                            });
                    }
                }
            });
        },
        // 新增公式
        handleClickBtnAddedFormulas() {
            this.thePriceIsCalculatedFormulaVisible = true;
            this.thePriceIsCalculatedFormulaTitle = '新增价格本计算公式';
            this.thePriceIsCalculatedFormulaModify = false;
        },
        // 修改公式
        handleClickBtnModifyTheFormula(val) {
            this.thePriceIsCalculatedFormulaVisible = true;
            this.thePriceIsCalculatedFormulaTitle = '修改价格本计算公式';
            this.thePriceIsCalculatedFormulaModify = true;
            const { id } = val;
            // 获取详情
            if (id) this.getPriceBookFormulaDetail(id);
        },
        // 发布公式
        handleClickBtnPublishTheFormula(row) {
            const { id, name } = row;
            if (id) {
                this.$confirm('是否发布公式：' + name, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        this.loading = true;
                        priceCalculatedByTheFormula
                            .releaseFormulaPriceBook({ id })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功发布公式！');
                                    this.getList();
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .finally(() => {
                                this.loading = false;
                            });
                    })
                    .catch(() => {});
            } else {
                this.$message.error('公式不存在！');
            }
        },
        // 注销公式
        handleClickBtnWriteOffFormula(row) {
            const { id, name } = row;
            if (id) {
                this.$confirm('是否注销公式：' + name, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        this.loading = true;
                        priceCalculatedByTheFormula
                            .logoutFormulaPriceBook({ id })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('成功注销公式！');
                                    this.getList();
                                } else {
                                    this.$message.error(res.msg);
                                }
                            })
                            .finally(() => {
                                this.loading = false;
                            });
                    })
                    .catch(() => {});
            } else {
                this.$message.error('公式不存在！');
            }
        },
        // 隐藏 新增修改公式
        handleClickCloseThePriceIsCalculatedFormula() {
            this.thePriceIsCalculatedFormulaVisible = false;
            this.$refs.thePriceIsCalculatedFormulaForm.resetFields();
            this.thePriceIsCalculatedFormulaForm = {
                name: '',
                type: '',
                transType: '',
                description: '',
                remark: '',
                index: '-1',
                defaultFormulaList: [],
                formulaConditionList: []
            };
            // 取消已发布状态
            this.isDisabled = false;
            this.$nextTick(() => {
                // 加上延时避免 mounted 方法比页面加载早执行 或者 对img进行块级化设置宽高进行 提前站位
                setTimeout(() => {
                    // 描述文字清除
                    this.thePriceIsCalculatedFormulaForm.description = '';
                }, 1000);
            });
        },
        handleIndexChange(e) {
            this.thePriceIsCalculatedFormulaForm.index = e;
            this.thePriceIsCalculatedFormulaForm.formulaConditionList.forEach((item) => {
                item.conditionType = '';
            });
        },
        handleQuery() {
            this.queryForm.current = 1;
            this.getList();
        },
        // 调整公式状态
        handleSwitchChange(row) {
            const { status, id } = row;
            if (status && id) {
                this.loading = true;
                priceCalculatedByTheFormula
                    .updateFormulaStatus({ id, status })
                    .then((res) => {
                        if (res.code === 200) {
                            this.$message.success('成功修改公式状态！');
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .finally(() => {
                        this.loading = false;
                        this.getList();
                    });
            }
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleQuery();
        },
        resetTheFormulaOrCondition(type, i) {
            if (type === 'defaultFormulaList') {
                this.thePriceIsCalculatedFormulaForm.defaultFormulaList = [];
            } else {
                this.thePriceIsCalculatedFormulaForm.formulaConditionList[i][type] = [];
            }
            this.$forceUpdate();
        },
        // 撤销操作
        revokeFormula(type, i) {
            if (type === 'defaultFormulaList') {
                this.thePriceIsCalculatedFormulaForm[type].pop();
            } else {
                this.thePriceIsCalculatedFormulaForm.formulaConditionList[i][type].pop();
            }
            this.$forceUpdate();
        },
        // 描述文字传参
        setFormulaDescription() {
            // this.thePriceIsCalculatedFormulaForm 拷贝
            const data = JSON.parse(JSON.stringify(this.thePriceIsCalculatedFormulaForm));
            // 对默认公式进行处理
            let defaultFormula = '';
            data.defaultFormulaList.forEach((item) => {
                if (item.type === 'Param' || item.type === 'Var') {
                    defaultFormula += item.id + '@' + item.type;
                } else if (item.type === 'Other') {
                    defaultFormula += item.id;
                } else {
                    defaultFormula += item.value;
                }
            });

            // 对新增公式组 处理
            data.formulaConditionList.forEach((item) => {
                item.formula = '';
                item.conditions = '';
                item.formulaList.forEach((item2) => {
                    if (item2.type === 'Param' || item2.type === 'Var') {
                        item.formula += item2.id + '@' + item2.type;
                    } else if (item2.type === 'Other') {
                        item.formula += item2.id;
                    } else {
                        item.formula += item2.value;
                    }
                });
                item.conditionsList.forEach((item2) => {
                    if (item2.type === 'Param' || item2.type === 'Var') {
                        item.conditions += item2.id + '@' + item2.type;
                    } else if (item2.type === 'Other') {
                        item.conditions += item2.id;
                    } else {
                        item.conditions += item2.value;
                    }
                });
                item.sort = data.formulaConditionList.indexOf(item);
            });
            priceCalculatedByTheFormula.translatePriceBookFormula({ ...data, defaultFormula }).then((res) => {
                if (res.code === 200) {
                    this.thePriceIsCalculatedFormulaForm.description = res.data;
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        // 编辑公式
        setParameterSelection(value, type, id) {
            if (this.thePriceIsCalculatedFormulaForm.index === '-1') {
                this.thePriceIsCalculatedFormulaForm.defaultFormulaList.push({
                    id,
                    type,
                    value
                });
            } else {
                const { conditionType, conditionsList, formulaList, id: formulaConditionListId } = this.thePriceIsCalculatedFormulaForm.formulaConditionList[this.thePriceIsCalculatedFormulaForm.index];
                // 已发布 id存在 提示
                if (formulaConditionListId && this.isDisabled) {
                    this.$message.warning('已发布的公式不可编辑！可修改公式状态与新增公式');
                }
                // 新增，修改
                else {
                    if (conditionType === '1') {
                        conditionsList.push({ id, type, value });
                    } else if (conditionType === '2') {
                        formulaList.push({ id, type, value });
                    }
                }
            }
            this.$forceUpdate();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
:deep(.el-input.is-disabled .el-input__inner),
:deep(.el-textarea.is-disabled .el-textarea__inner),
:deep(.el-radio__input.is-disabled + span.el-radio__label) {
    color: #666666;
    background-color: #f5f7fa;
}
.billingFormulaForm__radio {
    :deep(.el-radio.is-bordered) {
        height: auto;
        width: 100%;
        display: flex;
        align-items: center;
        padding: 15px;

        > :nth-child(1) {
            display: none;
        }
    }
    > :deep(.el-radio.is-bordered) {
        padding: 0;
    }
}
.billingFormulaForm__radio,
.billingFormulaForm__radio__condition {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    align-items: stretch;

    :deep(.el-radio.is-bordered + .el-radio.is-bordered) {
        margin-left: 0;
    }

    :deep(.el-radio__label) {
        display: flex;
        padding-left: 0;
        align-items: center;
        gap: 20px;
        flex: 1;

        > :nth-child(1) {
            flex: 1;
            margin-bottom: 0;
        }
    }
}
.billingFormulaForm__radio__condition {
    :deep(.el-form-item__label) {
        cursor: move;
    }
}
.box__grid__parameter {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    button {
        margin-left: 0;
    }
}
.btn__revoke {
    margin-left: auto;
    color: #5670fe;
    cursor: pointer;
}
.tag__box {
    display: flex;
    grid-gap: 10px;
    flex: 1;
}
.tag__m {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex: 1;
    padding: 0 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
}
.box__numberButton {
    display: flex;
    flex-wrap: wrap;
    grid-gap: 10px;
    :deep(.el-button + .el-button) {
        margin-left: 0;
    }
}
.box__grid__parameter {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    button {
        margin-left: 0;
    }
}
.btn__revoke {
    margin-left: auto;
    color: #5670fe;
    cursor: pointer;
}
.list {
    :deep(.list-item) {
        cursor: move;
        transition: transform 0.3s;
        display: flex;
        gap: 15px;
        padding: 15px 0 15px 15px;
        position: relative;
        align-items: center;

        :deep(.list__item__sort) {
            width: 0;
            height: 0;
            border-top: 30px solid #666666;
            border-right: 30px solid transparent;
            position: absolute;
            top: 0;
            left: 0;
        }

        :deep(.list__item__serialNumber) {
            font-size: 12px;
            text-align: center;
            color: #ffffff;
            position: absolute;
            top: 3px;
            left: 5px;
        }
    }
}
:deep(.is-bordered.is-checked .list-item .list__item__sort) {
    border-top: 30px solid #5670fe;
}
.box__grid__parameter {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    button {
        margin-left: 0;
    }
}
.only-read {
    pointer-events: none;
}
.drawer__add {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    margin-top: 10px;

    :deep(.add__tip) {
        font-size: 12px;
        color: #b1b1b1;
        display: flex;
        gap: 3px;
        align-items: baseline;
    }
}
pre {
    color: #666666;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
