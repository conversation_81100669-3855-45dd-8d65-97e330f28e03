<!--
 * @Author: saya
 * @Date: 2023-10-26 14:42:16
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-11-06 09:00:59
 * @FilePath: \zxhy-4pl-font-pc\src\views\waybillManagement\pharmaceuticalloading\incubator.vue
 * @Description: 
-->
<template>
    <div style="width: 500px;">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"> 全选</el-checkbox>
        <el-collapse v-model="activeNames" @change="handleChange" v-for="(item, index) in sensorList" :key="index">
            <el-checkbox-group v-model="checkedCities">
                <el-collapse-item>
                    <template #title>
                        <div
                            style="width: 450px;display: flex;justify-content: space-between;height:30px;line-height:30px;">
                            <div style="display: flex;">
                                <el-checkbox :label="item.recordId"
                                    @change="handleCheckedCitiesChange(item)"><br></el-checkbox>
                                <div style="font-size: 14px;font-weight: 600;">
                                    {{ item.incubatorName }}
                                </div>
                            </div>
                            <div>
                                {{ item.transOrderNum }}单 {{ item.goodsTotalNum }}件
                            </div>
                        </div>
                    </template>
                    <div style="margin-left: 20px;" v-for="(coun, index1) in item.countyList" :key="index1"
                        @click.stop="obtainWaybill(item, index, coun, index1)">
                        <el-collapse-item>
                            <template #title>
                                <div
                                    style="width: 450px;display: flex;justify-content: space-between;height:30px;line-height:30px;padding-right: 20px;">
                                    <div style="font-size: 14px;font-weight: 500;">
                                        {{ coun.provinceName }} {{ coun.cityName }} {{ coun.countyName }}
                                    </div>
                                    <div>
                                        {{ coun.transOrderNum }}单 {{ coun.goodsTotalNum }}件
                                    </div>
                                </div>
                            </template>
                            <div style="margin-left: 20px;font-size:12px" v-for="(listItem, index2) in coun.list"
                                :key="index2" @click.stop="obtainBoxCode(item, index, coun, index1, listItem, index2)">
                                <el-collapse-item :name="index2">
                                    <template #title>
                                        <div
                                            style="width: 450px;display: flex;justify-content: space-between;height:30px;line-height:30px;padding-right: 30px;">
                                            <div style="font-size: 14px;font-weight: 500;">
                                                {{ listItem.transOrderNo }}
                                            </div>
                                            <div>
                                                {{ coun.goodsTotalNum }}件
                                            </div>
                                        </div>
                                    </template>
                                    <div style="margin-left: 20px;font-size:12px;"
                                        v-for="(trans, index3) in listItem.listTrans" :key="index3">
                                        <div style="padding-right: 50px;display: flex;justify-content: space-between;">
                                            <div>
                                                {{ trans.code }}
                                            </div>
                                            <div>
                                                {{ trans.codeDesc }}
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-item>
                            </div>
                        </el-collapse-item>
                    </div>
                </el-collapse-item>
            </el-checkbox-group>
        </el-collapse>
    </div>
</template>

<script>
import pharmaceuticalloading from '@/api/waybillManagement/pharmaceuticalloading';
export default {
    name: "",
    props: {
        sensorList: {
            type: Array
        },
        citiesList: {
            type: Array
        },
    },
    data() {
        return {
            checkAll: false,
            isIndeterminate: false,
            checkedCities: [],
            cities: [],
            selectedList: [],
        };
    },
    mounted() {

    },
    methods: {
        // 点击运单获取数据
        obtainBoxCode(item, index, coun, index1, listItem, index2) {
            pharmaceuticalloading.incubatorCountyTransCodeList({
                recordId: item.id,
                countyId: coun.countyId,
                transOrderNo: listItem.transOrderNo
            }).then(res => {
                if (res.code === 200) {
                    const data = res.data.map(item => {
                        item.checked = false;
                        return item;
                    });
                    listItem.listTrans = data;
                }
            });
            // console.log(this.sensorList[index].countyList[index1].listItem[index2]);
        },
        // 点击保温箱省市区获取数据
        obtainWaybill(item, index, coun, index1) {
            pharmaceuticalloading.incubatorCountyTransOrderList({
                recordId: item.id,
                countyId: coun.countyId
            }).then(res => {
                if (res.code === 200) {
                    const data = res.data.map(item => {
                        item.checked = false;
                        return item;
                    });
                    coun.list = data;
                }
            });
            // console.log(this.sensorList[index].countyList[index1]);
        },

        handleCheckAllChange(val) {
            var arr = this.sensorList.map(item => {
                return item.recordId;
            });
            this.cities = arr;
            this.checkedCities = val ? this.cities : [];
            this.isIndeterminate = false;
            this.$emit('selectedIncubator', this.checkedCities)
        },

        handleCheckedCitiesChange(value) {
            const indexs = this.checkedCities.indexOf(value.recordId);
            if (indexs !== -1) {
                // 如果值已存在，就从数组中删除它
                this.checkedCities.splice(indexs, 1);
                var arr = this.checkedCities.filter(function (x, index) {
                    return index != indexs;
                });
                this.$emit('selectedIncubator', this.checkedCities)
                return this.checkedCities;
            } else {
                // 如果值不存在，就将它添加到数组中
                this.checkedCities.push(value.recordId);
                this.$emit('selectedIncubator', this.checkedCities)
                return this.checkedCities;
            }
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep {
    .el-collapse {
        border: 0px;
    }
}
</style>