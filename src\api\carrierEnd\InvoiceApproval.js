import request from '@/utils/request';

export default {
    // 开票审批提交
    submitInvoiceApproval: (params) => {
        return request.post('/tms/payment/invoiceApply/approve', params);
    },
    // 发起红冲申请
    submitInvoiceRedInvoicing: (params) => {
        return request.post('/tms/redRush/apply/submitApply', params);
    },
    // 红冲申请详情
    getInvoiceRedInvoicingDetail: (params) => {
        return request.get('/tms/redRush/apply/queryById', params);
    },
    // 红冲发票查询
    getInvoiceRedInvoicingList: (params) => {
        return request.get('/tms/redRush/apply/query-invoice', params);
    }
};
