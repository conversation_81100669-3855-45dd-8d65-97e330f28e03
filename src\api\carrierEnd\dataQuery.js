import http from '@/utils/request';
export default {
    // 百望开票记录-列表查询
    listInvoiceRecord: function (params) {
        return http.get('/tms/advancepayment/apply/getInvoiceRecord', params);
    },
    // 百望开票记录-导出
    exporInvoiceRecord: function (params, config, resDetail, responseType) {
        return http.get('/tms/advancepayment/apply/export-invoice-record', params, config, resDetail, responseType);
    },
    // 开票记录总金额查询
    getInvoiceRecordTotalMoney: function (params) {
        return http.get('/tms/advancepayment/apply/getInvoiceRecordTotalMoney', params);
    },
    // 预付款收款开票记录-合计金额计算
    getInvoicMoney: function (params) {
        return http.get('/tms/advancepayment/apply/getInvoicMoney', params);
    },
    // 预付款收款开票统计查询
    getAdvanceInvoiceRecordPage: function (params) {
        return http.get('/tms/advancepayment/apply/getAdvanceInvoiceRecordPage', params);
    }
};
