/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-08-04 16:33:42
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-09 13:16:24
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\purchasePriceAdjustment.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
    getList: function (inputForm) {
      return http.get('/erp/procure/adjust/purchaseAdjust/list',inputForm)
    },
  
    getIdOrder: function (inputForm) {
      return http.get('/erp/procure/adjust/purchaseAdjust/adjustDetailById',inputForm)
    },
    
    save: function (data) {
      return http.post('/erp/procure/adjust/purchaseAdjust/create',data)
    },
    getReview: function (inputForm) {
      return http.get('/erp/procure/adjust/erpPurchaseAdjustApproval/list',inputForm)
    },
    getOut: function (inputForm) {
      return http.get('/erp/procure/retreat/erpPurchaseRetreatOutbound/list',inputForm)
    },
    delete:function (ids) {
      return http.delete('/erp/procure/adjust/purchaseAdjust/delete',ids)
    },
  }
  