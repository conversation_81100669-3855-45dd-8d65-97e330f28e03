<!--  传感器设置 -->
<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" :rules="rulesing" class="seache-form" label-width="auto">
                <el-form-item label="设备编号" prop="deviceNo">
                    <el-input v-model="queryParams.deviceNo" placeholder="请输入设备编号" clearable/>
                </el-form-item>
                <el-form-item label="数据时间" prop="pickerValue" style="width: 450px">
                    <el-date-picker v-model="queryParams.pickerValue" end-placeholder="结束日期" range-separator="-" start-placeholder="开始日期"
									type="datetimerange" value-format="YYYY-MM-DD HH:mm:ss" clearable></el-date-picker>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="deviceQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card v-show="showPicture" :body-style="{ padding: '10px' }" shadow="never">
            <div class="leftCenter">
                <div class="title">
                    <span class="txt">温度折线图</span>
                </div>
                <div ref="chartRef" class="line"></div>
            </div>
        </el-card>
        <el-card class="box-card" shadow="never">
            <el-row v-show="showPicture" :gutter="10" class="mb8">
                <!-- <el-col :span="1.5">
                    <el-button v-hasPermi="['device:iceRaft:add']" :icon="Download" size="mini" type="warning" @click="exportList">导出 </el-button>
                </el-col> -->
                <el-col :span="1.5">
                    <el-button v-hasPermi="['device:iceRaft:add']" icon="el-icon-plus" size="mini" type="primary" @click="printRecord">打印 </el-button>
                </el-col>
            </el-row>
            <div style="display: flex; justify-content: flex-end; align-items: center">
                <RightToptipBarV2 className="purchasingManagement_purchasingOrder" @handleRefresh="getList" />
            </div>
            <el-table v-loading="loading" :data="terminalData" border style="margin-top: 15px">
                <el-table-column :index="idxMethod" align="center" label="序号" type="index" width="50" />
                <el-table-column align="center" label="设备编号" prop="serialNumber">
                    <template #default="scope">
                        <span>{{ queryParams.deviceNo }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="当前温度" prop="temperature" />
                <el-table-column align="center" label="记录时间" prop="dateTime" />
            </el-table>
            <div style="float: right; margin: 15px 0">
                <pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="queryParams.total" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>
<script setup>
import { ref, reactive, getCurrentInstance, onMounted, onUnmounted } from 'vue';
import qs from 'qs';
import * as echarts from 'echarts';
import sensorApi from '@/api/management/sensor';
import { Download } from '@element-plus/icons-vue';
import orderManagement from '@/api/logisticsManagement/orderManagement';
import SearchButton from '@/components/searchModule/SearchButton.vue';

const { proxy } = getCurrentInstance();

// 显示搜索条件
const showSearch = ref(true);
// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    pickerValue: []
});

const chartRef = ref(null);
let chartInstance = null;

// 查询条件验证
const rulesing = reactive({
    deviceNo: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
    pickerValue: [{ required: true, message: '请选择数据时间', trigger: 'change' }]
});
// 查询列表
const terminalData = ref([]);
const showPicture = ref(false);
const dateList = ref([]);
const timeList = ref([]);
// 打印
function printRecord() {
    let params = {};
    params = {
        templateId: '8e03f081411647a4a7d05d2536aee6ad',
        deviceNo: queryParams.value.deviceNo,
        queryStartTime: queryParams.value.pickerValue[0],
        queryEndTime: queryParams.value.pickerValue[1],
        logPrint: false,
		transOrderNo:' ',
		orderNo:' ',
		transportUnit:' ',
		sendingCompany:' ',
		receivingCompany:' ',
    };
    new Promise((resolve, reject) => {
        orderManagement.printPdfTmpList(params).then((res) => {
            const binaryData = [];
            binaryData.push(res);
            //获取blob链接
            let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
            window.open(pdfUrl);
        });
        setTimeout(() => {
            resolve();
        }, 20000);
    })
        .then((data) => {
            this.msgSuccess('打印成功');
            this.loading = false;
        })
        .catch((err) => {
            this.msgError('打印失败');
            this.loading = false;
        });
}

// 导出
function exportList() {
    let list = {
        filename: '设备温度记录',
        deviceNo: queryParams.value.deviceNo,
        queryStartTime: queryParams.value.pickerValue[0],
        queryEndTime: queryParams.value.pickerValue[1]
    };
    sensorApi
        .tmpExport(qs.stringify(list, { arrayFormat: 'repeat' }), '', '', 'blob')
        .then((res) => {
            var debug = res;
            if (debug) {
                var elink = document.createElement('a');
                elink.download = '传感器列表记录.xlsx';
                elink.style.display = 'none';
                var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                elink.href = URL.createObjectURL(blob);
                document.body.appendChild(elink);
                elink.click();
                document.body.removeChild(elink);
            } else {
                this.$message.error('导出异常请联系管理员');
            }
        })
        .catch((err) => {
            proxy.msgError(err.msg);
        });
}

// 温度打印记录查询
function deviceQuery() {
    proxy.$refs['queryForm'].validate((valid) => {
        if (valid) {
            initial();
			dateList.value = [];
			timeList.value = [];
            sensorApi
                .getTmpList({
					deviceNo: queryParams.value.deviceNo,
                    queryStartTime: queryParams.value.pickerValue[0],
                    queryEndTime: queryParams.value.pickerValue[1],
                })
                .then((res) => {
                    if (res.code == 200) {
                        if (res.data.record.length > 0) {
                            showPicture.value = true;
                            terminalData.value = res.data.record;
                            res.data.record.forEach((element) => {
                                dateList.value.push(element.dateTime);
                                timeList.value.push(element.temperature);
                            });
                            displayDrawing(dateList.value, timeList.value);
                            showPicture.value = true;
                        } else {
                            proxy.msgError('此数据暂无温度数据');
                        }
                    }
                })
                .catch((err) => {
                    proxy.msgError(err.msg);
                });
        }
    });
}

function initial() {
    chartInstance = echarts.init(chartRef.value, null, {
        width: 1550,
        height: 300
    });
}

function displayDrawing(e, val) {
    // 配置项
    let options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        toolbox: {
            show: true,
            feature: {
                saveAsImage: {}
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: e
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value} °C'
            }
        },
        series: [
            {
                name: '温度',
                type: 'line',
                smooth: true,
                // data: [1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6, 1, 2, 3, 4, 5, 6, 1, 2, 3, 4],
                data: val
            }
        ]
    };
    // 设置图表配置项
    chartInstance.setOption(options);
}

// 表单重置
function resetQuery() {
    showPicture.value = false;
    queryParams.value = {
        pageNum: 1,
        pageSize: 10,
        pickerValue: []
    };
	terminalData.value = [];
    // getList();
}
</script>

<style lang="scss" scoped>
::v-deep #zr_0 {
    height: 300px;
}

::v-deep .Botm {
    margin: 0 0 10px 0;

    .el-card__body {
        padding-bottom: 0px;
    }
}

.leftCenterCard {
    padding: 15px 0px 0 0px;
}

.leftCenter {
    width: 100%;
    height: 30vh;

    .line {
        height: 90%;
        width: 100%;
    }

    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;

        .txt {
            font-size: 14px;
            font-weight: bold;
            color: #000000;
        }

        ::v-deep .el-input__wrapper {
            background-color: #f1f1f1 !important;
        }
    }
}

::v-deep .margin20 {
    margin-top: 10px;

    .el-card__body {
        padding: 0;
    }
}
</style>
