<template>
    <div :style="{ zIndex: zIndex }" class="preview-dialog">
        <div class="mask"></div>
        <span class="btn close" @click="close()">
            <el-icon><Close /></el-icon>
        </span>
        <div ref="fileMain" class="preview-main">
            <div
                :style="{
                    transform: `scale(${dialogStyle.scale}) rotate(${dialogStyle.rotate}deg)`,
                    transformOrigin: 'center center'
                }"
                class="preview-content"
            >
                <vue-office-pdf v-if="checkIsPDFFile" ref="pdfViewer" :src="fileUrl" @load="rendered" />
                <img v-else :src="fileUrl" alt="" @load="rendered" />
            </div>
            <span v-if="urlList.length > 1 && index !== 0" class="btn prev" @click="pageTurning('prev')">
                <el-icon><ArrowLeftBold /></el-icon>
            </span>
            <span v-if="urlList.length > 1 && index !== urlList.length - 1" class="btn next" @click="pageTurning('next')">
                <el-icon><ArrowRightBold /></el-icon>
            </span>
        </div>
        <div class="footer-btn">
            <el-button-group>
                <el-tooltip content="放大" placement="top">
                    <el-button icon="el-icon-zoom-in" size="mini" @click="handleZoom" />
                </el-tooltip>
                <el-tooltip content="缩小" placement="top">
                    <el-button icon="el-icon-zoom-out" size="mini" @click="handleDown" />
                </el-tooltip>
                <el-tooltip :content="isFullScreen ? '全屏' : '正常'" placement="top">
                    <el-button :icon="isFullScreen ? 'el-icon-full-screen' : 'el-icon-rank'" size="mini" @click="handleFullScreen" />
                </el-tooltip>
                <el-tooltip content="向左旋转" placement="top">
                    <el-button icon="el-icon-refresh-left" size="mini" @click="handleRotateLeft" />
                </el-tooltip>
                <el-tooltip content="向右旋转" placement="top">
                    <el-button icon="el-icon-refresh-right" size="mini" @click="handleRotateRight" />
                </el-tooltip>
            </el-button-group>
        </div>
    </div>
</template>
<script>
import { ArrowLeftBold, ArrowRightBold, Close } from '@element-plus/icons-vue';
import VueOfficePdf from '@vue-office/pdf';
export default {
    name: 'ThePreviewDialog',
    components: {
        VueOfficePdf,
        ArrowLeftBold,
        ArrowRightBold,
        Close
    },
    props: {
        urlList: {
            type: Array,
            default: () => []
        },
        current: {
            type: Number,
            default: 0
        },
        zIndex: {
            type: Number,
            default: 9999
        },
        fileType:{
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dialogStyle: {
                scale: 1,
                rotate: 0
            },
            fileUrl: '',
            isFullScreen: true,
            index: this.current
        };
    },
    mounted() {
        // 在组件挂载时监听滚动事件
        window.addEventListener('scroll', this.handleScroll);
    },
    destroy() {
        // 在组件销毁前移除全局事件监听器
        window.removeEventListener('scroll', this.handleScroll);
    },
    computed: {
        /**
         * 是否为pdf文件
         * @returns {boolean}
         */
        checkIsPDFFile() {
            return this.fileType && this.fileType === 'pdf'?true:this.fileUrl.toLowerCase().endsWith('.pdf');
        }
    },
    created() {
        this.fileUrl = this.urlList[this.index];
    },
    methods: {
        close() {
            this.$emit('close');
        },
        /**
         * 滚动
         */
        handleScroll() {

        },
        /**
         * 放大
         */
        handleZoom() {
            // 限制最大放大倍数
            if (this.dialogStyle.scale < 2) {
                this.dialogStyle.scale = +(this.dialogStyle.scale + 0.1).toFixed(1);
            }
        },
        /**
         * 缩小
         */
        handleDown() {
            // 限制最小缩小倍数
            if (this.dialogStyle.scale > 0.5) {
                this.dialogStyle.scale = +(this.dialogStyle.scale - 0.1).toFixed(1);
            }
        },
        /**
         * 全屏/正常
         */
        handleFullScreen() {
            if (this.isFullScreen) {
                // 切换到全屏
                this.enterFullScreen();
            } else {
                // 恢复到正常
                this.exitFullScreen();
            }
            this.isFullScreen = !this.isFullScreen;
        },
        /**
         * 设置全屏
         */
        enterFullScreen() {
            let element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                /* Firefox */
                element.mozRequestFullScreen();
            } else if (element.webkitRequestFullscreen) {
                /* Chrome, Safari & Opera */
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                /* IE/Edge */
                element.msRequestFullscreen();
            }
        },
        /**
         * 退出全屏
         */
        exitFullScreen() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                /* Firefox */
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                /* Chrome, Safari and Opera */
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                /* IE/Edge */
                document.msExitFullscreen();
            }
        },
        /**
         * 向左旋转
         */
        handleRotateLeft() {
            // 向左旋转就是减去90度
            this.dialogStyle.rotate = (this.dialogStyle.rotate - 90) % 360;
            // 处理负数情况
            if (this.dialogStyle.rotate < 0) {
                this.dialogStyle.rotate += 360;
            }
        },
        /**
         * 向右旋转
         */
        handleRotateRight() {
            // 向右旋转就是加上90度
            this.dialogStyle.rotate = (this.dialogStyle.rotate + 90) % 360;
        },
        /**
         * 翻页
         * @param type
         */
        pageTurning(type) {
            if (type == 'prev') {
                if (this.index == 0) {
                    this.index = this.urlList.length - 1;
                } else {
                    this.index--;
                }
            }
            if (type == 'next') {
                if (this.index == this.urlList.length - 1) {
                    this.index = 0;
                } else {
                    this.index++;
                }
            }
            this.fileUrl = this.urlList[this.index];
            // 加载动画
            this.loading = true;
        },
        /**
         * 加载完成
         */
        rendered() {
            this.loading = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.preview-dialog {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    .mask {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        opacity: 0.5;
        background: #000;
    }
    .footer-btn {
        position: absolute;
        z-index: 200;
        bottom: 40px;
        left: 40%;
        .el-button-group {
            border-radius: 50%;
            .el-button {
                background-color: #606266;
                color: #ffffff;
                border: none;
                font-size: 32px;
                padding: 20px 10px;
            }
        }
    }
    .btn {
        width: 40px;
        height: 40px;
        line-height: 45px;
        font-size: 32px;
        color: #fff;
        background-color: #606266;
        text-align: center;
        border-radius: 50%;
        z-index: 20;
        transform: translateY(-50%);
    }
    .close {
        position: absolute;
        top: 40px;
        right: 40px;
    }
    .preview-main {
        position: relative;
        width: 100%;
        height: 100%;
        .preview-content {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
                width: auto;
                height: auto;
                max-height: 100%;
                max-width: 100%;
            }
            .vue-office-pdf {
                width: 100%;
                height: 100%;
                ::v-deep{
                    .vue-office-pdf-wrapper{
                        background: transparent!important;
                    }
                }
            }
        }
        .prev {
            position: absolute;
            left: 40px;
            top: 50%;
        }
        .next {
            position: absolute;
            top: 50%;
            right: 40px;
        }
    }
}
</style>
