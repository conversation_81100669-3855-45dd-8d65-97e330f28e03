<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-05 14:03:01
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-03 17:50:09
 * @FilePath: \zhixing-heyue-erp-front-end-pc\README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 知行合越ERP前端PC



#### 安装教程

1.  启动  npm run serve
2.  打包  npm run build

### 公用方法
  #### isDeleteAuthority  鉴权是否为制单用户或者superadmin用户并且审核状态为 待审核、已驳回、草稿 状态  用来校验是否有删除权限    路径：utils/isDeleteAuthority.js  用法见view/assist/cooperationQualification/custom/index

  #### dict  用来获取字典值的公用方法(全局方法)    路径：utils/dict.js    用法见view/assist/cooperationQualification/custom/index

  #### dictLabel  用来翻译字典值的公用方法    路径：utils/dictLabel.js    用法见view/assist/cooperationQualification/custom/index

  #### v-debounce  自定义指令  防抖自定义指令   路径：utils/index.js  

  #### emitter 用来兄弟组件进行传值   路径：utils/bus.js 

  #### download 全局下载导出公用方法   路径：utils/index.js   用法见view/assist/cooperationQualification/custom/index

  #### resetForm  全局重置表单公用方法  路径：utils/index.js   用法见view/assist/cooperationQualification/custom/index

  #### deepClone  递归深拷贝  路径：utils/index.js 
      


### 公用组件
 #### scrollNumber 数字滚动组件  路径：utils/scrollNumber.js  

### 全局组件
  #### Pagination  全局分页组件    路径：components/Pagination.js 用法见view/assist/cooperationQualification/custom/index

  #### dragTableColumn  拖拽表格组件    路径：components/dragTableColumn.js 用法见view/assist/cooperationQualification/custom/index

  #### rightToptipBar  刷新表格、全屏、显隐列(不走接口)    路径：components/rightToptipBar.js 

  #### rightToptipBarV2  刷新表格、全屏、显隐列(走接口)    路径：components/rightToptipBarV2.js  用法见view/assist/cooperationQualification/custom/index

  #### topTitle 表头搜索条件  展开收起  路径：components/topTitle.js  用法见view/assist/cooperationQualification/custom/index

### 依赖
  #### moment 时间格式化插件   用法(见百度)

  #### sortablejs  拖拽插件   用法(见百度)

  #### mitt  兄弟组件传值   用法(见百度)

  #### lodash  js工具方法合集库  用法(见百度)

  #### echarts  数据可视化图表插件 用法(见百度)

  #### postcss-pxtorem  amfe-flexible  自适应分辨率插件  用法(见百度)  
  
  #### vue3-seamless-scroll 列表无缝滚动插件   用法(见百度)  

  #### pureadmin/utils 常用方法公共库   用法(见百度)  