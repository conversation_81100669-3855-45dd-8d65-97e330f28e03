<template>
    <div v-loading="loading" class="app-container">
        <el-form ref="sendPartyFormRef" :model="sendPartyForm" :rules="sendPartyFormRules" label-position="top" label-width="auto" size="small" @submit.prevent>
            <el-card class="mb10" shadow="never">
                <div class="box" style="display: flex">
                    <el-form-item class="mb0" label="承运商" prop="companyId">
                        <el-select v-model="sendPartyForm.companyId" clearable filterable placeholder="请选择承运商" style="width: 500px" @change="selectTheShipper">
                            <el-option v-for="item in ownerList" :key="item.companyId" :label="item.carrierName" :value="item.carrierId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="mb0" label="网点" prop="sendBranch" style="margin-left: 20px">
                        <el-select v-model="sendPartyForm.sendBranch" clearable filterable placeholder="请选择网点" style="width: 500px">
                            <el-option v-for="item in branchData" :key="item.id" :label="item.branchName" :value="item.branchCode"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </el-card>
            <el-card :disabled="belongUserId == undefined || belongUserId == ''" class="mb10" shadow="never">
                <template #header>
                    <div class="titleLayout flex-r">
                        <div class="d-flex">
                            <span class="verticalBar"></span>
                            <span class="title">发货方</span>
                        </div>
                        <el-popover v-model:visible="sendPartyForm.isShow" :width="800" placement="top" style="justify-self: end" trigger="click" @show="isExistCompanyIds">
                            <div>
                                <el-form>
                                    <el-input v-model="sendAddressParams.searchValue" clearable placeholder="输入关键字搜索" size="mini" />
                                </el-form>
                                <el-table :data="sendAddressBook" height="300" size="mini" style="width: 780px" @row-click="getSendAddress">
                                    <el-table-column align="center" label="姓名" prop="user" width="110px" />
                                    <el-table-column align="center" label="公司" prop="company" width="200px" />
                                    <el-table-column align="center" label="电话" prop="phone" width="110px" />
                                    <el-table-column align="center" label="地址" prop="address" width="350px" />
                                </el-table>
                                <pagination v-show="sendAddressParams.total > 0" v-model:limit="sendAddressParams.size" v-model:page="sendAddressParams.current" :total="sendAddressParams.total" style="margin-bottom: 0" @pagination="AddressBook" />
                            </div>
                            <template #reference>
                                <el-button round size="medium" type="primary">地址簿</el-button>
                            </template>
                        </el-popover>
                    </div>
                </template>

                <div class="box-shipper">
                    <el-form-item label="姓名" prop="sendUser">
                        <el-input v-model="sendPartyForm.sendUser" clearable placeholder="请输入发件人姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="电话" prop="sendUserPhone">
                        <el-input v-model="sendPartyForm.sendUserPhone" clearable placeholder="请输入联系电话"></el-input>
                    </el-form-item>
                    <el-form-item label="公司" prop="sendCompany">
                        <el-input v-model="sendPartyForm.sendCompany" clearable placeholder="请输入公司"></el-input>
                    </el-form-item>
                    <el-form-item label="发件地址" prop="sendPCC">
                        <el-cascader v-model="sendPartyForm.sendPCC" :options="sysAreas" @change="formChangeHandle($event, 'sendPCC')" clearable filterable placeholder="请选择省市区" @visible-change="visibleChange" style="width: 100%" />

                        <!-- <el-cascader v-model="sendPartyForm.sendPCC" :options="sysAreas" clearable filterable
                            placeholder="请选择发件地址" style="width: 100%" /> -->
                    </el-form-item>
                    <el-form-item label="详细地址" prop="sendAddress">
                        <el-input v-model="sendPartyForm.sendAddress" clearable maxlength="200" placeholder="请输入详细地址" show-word-limit></el-input>
                    </el-form-item>
                </div>
            </el-card>

            <el-card class="mb10" shadow="never">
                <template #header>
                    <div class="titleLayout">
                        <span class="verticalBar"></span>
                        <span class="title">EXCEL批量处理</span>
                    </div>
                </template>
                <div class="box-batchProcessing-top">
                    <div>
                        <div style="display: flex" v-if="responseData.analysis">
                            <div>
                                <span>成功：</span>
                                <span v-if="responseData.analysis.successData" style="color: #5670fe">{{ responseData.analysis.successData.length }}</span>
                                <span v-else style="color: #5670fe">0</span>
                            </div>
                            <div>
                                <span>失败：</span>
                                <span v-if="responseData.analysis.errorData" style="color: #ff2a2a">{{ responseData.analysis.errorData.length }}</span>
                                <span v-else style="color: #ff2a2a">0</span>
                            </div>
                        </div>
                        <div style="display: flex" v-else>
                            <div>
                                <span>成功：</span>
                                <span style="color: #5670fe">0</span>
                            </div>
                            <div style="margin-left: 20px">
                                <span>失败：</span>
                                <span style="color: #ff2a2a">0</span>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; gap: 12px">
                        <el-button icon="el-icon-download" size="small" type="primary" @click="downloadTemplate">下载</el-button>
                        <el-upload
                            v-if="sendPartyForm.companyId && sendPartyForm.sendBranch"
                            ref="uploadOrderDetailExcel"
                            :action="uploadFileUrl"
                            :before-upload="beforeUploadExcel"
                            :data="{ carrierId: sendPartyForm.companyId, type: '1' }"
                            :headers="headers"
                            :limit="1"
                            :on-progress="progress"
                            :on-success="fileUploadSuccess"
                            :show-file-list="false"
                            :on-error="error"
                            accept=".xls,.xlsx"
                        >
                            <el-button icon="el-icon-upload" size="small" type="warning">上传</el-button>
                        </el-upload>
                        <el-button v-if="responseData.analysis" icon="el-icon-delete" size="small" type="warning" @click="clearFileList">清空列表 </el-button>
                    </div>
                </div>
                <div v-if="responseData.analysis">
                    <div v-if="responseData.analysis.failureMsg" v-html="responseData.analysis.failureMsg"></div>
                </div>
                <el-table v-loading="checkDataLoading" :data="bulkOrderList.orderList" border style="margin: 16px 0">
                    <el-table-column align="center" label="行号" type="index" width="50" />
                    <el-table-column align="center" label="收货方姓名" prop="receiverUser" />
                    <el-table-column align="center" label="件数" prop="goodsPackages" />
                    <el-table-column align="center" label="货物揽收方式" prop="orderType" width="140" :formatter="(row) => formDict(collectionMethod, row.orderType)" />
                    <el-table-column align="center" label="产品类型" prop="productType" :formatter="(row) => formDict(fourplProductTypeList, row.productType)" />
                    <el-table-column align="center" label="随货同行单号" prop="externalOrderNo" />
                    <el-table-column align="center" label="付款方式" prop="paymentMethod" :formatter="(row) => formDict(settlementMethodList, row.paymentMethod)" />
                    <el-table-column align="center" label="冷链明细">
                        <template #default="scope">
                            <el-popover effect="light" trigger="hover" placement="top" width="auto">
                                <template #default>
                                    <el-table :data="scope.row.orderDetailList" height="200" size="mini" style="width: 600px">
										<el-table-column :show-overflow-tooltip="true" align="center" label="通用名称" prop="name" />
										<el-table-column :show-overflow-tooltip="true" align="center" label="规格/型号" prop="specifications" />
										<el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" prop="manufacturer" />
										<el-table-column :show-overflow-tooltip="true" align="center" label="单位" prop="basicUnit" />
										<el-table-column align="center" label="数量" prop="quantity" />
										<el-table-column :show-overflow-tooltip="true" align="center"  min-width="100" label="批号/序列号" prop="batchNumber" />
										<el-table-column :show-overflow-tooltip="true" align="center"  min-width="160" label="使用期限/失效日期" prop="validityDate" />
										<el-table-column :show-overflow-tooltip="true" align="center"  min-width="200" label="批准文号/注册证号/备案证号" prop="registrationNumber" />
										<el-table-column :show-overflow-tooltip="true" align="center"  min-width="200" label="上市许可持有人/注册人/备案人" prop="listPermitHolder" />
										<el-table-column align="center" label="资料是否齐全" prop="completeInformation" >
											<template #default="scope">{{scope.row.completeInformation=='1'?'是':'否'}}</template>
										</el-table-column>
                                    </el-table>
                                </template>
                                <template #reference>
                                    <el-button :disabled="scope.row.orderDetailList.length > 0" size="mini" type="text">查看</el-button>
                                </template>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table>
                <span v-if="responseData.analysis" style="color: #ff2a2a; font-size: 12px; font-weight: bold">注：本次批量下单将生成 {{ responseData.list.length }} 单，请耐心等待</span>
                <span v-if="!responseData.analysis" style="color: #ff2a2a; font-size: 12px; font-weight: bold">注：本次批量下单将生成 0 单，请耐心等待</span>
            </el-card>

            <!-- /阅读条款  -->
            <div class="box-footer">
                <el-form-item prop="agreeToTermsCarrier">
                    <el-checkbox v-model="sendPartyForm.agreeToTermsCarrier" style="color: #bbbbbb">
                        阅读并同意
                        <el-button style="color: #fcb824; font-size: 14px" type="text" @click="agreement">《违禁品协议》</el-button>
                    </el-checkbox>
                </el-form-item>
                <el-form-item>
                    <el-button round size="medium" type="primary" @click="placeOrder">立即下单</el-button>
                    <!-- <el-button round size="medium" type="primary" @click="checkOrder">查看订单</el-button> -->
                </el-form-item>
            </div>
        </el-form>

        <!-- 下单进度 -->
        <!-- <el-dialog :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true" :title="title"
            :visible.sync="placeOrderProgressDialog" append-to-body center width="30%">
            <el-card shadow="never">
                <div v-if="loading" v-loading="loading" element-loading-text="批量下单中" style="height: 120px"></div>
                <div v-if="!loading" class="box-orderProgress">
                    <div>
                        <span>成功：</span>
                        <span style="color: #5670fe">{{ numberOfSuccesses || 0 }}</span>
                    </div>
                </div>
                <el-divider v-if="!loading"></el-divider>
                <div v-if="!loading" class="box-period">
                    <el-scrollbar>
                        <div v-if="successList" class="box-list">
                            <div v-for="item in successList" :key="item">{{ item }}</div>
                        </div>
                    </el-scrollbar>
                </div>
            </el-card>
            <div style="display: flex; justify-content: center; margin-top: 20px">
                <el-button @click="placeOrderProgressDialog = false">取 消</el-button>
                <el-button type="primary" @click="checkOrder">查看订单</el-button>
            </div>
        </el-dialog> -->

        <!--s 违禁品协议组件  -->
        <el-dialog v-model="agreementView" center append-to-body title="违禁品协议" width="40%" top="0">
            <el-image :src="require('@/assets/images/Security-protocols.jpg')" style="width: 100%; height: 100%" />
            <div style="display: flex; justify-content: center">
                <el-button size="small" @click="agreementView = false">关闭</el-button>
                <el-button size="small" type="primary" @click="agreeToTheAgreement()">同意</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import tool from '@/utils/tool';
import batchOrderPlacementApi from '@/api/logisticsManagement/BatchOrderPlacement';
import { selectDictLabel } from '@/utils/dictLabel';

export default {
    name: 'BatchOrderingAtTheCarrierEnd',
    data() {
        return {
            sendPartyForm: {
                isShow: false,
                sendUser: '',
                sendUserPhone: '',
                sendAddress: '',
                sendCompany: '',
                sendPCC: [],
                agreeToTermsCarrier: [],
                companyId: '', // 货主
                sendBranch: '', //网点
                fourplProductTypeList: [], //产品类型
                collectionMethod: [], //货物揽收方式
                settlementMethodList: [] //付款方式
            },
            sendPartyFormRules: {
                sendUser: [{ required: true, message: '请输入收货人姓名', trigger: 'change' }],
                sendUserPhone: [
                    { required: true, message: '请输入收货人电话', trigger: 'change' },
                    { type: 'string', pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/, message: '请输入正确的电话', trigger: 'change' }
                ],
                sendAddress: [
                    { required: true, message: '请输入收货人地址', trigger: 'change' },
                    { max: 200, message: '最多输入200个字符', trigger: 'change' }
                ],
                sendCompany: [{ required: true, message: '请输入收货人公司', trigger: 'change' }],
                sendPCC: [{ required: true, message: '请选择收货人省市区', trigger: 'change' }],
                companyId: [{ required: true, message: '请选择承运商', trigger: 'change' }],
                sendBranch: [{ required: true, message: '请选择网点', trigger: 'change' }]
            },
            // 取件地址
            sendAddressParams: {
                total: 0,
                type: 1,
                pageNum: 1,
                pageSize: 10,
                searchValue: '',
                companyIds: null,
                companyId: ''
            },
            sendAddressBook: [],
            orderFile: {
                name: '暂无文件，请上传！',
                url: ''
            },
            uploadFileUrl: process.env.VUE_APP_API_GAENT + '/tms/orderDrug/importOrder', // 上传的图片服务器地址
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                clientType: 'pc'
            },
            checkButtonDisabled: false,
            belongUserId: '',
            userSelectLoading: false,
            tmsCustomerList: [],
            isAdmin: false,
            responseData: {},
            checkDataLoading: false,
            sysAreas: [],
            placeOrderProgressDialog: false,
            title: '批量下单',
            loading: false,
            listOfFailures: [],
            successList: [],
            numberOfFailures: 0,
            numberOfSuccesses: 0,
            ownerList: [], // 货主列表
            productTypeDicts: [], //产品类型4PL
            fourplPaymentMethodOptions: [], // 付款方式
            collectionMethod: [], // 货物揽收方式
            open: false,
            agreementView: false,
            branchData: {}, //网点列表
            bulkOrderList: []
        };
    },
    watch: {
        'sendAddressParams.searchValue'() {
            this.getSendAddressBooks(true);
        }
    },
    created() {
        this.visibleChange();
        // 获取承运商
        this.getCompanySelect();
        this.getDict();
    },
    methods: {
        // 网点
        branchList(val) {
            batchOrderPlacementApi
                .branchList({
                    carrierId: val,
                    size: -1,
                    current: 0
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.branchData = res.data;
                        this.sendPartyForm.sendBranch = this.branchData[0].branchCode;
                    }
                });
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        },
        // 获取承运商下拉
        getCompanySelect() {
            batchOrderPlacementApi.carrierSelect().then((res) => {
                if (res.code == 200) {
                    this.ownerList = res.data;
                    this.sendPartyForm.companyId = this.ownerList[0].carrierId;
                    this.branchList(this.sendPartyForm.companyId); //获取网点
                    this.AddressBook(this.sendPartyForm.companyId); //获取地址薄
                    // if (this.ownerList.length == 1) {
                    //     this.sendPartyForm.companyId = this.ownerList[0].carrierId;
                    //     this.branchList(this.sendPartyForm.companyId);//获取网点
                    //     this.AddressBook(this.sendPartyForm.companyId);//获取地址薄
                    // } else {
                    //     this.ownerList = res.data;
                    // }
                }
            });
        },
        // 查询地址薄
        isExistCompanyIds() {
            if (!this.sendPartyForm.companyId) {
                this.$message.error('请选择承运商');
                this.sendPartyForm.isShow = false;
            }
        },
        AddressBook(val) {
            this.addressLoading = true;
            this.sendAddressParams.queryType = 0;
            this.sendAddressParams.companyId = val;
            if (this.isAdmin) {
                this.sendAddressParams.createUserId = this.belongUserId;
                this.tmsCustomerList.forEach((t) => {
                    if (t.userId == this.belongUserId) {
                        this.belongUserName = t.userName;
                    }
                });
                this.sendPartyForm = {
                    sendUser: '',
                    sendUserPhone: '',
                    sendAddress: '',
                    sendCompany: '',
                    sendPCC: []
                };
            }
            batchOrderPlacementApi.addressBookList(this.sendAddressParams).then((res) => {
                if (res.code == 200) {
                    this.sendAddressBook = res.data.records;
                    var arr2 = this.sendAddressBook.filter((item) => {
                        return item.isDefaults == true;
                    });
                    if (arr2.length > 0) {
                        this.getSendAddress(arr2[0]);
                    }
                    this.sendAddressParams.total = res.data.total;
                    this.addressLoading = false;
                }
            });
        },
        // 订单-批量下单模板下载
        downloadTemplate() {
            let list = {
                carrierId: this.sendPartyForm.companyId
            };
            batchOrderPlacementApi
                .downloadTemplate(list, '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '批量下单导入模版.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                    } else {
                        this.$message.error('导出异常请联系管理员');
                    }
                })
                .catch((err) => {
                    this.$message.error(err.msg);
                });
        },

        // 跳转订单列表
        checkOrder() {
            this.placeOrderProgressDialog = false;
            // 跳转到订单列表
            this.$router.push({
                name: 'OrderList'
            });
        },

        // 上传文件
        beforeUploadExcel(file) {
            if (this.sendPartyForm.companyId == '') {
                this.msgError('请选择货主');
                return false;
            }
            // 限制文件类型 xls、xlsx
            const fileType = file.type;
            const fileTypeArr = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
            const isFileType = fileTypeArr.includes(fileType);
            if (!isFileType) {
                this.msgError('请上传xls、xlsx格式的文件');
                return false;
            }
            // return true;
        },
        error(err) {
            this.loading = false;
            this.percentfalse;
            this.$notify.error({
                title: '上传文件未成功',
                message: err
            });
        },
        fileUploadSuccess(res) {
            this.checkDataLoading = false;
            this.responseData = res.data;
            this.bulkOrderList = this.responseData.analysis;
        },
        progress() {
            this.checkDataLoading = true;
        },

        // 同意协议
        agreeToTheAgreement() {
            this.sendPartyForm.agreeToTermsCarrier.push(false);
            this.agreementView = false;
        },
        // 打开协议
        agreement() {
            this.agreementView = true;
        },

        placeOrder() {
            this.$refs['sendPartyFormRef'].validate((valid) => {
                if (valid) {
                    // 协议是否同意
                    if (this.sendPartyForm.agreeToTermsCarrier.length == 0) {
                        this.msgError('请先阅读并同意服务协议和隐私政策');
                        return;
                    }

                    if (!this.responseData.analysis) {
                        this.msgError('无可下单数据');
                        return;
                    }
                    this.placeOrderProgressDialog = true;
                    this.title = '批量下单';
                    this.loading = true;
                    let dataArr = [];
                    this.responseData.analysis.orderList.forEach((data) => {
                        data.belongUserName = this.belongUserName;
                        data.sendUser = this.sendPartyForm.sendUser;
                        data.sendUserPhone = this.sendPartyForm.sendUserPhone;
                        data.sendCompany = this.sendPartyForm.sendCompany;
                        data.sendBranch = this.sendPartyForm.sendBranch;
                        data.sendAddress = this.sendPartyForm.sendAddress;
                        data.sendTown = {
                            id: this.sendPartyForm.sendPCC[3]
                        };
                        data.sendProvinceId = this.sendPartyForm.sendPCC[0];
                        data.sendCityId = this.sendPartyForm.sendPCC[1];
                        data.sendCountyId = this.sendPartyForm.sendPCC[2];
                        data.belongUserId = null; // 订单所属人ID（租户/货主ID）
                        data.createUserId = null; // 下单人ID
                        dataArr.push(data);
                    });
                    batchOrderPlacementApi
                        .batchImportOrder({
                            orderList: dataArr,
                            type: 2
                        })
                        .then((res) => {
                            if (res.code == 200) {
                                this.loading = false;
                                this.msgSuccess('批量下单成功');
                                this.clearFileList();
                                // // 清空文件上传列表
                                this.$refs.uploadOrderDetailExcel.clearFiles();
                                // // 清空表单
                                this.$refs.sendPartyFormRef.resetFields();
                                this.responseData = {
                                    errorData: [],
                                    successData: [],
                                    rows: 0
                                };
                                // this.$router.push({ name: 'order' });
                            } else {
                                this.placeOrderProgressDialog = false;
                                this.loading = false;
                            }
                        })
                        .catch(() => {
                            this.placeOrderProgressDialog = false;
                            this.loading = false;
                        });
                }
            });
        },
        // 清空文件上传列表
        clearFileList() {
            this.responseData = { errorData: [], successData: [], rows: 0 };
            this.bulkOrderList = [];
            this.$refs.uploadOrderDetailExcel.clearFiles();
        },

        // 填写地址簿信息 取件
        getSendAddress(row) {
            const { user, phone, address, company, companyId, provinceId, cityId, countyId, town } = row;
            this.sendPartyForm.sendUser = user;
            this.sendPartyForm.sendUserPhone = phone;
            this.sendPartyForm.sendAddress = address;
            this.sendPartyForm.sendCompany = company;
            // this.sendPartyForm.companyId = companyId;
            // this.sendPartyForm.sendPCC = [provinceId, cityId, countyId, townId];
            this.sendPartyForm.sendPCC = [];
            this.sendPartyForm.sendPCC.push('' + provinceId + '');
            this.sendPartyForm.sendPCC.push('' + cityId + '');
            this.sendPartyForm.sendPCC.push('' + countyId + '');
            this.sendPartyForm.sendPCC.push('' + town.id + '');
            this.sendPartyForm.town = town;
            this.visibleChange();
            // // 提示地址填写完成
            this.msgSuccess('地址填写完成');
            this.$refs.sendPartyFormRef.clearValidate('sendPCC');
            this.sendPartyForm.isShow = false;
        },

        selectTheShipper(e) {
            this.branchList(e);
            this.AddressBook(e);
            this.sendPartyForm.companyId = e;
            this.getSendAddressBooks();
            this.sendPartyForm.sendUser = '';
            this.sendPartyForm.sendUserPhone = '';
            this.sendPartyForm.sendAddress = '';
            this.sendPartyForm.sendCompany = '';
            this.sendPartyForm.sendPCC = [];
        },
        //字典回显
        formDict(data, val) {
            return selectDictLabel(data, val);
        },
        /**
         * 获取字典值
         */
        async getDict() {
            this.fourplProductTypeList = await this.getDictList('fourpl_product_type');
            this.collectionMethod = await this.getDictList('fourpl_mail_service');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    thead th {
        border-right: none !important;
    }

    .form-mb0 .el-form-item {
        margin-bottom: 4px;
        margin-top: 4px;
    }

    .el-drawer__header {
        margin-bottom: 20px;
    }

    label.el-radio {
        margin-right: 8px;
    }

    .el-button-group > .el-button:not(:last-child) {
        margin-right: 8px;
    }

    .el-tabs__header.is-top {
        margin-bottom: 0;
    }

    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .card-pb-10 .el-card__body {
        padding-bottom: 10px;
    }

    .el-dialog__header {
        background: none;

        span {
            color: #5c6b8a;
        }
    }

    .box-shipper .el-form-item .el-form-item__label {
        padding-bottom: 0;
    }

    .el-divider.el-divider--horizontal {
        margin: 10px 0;
        background-color: #e6ebf5;
    }

    .el-dialog__body {
        background-color: #f2f2f2;
    }
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.mb8 {
    margin-bottom: 8px !important;
}

.mb16 {
    margin-bottom: 16px;
}

.box-table-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.el-table .cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-table .warning-row {
    background-image: linear-gradient(to top, #e6e9f0 0%, #eef1f5 100%);
}

.p-16 {
    padding: 16px;
}

.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}

.flex-r {
    justify-content: space-between;
}

.d-flex {
    display: flex;
    align-items: center;
}

.box-shipper {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1.5fr 1.5fr;
    gap: 16px;
}

.box-batchProcessing-top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    > :nth-child(1) {
        display: flex;
        gap: 32px;
    }
}

.box-footer {
    padding-left: 8px;
    display: flex;
    justify-content: space-between;
}

.box-orderProgress {
    display: flex;
    gap: 16px;
    font-size: 16px;

    & > div {
        display: flex;
    }
}

.box-period {
    height: 140px;
    padding: 8px 0 0 8px;

    ::v-deep {
        .el-scrollbar {
            height: 100%;
        }

        .el-scrollbar__wrap {
            //overflow: scroll;
            width: 110%;
        }
    }

    .box-list {
        display: grid;
        grid-template-columns: 1fr;

        > div {
            height: 32px;
            line-height: 32px;
            padding-left: 16px;
        }

        > div:hover {
            color: #5670fe;
        }

        > div.active {
            color: #5670fe;
            font-weight: bold;
        }
    }
}
</style>
