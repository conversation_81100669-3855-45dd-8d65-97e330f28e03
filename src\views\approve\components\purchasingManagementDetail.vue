<template>
    <div class="app-container" style="padding: 20px;width: 100%;">
        <el-dialog title="采购详情" v-model="visible" width="85%" :append-to-body="false" :before-close="beforeClose"
            style="position: relative;" v-if="visible">
            <div v-loading="modalLoding">
                <div style="display: flex;justify-content: space-between;padding: 20px 20px;">
                    <span style="color:#333;font-size: 18px;">{{ data.document }}</span>
                    <span style="color:#333;font-weight: bold;font-size: 18px;" v-if="type != 'record'">{{ '审批中' }}</span>
					<span style="color:#333;font-weight: bold;font-size: 18px;"  v-if="type == 'record'">{{ '审批完成' }}</span>
                </div>
                <el-collapse v-model="activeNames" v-loading="loading">
                <el-collapse-item title="基本信息" name="1">
                    <template #title>
                        <span class="col_title">基本信息</span>
                    </template>
                    <el-descriptions :column="3" border>
                        <el-descriptions-item label="供应商" label-align="left" align="center" label-class-name="my-label"
                            class-name="my-content">{{ list.essentialInformation.supplier.enterpriseName || '--' }}</el-descriptions-item>
                        <el-descriptions-item label="供应商代表" label-align="left" align="center">{{ list.essentialInformation.supplierRepresentative.delegateName|| '--'  }}</el-descriptions-item>
                        <el-descriptions-item label="结算方式" label-align="left" align="center">{{ formDict(settlementMethod,list.essentialInformation.settlementMethod) }}</el-descriptions-item>
                        <el-descriptions-item label="付款期限" label-align="left" align="center">
                            {{ list.essentialInformation.termsPayment ? moment(list.essentialInformation.termsPayment).format('YYYY-MM-DD') : '--'}}
                        </el-descriptions-item>
                        <!-- <el-descriptions-item label="发票类型" label-align="left" align="center">
                            {{ formDict(InvoiceTypeList,list.essentialInformation.invoiceType) }}</el-descriptions-item> -->
                        <el-descriptions-item label="物流方式" label-align="left"
                            align="center">{{ formDict(logisticsMethodsList,list.essentialInformation.logisticsMode) }}</el-descriptions-item>
                        <el-descriptions-item label="库号" label-align="left"
                            align="center">{{ list.essentialInformation.warehouseNumber.warehouseNumber }}</el-descriptions-item>
                        <el-descriptions-item label="经手人" label-align="left"
                            align="center">{{ list.essentialInformation.handledBy.name }}</el-descriptions-item>
                        <el-descriptions-item label="制单人" label-align="left"
                            align="center">{{ list.essentialInformation.preparedBy.name }}</el-descriptions-item>
                        <el-descriptions-item label="自营扣率" label-align="left"
                            align="center">{{ formDict(deductionRateList,list.essentialInformation.selfRate) }}</el-descriptions-item>
                        <el-descriptions-item label="折扣金额" label-align="left"
                            align="center">{{ list.essentialInformation.discountAmount.toFixed(2) }}</el-descriptions-item>
                        <el-descriptions-item label="是否生成合同" label-align="left"
                            align="center">{{ list.essentialInformation.isGenerateContract == '1' ? '是': '否' }}</el-descriptions-item>
                        <el-descriptions-item label="备注" label-align="left"
                            align="left">{{ list.essentialInformation.remark|| '--' }}</el-descriptions-item>
                    </el-descriptions>
                </el-collapse-item>
                <el-collapse-item title="细单信息" name="2">
                    <template #title>
                        <span class="col_title">细单信息</span>
                    </template>
                    <el-table v-loading="loading" :data="list.detailedOrderInformation" border style="margin-top: 30px;"
                        size="small">
                        <el-table-column min-width="80" align="center" label="序号" fixed="left">
                            <template #default="scope">
                                {{ scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column label="商品名称" align="center" prop="tradeName" :show-overflow-tooltip="true"
                            min-width="140" fixed="left" />
                        <el-table-column label="数量" align="center" :show-overflow-tooltip="true" min-width="140"
                            fixed="right">
                            <template #default="scope">
                                <div>
                                    <p style="color: red;font-size: 16px;">
                                        {{ scope.row.quantity }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="单价" align="center" :show-overflow-tooltip="true" min-width="140"
                            fixed="right">
                            <template #default="scope">
                                <div>
                                    <p style="color: red;font-size: 16px;">
                                        {{ scope.row.unitPrice }}</p>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="细单合计金额" prop="amountMoney" :show-overflow-tooltip="true" align="center"
                            min-width="100" fixed="right">
                            <template #default="scope">
                                <span style="color: red;font-size: 16px;">{{
                                    (Number(scope.row.quantity || 0) * Number(scope.row.unitPrice || 0)).toFixed(2)
                                }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="到货数量" prop="arrivalQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="合格收货数量" prop="receivingQuantity" :show-overflow-tooltip="true"
                            align="center" min-width="140" />
                        <el-table-column label="入库数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="商品编码" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="商品自编码" prop="commoditySelfCode" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <!-- <el-table-column label="类型" prop="commodityType"
                            :formatter="(row) => formDict(productType, row.commodityType)" :show-overflow-tooltip="true"
                            align="center" min-width="140" /> -->
                        <el-table-column label="生产厂家" prop="manufacture.enterpriseName" :show-overflow-tooltip="true"
                            align="center" min-width="140" />
                        <el-table-column label="产地" prop="originPlace" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="规格" prop="packageSpecification" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="税率" prop="taxRate" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="件装量" prop="ratio" :show-overflow-tooltip="true" align="center"
                            min-width="140" />

                        <el-table-column label="件数" prop="pieceNumber" :show-overflow-tooltip="true" align="center"
                            min-width="140">
                            <template #default="scope">
                                <span>{{ String(Math.floor(((scope.row.quantity ? Number(scope.row.quantity || 0) : 0) /
                                    (scope.row.ratio ? Number(scope.row.ratio || 0) : 1)))) + (scope.row.completeUnit ||
                                        '箱')
                                    + (((scope.row.quantity ? Number(scope.row.quantity || 0) : 0) % (scope.row.ratio ?
                                        Number(scope.row.ratio || 0) : 1)) ? (((scope.row.quantity ? Number(scope.row.quantity
                                            || 0) : 0) % (scope.row.ratio ? Number(scope.row.ratio || 0) : 1)) +
                                            (scope.row.basicUnit)) : '') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="基本单位" prop="basicUnit" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="整件单位" prop="completeUnit" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="剂型" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="生产日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                            min-width="140"
                            :formatter="row => row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'" />
                        <!-- TODO -->
                        <el-table-column label="过期日期" prop="noticeContent" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="有效期" prop="validityTime" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="贮藏温区" prop="storageTemperature" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="库存余量" prop="availableInventory" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="上次购价" prop="lastDifferencePrice" :show-overflow-tooltip="true"
                            align="center" min-width="140" />
                        <el-table-column label="上次供应商" prop="lastSupplier" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                    </el-table>
                </el-collapse-item>
                <el-collapse-item title="入库记录" name="3">
                    <template #title>
                        <span class="col_title">入库记录</span>
                    </template>
                    <el-table :data="list.WarehousingList" border>
                        <el-table-column label="自编码" prop="commodity.commoditySelfCode" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <!-- // TODO -->
                        <el-table-column label="入库单编号" prop="commodityType" :show-overflow-tooltip="true" align="center"
                            :formatter="(row) => row.nodeName" min-width="120" />
                        <el-table-column label="入库日期" prop="intoTime" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="(row) => row.intoTime ? moment(row.intoTime).format('YYYY-MM-DD') : '--'" />
                        <!-- // TODO -->
                        <el-table-column label="入库数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="批号" prop="intoNo" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <!-- // TODO -->
                        <el-table-column label="单据状态" prop="nodeName" align="center" min-width="120" />
                    </el-table>
                </el-collapse-item>
                <el-collapse-item title="合计信息" name="4">
                    <template #title>
                        <span class="col_title">合计信息</span>
                    </template>
                    <div class="total">
                        <p><span>单据数量：</span><span>{{ list.essentialInformation.totalQuantity }}</span></p>
                        <p><span>单据金额：</span><span>{{ list.essentialInformation.totalAmount  }}</span></p>
                        <p><span>折后总金额：</span><span>{{ list.essentialInformation.totalAmountAfterDiscount  }}</span></p>
                    </div>
                </el-collapse-item>
                <el-collapse-item title="操作日志" name="5" >
                            <template #title>
                                <span class="col_title">操作日志</span>
                            </template>
                            <div style="max-height:500px;overflow-y:scroll">
                                <LogQuery ref="childRef" />
                            </div>
                        </el-collapse-item>
                        <el-collapse-item title="审批意见" name="6" v-if="type != 'record'">
                            <template #title>
                                <span class="col_title">审批意见</span>
                            </template>
                            <Audit ref="auditRef"  @refresh="getList"/>
                        </el-collapse-item>
            </el-collapse>
            </div>



            <template #footer>
                <div class="dialog-footer" v-if="!modalLoding">
                    <el-button type="primary" @click="handleSubmit" v-if="type != 'record'">确认</el-button>
                    <el-button @click="beforeClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup >

import { reactive, ref, getCurrentInstance, toRefs, watch, defineProps, onMounted } from 'vue'
import Audit from '@/components/detailsForm/audit.vue'
import LogQuery from '@/components/detailsForm/logQuery.vue'
import moment from 'moment'
import purchasingManagement from '@/api/erp/purchasingManagement'
import { drugApi } from "@/api/model/commodity/drug/index";
const { proxy } = getCurrentInstance();
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    beforeClose: {
        type: Function,
        default: () => { }
    },
    data: {
        type: Object,
        default: () => { }
    },
    getList:{
        type: Function,
        default: () => { }
    },
    type:{
        type: String,
        default: ''
    },
})

const modalLoding = ref(false)
const activeNames = ref(['1', '2', '3', '4', '5', '6'])
const form = ref({})
const list = ref([])
const InvoiceTypeList = ref([])
const settlementMethod = ref([])
const logisticsMethodsList = ref([])
const deductionRateList = ref([])
const listRecord = ref([])
const childRef = ref(null)
const auditRef = ref(null)
const { visible, beforeClose, data,getList,type } = toRefs(props)
const dateFomater = (date) => {
    return date ? moment(date)?.format('YYYY-MM-DD') : '--'
}
const formDict = (data, val) => {
    return (data && val) ? proxy.selectDictLabel(data, val) : '--'
}
const handleChange_msg = () => {

}
const handleChange = () => {

}

const handleSubmit = async () => {
    if(!auditRef.value.form?.status) return proxy.msgError('请先选择审批结果！')
    await auditRef.value.formSub(data.value?.id);
    await beforeClose.value()
}
async function dict() {
    InvoiceTypeList.value = await proxy.getDictList('erp_Invoice_type')  // 发票类型
    settlementMethod.value = await proxy.getDictList('erp_settlement_method') // 结算方式
    logisticsMethodsList.value = await proxy.getDictList('erp_logistics_methods') // 物流方式
    deductionRateList.value = await proxy.getDictList('serp_elfoperated_deduction_rate') // 自营扣率
}
onMounted(async () => {
    modalLoding.value = true
    await dict()
    await getDetail()
    let reviewRes = await purchasingManagement.erpPurchaseOrderApproval({ 'purchaseOrder.id': data.value.documentId })
    let resq = await drugApi.drugLog({ masterId: data.value.documentId })
    if ((reviewRes.code == 200 && reviewRes.data) || (resq.code == 200 && resq.data) ) {
        childRef.value.timeFns(reviewRes?.data?.records,resq?.data?.records)
        }
    // TODO 操作记录
    modalLoding.value = false
})
const getDetail = async () => {
    try {
        let res = await purchasingManagement.getIdOrder({ id: data.value.documentId })
        if (res.code == 200) {
            list.value.essentialInformation = {
                supplier: res.data?.erpPurchaseOrderDTO?.supplier,
                supplierRepresentative: res.data?.erpPurchaseOrderDTO?.supplierRepresentative,
                settlementMethod: res.data?.erpPurchaseOrderDTO?.settlementMethod,
                termsPayment: res.data?.erpPurchaseOrderDTO?.termsPayment,
                invoiceType: res.data?.erpPurchaseOrderDTO?.invoiceType,
                logisticsMode: res.data?.erpPurchaseOrderDTO?.logisticsMode,
                warehouseNumber: res.data?.erpPurchaseOrderDTO?.warehouseNumber,
                handledBy: res.data?.erpPurchaseOrderDTO?.handledBy,
                preparedBy: res.data?.erpPurchaseOrderDTO?.preparedBy,
                selfRate: res.data?.erpPurchaseOrderDTO?.selfRate,
                remark: res.data?.erpPurchaseOrderDTO?.remark,
                discountAmount: res.data?.erpPurchaseOrderDTO?.discountAmount,
                isGenerateContract: res.data?.erpPurchaseOrderDTO?.isGenerateContract,
                id: res.data?.erpPurchaseOrderDTO?.id,
                totalQuantity: res.data?.erpPurchaseOrderDTO?.totalQuantity,
                totalAmount: res.data?.erpPurchaseOrderDTO?.totalAmount,
                totalAmountAfterDiscount: res.data?.erpPurchaseOrderDTO?.totalAmountAfterDiscount,
            }
            list.value.detailedOrderInformation = res.data?.orderFormDTOS
        }
        // 获取入库记录
        let WarehousingRecords = await purchasingManagement.erpPurchaseWarehousingRecord({ purchaseId: data.value.documentId })
        if (WarehousingRecords.code == 200) {
            list.value.WarehousingList = WarehousingRecords.data?.records
        }
    } catch (error) {
        proxy.msgError(error)
    } 

}
</script>
<style lang="scss" scoped>
.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    ::v-deep .el-form-item {
        width: 25%;
    }

    ::v-deep .el-form-item__label {
        color: #333;
        font-weight: bold;
    }
}

.el-tabs {
    display: flex;
    // align-items: center;
}

.el-tabs__nav-wrap::after {
    width: 0 !important;
}

::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

::v-deep .rules {
    position: relative;

    .cell::after {
        content: "*";
        color: red;
        display: inline-block;
        position: absolute;
        top: 30%;
        left: 70px;
    }
}

::v-deep .rulesRemark {
    position: relative;

    .cell::after {
        content: "*";
        color: red;
        display: inline-block;
        position: absolute;
        top: 30%;
        left: calc(50% - 30px)
    }
}

.detailSpan {
    display: inline-block;
    text-overflow: ellipsis;
    /* 溢出显示省略号 */
    overflow: hidden;
    /* 溢出隐藏 */
    white-space: nowrap;
    /* 强制不换行 */
    color: #666;
}

.total {
    display: flex;
    margin: 10px 20px;

    p {
        margin-right: 50px;

        & span:nth-of-type(1) {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        & span:nth-of-type(2) {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}
</style>
