<template>
	<div class="app-container">
		<el-card class="box-card Botm" style="margin: 10px;">
			<el-form ref="queryRef" :inline="true" :model="queryParams" class="form_130">
				<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
					<el-form-item label="供应商" prop="suppier">
            <el-input v-model="queryParams.suppier" class="form_225" clearable placeholder="请输入供应商"/>
					</el-form-item>
					<el-form-item label="商品名称" prop="tradeName">
            <el-input v-model="queryParams.tradeName" class="form_225" clearable placeholder="请输入商品名称"/>
					</el-form-item>
					<el-form-item label="商品自编码" prop="commoditySelfCode">
						<el-input v-model="queryParams.commoditySelfCode" class="form_225" clearable
                      placeholder="请输入商品自编码"/>
					</el-form-item>
					<el-form-item label="商品编号" prop="commodityCode">
            <el-input v-model="queryParams.commodityCode" class="form_225" clearable placeholder="请输入商品编号"/>
					</el-form-item>

					<el-form-item label="库号" prop="warehouseNumber">
            <el-input v-model="queryParams.warehouseNumber" class="form_225" clearable placeholder="请输入库号"/>
					</el-form-item>
					<el-form-item label="经手人" prop="creator">
            <el-input v-model="queryParams.creator" class="form_225" clearable placeholder="请输入经手人"/>
					</el-form-item>
					<el-form-item label="申请人" prop="applyByName">
            <el-input v-model="queryParams.applyByName" class="form_225" clearable placeholder="请输入申请人"/>
					</el-form-item>
					<el-form-item label="采购单据编号" prop="purchaseOrder">
            <el-input v-model="queryParams.purchaseOrder" class="form_225" clearable placeholder="请输入采购单据编号"/>
					</el-form-item>
					<el-form-item label="采退申请编号" prop="applyNo">
            <el-input v-model="queryParams.applyNo" class="form_225" clearable placeholder="请输入采退申请编号"/>
					</el-form-item>
					<el-form-item label="出库日期" prop="createDate">
						<div class="box_date">
							<el-date-picker v-model="queryParams.createDate" class="form_225" end-placeholder="结束日期"
                              range-separator="至" start-placeholder="开始日期" type="daterange"/>
						</div>
					</el-form-item>
				</TopTitle>
			</el-form>
		</el-card>

		<!-- 表格数据 -->
		<el-card class="box-card" style="margin: 10px;">
			<div style="display:flex;justify-content: space-between;">
				<el-button size="default" type="primary" @click="handlerExport">导出关联票据</el-button>
        <RightToptipBar v-model:columns="columns" @handleRefresh="getList"/>
			</div>
      <el-table v-loading="loading" :data="list" border style="margin: 20px 0 0 0 " @selection-change="handlerSelect">
        <el-table-column align="center" min-width="55" type="selection"/>
				<el-table-column align="center" label="序号" prop="sort" width="80">
					<template #default="scope">
						<span>{{ scope.$index + 1 }}</span>
					</template>
				</el-table-column>
        <el-table-column align="left" label="采退申请编号" min-width="170" prop="applyNo"/>
				//TODO: ADD
        <el-table-column align="left" label="采购单据编号" min-width="170" prop="purchaseOrder"/>
        <el-table-column align="left" label="商品自编码" min-width="120" prop="commodity.commoditySelfCode"/>
        <el-table-column v-if="tableIsShow('commodity.commodityCode')" align="left" label="商品编号" min-width="120"
                         prop="commodity.commodityCode"/>
        <el-table-column align="left" label="商品名称" min-width="120" prop="commodity.tradeName"/>
				<el-table-column v-if="tableIsShow('commodity.commonName')" align="left" label="通用名" min-width="120"
                         prop="commodity.commonName"/>
        <el-table-column align="left" label="商品规格" min-width="120" prop="commodity.packageSpecification"/>
				<el-table-column v-if="tableIsShow('commodity.producingArea')" align="left" label="产地" min-width="120"
                         prop="commodity.producingArea"/>
				<el-table-column :show-overflow-tooltip="true" align="left" label="生产厂家" min-width="120"
                         prop="commodity.manufactureName"/>
				<el-table-column v-if="tableIsShow('approveNo')" align="left" label="批准文号" min-width="120"
                         prop="approveNo"/>
        <el-table-column align="left" label="剂型" min-width="120" prop="commodity.dosageForm"/>
        <el-table-column v-if="tableIsShow('suppier')" align="left" label="供应商" min-width="120" prop="suppier"/>
        <el-table-column align="left" label="批号" min-width="160" prop="batchNumber"/>
				<el-table-column align="left" label="生产日期" min-width="130">
					<template #default="scope">
						<span>{{ moment(scope.row.produceDate).format("YYYY-MM-DD") }}</span>
					</template>
				</el-table-column>
        <el-table-column align="left" label="有效期" min-width="150" prop="validate"/>
        <el-table-column align="left" label="仓库" min-width="120" prop="warehouse"/>
        <el-table-column align="left" label="库号" min-width="120" prop="warehouseNumber"/>
        <el-table-column align="left" label="货位" min-width="120" prop="goodsShelves"/>
        <el-table-column align="left" label="经手人" min-width="120" prop="creator"/>
        <el-table-column align="left" label="单位" min-width="120" prop="basicUnit"/>
        <el-table-column align="left" label="出库数量" min-width="120" prop="receivingQuantity"/>
        <el-table-column align="left" label="单价" min-width="120" prop="unitPrice"/>
        <el-table-column align="left" label="金额" min-width="120" prop="money"/>
        <el-table-column align="left" label="件装量" min-width="120" prop="unitLoading"/>
        <el-table-column align="left" label="件数" min-width="120" prop="pieceNumber"/>
        <el-table-column v-if="tableIsShow('listPermitHolder')" align="left" label="上市许可持有人" min-width="120"
                         prop="listPermitHolder"/>
				<el-table-column v-if="tableIsShow('applyByName')" align="left" label="申请人" min-width="120"
                         prop="applyByName"/>
				<el-table-column align="left" label="出库日期" min-width="130">
					<template #default="scope">
						<span>{{ moment(scope.row.intoTime).format("YYYY-MM-DD") }}</span>
					</template>
				</el-table-column>
        <el-table-column v-if="tableIsShow('receiver')" align="left" label="收货人" min-width="120" prop="receiver"/>
				<el-table-column v-if="tableIsShow('suppierContract')" align="left" label="供应商联系人" min-width="120"
                         prop="suppierContract"/>
				<el-table-column v-if="tableIsShow('suppierAddress')" align="left" label="供应商地址" min-width="120"
                         prop="suppierAddress"/>
				<el-table-column v-if="tableIsShow('suppierPhone')" align="left" label="联系电话" min-width="120"
                         prop="suppierPhone"/>

				<el-table-column align="center" fixed="right" label="进项发票" min-width="120">
					<template #default="scope">
						<el-button text type="primary"
                       @click="invoiceCheck(scope.row.inInvoiceId, 1, scope.row.inputBill)">{{
								echo2(scope.row.inputBill)
							}}
						</el-button>
					</template>
				</el-table-column>
				<el-table-column align="center" fixed="right" label="采退凭证" min-width="120">
					<template #default="scope">
						<el-button text type="primary" @click="detailFile(scope.row, '6')">查看</el-button>
					</template>
				</el-table-column>
				<el-table-column align="center" fixed="right" label="采退单" min-width="120">
					<template #default="scope">
						<el-button text type="primary" @click="uploadBtn(scope.row, '7', '采退单')">查看</el-button>
					</template>
				</el-table-column>
				<!--                <el-table-column label="状态" align="center">-->
				<!--                    <template #default="scope">-->
				<!--                        <span>{{ formDict(statusList, scope.row.status) }}</span>-->
				<!--                    </template>-->
				<!--                </el-table-column>-->

				<!--                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300px">-->
				<!--                    <template #default="scope">-->
				<!--                        <el-button link type="primary"  @click="handleAdd(scope.row)"-->
				<!--                            >详细信息</el-button>-->

				<!--                    </template>-->
				<!--                </el-table-column>-->

			</el-table>
			<div style="float: right;">
				<pagination v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
                    @pagination="getList"/>
			</div>
		</el-card>
		<!--查看进项发票信息-->
		<el-dialog v-model="dialogVisible" title="查看进项发票信息" width="80%">
      <billMess/>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisible3" title="采退凭证" width="40%">
      <listMess ref="messRef" :fileArr="data.fileArr"/>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible3 = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
		<!--采退单-->
		<el-dialog v-model="dialogVisible2" :title="data.title" width="40%">
      <uploadCom ref="uploadRef"/>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="uploadCancal()">取消</el-button>
					<el-button v-if="!rightBtnFlag" type="primary" @click="rightUpload()">确定</el-button>
				</span>
			</template>
		</el-dialog>
		<!-- 添加或修改角色配置对话框 -->
		<el-dialog v-model="open" :title="title" append-to-body width="600px">

			<p>将对一下{{ chooseList.length }}个审核任务进行批量同意操作，请确定</p>
			<div v-for="(item, index) in chooseList" :key="index" style="display:flex">
				<p>
					<span>ID=</span>
					<span>{{ item.id }}，</span>
				</p>
				<p>
					<span>任务标题：</span>
					<span>{{ item.document }}</span>
				</p>
			</div>
			<div v-show="batch == '2' ? true : false">
				<span>驳回原因</span>
        <el-input v-model="idea" clearable placeholder="请输入驳回原因" style="width: 240px"/>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="() => open = false">取 消</el-button>
					<el-button type="primary" @click="submitForm">确 定</el-button>
				</div>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogVisibleIn" title="查看开票详情" width="80%">
			<div v-loading="checkFlag">
				<relevancyDetails :detailType="data.detailType" :price="data.price" :strs="data.row"
                          :table1="data.detailTable1" :table2="data.detailTable2"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisibleIn = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
    <el-dialog v-model="dialogVisibleOut" title="查看开票详情" width="80%">
			<div v-loading="detailFlag">
				<ApplicationDetails v-if="dialogVisibleOut" :obj="data.obj" :table1="data.table1" :table2="data.table2"
                            :types="data.types"/>
			</div>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisibleOut = false">取消</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, toRefs} from 'vue'
import inboundRecordService from '@/api/erp/procure/erpPurchaseRetreatRecordService'
import moment from 'moment'
import TopTitle from '@/components/topTitle'
import RightToptipBar from '@/components/rightToptipBar'
import billMess from '../components/billMess.vue'
import uploadCom from '../components/uploadCom.vue'
import {inventoryApi} from "@/api/erp/inventory";
import {ElMessage} from "element-plus";
import listMess from '../components/listMess'
import {applocation, outPut, relevanceInput} from "@/api/model/invoice";
import relevancyDetails from '@/views/invoiceManagement/components/relevancyDetails.vue'
import ApplicationDetails from '@/views/invoiceManagement/components/ApplicationDetails.vue'

const dialogVisible3 = ref(false)
const uploadRef = ref(null)
const {proxy} = getCurrentInstance();

const list = ref([]);
const open = ref(false);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const batch = ref("");
const idea = ref("");
const statusList = ref([])
const chooseList = ref([])
const newFilArr = ref([])
const selsctList = ref([])
const showSearch = ref(false)
const dialogVisible = ref(false)
const dialogVisible2 = ref(false)

const data = reactive({
	form: {},
	queryParams: {
		current: 1,
		size: 10,
		busType: 2
	},
	row: {},
	fileType: null,
	title: '',
	fileArr: []
});
const messRef = ref(null)
const rightBtnFlag = ref(false)
const echo1 = (status) => {
	if (status == 0) {
		return '未上传'
	} else if (status == 1) {
		return '已上传'
	} else {
		return '未知'
	}
}
const echo2 = (status) => {
	console.log(status)
	if (status == 0) {
		return '未关联'
	} else if (status == 1) {
		return '已关联'
	} else {
		return '未知'
	}
}
//选中列表数据
const handlerSelect = (v) => {
	selsctList.value = v
}
const checkFlag = ref(false)
const detailFlag = ref(false)
const dialogVisibleOut = ref(false)
const dialogVisibleIn = ref(false)
const invoiceCheck = (row, num, status) => {
	if (status == 1) {
		if (num == 1) {
			detailTable(row)
		} else {
			detailTableOut(row)
		}
	}
}
const detailTableOut = async (row) => {
	data.table2 = []
	data.table1 = []
	dialogVisibleOut.value = true
	detailFlag.value = true
	const res = await applocation.detailList({
		'salesInvoiceId': row
	})
	const res2 = await outPut.detaiInvoices({
		'salesInvoice.id': row
	})
	if (res.code == 200 && res2.code == 200) {
		detailFlag.value = false
		data.types = res.data.salesInvoice.billingType
		data.auditStatus = res.data.salesInvoice.auditStatus
		data.invoiceStatus = res.data.salesInvoice.status
		if (data.types == 0) {
			res2.data.records.forEach(record => {
				data.table1.push(record.salesInvoiceReceipt)
			})
			res.data.salesInvoiceForms.forEach(item => {
				if (!item.invoiceFormPriceList) {
					item.invoiceFormPriceList = []
					item.invoiceFormPriceList.push({
						invoicePrice: item.unitPrice,
						invoiceQuantity: item.invoicedQuantity,
						totalAmount: item.totalAmount
					})
				}
				data.table2.push({
					'docNum': item.salesNo,
					'erpCustomer': {
						id: item.customer.id
					},
					'editId': item.id,
					'customer': item.customerName,
					'commodity': {
						'commonName': item.commodityName,
						'commoditySelfCode': item.commoditySelfCode,
						'packageSpecification': item.commodityPackageSpecification,
						'manufactureName': item.commodityManufactureName,
						'producingArea': item.commodityProducingArea,
						'validityTime': item.commodityValidityTime,
						'basicUnit': item.commodityBasicUnit,
					},
					'checkFlag': false,
					'invoiceFormPriceList': item.invoiceFormPriceList,
					'totalAmount': item.totalAmount,
					'salesOutBound': {
						'outTime': item.salesOutBound.outTime
					},
          'totalPriceTax': item.totalPriceTax,
          'totalTax': item.totalTax,
          'taxRate': item.taxRate,
					'createDate': item.salesOrder.docDate,
					'produceDate': item.commodityProduceDate,
					'batchNumber': item.batchNo,
					'unitPrice': item.unitPrice,
					'outQuantity': item.boundQuantity,
					'supplier': null,
					'supplierName': null,
					'allPrice': (item.unitPrice * item.boundQuantity).toFixed(2)
				})
				data.obj = res.data.salesInvoice
			})
		} else {
			res2.data.records.forEach(record => {
				data.table1.push(record.salesInvoiceReceipt)
			})
			res.data.salesInvoiceForms.forEach(item => {
				data.table2.push({
					'docNum': item.salesNo,
					'editId': item.id,
					'customer': item.customerName,
					'commodity': {
						'commonName': item.commodityName,
						'commoditySelfCode': item.commoditySelfCode,
						'packageSpecification': item.commodityPackageSpecification,
						'manufactureName': item.commodityManufactureName,
						'producingArea': item.commodityProducingArea,
						'validityTime': item.commodityValidityTime,
						'basicUnit': item.commodityBasicUnit,
					},
					'totalAmount': item.totalAmount,
					'salesOutBound': {
						'outTime': item.salesRetreatInbound.inTime
					},
					'createDate': item.salesInvoice.applyDate,
					'produceDate': item.commodityProduceDate,
					'batchNumber': item.batchNo,
					'unitPrice': item.unitPrice,
					'outQuantity': item.boundQuantity,
					'num': item.invoicedQuantity,
					'supplier': null,
					'supplierName': null,
					'allPrice': item.totalAmount
				})
				data.obj = res.data.salesInvoice
			})
		}
	} else {
		ElMessage.error('获取失败')
	}
}
const detailTable = (row) => {
	checkFlag.value = true
	dialogVisibleIn.value = true
	data.detailTable1 = []
	data.price = 0
	data.row = {}
	data.detailTable2 = []
	relevanceInput.detailList({
		id: row
	}).then(res => {
		if (res.code == 200) {
			data.row = res.data.erpPurchaseInvoiceDto
			data.detailType = data.row.type
			res.data.erpPurchaseInvoiceFormDtos.forEach((item) => {
				data.detailTable2.push(
					{
						id: item.id,
						orderCode: item.purchaseNo,
						commodity: {
							tradeName: item.commodityName,
							commoditySelfCode: item.commoditySelfCode,
							packageSpecification: item.commodityPackageSpecification,
							manufactureName: item.commodityManufactureName,
							producingArea: item.commodityProducingArea,
							commodityCode: item.commodityCode,
							produceDate: item.commodityProduceDate
						},
						checkFlag: false,
						invoicingInfo: item.invoicingInfo?.split(','),
						unitPrice: item.commodityUnitPrice.toFixed(2),
						receivingQuantity: item.inboundQuantity,
						allPrice: item.invoicingAmount.toFixed(2),
						basicUnit: item.commodityBasicUnit,
						validate: item.commodityValidityTime,
						invoicingQuantity: item.invoicingQuantity,
						purchaseCreatedDate: item.purchaseCreatedDate,
						intoNo: item.batchNo,
						produceDate: item.produceDate,
						purchaseCreateDate: item.purchaseCreateDate,
						intoDate: item.intoDate,
						suppier: item.supplierName,
					}
				)
			})
			data.price = res.data.erpPurchaseInvoiceDto.invoiceAmount
			res.data.erpPurchaseInvoiceUnionDtos.forEach((item, index) => {
				data.detailTable1.push({
					id: item.id,
					invoiceNo: item.purchaseInvoiceReceipt.invoiceNo,
					invoiceCode: item.purchaseInvoiceReceipt.invoiceCode,
					invoiceSupplier: item.purchaseInvoiceReceipt.invoiceSupplier,
					taxpayerNo: item.purchaseInvoiceReceipt.taxpayerNo,
					invoicingDate: item.purchaseInvoiceReceipt.invoicingDate,
					invoicingAmount: item.purchaseInvoiceReceipt.invoicingAmount,
          excludingTax: item.purchaseInvoiceReceipt.excludingTax,
          totalTax: item.purchaseInvoiceReceipt.totalTax,
          effectiveTax: item.purchaseInvoiceReceipt.effectiveTax,
          type: item.purchaseInvoiceReceipt.type,
					fileDtos: []
				})
				item.purchaseInvoiceReceipt.fileDtos.forEach(item => {
					data.detailTable1[index].fileDtos.push({
						id: item.id,
						name: item.fileName,
						url: item.fileUrl
					})
				})
			})
		}
		checkFlag.value = false
	})
}
//导出列表数据
const handlerExport = () => {
	if (selsctList.value.length == 0) {
		ElMessage({
			type: "error",
			message: "请勾选数据后再执行操作",
		});
	}
	if (0 < selsctList.value.length && selsctList.value.length < 1000) {
		let exportList = []
		selsctList.value?.forEach((v) => {
			exportList.push({
				purchaseRecodeId: v?.id,
				tradeName: v.commodity?.tradeName,
				purchaseOrderNo: v?.applyNo,
			})
		})
		let params1 = {
			params: exportList,
			busType: '3'
		}
		inboundRecordService.export(params1).then(res => {
			proxy.download(res, "application/zip")
		})
	}
	if (selsctList.value.length > 1000) {
		ElMessage({
			type: "error",
			message: "请勾选1000条以下的数据再执行导出",
		});
	}
}
const uploadBtn = (row, status, title) => {
	data.title = title
	dialogVisible2.value = true
	setTimeout(() => {
		data.row = row
		data.fileType = status
		uploadRef.value.fileList = []
		uploadRef.value.delFile = []
		uploadRef.value.loadingFlag = true
		inventoryApi.getFile({
			id: row.id,
			type: status,
			busType: 25
		}).then(res => {
			if (res.code != 200) {
				uploadRef.value.loadingFlag = false
				ElMessage.error('获取失败')
				return
			}
			res.data.forEach(item => {
				uploadRef.value.fileList.push({
					id: item.id,
					name: item.fileName,
					url: item.fileUrl ? item.fileUrl : null
				})
			})
			uploadRef.value.loadingFlag = false
		})
	})
}
const rightUpload = () => {
	const fileArr = []
	const delFile = []
	uploadRef.value.fileList.forEach(item => {
		if (item.response) {
			fileArr.push({
				commonId: data.row.id,
				commonType: 25,
				fileType: data.fileType,
				fileName: item.name,
				fileUrl: item.response.data.url
			})
		}
	})
	uploadRef.value.delFile.forEach(item => {
		if (!item.response) {
			delFile.push({
				id: item.id,
				name: item.name,
			})
		}
	})
	delFile.forEach(item => {
    inventoryApi.delFile({id: item.id}).then(res => {
			if (res.code != 200) {
				ElMessage.error(item.name + '删除失败')
			}
		})
	})
	if (fileArr.length > 0) {
		inventoryApi.saveFileOrder(fileArr).then(res => {
			if (res.code == 200) {
				ElMessage.success('文件保存成功')
				dialogVisible2.value = false
				uploadRef.value.fileList = []
				uploadRef.value.delFile = []
			} else {
				ElMessage.error('文件保存失败')
			}
		})
	} else {
		dialogVisible2.value = false
	}
	setTimeout(() => {
		handleQuery()
	})
}
const detailFile = (row, status) => {
	dialogVisible3.value = true
	data.fileArr = []
	setTimeout(() => {
		data.row = row
		data.fileType = status
		messRef.value.loadingFlag = true
		inventoryApi.getFile({
			id: row.id,
			type: status,
			busType: 25
		}).then(res => {
			if (res.code != 200) {
				messRef.value.loadingFlag = false
				ElMessage.error('获取失败')
				return
			}
			data.fileArr = res.data
			messRef.value.loadingFlag = false
		})
	})
}
const uploadCancal = () => {
	dialogVisible2.value = false
	setTimeout(() => {
		uploadRef.value.fileList = []
		uploadRef.value.delFile = []
	}, 500)
}
const columns = ref([
	// {
	//    label:'采购订单编号',
	//    prop:'orderCode'
	// },
	// {
	//    label:'商品自编码',
	//    prop:'orderCode'
	// },
	{
		label: '商品编号',
		prop: 'commodity.commodityCode',
		isShow: true
	},
	// {
	//    label:'商品名称',
	//    prop:'commodity.tradeName'
	// },
	{
		label: '通用名',
		prop: 'commodity.commonName',
		isShow: true
	},
	// {
	//    label:'商品规格',
	//    prop:'commodity.packageSpecification'
	// },
	{
		label: '产地',
		prop: 'commodity.producingArea',
		isShow: true
	},
	// {
	//    label:'生产厂家',
	//    prop:'commodity.manufactureName'
	// },
	{
		label: '批准文号',
		prop: 'approveNo',
		isShow: true
	},
	{
		label: '剂型',
		prop: 'commodity.dosageForm',
		isShow: true
	},
	{
		label: '供应商',
		prop: 'suppier',
		isShow: true
	},
	// {
	//    label:'批号',
	//    prop:'intoNo'
	// },
	// {
	//    label:'生产日期',
	//    prop:'produceDate'
	// },
	// {
	//    label:'有效期',
	//    prop:'validate'
	// },
	{
		label: '库号',
		prop: 'storages',
		isShow: true
	},
	{
		label: '货位',
		prop: 'document',
		isShow: true
	},
	// {
	//    label:'经手人',
	//    prop:'creator'
	// },
	// {
	//    label:'单位',
	//    prop:'basicUnit'
	// },
	// {
	//    label:'出库数量',
	//    prop:'receivingQuantity'
	// },
	// {
	//    label:'单价',
	//    prop:'unitPrice'
	// },
	// {
	//    label:'金额',
	//    prop:'money'
	// },
	// {
	//    label:'件装量',
	//    prop:'unitLoading'
	// },
	// {
	//    label:'件数',
	//    prop:'pieceNumber'
	// },
	{
		label: '上市许可持有人',
		prop: 'listPermitHolder',
		isShow: true
	},
	// {
	//    label:'发票号',
	//    prop:'nodeName',
	// 	isShow:false
	// },
	{
		label: '开票时间',
		prop: 'document',
		isShow: true
	},
	{
		label: '申请人',
		prop: 'applyByName',
		isShow: true
	},
	{
		label: '审核',
		prop: 'creator',
		isShow: true
	},
	{
		label: '收货',
		prop: 'receiver',
		isShow: true
	},
	{
		label: '验收',
		prop: 'checker',
		isShow: true
	},
	{
		label: '上架',
		prop: 'upper',
		isShow: true
	},
	// {
	//    label:'入库时间',
	//    prop:'intoTime'
	// },
	{
		label: '供应商联系人',
		prop: 'suppierContract',
		isShow: true
	},
	{
		label: '供应商地址',
		prop: 'suppierAddress',
		isShow: true
	},
	{
		label: '联系电话',
		prop: 'suppierPhone',
		isShow: true
	},
])
const {queryParams, form, rules} = toRefs(data);
const handleSelectionChange_file = (key) => {
	chooseList.value = key

}
const tableIsShow = key => {
	return columns.value.filter(item => item.prop == key)[0]?.isShow
}
const handleAllSub = () => {
	if (batch.value == "2") {
		title.value = "批量驳回"
	} else {
		title.value = "批量同意"
	}
	open.value = true
}

/** 查询角色列表 */
function getList() {
	loading.value = true
  const params = {...queryParams.value}
	params.beginInTime = params.createDate?.length ? moment(params.createDate[0]).format('YYYY-MM-DD') : undefined
	params.endInTime = params.createDate?.length ? moment(params.createDate[1]).format('YYYY-MM-DD') : undefined
	delete params.createDate
	inboundRecordService.list(params).then(res => {
		if (res.code == 200) {
			list.value = res.data.records
			total.value = res.data.total
		}
		loading.value = false
	})
}

const formDict = (data, val) => {
	return proxy.selectDictLabel(data, val)
}

/** 搜索按钮操作 */
function handleQuery() {
	getList();
}

const handleAdd = (row) => {
	row ? (title.value = '修改') : (title.value = '新增')
	if (row) {
    const {document, sort, noticeContent, status, id} = row
		typeList.value = {
			document,
			sort,
			noticeContent,
			status,
			id
		}
	} else {
		form.value = {}
	}
	open.value = true
}

/** 重置按钮操作 */
function resetQuery() {
	proxy.resetForm("queryRef");
	handleQuery();
}

function submitForm() {

	let idss = "";
	let status = "";
	if (batch.value == "2") {
		status = "11"
	} else {
		status = "30"
	}
	newFilArr.value = [];
	chooseList.value.filter((item) => {
		newFilArr.value.push(item.id)

	})
	console.log(newFilArr.value)
	idss = newFilArr.value.join(",")
  inboundRecordService.handbatch({ids: idss, status: status, idea: idea.value}).then(res => {
		console.log(res);
		if (res.code == 200) {
			proxy.msgSuccess(`批量审批成功`)
			open.value = false
			getList()
		} else {
			open.value = false
			proxy.msgSuccess(res.msg)
		}
	})


}


async function dict() {
	statusList.value = await proxy.getDictList('audit_status')
	// typeList.value = await proxy.getDictList('sys_notice_type')
}

dict()
getList();
</script>

<style lang="scss" scoped>
::v-deep .Botm {
	.el-card__body {
		padding-bottom: 0px
	}
}

.box_date {
	width: 220px;
}

::v-deep .el-upload {
	display: inline;
}

::v-deep .el-upload-list {
	margin-top: 0px;
}
</style>
