import http from '@/utils/request';

export default {
    /** s 客户预存款 */
    // 预付款列表
    listAdvancePayment: function (params) {
        return http.get('/tms/advancePayment/list', params);
    },
    // 有预付款账户的客户下拉框
    companySelect: function (params) {
        return http.get('/tms/advancePayment/companySelect', params);
    },
    // 保存
    saveAdvancePayment: function (data) {
        return http.post('/tms/advancePayment/save', data);
    },
    // 详情
    queryAdvancePaymentById: function (params) {
        return http.get('/tms/advancePayment/queryById', params);
    },
    // 修改状态
    updateStatus: function (params) {
        return http.get('/tms/cooperate/company/updateStatus', params);
    },
    // 预付款交易记录列表
    listAdvancePaymentDetail: function (params) {
        return http.get('/tms/advancePaymentDetail/list', params);
    },
    // 充值录入
    recharge: function (data) {
        return http.post('/tms/advancePayment/recharge', data);
    },
    // 交易录入
    transaction: function (data) {
        return http.post('/tms/advancePayment/transaction', data);
    },
    // 预付款列表导出
    advancePaymentExport: function (params, config, resDetail, responseType) {
        return http.get('/tms/advancePayment/export', params, config, resDetail, responseType);
    },
    // 搜索运单号
    searchOrderNo: function (params) {
        return http.get('/tms/orderDrug/searchOrderNo', params);
    },
    // 交易记录导出
    advancePaymentDetailExport: function (params, config, resDetail, responseType) {
        return http.get('/tms/advancePaymentDetail/export', params, config, resDetail, responseType);
    },
    // 交易记录金额统计
    getTradeRecordAmount: function (params) {
        return http.get('/tms/advancePaymentDetail/amount/stats', params);
    },
    // 预付款统计
    statAdvancePayment: function (params) {
        return http.get('/tms/advancePayment/stat', params);
    }
    /** e 客户预存款 */
};
