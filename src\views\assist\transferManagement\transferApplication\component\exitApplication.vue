<template>
    <div class="app-container" v-if="open">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog :title="title" v-model="open" width="85%" :before-close="close">
            <div v-loading="loading">
                <div class="step" v-if="modalType != 'detail'">
                    <el-steps :active="active" align-center>
                        <el-step title="选择调拨库存" />
                        <el-step title="填写调拨信息" />
                        <el-step title="提交申请" />
                    </el-steps>
                </div>

                <div class="step1 " v-show="active == 1">
                    <el-form :model="queryParams" ref="queryRef" :inline="true" class="form_130"
                        v-if="modalType != 'detail'" :rules="rules">
                        <div class="box">
                            <el-form-item label="经手人" prop="creatorId">
                                <el-select v-model="queryParams.creatorId" filterable placeholder="请选择经手人" clearable
                                    class="form_225" :disabled="roleList.length == 1 && roleList[0].enName == 'ywjl'"
                                    @change="handleIncomingBy_step1">
                                    <el-option v-for="item in optionList" :key="item.id" :label="item.name"
                                        :value="item.id" />
                                </el-select>
                            </el-form-item>

                            <el-form-item label="
                            库号" prop="warehouseNumberId">
                                <el-select v-model="queryParams.warehouseNumberId" filterable placeholder="请选择库号" clearable
                                    class="form_225" :disabled="!queryParams.creatorId">
                                    <el-option v-for="item in warehouseNumber_step1" :key="item.id"
                                        :label="item.warehouseNumber" :value="item.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="供应商" prop="suppier">
                                <el-select v-model="queryParams.suppier" filterable placeholder="请选择供应商" class="form_225"
                                    clearable>
                                    <el-option :label="item.enterpriseName" :value="item.id" v-for="item in supplierList"
                                        :key="item.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="商品名称" prop="tradeName">
                                <el-input v-model="queryParams.tradeName" placeholder="请输入商品名称" clearable
                                    class="form_225" />
                            </el-form-item>

                            <el-form-item label="单据编号" prop="orderCode">
                                <el-input v-model="queryParams.orderCode" placeholder="请输入单据编号" clearable
                                    class="form_225" />
                            </el-form-item>
                        </div>
                        <div style="float: right;padding-right: 40px;padding-bottom: 20px;">
                            <el-button type="primary" @click="step1_getList" class="step1_search_btn">查询</el-button>
                        </div>
                    </el-form>
                    <h4 class="titleH4" v-if="modalType == 'detail'">
                        <span>
                            申请编号:<span
                                style="color: #505050;font-size:13px;font-weight: 500;margin-right:10px;margin-left:10px">
                                {{
                                    detailData.applyNo ||
                                    '--' }}</span>
                        </span>
                        <span>
                            申请日期: <span
                                style="color: #505050;font-size:13px;font-weight: 500;margin-right:10px;margin-left:10px">
                                {{
                                    detailData.applyDate ?
                                    moment(detailData.applyDate).format('YYYY-MM-DD') : '--' }}</span>
                        </span>
                        <span v-if="activeName === '2'">
                            调拨类型:<span
                                style="color: #505050;font-size:13px;font-weight: 500;margin-right:10px;margin-left:10px">
                                {{
                                    formDict(allocationType, detailData.transferType) || '--' }}</span>
                        </span>
                        <span>
                            申请人:<span
                                style="color: #505050;font-size:13px;font-weight: 500;margin-right:10px;margin-left:10px">
                                {{
                                    detailData.createBy &&
                                    detailData.createBy.name || '--' }}</span>
                        </span>
                        <br />
                        <span style="margin-top: 10px;display: inline-block;">
                            确认状态: <span
                                style="color: #505050;font-size:13px;font-weight: 500;margin-right:10px;margin-left:10px">{{
                                    formDict(transferConfirmationStatus,
                                        detailData.confirmStatus) }}</span> <span style="margin-left: 50px;"
                                v-if="detailData.confirmStatus === '2'">拒绝理由：</span> <span v-if="detailData.confirmStatus === '2'"
                                style="color: #505050;font-size:13px;font-weight: 500;margin-right:10px;margin-left:10px">{{ form.reason }}</span>
                        </span>
                        <br />
                        <span style="margin-top: 10px;display: inline-block;">
                            审核状态: <span
                                style="color: #505050;font-size:13px;font-weight: 500;margin-right:10px;margin-left:10px">{{
                                    formDict(reviewStatus,
                                        detailData.auditStatus) }}</span>
                        </span>
                    </h4>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px" v-if="modalType == 'detail'">调拨明细</h3>
                    <el-table :data="step1_list" border style="margin-top: 20px;" row-key="id"
                        ref="entrust_out_table_listRef" @selection-change="handleSelectionChange_step1_list">
                        <el-table-column type="selection" min-width="55" align="center" :reserve-selection="true"
                            v-if="modalType != 'detail'" />
                        <el-table-column label="序号">
                            <template #default="scope">
                                {{ scope.$index + 1 }}
                            </template>
                        </el-table-column>

                        <el-table-column label="单据编号" :prop="modalType == 'detail' ? 'purchaseOrderCode' : 'orderCode'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="商品名称"
                            :prop="modalType == 'detail' ? 'commodityName' : 'commodity.tradeName'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="自编码"
                            :prop="modalType == 'detail' ? 'commoditySelfCode' : 'commodity.commoditySelfCode'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="规格"
                            :prop="modalType == 'detail' ? 'commodityPackageSpecification' : 'commodity.packageSpecification'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="生产厂家"
                            :prop="modalType == 'detail' ? 'commodityManufactureName' : 'commodity.manufactureName'"
                            align="center" min-width="120" :show-overflow-tooltip="true" />
                        <el-table-column label="产地"
                            :prop="modalType == 'detail' ? 'commodityOriginPlace' : 'commodity.producingArea'"
                            align="center" min-width="120" />
                        <el-table-column label="供应商" :prop="modalType == 'detail' ? 'supplier.enterpriseName' : 'suppliers'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="生产日期" :prop="modalType == 'detail' ? 'commodityProduceDate' : 'proDate'"
                            :show-overflow-tooltip="true" align="center" min-width="120"
                            :formatter="row => row[modalType == 'detail' ? 'commodityProduceDate' : 'proDate'] ? moment(row[modalType == 'detail' ? 'commodityProduceDate' : 'proDate']).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="批号" :prop="modalType == 'detail' ? 'batchNumber' : 'lotNo'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="有效期" :prop="modalType == 'detail' ? 'commodityValidityTime' : 'valiDate'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="基本单位"
                            :prop="modalType == 'detail' ? 'commodityBasicUnit' : 'commodity.basicUnit'"
                            :show-overflow-tooltip="true" align="center" min-width="140" />
                        <el-table-column label="商品编号" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="库存数量" prop="availableInventory" :show-overflow-tooltip="true" align="center"
                            min-width="120" v-if="activeName == '2'"/>
                        <el-table-column :label="modalType == 'detail' ? '调拨数量' : '可调拨数量'"
                            :prop="modalType == 'detail' ? 'transferQuantity' : 'availableInventory'"
                            :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="调拨单价" :prop=" detailData.transferType == '2' ? 'transferUnitPrice' : 'commodityUnitPrice'" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="单价" :prop="modalType == 'detail' ? 'commodityUnitPrice' : 'unitPrice'"
                            v-if="activeName == '2'" :show-overflow-tooltip="true" align="center" min-width="120" />
                        <el-table-column label="金额" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            v-if="activeName == '2'" min-width="120"
                            :formatter="row => (Number((modalType == 'detail' ? row.commodityUnitPrice : row.unitPrice) || 0) * Number((modalType == 'detail' ? row.transferQuantity : row.availableInventory) || 0)).toFixed(2)" />
                    </el-table>
                    <div style="display:flex;justify-content:end;margin-top:20px" v-if="modalType != 'detail'">
                        <pagination v-show="step1_list_Total > 0" :total="step1_list_Total"
                            v-model:page="queryParams.current" v-model:limit="queryParams.size"
                            @pagination="step1_getList" />
                    </div>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;" v-if="modalType == 'detail'">
                        调拨信息
                    </h3>
                    <table border="0" cellpadding="0" cellspacing="1" class="messTable"
                        v-if="modalType == 'detail' && form">
                        <tr v-if="activeName=='2'">
                            <td>调拨类型</td>
                            <td>{{ formDict(allocationType, form.transferType) || '--' }}</td>
                        </tr>

                        <tr>
                            <td>合计调拨数量</td>
                            <td>{{ form.transferAmount || '--' }}</td>
                        </tr>
                        <tr v-if="activeName == '2'">
                            <td>合计调拨差价</td>
                            <td>{{ form.transferQuantity || '--' }}</td>
                        </tr>
                        <tr>
                            <td>出货经手人</td>
                            <td>{{ form.shipmentBy && form.shipmentBy.name || '--' }}</td>
                        </tr>
                        <tr>
                            <td>出货库号</td>
                            <td>{{ form.shipmentWarehouseNo && form.shipmentWarehouseNo.warehouseNumber || '--' }}</td>
                        </tr>
                        <tr>
                            <td>入货经手人</td>
                            <td>{{ form.incomingBy && form.incomingBy.name || '--' }}</td>
                        </tr>
                        <tr>
                            <td>入货库号</td>
                            <td>{{ form.incomingWarehouseNo && form.incomingWarehouseNo.warehouseNumber || '--' }}</td>
                        </tr>
                        <tr>
                            <td>备注</td>
                            <td>{{ form.remark || '--' }}</td>
                        </tr>
                    </table>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;"
                        v-if="modalType == 'detail' && activeName === '1' && detailData.confirmStatus === '0'">
                        确认意见
                    </h3>
                    <el-form ref="creatformRef" :model="creatform" :rules="creatRules" label-width="70px"  v-if="modalType == 'detail' && activeName === '1' && detailData.confirmStatus === '0'">
                        <el-form-item label="确认结果" label-width="100px" prop="confirmStatus">
                            <el-radio-group v-model="creatform.confirmStatus" >
                                <el-radio label="1">同意</el-radio>
                                <el-radio label="2">拒绝</el-radio>
                            </el-radio-group>
                        </el-form-item>

                        <el-form-item  prop="reason" label="拒绝原因" label-width="100px" v-if="creatform.confirmStatus == '2'">
                            <el-input v-model="creatform.reason" :rows="3" placeholder="请输入拒绝原因" style="width: 60%"
                                type="textarea"   maxlength="100" show-word-limit clearable/>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="step2" v-show="active == 2">
                    <div class="transferType">
                        <span>调拨类型：</span>
                        <el-radio-group v-model="transferType">
                            <el-radio :label="item.value" v-for="(item, index) in allocationType" :key="index">{{ item.name
                            }}</el-radio>
                        </el-radio-group>
                    </div>
                    <el-table :data="choose_setp1" border>
                        <el-table-column label="商品名称" prop="commodity.tradeName" />
                        <el-table-column label="规格" prop="commodity.packageSpecification" />
                        <el-table-column label="生产厂家" prop="commodity.manufactureName" />
                        <el-table-column label="批号" prop="lotNo" />
                        <el-table-column label="可调拨数量" prop="availableInventory" />
                        <el-table-column label="单价" prop="unitPrice" />
                        <el-table-column label="调拨数量" align="center" :show-overflow-tooltip="true" fixed="right">
                            <template #default="scope">
                                <div v-clickOutside="() => handleClickOutside(scope, 'isShowtransferQuantity')">
                                    <p v-if="!scope.row.isShowtransferQuantity"
                                        style="height: 30px;line-height:30px;color: red;font-size: 16px;"
                                        @click="handleInputEdit(scope, 'transferQuantity')">
                                        {{ Tofixed_(scope.row.transferQuantity, 0) || '请输入数量' }}</p>
                                    <el-input-number v-model="scope.row.transferQuantity" placeholder="数量"
                                        v-if="scope.row.isShowtransferQuantity" :min="0" :precision="0"
                                        style="width: 100%;height: 30px" :max="scope.row.availableInventory" clearable />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="调拨单价" align="center" :show-overflow-tooltip="true" fixed="right">
                            <template #default="scope">
                                <div v-clickOutside="() => handleClickOutside(scope, 'isShowtransferUnitPrice')">
                                    <p v-if="!scope.row.isShowtransferUnitPrice"
                                        style="height: 30px;line-height:30px;color: red;font-size: 16px;"
                                        @click="transferType != '1' ? handleInputEdit(scope, 'transferUnitPrice') : undefined">
                                        {{ transferType == '1' ? scope.row.unitPrice : (scope.row.transferUnitPrice ||
                                            '请输入单价')
                                        }}
                                    </p>
                                    <el-input-number v-model="scope.row.transferUnitPrice" placeholder="单价"
                                        v-if="scope.row.isShowtransferUnitPrice && transferType != '1'"
                                        :min="scope.row.unitPrice" :precision="2" style="width: 100%;height: 30px"
                                        clearable />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" fixed="right" align="center">
                            <template #default="scope">
                                <el-button type="primary" text @click="detailGoods(scope.row)">
                                    查看详情
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-drawer v-model="detailFlag" title="商品详情" direction="rtl" size="30%">
                        <div style="padding: 0 20px">
                            <table border="0" cellpadding="0" cellspacing="1" class="detailTable">
                                <tr>
                                    <td>单据编号</td>
                                    <td>{{ step2_detail.orderCode }}</td>
                                </tr>
                                <tr>
                                    <td>商品名称</td>
                                    <td>{{ step2_detail.commodity && step2_detail.commodity.tradeName || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>自编码</td>
                                    <td>{{ step2_detail.commodity && step2_detail.commodity.commoditySelfCode || '--' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td>规格</td>
                                    <td>{{ step2_detail.commodity && step2_detail.commodity.packageSpecification || '--' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td>生产厂家</td>
                                    <td>{{ step2_detail.commodity && step2_detail.commodity.manufactureName || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>产地</td>
                                    <td>{{ step2_detail.commodity && step2_detail.commodity.producingArea || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>供应商</td>
                                    <td>{{ step2_detail.suppliers || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>生产日期</td>
                                    <td>{{ step2_detail.proDate ? moment(step2_detail.proDate).format('YYYY-MM-DD') : '--'
                                    }}</td>
                                </tr>
                                <tr>
                                    <td>批号</td>
                                    <td>{{ step2_detail.lotNo || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>有效期</td>
                                    <td>{{ step2_detail.valiDate || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>单位</td>
                                    <td>{{ step2_detail.commodity && step2_detail.commodity.basicUnit || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>商品编号</td>
                                    <td>{{ step2_detail.commodityCode || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>库存数量</td>
                                    <td>{{ step2_detail.availableInventory || '-' }}</td>
                                </tr>
                                <tr>
                                    <td>可调拨数量</td>
                                    <td>{{ step2_detail.availableInventory || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>单价</td>
                                    <td>{{ step2_detail.unitPrice || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>金额</td>
                                    <td>{{ (Number(step2_detail.unitPrice || 0) * Number(step2_detail.availableInventory ||
                                        0)).toFixed(2) }}</td>
                                </tr>
                            </table>
                        </div>
                    </el-drawer>
                    <el-form :model="form" ref="queryRef_setp2" :inline="true" class="form_130" style="margin-top: 30px;"
                        :rules="formRules">
                        <div class="box">
                            <el-form-item label="入货经手人" prop="incomingBy">
                                <el-select v-model="form.incomingBy" filterable placeholder="请选择入货经手人" clearable
                                    class="form_225" @change="handleIncomingBy">
                                    <el-option v-for="item in optionList" :key="item.id" :label="item.name"
                                        :value="item.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="入货库号" prop="incomingWarehouseNo">
                                <el-select v-model="form.incomingWarehouseNo"
                                    :placeholder="!form.incomingBy ? '请先选择入货经手人' : '请选择入货库号'" class="form_225"
                                    :disabled="!form.incomingBy" @change="handleIncomingWarehouseNo">
                                    <el-option :label="item.warehouseNumber" :value="item.id"
                                        v-for="item in warehouseNumber " :key="item.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="出货经手人" prop="shipmentBy">
                                <el-select v-model="form.shipmentBy" filterable clearable class="form_225" disabled>
                                    <el-option v-for="item in optionList" :key="item.id" :label="item.name"
                                        :value="item.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="出货库号" prop="shipmentWarehouseNo">
                                <el-select v-model="form.shipmentWarehouseNo" placeholder="请选择出货库号" class="form_225"
                                    disabled>
                                    <el-option :label="item.warehouseNumber" :value="item.id"
                                        v-for="item in warehouseNumber_step1 " :key="item.id" />
                                </el-select>
                            </el-form-item>
                        </div>
                        <div style="width: 100%;" class="box_2">
                            <el-form-item label="备注" prop="remark" style="width: 100%;">
                                <el-input v-model="form.remark" placeholder="请输入备注" clearable type="textarea"
                                    maxlength="100" show-word-limit style="width: 94%;" />
                            </el-form-item>
                        </div>
                    </el-form>
                </div>
                <div class="step2" v-show="active == 3">
                    <h3 style="margin: 10px 0">退货明細</h3>
                    <el-table :data="choose_setp1" border style="margin-top: 30px;">
                        <el-table-column label="序号">
                            <template #default="scope">
                                {{ scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column label="单据编号" prop="orderCode" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="自编码" prop="commodity.commoditySelfCode" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="规格" prop="commodity.packageSpecification" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="生产厂家" prop="commodity.manufactureName" align="center" min-width="120" />
                        <el-table-column label="产地" prop="commodity.producingArea" align="center" min-width="120" />
                        <el-table-column label="供应商" prop="suppliers" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="生产日期" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="row => row.proDate ? moment(row.proDate).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="批号" prop="lotNo" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="有效期" prop="valiDate" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="基本单位" prop="commodity.basicUnit" :show-overflow-tooltip="true"
                            align="center" min-width="140" />
                        <el-table-column label="商品编号" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                            min-width="140" />
                        <el-table-column label="库存数量" prop="availableInventory" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="可调拨数量" prop="availableInventory" :show-overflow-tooltip="true"
                            align="center" min-width="120" />

                        <el-table-column label="单价" prop="unitPrice" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="调拨数量" prop="transferQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="120" fixed="right" />
                        <el-table-column label="调拨价格" prop="transferUnitPrice" :show-overflow-tooltip="true" align="center"
                            min-width="120" fixed="right"
                            :formatter="row => transferType == '1' ? row.unitPrice : row.transferUnitPrice" />
                        <el-table-column label="调拨差价" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120" fixed="right"
                            :formatter="row => transferType == '1' ? '0.00' : (Number(row.transferUnitPrice || 0) - Number(row.unitPrice || 0)).toFixed(2)" />
                    </el-table>
                    <h3 style="margin: 10px 0">退货信息</h3>
                    <table border="0" cellpadding="0" cellspacing="1" class="messTable">
                        <tr>
                            <td>调拨类型</td>
                            <td>{{ formDict(allocationType, transferType) || '--' }}</td>
                        </tr>
                        <tr>
                            <td>合计调拨数量</td>
                            <td>{{ form.num || '--' }}</td>
                        </tr>
                        <tr>
                            <td>合计调拨差价</td>
                            <td>{{ form.cost || '--' }}</td>
                        </tr>
                        <tr>
                            <td>出货经手人</td>
                            <td>{{ form.shipmentByEn || '--' }}</td>
                        </tr>
                        <tr>
                            <td>出货库号</td>
                            <td>{{ form.shipmentWarehouseNoEn || '--' }}</td>
                        </tr>
                        <tr>
                            <td>入货经手人</td>
                            <td>{{ form.incomingByEn || '--' }}</td>
                        </tr>
                        <tr>
                            <td>入货库号</td>
                            <td>{{ form.incomingWarehouseNoEn || '--' }}</td>
                        </tr>
                        <tr>
                            <td>备注</td>
                            <td>{{ form.remark || '--' }}</td>
                        </tr>
                    </table>
                    <p style="color: red;margin: 40px 2% 0 4%;text-align: left">
                        请检查以上信息是否填写准确，
                        若信息准确，请点击“确定”发起“调拨申请，
                        若需修改，请点击“上一步”进行修改。</p>


                </div>
            </div>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button @click="next">{{ textBtn1 }}</el-button>
                    <el-button type="primary" @click="detailSubmit" v-if="modalType == 'detail' && activeName==='1'&& detailData.confirmStatus === '0'">确认</el-button>
                    <el-button type="primary" @click="prev" v-if="modalType != 'detail'">{{ textBtn2 }}</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, watch, defineExpose, onMounted } from 'vue'
import outboundRecordService from '@/api/erp/storage/erpInventoryService'
import { ElMessageBox, ElMessage } from "element-plus";
import moment from 'moment'
import { ClickOutside as vClickOutside } from 'element-plus'
import Table from "@/components/assist/massRange/Table.vue";
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import purchasingManagement from '@/api/erp/purchasingManagement'
import tool from '@/utils/tool';
import transferApplication from '@/api/erp/transferApplication'
import inboundRecordService from '@/api/erp/procure/erpPurchaseWarehousingRecordService'
const Tofixed_ = (value, num) => {
    if (value) {
        return parseFloat(value)?.toFixed(num)
    } else {
        return false
    }
}
const { proxy } = getCurrentInstance();
const loading = ref(false);
const active = ref(1)
const textBtn1 = ref('取消')
const textBtn2 = ref('下一步')
const step1_list = ref([])
const step1_list_Total = ref(0)
const form = ref({})
const transferType = ref('1') // 调拨类型
const optionList = ref([])
const roleList = ref({})
const supplierList = ref([])
const allocationType = ref([])
const choose_setp1 = ref([])
const step2_detail = ref({})
const warehouseNumber = ref([])
const warehouseNumberOut = ref([])
const optionListOut = ref([])
const reviewStatus = ref([])
const warehouseNumber_step1 = ref([])
const transferConfirmationStatus = ref([])
const creatform = ref({
    
})
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
        creatorId: tool.data?.get("ROLE_LIST")?.length && tool.data?.get("ROLE_LIST")?.[0]?.enName == 'ywjl' ? tool.data?.get("USER_INFO")?.id : undefined
    },
    rules: {
        creatorId: [{ required: true, message: "经手人不能为空", trigger: "change" }],
        warehouseNumberId: [{ required: true, message: "库号不能为空", trigger: "change" }],
    },
    formRules: {
        incomingBy: [{ required: true, message: "入货经手人不能为空", trigger: "change" }],
        incomingWarehouseNo: [{ required: true, message: "入货库号不能为空", trigger: "change" }],
        // shipmentBy: [{ required: true, message: "出货经手人不能为空", trigger: "change" }],
        // shipmentWarehouseNo: [{ required: true, message: "出货库号不能为空", trigger: "change" }],
    },
    creatRules:{
        confirmStatus:[{ required: true, message: "确认结果不能为空", trigger: "change" }],
        reason:[{ required: true, message: "拒绝原因不能为空", trigger: "blur" }],
    }
});

const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    beforClose: {
        type: Function,
        default: () => { }
    },
    title: {
        type: String,
        default: ''
    },
    modalType: {
        type: String,
        default: ""
    },
    getList: {
        type: Function,
        default: () => { }
    },
    detailData: {
        type: Object,
        default: () => { { } }
    },
    activeName: {
        type: String,
        default: ''
    }
})
const detailFlag = ref(false)
const entrust_out_table_listRef = ref(null)
const { open, beforClose, title, modalType, getList, detailData, activeName } = toRefs(props)
const { queryParams, rules, formRules,creatRules } = toRefs(data);
const formDict = (data, val) => {
    if (!data?.length || !val) return '--'
    return proxy.selectDictLabel(data, val)
}
const handleInputEdit = (scope, type) => {
    if (modalType.value == 'detail') return
    scope.row[`isShow${type}`] = true
}
const detailGoods = (row) => {
    detailFlag.value = true
    step2_detail.value = row
}
const handleClickOutside = (scope, key) => {
    scope.row[key] = false
}
const detailSubmit = () => {
    proxy.$refs["creatformRef"].validate(valid => {
        if (valid) {
            loading.value = true
            transferApplication.confirm({id:detailData.value.id,...creatform.value}).then(res=>{
                if(res.code == 200){
                    proxy.msgSuccess('操作成功')
                    beforClose.value()
                    getList.value()
                }else{
                    proxy.msgError(res?.msg) 
                }
                loading.value = false
            }).finally(()=>{
                loading.value = false
            })  
        }
    }) 
}
const step1_getList = () => {
    proxy.$refs["queryRef"].validate(valid => {
        if (valid) {
            loading.value = true
            const params = { ...queryParams.value }
            inboundRecordService.list(params).then(res => {
                if (res.code == 200) {
                    step1_list.value = res.data.records
                    step1_list_Total.value = res.data.total
                    loading.value = false
                }
            })
        }
    })

}
const handleSelectionChange_step1_list = (key, vvv) => {
    choose_setp1.value = key

}
const handleIncomingBy_step1 = value => {
    queryParams.value.warehouseNumberId = undefined
    warehouseNumberManagement.getList({ handledBy: value }).then(res => {
        if (res.code == 200) {
            warehouseNumber_step1.value = res.data.records
            if (res.data?.records?.length == 1) {
                queryParams.value.warehouseNumberId = warehouseNumber_step1.value[0].id
            }
        }
    })
}
const handleIncomingBy = value => {
    form.value.incomingWarehouseNo = undefined
    form.value.incomingByEn = optionList.value.filter(item => item.id == value)?.[0]?.name
    getWarehouseNumberManagement({ handledBy: value })
}
const getWarehouseNumberManagement = (value) => {
    warehouseNumberManagement.getList(value).then(res => {
        if (res.code == 200) {
            warehouseNumber.value = res.data.records
            if (res.data?.records?.length == 1) {
                form.value.incomingWarehouseNo = warehouseNumber.value[0].id
                form.value.incomingWarehouseNoEn = warehouseNumber.value[0]?.warehouseNumber
            }
        }
    })
}
const handleIncomingWarehouseNo = (value) => {
    if (value === form.value.shipmentWarehouseNo && form.value.incomingBy === form.value.shipmentBy) {
        form.value.incomingWarehouseNo = undefined
        proxy.msgError("同一经手人，入货库号和出货库号不能一样！")
    } else {
        form.value.incomingWarehouseNoEn = warehouseNumber.value.filter(item => item.id == value)?.[0].warehouseNumber
    }

}
const regxNum = () => {
    let index = 0
    for (let i = 0; i < choose_setp1.value.length; i++) {
        if (!choose_setp1.value[i]?.transferQuantity) {
            proxy.msgError(`请输入商品名称为${choose_setp1.value[i]?.commodity?.tradeName}的调拨数量！`)
            break
        } else if (transferType.value == '2' && !choose_setp1.value[i]?.transferUnitPrice) {
            proxy.msgError(`请输入商品名称为${choose_setp1.value[i]?.commodity?.tradeName}的调拨单价！`)
            break
        } else {
            index++
        }
    }
    return index
}
const calculateTotal = () => {
    form.value.cost = 0
    choose_setp1.value.forEach(item => {
        form.value.num = (isNaN(Number(form.value.num)) ? 0 : Number(form.value.num)) + Number(item.transferQuantity)
        if (transferType.value == '2') {
            let cost1 = Number(item.transferQuantity) * (isNaN(Number(item.transferUnitPrice)) ? 0 : Number(item.transferUnitPrice))
            let cost2 = Number(item.transferQuantity) * (isNaN(Number(item.unitPrice)) ? 0 : Number(item.unitPrice))
            form.value.cost = (Number(form.value.cost) + (cost1 - cost2)).toFixed(2)
        } else {
            form.value.cost = '0.00'
        }
    })

}
const getDetail = () => {
    transferApplication.getIdOrder({ id: detailData.value.id }).then(res => {
        if (res.code == 200) {
            step1_list.value = res.data?.transferFormDtos
            form.value = res?.data.transferDto
        } else {
            proxy.msgError(res.msg)
        }
    })
}
const next = () => {
    if (active.value == 1) {
        close()
        return
    }
    if (active.value == 2) {
        active.value = 1
        textBtn1.value = '取消'
        textBtn2.value = '下一步'
        entrust_out_table_listRef.value?.clearSelection()
        return
    }
    if (active.value == 3) {
        active.value = 2
        textBtn1.value = '上一步'
        textBtn2.value = '下一步'
        return
    }
}
const prev = () => {
    if (active.value == 1) {
        if (!choose_setp1.value.length) {
            proxy.msgError('请至少选择一条商品')
            return
        } else {
            active.value = 2
            textBtn1.value = '上一步'
            textBtn2.value = '下一步'
            form.value.shipmentBy = queryParams.value.creatorId
            form.value.shipmentWarehouseNo = queryParams.value.warehouseNumberId
            form.value.shipmentByEn = optionList.value.filter(v => v.id === queryParams.value.creatorId)?.[0]?.name
            form.value.shipmentWarehouseNoEn = warehouseNumber_step1.value.filter(v => v.id === queryParams.value.warehouseNumberId)?.[0]?.warehouseNumber
            return
        }

    }
    if (active.value == 2) {
        proxy.$refs["queryRef_setp2"].validate(valid => {
            if (valid) {
                if (regxNum() !== choose_setp1.value?.length) return
                calculateTotal()
                active.value = 3
                textBtn1.value = '上一步'
                textBtn2.value = '确认'
                return
            }
        })
    }
    if (active.value == 3) {
        loading.value = true
        textBtn1.value = '上一步'
        const transferDto = {}
        transferDto.id = form.value?.id
        transferDto.transferType = transferType.value
        transferDto.shipmentBy = form.value?.shipmentBy
        transferDto.handledBy = {
            id: queryParams.value.creatorId
        }
        transferDto.shipmentWarehouseNo = {
            id: form.value?.shipmentWarehouseNo
        }
        transferDto.incomingBy = form.value?.incomingBy
        transferDto.incomingWarehouseNo = {
            id: form.value?.incomingWarehouseNo
        }
        transferDto.transferQuantity = form.value?.transferQuantity
        transferDto.remark = form.value?.remark
        transferDto.transferQuantity = form.value?.cost
        transferDto.transferAmount = form.value?.num
        const transferFormDtos = []
        choose_setp1.value.forEach(item => {
            const itemData = {
                // id: item?.id,
                purchaseOrderCode: item.orderCode,
                commodity: { id: item.commodity?.id },
                commodityName: item.commodityName,
                commoditySelfCode: item.commoditySelfCode,
                commodityPackageSpecification: item.commodity.packageSpecification,
                commodityManufactureName: item.commodity.manufactureName,
                commodityOriginPlace: item.commodity.producingArea,
                commodityProduceDate: item.proDate,
                commodityValidityTime: item.valiDate,
                commodityBasicUnit: item.commodity.basicUnit,
                commodityCode: item.commodityCode,
                // supplier: item.suppliers,
                supplier: {
                    id: item.supplierId
                },
                batchNumber: item.lotNo,
                commodityUnitPrice: item.unitPrice,
                commodityAmount: Number(item.unitPrice || 0) * Number(item.availableInventory || 0)?.toFixed(2),
                transferQuantity: item.transferQuantity,
                transferUnitPrice: transferType.value == '1' ? item.unitPrice : item.transferUnitPrice,
                currenttinventory: item?.availableInventory

            }
            transferFormDtos.push(itemData)
        })
        transferApplication.save({
            transferDto,
            transferFormDtos,
            formType: 'save'
        }).then(res => {
            if (res.code == 200) {
                proxy.msgSuccess('操作成功')
                beforClose.value()
                choose_setp1.value = []
                step1_list.value = []
                step1_list_Total.value = 0
                loading.value = false
                active.value = 1
                getList.value()
            } else {
                loading.value = false
                proxy.msgError(res.msg)
            }
        }).catch(() => {
            loading.value = false
        }).finally(() => {
            loading.value = false
        })

    }
}
const close = () => {
    if (modalType.value == 'detail') {
        beforClose.value()
    } else {
        ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
            .then(() => {
                beforClose.value()
            })
            .catch(() => {

            });
    }


}
const getUsers = () => {
    warehouseNumberManagement.getUser({ size: 9999, current: 1, status: '1' }).then(res => {
        if (res.code == 200) {
            optionList.value = res.data?.records
        }

    })
}
/**
 * @description: 获取供应商
 * @return {*}
 */
const getSupplier = () => {
    purchasingManagement.getSupplierProduction({ status: 3, customLabel: 2, size: 9999, current: 1 }).then(res => {
        if (res.code == 200) {
            supplierList.value = res.data?.records
        }
    })
}
async function dict() {
    allocationType.value = await proxy.getDictList('allocation_type')
    reviewStatus.value = await proxy.getDictList('erp_review_status')
    transferConfirmationStatus.value = await proxy.getDictList('transfer_confirmation_status')
}
onMounted(() => {
    loading.value = true
    dict()
    if (modalType.value == 'detail') {
        getDetail()
    } else {
        getUsers()
        getSupplier()
        roleList.value = tool.data.get("ROLE_LIST")
        optionListOut.value = [tool.data.get("USER_INFO")]
        warehouseNumberManagement.getList({ handledBy: form.value.shipmentBy }).then(res => {
            if (res.code == 200) {
                warehouseNumberOut.value = res.data.records
                if (res.data?.records?.length == 1) {
                    form.value.shipmentWarehouseNo = warehouseNumberOut.value[0].id
                    form.value.shipmentWarehouseNoEn = warehouseNumberOut.value[0]?.warehouseNumber
                }
            }
        })
    }
    loading.value = false
})

defineExpose({

})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.rowStyle {
    .el-col {
        margin-top: 20px;
        font-size: 15px;

        .rowTitle {
            width: 120px;
            text-align: right;
            display: inline-block;
            font-size: 15px;
            font-weight: bolder;
            color: #000;
        }

        .rowMess {
            color: #4d4d4d;
            font-weight: 600;
        }

        .rowRed {
            color: red;
        }
    }
}

.total {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    margin: 0px 20px;

    p {
        margin-right: 50px;
        margin-top: 20px;

        & span:nth-of-type(1) {
            font-size: 15px;
            font-weight: bold;
            color: #333;
            display: inline-block;
            width: 130px;
            text-align: right;
        }

        .red {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}

.box_date {
    width: 220px;
}

.step {
    margin-bottom: 30px;
}

.step1_search_btn {
    width: 60px;
    margin-left: 50px;
}

h3 {
    color: black;
}

.detailTable {
    width: 100%;
    background-color: #eaedf3;
    font-size: 14px;
    border-radius: 5px;

    tr {
        height: 40px;

        td {
            background-color: white;
        }

        td:nth-child(1) {
            padding: 0 10px;
            font-weight: bold;
            width: 20%;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            width: 80%;
            color: #606266;
            padding: 0 10px;
        }
    }

}

.messTable {
    width: 100%;
    background-color: #eaedf3;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    padding: 1px 1px 0 1px;

    tr {
        margin-bottom: 1px;
        display: flex;

        td {
            background-color: white;
            line-height: 40px;
        }

        td:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2
        }
    }
}

.titleH4 {
    margin-bottom: 20px;
    color: #000;
    font-weight: bolder;
    font-size: 15px;
}

.transferType {
    line-height: 42px;

    span {
        color: #333;
        font-size: 14px;
        font-weight: bold;
    }
}
</style>
