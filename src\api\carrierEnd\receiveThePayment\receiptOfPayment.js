import request from '@/utils/request';
export default {
    // 到款认款列表查询
    getClaimApplyList: function (params) {
        return request.get('/tms/arrive/claimApply/list', params);
    },
    // 到款认款列表导出
    exportClaimApply: function (params, config, resDetail) {
        return request.post('/tms/arrive/claimApply/exportList', {}, config, resDetail,params);
    },
    // 认款时查询未完成认款的支付申请
    getPaymentApply: function (params) {
        return request.get('/tms/advancepayment/apply/claim/getPaymentApply', params);
    },
    // 认款申请提交
    submitApply: function (data) {
        return request.post('/tms/arrive/claimApply/submitApply', data);
    },
    // 认款申请-详情
    queryById: function (params) {
        return request.get('/tms/arrive/claimApply/queryById', params);
    },
    // 认款申请-删除
    delClaimApply: function (params) {
        return request.delete('/tms/arrive/claimApply/delete', params);
    },
    // 认款申请-审批进度
    getAuditRecord: function (params) {
        return request.get('/tms/arrive/claimApply/getAuditRecord', params);
    },
    // 统计合计金额
    getTotalAmount: function (params) {
        return request.get('/tms/arrive/claimApply/getAmount', params);
    },
};
