<template>
    <div v-loading="loadingUpload" class="app-container">
        <el-form ref="sendPartyFormRef" :model="sendPartyForm" :rules="sendPartyFormRules" label-width="auto" class="batch-order-form" @submit.prevent>
            <el-card class="form-card basic-info-card" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <div class="titleLayout">
                            <span class="verticalBar"></span>
                            <span class="title">基本信息</span>
                        </div>
                    </div>
                </template>
                <div class="box-top-form">
                    <el-form-item class="mb0" label="货主" prop="companyId">
                        <el-select v-model="sendPartyForm.companyId" clearable filterable placeholder="请选择货主" @change="selectTheShipper">
                            <el-option v-for="item in ownerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="mb0" label="网点" prop="sendBranch">
                        <el-select v-model="sendPartyForm.sendBranch" clearable filterable placeholder="请选择网点">
                            <el-option v-for="(item, index) in branchData" :key="index" :label="item.branchName" :value="item.branchCode"> </el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </el-card>
            <el-card :disabled="belongUserId == undefined || belongUserId == ''" class="form-card sender-info-card" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <div class="titleLayout flex-r">
                            <div class="d-flex">
                                <span class="verticalBar"></span>
                                <span class="title">发货方信息</span>
                            </div>
                            <el-popover v-if="sendPartyForm.companyId" v-model:visible="sendPartyForm.isShow" :width="800" placement="top" trigger="click" @show="isExistCompanyIds">
                                <div v-if="sendPartyForm.isShow">
                                    <el-form>
                                        <el-input v-model="sendAddressParams.searchValue" clearable placeholder="输入关键字搜索" size="mini" @change="AddressBook()" />
                                    </el-form>
                                    <el-table :data="sendAddressBook" height="300" size="mini" style="width: 780px" @row-click="getSendAddress">
                                        <el-table-column align="center" label="姓名" prop="user" width="110px" />
                                        <el-table-column align="center" label="公司" prop="company" width="200px" />
                                        <el-table-column align="center" label="电话" prop="phone" width="110px" />
                                        <el-table-column align="center" label="地址" prop="address" width="350px" />
                                    </el-table>
                                    <pagination v-show="sendAddressParams.total > 0" v-model:limit="sendAddressParams.size" v-model:page="sendAddressParams.current" :total="sendAddressParams.total" style="margin-bottom: 0" @pagination="AddressBook" />
                                </div>
                                <template #reference>
                                    <el-button class="address-book-btn" round size="medium" type="primary">
                                        <i class="el-icon-notebook-1"></i>
                                        地址簿
                                    </el-button>
                                </template>
                            </el-popover>
                        </div>
                    </div>
                </template>

                <div class="box-shipper-responsive">
                    <el-form-item label="姓名" prop="sendUser">
                        <el-input v-model="sendPartyForm.sendUser" clearable maxlength="20" placeholder="请输入发件人姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="电话" prop="sendUserPhone">
                        <el-input v-model="sendPartyForm.sendUserPhone" clearable placeholder="请输入联系电话"></el-input>
                    </el-form-item>
                    <el-form-item label="公司" prop="sendCompany">
                        <el-input v-model="sendPartyForm.sendCompany" clearable maxlength="60" placeholder="请输入公司"></el-input>
                    </el-form-item>
                    <el-form-item label="发件地址" prop="sendPCC">
                        <el-cascader v-model="sendPartyForm.sendPCC" :options="sysAreas" clearable filterable placeholder="请选择省市区" style="width: 100%" @change="formChangeHandle($event, 'sendPCC')" @visible-change="visibleChange" />
                    </el-form-item>
                    <el-form-item label="详细地址" prop="sendAddress">
                        <el-input v-model="sendPartyForm.sendAddress" clearable maxlength="200" placeholder="请输入详细地址" show-word-limit></el-input>
                    </el-form-item>
                </div>
            </el-card>

            <el-card class="form-card batch-processing-card" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <div class="titleLayout">
                            <span class="verticalBar"></span>
                            <span class="title">EXCEL批量处理</span>
                        </div>
                    </div>
                </template>
                <div class="batch-processing-content">
                    <div class="status-section">
                        <div class="status-info">
                            <div v-if="responseData.analysis" class="status-display">
                                <div class="status-item success">
                                    <i class="el-icon-success"></i>
                                    <span class="label">成功：</span>
                                    <span v-if="responseData.analysis.successData" class="count">{{ responseData.analysis.successData.length }}</span>
                                    <span v-else class="count">0</span>
                                </div>
                                <div class="status-item error">
                                    <i class="el-icon-error"></i>
                                    <span class="label">失败：</span>
                                    <span v-if="responseData.analysis.errorData" class="count">{{ responseData.analysis.errorData.length }}</span>
                                    <span v-else class="count">0</span>
                                </div>
                            </div>
                            <div v-else class="status-display">
                                <div class="status-item success">
                                    <i class="el-icon-success"></i>
                                    <span class="label">成功：</span>
                                    <span class="count">0</span>
                                </div>
                                <div class="status-item error">
                                    <i class="el-icon-error"></i>
                                    <span class="label">失败：</span>
                                    <span class="count">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="action-section">
                        <el-button class="action-btn download-btn" icon="el-icon-download" size="medium" type="primary" @click="downloadTemplate">
                            下载模板
                        </el-button>
                        <el-upload
                            v-if="sendPartyForm.companyId && sendPartyForm.sendBranch"
                            ref="uploadOrderDetailExcel"
                            :action="uploadFileUrl"
                            :before-upload="beforeUploadExcel"
                            :data="{ companyId: sendPartyForm.companyId, type: '2' }"
                            :headers="headers"
                            :limit="1"
                            :on-error="error"
                            :on-progress="progress"
                            :on-success="fileUploadSuccess"
                            :show-file-list="false"
                            accept=".xls,.xlsx"
                        >
                            <el-button class="action-btn upload-btn" icon="el-icon-upload" size="medium" type="warning">
                                上传文件
                            </el-button>
                        </el-upload>
                        <el-button v-if="responseData.analysis" class="action-btn clear-btn" icon="el-icon-delete" size="medium" type="danger" @click="clearFileList">
                            清空列表
                        </el-button>
                    </div>
                </div>
                <div v-if="responseData.analysis">
                    <div v-if="responseData.analysis.failureMsg" v-html="responseData.analysis.failureMsg"></div>
                </div>
                <div class="table-container">
                    <el-table v-loading="checkDataLoading" :data="bulkOrderList.orderList" border class="responsive-table">
                        <el-table-column align="center" label="行号" type="index" width="60" />
                        <el-table-column :show-overflow-tooltip="true" align="center" label="收货方姓名" prop="receiverUser" min-width="120" />
                        <el-table-column align="center" label="件数" prop="goodsPackages" width="80" />
                        <el-table-column :formatter="(row) => formDict(collectionMethod, row.orderType)" :show-overflow-tooltip="true" align="center" label="货物揽收方式" prop="orderType" min-width="140" />
                        <el-table-column :formatter="(row) => formDict(fourplProductTypeList, row.productType)" :show-overflow-tooltip="true" align="center" label="产品类型" prop="productType" min-width="120" />
                        <el-table-column :show-overflow-tooltip="true" align="center" label="随货同行单号" prop="externalOrderNo" min-width="150" />
                        <el-table-column :formatter="(row) => formDict(settlementMethodList, row.paymentMethod)" :show-overflow-tooltip="true" align="center" label="付款方式" prop="paymentMethod" min-width="120" />
                        <el-table-column align="center" label="冷链明细" width="100">
                            <template #default="scope">
                                <el-popover effect="light" placement="top" trigger="hover" width="auto">
                                    <template #default>
                                        <el-table :data="scope.row.orderDetailList" height="200" size="mini" style="width: 600px">
                                            <el-table-column :show-overflow-tooltip="true" align="center" label="通用名称" prop="name" />
                                            <el-table-column :show-overflow-tooltip="true" align="center" label="规格/型号" prop="specifications" />
                                            <el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" prop="manufacturer" />
                                            <el-table-column :show-overflow-tooltip="true" align="center" label="单位" prop="basicUnit" />
                                            <el-table-column align="center" label="数量" prop="quantity" />
                                            <el-table-column :show-overflow-tooltip="true" align="center" min-width="100" label="批号/序列号" prop="batchNumber" />
                                            <el-table-column :show-overflow-tooltip="true" align="center" min-width="160" label="使用期限/失效日期" prop="validityDate" />
                                            <el-table-column :show-overflow-tooltip="true" align="center" min-width="200" label="批准文号/注册证号/备案证号" prop="registrationNumber" />
                                            <el-table-column :show-overflow-tooltip="true" align="center" min-width="200" label="上市许可持有人/注册人/备案人" prop="listPermitHolder" />
                                            <el-table-column align="center" label="资料是否齐全" prop="completeInformation" >
                                                <template #default="scope">{{scope.row.completeInformation=='1'?'是':'否'}}</template>
                                            </el-table-column>
                                        </el-table>
                                    </template>
                                    <template #reference>
                                        <el-button :disabled="scope.row.orderDetailList.length > 0" size="mini" type="text">查看</el-button>
                                    </template>
                                </el-popover>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <span v-if="responseData.analysis" style="color: #ff2a2a; font-size: 12px; font-weight: bold">注：本次批量下单将生成 {{ responseData.list.length }} 单，请耐心等待</span>
                <span v-if="!responseData.analysis" style="color: #ff2a2a; font-size: 12px; font-weight: bold">注：本次批量下单将生成 0 单，请耐心等待</span>
            </el-card>

            <!-- 操作区域 -->
            <div class="action-footer">
                <div class="agreement-section">
                    <el-form-item prop="agreeToTermsCarrier">
                        <el-checkbox v-model="sendPartyForm.agreeToTermsCarrier" class="agreement-checkbox">
                            <span class="agreement-text">
                                阅读并同意
                                <el-button class="agreement-link" type="text" @click="agreement">《违禁品协议》</el-button>
                            </span>
                        </el-checkbox>
                    </el-form-item>
                </div>
                <div class="submit-section">
                    <el-form-item>
                        <el-button class="submit-btn" round size="large" type="primary" @click="placeOrder">
                            <i class="el-icon-check"></i>
                            立即下单
                        </el-button>
                    </el-form-item>
                </div>
            </div>
        </el-form>

        <!-- 下单进度 -->
        <!-- <el-dialog :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true" :title="title"
            :visible.sync="placeOrderProgressDialog" append-to-body center width="30%">
            <el-card shadow="never">
                <div v-if="loading" v-loading="loading" element-loading-text="批量下单中" style="height: 120px"></div>
                <div v-if="!loading" class="box-orderProgress">
                    <div>
                        <span>成功：</span>
                        <span style="color: #5670fe">{{ numberOfSuccesses || 0 }}</span>
                    </div>
                    <div>
                        <span>失败：</span>
                        <span style="color: #ff2a2a">{{ numberOfFailures || 0 }}</span>
                    </div>
                </div>
                <el-divider v-if="!loading"></el-divider>
                <div v-if="!loading" class="box-period">
                    <el-scrollbar>
                        <div v-if="listOfFailures" class="box-list">
                            <div v-for="item in listOfFailures">{{ item }}</div>
                        </div>
                        <div v-if="successList" class="box-list">
                            <div v-for="item in successList" :key="item">{{ item }}</div>
                        </div>
                    </el-scrollbar>
                </div>
            </el-card>
            <div style="display: flex; justify-content: center; margin-top: 20px">
                <el-button @click="placeOrderProgressDialog = false">取 消</el-button>
                <el-button type="primary" @click="checkOrder">查看订单</el-button>
            </div>
        </el-dialog> -->

        <!--s 违禁品协议组件  -->
        <el-dialog v-model="agreementView" append-to-body center title="违禁品协议" top="0" width="40%">
            <el-image :src="require('@/assets/images/Security-protocols.jpg')" style="width: 100%; height: 100%" />
            <div style="display: flex; justify-content: center">
                <el-button size="small" @click="agreementView = false">关闭</el-button>
                <el-button size="small" type="primary" @click="agreeToTheAgreement()">同意</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import tool from '@/utils/tool';
import batchOrderPlacementApi from '@/api/logisticsManagement/BatchOrderPlacement';
import { selectDictLabel } from '@/utils/dictLabel';

export default {
    name: 'BatchOrderingAtTheCarrierEnd',
    data() {
        return {
            sendPartyForm: {
                isShow: false,
                sendUser: '',
                sendUserPhone: '',
                sendAddress: '',
                sendCompany: '',
                sendPCC: [],
                agreeToTermsCarrier: [],
                companyId: '', // 货主
                sendBranch: '' //网点
            },
            sendPartyFormRules: {
                sendUser: [{ required: true, message: '请输入收货人姓名', trigger: 'change' }],
                sendUserPhone: [
                    { required: true, message: '请输入收货人电话', trigger: 'change' },
                    { type: 'string', pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/, message: '请输入正确的电话', trigger: 'change' }
                ],
                sendAddress: [
                    { required: true, message: '请输入收货人地址', trigger: 'change' },
                    { max: 200, message: '最多输入200个字符', trigger: 'change' }
                ],
                sendCompany: [{ required: true, message: '请输入收货人公司', trigger: 'change' }],
                sendPCC: [{ required: true, message: '请选择收货人省市区', trigger: 'change' }],
                companyId: [{ required: true, message: '请选择货主', trigger: 'change' }],
                sendBranch: [{ required: true, message: '请选择网点', trigger: 'change' }]
            },
            // 取件地址
            sendAddressParams: {
                total: 0,
                type: 1,
                pageNum: 1,
                pageSize: 10,
                searchValue: '',
                companyId: null
            },
            sendAddressBook: [],
            orderFile: {
                name: '暂无文件，请上传！',
                url: ''
            },
            uploadFileUrl: process.env.VUE_APP_API_GAENT + '/tms/orderDrug/importOrder', //上传
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                clientType: 'pc'
            },
            checkButtonDisabled: false,
            belongUserId: '',
            userSelectLoading: false,
            tmsCustomerList: [],
            isAdmin: false,
            responseData: {},
            checkDataLoading: false,
            sysAreas: [],
            title: '批量下单',
            loading: false,
            listOfFailures: [],
            successList: [],
            numberOfFailures: 0,
            numberOfSuccesses: 0,
            ownerList: [], // 货主列表
            productTypeDicts: [], //产品类型4PL
            fourplPaymentMethodOptions: [], // 付款方式
            collectionMethod: [], // 货物揽收方式
            open: false,
            agreementView: false,
            branchData: [], //网点列表
            bulkOrderList: [],
            loadingUpload: false
        };
    },
    watch: {
        'sendAddressParams.searchValue'() {
            this.getSendAddressBooks(true);
        }
    },
    created() {
        this.visibleChange();
        // 获取货主
        this.getCompanySelect();
        this.getDict();
    },
    methods: {
        // 网点
        branchList(val) {
            batchOrderPlacementApi
                .branchList({
                    companyId: val,
                    size: -1,
                    current: 0
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.branchData = res.data;
                        this.sendPartyForm.sendBranch = this.branchData[0].branchCode;
                    }
                });
        },
        // 跳转订单列表
        checkOrder() {
            // 跳转到订单列表
            this.$router.push({
                name: 'OrderList'
            });
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        },
        // 获取货主下拉
        getCompanySelect() {
            batchOrderPlacementApi.cooperateSelect({ status: '1' }).then((res) => {
                if (res.code == 200) {
                    this.ownerList = res.data;
                }
            });
        },
        // 查询地址薄
        isExistCompanyIds() {
            if (!this.sendAddressParams.companyId) {
                this.sendPartyForm.isShow = false;
            } else {
                this.sendPartyForm.isShow = true;
            }
        },
        AddressBook() {
            this.sendAddressParams.queryType = 1;
            if (this.isAdmin) {
                this.sendAddressParams.createUserId = this.belongUserId;
                this.tmsCustomerList.forEach((t) => {
                    if (t.userId == this.belongUserId) {
                        this.belongUserName = t.userName;
                    }
                });
                this.sendPartyForm = {
                    sendUser: '',
                    sendUserPhone: '',
                    sendAddress: '',
                    sendCompany: '',
                    sendPCC: []
                };
            }
            batchOrderPlacementApi.addressBookList(this.sendAddressParams).then((res) => {
                if (res.code == 200) {
                    this.sendAddressBook = res.data.records;
                    var arr2 = this.sendAddressBook.filter((item) => {
                        return item.isDefaults == true;
                    });
                    if (arr2.length > 0) {
                        this.getSendAddress(arr2[0]);
                    }
                    this.sendAddressParams.total = res.data.total;
                }
            });
        },
        // 订单-批量下单模板下载
        downloadTemplate() {
            batchOrderPlacementApi
                .downloadTemplate('', '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '批量下单导入模版.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                    } else {
                        this.$message.error('导出异常请联系管理员');
                    }
                })
                .catch((err) => {
                    this.$message.error(err.msg);
                });
        },

        // 上传文件
        beforeUploadExcel(file) {
            if (this.sendPartyForm.companyId == '') {
                this.msgError('请选择货主');
                return false;
            }
            // 限制文件类型 xls、xlsx
            const fileType = file.type;
            const fileTypeArr = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
            const isFileType = fileTypeArr.includes(fileType);
            if (!isFileType) {
                this.msgError('请上传xls、xlsx格式的文件');
                return false;
            }
            // return true;
        },
        error(err) {
            this.loading = false;
            this.percentfalse;
            this.$notify.error({
                title: '上传文件未成功',
                message: err
            });
        },
        fileUploadSuccess(res) {
            this.checkDataLoading = false;
            this.responseData = res.data;
            this.bulkOrderList = this.responseData.analysis;
        },
        progress() {
            this.checkDataLoading = true;
        },

        // 同意协议
        agreeToTheAgreement() {
            this.sendPartyForm.agreeToTermsCarrier.push(false);
            this.agreementView = false;
        },
        // 打开协议
        agreement() {
            this.agreementView = true;
        },

        placeOrder() {
            this.$refs['sendPartyFormRef'].validate((valid) => {
                if (valid) {
                    // 协议是否同意
                    if (this.sendPartyForm.agreeToTermsCarrier.length == 0) {
                        this.msgError('请先阅读并同意服务协议和隐私政策');
                        return;
                    }

                    if (!this.responseData.analysis) {
                        this.msgError('无可下单数据');
                        return;
                    }
                    this.loadingUpload = true;
                    this.title = '批量下单';
                    this.loading = true;
                    let dataArr = [];
                    this.responseData.analysis.orderList.forEach((data) => {
                        data.belongUserName = this.belongUserName;
                        data.sendUser = this.sendPartyForm.sendUser;
                        data.sendUserPhone = this.sendPartyForm.sendUserPhone;
                        data.sendCompany = this.sendPartyForm.sendCompany;
                        data.sendBranch = this.sendPartyForm.sendBranch;
                        data.sendAddress = this.sendPartyForm.sendAddress;
                        data.sendTown = {
                            id: this.sendPartyForm.sendPCC[3]
                        };
                        data.sendProvinceId = this.sendPartyForm.sendPCC[0];
                        data.sendCityId = this.sendPartyForm.sendPCC[1];
                        data.sendCountyId = this.sendPartyForm.sendPCC[2];
                        data.belongUserId = null; // 订单所属人ID（租户/货主ID）
                        data.createUserId = null; // 下单人ID
                        dataArr.push(data);
                    });
                    batchOrderPlacementApi
                        .batchImportOrder({
                            orderList: dataArr,
                            type: 2
                        })
                        .then((res) => {
                            if (res.code == 200) {
                                this.loadingUpload = false;
                                this.msgSuccess('批量下单成功');
                                this.clearFileList();
                                // // 清空文件上传列表
                                this.$refs.uploadOrderDetailExcel.clearFiles();
                                // // 清空表单
                                this.$refs.sendPartyFormRef.resetFields();
                                this.responseData = {
                                    errorData: [],
                                    successData: [],
                                    rows: 0
                                };
                                // this.$router.push({ name: 'order' });
                            } else {
                                this.loadingUpload = false;
                                this.loading = false;
                                this.msgError(res.msg);
                            }
                        })
                        .catch(() => {
                            this.loadingUpload = false;
                            this.loading = false;
                        });
                }
            });
        },
        // 清空文件上传列表
        clearFileList() {
            this.responseData = { errorData: [], successData: [], rows: 0 };
            this.bulkOrderList = [];
            this.$refs.uploadOrderDetailExcel.clearFiles();
        },

        // 填写地址簿信息 取件
        getSendAddress(row) {
            const { user, phone, address, company, companyId, provinceId, cityId, countyId, town } = row;
            this.sendPartyForm.sendUser = user;
            this.sendPartyForm.sendUserPhone = phone;
            this.sendPartyForm.sendAddress = address;
            this.sendPartyForm.sendCompany = company;
            // this.sendPartyForm.companyId = companyId;
            this.sendPartyForm.sendPCC = [];
            this.sendPartyForm.sendPCC.push('' + provinceId + '');
            this.sendPartyForm.sendPCC.push('' + cityId + '');
            this.sendPartyForm.sendPCC.push('' + countyId + '');
            this.sendPartyForm.sendPCC.push('' + town.id + '');
            this.sendPartyForm.town = town;
            this.visibleChange();
            // // 提示地址填写完成
            this.msgSuccess('地址填写完成');
            this.$refs.sendPartyFormRef.clearValidate('sendPCC');
            this.sendPartyForm.isShow = false;
        },

        selectTheShipper(e) {
            this.branchList(e); //获取网点
            this.AddressBook(e); //获取地址簿
            this.sendAddressParams.companyId = e;
            // this.getSendAddressBooks();
            this.sendPartyForm.sendUser = '';
            this.sendPartyForm.sendUserPhone = '';
            this.sendPartyForm.sendAddress = '';
            this.sendPartyForm.sendCompany = '';
            this.sendPartyForm.sendPCC = [];
        },
        //字典回显
        formDict(data, val) {
            return selectDictLabel(data, val);
        },
        /**
         * 获取字典值
         */
        async getDict() {
            this.fourplProductTypeList = await this.getDictList('fourpl_product_type');
            this.collectionMethod = await this.getDictList('fourpl_mail_service');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    thead th {
        border-right: none !important;
    }

    .form-mb0 .el-form-item {
        margin-bottom: 4px;
        margin-top: 4px;
    }

    .el-drawer__header {
        margin-bottom: 20px;
    }

    label.el-radio {
        margin-right: 8px;
    }

    .el-button-group > .el-button:not(:last-child) {
        margin-right: 8px;
    }

    .el-tabs__header.is-top {
        margin-bottom: 0;
    }

    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .card-pb-10 .el-card__body {
        padding-bottom: 10px;
    }

    .el-dialog__header {
        background: none;

        span {
            color: #5c6b8a;
        }
    }

    .box-shipper .el-form-item .el-form-item__label {
        padding-bottom: 0;
    }

    .el-divider.el-divider--horizontal {
        margin: 10px 0;
        background-color: #e6ebf5;
    }

    .el-dialog__body {
        background-color: #f2f2f2;
    }
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.mb8 {
    margin-bottom: 8px !important;
}

.mb16 {
    margin-bottom: 16px;
}

.box-table-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.el-table .cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-table .warning-row {
    background-image: linear-gradient(to top, #e6e9f0 0%, #eef1f5 100%);
}

.p-16 {
    padding: 16px;
}

/* ==================== 标题样式优化 ==================== */
.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0;

    .verticalBar {
        display: inline-block;
        background-color: #409eff;
        width: 3px;
        height: 16px;
        margin-right: 8px;
        border-radius: 1px;
    }

    .title {
        color: #303133;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: normal;
    }
}

.form-card.basic-info-card .titleLayout .title {
    color: #303133;
}

.form-card.basic-info-card .titleLayout .verticalBar {
    background-color: #409eff;
}

.flex-r {
    justify-content: space-between;
}

/* ==================== 整体布局样式 ==================== */

.batch-order-form {
    width: 100%;
    margin: 0;
}

/* ==================== 卡片通用样式 ==================== */
.form-card {
    margin-bottom: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-card.basic-info-card {
    background: #ffffff;
    color: inherit;
}

.form-card.basic-info-card .el-form-item__label {
    color: #606266 !important;
    font-weight: normal;
}

.form-card.sender-info-card {
    border-left: none;
}

.form-card.batch-processing-card {
    border-left: none;
}

/* ==================== 卡片头部样式 ==================== */
.card-header {
    padding: 0;
    border-bottom: none;
}

.card-header .titleLayout {
    padding: 0;
}

.card-header .title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
}

.form-card.basic-info-card .card-header .title {
    color: #303133;
}

.d-flex {
    display: flex;
    align-items: center;
}

/* ==================== 表单布局样式 ==================== */
/* 上方表单布局 - 两个表单项各占50% */
.box-top-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    align-items: center;
    padding: 0;
}

.box-top-form .el-form-item {
    margin-bottom: 0;
}

.box-top-form .el-select {
    width: 100%;
}

/* 响应式：小屏幕时垂直排列 */
@media (max-width: 768px) {
    .box-top-form {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

/* ==================== 表单项样式优化 ==================== */
.el-form-item__label {
    font-weight: normal;
    color: #606266;
    line-height: 1.5;
}

.el-input__inner,
.el-select .el-input__inner,
.el-cascader .el-input__inner {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    transition: border-color 0.2s ease;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
}

.el-input__inner:focus,
.el-select .el-input__inner:focus,
.el-cascader .el-input__inner:focus {
    border-color: #409eff;
    outline: none;
}

.el-input__inner:hover,
.el-select .el-input__inner:hover,
.el-cascader .el-input__inner:hover {
    border-color: #c0c4cc;
}

/* ==================== 按钮样式优化 ==================== */
.address-book-btn {
    background-color: #409eff;
    border-color: #409eff;
    border-radius: 4px;
}

.address-book-btn:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
}

/* 发货方表单自适应布局 */
.box-shipper-responsive {
    display: grid;
    gap: 16px;
    /* 默认布局：姓名、电话、公司一行，地址相关占两行 */
    grid-template-columns: repeat(3, 1fr);
    grid-template-areas:
        "name phone company"
        "address address address"
        "detail detail detail";
}

.box-shipper-responsive .el-form-item:nth-child(1) { /* 姓名 */
    grid-area: name;
}

.box-shipper-responsive .el-form-item:nth-child(2) { /* 电话 */
    grid-area: phone;
}

.box-shipper-responsive .el-form-item:nth-child(3) { /* 公司 */
    grid-area: company;
}

.box-shipper-responsive .el-form-item:nth-child(4) { /* 发件地址 */
    grid-area: address;
}

.box-shipper-responsive .el-form-item:nth-child(5) { /* 详细地址 */
    grid-area: detail;
}

/* 大屏幕：优化布局 */
@media (min-width: 1200px) {
    .box-shipper-responsive {
        grid-template-columns: repeat(4, 1fr);
        grid-template-areas:
            "name phone company address"
            "detail detail detail detail";
    }
}

/* 中等屏幕：两列布局 */
@media (min-width: 768px) and (max-width: 1199px) {
    .box-shipper-responsive {
        grid-template-columns: repeat(2, 1fr);
        grid-template-areas:
            "name phone"
            "company address"
            "detail detail";
    }
}

/* 小屏幕：单列布局 */
@media (max-width: 767px) {
    .box-shipper-responsive {
        grid-template-columns: 1fr;
        grid-template-areas:
            "name"
            "phone"
            "company"
            "address"
            "detail";
        gap: 12px;
    }
}

/* 保留原有的box-shipper样式以防其他地方使用 */
.box-shipper {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1.5fr 1.5fr;
    gap: 16px;
}

/* ==================== 批量处理区域样式 ==================== */
.batch-processing-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 0;
}

.status-section {
    flex: 1;
    min-width: 200px;
}

.status-display {
    display: flex;
    gap: 20px;
    align-items: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-item .label {
    font-weight: normal;
    color: #606266;
    font-size: 14px;
}

.status-item.success .count {
    color: #67c23a;
    font-weight: 500;
}

.status-item.error .count {
    color: #f56c6c;
    font-weight: 500;
}

.status-item i {
    font-size: 14px;
    margin-right: 2px;
}

.status-item.success i {
    color: #67c23a;
}

.status-item.error i {
    color: #f56c6c;
}

.action-section {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.action-btn {
    height: 32px;
    padding: 0 15px;
    border-radius: 4px;
    font-weight: normal;
    font-size: 14px;
}

.download-btn {
    background-color: #409eff;
    border-color: #409eff;
}

.download-btn:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
}

.upload-btn {
    background-color: #e6a23c;
    border-color: #e6a23c;
}

.upload-btn:hover {
    background-color: #ebb563;
    border-color: #ebb563;
}

.clear-btn {
    background-color: #f56c6c;
    border-color: #f56c6c;
}

.clear-btn:hover {
    background-color: #f78989;
    border-color: #f78989;
}

/* 响应式：小屏幕时垂直排列 */
@media (max-width: 768px) {
    .batch-processing-content {
        flex-direction: column;
        align-items: stretch;
    }

    .status-display {
        justify-content: center;
        gap: 16px;
    }

    .action-section {
        justify-content: center;
    }
}

/* ==================== 底部操作区域样式 ==================== */
.action-footer {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px 20px;
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e4e7ed;
}

.agreement-section {
    flex: 1;
}

.agreement-checkbox {
    margin-bottom: 0;
}

.agreement-text {
    color: #606266;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.agreement-link {
    color: #409eff !important;
    font-weight: normal;
    text-decoration: none;
    padding: 0;
    font-size: 14px;
}

.agreement-link:hover {
    color: #66b1ff !important;
    text-decoration: underline;
}

.submit-section {
    margin-left: 20px;
}

.submit-btn {
    background-color: #67c23a;
    border-color: #67c23a;
    height: 36px;
    padding: 0 20px;
    font-size: 14px;
    font-weight: normal;
    border-radius: 4px;
}

.submit-btn:hover {
    background-color: #85ce61;
    border-color: #85ce61;
}

/* 响应式：小屏幕时垂直排列 */
@media (max-width: 768px) {
    .action-footer {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        padding: 16px;
    }

    .submit-section {
        margin-left: 0;
    }

    .submit-btn {
        width: 100%;
        max-width: 200px;
    }
}

.box-orderProgress {
    display: flex;
    gap: 16px;
    font-size: 16px;

    & > div {
        display: flex;
    }
}

.box-period {
    height: 140px;
    padding: 8px 0 0 8px;

    ::v-deep {
        .el-scrollbar {
            height: 100%;
        }

        .el-scrollbar__wrap {
            //overflow: scroll;
            width: 110%;
        }
    }

    .box-list {
        display: grid;
        grid-template-columns: 1fr;

        > div {
            height: 32px;
            line-height: 32px;
            padding-left: 16px;
        }

        > div:hover {
            color: #5670fe;
        }

        > div.active {
            color: #5670fe;
            font-weight: bold;
        }
    }
}

/* ==================== 表格样式优化 ==================== */
.table-container {
    margin: 16px 0;
    overflow-x: auto;
    border-radius: 4px;
}

.responsive-table {
    min-width: 800px;
    width: 100%;
}

.responsive-table .el-table__header th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 500;
    padding: 12px 8px;
}

.responsive-table .el-table__body td {
    padding: 12px 8px;
}

.responsive-table .el-button--text {
    color: #409eff;
    font-weight: normal;
}

.responsive-table .el-button--text:hover {
    color: #66b1ff;
}

/* 响应式：确保表格在小屏幕上可以水平滚动 */
@media (max-width: 1200px) {
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .responsive-table {
        min-width: 1000px;
    }
}

@media (max-width: 768px) {
    .table-container {
        margin: 12px 0;
    }

    .responsive-table {
        min-width: 800px;
        font-size: 12px;
    }

    .responsive-table .el-table__cell {
        padding: 8px 4px;
    }
}

/* ==================== 其他样式 ==================== */
.mb10 {
    margin-bottom: 16px;
}

.mb0 {
    margin-bottom: 0;
}

/* 分页样式 */
.el-pagination {
    text-align: center;
    margin-top: 16px;
}
</style>
