import http from "@/utils/request"
export default {
  productlist: function (params) {
    return http.get('/erp/product/erpCommodity/list', params)
  },
  searchProduct: function (params) {
    return http.get('/erp/product/erpCommodity/listBy3TJ', params)
  },
  customerList: function (params) {
    return http.get('/erp/customer/erpCustomers/list', params)
  },
  list: function (params) {
    return http.get('/erp/sales/rule/saleRule/list', params)
  },
  saveRule: function (params) {
    return http.post('/erp/sales/rule/saleRule/saveSalesRule', params)
  },
  saveCustomers: function (params) {
    return http.post('/erp/sales/rule/saleRule/saveSalesRuleCustomers', params)
  },
  saveAreas: function (params) {
    return http.post('/erp/sales/rule/saleRule/saveSalesRuleAreas', params)
  },
  saveCommoditys: function (params) {
    return http.post('/erp/sales/rule/saleRule/saveSalesRuleCommoditys', params)
  },
  delete: function (ids) {
    return http.delete('/erp/sales/rule/saleRule/deleteSalesRule', ids)
  },

  deleteCustomers: function (params) {
    return http.delete('/erp/sales/rule/saleRule/deleteRuleCustomer', params)
  },
  deleteAreas: function (params) {
    return http.delete('/erp/sales/rule/saleRule/deleteRuleArea', params)
  },
  deleteCommoditys: function (params) {
    return http.delete('/erp/sales/rule/saleRule/deleteRuleCommodity', params)
  },

  details: function (id) {
    return http.get('/erp/sales/rule/saleRule/findSalesRuleDetail', id)
  },
}