<template>
  <div>
    <div>
      <!-- 表头 -->
      <el-card class="box-card Botm">
        <TopTitle title="筛选">
        </TopTitle>
        <el-form ref="queryForm" :inline="true" :model="queryParams" class="form_130">
          <el-form-item label="商品类型" prop="type">
            <el-select v-model="queryParams.dictValue" class="form_225" placeholder="请选择商品类型">
              <el-option :label="item.name" :value="item.name" v-for=" item  in  diaSelectList " :key="item.id"
                @click="getDictValueid(item)" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchQuality">搜索</el-button>
            <el-button @click="resetQuery(queryForm)">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <!-- 表格部分 -->
      <!-- 按钮 -->
      <el-card style="margin:10px;">
        <TopTitle title="查询列表">
          <el-button type="primary" size="mini" @click="handlerCreat(creatform)" style="float: right;">新增</el-button>
        </TopTitle>
        <!-- 表格 -->
        <el-table :data="commodityConfigList" class="el-table" border>
          <el-table-column label="商品类型" align="left" prop="dictionaryValue" />
          <el-table-column align="left" label="质量范围" prop="commodityClassify.massName"/>
          <el-table-column label="创建时间" align="left" prop="createDate">
            <template #default="scope">
              {{ formatDate(scope.row.createDate) }}
            </template>
          </el-table-column>
          <el-table-column label="修改时间" align="left" prop="updateDate">
            <template #default="scope">
              {{ formatDate(scope.row.updateDate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)"><img src="@/assets/icons/update.png"
                                                                                 style="margin-right:5px"/>编辑
              </el-button>
              <el-button link type="danger" @click="handleDelete(scope.row)"><img src="@/assets/icons/delete.png"
                                                                                  style="margin-right:5px"/>删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="float: right;">
          <pagination :total="queryParams.total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
            @pagination="getList" />
        </div>
      </el-card>
      <el-dialog v-model="dialogFormVisible" :title="!state.boxFlag ? '添加商品分类配置' : '修改商品分类配置'" style="width:550px ;"
        :close-on-press-escape="false" :close-on-click-modal="false">
        <!-- 创建 -->
        <div v-show="!state.boxFlag">
          <el-form :model="form" label-position="right" ref="creatform" :rules="creatRules">
            <el-form-item label="商品类型" prop="dictValue">
              <el-select v-model="form.dictValue" placeholder="请选择商品类型" class="el-select">
                <el-option :label="item.name" :value="item.name" v-for=" item  in  diaSelectList " :key="item.id"
                  @click="getDictValueid(item)" />
              </el-select>
            </el-form-item>
            <el-form-item label="质量范围" prop="commodity">
              <el-tree-select v-model="form.commodity" :data="treedata" check-strictly :render-after-expand="false"
                              :props="{ value: 'massName', label: 'massName' }" placeholder="请选择质量范围"
                              @node-click="getCheckedNodes"/>
            </el-form-item>
          </el-form>
          <div class="footer">
            <el-button type="primary" @click="creat(creatform)">确定</el-button>
            <el-button @click="dialogFormVisible = false">取消</el-button>
          </div>
        </div>
        <!-- 编辑 -->
        <div v-show="state.boxFlag">
          <el-form :model="editForm" label-position="right" ref="editform" :rules="creatRules2">
            <el-form-item label="商品类型" prop="dictValue">
              <el-select v-model="editForm.dictValue" placeholder="请选择商品类型" class="el-select">
                <el-option :label="item.name" :value="item.name" v-for=" item  in  diaSelectList " :key="item.id"
                  @click="getDictValueid(item)" />
              </el-select>
            </el-form-item>
            <el-form-item label="质量范围" prop="commodity">
              <el-tree-select v-model="editForm.commodity" :data="treedata" check-strictly :render-after-expand="false"
                              :props="{ value: 'massName', label: 'massName' }" @node-click="getCheckedNodes"/>
            </el-form-item>
          </el-form>
          <div class="footer">
            <el-button type="primary" @click="edit(editform)">确定</el-button>
            <el-button @click="dialogFormVisible = false">取消</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, watchEffect, computed, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { massRange } from "@/api/model/assist/massRange/index";
const { proxy } = getCurrentInstance();
import { ElMessage } from 'element-plus';
import quality from "@/api/erp/quality"
import TopTitle from '@/components/topTitle'
const creatRules = reactive({
  dictValue: [{ required: true, message: "请选择商品类型", trigger: "blur" }],
  commodity: [{ required: true, message: "请选择质量范围", trigger: "blur" }],
});
const creatRules2 = reactive({
  dictValue: [{ required: true, message: "请选择商品类型", trigger: "blur" }],
  commodity: [{ required: true, message: "请选择质量范围", trigger: "blur" }],
});
const treedata = ref([
]);
const ids = ref('')
const diaSelectList = ref([])
const queryForm = ref()
const dialogFormVisible = ref(false);
const queryParams = reactive({
  current: 1,
  size: 10,
  total: 0,
  dictValue: '',
  dictionaryType: ''
});
const state = reactive({
  pageNo: 1,
  pageSize: 10,
  dataCount: 0,
  boxFlag: false
});
const form = reactive({
  dictValue: '',
  commodity: '',
})
const editForm = reactive({})
const creatform = ref()
const editform = ref()
const commodityid = ref('')
const dictValueid = ref('')
const commodityConfigList = ref([]);
// 新增按钮
function handlerCreat(formEl) {
  dialogFormVisible.value = true
  state.boxFlag = false
  formEl.resetFields()
}
//树结构点击事件
function getCheckedNodes(node, e) {
  commodityid.value = node.id

}
// option点击事件
function getDictValueid(item) {
  dictValueid.value = item.id
}
// 格式化日期
function formatDate(d) {
  var date = new Date(d);
  var YY = date.getFullYear() + "-";
  var MM =
    (date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1) + "-";
  var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  return YY + MM + DD;
}
//质量范围配置列表
function getList() {
  console.log({...queryParams});
  quality.list({
    size: queryParams.size,
    current: queryParams.current,
  }).then(res => {
    if (res.code == 200) {
      commodityConfigList.value = res.data.records
      queryParams.total = res.data.total
    }
  })
}
getList()
//获取商品类型
function getValue() {
  quality.value({ 'dictType': 'product_type_config' }).then(res => {
    if (res.code == 200) {
      diaSelectList.value = res.data.records
    }
  })
}
getValue()

//新增质量范围配置
const creat = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      var params = {
        dictionaryType: dictValueid.value,
        commodityClassify: {
          id: commodityid.value
        }
      }
      quality.save(params).then(res => {
        if (res.code == 200) {
          commodityConfigList.value.push(res.data)
          dialogFormVisible.value = false
          ElMessage({
            message: "保存成功",
            type: "success",
          });
          getList()
        } else {
          proxy.msgError(res.msg)
        }
      })
    }
  });
};
//搜索
function searchQuality() {
  var params = {
    dictionaryType: dictValueid.value,
  }
  quality.list(params).then(res => {
    if (res.code == 200) {
      commodityConfigList.value = res.data.records
    }
  })
}
//编辑按钮
function handleEdit(row) {
  console.log(row);
  dialogFormVisible.value = true
  state.boxFlag = true
  ids.value = row.id
  editForm.dictValue = row.dictionaryValue
  editForm.commodity = row.commodityClassify.massName
  dictValueid.value = row.dictionaryType
  commodityid.value = row.commodityClassify.id
}
// 编辑的请求
const edit = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      var params = {
        id: ids.value,
        dictionaryType: dictValueid.value,
        commodityClassify: {
          id: commodityid.value
        }
      }
      quality.save(params).then(res => {
        if (res.code == 200) {
          // diagnosisList1.value.push(res.data)
          ElMessage({
            message: "修改成功",
            type: "success",
          });
          dialogFormVisible.value = false
          getList()
        }
      })
    }
  });
};

// 重置
function resetQuery(formEl) {
  queryParams.dictValue = ''
  formEl.resetFields()
  getList()
}
// 获取质量树表
const getData2 = () => {
  massRange
    .treeData()
    .then((res) => {
      if (res.code == 200) {
        treedata.value = res.data
      }
    });
};
getData2()
//删除
function handleDelete(row) {
  proxy.$confirm('是否确认删除此质量范围配置?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    quality.delete({ ids: row.id }).then(res => {
      if (res.code == 200) {
        getList();
        proxy.msgSuccess("删除成功");
      }
    })
  }).catch(() => { });
}
/**
* 仓库
*/
// const store = useStore();
/**
* 路由对象
*/
const route = useRoute();
/**
* 路由实例
*/
const router = useRouter();
//console.log('1-开始创建组件-setup')
/**
* 数据部分
*/
const data = reactive({})
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})

</script>
<style scoped lang='scss'>
.el-table {
  margin-top: 20px;
}

.formHeard {
  // margin-top: 12px;
}

.commonTopBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  background-color: #fff;
  margin: 0px 20px 0px 0px;
  -webkit-box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
  border-radius: 7px;
}

.footer {
  margin: 0px 0px 10px 350px;
}

.commonBox {
  padding: 15px 15px 0 15px;
  box-sizing: border-box;
  background-color: #fff;
  margin: 20px 20px 0px 0px;
  -webkit-box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%), 0 0 6px 0 rgb(0 0 0 / 4%);
  border-radius: 7px;
}

// .adminui-main {
//   // margin: 20px 0px 0px 20px;
// }
.topSpan {
  width: 45px;
  height: 23px;
  font-size: 22px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  display: block;
  line-height: 30px;
  margin-bottom: 20px;
}

::v-deep .Botm {
  margin: 10px;

  .el-card__body {
    padding-bottom: 0px
  }
}

.searchSpan {
  width: 138px;
  height: 23px;
  font-size: 22px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  // display: block;
  line-height: 30px;
  // margin-right:100px;
}
</style>
