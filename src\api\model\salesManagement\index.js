import http from "@/utils/request";

export const manageApi = {
	clientType: function (params) {
		return http.get("/erp/customer/erpCustomers/list", params);
	},
	powerType: function (params) {
		return http.get("/erp/customer/erpCustomers/queryById", params);
	},
	serialType: function (params) {
		return http.get("/erp/housenum/erpWarehouseNumberSet/listJsr", params);
	},
	handleType: function (params) {
		return http.get(
			"/erp/housenum/erpWarehouseNumberHandledBySet/queryByWarehouseNumberId",
			params
		);
	},
	getMO: function (params) {
		return http.get(
			"/erp/sale/orderTemplate/list", params);
	},
	subAll: function (params) {
		return http.get("/erp/sales/erpSalesOrder/submitAudit", params);
	},
	logList: function (params) {
		return http.get("/erp/log/operatelog/list", params);
	},
	warehouseList: function (params) {
		return http.get("/erp/sales/erpSalesOutboundRecord/list", params);
	},
	auditList: function (params) {
		return http.get("/erp/sales/erpSalesOrderApproval/list", params);
	},
	auditLists: function (params) {
		return http.get("/erp/sales/retreat/approval/list", params);
	},
	delGoods: function (params) {
		return http.delete("/erp/sales/erpSalesOrderForm/delete", params);
	},
	detailManage: function (params) {
		return http.get("/erp/sales/erpSalesOrder/findSalesOrderById", params);
	},
	delManage: function (params) {
		return http.delete("/erp/sales/erpSalesOrder/delete", params);
	},
	goodsSearch: function (params) {
		return http.get(
			"/erp/sales/erpSalesOrder/findSalesCommodityList",
			params
		);
	},
	saveManage: function (data) {
		return http.post("/erp/sales/erpSalesOrder/createSalesOrder", data);
	},
	manageList: function (params) {
		return http.get("/erp/sales/erpSalesOrder/findMultipleList", params);
	},
	manageSearch: function (data) {
		return http.get("/erp/sales/erpSalesOrder/findMultipleList", data);
	},
};
export const backApi = {
	getList: function (params) {
		return http.get("/erp/sales/erpSalesOutboundRecord/list", params);
	},
	delFn: function (params) {
		return http.delete("/erp/sales/retreat/delete", params);
	},
	handleList: function (params) {
		return http.get("/sys/user/list", params);
	},
	saveFile: function (params) {
		return http.get("/file/onfile", params);
	},
	handleLists: function (params) {
		return http.get("/erp/customer/erpCustomers/queryById", params);
	},
	saveList: function (data) {
		return http.post("/erp/sales/retreat/createSalesRetreat", data);
	},
	getTable: function (params) {
		return http.get("/erp/sales/retreat/list", params);
	},
	getDetails: function (params) {
		return http.get("/erp/sales/retreat/salesRetreatDetail", params);
	},
	getPut: function (params) {
		return http.get("/erp/sales/retreat/inboundrecord/list", params)
	}
}

export const priceApi = {
	savePrice: function (data) {
		return http.post("/erp/sales/adjust/saveSalesAdjust", data);
	},
	auditLists: function (params) {
		return http.get("/erp/sales/adjust/approval/list", params);
	},
	saveGoods: function (data) {
		return http.post("/erp/sales/adjust/saveSalesAdjustForm", data);
	},
	submitAll: function (params) {
		return http.get("/erp/sales/adjust/submits", params);
	},
	savePriceForm: function (data) {
		return http.post("/erp/sales/adjust/saveSalesAdjustPrice", data);
	},
	getList: function (params) {
		return http.get("/erp/sales/adjust/list", params);
	},
	delList: function (params) {
		return http.delete("/erp/sales/adjust/deleteAdjust", params);
	},
	getDetail: function (params) {
		return http.get("/erp/sales/adjust/findAdjustDetail", params);
	},
}
