<template>
    <div>
        <!-- 搜索 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item prop="warehouse">
                    <el-select v-model="queryParams.warehouse" @change="handlerWare4" placeholder="请选择所属仓" clearable
                        style="width:100%;" @clear="getList">
                        <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="warehouse">
                    <el-select v-model="queryParams.storeHouse" @change="handlerWare5" placeholder="请选择所属库" clearable
                        style="width:100%;" @clear="getList">
                        <el-option v-for="item in libraryOptions4" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="warehouse">
                    <el-select v-model="queryParams.storeHouseArea" @change="handlerWare6" placeholder="请选择所属库区" clearable
                        style="width:100%;" @clear="getList">
                        <el-option v-for="item in libraryOptions5" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="warehouse">
                    <el-select v-model="queryParams.goodsShelves" placeholder="请选择所属货架" @change="handleSearch" clearable style="width:100%;"
                        @clear="getList" >
                        <el-option v-for="item in libraryOptions6" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(creatForm)" class="creatSpan">新增</el-button>
            <RightToptipBarV2 @handleRefresh="getList" className="locationInformation"
                style="float:right;margin-top:10px" />
            <DragTableColumn :columns="columns" :tableData="recordList" className="locationInformation"
                v-model:queryParams="queryParams" :getList="getList">
                <template v-slot:operate="{ scopeData }">
                    <el-button link type="primary" @click="handleEdit(scopeData.row)">
                        <img src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                    <el-button link type="danger" @click="handleDelete(scopeData.row)"><img src="@/assets/icons/delete.png"
                            style="margin-right:5px" />删除</el-button>
                    <el-button link @click="handlerLog(scopeData.row)" style="color:#67c23a"><img
                            src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                </template>
            </DragTableColumn>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增的弹框 -->
        <el-dialog v-model="dialogFormVisible" width="50%" title="新增货架信息">
            <el-form :model="dialogform" label-width="110px" :rules="rules" ref="creatForm">
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="所属仓" prop="warehouse">
                            <el-select v-model="dialogform.warehouse" @change="handlerWare" placeholder="请选择所属仓" clearable
                                style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库" prop="storeHouse">
                            <el-select v-model="dialogform.storeHouse" @change="handlerWare2" placeholder="请选择所属库" clearable
                                style="width:100%;">
                                <el-option v-for="item in libraryOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库区" prop="storeHouseArea">
                            <el-select v-model="dialogform.storeHouseArea" @change="handlerWare3" placeholder="请选择所属库区"
                                clearable style="width:100%;">
                                <el-option v-for="item in libraryOptions2" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="所属货架" prop="goodsShelves">
                            <el-select v-model="dialogform.goodsShelves" placeholder="请选择所属货架" clearable
                                style="width:100%;">
                                <el-option v-for="item in libraryOptions3" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所在层数" prop="location">
                            <el-input v-model="dialogform.location" placeholder="请输入所在层数" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货位编码" prop="code">
                            <el-input v-model="dialogform.code" placeholder="请输入货位编码" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货位名称" prop="name">
                            <el-input v-model="dialogform.name" placeholder="请输入货位名称" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货位长度(cm)" prop="length">
                            <el-input v-model="dialogform.length2" placeholder="请输入货架宽度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货位宽度(cm)" prop="hight">
                            <el-input v-model="dialogform.hight" placeholder="请输入货位宽度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="最大承重(kg)" prop="maxLoad">
                            <el-input v-model="dialogform.maxLoad" placeholder="请输入最大承重" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="dialogform.remark" :rows="1" type="textarea" placeholder="请输入备注" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="dialogImageUrl" />
            </el-dialog>
            <div class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary" @click="creat(creatForm)">确定</el-button>
            </div>
        </el-dialog>
        <!-- 编辑的弹框 -->
        <el-dialog v-model="editdialogFormVisible" width="50%" title="新增货架信息">
            <el-form :model="editdialogform" label-width="110px" :rules="rules" ref="editcreatForm">
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="所属仓" prop="warehouse">
                            <el-select v-model="editdialogform.warehouse" @change="handlerWare7" placeholder="请选择所属仓"
                                clearable style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库" prop="storeHouse">
                            <el-select v-model="editdialogform.storeHouse" @change="handlerWare8" placeholder="请选择所属库"
                                clearable style="width:100%;">
                                <el-option v-for="item in libraryOptions7" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属库区" prop="storeHouseArea">
                            <el-select v-model="editdialogform.storeHouseArea" @change="handlerWare9" placeholder="请选择所属库区"
                                clearable style="width:100%;">
                                <el-option v-for="item in libraryOptions8" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="所属货架" prop="goodsShelves">
                            <el-select v-model="editdialogform.goodsShelves" placeholder="请选择所属货架" clearable
                                style="width:100%;">
                                <el-option v-for="item in libraryOptions9" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所在层数" prop="location">
                            <el-input v-model="editdialogform.location" placeholder="请输入所在层数" clearable
                                @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货位编码" prop="code">
                            <el-input v-model="editdialogform.code" placeholder="请输入货位编码" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="货位名称" prop="name">
                            <el-input v-model="editdialogform.name" placeholder="请输入货位名称" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货位长度(cm)" prop="length">
                            <el-input v-model="editdialogform.length" placeholder="请输入货架宽度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="货位宽度(cm)" prop="hight">
                            <el-input v-model="editdialogform.hight" placeholder="请输入货位宽度" clearable @change=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :span="8">
                        <el-form-item label="最大承重(kg)" prop="maxLoad">
                            <el-input v-model="editdialogform.maxLoad" placeholder="请输入最大承重" clearable
                                @change=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="editdialogform.remark" :rows="1" type="textarea" placeholder="请输入备注" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-dialog v-model="dialogVisible">
                <img w-full :src="dialogImageUrl" alt="dialogImageUrl" />
            </el-dialog>
            <div class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary" @click="edit(editcreatForm)">确定</el-button>
            </div>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus'
import informationMaintenance from '@/api/erp/warehouseManagement/informationMaintenance'
import libraryInformation from '@/api/erp/warehouseManagement/libraryInformation'
import reservoirInformation from '@/api/erp/warehouseManagement/reservoirInformation'
import shelfInformation from '@/api/erp/warehouseManagement/shelfInformation'
import locationInformation from '@/api/erp/warehouseManagement/locationInformation'
import logList from './logList.vue'
import tool from '@/utils/tool';
const queryParams = reactive({
    current: 1,
    size: 10,
})
const { proxy } = getCurrentInstance();
const reviewRow = ref({})
const reviewVisible = ref(false)
const warehouseOptions = ref([])
const ids = ref('')
const recordList = ref([])
const libraryOptions = ref([])
const libraryOptions2 = ref([])
const libraryOptions3 = ref([])
const libraryOptions4 = ref([])
const libraryOptions5 = ref([])
const libraryOptions6 = ref([])
const libraryOptions7 = ref([])
const libraryOptions8 = ref([])
const libraryOptions9 = ref([])
const selected = ref([])
const dialogFormVisible = ref(false)
const editdialogFormVisible = ref(false)
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const dialogform = reactive([])
const editdialogform = ref([])
const creatForm = ref()
const editcreatForm = ref()
const total = ref(0)
const columns = ref(
    [
        {
            label: '所属仓简称',
            prop: 'wareHouse.nameSimplified',
            // type: "sort",
            // fixed: 'left'
        },
        {
            label: '所属库简称',
            prop: 'storeHouse.nameSimplified'
        }, {
            label: '所属库区简称',
            prop: 'storeHouseArea.nameSimplified'
        }, {
            label: '所属货架简称',
            prop: 'goodsShelves.name'
        }, {
            label: '所在层数',
            prop: 'location'
        }
        , {
            label: '货位编码',
            prop: 'code'
        }, {
            label: '货位名称',
            prop: 'name'
        }, {
            label: '货位长度(cm)',
            prop: 'length'
        }, {
            label: '货位宽度(cm)',
            prop: 'hight'
        }, {
            label: '最大承重(kg)',
            prop: 'maxLoad'
        }, {
            label: '操作',
            prop: 'operate',
            type: 'operate',
            minWidth: 200,
        },
    ]
)
const rules = reactive({
    warehouse: [{ required: true, message: '请选择所属仓', trigger: 'blur' },],
    storeHouse: [{ required: true, message: '请选择所属库', trigger: 'blur' },],
    storeHouseArea: [{ required: true, message: '请选择所属库区', trigger: 'blur' },],
    goodsShelves: [{ required: true, message: '请选择所属货架', trigger: 'blur' },],
    location: [{ required: true, message: '请输入所在层数', trigger: 'blur' },],
    code: [{ required: true, message: '请输入货位编码', trigger: 'blur' },],
    name: [{ required: true, message: '请输入货位名称', trigger: 'blur' },],
    length: [{ required: true, message: '请输入货位长度', trigger: 'blur' },],
    hight: [{ required: true, message: '请输入货位宽度', trigger: 'blur' },],
    maxLoad: [{ required: true, message: '请输入最大承重', trigger: 'blur' },],
})
//获取仓列表
function getwarehouseList() {
    informationMaintenance.list().then(res => {
        if (res.code == 200) {
            warehouseOptions.value = res.data.records
        }
    })
}
getwarehouseList()
const handlerWare = (item) => {
    dialogform.storeHouse = ''
    dialogform.storeHouseArea = ''
    dialogform.goodsShelves = ''
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions.value = res.data.records
            // total.value = res.data.total
        }
    })
}
const handlerWare2 = (item) => {
    // dialogform.value.warehouse = ''
    dialogform.storeHouseArea = ''
    dialogform.goodsShelves = ''
    //获取库区
    function getList3() {
        reservoirInformation.list({
            'storeHouse.id': item
        }).then(res => {
            if (res.code == 200) {
                libraryOptions2.value = res.data.records
                // total.value = res.data.total
            }
        })
    }
    getList3()
}
const handlerWare3 = (item) => {
    // dialogform.value.warehouse = ''
    dialogform.goodsShelves = ''
    //获取货架
    function getList4() {
        shelfInformation.list({
            'storeHouseArea.id': item
        }).then(res => {
            if (res.code == 200) {
                libraryOptions3.value = res.data.records
                // total.value = res.data.total
            }
        })
    }
    getList4()
}
//搜索
const handleSearch = () => {
    getList()
}
const handlerWare4 = (item) => {
    handleSearch()
    queryParams.storeHouse = ''
    queryParams.storeHouseArea = ''
    queryParams.goodsShelves = ''
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions4.value = res.data.records
            // total.value = res.data.total
        }
    })
}
const handlerWare5 = (item) => {
    handleSearch()
    queryParams.storeHouseArea = ''
    queryParams.goodsShelves = ''
    //获取库区
    reservoirInformation.list({
        'storeHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions5.value = res.data.records
        }
    })

}
const handlerWare6 = (item) => {
    handleSearch()
    queryParams.goodsShelves = ''
    //获取货架
        shelfInformation.list({
            'storeHouseArea.id': item
        }).then(res => {
            if (res.code == 200) {
                libraryOptions6.value = res.data.records
                // total.value = res.data.total
            }
        })
}
handlerWare4()
handlerWare5()
handlerWare6()
const handlerWare7 = (item) => {
    editdialogform.storeHouse = ''
    editdialogform.storeHouseArea = ''
    editdialogform.goodsShelves = ''
    //获取库
    libraryInformation.list({
        'wareHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions7.value = res.data.records
            // total.value = res.data.total
        }
    })
}
const handlerWare8 = (item) => {
    editdialogform.storeHouseArea = ''
    editdialogform.goodsShelves = ''
    //获取库区
    reservoirInformation.list({
        'storeHouse.id': item
    }).then(res => {
        if (res.code == 200) {
            libraryOptions8.value = res.data.records
        }
    })

}
const handlerWare9 = (item) => {
    editdialogform.goodsShelves = ''
    //获取货架
        shelfInformation.list({
            'storeHouseArea.id': item
        }).then(res => {
            if (res.code == 200) {
                libraryOptions9.value = res.data.records
                // total.value = res.data.total
            }
        })
}
//货位列表
function getList() {
    const { warehouse,storeHouse,storeHouseArea,goodsShelves,...rest } = queryParams;
    const queryParamsUpdated = {
        'wareHouse.id': warehouse,
        'storeHouse.id': storeHouse,
        'storeHouseArea.id':storeHouseArea,
        'goodsShelves.id':goodsShelves,
        ...rest
    };
    locationInformation.list({...queryParamsUpdated}).then(res => {
        if (res.code == 200) {
            recordList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
// 新增按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    formEl.resetFields()
}

//新增请求
const creat = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            const params = {
                goodsShelves: {
                    id: dialogform.goodsShelves
                },
                code: dialogform.code,
                name: dialogform.name,
                location: dialogform.location,
                length: dialogform.length,
                hight: dialogform.hight,
                maxLoad: dialogform.maxLoad,
                remark: dialogform.remark,
            }
            locationInformation.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        dialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "添加失败，请稍后重试",
                        });
                    }
                })
        }
    });
};
// 编辑按钮
const handleEdit = (row) => {
    editdialogFormVisible.value = true
    handlerWare()
    handlerWare2()
    handlerWare3()
    handlerWare7()
    handlerWare8()
    handlerWare9()
    locationInformation.detail({ id: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.value = res.data
            ids.valus = res.data.id
            console.log(res.data);
            editdialogform.value.warehouse = res.data.wareHouse.id
            editdialogform.value.storeHouse = res.data.storeHouse.id
            editdialogform.value.storeHouseArea = res.data.storeHouseArea.id
            editdialogform.value.goodsShelves = res.data.goodsShelves.id
        }
    })
}
// 编辑请求
const edit = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            console.log(editdialogform.value.goodsShelves);
            const params = {
                goodsShelves: {            
                    id: editdialogform.value.goodsShelves
                },
                code:  editdialogform.value.code,
                name:  editdialogform.value.name,
                location:  editdialogform.value.location,
                length:  editdialogform.value.length,
                hight:  editdialogform.value.hight,
                maxLoad:  editdialogform.value.maxLoad,
                remark:  editdialogform.value.remark,
            }
            params.id = ids.valus
            console.log(params);
            locationInformation.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "修改成功",
                            type: "success",
                        });
                        editdialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "修改失败，请稍后重试",
                        });
                    }
                })
        }
    });
};
// 删除货架
const handleDelete = (row) => {
    proxy.$confirm('是否确认删除此货架信息?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        locationInformation.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
</script>

<style lang="scss" scoped>
.dialog {
    padding: 30px
}

::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.dialogP {
    position: relative;
    top: -10px;
    left: 100px
}

.creatSpan {
    margin-bottom: 10px;
}

.dialog-footer {
    display: flex;
    justify-content: end;
}</style>