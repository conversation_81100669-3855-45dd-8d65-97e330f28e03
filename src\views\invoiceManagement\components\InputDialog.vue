<template>
  <table v-if="formFlag" border="0" cellpadding="0" cellspacing="1" class="messTable">
    <tr>
      <td>发票号码</td>
      <td>{{ backForm.n1 }}</td>
    </tr>
    <tr>
      <td>发票代码</td>
      <td>{{ backForm.n2 }}</td>
    </tr>
    <tr>
      <td>发票类型</td>
      <td>{{ backForm.n8 ? echo(backForm.n8) : '' }}</td>
    </tr>
    <tr>
      <td>发票供应商</td>
      <td>{{ supplierType.find(item => item.id == backForm.n3)?.enterpriseName }}</td>
    </tr>
    <tr>
      <td>纳税人识别号</td>
      <td>{{ backForm.n4 }}</td>
    </tr>
    <tr>
      <td>开票日期</td>
      <td>{{ functionIndex.transformTimestamp(backForm.n5) }}</td>
    </tr>
    <tr>
      <td>发票金额</td>
      <td>{{ Number(backForm.n6).toFixed(2) }}</td>
    </tr>
    <tr>
      <td>不含税金额</td>
      <td>{{ Number(backForm.n10).toFixed(2) }}</td>
    </tr>
    <tr>
      <td>税额</td>
      <td>{{ Number(backForm.n11).toFixed(2) }}</td>
    </tr>
    <tr>
      <td>可抵扣金额</td>
      <td>{{ Number(backForm.n12).toFixed(2) }}</td>
    </tr>
    <tr>
      <td>上传附件</td>
      <td>
        <p v-for="(item, index) in backForm.n7" :key="index"
           :style="backForm.n7.length == 1 ? 'color:#2a75f6;cursor: pointer;' : 'color:#2a75f6;cursor: pointer;line-height: 25px'"
           style="overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:15em"
           @click="() => handlePreview(item)">{{
            item.name
          }}</p>
      </td>
    </tr>
    <tr style="border-left: 1px solid #e9ecf1"></tr>
  </table>
  <el-form v-if="!formFlag" ref="creatform" :disabled="formFlag" :inline="true" :model="backForm" :rules="creatRules"
           class="form_130" label-width="120px" style="margin-top: 20px">
    <el-form-item :prop="formFlag ? null : 'n1'" label="发票号码">
      <el-input v-model="backForm.n1" :placeholder="formFlag ? ' ' : '请输入发票号码'" class="form_225" clearable
                style="width: 100%;" @input="(val) => { backForm.n1 = backForm.n1.replace(/[^0-9.]/g, '') }"/>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n2'" label="发票代码">
      <el-input v-model="backForm.n2" :placeholder="formFlag ? ' ' : '请输入发票代码'" class="form_225" clearable
                style="width: 100%;" @input="(val) => { backForm.n2 = backForm.n2.replace(/[^0-9.]/g, '') }"/>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n8'" label="发票类型">
      <el-select v-model="backForm.n8" :placeholder="formFlag ? ' ' : '请选择发票类型'" class="form_225" clearable
                 filterable
                 style="width: 100%;">
        <el-option v-for="item in invoiceType" :key="item.value" :label="item.name" :value="item.value"/>
      </el-select>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n3'" label="发票供应商">
      <el-select v-model="backForm.n3" :placeholder="formFlag ? ' ' : '请选择发票供应商'" class="form_225" clearable
                 @change="n4Fn"
                 filterable style="width: 100%;">
        <el-option v-for="item in supplierType" :key="item.value" :label="item.enterpriseName" :value="item.id"/>
      </el-select>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n4'" label="纳税人识别号">
      <el-input v-model="backForm.n4" :placeholder="formFlag ? ' ' : '请输入纳税人识别号'" class="form_225" clearable
                style="width: 100%;"/>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n5'" label="发票日期">
      <el-date-picker v-model="backForm.n5" :placeholder="formFlag ? ' ' : '请选择发票日期'" class="form_225"
                      size="default"
                      style="width: 100%;" type="date" value-format="YYYY-MM-DD"/>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n6'" label="发票金额">
      <el-input-number v-model="backForm.n6" :min="0" :placeholder="formFlag ? ' ' : '请输入开票金额'" :precision="2"
                       :step="1"
                       :value-on-clear="0" class="form_225" style="width: 100%;height: 100%"/>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n10'" label="不含税金额">
      <el-input-number v-model="backForm.n10" :min="0" :placeholder="formFlag ? ' ' : '请输入不含税金额'" :precision="2"
                       :step="1"
                       :value-on-clear="0" class="form_225" style="width: 100%;height: 100%"/>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n11'" label="税额">
      <el-input-number v-model="backForm.n11" :min="0" :placeholder="formFlag ? ' ' : '请输入税额'" :precision="2"
                       :step="1"
                       :value-on-clear="0" class="form_225" style="width: 100%;height: 100%"/>
    </el-form-item>
    <el-form-item :prop="formFlag ? null : 'n12'" label="可抵扣金额">
      <el-input-number v-model="backForm.n12" :min="0" :placeholder="formFlag ? ' ' : '请输入可抵扣金额'" :precision="2"
                       :step="1"
                       :value-on-clear="0" class="form_225" style="width: 100%;height: 100%"/>
    </el-form-item>
    <br/>
    <el-form-item :prop="formFlag ? null : 'n7'" label="上传附件">
      <el-upload v-model:file-list="backForm.n7" :before-upload="(file) => beforeFile(file)" :headers="headers"
                 :limit="0"
                 :on-error="() => errorFile()" :on-preview="handlePreview" :on-remove="handleRemove"
                 :on-success="(response) => successFile(response)" :action="process.env.VUE_APP_API_UPLOAD" class="upload-demo" multiple>
        <el-button v-show="!formFlag" type="primary">点击上传</el-button>
        <template #tip>
          <div v-show="!formFlag" class="el-upload__tip">
            文件大小不大于5MB
          </div>
        </template>
      </el-upload>
    </el-form-item>
  </el-form>
  <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
import tool from "@/utils/tool";
import {ElMessage} from "element-plus";
import {inputInvoice} from "@/api/model/invoice";
import {functionIndex} from "@/views/salesManagement/functionIndex";

const {proxy} = getCurrentInstance();
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const data = reactive({
  imgUrl: [],
  checkFlag: false,
})
const delList = ref([])
const emit = defineEmits([])
const formFlag = ref(false)
const creatform = ref(); //验证表单/流程新增
const creatRules = reactive({
  n8: [{required: true, message: "请选择发票类型", trigger: "blur"}],
  n1: [{required: true, message: "请输入发票号码", trigger: "blur"}],
  n2: [{required: true, message: "请输入发票代码", trigger: "blur"}],
  n3: [{required: true, message: "请选择发票供应商", trigger: "blur"}],
  n4: [{required: true, message: "请输入纳税人识别号", trigger: "blur"}],
  n5: [{required: true, message: "请选择发票日期", trigger: "blur"}],
  n6: [{required: true, message: "请输入发票金额", trigger: "blur"}],
  n7: [{required: true, message: "请上传文件", trigger: "blur"}],
  n10: [{required: true, message: "请输入不含税金额", trigger: "blur"}],
  n11: [{required: true, message: "请输入税额", trigger: "blur"}],
  n12: [{required: true, message: "请输入可抵扣金额", trigger: "blur"}],
});
const backForm = ref({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
  n5: "",
  n6: null,
  n7: [],
  n8: "",
  n9: "",
  n10: null,
  n11: null,
  n12: null
})
const n4Fn = () => {
  let item = supplierType.value.find(item => item.id == backForm.value.n3)
  backForm.value.n4 = item.socialCreditCode
  backForm.value.n9 = item.enterpriseName
}
const supplierType = ref([])
const invoiceType = ref([])
const close = () => {
  data.checkFlag = false;
  data.imgUrl = []
}
const echo = (str) => {
  let find = invoiceType.value.find(item => item.value == str)?.name
  return find ? find : ""
}
const handleRemove = (file) => {
  if (file.delId) {
    delList.value.push(file.delId)
  }
}

const props = defineProps({})
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  inputInvoice.getSupplier({
    customLabel: 2,
    size: 1000
  }).then(res => {
    supplierType.value = res.data.records
  })
  let typeLo = JSON.parse(window.localStorage.getItem('In_Invioce'))

  if (typeLo) {
    invoiceType.value = typeLo
  } else {
    invoiceType.value = await proxy.getDictList("In_Invioce")
  }
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  formFlag,
  creatform,
  backForm,
  delList
})
const beforeFile = (file) => {
  if (file.size > 5242880) {
    ElMessage.error("文件不能大于5M");
    return false;
  }
}
const headers = {
  Authorization: "Bearer " + tool.cookie.get("TOKEN"),
  ContentType: "multipart/form-data",
  clientType:'pc',
};
const errorFile = () => {
  ElMessage.error("上传失败");
};
const handlePreview = (uploadFile) => {
  const fileName = uploadFile.name.split(".")
  data.imgUrl = []
  if (fileName[fileName.length - 1] == "pdf") {
    var s = window.location.toString();
    var s1 = s.substr(7, s.length);
    var s2 = s1.indexOf("/");
    s = s.substr(0, 8 + s2);
    window.open(s + (uploadFile.response ? uploadFile.response.data.url : uploadFile.url))
  } else {
    data.checkFlag = true;
    uploadFile.response ? data.imgUrl.push(uploadFile.response.data.url) : data.imgUrl.push(uploadFile.url)
  }
}
const successFile = () => {
  ElMessage.success("上传成功");
  console.log(backForm.value.n7)
};

</script>
<style lang='scss' scoped>
::v-deep .el-input.is-disabled .el-input__wrapper {
  background: none !important;
  box-shadow: none !important;
  color: #000 !important;
}

::v-deep .el-input.is-disabled .el-input__icon {
  display: none !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
  -webkit-text-fill-color: #000 !important;
}

::v-deep .el-upload {
  display: inline;
}

::v-deep .el-upload-list {
  margin-top: 0px;
}

.messTable {
  width: 100%;
  background-color: #eaedf3;
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 1px 1px 0 1px;
  margin-top: 20px;

  tr {
    margin-bottom: 1px;
    display: flex;
    background: #fff;

    td {
      background-color: white;
      line-height: 40px;
    }

    td:nth-child(1) {
      flex: 1;
      padding: 0 10px;
      font-weight: bold;
      color: #505050;
      background: #f7f7f7;
    }

    td:nth-child(2) {
      color: #606266;
      padding: 0 10px;
      flex: 2
    }
  }
}
</style>
