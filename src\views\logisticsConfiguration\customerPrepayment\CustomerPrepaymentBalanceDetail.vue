<template>
    <div class="app-container customer-auto-height-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryParams" :inline="true" :model="queryParams" class="seache-form" @submit.native.prevent>
                <el-form-item label="单号" prop="orderNo">
                    <el-input v-model="queryParams.orderNo" placeholder="请输入单号" clearable @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="日期时段" prop="datePeriod" style="width: 320px">
                    <el-date-picker v-model="queryParams.datePeriod" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item label="付款方式" prop="paymentType">
                    <el-select v-model="queryParams.paymentType" clearable filterable placeholder="请选择付款方式" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in rechargeMethodList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                        <el-option key="auto-deduction" label="收款单自动扣款" value="auto-deduction"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="交易类型" prop="dealType" v-show="isShowAll">
                    <el-select v-model="queryParams.dealType" clearable filterable placeholder="请选择充值方式" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in transactionTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <div class="d__flex__statisticsRows">
                <el-statistic :precision="2" :value="overview.totalRecharge" :value-style="{ color: '#5670FE' }" group-separator="," title="预付款总计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalRechargeRebate" :value-style="{ color: '#11caca' }" group-separator="," title="返利总计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalDeduct" :value-style="{ color: '#fea700' }" group-separator="," title="扣款总计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalRefund" :value-style="{ color: '#3dc726' }" group-separator="," title="退款总计"></el-statistic>
            </div>
        </el-card>

        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <el-button :disabled="total === 0" :loading="loadingExport" type="primary" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" :loading="loading" table-i-d="transactionDetailsTable" @queryTable="getList" />
            </div>
            <column-table ref="transactionDetailsTable" :columns="columns" :data="dataList" :loading="loading" :show-index="true" class="customer-auto-height-table" max-height="null">
                <template #dealType="{ row }">
                    <span>{{ formatDictionaryData('transactionTypeList', row.dealType) }}</span>
                </template>
                <template #paymentType="{ row }">
                    <span>{{ formatDictionaryData('rechargeMethodList', row.paymentType) }}</span>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb16" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import customerPrepayment from '@/api/logisticsConfiguration/customerPrepayment.js';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar';
import { setDatePickerShortcuts } from '@/utils/config-store';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'CustomerPrepaymentBalanceDetail',
    components: { ColumnTable, RightToolbar, SearchButton},
    data() {
        return {
            showSearch: true,
            total: 0,
            loading: false,
            queryParams: {
                current: 1,
                size: 10,
                // 预付款账户id
                advancePaymentId: null,
                datePeriod: [],
                startDate: null,
                endDate: null
            },
            columns: [
                { title: '单号', key: 'orderNo', align: 'center', minWidth: '180px', columnShow: true, fixed: 'left' },
                { title: '客户名称', key: 'companyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '日期', key: 'createDate', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易类型', key: 'dealType', align: 'center', minWidth: '140px', columnShow: true },
                { title: '付款方式', key: 'paymentType', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易前金额', key: 'dealBeforeAmount', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易金额', key: 'dealAmount', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易后余额', key: 'dealAfterAmount', align: 'center', minWidth: '140px', columnShow: true },
                { title: '操作员', key: 'createBy', align: 'center', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true }
            ],
            dataList: [],
            rechargeMethodList: [],
            transactionTypeList: [],
            overview: {
                totalRecharge: 0,
                totalRechargeRebate: 0,
                totalDeduct: 0,
                totalRefund: 0
            },
            loadingExport: false,
            shortcuts: setDatePickerShortcuts(),
            isShowAll: false
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                return this.selectDictLabel(this[dictionaryName], value) || value || '--';
            };
        }
    },
    async created() {
        // 交易类型 字典
        this.rechargeMethodList = await this.getDictList('fourpl_payment_type');
        // 交易类型 字典
        this.transactionTypeList = await this.getDictList('fourpl_deal_type');
        if (this.$route.query) {
            this.query = this.$route.query;
            if (this.$route.query.advanceId) {
                this.queryParams.advancePaymentId = this.$route.query.advanceId;
                this.getList();
            }
        }
    },
    activated() {
        if (this.$route.query && !this.$route.query.advancePaymentId) {
            this.queryParams.advancePaymentId = this.query.advanceId;
            this.getList();
        }
    },
    methods: {
        getList() {
            this.loading = true;
            let params = { ...this.queryParams };
            delete params.datePeriod;
            customerPrepayment
                .listAdvancePaymentDetail(params)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                        this.getStatistics();
                    } else {
                        this.dataList = [];
                        this.total = 0;
                    }
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 获取统计数据
         */
        getStatistics() {
            customerPrepayment.getTradeRecordAmount(this.queryParams).then((res) => {
                if (res.code === 200 && res.data) {
                    this.overview = res.data;
                } else {
                    this.overview = {};
                }
            });
        },
        /**
         * 导出交易明细
         */
        handleExport() {
            if (this.loadingExport) return;
            this.loadingExport = true;
            const params = {
                advancePaymentId: this.queryParams.advancePaymentId,
                startDate: this.queryParams.startDate,
                endDate: this.queryParams.endDate,
                dealType: this.queryParams.dealType,
                paymentType: this.queryParams.paymentType,
                orderNo: this.queryParams.orderNo
            };
            customerPrepayment
                .advancePaymentDetailExport({ filename: '预付款交易明细.xls', ...params }, '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '预付款交易明细.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                    } else {
                        this.$message.error('导出异常请联系管理员');
                    }
                })
                .finally(() => {
                    this.loadingExport = false;
                });
        },
        handleQuery() {
            this.queryParams.current = 1;
            const { datePeriod } = this.queryParams;
            if (datePeriod && datePeriod.length) {
                this.queryParams.startDate = datePeriod[0] + ' 00:00:00';
                this.queryParams.endDate = datePeriod[1] + ' 23:59:59';
            } else {
                this.queryParams.startDate = null;
                this.queryParams.endDate = null;
            }
            this.getList();
        },
        resetQuery() {
            this.$refs['queryParams'].resetFields();
            this.handleQuery();
        },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}

.d__flex__statisticsRows {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}
</style>
