<template>
    <div>
        <el-drawer v-if="visible" v-model="visible" :title="prepaymentRechargeModificationTitle" size="770px" @close="closeVisible" class="self-drawer">
            <el-form ref="prepaymentRechargeModificationForm" :model="prepaymentRechargeModificationForm" label-width="auto">
                <el-descriptions v-if="prepaymentRechargeModificationStatus === 'approval' || prepaymentRechargeModificationStatus === 'detail'" :column="2" border size="small" title="申请信息">
                    <el-descriptions-item label="付款公司">{{ prepaymentRechargeModificationForm.paymentApply.companyName }}</el-descriptions-item>
                    <el-descriptions-item label="预付款账号">{{ prepaymentRechargeModificationForm.paymentApplyDto.advanceAccount }}</el-descriptions-item>
                    <el-descriptions-item label="收款公司" span="2">{{ formatDictionaryData('companyList', prepaymentRechargeModificationForm.paymentApplyDto.receiveCompany) }}</el-descriptions-item>
                    <el-descriptions-item label="汇款金额">{{ prepaymentRechargeModificationForm.paymentApplyDto.remitAmount }}</el-descriptions-item>
                    <el-descriptions-item label="汇款时间">{{ prepaymentRechargeModificationForm.paymentApplyDto.remitTime }}</el-descriptions-item>
                    <el-descriptions-item label="付款凭证" span="2">
                        <el-image v-for="(item, index) in prepaymentRechargeModificationForm.paymentApplyDto.remitFile" :key="index" :preview-src-list="[item.fileUrl]" :src="item.fileUrl" class="mr-5" fit="cover" style="width: 100px; height: 100px" />
                    </el-descriptions-item>
                    <el-descriptions-item v-if="prepaymentRechargeModificationStatus === 'detail' && prepaymentRechargeModificationForm.paymentApplyDto?.rebateAmount" label="返利金额">{{ prepaymentRechargeModificationForm.paymentApplyDto.rebateAmount }}</el-descriptions-item>
                    <el-descriptions-item label="备注">{{ prepaymentRechargeModificationForm.paymentApplyDto.remark }}</el-descriptions-item>
                </el-descriptions>
                <div v-else-if="prepaymentRechargeModificationStatus === 'modify'">
                    <el-descriptions border class="mt-10 mb-10" size="small" title="申请信息">
                        <el-descriptions-item label="付款公司">{{ prepaymentRechargeModificationForm.paymentApply.companyName }}</el-descriptions-item>
                        <el-descriptions-item label="预付款账号">{{ prepaymentRechargeModificationForm.paymentApplyDto.advanceAccount }}</el-descriptions-item>
                    </el-descriptions>
                    <el-form-item :rules="{ required: true, message: '请选择收款公司', trigger: 'change' }" label="收款公司" prop="paymentApplyDto.receiveCompany">
                        <el-select v-model="prepaymentRechargeModificationForm.paymentApplyDto.receiveCompany" class="w-full" clearable placeholder="请选择收款公司">
                            <el-option v-for="item in companyList" :key="item.code" :label="item.name" :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <div class="grid" style="grid-template-columns: 1fr 1fr; grid-gap: 10px">
                        <el-form-item :rules="{ required: true, message: '请输入汇款金额', trigger: 'blur' }" label="汇款金额" prop="paymentApplyDto.remitAmount">
                            <el-input-number v-model="prepaymentRechargeModificationForm.paymentApplyDto.remitAmount" :max="99999999" :min="0" :precision="2" class="w-full number__unit__element" controls-position="right" placeholder="请输入汇款金额" />
                        </el-form-item>
                        <el-form-item :rules="{ required: true, message: '请选择汇款时间', trigger: 'change' }" class="w-full" label="汇款时间" prop="paymentApplyDto.remitTime">
                            <el-date-picker v-model="prepaymentRechargeModificationForm.paymentApplyDto.remitTime" class="w-full" clearable format="YYYY-MM-DD HH:mm:ss" placeholder="请选择汇款时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
                        </el-form-item>
                    </div>
                    <el-form-item :rules="{ required: true, message: '请上传付款凭证', trigger: 'change' }" label="付款凭证" prop="paymentApplyDto.remitFile">
                        <el-upload
                            :action="uploadFileUrl"
                            :file-list="prepaymentRechargeModificationForm.paymentApplyDto.remitFile"
                            :headers="headers"
                            :limit="9"
                            :on-preview="handlePictureCardPreview"
                            :on-remove="handleRemovePrepaymentRechargeModification"
                            :on-success="fileUploadSuccess"
                            accept="image/*"
                            list-type="picture-card"
                        >
                            <el-icon>
                                <Plus />
                            </el-icon>
                        </el-upload>
                    </el-form-item>
                    <el-form-item :rules="{ required: true, message: '请输入备注', trigger: 'blur' }" label="备注" prop="paymentApplyDto.remark">
                        <el-input v-model="prepaymentRechargeModificationForm.paymentApplyDto.remark" maxlength="500" placeholder="请输入备注" rows="4" show-word-limit type="textarea"></el-input>
                    </el-form-item>
                </div>
                <el-form-item v-if="prepaymentRechargeModificationStatus === 'approval'" :rules="{ required: true, message: '请输入返利金额', trigger: 'blur' }" class="mt-10" label="返利金额" prop="paymentApplyDto.rebateAmount">
                    <el-input-number v-model="prepaymentRechargeModificationForm.paymentApplyDto.rebateAmount" :max="99999999" :min="0" :precision="2" class="w-50 number__unit__element" controls-position="right" placeholder="请输入返利金额" />
                </el-form-item>
                <el-descriptions v-if="prepaymentRechargeModificationStatus === 'approval'" border class="mt-10 mb-10" size="small" title="审批">
                    <el-descriptions-item label="申请人">{{ prepaymentRechargeModificationForm.paymentApplyDto.createBy.name }}</el-descriptions-item>
                    <el-descriptions-item label="申请时间">{{ formatDate(prepaymentRechargeModificationForm.paymentApplyDto.createDate) }}</el-descriptions-item>
                </el-descriptions>
                <div v-if="prepaymentRechargeModificationStatus === 'approval'">
                    <el-form-item :rules="{ required: true, message: '请选择审批意见', trigger: 'change' }" label="审批意见" prop="approveStatus">
                        <el-radio-group v-model="prepaymentRechargeModificationForm.approveStatus">
                            <el-radio label="1">通过</el-radio>
                            <el-radio label="2">驳回</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
                <div v-else-if="prepaymentRechargeModificationStatus === 'detail'" class="mt-10">
                    <div v-if="prepaymentRechargeModificationForm.approveList && prepaymentRechargeModificationForm.approveList.length">
                        <audit-and-flow-records ref="AuditAndFlowRecords"></audit-and-flow-records>
                    </div>
                </div>
                <el-form-item v-if="prepaymentRechargeModificationForm?.approveStatus === '2'" :rules="{ required: true, message: '请输入驳回原因', trigger: 'blur' }" label="驳回原因" prop="approveIdea">
                    <el-input v-model="prepaymentRechargeModificationForm.approveIdea" :rows="4" maxlength="500" placeholder="请输入驳回原因" show-word-limit type="textarea"></el-input>
                </el-form-item>
            </el-form>
            <template v-if="prepaymentRechargeModificationStatus === 'modify' || prepaymentRechargeModificationStatus === 'approval'" #footer>
                <div v-loading="prepaymentRechargeModificationLoading" style="text-align: right; margin-top: 10px">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm">提 交</el-button>
                </div>
            </template>
        </el-drawer>
        <el-image-viewer v-if="viewerVisible" v-model="viewerVisible" :url-list="viewerList" @close="viewerVisible = false" />
    </div>
</template>
<script>
import tool from '@/utils/tool';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import AuditAndFlowRecords from '@/views/auditManagement/AuditAndFlowRecords';
import { selectDictLabel } from '@/utils/dictLabel';
import { Close, Delete, Plus } from '@element-plus/icons-vue';
import paymentDoc from '@/api/shipperEnd/paymentDoc';
import moment from 'moment';

export default {
    name: 'PrepaymentRechargeModification',
    components: { AuditAndFlowRecords, Close, Delete, Plus },
    model: {
        prop: 'prepaymentRechargeModificationVisible',
        event: 'update:prepaymentRechargeModificationVisible'
    },
    props: {
        prepaymentRechargeModificationId: {
            type: String,
            default: undefined
        },
        prepaymentRechargeModificationStatus: {},
        prepaymentRechargeModificationTitle: {},
        prepaymentRechargeModificationVisible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            visible: this.prepaymentRechargeModificationVisible,
            viewerVisible: false,
            viewerList: [],
            prepaymentRechargeModificationForm: {
                approveList: [],
                approveStatus: undefined,
                approveIdea: undefined,
                paymentApplyDto: {
                    carrier: {
                        name: undefined
                    },
                    receiveCompany: undefined,
                    remitAmount: undefined,
                    remitTime: undefined,
                    remitFile: [],
                    remark: undefined,
                    createBy: {
                        name: undefined
                    },
                    createDate: undefined,
                    rebateAmount: undefined,
                    advanceAccount: undefined
                },
                paymentApply: {
                    companyName: undefined,
                    remark: undefined
                }
            },
            prepaymentRechargeModificationLoading: false,
            companyList: []
        };
    },
    computed: {
        /**
         * 格式化日期
         * @returns {function(*): *}
         */
        formatDate() {
            return (value) => {
                return moment(value).format('YYYY-MM-DD');
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    watch: {
        prepaymentRechargeModificationVisible: {
            handler(val) {
                this.visible = val;
            },
            immediate: true
        }
    },
    created() {
        if (this.prepaymentRechargeModificationId) {
            this.getPrepaymentRechargeModificationDetail(this.prepaymentRechargeModificationId);
        }
        this.getDict();
    },
    methods: {
        /**
         * 关闭抽屉
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:prepaymentRechargeModificationVisible', false);
        },
        /**
         * 文件上传成功
         * @param res
         */
        fileUploadSuccess(res) {
            this.prepaymentRechargeModificationForm.paymentApplyDto.remitFile.push({
                name: res.data.fileName,
                url: res.data.fileUrl
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
        },
        /**
         * 获取预付款审批详情
         */
        async getPrepaymentRechargeModificationDetail(id) {
            this.prepaymentRechargeModificationLoading = true;
            const res = await paymentOrderApproval.getPaymentOrderApprovalDetail({ id });
            if (res.code === 200) {
                if (this.prepaymentRechargeModificationStatus === 'modify') {
                    // res.data.paymentApplyDto.remitFile 处理为 el-upload 组件的 file-list
                    res.data.paymentApplyDto.remitFile = res.data.paymentApplyDto.remitFile.map((item) => ({
                        name: item.fileName,
                        url: item.fileUrl
                    }));
                }

                this.prepaymentRechargeModificationForm = Object.assign({}, this.prepaymentRechargeModificationForm, res.data);
                this.$nextTick(() => {
                    if (this.prepaymentRechargeModificationForm.approveList.length > 0) {
                        this.$refs.AuditAndFlowRecords.timeFns(this.prepaymentRechargeModificationForm.approveList);
                    }
                });
            }
            this.prepaymentRechargeModificationLoading = false;
        },
        /**
         * 图片预览
         * @param file
         */
        handlePictureCardPreview(file) {
            this.viewerVisible = true;
            this.viewerList = [];
            this.viewerList.push(file.url);
        },
        /**
         * 文件上传移除
         * @param uploadFile
         */
        handleRemovePrepaymentRechargeModification(uploadFile) {
            this.prepaymentRechargeModificationForm.paymentApplyDto.remitFile = this.prepaymentRechargeModificationForm.paymentApplyDto.remitFile.filter((item) => item.url !== uploadFile.url);
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.prepaymentRechargeModificationForm.validate(async (valid) => {
                if (valid) {
                    this.prepaymentRechargeModificationLoading = true;
                    // approval 审批
                    if (this.prepaymentRechargeModificationStatus === 'approval') {
                        let params = {
                            id: this.prepaymentRechargeModificationId,
                            approveStatus: this.prepaymentRechargeModificationForm.approveStatus,
                            rebateAmount: this.prepaymentRechargeModificationForm.paymentApplyDto.rebateAmount || 0.0
                        };
                        if (this.prepaymentRechargeModificationForm.approveStatus === '2') {
                            params.approveIdea = this.prepaymentRechargeModificationForm.approveIdea;
                        }
                        const res = await paymentOrderApproval.approvalPaymentOrder(params);
                        if (res.code === 200) {
                            this.$message.success('操作成功');
                            this.$emit('getList');
                            this.closeVisible();
                        }
                    }
                    // modify 修改
                    else if (this.prepaymentRechargeModificationStatus === 'modify') {
                        // this.prepaymentRechargeModificationForm.paymentApplyDto.remitFile 处理 url -> fileUrl name -> fileName
                        this.prepaymentRechargeModificationForm.paymentApplyDto.remitFile = this.prepaymentRechargeModificationForm.paymentApplyDto.remitFile.map((item) => ({
                            fileUrl: item.url,
                            fileName: item.name
                        }));
                        let params = {
                            applyWay: '2', // 1-货主发起 2-承运商发起
                            businessType: this.prepaymentRechargeModificationForm.paymentApplyDto?.businessType || '',
                            id: this.prepaymentRechargeModificationForm.paymentApplyDto?.id || '',
                            applyNo: this.prepaymentRechargeModificationForm.paymentApplyDto?.applyNo || '',
                            companyId: this.prepaymentRechargeModificationForm.paymentApplyDto?.companyId || '',
                            companyName: this.prepaymentRechargeModificationForm.paymentApplyDto?.companyName || '',
                            carrierId: this.prepaymentRechargeModificationForm.paymentApplyDto?.carrier?.id || '',
                            receiveCompany: this.prepaymentRechargeModificationForm.paymentApplyDto?.receiveCompany || '',
                            advanceAccount: this.prepaymentRechargeModificationForm.paymentApplyDto?.advanceAccount || '',
                            remitAmount: this.prepaymentRechargeModificationForm.paymentApplyDto?.remitAmount || 0,
                            remitTime: this.prepaymentRechargeModificationForm.paymentApplyDto?.remitTime || '',
                            remitFile: JSON.stringify(this.prepaymentRechargeModificationForm.paymentApplyDto?.remitFile || []),
                            remark: this.prepaymentRechargeModificationForm.paymentApplyDto?.remark || '',
                            payMethod: '1'
                        };
                        const res = await paymentDoc.applyPayment(params);
                        if (res.code === 200) {
                            this.$message.success('修改成功');
                            this.$emit('getList');
                            this.closeVisible();
                        }
                    }
                    this.prepaymentRechargeModificationLoading = false;
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
.number__unit__element {
    position: relative;

    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
