<template>
  <div class="app-container" style="width: 100%;">
    <el-card class="box-card Botm" style="margin:10px">
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="form_130">
        <TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
          <el-form-item label="企业名称" prop="enterpriseName">
            <el-input v-model="queryParams.enterpriseName" class="form_225" clearable placeholder="请输入企业名称" />
          </el-form-item>
          <el-form-item label="拼音码" prop="pinyin">
            <el-input v-model="queryParams.pinyin" class="form_225" clearable placeholder="请输入拼音码" />
          </el-form-item>
          <el-form-item label="自编码" prop="manufacturerSelfNo">
            <el-input v-model="queryParams.manufacturerSelfNo" class="form_225" clearable placeholder="请输入自编码" />
          </el-form-item>
          <el-form-item label="统一社会信用代码" prop="businessLicenseCode">
            <el-input v-model="queryParams.businessLicenseCode" class="form_225" clearable placeholder="请输入统一社会信用代码" />
          </el-form-item>
          <el-form-item label="创建来源" prop="source">
            <el-select v-model="queryParams.source" class="form_225" placeholder="请选择创建来源">
              <el-option label="全部" value=""/>
              <el-option label="手工录入" value="1"/>
              <el-option label="智能录入" value="2"/>
            </el-select>
          </el-form-item>
          <el-form-item v-show="showSearch" label="审核状态" prop="status">
            <el-select v-model="queryParams.status" class="form_225" placeholder="请选择审核类型">
              <el-option v-for="item in reviewStatus" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-show="showSearch" label="禁用状态" prop="customLabel">
            <el-select v-model="queryParams.customLabel" class="form_225" placeholder="请选择禁用状态">
              <el-option v-for="item in delFlagList" :key="item.value" :label="item.name" :popper-append-to-body="false"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-show="showSearch" label="创建日期" prop="createDate">
            <div class="box_date">
              <el-date-picker v-model="queryParams.createDate" class="form_225" end-placeholder="结束日期" range-separator="至"
                start-placeholder="开始日期" type="daterange" />
            </div>

          </el-form-item>
          <el-form-item v-show="showSearch" label="修改日期" prop="updateDate">
            <div class="box_date">
              <el-date-picker v-model="queryParams.updateDate" class="form_225" end-placeholder="结束日期" range-separator="至"
                start-placeholder="开始日期" type="daterange" />
            </div>
          </el-form-item>
        </TopTitle>
      </el-form>
    </el-card>
    <el-card class="box-card" style="margin: 10px;">
      <div style="display:flex;justify-content: space-between;">
        <el-button style="margin-bottom:10px" type="primary" @click="() => handleAdd(undefined, 'add')">新增
        </el-button>
        <RightToptipBarV2 className="manufacturer_manufacturerManagement" @handleRefresh="getList" />
      </div>
      <div v-loading="loading" style="min-height:200px">
        <DragTableColumn v-if="reviewStatus.length && delFlagList.length" v-model:queryParams="queryParams"
          :columns="columns" :getList="getList" :row-style="tableRowStyle" :tableData="list"
          className="manufacturer_manufacturerManagement">
          <template v-slot:operate="{ scopeData }">
            <el-button link type="primary" @click="handleDetail(scopeData.row, 'detail')"><img
                src="@/assets/icons/detail.png" style="margin-right:5px" />详情
            </el-button>
            <el-button :disabled="scopeData.row.status == '1' || scopeData.row.status == '2'" link type="primary"
              @click="handleDetail(scopeData.row, 'edit')"><img src="@/assets/icons/update.png"
                style="margin-right:5px" />编辑
            </el-button>
            <el-button :disabled="isDeleteAuthority(scopeData.row.createBy.id, ['1', '4', '6'], scopeData.row.status)"
              link type="danger" @click="handleDelete(scopeData.row)">
              <img src="@/assets/icons/delete.png" style="margin-right:5px" />删除
            </el-button>
            <el-dropdown>
              <el-button link style="margin-top: 3px;" type="primary">更多
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :disabled="scopeData.row.status !== '6'" style="color:#e6a23c"
                    @click="handleDetail(scopeData.row, 'draft')"><img src="@/assets/icons/draft.png"
                      style="margin-right:5px;" />查看草稿
                  </el-dropdown-item>

                  <el-dropdown-item style="color:#67c23a" @click="handleLog(scopeData.row)"><img
                      src="@/assets/icons/review.png" style="margin-right:5px" />操作记录
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scopeData.row.customLabel == '2'" style="color:#909399"
                    @click="handleDisabled(scopeData.row, '1')"><img src="@/assets/icons/disabled.png"
                      style="margin-right:5px" />禁用
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scopeData.row.customLabel == '1'" style="color:#909399"
                    @click="handleDisabled(scopeData.row)"><img src="@/assets/icons/disabled.png"
                      style="margin-right:5px" />解禁
                  </el-dropdown-item>

                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>

        </DragTableColumn>
      </div>
      <div style="float: right;">
        <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total"
          @pagination="getList" />
      </div>
    </el-card>
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog v-if="open" v-model="open" :append-to-body="ture" :before-close="() => submitForm(3)"
      :show-close="!modalLoading" :title="title" destroy-on-close width="90%">
      <div v-loading="modalLoading">
        <el-button v-if="modalType == 'detail'" style="margin-top: 3px;position: absolute;right: 50px;top: 10px;"
          @click="handleFileList">附件列表
        </el-button>

        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item name="1" title="基本信息">
            <template #title>
              <span class="col_title">基本信息</span>
            </template>
            <el-form ref="formRef" :model="form" :rules="rules" label-width="auto"
              style="margin-top: 0px;padding-right: 20px;">
              <div class="box_2">
                <el-form-item label="企业名称" prop="enterpriseName">
                  <el-input v-model="form.enterpriseName" :disabled="modalType == 'detail'" clearable maxlength="100"
                    placeholder="请输入企业名称" show-word-limit />
                </el-form-item>
                <el-form-item label="统一社会信用代码" prop="businessLicenseCode">
                  <el-input v-model="form.businessLicenseCode" :disabled="modalType == 'detail'" clearable maxlength="100"
                    placeholder="请输入统一社会信用代码" show-word-limit @blur="handleSocialCreditCodeBlur" />
                </el-form-item>
              </div>
              <div class="box">

                <el-form-item label="营业执照经营范围" prop="businessScope">
                  <el-input v-model="form.businessScope" :disabled="modalType == 'detail'" clearable maxlength="500"
                    placeholder="请输入营业执照经营范围" show-word-limit />
                </el-form-item>

                <el-form-item label="营业执照发证日期" prop="enterpriseStartTime">
                  <el-date-picker v-model="form.enterpriseStartTime" :disabled="modalType == 'detail'"
                    :disabled-date="disabledDateAfter" placeholder="请选择营业执照发证日期" style="width: 100%;" type="date" />
                </el-form-item>

                <el-form-item label="营业执照有效期" prop="businessLicenseExpiredTime">
                  <el-date-picker v-model="form.businessLicenseExpiredTime" :disabled="modalType == 'detail'"
                    :disabled-date="disabledDate" placeholder="请选择营业执照有效期" style="width: 100%;" type="date" />
                </el-form-item>
                <el-form-item label="发证机关" prop="enterpriseOffice">
                  <el-input v-model="form.enterpriseOffice" :disabled="modalType == 'detail'" clearable maxlength="100"
                    placeholder="请输入发证机关" show-word-limit />
                </el-form-item>
                <!-- <el-form-item label="拼音码" prop="pinyin" v-if="modalType == 'edit'">
                      <el-input v-model="form.pinyin" placeholder="请输入拼音码"
                          :disabled="modalType == 'detail'" />
                  </el-form-item> -->
                <el-form-item label="自编码" prop="manufacturerSelfNo">
                  <el-input v-model="form.manufacturerSelfNo" placeholder="请输入自编码" />
                </el-form-item>
                <el-form-item label="备注" prop="remark" style="width:200%">
                  <el-input v-model="form.remark" :disabled="modalType == 'detail'" :rows="1" clearable maxlength="100"
                    placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
              </div>

              <el-form-item label="营业执照上传" prop="businessLicensePicturt">
                <el-upload ref="businessLicensePicturtRef" v-model:file-list="form.businessLicensePicturt"
                  :action="uploadUrl" :before-upload="(file) => beforeFile(file)"
                  :data="{ fileType: 'image', fjType: '基本信息', zhType: '生产厂家' }" :disabled="modalType == 'detail'"
                  :headers='headers' :on-preview="handlePictureCardPreview"
                  :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 1)"
                  class="upload-demo" drag list-type="picture-card" multiple>
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-upload>

              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item class="step2" name="2" title="生产许可证">
            <template #title>
              <span class="col_title">生产许可证</span>
            </template>
            <div class="btn_cmt">
              <el-button :type="tabKey == '1' ? 'primary' : 'default'" @click="() => handleTabClick('1')">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>药品</span></el-button>
              <el-button :type="tabKey == '3' ? 'primary' : 'default'" @click="() => handleTabClick('3')">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>一类</span></el-button>
              <el-button :type="tabKey == '4' ? 'primary' : 'default'" @click="() => handleTabClick('4')">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>二类</span></el-button>
              <el-button :type="tabKey == '2' ? 'primary' : 'default'" @click="() => handleTabClick('2')">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>三类</span></el-button>
              <el-button :type="tabKey == '5' ? 'primary' : 'default'" @click="() => handleTabClick('5')">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>食品</span></el-button>
              <el-button :type="tabKey == '6' ? 'primary' : 'default'" class="last-btn"
                @click="() => handleTabClick('6')">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>消杀</span></el-button>
            </div>
            <el-form :key="tabKey" ref="modalTableListRef" :model="licensedata" :rules="modalTableListRules"
              label-width="auto" style="margin-top: 0px;padding-right: 20px;">
              <div class="box">
                <el-form-item label="许可证编号" prop="licenseNo">
                  <el-input v-model="licensedata.licenseNo" :disabled="modalType == 'detail'" clearable maxlength="100"
                    placeholder="请输入许可证编号" show-word-limit />
                </el-form-item>
                <el-form-item label="生产地址" prop="licenseAddress">
                  <el-input v-model="licensedata.licenseAddress" :disabled="modalType == 'detail'" clearable
                    maxlength="100" placeholder="请输入生产地址" show-word-limit />
                </el-form-item>
                <el-form-item label="生产范围" prop="licenceScopesName">
                  <el-select v-if="isquiry()" v-model="licensedata.licenceScopesName" :disabled="modalType == 'detail'"
                    multiple placeholder="请选择生产范围" style="width:100%"
                    @click=" modalType == 'detail' ? null : () => handleLicenseScope(licensedata)">
                    <el-option v-for="item in licenseScopeListData" :key="item.id" :label="item.valueName"
                      :value="item.id" />
                  </el-select>
                  <el-input v-else v-model="licensedata.licenceScopesName" :disabled="modalType == 'detail'" clearable
                    maxlength="100" placeholder="请选择生产范围" readonly show-word-limit
                    @click="() => handleLicenseScope(licensedata)" />
                </el-form-item>

                <el-form-item label="发证日期" prop="licenseStartTime">
                  <el-date-picker v-model="licensedata.licenseStartTime" :disabled="modalType == 'detail'"
                    :disabled-date="disabledDateAfter" placeholder="请选择发证日期" style="width: 100%;" type="date" />
                </el-form-item>

                <el-form-item label="有效期" prop="licenseValidity">
                  <el-date-picker v-model="licensedata.licenseValidity" :disabled="modalType == 'detail'"
                    :disabled-date="disabledDate" placeholder="请选择有效期" style="width: 100%;" type="date" />
                </el-form-item>
                <el-form-item label="发证机关" prop="licenseOffice">
                  <el-input v-model="licensedata.licenseOffice" :disabled="modalType == 'detail'" clearable
                    maxlength="100" placeholder="请输入发证机关" show-word-limit />
                </el-form-item>
                <el-form-item :label="tabKey == '1' ? '生产（企业）负责人' : '生产负责人'" prop="licenseDirector">
                  <el-input v-model="licensedata.licenseDirector" :disabled="modalType == 'detail'" clearable
                    maxlength="100" placeholder="请输入生产负责人" show-word-limit />
                </el-form-item>
                <el-form-item label="质量负责人" prop="qualityDirector">
                  <el-input v-model="licensedata.qualityDirector" :disabled="modalType == 'detail'" clearable
                    maxlength="100" placeholder="请输质量负责人" show-word-limit />
                </el-form-item>
              </div>
              <el-form-item label="许可证图片" prop="licenseImg">
                <el-upload ref="licenseImgRef" v-model:file-list="licensedata.licenseImg" :action="uploadUrl"
                  :before-upload="(file) => beforeFile(file)"
                  :data="{ fileType: 'image', fjType: '生产许可证', zhType: '生产厂家' }" :disabled="modalType == 'detail'"
                  :headers='headers' :on-exceed="(files) => handleExceed(files, licenseImgRef)"
                  :on-preview="handlePictureCardPreview"
                  :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, undefined, 2)"
                  class="upload-demo" drag list-type="picture-card">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-upload>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item name="3" title="GMP证书">
            <template #title>
              <span class="col_title">GMP证书</span>
            </template>
            <div style="margin-bottom: 20px;">
              <el-button :disabled="modalType == 'detail'" type="primary" @click="handleAdd_GMP">新增
              </el-button>
              <el-button :disabled="modalType == 'detail' || !chooseList_GMP.length" type="danger"
                @click="handleDelete_GMP">删除
              </el-button>
            </div>
            <el-table ref="multipleTableRef_GMP" :data="modalTableList_GMP" border
              @selection-change="handleSelectionChange_GMP">
              <el-table-column align="center" min-width="55" type="selection" />
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书编号" min-width="100">
                <template #default="scope">
                  <div v-clickOutside="() => handleClickOutside(scope, 'isShowgmpCertificateNo')">
                    <p v-show="!scope.row.isShowgmpCertificateNo" style="height: 42px;line-height:42px"
                      @click="handleInputEdit(scope, 'gmpCertificateNo')">
                      {{ scope.row.gmpCertificateNo || '请输入证书编号' }}</p>
                    <el-input v-show="scope.row.isShowgmpCertificateNo" v-model="scope.row.gmpCertificateNo" clearable
                      maxlength="100" placeholder="请输入证书编号" show-word-limit />
                  </div>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书地址" min-width="100">
                <template #default="scope">
                  <div v-clickOutside="() => handleClickOutside(scope, 'isShowgmpCertificateAddress')">
                    <p v-show="!scope.row.isShowgmpCertificateAddress" style="height: 42px;line-height:42px"
                      @click="handleInputEdit(scope, 'gmpCertificateAddress')">
                      {{ scope.row.gmpCertificateAddress || '请输入证书地址' }}</p>
                    <el-input v-show="scope.row.isShowgmpCertificateAddress" v-model="scope.row.gmpCertificateAddress"
                      clearable maxlength="100" placeholder="请输入证书地址" show-word-limit />
                  </div>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书图片" min-width="100">
                <template #default="scope">
                  <div style="display:flex;justify-content: center;">
                    <p v-if="scope.row.gmpCertificatePicture.length"
                      style="color:#2A76F8;cursor: pointer;width:150px;line-height:42px;white-space: nowrap;overflow:hidden;text-overflow: ellipsis;"
                      @click="() => handlePictureCardPreview(scope.row.gmpCertificatePicture[0].url)">
                      {{
                        scope.row.gmpCertificatePicture[0].name
                      }}
                    </p>
                    <el-upload v-if="modalType !== 'detail'" ref="gmpCertificatePictureRef"
                      v-model:file-list="scope.row.gmpCertificatePicture" :action="uploadUrl"
                      :before-upload="(file) => beforeFile(file)"
                      :data="{ fileType: 'image', fjType: 'GMP证书', zhType: '生产厂家' }" :headers='headers' :limit="1"
                      :on-exceed="(files) => handleExceed(files, gmpCertificatePictureRef)"
                      :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, scope.$index, 3)"
                      :show-file-list="false" accept=".jpg,.jpeg,.png,.gif.JPG,.JPEG,.PNG" class="upload-demo">
                      <template #trigger>

                        <el-button type="primary">
                          <el-icon style="margin-right:5px;">
                            <UploadFilled />
                          </el-icon>
                          上传
                        </el-button>
                      </template>
                    </el-upload>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="证书有效期" min-width="100">
                <template #default="scope">
                  <div v-clickOutside="() => handleClickOutside(scope, 'isShowgmpExpiredTime')">
                    <p v-show="!scope.row.isShowgmpExpiredTime" style="height: 42px;line-height:42px"
                      @click="handleInputEdit(scope, 'gmpExpiredTime')">
                      {{
                        (scope.row.gmpExpiredTime ?
                          moment(scope.row.gmpExpiredTime).format('YYYY-MM-DD') :
                          undefined) || '请选择有效期'
                      }}</p>
                    <el-date-picker v-show="scope.row.isShowgmpExpiredTime" v-model="scope.row.gmpExpiredTime"
                      :disabled-date="disabledDate"
                      :style="[{ width: '100%' }, { display: scope.row.isShowgmpExpiredTime ? 'block' : 'none' }]"
                      clearable placeholder="请选择有效期" type="date" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" label="证书授权范围" min-width="100">
                <template #default="scope">
                  <div v-clickOutside="($event) => handleClickOutside(scope, 'isShowgmpCertificateScope', $event)">
                    <p v-show="!scope.row.isShowgmpCertificateScope" style="height: 42px;line-height: 42px;"
                      @click="handleInputEdit(scope, 'gmpCertificateScope')">
                      {{
                        filterArr(licenseScopeList, 'id', scope.row.gmpCertificateScope) ||
                        '请选择证书授权范围'
                      }} </p>
                    <div v-show="scope.row.isShowgmpCertificateScope">
                      <el-select v-model="scope.row.gmpCertificateScope" :popper-append-to-body="false" multiple
                        placeholder="请选择证书授权范围" popper-class="selectClass" style="width:100%">
                        <el-option v-for=" item  in  licenseScopeList " :key="item.id" :label="item.valueName"
                          :value="item.id" />
                      </el-select>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="4" title="附件列表">
            <template #title>
              <span class="col_title">附件列表</span>
            </template>
            <div style="margin-bottom: 20px;">
              <el-button :disabled="modalType == 'detail'" type="primary" @click="handleGetFileList">获取附件列表
              </el-button>
            </div>
            <el-table ref="multipleTableRef_file" v-loading="file_loading" :data="modalTableList_file" border>
              <el-table-column align="center" label="序号" width="50">
                <template #default="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="类型" width="150">
                <template #default="scope">
                  <span>{{ formDict(manufacturerType, scope.row.smallType) }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="文件类型" width="250">
                <template #default="scope">
                  <span>{{ formDict(directoryFileName, scope.row.categoryName) }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="文件名称" width="400">
                <template #default="scope">
                  <div>
                    <div v-if="scope.row.fileList">
                      <span v-for="(item, index) in scope.row.fileList" :key="item.uid" class="fileName_t"><span
                          @click="() => handlePictureCardPreview(scope.row.fileList[index].url)">{{
                            item.name
                          }}</span><span class="fileName_t_icon"
                          @click="() => handleDeleteFile(scope.$index, index)"><el-icon>
                            <Delete />
                          </el-icon></span></span>
                    </div>
                    <span v-else>请上传</span>
                  </div>

                </template>
              </el-table-column>
              <el-table-column align="center" label="文件上传" width="130">
                <template #default="scope">
                  <el-upload v-model:file-list="scope.row.fileList" :action="uploadUrl"
                    :before-upload="(file) => beforeFile(file)"
                    :data="{ fileType: 'image', fjType: formDict(directoryFileName, scope.row.categoryName), zhType: '生产厂家' }"
                    :headers='headers' :limit="scope.row.isMultiPage === '1' ? 999 : 1"
                    :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, scope.$index, 4)"
                    :show-file-list="false" accept=".jpg,.jpeg,.png,.gif.JPG,.JPEG,.PNG" class="upload-demo">
                    <template #trigger>
                      <el-button type="primary">
                        <el-icon style="margin-right:5px;">
                          <UploadFilled />
                        </el-icon>
                        上传
                      </el-button>
                    </template>
                  </el-upload>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="是否必传" width="100">
                <template #default="scope">
                  <span>{{
                    scope.row.isUpload === '1' ? '是' : scope.row.isUpload === '0' ? '否' : '--'
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="是否多张" width="100">
                <template #default="scope">
                  <span>{{
                    scope.row.isMultiPage === '1' ? '是' : scope.row.isMultiPage === '0' ? '否' :
                    '--'
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column :show-overflow-tooltip="true" align="center" label="备注" min-width="300">
                <template #default="scope">
                  <div v-clickOutside="() => handleClickOutside(scope, 'isShowremark')">
                    <p v-show="!scope.row.isShowremark" style="height: 42px;line-height:42px"
                      @click="handleInputEdit(scope, 'remark')">
                      {{ scope.row.remark || '请输入备注' }}</p>
                    <el-input v-show="scope.row.isShowremark" v-model="scope.row.remark" clearable maxlength="100"
                      placeholder="请输入备注" show-word-limit />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>

      </div>
      <template v-if="!modalLoading" #footer>
        <div class="dialog-footer">
          <el-button @click="() => submitForm(3)">取 消</el-button>
          <el-button v-if="(modalType !== 'detail') && (modalType !== 'edit')" type=""
            @click="() => submitForm('save')">保存草稿
          </el-button>
          <el-button v-if="modalType !== 'detail'" type="primary" @click="() => submitForm('submit')">提交审核
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-if="prohibitionAndLiftingVisible" v-model="prohibitionAndLiftingVisible"
      :before-close="prohibitionAndLiftingClose" :title="prohibitionAndLiftingTitle" width="30%">
      <el-form :model="form" label-width="100px">
        <el-form-item label="企业名称：">
          <span>{{ customLabelValue.enterpriseName }}</span>
        </el-form-item>
        <el-form-item label="自编码：">
          <span>{{ customLabelValue.enterpriseName }}</span>
        </el-form-item>
        <el-form-item label="原因：">
          <el-input v-model="idea" placeholder="请填写原因" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="prohibitionAndLiftingVisible = false">取消</el-button>
          <el-button type="primary" @click="prohibitionAndLiftingSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-drawer v-if="fileListVisible" v-model="fileListVisible" v-loading="true"
      :before-close="() => fileListVisible = false" direction="rtl" size="60%" title="附件列表"
      @selection-change="handleDetailFileListChoose">
      <div style="display:flex;justify-content:end">
        <el-button :disabled="!fileListChooseList.length" style="margin-top: 10px; margin-bottom:20px;margin-right:20px"
          type="primary" @click="handleExportFile">导出
        </el-button>
      </div>
      <el-table v-loading="fileDraLoading" :data="fileListData" border @selection-change="handleDetailFileListChoose">
        <el-table-column align="center" min-width="55" type="selection" />
        <el-table-column :formatter="(row) => row.cardName || '--'" :show-overflow-tooltip="true" align="center"
          label="证件名称" prop="cardName" show-overflow-tooltip />
        <el-table-column :show-overflow-tooltip="true" align="center" label="编号" prop="fileCoder" show-overflow-tooltip />
        <el-table-column :show-overflow-tooltip="true" align="center" label="证件图片" min-width="280" prop="fileName"
          show-overflow-tooltip>
          <template #default="scope">
            <el-button link type="primary" @click="() => handlePictureCardPreview(scope.row.fileUrl)">{{
              scope.row.fileName
            }}
            </el-button>
          </template>
        </el-table-column>
        >
        <el-table-column :formatter="(row) => moment(row.fileEndDate).format('YYYY-MM-DD')" :show-overflow-tooltip="true"
          align="center" label="有效期" prop="fileEndDate" show-overflow-tooltip />
        <el-table-column :formatter="(row) => row.range || '--'" :show-overflow-tooltip="true" align="center" label="范围"
          prop="range" show-overflow-tooltip />
        <el-table-column
          :formatter="(row) => (row.busType == '1' || row.busType == '2') ? row.busType == '1' ? '普药' : row.busType == '2' ? '特药' : '--' : row.busType"
          :show-overflow-tooltip="true" align="center" label="类型" prop="busType" show-overflow-tooltip />
        <el-table-column :formatter="(row) => row.remark || '--'" :show-overflow-tooltip="true" align="center" label="备注"
          prop="remark" show-overflow-tooltip />
        <el-table-column :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')" :show-overflow-tooltip="true"
          align="center" label="归档时间" prop="createDate" show-overflow-tooltip />
      </el-table>
      <div style="display:flex;justify-content:end;margin-top:20px">
        <pagination v-show="fileListDataTotal > 0" v-model:limit="fileListDataPage.size"
          v-model:page="fileListDataPage.current" :total="fileListDataTotal" @pagination="handleFileList" />
      </div>
    </el-drawer>
    <el-dialog v-if="licenseScopeVisible" v-model="licenseScopeVisible" :before-close="() => licenseScopeVisible = false"
      style="padding:0 30px" title="生产范围" width="60%">
      <el-table v-loading="lisloading" :data="licenseScopeListData" :select-on-indeterminate="true"
        :tree-props="{ children: 'children' }" border row-key="id" style="width: 100%;margin-bottom: 20px"
        @row-click="handleRowClick">
        <el-table-column align=center label="选择" min-width="140">
          <template #default="scope">
            <el-checkbox v-model="checkedKeys" :label="scope.row.id"
              @change="(isChoose) => handleCheckChange(isChoose, scope.row)">&nbsp;
            </el-checkbox>
          </template>
        </el-table-column>
        <!-- <el-table-column type="selection" width="55" :reserve-selection="true"
              :selectable="selectable"></el-table-column> -->
        <!-- <el-table-column type="selection" width="55" align="center"> </el-table-column> -->
        <el-table-column align="center" label="质量名称" min-width="250" prop="massName">
        </el-table-column>
        <el-table-column align="center" label="质量编号" min-width="180" prop="massNo">
        </el-table-column>
        <el-table-column align="center" label="属性分类" prop="massType">
          <template #default="scope">
            {{ scope.row.isStop == 1 ? "新范围" : "旧范围" }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="isStop">
          <template #default="scope">
            {{ scope.row.isStop == 1 ? "正常" : "停用" }}
          </template>
        </el-table-column>
        <el-table-column :formatter="row => row.remark ? row.remark : '--'" align="center" label="备注" min-width="150px"
          prop="remark" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="licenseScopeVisible = false">取消</el-button>
          <el-button type="primary" @click="lisSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <Review v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" :reviewVisible="reviewVisible" />
    <detail v-if="detailOpen" :beforeClose="() => detailOpen = false" :detailData="detailData" :detailOpen="detailOpen" />
    <viewImg v-if="uploadVisible" :beforeClose="() => uploadVisible = false" :src="uploadViewImgUrl"
      :visible="uploadVisible" />
  </div>
</template>

<script setup>

import {getCurrentInstance, onMounted, reactive, ref, toRefs, watch} from 'vue'
import {ArrowDown, Delete, Plus, UploadFilled} from '@element-plus/icons-vue'
import moment from 'moment'
import {ElMessageBox, genFileId} from 'element-plus'
import manufacturerManagement from '@/api/erp/manufacturerManagement'
import tool from '@/utils/tool';
import {uuid} from 'vue-uuid';
import custom from '@/api/erp/custom'
import Review from './review.vue'
import TopTitle from '@/components/topTitle'
import isDeleteAuthority from '@/utils/isDeleteAuthority'
import detail from './detail.vue'

const { proxy } = getCurrentInstance();
//  const { sys_normal_disable } = proxy.useDict("sys_normal_disable");
const typeDict = {
  '1': '药品',
  '2': '三类',
  '3': '一类',
  '4': '二类',
  '5': '食品',
  '6': '消杀',
}
const titleDict = {
  add: "新增",
  edit: "编辑",
  detail: "详情",
  draft: "草稿"
}
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
  Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
  ContentType: 'multipart/form-data',
  clientType:'pc',
}
const detailOpen = ref(false)
const detailData = ref({})
const fileUrl = process.env.VUE_APP_API_fileUrl
const reviewVisible = ref(false)
const lisloading = ref(false)
const licenseScopeVisible = ref(false)
const licenseScopeListData = ref([])
const fileListVisible = ref(false)
const fileListData = ref([])
const fileListDataTotal = ref(0)
const fileDraLoading = ref(false)
const fileListChooseList = ref([])
const uploadVisible = ref(false)
const uploadVisibleFile = ref(false)
const uploadViewImgUrl = ref("")
const uploadViewImgUrlFile = ref("")
const delFlagList = ref([]) // 禁用状态list
const modalType = ref("")
const prohibitionAndLiftingVisible = ref(false)
const prohibitionAndLiftingTitle = ref("")
const list = ref([]);
const open = ref(false);
const loading = ref(false);
const showSearch = ref(false)
const total = ref(0);
const title = ref("");
const reviewStatus = ref([])
const activeNames = ref(['1'])
const tabKey = ref('1')
const modalTableList = ref([])
const licensedata = ref({ licenseImg: [], licenceScopes: [], type: '1' })
const modalTableList_GMP = ref([])
const chooseList = ref([])
const chooseList_GMP = ref([])
const licenseScopeList = ref([])
const idea = ref("")
const customLabelValue = ref({})
const modalLoading = ref(false)
const massRangeOption = ref([])
const shopType = ref([])
const chooseLicenseScope = ref([])
const checkedKeys = ref([])
const licenseScopeRow = ref({})
const deleteGmpList = ref([])
const reviewRow = ref({})
// 附件列表
const modalTableList_file = ref([])
const file_loading = ref(false)
const manufacturerType = ref([])
const directoryFileName = ref([])
const data = reactive({
  form: {},
  queryParams: {
    current: 1,
    size: 10,
  },
  fileListDataPage: {
    current: 1,
    size: 10,
  },

  rules: {
    enterpriseName: [{ required: true, message: "企业名称不能为空", trigger: "blur" }],
    manufacturerSelfNo: [
      { required: true, message: "自编码不能为空", trigger: "blur" },
      { pattern: /^SC\d{4}(-\d{2})?$/, message: '请输入SCxxxx或者SCxxxx-xx,其中xx为纯数字', trigger: "blur" }
    ],
    // businessScope: [{ required: true, message: "营业执照经营范围不能为空", trigger: "blur" }],
    businessLicenseCode: [{ required: true, message: "统一社会信用代码不能为空", trigger: "blur" }],
    businessLicenseExpiredTime: [{ required: true, message: "营业执照有效期不能为空", trigger: "change" }],
    enterpriseOffice: [{ required: true, message: "发证机关不能为空", trigger: "blur" }],
    businessLicensePicturt: [{ required: true, message: "营业执照不能为空", trigger: "change" }],
    // remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
  },
  modalTableListRules: {
    licenseNo: [{ required: true, message: "许可证编号不能为空", trigger: "blur" }],
    licenseAddress: [{ required: true, message: "生产地址不能为空", trigger: "blur" }],
    licenceScopesName: [{ required: true, message: "生产范围不能为空", trigger: "change" }],
    licenseStartTime: [{ required: true, message: "发证日期不能为空", trigger: "change" }],
    licenseValidity: [{ required: true, message: "有效期不能为空", trigger: "change" }],
    licenseOffice: [{ required: true, message: "发证机关不能为空", trigger: "blur" }],
    licenseDirector: [{ required: true, message: "生产负责人不能为空", trigger: "blur" }],
    qualityDirector: [{ required: true, message: "质量负责人不能为空", trigger: "blur" }],
    licenseImg: [{ required: true, message: "许可证图片不能为空", trigger: "change" }],
  }
});
const businessLicensePicturtRef = ref(null)
const licenseImgRef = ref(null)
const gmpCertificatePictureRef = ref(null)
const isClearFileWaring = ref(true)
const { queryParams, form, rules, fileListDataPage, modalTableListRules } = toRefs(data)

watch(() => modalTableList.value, (newValue, oldValue) => {
  if (!open.value || !isClearFileWaring.value) return
  if (modalTableList_file.value?.length) {
    ElMessageBox.confirm("修改会清空附件列表？", '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(res => {
      modalTableList_file.value = []
    }).catch(() => {

    })
  }
}, { deep: true })
const tableRowStyle = ({ row, rowIndex }) => {
  if (row.status == '6' && row.customLabel == '2') {  // 草稿
    return {
      color: '#e6a23c'
    }
  } else if (row.status == '1' && row.customLabel == '2') {  // 待审核
    return {
      color: '#409eff'
    }
  } else if (row.status == '2' && row.customLabel == '2') {  //审核中
    return {
      color: '#67c23a'
    }
  } else if (row.status == '4' && row.customLabel == '2') {  //已驳回
    return {
      color: '#ff4800'
    }
  } else if (row.customLabel == '1') {   // 禁用
    return {
      color: '#f56c6c'
    }
  }
}
const isquiry = () => {
  if (tabKey.value == '1' || tabKey.value == '6') {
    return true
  }
  return false
}
const handleDeleteFile = (scopeIndex, index) => {
  modalTableList_file.value[scopeIndex].fileList.splice(index, 1)
  proxy.msgSuccess('删除成功！')
}
// 获取附件列表
const handleGetFileList = () => {
  isClearFileWaring.value = false
  file_loading.value = true
  let index = 0
  const data = licensedata.value
  const regKey = ["licenseNo", "licenseAddress", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg"]
  regKey.forEach(item => {
    if (Array.isArray(data[item]) ? data[item]?.length : data[item]) {
      index++
    }

  })
  if (index != 0) {
    let modalTableListNew = []
    if (modalTableList.value?.length) {
      modalTableList.value?.forEach(val => {
        console.log(val);
        if (val.type === licensedata.value?.type) {
          modalTableListNew = modalTableList.value?.filter(item => item.type !== licensedata.value?.type)
          modalTableListNew = [...modalTableListNew, licensedata.value]
        } else {
          modalTableListNew = [...modalTableListNew, licensedata.value]
        }
      })
    } else {
      modalTableListNew = [licensedata.value]
    }

    modalTableList.value = modalTableListNew
  }
  const ids = []
  let smallTypes = []
  modalTableList.value?.forEach(item => {
    if (item.licenseNo) {
      if (item.type == '1' && smallTypes.indexOf(1) == -1) {
        smallTypes.push(1)
      } else if ((item.type == '2' || item.type == '3' || item.type == '4') && smallTypes.indexOf(2) == -1) {
        smallTypes.push(2)
      } else if (item.type == '5' && smallTypes.indexOf(3) == -1) {
        smallTypes.push(3)
      } else if (item.type == '6' && smallTypes.indexOf(4) == -1) {
        smallTypes.push(4)
      }
    }
  })
  console.log(smallTypes)
  // manufacturerType.value?.forEach(item => {
  //   if (ids.includes(item.name)) {
  //     smallType.push(item.value)
  //   }
  // })
  manufacturerManagement.getFileList({
    bigType: '1',
    smallType: smallTypes?.length ? smallTypes.toString() : undefined,
    isFile: 1,
    size: 1000
  }).then(res => {
    if (res.code == 200) {
      modalTableList_file.value = res?.data?.records

    } else {
      proxy.msgError(res.msg)
    }
    file_loading.value = false
  }).finally(() => {
    file_loading.value = false
  })
  isClearFileWaring.value = true
  // modalTableList_file.value = []
}
const handleSocialCreditCodeBlur = () => {
  if (form.value.businessLicenseCode ?? false) {
    manufacturerManagement.checkLicenseCode({ code: form.value.businessLicenseCode, id: form.value?.id }).then(res => {
      if (res.data) {
        proxy.msgError('统一社会信用代码已经存在！请重新输入')
        form.value.businessLicenseCode = undefined
      }
    })
  }
}
/**
 * @description: 点击操作日志
 * @return {*}
 */
const handleLog = (row) => {
  reviewVisible.value = true
  reviewRow.value = row
}
/**
 * @description: 关闭操作日志
 * @return {*}
 */
const beforeClose_review = () => {
  reviewVisible.value = false
}
const lisSubmit = () => {
  checkedKeys.value = [...new Set(checkedKeys.value)]
  licenseScopeRow.value.licenceScopes = []
  checkedKeys.value.forEach(x => {
    licenseScopeRow.value.licenceScopes.push({
      massRangeSet: {
        id: x
      }
    })
  })
  const arr = treeToArray(licenseScopeListData.value)
  const chooseName = []
  arr.forEach(item => {
    if (checkedKeys.value.includes(item.id)) {
      chooseName.push(item.massName)
    }
  })
  licenseScopeRow.value.licenceScopesName = chooseName.toString()
  licenseScopeVisible.value = false
}
const viewSelection = (row, isChoose) => {
  const forTreeChoose = (arr) => {
    arr.forEach(v => {
      checkedKeys.value.push(v.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (v.children?.length) {
        forTreeChoose(v.children)
      }
    })
  }
  const forTreeChoose_no = (arr) => {
    arr.forEach(v => {
      checkedKeys.value = [...checkedKeys.value].filter(x => x != v.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (v.children?.length) {
        forTreeChoose_no(v.children)
      }
    })
  }
  row.forEach(x => {
    if (isChoose) {
      checkedKeys.value.push(x.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (x.children?.length) {
        forTreeChoose(x.children)

      }
    } else {
      // indeterminate
      checkedKeys.value = [...checkedKeys.value].filter(s => s != x.id)
      checkedKeys.value = [...new Set(checkedKeys.value)]
      if (x.children?.length) {
        forTreeChoose_no(x.children)

      }
    }

  })
}
const handleCheckChange = (isChoose, row) => {
  viewSelection([row], isChoose)
}

function treeToArray(tree) {
  return tree.reduce((res, item) => {
    const { children, ...i } = item
    return res.concat(i, children && children.length ? treeToArray(children) : [])
  }, [])
}

const handleQualityList = (params) => {
  lisloading.value = true
  manufacturerManagement.erpBaseCommonValues({ ...params }).then(res => {
    if (res.code == 200) {
      lisloading.value = false
      licenseScopeListData.value = res.data?.records
    }
    licenseScopeRow.value.licenceScopes?.forEach(v => {
      checkedKeys.value.push(v.massRangeSet.id)
    })
  })
}
const handleLicenseScope = (scope) => {
  chooseLicenseScope.value = []
  licenseScopeListData.value = []
  checkedKeys.value = []
  licenseScopeRow.value = scope
  if (isquiry()) return
  licenseScopeVisible.value = true
  const shopKey = shopType.value.filter(item => item.name === typeDict[scope.type])?.[0]?.id
  if (shopKey) {
    lisloading.value = true
    custom.getWightConfig({ dictid: shopKey }).then(res => {
      if (res.code == 200) {
        lisloading.value = false
        res.data.forEach(v => {
          if (v.length) {
            licenseScopeListData.value.push(v[0])
          }

        })
        // licenseScopeListData.value = res.data[0]
        licenseScopeRow.value.licenceScopes?.forEach(v => {
          checkedKeys.value.push(v.massRangeSet.id)
        })

      }
    }).finally(() => {
      lisloading.value = false
    })
  } else {
    lisloading.value = false
  }
}
const handleExceed = (files, ref) => {
  ref.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  ref.handleStart(file)
  ref.submit()
}
const handleFileList = () => {
  fileListVisible.value = true
  fileDraLoading.value = true
  manufacturerManagement.getFileLists({
    commonId: form.value.id,
    commonType: '10', ...fileListDataPage.value
  }).then(res => {
    if (res.code == 200) {
      fileListData.value = res.data?.records
      fileListDataTotal.value = res.data?.total

    }
    fileDraLoading.value = false
  })
}
const beforeFile = (file) => {
  if (file.size > 2097152) {
    proxy.msgError("文件不能大于2M");
    return false;
  }
}
const handleExportFile = () => {
  const chooseArr = []
  fileListChooseList.value.forEach(v => {
    chooseArr.push(v.fileUrl)
  })
  manufacturerManagement.exportFile({ fileNames: chooseArr.toString() }).then(res => {
    proxy.download(res)
  })
}
const handleDetailFileListChoose = (key) => {
  fileListChooseList.value = key
}
const handleClickOutside = (scope, key, e) => {
  if (e?.target?.parentNode?.parentNode?.parentNode?.parentNode.className.includes('selectClass') || e?.target?.parentNode?.parentNode?.parentNode?.parentNode?.parentNode.className.includes('selectClass')) {
    return
  }
  scope.row[key] = false
}

const handlePictureCardPreview = (uploadFile) => {
  uploadVisible.value = true
  uploadViewImgUrl.value = uploadFile?.url || uploadFile

}

const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7
}
const disabledDateAfter = (time) => {
  return time.getTime() > Date.now()
}
// GMP
const handleAdd_GMP = () => {
  modalTableList_GMP.value.push({ id: uuid.v1(), gmpCertificatePicture: [], isAdd: true })
}
const handleDelete_GMP = () => {
  proxy.$confirm('是否确认删除选中数据项?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    const ids = []
    chooseList_GMP.value.forEach(item => {
      if (!item.isAdd) {
        ids.push(item.id)
      }
    })
    if (ids?.length) {
      deleteGmpList.value = [...deleteGmpList.value, ...ids]
      modalTableList_GMP.value = modalTableList_GMP.value.filter((item) => !(chooseList_GMP.value.some((ele) => ele.id === item.id)));
      proxy.msgSuccess('删除成功')
    } else {
      modalTableList_GMP.value = modalTableList_GMP.value.filter((item) => !(chooseList_GMP.value.some((ele) => ele.id === item.id)));
      proxy.msgSuccess('删除成功')
    }

  }).catch(() => {

  })
}
// 禁用or解禁提交
const prohibitionAndLiftingSubmit = () => {
  const params = {
    idea: idea.value,
    status: customLabelValue.value.customLabel == '1' ? '2' : '1',
    id: customLabelValue.value.id
  }
  manufacturerManagement.delFlag(params).then(res => {
    if (res.code == 200) {
      proxy.msgSuccess(customLabelValue.value.customLabel == '1' ? '解禁成功' : '禁用成功')
      getList()
      customLabelValue.value = {}
    }
  }).finally(() => {
  })
  prohibitionAndLiftingVisible.value = false
}
// 关闭禁用or解禁
const prohibitionAndLiftingClose = () => {
  prohibitionAndLiftingVisible.value = false
}
// 禁用
const handleDisabled = (row, type) => {
  prohibitionAndLiftingVisible.value = true
  idea.value = undefined
  customLabelValue.value = row
  if (type == '1') {
    prohibitionAndLiftingTitle.value = '设置禁用'
  } else {
    prohibitionAndLiftingTitle.value = '设置解禁'
  }
}
// 查看详情modal
const handleDetail = async (row, type) => {
  if (type === 'detail') {
    detailOpen.value = true
    detailData.value = row
    modalType.value = type
    return
  }
  isClearFileWaring.value = false
  open.value = true
  modalLoading.value = true
  modalType.value = type
  title.value = titleDict[type]
  let res = await manufacturerManagement.getLicenseScopeList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
  licenseScopeList.value = res?.data?.records
  manufacturerManagement.detail({ id: row.id }).then(res => {
    if (res.code == 200) {
      form.value = res.data?.informationDTO
      modalTableList.value = res.data?.licenceDTOS
      modalTableList_GMP.value = res.data?.gmpCertificateDTOS
      modalTableList_file.value = res.data?.fileDTOS
      form.value.businessLicensePicturt = form.value?.businessLicensePicturt ? JSON.parse(form.value?.businessLicensePicturt) : []
      modalTableList.value?.forEach(item => {
        item.type = Object.entries(typeDict)?.find(([key, val]) => val === item?.type)?.[0]
        if (item?.type == '1' || item?.type == '6') {
          item.licenceScopes = item?.licenceScopesName?.split(',')
          item.licenceScopesName = item?.licenceScopesName?.split(',')
        }
        item.licenseImg = item?.licenseImg ? JSON.parse(item?.licenseImg) : []

      })
        if (modalTableList.value) {
            if (modalTableList.value.length > 0) {
              licensedata.value = modalTableList.value[modalTableList.value.length - 1]
              tabKey.value = modalTableList.value[modalTableList.value.length - 1].type
            } else {
                licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
                tabKey.value = '1'
            }
        } else {
            licensedata.value = {licenseImg: [], licenceScopes: [], type: '1'}
            tabKey.value = '1'
            modalTableList.value = []
        }
      if (licensedata.value?.type == '6') {
        handleQualityList({ 'item.id': '1', isStop: '1', current: 1, size: 999 })
      }
      if (licensedata.value?.type == '1') {
        handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
      }
      modalTableList_GMP.value.forEach(item => {
        item.gmpCertificatePicture = item.gmpCertificatePicture ? [JSON.parse(item.gmpCertificatePicture)] : []
        item.gmpCertificateScope = item.gmpCertificateScope ? item.gmpCertificateScope?.split(',') : []
      })
      modalTableList_file.value.forEach(item => {
        item.fileList = item.fileNameUrl ? JSON.parse(item.fileNameUrl) : []
      })
    }
    modalLoading.value = false
    isClearFileWaring.value = true
  }).finally(() => {
    modalLoading.value = false
  })
}
const handleInputEdit = (scope, type) => {
  if (modalType.value == 'detail') return
  scope.row[`isShow${type}`] = true


}
const handleUploadSuccess = (res, file, list, index, type) => {
  if (res.code == 200) {
    if (type == 1) {
      const fileList = [...form.value.businessLicensePicturt, res.data]
      form.value.businessLicensePicturt = fileList.filter(item => !item.response)
      console.log(form.value);
    }
    if (type == 2) {
      const fileList = [...licensedata.value.licenseImg, res.data]
      licensedata.value.licenseImg = fileList.filter(item => !item.response)
    }
    if (type == 3) {
      modalTableList_GMP.value[index].gmpCertificatePicture = [{ ...res.data }]
    }
    if (type == 4) {
      const fileList = [...modalTableList_file.value[index].fileList, res.data]
      modalTableList_file.value[index].fileList = fileList.filter(item => !item.response)
    }
  }
}
const handleSelectionChange = (key) => {
  chooseList.value = key
}
const handleSelectionChange_GMP = (key) => {
  chooseList_GMP.value = key
}
const handleChange = (value) => {
  activeNames.value = value
}
// 储存数据  回显数据
const saveLicendata = (value) => {
  let isHas = false
  modalTableList.value.forEach(item => {
    if (item.type === licensedata.value.type) {
      isHas = true
    }
  })
  if (!isHas) {
    modalTableList.value = [...modalTableList.value, licensedata.value]
  }
  const data = modalTableList.value.filter(item => item.type === value)
  if (data?.length) {
    licensedata.value = data[0]

  } else {
    licensedata.value = { licenseImg: [], licenceScopes: [], type: value }
  }
  tabKey.value = value
  if (value == '1') {
    handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
  }
  if (value == '6') {
    handleQualityList({ 'item.id': '1', isStop: '1', current: 1, size: 999 })
  }
}
// 判断数组是否有该类型数据  有则回显  无则新增
const isLicenseData = (value) => {
  const cloneTabKey = JSON.parse(JSON.stringify(tabKey.value))
  if (regData()) {
    saveLicendata(value)
  } else {
    proxy.$refs["modalTableListRef"].validate((valid) => {
      if (valid) {
        saveLicendata(value)
      } else {
        tabKey.value = cloneTabKey
      }
    })
  }
}
// 检验该类型是否为空数据
const regData = () => {
  let index = 0
  const data = licensedata.value
  const regKey = ["licenseNo", "licenseAddress", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg"]
  regKey.forEach(item => {
    if (Array.isArray(data[item]) ? data[item]?.length : data[item]) {
      index++
    }

  })
  if (index === regKey.length || index === 0) {
    return true
  }
  return false
}

const handleTabClick = (value) => {
  isLicenseData(value)
}

/** 查询角色列表 */
function getList() {
  loading.value = true
  const params = {
    ...queryParams.value,
  }
  if (params?.createDate?.length) {
    params.beginCreateDate = moment(params?.createDate[0]).format('YYYY-MM-DD 00:00:00')
    params.endCreateDate = moment(params?.createDate[1]).format('YYYY-MM-DD 23:59:59')
    delete params.createDate
  }
  if (params?.updateDate?.length) {
    params.beginUpdateDate = moment(params?.updateDate[0]).format('YYYY-MM-DD')
    params.endUpdateDate = moment(params?.updateDate[1]).format('YYYY-MM-DD')
    delete params.updateDate
  }
  manufacturerManagement.getList(params).then(res => {
    if (res.code == 200) {
      list.value = res.data.records
      total.value = res.data.total
      loading.value = false
    }
  })
}

const formDict = (data, val) => {
  return proxy.selectDictLabel(data, val)
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

const filterArr = (option, key, value) => {
  if (!option?.length || !key || !value?.length) return
  const res = option.filter(v => value.includes(v[key]) === true)
  const NameArr = []
  res?.forEach(item => {
    NameArr.push(item?.valueName)
  })
  return NameArr?.toString()
}

const handleAdd = async (row, type) => {
  open.value = true
  modalType.value = type
  title.value = titleDict[type]
  let res = await manufacturerManagement.getLicenseScopeList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
  licenseScopeList.value = res?.data?.records
  handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
  manufacturerManagement.getSelfCodeByType().then(res => {
    res.code === 200 ? (form.value.manufacturerSelfNo = res.data) : proxy.msgError('自编码获取失败')
  })
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

const submitForm = async (type) => {

  if (type == 3) {
    if (modalType.value !== 'detail') {
      ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          open.value = false
          form.value = {}
          modalTableList.value = []
          licensedata.value = { licenseImg: [], licenceScopes: [], type: '1' }
          modalTableList_GMP.value = []
          modalTableList_file.value = []
          tabKey.value = '1'
          activeNames.value = '1'
          const query = proxy.$route?.query
          if (query && query.type == 'abnormalTask') {
            proxy.$router.push('/abnormalTask')
          }
        })
        .catch(() => {
          // catch error
        });
    } else {
      open.value = false
      form.value = {}
      modalTableList.value = []
      licensedata.value = { licenseImg: [], licenceScopes: [], type: '1' }
      modalTableList_GMP.value = []
      modalTableList_file.value = []
      tabKey.value = '1'
      activeNames.value = '1'

    }


  } else {

    proxy.$refs["formRef"].validate(async (valid, fields) => {
      if (valid) {
        const submitFormDatalist = () => {
          const delteParams = ['isShowlicenseAddress', 'isShowlicenseDirector', 'isShowlicenseNo', 'isShowlicenseOffice', 'isShowlicenseScope', 'isShowlicenseStartTime', 'isShowlicenseValidity', 'isShowqualityDirector', 'isShowgmpCertificateAddress', 'isShowgmpCertificateNo', 'isShowgmpCertificateScope', 'isShowgmpExpiredTime']
          const licenceDTOS = JSON.parse(JSON.stringify(modalTableList.value))

          const gmpCertificateDTOS = modalTableList_GMP.value?.length ? JSON.parse(JSON.stringify(modalTableList_GMP.value)) : []
          const fileDTOS = modalTableList_file.value?.length ? JSON.parse(JSON.stringify(modalTableList_file.value)) : []
          const informationDTO = form.value ? JSON.parse(JSON.stringify(form.value)) : {}
          informationDTO.businessLicensePicturt = informationDTO?.businessLicensePicturt?.length ? JSON.stringify(informationDTO?.businessLicensePicturt) : undefined
          informationDTO.enterpriseStartTime = informationDTO?.enterpriseStartTime ? moment(informationDTO?.enterpriseStartTime).format("YYYY-MM-DD") : undefined
          informationDTO.businessLicenseExpiredTime = informationDTO?.businessLicenseExpiredTime ? moment(informationDTO?.businessLicenseExpiredTime).format("YYYY-MM-DD") : undefined
          licenceDTOS?.forEach(item => {
            if (item) {
              const regKey = ["licenseNo", "licenseAddress", "licenceScopesName", "licenceScopes", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg"]
              regKey.forEach(value => {
                if (Array.isArray(item[value]) ? (!item[value]?.length) : (!item[value])) {
                  delete item[value]
                }
              })
              item.type = item.type && typeDict[item.type]
              item.licenseStartTime = item.licenseStartTime ? moment(item.licenseStartTime).format("YYYY-MM-DD") : undefined
              item.licenseValidity = item.licenseValidity ? moment(item.licenseValidity).format("YYYY-MM-DD") : undefined
              item.licenseImg = item.licenseImg && JSON.stringify(item?.licenseImg)
              if (Object.entries(typeDict)?.find(([key, val]) => val === item?.type)?.[0] == '1' || Object.entries(typeDict)?.find(([key, val]) => val === item?.type)?.[0] == '6') {
                const cloneliser = item.licenceScopesName ? JSON.parse(JSON.stringify(item.licenceScopesName)) : undefined
                item.licenceScopesName = cloneliser?.toString() || undefined
                const lisenArr = []
                cloneliser?.forEach(v => {
                  lisenArr.push({
                    massRangeSet: {
                      id: v
                    }
                  })
                })
                item.licenceScopes = lisenArr?.length ? lisenArr : undefined
              }
            }
          })
          gmpCertificateDTOS?.forEach(item => {
            item.gmpExpiredTime = item.gmpExpiredTime ? moment(item.gmpExpiredTime).format("YYYY-MM-DD") : undefined
            item.gmpCertificatePicture = item.gmpCertificatePicture?.length && JSON.stringify(item.gmpCertificatePicture?.[0])
            item.gmpCertificateScope = item.gmpCertificateScope?.length && item.gmpCertificateScope?.toString()
            delteParams.forEach(x => {
              delete item[x]
            })

          })
          let fileIndex = 0
          for (var item of fileDTOS) {
            item.fileType =
              item.cardName = item.categoryName
            item.fileName = item.file
            if (item.isUpload == '1' && !item?.fileList?.length) {
              break
            } else {
              fileIndex++
            }
          }
          const fileListArr = []
          const fileName_ = []
          const fileUrl_ = []
          fileDTOS.forEach(item => {
            item.fileList?.forEach(val => {
              fileName_.push(val?.name)
              fileUrl_.push(val?.url)
            })
            fileListArr.push({
              smallClass: item.smallType,
              smallType: item.smallType,
              categoryName: item.categoryName,
              isMultiPage: item.isMultiPage,
              cardName: item.categoryName,
              fileName: fileName_.toString(),
              fileUrl: fileUrl_.toString(),
              isUpload: item.isUpload,
              isPage: item.isMultiPage,
              fileNameUrl: JSON.stringify(item.fileList),
            })
          })
          const params = {
            informationDTO,
            licenceDTOS,
            gmpCertificateDTOS,
            operate: type,
            fileDTOS: fileListArr
          }

          if ((Object?.entries(titleDict)?.find(([key, val]) => val === title?.value)?.[0] == 'edit' && type == 'save') || (Object?.entries(titleDict)?.find(([key, val]) => val === title?.value)?.[0] == 'draft' && type == 'save')) {
            params.operate = 'update'
          }


          const deleteData = async () => {
            if (modalType.value !== 'add') {
              if (deleteGmpList.value?.length) {
                await manufacturerManagement.erpManufacturerGmpCertificate({ ids: [...deleteGmpList.value]?.toString() })
              }

            }
          }

          // if (params.operate !== 'submit' && !fileDTOS?.length) return proxy.msgError('请填写附件信息！')
          if (fileIndex !== fileDTOS?.length) return proxy.msgError('请上传必传附件！')
          modalLoading.value = true
          manufacturerManagement.save(params).then(res => {
            if (res.code == 200) {
              open.value = false
              tabKey.value = '1'
              activeNames.value = '1'
              form.value = {}
              modalTableList_file.value = []
              modalTableList.value = []
              licensedata.value = { licenseImg: [], licenceScopes: [], type: '1' }
              modalTableList_GMP.value = []
              proxy.msgSuccess('操作成功')
              getList()
              modalLoading.value = false
              const query = proxy.$route?.query
              if (query && query.type == 'abnormalTask') {
                proxy.$router.push('/abnormalTask')
              }
              deleteData()
            } else {
              proxy.msgError(res.msg)
              modalLoading.value = false
            }
          }).finally(() => {
            modalLoading.value = false
          })


        }
        let isSet = false
        if (type === 'submit') {
          proxy.$refs["modalTableListRef"].validate((valid, fields) => {
            if (valid) {
              modalTableList.value.forEach(item => {
                if (item.type === licensedata.value.type) {
                  isSet = true
                }
              })
              if (!isSet) {
                modalTableList.value = [...modalTableList.value, licensedata.value]
              }
              submitFormDatalist()
            } else {
              Object.keys(fields).forEach((key, i) => {
                const propName = fields[key][0].field
                if (i == 0) {
                  proxy.$refs["modalTableListRef"].scrollToField(propName)
                }
              })
              if (activeNames.value.indexOf('2') == -1) {
                activeNames.value.push('2')
              }
            }
          })
        } else {
          modalTableList.value.forEach(item => {
            if (item.type === licensedata.value.type) {
              isSet = true
            }
          })
          if (!isSet) {
            modalTableList.value = [...modalTableList.value, licensedata.value]
          }
          submitFormDatalist()
        }
      } else {
        Object.keys(fields).forEach((key, i) => {
          const propName = fields[key][0].field
          if (i == 0) {
            proxy.$refs["formRef"].scrollToField(propName)
          }
        })
        if (activeNames.value.indexOf('1') == -1) {
          activeNames.value.push('1')
        }
      }
    })
  }

}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$confirm('是否确认删除改数据项?', '提示', {
    type: 'warning',
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    manufacturerManagement.delete({ ids: row.id }).then(res => {
      if (res.code == 200) {
        proxy.msgSuccess('删除成功')
        getList()
      } else {
        proxy.msgError(res.msg)
      }
    })
  }).catch(() => {
  });
}

const recursion = (option, key) => {
  option.forEach(v => {
    if (v[key]) {
      v.label = v[key]
      v.value = v.id
    }
    if (v.children?.length) {
      recursion(v.children, key)
    }
  })
}

async function dict() {
  delFlagList.value = await proxy.getDictList('erp_delFlag_status')
  reviewStatus.value = await proxy.getDictList('erp_review_status')
  shopType.value = await proxy.getDictList('product_type_config')
  manufacturerType.value = await proxy.getDictList('manufacturer_type')
  directoryFileName.value = await proxy.getDictList('directory_file_name')

}

onMounted(async () => {
  isClearFileWaring.value = false
  const query = proxy.$route?.query
  if (query && query.id && query.type == 'abnormalTask') {
    open.value = true
    modalLoading.value = true
    modalType.value = 'edit'
    title.value = titleDict['edit']
    let res = await manufacturerManagement.getLicenseScopeList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
    licenseScopeList.value = res?.data?.records
    manufacturerManagement.detail({ id: query.id }).then(res => {
      if (res.code == 200) {
        form.value = res.data?.informationDTO || {}
        modalTableList.value = res.data?.licenceDTOS
        modalTableList_GMP.value = res.data?.gmpCertificateDTOS
        modalTableList_file.value = res.data?.fileDTOS
        form.value.businessLicensePicturt = form.value?.businessLicensePicturt ? JSON.parse(form.value?.businessLicensePicturt) : []
        modalTableList.value?.forEach(item => {
          item.type = Object.entries(typeDict)?.find(([key, val]) => val === item?.type)?.[0]
          if (item?.type == '1' || item?.type == '6') {
            item.licenceScopes = item?.licenceScopesName?.split(',')
            item.licenceScopesName = item?.licenceScopesName?.split(',')
          }
          item.licenseImg = item?.licenseImg ? JSON.parse(item?.licenseImg) : []

        })
        licensedata.value = modalTableList.value[0]
        tabKey.value = modalTableList.value[0]?.type
        if (licensedata.value?.type == '6') {
          handleQualityList({ 'item.id': '1', isStop: '1', current: 1, size: 999 })
        }
        if (licensedata.value?.type == '1') {
          handleQualityList({ 'item.id': '6', isStop: '1', current: 1, size: 999 })
        }
        modalTableList_GMP.value.forEach(item => {
          item.gmpCertificatePicture = item.gmpCertificatePicture ? [JSON.parse(item.gmpCertificatePicture)] : []
          item.gmpCertificateScope = item.gmpCertificateScope ? item.gmpCertificateScope?.split(',') : []
        })
        modalTableList_file.value.forEach(item => {
          item.fileList = item.fileNameUrl ? JSON.parse(item.fileNameUrl) : []
        })
      }
      modalLoading.value = false
      isClearFileWaring.value = true
    }).finally(() => {
      modalLoading.value = false
    })
  }
  dict()
  getList();
})

const columns = ref([
  {
    label: '序号',
    prop: 'commodityCommonName',
    type: "sort",
    fixed: 'left'
  },
  {
    label: '企业名称',
    prop: 'enterpriseName'
  },
  {
    label: '拼音码',
    prop: 'pinyin',
  },
  {
    label: '自编码',
    prop: 'manufacturerSelfNo',
  },
  {
    label: '统一社会信用代码',
    prop: 'businessLicenseCode',
    minWidth: '130'
  },
  {
    label: '营业执照有效期',
    prop: 'businessLicenseExpiredTime',
    type: 'date',
    minWidth: 150,
  },
  {
    label: '创建日期',
    prop: 'createDate',
    type: 'date'
  },
  {
    label: '修改日期',
    prop: 'updateDate',
    type: 'date'
  },
  {
    label: '创建来源',
    prop: 'source',
    type: 'status',
    filters: [
      {
        name: '手工录入',
        value: '1'
      },
      {
        name: '智能录入',
        value: '2'
      },
    ],
  },
  {
    label: '审核状态',
    prop: 'status',
    type: 'status',
    filters: reviewStatus
  },
  {
    label: '禁用状态',
    prop: 'customLabel',
    type: 'status',
    filters: delFlagList
  },
  {
    label: '操作',
    prop: 'operate',
    type: 'operate',
    minWidth: 300,
    fixed: 'right'
  },
])

</script>
<style lang="scss" scoped>
.col_title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 8px;

  &::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 20px;
    background-color: #2878ff;
    border-radius: 2px;
    position: absolute;
    top: 15px;
    left: 0;
  }
}

.box {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  // grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: start;
}

.el-tabs {
  display: flex;
  // align-items: center;
}

.el-tabs__nav-wrap::after {
  width: 0 !important;
}

::v-deep .rules {
  position: relative;

  .cell::after {
    content: "*";
    color: red;
    display: inline-block;
    position: absolute;
    top: 30%;
    left: 70px;
  }
}

::v-deep .Botm {
  .el-card__body {
    padding-bottom: 0px
  }
}

.box_date {
  width: 220px;
}

.box_2 {
  width: 100%;
  display: grid;
  // grid-template-rows: 50% 50%;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-items: stretch;
  align-items: stretch;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
  display: contents !important;
}

::v-deep .el-upload-dragger {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .step2 {
  .el-button {
    // border-bottom: none;
    border-right: none;
    border-radius: 4px 0 0 4px;

  }

  .el-button+.el-button {
    margin-left: 0;
    border-radius: 0 0 0 0;
    // border-left: none;
  }
}

.btn_cmt {
  margin-bottom: 20px;
}

.last-btn {
  border: 1px solid #dcdfe6 !important;
}

.fileName_t {
  display: flex;
  width: 100%;
  color: #2a76f8;
  cursor: pointer;
  align-items: center;
  margin-top: 4px;

  span:nth-of-type(2) {
    display: none;
    margin-left: 10px;

    ::v-deep .el-icon {
      // margin-top:10px;
      font-size: 12px;
      color: red
    }
  }
}

.fileName_t:hover .fileName_t_icon {
  display: block;
}
</style>
