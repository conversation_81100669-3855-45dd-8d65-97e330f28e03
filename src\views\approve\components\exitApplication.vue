<template>
    <div class="app-container" v-if="visible">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog title="采购退回申请详情" v-model="visible" width="85%" :before-close="next">
            <div v-loading="loading">

                <div class="step1 " v-show="active == 1">
                    <h4 class="titleH4">
                        申请编号:<span style="color: #505050;font-size: 13px;font-weight: 500;margin-right:10px;margin-left:10px"> {{
                            headerData.applyNo ||
                            '--' }}</span>
                        申请日期: <span style="color: #505050;font-size: 13px;font-weight: 500;margin-right:10px;margin-left:10px"> {{
                            headerData.applyDate ?
                            moment(headerData.applyDate).format('YYYY-MM-DD') : '--' }}</span>
                        申请人:<span style="color: #505050;font-size: 13px;font-weight: 500;margin-right:10px;margin-left:10px"> {{
                            headerData.createBy &&
                            headerData.createBy.name || '--' }}</span>
                        审核状态: <span style="color: #505050;font-size: 13px;font-weight: 500;margin-right:10px;margin-left:10px">{{
                            formDict(reviewStatus,
                                headerData.auditStatus) }}</span> </h4>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px">退货明细</h3>
                    <el-table :data="step1_list" border style="margin-top: 20px;" row-key="id"
                        ref="entrust_out_table_listRef">
                        <el-table-column label="单据编号" prop="orderCode" :show-overflow-tooltip="true" align="center"
                            min-width="170" />
                        <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="自编码" prop="commodity.commoditySelfCode" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="规格" prop="commodity.packageSpecification" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="生产厂家" prop="manufactureName" align="center" min-width="120" :show-overflow-tooltip="true"/>
                        <el-table-column label="产地" prop="commodity.producingArea" align="center" min-width="120" />
                        <el-table-column label="剂型" prop="commodity.dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="基本单位" prop="commodityBasicUnit" :show-overflow-tooltip="true" align="center"
                            min-width="80" />

                        <el-table-column label="成本单价" prop="unitPrice" :show-overflow-tooltip="true" align="center"
                            min-width="80" />
                        <el-table-column label="批号" prop="batchNumber" :show-overflow-tooltip="true" align="center"
                            min-width="160" />
                        <el-table-column label="生产日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="row => row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="有效期" prop="commodity.validityTime" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="申退数量" prop="retreatQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="申退金额" prop="retreatAmount" :show-overflow-tooltip="true" align="center"
                            min-width="120">
                            <template #default="scope">
                                <p>{{ (scope.row.retreatQuantity *
                                    (isNaN(Number(scope.row.unitPrice || 0)) ? 0 : Number(scope.row.unitPrice ||
                                        0))).toFixed(2)
                                }}</p>
                            </template>
                        </el-table-column>>
                    </el-table>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;">
                        退货信息
                    </h3>
                    <table border="0" cellpadding="0" cellspacing="1" class="messTable">
                        <tr v-if="form.retreatAmount">
                            <th>合计申退金额</th>
                            <th>{{ form.retreatAmount.toFixed(2) }}</th>
                        </tr>
                        <tr>
                            <th>合计申退数量</th>
                            <th>{{ form.retreatQuantity }}</th>
                        </tr>
                        <tr>
                            <th>退货原因</th>
                            <th>{{ form.returnReason == '3' ? form.otherReasons : formDict(reasonForReturn,
                                form.returnReason)
                            }}</th>
                        </tr>
                        <tr>
                            <th>货物处理</th>
                            <th>{{ formDict(cargoHandling, form.cargoHandling) }}</th>
                        </tr>
                        <tr>
                            <th>款项处理</th>
                            <th>{{ formDict(paymentProcessing, form.payProcess) }}</th>
                        </tr>
                        <tr>
                            <th>物流方式</th>
                            <th>{{ formDict(logisticsMethods, form.logisticsMethods) }}</th>
                        </tr>
                        <tr>
                            <th>三方物流</th>
                            <th>{{ formDict(thirdPartyIogistics, form.thirdLogistics) }}</th>
                        </tr>
                        <tr>
                            <th>收货地址</th>
                            <th>{{ form.deliveryAddressCn }}</th>
                        </tr>
                        <tr>
                            <th>详细地址</th>
                            <th>{{ form.detailAddress }}</th>
                        </tr>
                        <tr>
                            <th>姓名</th>
                            <th>{{ form.name }}</th>
                        </tr>
                        <tr>
                            <th>电话</th>
                            <th>{{ form.phone }}</th>
                        </tr>
                        <tr style="background:#fff">
                            <th>电话</th>
                            <th>{{ form.phone }}</th>
                        </tr>
                    </table>
                    <div class="remark">
                        <tr style="width: 100%;">
                            <th style="flex: 1;">备注</th>
                            <th style="flex: 12;text-align: left;">{{ form.remark }}</th>
                        </tr>
                    </div>
                    <div class="file" >
                        <tr style="width: 100%;">
                            <th style="flex: 1;">附件</th>
                            <th
                                style="flex: 12;text-align: left;display: flex;flex-direction: column;align-items: flex-start;">
                                <p v-for="(item, index) in form.fileList" :key="index" @click="handlePreview(item)">{{
                                    item.fileName }}
                                </p>
                            </th>
                        </tr>
                    </div>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;">
                        出库记录
                    </h3>
                    <el-table :data="outboundList" border style="margin-top: 20px;">
                        <el-table-column label="自编码" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="商品名称" prop="tradeName" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="批号" prop="intoNo" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="采退出库单编号" prop="packageSpecification" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="出库时间" prop="manufacture.enterpriseName" align="center" min-width="120" />
                        <el-table-column label="出库数量" prop="originPlace" align="center" min-width="120" />
                        <el-table-column label="出库状态" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                    </el-table>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;">
                        操作日志
                    </h3>
                    <LogQuery ref="childRef" />
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;" v-if="type != 'record'">
                        审批意见
                    </h3>
                    <Audit ref="auditRef" v-if="type != 'record'"  @refresh="getList"/>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="next">取消</el-button>
                    <el-button type="primary" @click="prev" v-if="type != 'record'">确认</el-button>
                </div>
            </template>
        </el-dialog>
        <viewImg v-if="uploadVisibleFile" :visible="uploadVisibleFile" :src="uploadViewImgUrlFile"
            :beforeClose="() => uploadVisibleFile = false" />
    </div>
</template>

<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, watch, defineExpose, onMounted } from 'vue'
import moment from 'moment'
import Table from "@/components/assist/massRange/Table.vue";
import purchaseWithdrawalReview from '@/api/erp/purchaseWithdrawalReview'
import Audit from '@/components/detailsForm/audit.vue'
import LogQuery from '@/components/detailsForm/logQuery.vue'
import { drugApi } from "@/api/model/commodity/drug/index";
const { proxy } = getCurrentInstance();
const loading = ref(false);
const active = ref(1)
const step1_list = ref([])
const form = ref({
    fileList: []
})
const cargoHandling = ref([])
const paymentProcessing = ref([])
const logisticsMethods = ref([])
const thirdPartyIogistics = ref([])
const reasonForReturn = ref([])
const uploadVisibleFile = ref(false)
const uploadViewImgUrlFile = ref('')
const reviewStatus = ref([])
const outboundList = ref([])
const headerData = ref({})
const childRef = ref(null)
const auditRef = ref(null)

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    beforeClose: {
        type: Function,
        default: () => { }
    },
    data: {
        type: Object,
        default: () => { }
    },
    getList: {
        type: Function,
        default: () => { }
    },
    type: {
        type: String,
        default: ''
    },
})
const detailFlag = ref(false)
const shopDetail = ref({})
const { visible, beforeClose, data, getList, type } = toRefs(props)
const handlePreview = (res) => {
    uploadVisibleFile.value = true
    uploadViewImgUrlFile.value = res.url || res.fileUrl
}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}

const getDetail = () => {
    purchaseWithdrawalReview.getIdOrder({ id: data.value.documentId }).then(res => {
        if (res.code == 200) {
            step1_list.value = res.data?.purchaseRetreatForms
            form.value = res.data?.purchaseRetreatReason
            form.value = { ...form.value, retreatAmount: res.data?.purchaseRetreat?.retreatAmount, retreatQuantity: res.data?.purchaseRetreat?.retreatQuantity, fileList: res.data?.fileDtos }
            headerData.value = res.data?.purchaseRetreat
        } else {
            proxy.msgError(res.msg)
        }
    })
}
const getOut = () => {
    purchaseWithdrawalReview.getOut({ 'purchaseRetreat.id': data.value.documentId }).then(res => {
        if (res.code == 200) {
            outboundList.value = res.data?.record
        }
    })
}
const next = () => {
    beforeClose.value()
}
const prev = async () => {
    if(!auditRef.value.form?.status) return proxy.msgError('请先选择审批结果！')
    await auditRef.value.formSub(data.value?.id);
    await beforeClose.value()
}
async function dict() {
    cargoHandling.value = await proxy.getDictList('cargo_handling')
    paymentProcessing.value = await proxy.getDictList('payment_processing')
    logisticsMethods.value = await proxy.getDictList('logistics_methods')
    thirdPartyIogistics.value = await proxy.getDictList('third_party_iogistics')
    reasonForReturn.value = await proxy.getDictList('reason_for_return')
    reviewStatus.value = await proxy.getDictList('erp_review_status')
}
onMounted(async () => {
    loading.value = true
    await dict()
    getDetail()
    getOut()
    const reviewRes = await purchaseWithdrawalReview.getReview({ 'purchaseRetreat.id': data.value.documentId })
    let resq = await drugApi.drugLog({ masterId: data.value.documentId })
    if ((reviewRes.code == 200 && reviewRes.data) || (resq.code == 200 && resq.data)) {
        childRef.value?.timeFns(reviewRes?.data?.records, resq?.data?.records)
    }
    loading.value = false
})

defineExpose({

})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.rowStyle {
    .el-col {
        margin-top: 20px;
        font-size: 15px;

        .rowTitle {
            width: 120px;
            text-align: right;
            display: inline-block;
            font-size: 15px;
            font-weight: bolder;
            color: #000;
        }

        .rowMess {
            color: #4d4d4d;
            font-weight: 600;
        }

        .rowRed {
            color: red;
        }
    }
}

.total {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    margin: 0px 20px;

    p {
        margin-right: 50px;
        margin-top: 20px;

        & span:nth-of-type(1) {
            font-size: 15px;
            font-weight: bold;
            color: #333;
            display: inline-block;
            width: 130px;
            text-align: right;
        }

        .red {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}

.box_date {
    width: 220px;
}

.step {
    margin-bottom: 30px;
}

.step1_search_btn {
    width: 60px;
    margin-left: 50px;
}

h3 {
    color: black;
}

.detailTable {
    width: 100%;
    background-color: #eaedf3;
    font-size: 14px;
    border-radius: 5px;

    tr {
        height: 40px;

        td {
            background-color: white;
        }

        td:nth-child(1) {
            padding: 0 10px;
            font-weight: bold;
            width: 20%;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            width: 80%;
            color: #606266;
            padding: 0 10px;
        }
    }

}

.messTable {
    width: 100%;
    background-color: #eaedf3;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    padding: 1px 1px 0 1px;

    tr {
        margin-bottom: 1px;
        display: flex;

        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2
        }
    }
}

.remark,
.file {
    tr {
        margin-bottom: 1px;
        display: flex;

        // border: 1px solid #eaedf3;
        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2;
            display: flex;
            align-items: center;
        }
    }
}

.remark,
.file {
    border-bottom: 1px solid #eaedf3;
    border-left: 1px solid #eaedf3;
    border-right: 1px solid #eaedf3;
}

.file {
    th:nth-child(2) {
        line-height: 25px;
        color: #2878ff !important;
        cursor: pointer;
        font-weight: 400;
    }
}

.titleH4 {
    margin-bottom: 20px;
    color: #000;
    font-weight: bolder;
    font-size: 15px;
}
</style>
