<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-18 10:06:52
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-16 14:51:19
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\views\assist\purchasingManagement\component\fineSingle.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="app-container">
    <el-dialog v-model="visible" :before-close="beforeClose" :show-close="!loading" destroy-on-close title="请选择商品"
               width="70%">
      <div v-loading="loading">
        <el-row :gutter="10" class="mb8">
          <el-col :span="6">
            <el-input v-model="queryParams.name" clearable placeholder="商品名称/自编码/拼音码"/>
          </el-col>
          <el-col :span="1.5">
            <el-button v-click="() => getShopList()" :disabled="modalType == 'detail'"
                       type="primary">搜索
            </el-button>
          </el-col>
        </el-row>
        <el-table v-loading="loading" :data="list" :row-style="addClass" border style="margin-top: 30px;"
                  @selection-change="handleChangeSelection">
          <el-table-column :selectable="selectable" align="center" min-width="55" type="selection"/>
          <!-- <el-table-column label="选择" align="center" width="60">
              <template #default="scope">
                  <el-radio v-model="radio" :label="scope.$index"
                      @change.native="getCurrentRow(scope.row)">&nbsp;</el-radio>
              </template>
          </el-table-column> -->

          <el-table-column :show-overflow-tooltip="true" align="center" label="商品名称" min-width="180"
                           prop="commonName">
            <template #default="scope">
              <p style="display:flex;align-items:center;justify-content:center">
                <el-tooltip :content="scope.row.tradeName" class="box-item" effect="dark"
                            placement="top">
                  <span class="tradeNameStyle">{{ scope.row.tradeName }}</span>
                </el-tooltip>
                <el-tooltip v-if="!scope.row.cgVerifyResult" :content="'超出委托书商品范围'" class="box-item"
                            effect="dark"
                            placement="top">
                  <el-icon :size="20" color="#b0b2b5">
                    <QuestionFilled size="30"/>
                  </el-icon>
                </el-tooltip>

              </p>
            </template>
          </el-table-column>
          >
          <el-table-column :formatter="(row) => formDict(productType, row.commodityType)" :show-overflow-tooltip="true"
                           align="center" label="类型"
                           min-width="120" prop="commodityType"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="剂型" min-width="120"
                           prop="dosageForm"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="基本单位" min-width="80"
                           prop="basicUnit"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="有效期" min-width="120"
                           prop="validityTime"/>

          <el-table-column align="center" label="生产厂家" min-width="120" prop="manufacture.enterpriseName"/>
          <el-table-column align="center" label="生产地址" min-width="120" prop="originPlace"/>
          <el-table-column :show-overflow-tooltip="true" align="center" label="状态" min-width="140" prop="status">
            <template #default="scope">
              <span>{{ formDict(statusList, scope.row.status) }}</span>
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="库存余量" min-width="120"
                           prop="availableInventory"/>
        </el-table>
        <div style="display:flex;justify-content:end;margin-top:20px">
          <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
                      :total="total" @pagination="getShopList"/>
        </div>
      </div>
      <template #footer>
                <span v-if="!loading" class="dialog-footer">
                    <el-button @click="beforeClose">取消</el-button>
                    <el-button v-click="() => handleSubmit()" type="primary">确定</el-button>
                </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import {reactive, ref, getCurrentInstance, toRefs, defineProps, defineExpose} from 'vue'
import purchasingManagement from '@/api/erp/purchasingManagement'
import {Plus, UploadFilled, Search, QuestionFilled} from '@element-plus/icons-vue'

const {proxy} = getCurrentInstance();
const radio = ref("")
const list = ref([]);
const loading = ref(false);
const total = ref(0)
const chooseList = ref([])
const statusList = ref([])
const type = ref(false)
const productType = ref([])
const data = reactive({
  queryParams: {
    current: 1,
    size: 10,
    name: undefined
  },

});
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  beforeClose: {
    type: Function,
    default: () => {
    }
  },
  chooseListAfter: {
    type: Array,
    default: () => []
  },
  form: {
    type: Object,
    default: () => {
    }
  }
})
const {queryParams} = toRefs(data);
const {visible, beforeClose, chooseListAfter, form} = toRefs(props)

const formDict = (data, val) => {
  return proxy.selectDictLabel(data, val)
}

const selectable = (row) => {
  return row.cgVerifyResult ? true : false
}
const addClass = ({row, column, rowIndex, columnIndex}) => {
  if (!row.cgVerifyResult) {
    return {
      color: 'rgb(181,181,181)'
    }
  }
}
/**
 * @description: 选中的数据
 * @param {*} key
 * @return {*}
 */
const handleChangeSelection = (key) => {
  chooseList.value = key
}
// const getCurrentRow = (key) => {
//     chooseList.value = key
// }
const getShopList = async () => {
  loading.value = true
  try {
    let res = await purchasingManagement.shopList({
      ...queryParams.value,
      supplierId: form.value.supplier,
      certificateCard: form.value.certificateCard
    })
    // let res = await purchasingManagement.shopList({ ...queryParams.value, supplierId: '23423423423', certificateCard: '22' })
    // let res = await purchasingManagement.shopList({ids:'1659031349439729666'})
    if (res.code == 200) {
      total.value = res.data.total
      list.value = res.data.records
      // list.value = res.data
      loading.value = false
    } else {
      proxy.msgError(res.msg)
      loading.value = false
    }
  } catch {

  }
}
const handleSubmit = () => {
  if (!chooseList.value?.length) {
    return proxy.msgError('请选择商品！')
  }
  type.value = true
  loading.value = true
  const ids = []
  chooseList.value?.forEach(item => {
    ids.push(item.id)
  })
  purchasingManagement.getShopDetail({
    ids: ids?.toString(),
    HandledById: form.value?.handledBy,
    supplierId: form.value.supplier,
    certificateCard: form.value.certificateCard
  }).then(res => {
    if (res.code == 200) {
      if (res.data?.length) {
        chooseList.value = res.data
        chooseList.value.forEach(item => item.isAdd = true)
      }
      beforeClose.value()
      loading.value = false
    } else {
      proxy.msgError(res.msg)
      loading.value = false
    }
  }).catch(() => {
    loading.value = false
  }).finally(() => {
    loading.value = false
  })
  // let isClose = true
  // type.value = false
  // if (chooseList.value) {
  //     if (chooseListAfter.value?.length) {
  //         try {
  //             chooseListAfter.value.forEach(v => {
  //                 if (v.id === chooseList.value.id) {
  //                     isClose = false
  //                     throw ('该商品已添加，请重新选择！');

  //                 }
  //             })
  //         } catch (error) {
  //             proxy.msgError(error)
  //         }

  //     }
  //     if (isClose) {
  //         type.value = true
  //         loading.value = true
  //         purchasingManagement.getShopDetail({ ids: chooseList.value.id, HandledById: form.value?.handledBy, supplierId: form.value.supplier, certificateCard: form.value.certificateCard }).then(res => {
  //             if (res.code == 200) {
  //                 chooseList.value = { ...res.data[0], isAdd: true }
  //                 beforeClose.value()
  //                 loading.value = false
  //             } else {
  //                 proxy.msgError(res.msg)
  //                 loading.value = false
  //             }
  //         }).catch(() => {
  //             loading.value = false
  //         }).finally(() => {
  //             loading.value = false
  //         })

  //     }

  // } else {
  //     proxy.msgError('请选择商品！')
  // }
}

async function dict() {
  statusList.value = await proxy.getDictList('product_status')  // 商品状态
  productType.value = await proxy.getDictList('product_type')
}

dict()
getShopList()
defineExpose({
  chooseList,
  type
})
</script>
<style lang="scss" scoped>
.box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  ::v-deep .el-form-item {
    width: 22%;
  }
}

::v-deep .labelStyle {
  .el-form-item__label {
    margin-left: 10px;
  }
}

::v-deep .el-checkbox__inner::after {
  width: 38%;
  height: 72%
}

.tradeNameStyle {
  display: inline-block;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 5px
}
</style>
