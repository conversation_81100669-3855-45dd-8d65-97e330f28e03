<template>
  <el-dialog
    v-model="visible"
    title="组织机构选择"
    @closed="$emit('closed')"
    top="1vh"
  >
    <el-row style="cursor: pointer">
      <el-col :span="16">
        <el-container v-loading="listLoading">
          <el-header style="padding-left: 0px">
            <div class="right-panel-search">
              <el-input
                v-model="searchForm.name"
                placeholder="输入机构名称"
                clearable
              ></el-input>
              <el-button type="primary" icon="el-icon-search" @click="search"></el-button>
            </div>
          </el-header>
          <el-main class="nopadding" style="height: 360px">
            <ytzhTable
              ref="dataTable"
              :data="dataList"
              row-key="id"
              stripe
              highlight-current-row
              @current-change="handleCurrentChange"
              :tablePage="tablePage"
              :pageChangeHandle="getDataList"
              :refreshDataListHandle="getDataList"
              :treeLoadHandle="treeLoadHandle"
            >
              <el-table-column label="机构名称" prop="name"></el-table-column>
              <el-table-column label="机构类型" prop="type"></el-table-column>
              <el-table-column label="联系人" prop="contact"></el-table-column>
              <el-table-column label="是否启用" prop="enable" width="150">
                <template #default="scope">
                  <el-tag v-if="scope.row.enable == true" type="success">启用</el-tag>
                  <el-tag v-if="scope.row.enable == false">停用</el-tag>
                </template>
              </el-table-column>
            </ytzhTable>
          </el-main>
        </el-container>
      </el-col>
      <el-col :span="1"> </el-col>
      <el-col :span="7" style="">
        <el-header><h4>已选择的组织机构</h4></el-header>
        <el-main style="height: 360px; padding: 0">
          <el-table :data="selectData" ref="selectTableList">
            <el-table-column prop="name" label="机构名称"> </el-table-column>
            <el-table-column label="操作" fixed="right" align="right">
              <template #default="scope">
                <el-button
                  plain
                  size="small"
                  type="text"
                  @click="deleteSelect(scope.row, scope.$index)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-main>
      </el-col>
    </el-row>

    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submitComplet()">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import sysOrgService from "@/api/model/sys/sysOrgService";
export default {
  components: {},
  props: {
    //是否多选
    isMultiple: { type: Boolean, default: false },
    //回调函数
    selectChange: { type: Function },
  },
  data() {
    return {
      //数据列表
      dataList: {},
      //分页参数
      tablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
      },
      //查询表单
      searchForm: {},
      //数据列选中行
      selection: [],
      //列表加载
      listLoading: false,
      //已选择的数据
      selectData: [],

		tempData:{},
      visible: true,
    };
  },
  mounted() {
    setTimeout(() => {
      //刷新数据列表
      this.getDataList();
    }, 0);
  },
  methods: {
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      //初始化数据列表
      this.dataList = [];
      //只查询顶级节点
      this.searchForm = {
        ...this.searchForm,
        "parent.id": "0",
      };
      //请求接口
      var res = await this.reqeustList();
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        //数据列表
        this.dataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
    },
    /*
     * 列表请求接口
     * @author: 路正宁
     * @date: 2023-03-30 11:20:01
     */
    async reqeustList(pageSize = this.tablePage.pageSize) {
      //debugger;
      //页面加载
      this.listLoading = true;
      var res = await sysOrgService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: pageSize,
        //排序查询
        orders: this.tablePage.orders,
        //查询参数
        ...this.searchForm,
      });
      this.listLoading = false;
      return res;
    },
    /*
     * 表格选择后回调事件，单选
     * @author: 路正宁
     * @date: 2023-03-31 17:38:42
     */
    handleCurrentChange(val) {
      if (this.isMultiple == true) {
        //多选
        //选中数据去重
        for (var i = 0; i < this.selectData.length; i++) {
          if (this.selectData[i].id == val.id) {
            this.selectData[i] = val;
            return;
          }
        }
        this.selectData.push(val);
      } else {
        //单选
        this.selectData = [];
        this.selectData.push(val);
      }
      this.$message.success("选择了[" + val.name + "]");
    },
    /*
     * 头部搜索框
     * @author: 路正宁
     * @date: 2023-03-24 14:58:47
     */
    search() {
      this.getDataList();
    },
    /*
     * 树表格点击展开
     * @author: 路正宁
     * @date: 2023-03-30 11:16:33
     */
    async treeLoadHandle(tree, treeNode, resolve) {
      //查询当前节点下的子节点
      this.searchForm = {
        "parent.id": tree.id,
      };
      //请求接口
      var res = await this.reqeustList(100);
      if (res.code == 200) {
        if (res.data.records == null || res.data.records.length == 0) {
          this.$message.warning("无数据");
        } else {
          //同步本地
          tree.children = res.data.records;
        }
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      resolve(res.data.records);
    },
    /*
     * 选中的数据
     * @author: 路正宁
     * @date: 2023-03-31 17:26:29
     */
    selecteds(selectData,tempData= {}) {
      this.selectData = selectData;
      this.tempData=tempData;
    },
    /*
     * 删除已选择列表中的数据
     * @author: 路正宁
     * @date: 2023-04-03 09:47:24
     */
    deleteSelect(row, index) {
      this.selectData.splice(index, 1);
    },
    /*
     * 提交选择结果
     * @author: 路正宁
     * @date: 2023-04-03 09:55:23
     */
    submitComplet() {
      this.selectChange(this.selectData,this.tempData);
      this.visible = false;
    },
  },
};
</script>

<style></style>
