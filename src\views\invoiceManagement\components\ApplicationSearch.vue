<template>
	<el-card body-style="padding-bottom:2px" class="box-card">
    <el-form ref="queryRef" :inline="true" :model="searchForm" class="form_130" label-width="130px">
			<TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
				<el-form-item label="客户">
          <el-input v-model="searchForm.n1" class="form_225" clearable placeholder="请输入客户" style="width: 220px"/>
				</el-form-item>
				<el-form-item label="销售单据编号">
          <el-input v-model="searchForm.n2" class="form_225" clearable placeholder="请输入销售单据编号"
                    style="width: 220px"/>
				</el-form-item>
        <el-form-item label="申请日期" prop="noticeContent">
					<div class="xBox">
            <el-date-picker v-model="searchForm.n3" class="form_225" end-placeholder="结束日期" format="YYYY/MM/DD"
                            range-separator="至" size="default" start-placeholder="开始日期" style="width: 100%"
                            type="daterange"
                            value-format="YYYY-MM-DD HH:mm:ss"/>
					</div>
				</el-form-item>
				<el-form-item label="申请人" prop="noticeContent">
          <el-input v-model="searchForm.n4" class="form_225" clearable placeholder="请输入申请人"
                    style="width: 220px"/>
				</el-form-item>
				<el-form-item v-show="showSearch" label="审核状态" prop="noticeContent">
          <el-select v-model="searchForm.n5" class="form_225" placeholder="请选择审核状态" style="width: 220px">
            <el-option label="全部" value=""/>
            <el-option v-for="(item, index) in statusType" :key="index" :label="item.name" :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item v-show="showSearch" label="开票状态" prop="noticeContent">
          <el-select v-model="searchForm.n6" class="form_225" placeholder="请选择开票状态" style="width: 220px">
            <el-option label="未开票" value="0"/>
            <el-option label="已开票" value="1"/>
            <el-option label="开票失败" value="2"/>
					</el-select>
				</el-form-item>
				<el-form-item v-show="showSearch" label="类型" prop="noticeContent">
          <el-select v-model="searchForm.n7" class="form_225" placeholder="请选择类型" style="width: 220px">
            <el-option label="全部" value=""/>
            <el-option label="销售出库" value="0"/>
            <el-option label="销退入库" value="1"/>
					</el-select>
				</el-form-item>
			</TopTitle>
		</el-form>

	</el-card>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import TopTitle from "@/components/topTitle/index.vue";

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const searchForm = ref({
	n1: "",
	n2: "",
	n3: "",
	n4: "",
	n5: "",
	n6: "",
	n7: "",
	n8: "",
});
const showSearch = ref(false);
const data = reactive({});
const emit = defineEmits(["handleQuery"]);
const {proxy} = getCurrentInstance();

const handleQuery = () => {
	emit("handleQuery");
};
const resetQuery = () => {
	for (let i in searchForm.value) {
		searchForm.value[i] = "";
	}
	emit("handleQuery");
};
const statusType = ref([])
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	statusType.value = await proxy.getDictList("status_sales")
});
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	searchForm
});
</script>
<style lang="scss" scoped>
.butns {
	text-align: center;
}

.xBox {
	width: 220px;
}
</style>
