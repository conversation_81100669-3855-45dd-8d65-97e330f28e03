<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-05 14:03:11
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-12-21 16:47:32
 * @FilePath: \zxhy-4pl-font-pc\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-05 14:03:11
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-07 13:09:59
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-config-provider :button="config.button" :locale="locale" :size="config.size" :zIndex="config.zIndex">
    <router-view></router-view>
  </el-config-provider>
</template>

<script>
import colorTool from '@/utils/color'
import DevicePixelRatio from './rem2'

export default {
  name: 'App',
  data() {
    return {
      config: {
        size: "default",
        zIndex: 2000,
        button: {
          autoInsertSpace: false
        }
      }
    }
  },
  computed: {
    locale() {
      return this.$i18n.messages[this.$i18n.locale].el
    },
  },
  created() {
    window._AMapSecurityConfig = {
      securityJsCode: '51f87bd65bd5d120cd6f44cdc4bcd32a', // 你的密钥
    }
    // new DevicePixelRatio().init()
    //设置主题颜色
    const app_color = this.$CONFIG.COLOR || this.$TOOL.data.get('APP_COLOR')
    if (app_color) {
      document.documentElement.style.setProperty('--el-color-primary', app_color);
      for (let i = 1; i <= 9; i++) {
        document.documentElement.style.setProperty(`--el-color-primary-light-${i}`, colorTool.lighten(app_color, i / 10));
      }
      for (let i = 1; i <= 9; i++) {
        document.documentElement.style.setProperty(`--el-color-primary-dark-${i}`, colorTool.darken(app_color, i / 10));
      }
    }
  },
  methods: {}
}
</script>

<style lang="scss">
@import '@/style/style.scss';

.form_225 {
  width: 225px !important;
}

.form_130 {
  .el-form-item__label {
    width: 130px !important
  }

}

.form_95 {
  .el-form-item__label {
    width: 95px !important;
    text-align: right !important;
  }
}

.form_130 {
  .el-input__inner {
    height: auto !important;
  }
}

.el-dialog__body {
  overflow: auto !important;
}

.col_title {
  color: #333;
  font-size: 14px !important;
  font-weight: bold;
  position: relative;
  padding-left: 8px;

  &::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 17px !important;
    background-color: #2878ff;
    border-radius: 2px;
    position: absolute;
    top: 15px !important;
    left: 0;
  }
}

#toolPanel[data-v-6fb77b2a] {
  display: flex;
  justify-content: space-evenly;
  height: auto !important;
}

#toolPanel .brush, #toolPanel .square, #toolPanel .text, #toolPanel .mosaicPen, #toolPanel .right-top, #toolPanel .round, #toolPanel .separateLine, #toolPanel .undo-disabled {
  display: none;
}
</style>
