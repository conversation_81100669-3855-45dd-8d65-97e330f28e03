<template>
	<div class="drug" style="padding: 10px; width: 100%">
		<Sizer ref="searchRef" @handleQuery="handleQuery"/>
		<el-card body-style="padding-bottom:0" class="box-card" style="margin: 10px 0 0 0">
			<template #header>
				<div class="card-header">
					<span><el-button class="button" type="primary" @click="newAddNull()">新增</el-button></span>
					<span>
            <RightToptipBarV2 className="commodity_food" style="margin-bottom: -18px" @handleRefresh="getSearch"/>
          </span>
				</div>
			</template>
			<div class="text item">
				<DragTableColumn v-if="statusType.length" v-loading="loadingFlag" v-model:queryParams="searchForm"
								 :columns="columns" :getList="getSearch" :row-style="functionIndex.tableRowStyle"
								 :tableData="table.list"
								 className="commodity_food">
					<template v-slot:operate="{ scopeData }">

						<el-button
							:disabled="scopeData.row.status == '2' || scopeData.row.status == '3' || scopeData.row.status == '4'"
							link
							type="primary" @click="editTable(scopeData.row, '编辑')">
							<img src="@/assets/icons/update.png" style="margin: 0px 5px 0 0"/>编辑
						</el-button>
						<el-button :disabled="scopeData.row.status != '6' &&
              scopeData.row.status != '7'" link type="danger" @click="delTable(scopeData)"><img
							src="@/assets/icons/delete.png" style="margin: 0px 5px 0 0"/>删除
						</el-button>
						<el-button link type="primary" @click="detailTable(scopeData.row)"><img
							src="@/assets/icons/detail.png"
							style="margin: 2px 5px 0 0"/>详情
						</el-button>
						<el-dropdown>
							<el-button link style="margin-top: 1px" type="primary">更多
								<el-icon>
									<ArrowDown/>
								</el-icon>
							</el-button>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item style="color: #67c13a" @click="recordFn(scopeData.row.id)"><img
										src="@/assets/icons/review.png" style="margin: 0px 2px 0 0"/>操作记录
									</el-dropdown-item>
									<el-dropdown-item :disabled="scopeData.row.status != '0' &&
                    scopeData.row.status != '1'
                    " style="color: #909399" @click="offRow(scopeData)"><img src="@/assets/icons/disabled.png"
																			 style="margin: 2px 2px 0 0"/>{{
											scopeData.row.status == "0"
												? "启用"
												: "禁用"
										}}
									</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</template>
				</DragTableColumn>
				<el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
							   :page-sizes="[5, 10, 20, 50]" :total="data.total"
							   layout="->,total,sizes, prev, pager, next, jumper"
							   style="margin: 19px 0 0 0" @size-change="handleCurrentChange"
							   @current-change="handleCurrentChange"/>
			</div>
		</el-card>
		<el-dialog v-model="dialogVisible" :before-close="handleClose" :title="data.title" width="80%">
			<div v-if="data.editStr" slot="title" class="stateTitle">
				<span>{{ data.editStr == null ? "" : data.editStr.state }}</span>
				<!--				<el-button v-if="data.title=='查看详情'" slot="title" class="fileBtn">附件列表</el-button>-->
			</div>
			<allDialog ref="dialogRef" v-model:formInline="formInline" v-model:formInline2="formInline2"
					   v-model:formInline3="formInline3" v-model:formInline4="formInline4" :data='data'
					   :title="data.title"
					   @Pinyin="Pinyin" @checkFile="checkFile" @delFile="delFile" @delFileS="delFileS"
					   @errorFile="errorFile"
					   @newAddFile="newAddFile" @successFile="successFile" @venterSearch="venterSearch"/>
			<template #footer>
        <span v-if="data.title != '查看详情'" class="dialog-footer">
          <el-button @click="cancalFn()"> 取消 </el-button>
          <el-button v-if="data.status == '6'" @click="
            submitForm(
              data.state ? 'updata' : 'save'
            )
            ">保存草稿</el-button>
          <el-button type="primary" @click="
            submitForm(
              'submit'
            )
            ">
            提交审核
          </el-button>
        </span>
				<span v-else class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
        </span>
			</template>
		</el-dialog>
		<compile ref="compileRef"/>
		<el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
		<el-dialog v-model="dialogVisible2" title="操作记录" width="40%">
			<div v-loading="LoadingLog">
				<logQuery ref="childLog"/>
			</div>

			<template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible2 = false">取消</el-button>
        </span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watch, watchEffect,} from "vue";
import {foodApi} from "@/api/model/commodity/food/index";
import {ArrowDown} from "@element-plus/icons-vue";
import {ElLoading, ElMessage, ElMessageBox} from "element-plus";
import {drugApi} from "@/api/model/commodity/drug";
import {disappApi} from "@/api/model/commodity/disappear";
import {typeList} from "@/views/commodity/indexApi";
import AllDialog from "@/views/commodity/components/food/allDialog.vue";
import foodList from "@/views/commodity/editList/foodList";
import {functionIndex} from "@/views/commodity/functionIndex";
import Compile from "@/components/detailsForm/compile.vue";

const dialogRef = ref(null)
const {proxy} = getCurrentInstance();
const statusType = ref(null)
const LoadingLog = ref(false)
const compileRef = ref(null)
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 *
 */
const dict = async () => {
	statusType.value = await proxy.getDictList('product_status')
}
dict()
const columns = ref([
	{label: '序号', type: "sort", minWidth: '70', fixed: 'left'},
	{label: '通用名', prop: 'commonName'},
	{label: '商品名', prop: 'tradeName'},
	{label: '拼音码', prop: 'pinyinCode'},
	{label: '自编码', prop: 'commoditySelfCode'},
	{label: '商品编码', prop: 'commodityCode'},
	{label: '规格', prop: 'packageSpecification'},
	{
		label: '剂型',
		prop: 'dosageForm',
	},
  {
    label: '创建来源',
    prop: 'source',
    type: 'status',
    filters: [
      {
        name: '手工录入',
        value: '1'
      },
      {
        name: '智能录入',
        value: '2'
      },
    ],
    searchKey: "n8"
  },
	{
		label: '审核状态',
		prop: 'status',
		type: 'status',
		filters: statusType
	},
	{
		label: '禁用状态', prop: 'disable'
	},
	{label: '创建日期', prop: 'createDate', type: 'date'},
	{label: '修改日期', prop: 'updateDate', type: 'date'},
	{label: '操作', prop: 'operate', minWidth: "300px", type: 'operate', fixed: 'right'},
])
const searchRef = ref(null);


const dialogVisible = ref(false);
const table = reactive({
	list: [],
});
const data = reactive({
	pageNum: 1,
	checkFlag: false,
	pageSize: 10,
	state: false,
	total: 0,
	venderDrug: {
		pageNum: 1,
		pageSize: 1000,
		total: 0,
	},
});
const searchForm = ref({
	status: "",
	size: data.pageSize,
  current: data.pageNum,
  n8: ""
});
const handleCurrentChange = () => {
	tableListFn();
};
const venterSearch = () => {
	foodApi
		.venderDrug({
			current: data.venderDrug.pageNum,
			size: data.venderDrug.pageSize,
			enterpriseName: data.venderDrug.value,
      customLabel: 2,
      status: 3,
		})
		.then((res) => {
			data.venderDrug.pageNum = res.data.current;
			data.venderDrug.pageSize = res.data.size;
			data.venderDrug.total = res.data.total;
			data.venderDrug.records = res.data.records;
		});
};
const loadingFlag = ref(false);
const formInline = reactive({});
const formInline2 = reactive({});
const formInline3 = reactive({});
const formInline4 = reactive({});
const childLog = ref(null);
const dialogVisible2 = ref(false);
const recordFn = async (id) => {
	LoadingLog.value = true
	dialogVisible2.value = true;
	if (childLog.value) {
		childLog.value.data.list = [];
	}
	const journalList = await drugApi.journalList({"commodity.id": id})
	const drugLog = await drugApi.drugLog({masterId: id})
	if (journalList.code == 200 && drugLog.code == 200) {
		childLog.value.timeFns(journalList.data.records, drugLog.data.records);
	} else {
		ElMessage.error('加载失败')
	}
	LoadingLog.value = false

};
const detailTable = (row) => {
	compileRef.value.details(5, row.id, '', true, true);
}
const newAddNull = () => {
	dialogVisible.value = true;
	data.state == false;
	data.title = "新增";
	data.status = "6";
	data.editStr = null;
	foodApi.SelfCode({type: 4}).then((res) => {
		formInline.n4 = res.data.substr(2);
		formInline.n26 = res.data.substr(0, 2);
	});
	for (let key in formInline) {
		formInline[key] = "";
	}
	for (let key in formInline2) {
		formInline2[key] = "";
	}
	for (let key in formInline3) {
		formInline3[key] = "";
	}
	formInline4.table = [];
	formInline4.delAll = [];
};
watch(
	() => formInline2.n15,
	(newValue) => {
		if (newValue == "Ⅰ类医疗器械" || newValue == "Ⅱ类医疗器械") {
			formInline2.n22 = true;
		} else {
			formInline2.n22 = false;
		}
	}
);
watch(
	() => formInline.n12,
	(newValue) => {
		let name = "";
		data.venderDrug.records.forEach((record) => {
			if (record.id == newValue) {
				name = record.enterpriseName;
			}
		});
		foodApi
			.siteDrug({
				name: name,
			})
			.then((res) => {
				data.siteType = res.data.licenseList;
			});
	}
);
const Pinyin = () => {
	foodApi.getPinyin({tradeName: formInline.n1}).then((res) => {
		formInline.n3 = res.data;
	});
};
const newAddFile = () => {
	formInline4.table.push({
		n1: formInline4.table.length,
		n2: {
			str: "",
			flag: false,
		},
		n3: {
			str: "",
			flag: false,
		},
		n4: {
			str: "请先上传文件",
			flag: true,
			type: "",
			name: "",
			url: "",
		},
		n5: "",
		n6: {
			str: "",
			flag: false,
		},
	});
};
const delFileS = (ind1, ind2) => {
	ElMessageBox.confirm("确认删除文件吗？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			if (formInline4.table[ind1].n4.file[ind2].id) {
				ElLoading.service();
				disappApi.delFiles({ids: formInline4.table[ind1].n4.file[ind2].id}).then((res) => {
					if (res.code == 200) {
						formInline4.table[ind1].n4.file.splice(ind2, 1)
						ElMessage.success("删除成功");
					} else {
						ElMessage.error("删除失败");
					}
					const loadingInstance = ElLoading.service();
					loadingInstance.close();
				});
			} else {
				formInline4.table[ind1].n4.file.splice(ind2, 1)
				ElMessage.success("删除成功");
			}
		})
		.catch(() => {
		});
}
const successFile = (response, scope) => {
	const loadingInstance = ElLoading.service();
	loadingInstance.close();
	ElMessage.success("上传成功");
	formInline4.table[scope.$index].n4.file.push({
		name: response.data.name,
		url: response.data.url
	})
};
const errorFile = (scope) => {
	const loadingInstance = ElLoading.service();
	loadingInstance.close();
	ElMessage.error("上传失败");

};
const delFile = () => {
	if (formInline4.delAll.length) {
		ElMessageBox.confirm("确认删除文件吗？", "提示", {
			confirmButtonText: "确认",
			cancelButtonText: "取消",
			type: "warning",
		})
			.then(() => {
				let newarr = formInline4.table.filter(
					(item) =>
						!formInline4.delAll.some(
							(subItem) => subItem.n1 === item.n1
						)
				);
				let ids = [];
				formInline4.delAll.forEach((item) => {
					if (item.id) {
						ids.push(item.id);
					}
				});
				if (ids.length > 0) {
					ElLoading.service();
					foodApi.delFiles({ids: ids.toString()}).then((res) => {
						if (res.code == 200) {
							ElMessage.success("删除成功");
							formInline4.table = newarr;
							formInline4.table.forEach((item, index) => {
								item.n1 = index;
							});
						} else {
							ElMessage.error("删除失败");
						}
						const loadingInstance = ElLoading.service();
						loadingInstance.close();
					});
				} else {
					formInline4.table = newarr;
					formInline4.table.forEach((item, index) => {
						item.n1 = index;
					});
				}
			})
			.catch(() => {
			});
	} else {
		ElMessage.error('请先选择')
	}
};

const delTable = (scope) => {
	ElMessageBox.confirm("确认删除此项吗？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			ElLoading.service();
			foodApi.delDrug({type: "4", ids: scope.row.id}).then((res) => {
				if (res.code == 200) {
					ElMessage.success("删除成功");
					tableListFn();
				} else {
					ElMessage.error("删除失败");
				}
				const loadingInstance = ElLoading.service();
				loadingInstance.close();
			});
		})
		.catch(() => {
		});
};
const submitForm = (status) => {
	rightForm(dialogRef.value.creatform1, dialogRef.value.creatform2, dialogRef.value.creatform3, status)
};
const rightForm = async (formEl1, formEl2, formEl3, status) => {
  if (!formEl1 && !formEl2 && !formEl3) return
	let flag1 = false;
	let flag2 = false;
	let flag3 = false;
	let flag4 = true;
	let flag5 = false;
  await formEl1.validate((valid, fields) => {
		if (valid) {
			flag1 = true;
		} else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          dialogRef.value["creatform1"].scrollToField(propName)
        }
      })
      if (dialogRef.value.activeNames.indexOf('1') == -1) {
        dialogRef.value.activeNames.push('1')
      }
    }
	});
  await formEl2.validate((valid, fields) => {
		if (valid) {
			flag2 = true;
		} else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          dialogRef.value["creatform2"].scrollToField(propName)
        }
      })
      if (dialogRef.value.activeNames.indexOf('2') == -1) {
        dialogRef.value.activeNames.push('2')
      }
    }
	});
  await formEl3.validate((valid, fields) => {
		if (valid) {
			flag3 = true;
		} else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          dialogRef.value["creatform3"].scrollToField(propName)
        }
      })
      if (dialogRef.value.activeNames.indexOf('3') == -1) {
        dialogRef.value.activeNames.push('3')
      }
    }
	});
	const reg = /^[0-9]{5}[-][0-9]{2}$/;
	const reg2 = /^[0-9]{5}$/;
	flag5 = reg.test(formInline.n4) || reg2.test(formInline.n4);
	if (!flag5) {
		ElMessage.error("自编码格式不正确");
	}
	formInline4.table.forEach(item => {
		if (item.n5 == 1 && item.n4.file.length <= 0) {
			flag4 = false;
		}
	})
	if (!flag4) {
		ElMessage.error("必传文件请上传");
	}
	if (flag1 && flag2 && flag3 && flag4 && flag5) {
		const fileArr = [];
		formInline4.table.forEach((item) => {
			let fileArrs = []
			item.n4.file.forEach(fileItem => {
				fileArrs.push({
					filesName: fileItem.name,
					filesUrl: fileItem.url
				})
			})
			fileArr.push({
				status: "1",
				isMultiPage: item.n6,
				smallType: item.n2,
				categoryName: item.n3,
				isUpload: item.n5,
				remark: item.n7.str,
				files: fileArrs,
				id: data.state ? item.id : null,
			});
		});
		ElLoading.service();
		foodList.sendApi(formInline, formInline2, formInline3, formInline4, status, data.editStr, data.state, fileArr)
			.then((res) => {
				if (res.code == 200) {
					if (status == "save") {
						ElMessage.success("新增成功");
					} else if (status == "submit") {
						ElMessage.success("提交审核成功");
					} else {
						ElMessage.success("编辑成功");
					}
					tableListFn();

					dialogVisible.value = false;
					data.editStr = null;
					for (let key in formInline) {
						formInline[key] = "";
					}
					for (let key in formInline2) {
						formInline2[key] = "";
					}
					for (let key in formInline3) {
						formInline3[key] = "";
					}
					formInline4.table = [];
					formInline4.delAll = [];
				} else {
					ElMessage.error(res.msg);
				}
				const loadingInstance = ElLoading.service();
				loadingInstance.close();
				const query = proxy.$route?.query
				if (query && query.type == 'abnormalTask') {
					proxy.$router.push('/abnormalTask')
				}
			});
	}
};
const getSearch = () => {
	setTimeout(() => {
		console.log(searchForm.value.status, searchRef.value)
		searchRef.value.searchForm.status = searchForm.value.status
    searchRef.value.searchForm.n8 = searchForm.value.n8
		handleQuery()
	})
}
const changeTime = (time) => {
	if (time) {
		let newTime = new Date(time)
		newTime = newTime.setDate(newTime.getDate() + 1);
		newTime = functionIndex.transformTimestampSearch(newTime)
		return newTime
	} else {
		return null
	}
}
const handleQuery = () => {
	loadingFlag.value = true;
	foodApi
		.drugList({
			size: data.pageSize,
			current: data.pageNum,
			commodityType: "4",
			commonName: searchRef.value.searchForm.n1,
			tradeName: searchRef.value.searchForm.n2,
			pinyinCode: searchRef.value.searchForm.n3,
			commoditySelfCode: searchRef.value.searchForm.n4,
			status: searchRef.value.searchForm.status,
			beginCreateDate: searchRef.value.searchForm?.n6[0],
			endCreateDate: changeTime(searchRef.value.searchForm?.n6[1]),
			beginUpdateDate: searchRef.value.searchForm?.n7[0],
			endUpdateDate: changeTime(searchRef.value.searchForm?.n7[1]),
      source: searchRef.value.searchForm.n8
		})
		.then((res) => {
			if (res.code == 200) {
				table.list = res.data.records;
				data.total = res.data.total;
				table.list.forEach(item => {
					if (item.status == 0) {
						item.disable = '禁用'
					} else {
						item.disable = '正常'
					}
				})
			} else {
				ElMessage.error("搜索失败");
			}
			loadingFlag.value = false;
		});
};
const handleClose = (done) => {
	if (data.title != "查看详情") {
		ElMessageBox.confirm("信息未保存确认取消吗？", "提示", {
			confirmButtonText: "确认",
			cancelButtonText: "取消",
			type: "warning",
		})
			.then(() => {
				done();
				const query = proxy.$route?.query
				if (query && query.type == 'abnormalTask') {
					proxy.$router.push('/abnormalTask')
				}
			})
			.catch(() => {
				// catch error
			});
	} else {
		done();
	}
};
const tableListFn = () => {
	loadingFlag.value = true;
	foodApi
		.drugList({
			commodityType: "4",
			current: data.pageNum,
			size: data.pageSize,
		})
		.then((res) => {
			table.list = res.data.records;
			table.list.forEach(item => {
				if (item.status == 0) {
					item.disable = '禁用'
				} else {
					item.disable = '正常'
				}
			})
			data.total = res.data.total;
			loadingFlag.value = false;
			// console.log(table.list);
		});
};
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	tableListFn();
	let arr = await typeList(3)
	for (let key in arr) {
		data[key] = arr[key];
	}
	await disappApi.natureDrug({"item.id": 11, size: 1000}).then((res) => {
		data.drugType = res.data.records;
	});
	await drugApi.natureDrug({"item.id": 14, size: 1000}).then((res) => {
		data.typecos = res.data.records;
	});
	venterSearch();
	foodApi.TreesDrug({}).then((res) => {
		res.data.forEach((item) => {
			if (new RegExp('食品').test(item['massName'])) {
				data.treesType = [];
				data.treesType.push(item);
			}
		});
	});
});
const cancalFn = () => {
	ElMessageBox.confirm("信息未保存确认取消吗？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(() => {
			dialogVisible.value = false
			const query = proxy.$route?.query
			if (query && query.type == 'abnormalTask') {
				proxy.$router.push('/abnormalTask')
			}
		})
		.catch(() => {
			// catch error
		});
}
const offRow = (scope) => {
	console.log(scope);
	ElLoading.service();
	foodApi
		.offDrug({
			commodityId: scope.row.id,
			status: scope.row.status == "0" ? "1" : "0",
		})
		.then((res) => {
			if (res.code == 200) {
				ElMessage.success("更新成功");
				tableListFn();
			} else {
				ElMessage.error("更新失败");
			}
			const loadingInstance = ElLoading.service();
			loadingInstance.close();
		});
};
const clearForm = () => {
	setTimeout(() => {
		if (dialogRef.value) {
			dialogRef.value.creatform1.clearValidate()
			dialogRef.value.creatform2.clearValidate()
			dialogRef.value.creatform3.clearValidate()
		} else {
			clearForm()
		}
	})
}
const editTable = (row, title) => {
	console.log(row);
	ElLoading.service();
	clearForm()
	data.title = title;
	data.state = true;
	data.status = row.status;
	foodApi
		.searchDrug({
			id: row.id,
		})
		.then((res) => {
			console.log(res);
			if (res.code == 200) {
				let fileArr = [];
				data.editStr = res.data;
				data.editStr.state = functionIndex.echo(row)
				formInline2.n24 = res.data.erpFoodCommodityDTO.registerLicense;
				formInline2.n25 =
					res.data.erpFoodCommodityDTO.registerLicenseDate;
				formInline.n12 =
					res.data.erpFoodCommodityDTO.commodity.manufacture.id;

				dialogVisible.value = true;

				res.data.annexFileDTOS.forEach((item, index) => {
					item.files.forEach(item => {
						item.name = item.filesName
						item.url = item.filesUrl
						item.id = item.filesId
					})
					fileArr.push({
						id: item.id,
						n1: index + 1,
						n2: item.smallType,
						n3: item.categoryName,
						n4: {
							file: item.files
						},
						n5: item.isUpload,
						n6: item.isMultiPage,
						n7: {
							str: item.remark,
							flag: false
						}
					});
				});
				formInline4.table = fileArr;
			}
			const loadingInstance = ElLoading.service();
			loadingInstance.close();
		});
	const allRow = foodList.editList(row)
	for (let key in allRow.formInline) {
		formInline[key] = allRow.formInline[key]
	}
	for (let key in allRow.formInline2) {
		formInline2[key] = allRow.formInline2[key]
	}
	for (let key in allRow.formInline3) {
		formInline3[key] = allRow.formInline3[key]
	}
};
const checkFile = (item) => {
	data.imgUrl = []
	data.checkFlag = true;
	data.imgUrl.push(item.url)
};
const close = () => {
	data.checkFlag = false;
	data.imgUrl = []
}
onMounted(() => {
	const query = proxy.$route?.query
	if (query && query.id && query.type == 'abnormalTask') {
		// console.log(row);
		ElLoading.service();
		data.title = '编辑';
		data.state = true;
		// data.status = row.status;
		foodApi
			.searchDrug({
				id: query.id,
			})
			.then((res) => {
				// console.log(res);
				if (res.code == 200) {
					let row = res.data.erpFoodCommodityDTO.commodity;
					let fileArr = [];
					data.editStr = res.data;
					if (row.status == "1") {
						data.editStr.state = `状态: 审核通过`;
					} else if (row.status == "2") {
						data.editStr.state = `状态: 待审核`;
					} else if (row.status == "3") {
						data.editStr.state = `状态: 审核中`;
					} else if (row.status == "4") {
						data.editStr.state = `状态: 驳回`;
					} else if (row.status == "5") {
						data.editStr.state = `状态: 撤销`;
					} else if (row.status == "6") {
						data.editStr.state = `状态: 草稿`;
					} else {
						data.editStr.state = `状态: 无效`;
					}
					formInline2.n24 =
						res.data.erpFoodCommodityDTO.registerLicense;
					formInline2.n25 =
						res.data.erpFoodCommodityDTO.registerLicenseDate;
					formInline.n12 = row.manufacture.enterpriseName;

					formInline2.n26 = row.importMark == "1" ? true : false;
					formInline.n17 = row.commodityCode;
					formInline.n3 = row.pinyinCode;
					formInline.n2 = row.tradeName;
					formInline.n1 = row.commonName;
					formInline2.n16 = row.grugsType;
					formInline.n9 = row.dosageForm;
					formInline.n10 = row.packageSpecification;
					formInline.n8 = row.validityTime;
					formInline.n13 = row.originPlace;
					formInline.n15 = row.producingArea;
					formInline.n11 = row.listPermitHolder;
					formInline.n5 = row.basicUnit;
					formInline2.n1 = row.gspAttribute;
					formInline.n14 = row.businessScope;
					formInline2.n6 = row.qualityStandard;
					formInline2.n13 =
						row.specialMedicineControl == "1" ? true : false;
					formInline2.n12 = row.premiereVariety == "1" ? true : false;
					formInline2.n7 = row.curingType;
					formInline2.n8 = row.storageTemperature;
					formInline2.n9 = row.storageRange;
					formInline2.n10 = row.transportTemperature;
					formInline2.n11 = row.transportTemperatureRange;
					formInline2.n14 =
						row.specialTemperature == "1" ? true : false;
					formInline2.n23 =
						row.electronicSupervision == "1" ? true : false;
					formInline.n16 = row.remark;
					formInline.n4 = row.commoditySelfCode;
					formInline.n6 = row.completeUnit;
					formInline.n7 = row.ratio;
					formInline3.n1 = row.taxRate;
					formInline3.n2 = row.taxClassifyCode;
					formInline3.n3 = row.treatmentNumber;
					formInline3.n4 = row.medicationClassify;
					formInline3.n5 = row.usageDays;
					formInline3.n6 = row.medicalInsuranceClassify;
					formInline3.n7 = row.usageDosage;
					formInline3.n8 = row.taboo;
					formInline3.n9 = row.mainComponents;
					formInline3.n10 = row.specialStorage;
					formInline3.n11 = row.functionalIndications;
					formInline3.n12 = row.adaptiveSymptoms;
					formInline3.n13 =
						row.essentialMedicines == "1" ? true : false;
					formInline3.n14 =
						row.registeredTrademark == "1" ? true : false;
					formInline3.n15 = row.memberPoint == "1" ? true : false;
					formInline3.n17 =
						row.preciousMedicines == "1" ? true : false;
					formInline3.n18 = row.onlineSales == "1" ? true : false;
					formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
					formInline3.n20 = row.isFragile == "1" ? true : false;
					formInline3.n16 = row.priceProtection == "1" ? true : false;
					formInline2.n22 = row.onlyCode == "1" ? true : false; //
					formInline2.n5 = row.registrationNo;
					formInline2.n2 = row.approvalNumber;
					formInline2.n3 = row.approvalValidity;
					formInline2.n4 = row.proscriptionType;
					dialogVisible.value = true;

					res.data.fileDTOS.forEach((item, index) => {
						fileArr.push({
							id: item.id,
							n1: index,
							n2: {
								str: item.fileCoder,
								flag: false,
							},
							n3: {
								str: item.commodityType,
								flag: false,
							},
							n4: {
								str: item.fileName,
								flag: true,
								type: item.fileType,
								name: "",
								url: item.fileUrl,
							},
							n5: item.fileEndDate,
							n6: {
								str: item.remark,
								flag: false,
							},
						});
					});
					formInline4.table = fileArr;
					ElLoading.service().close()
				}
			})
	}
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	...toRefs(data),
});
</script>
<style lang="scss" scoped>
@import './style/index';
</style>
