<template>
    <div v-loading="loading" class="app-container" element-loading-text="加载中...">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="付款公司" prop="companyName" style="width: 230px">
                    <el-input v-model="queryForm.companyName" clearable placeholder="请选择付款公司" style="width: 200px" @change="handleQuery">
                    </el-input>
                </el-form-item>
                <el-form-item label="时间" prop="queryDate" style="width: 310px">
                    <el-date-picker v-model="queryForm.queryDate" clearable end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"> </el-date-picker>
                </el-form-item>
				<el-form-item label="审批状态" prop="status" style="width: 230px">
                    <el-select v-model="queryForm.status" clearable filterable placeholder="请选择审批状态" @change="handleQuery">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb-40">
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="PaymentApprovalList" @queryTable="getList" />
            </div>
            <column-table key="PaymentApprovalList" :columns="columns" :data="dataList" :max-height="tableHeight" element-loading-text="加载中..." show-summary>
                <template #businessType="{ row }">
                    <span :style="setBusinessTypeColor(row.businessType)">{{ formatDictionaryData('businessTypeList', row.businessType) }}</span>
                </template>
                <template #receiveCompany="{ row }">
                    {{ formatDictionaryData('companyList', row.receiveCompany) }}
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ formatDictionaryData('statusList', row.status) }}</span>
                </template>
				<template #invoiceApply="{ row }">
                    <span :style="setInvoiceApplyColor(row.invoiceApply)">{{ row.invoiceApply == '1' ? '已发起' : '未发起' }}</span>
                </template>
                <template #opt="{ row }">
                    <div>
                        <!-- 付款类型 businessType 1-预存款充值 2-付款单支付 3-收款单收款 -->
                        <!-- 审批状态 status 0-待审批 1-审批通过 2-审批驳回 3-作废 -->
                        <el-button v-if="(row.businessType === '2'|| row.businessType === '3') && row.status === '2'" link icon="el-icon-edit" size="small" type="warning" @click="onOpenPaymentApproval('modify', row)">编辑</el-button>
                        <el-button v-if="row.businessType === '1' && row.status === '2'" link icon="el-icon-edit" size="small" type="warning" @click="onOpenPrepaymentRechargeModification('modify', row)">编辑</el-button>
                        <el-button v-if="row.status === '2'" link icon="el-icon-delete" size="small" type="danger" @click="onCancelPayment(row)">撤销</el-button>
                        <el-button v-if="row.businessType === '2' || row.businessType === '3'" link icon="el-icon-info-filled" size="small" type="primary" @click="onOpenPaymentApproval('detail', row)">查看详情</el-button>
                        <el-button v-if="row.businessType === '1'" link icon="el-icon-info-filled" size="small" type="primary" @click="onOpenPrepaymentRechargeModification('detail', row)">查看详情</el-button>
                    </div>
                </template>
            </column-table>
            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" class="mb0" @pagination="getList" />
            </div>
        </el-card>

        <!-- / 付款单审批、修改	-->
        <payment-order-approval
            v-if="paymentOrderApprovalVisible"
            v-model:payment-order-approval-visible="paymentOrderApprovalVisible"
            :payment-order-approval-id="paymentOrderApprovalId"
            :payment-order-approval-status="paymentOrderApprovalStatus"
            :payment-order-approval-title="paymentOrderApprovalTitle"
            :query-type="queryForm.applyWay"
            @getList="getList"
        />

        <!--  / 预付款充值审批、修改  -->
        <prepayment-recharge-modification
            v-if="prepaymentRechargeModificationVisible"
            v-model:prepayment-recharge-modification-visible="prepaymentRechargeModificationVisible"
            :prepayment-recharge-modification-id="prepaymentRechargeModificationId"
            :prepayment-recharge-modification-status="prepaymentRechargeModificationStatus"
            :prepayment-recharge-modification-title="prepaymentRechargeModificationTitle"
            @getList="getList"
        />
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import {selectDictLabel} from '@/utils/dictLabel';
import PaymentOrderApproval from '@/views/carrierFunction/PaymentOrderApproval.vue';
import PrepaymentRechargeModification from '@/views/carrierFunction/PrepaymentRechargeModification.vue';
import paymentApplication from '@/api/shipperEnd/paymentApplication';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';

export default {
    name: 'PaymentApproval',
    components: { PrepaymentRechargeModification, PaymentOrderApproval, ColumnTable, RightToolbar, SearchButton },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                applyWay: '2',
                companyName: undefined,
                queryDate: [],
                queryStartDate: undefined,
                queryEndDate: undefined,
				status:undefined,
            },
            isShowAll: false,
            total: 0,
            loading: false,
            columns: [
                { title: '发起时间', key: 'applyTime', align: 'center', minWidth: '160px', columnShow: true ,showOverflowTooltip: true},
                { title: '发起人', key: 'applyUser', align: 'center', minWidth: '250px', columnShow: true ,showOverflowTooltip: true},
                { title: '汇款时间', key: 'remitTime', align: 'center', minWidth: '160px', columnShow: true ,showOverflowTooltip: true},
                { title: '付款类型', key: 'businessType', align: 'center', minWidth: '150px', columnShow: true ,showOverflowTooltip: true},
                { title: '付款公司', key: 'companyName', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '收款公司', key: 'receiveCompany', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '汇款金额（元）', key: 'remitAmount', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary', showOverflowTooltip: true },
                { title: '审批状态', key: 'status', align: 'center', minWidth: '120px', columnShow: true,fixed: 'right' },
				{ title: '是否发起开票申请', key: 'invoiceApply', align: 'center', width: '125px', columnShow: true, fixed: 'right' },
                { title: '结算公司', key: 'settleCompany', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', minWidth: '250px', columnShow: true, fixed: 'right',showOverflowTooltip: true }
            ],
            dataList: [],
            businessTypeList: [],
            paymentOrderApprovalVisible: false,
            paymentOrderApprovalTitle: '申请信息',
            paymentOrderApprovalStatus: undefined,
            paymentOrderApprovalId: undefined,
            prepaymentRechargeModificationVisible: false,
            prepaymentRechargeModificationTitle: undefined,
            prepaymentRechargeModificationStatus: undefined,
            prepaymentRechargeModificationId: undefined,
            companyList: [],
            statusList: [],
            tableHeight: 600,
			invoiceStatusList: []
		};
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置业务类型颜色
         */
        setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': { color: '#5cb85c' },
                        '3': { color: '#409EFF' }
                    }[businessType] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' },
                        '3': { color: '#999' }
                    }[status] || { color: '#999' }
                );
            };
        },
		        /**
         * 设置是否发起开票申请颜色
         */
        setInvoiceStatusColor() {
            return (status) => {
                return (
                    {
                        '1': { color: '#606266' }, // 未发起
                        '2': { color: '#1ACD7E' }  // 已发起
                    }[status] || { color: '#999' }
                );
            };
        },
		        /**
         * 设置开票申请颜色
         */
        setInvoiceApplyColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' }
                    }[status] || { color: '#999' }
                );
            };
        }
	},
    created() {
        this.getDict();
        const orgKey = this.$TOOL.data.get('orgKey');
        this.queryForm['carrier.id'] = this.$TOOL.data.get('Organization')[orgKey].id;
        this.handleQuery();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            this.businessTypeList = await this.getDictList('payment_type');
            this.companyList = await this.getDictList('signing_company');
            this.statusList = await this.getDictList('payment_approval_type');
			this.invoiceStatusList = await this.getDictList('cost_invoicing_status_receipt');
        },
        /**
         * 获取列表
         */
        async getList() {
            this.loading = true;
			// eslint-disable-next-line no-unused-vars
            const { queryDate, ...params } = this.queryForm;
            const res = await paymentApplication.getList(params);
            if (res.code === 200) {
                this.dataList = res.data.records;
                this.total = res.data.total;
            } else {
                this.dataList = [];
                this.total = 0;
            }
            this.tableHeight = window.innerHeight - this.$refs.searchCard.$el.offsetHeight - 240;
            this.loading = false;
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { queryDate } = this.queryForm;
            if (queryDate && queryDate.length) {
                this.queryForm.queryStartDate = queryDate[0] ? queryDate[0] + ' 00:00:00' : '';
                this.queryForm.queryEndDate = queryDate[1] ? queryDate[1] + ' 23:59:59' : '';
            } else {
                this.queryForm.queryStartDate = undefined;
                this.queryForm.queryEndDate = undefined;
            }
            this.getList();
        },
        /**
         * 撤销
         */
        onCancelPayment(row) {
            this.$confirm(`<span style="color: red">确认撤销？</span><br/>撤销后付款单将被全部释放为"未申请"状态!`, '', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '确 认',
                cancelButtonText: '取 消',
                showClose: false,
                center: true,
                customClass: 'dialog-confirm',
                confirmButtonClass: 'el-button--large',
                cancelButtonClass: 'el-button--large',
            }).then(async () => {
                try {
                    const res = await paymentOrderApproval.deletePaymentOrder({ids: row.id});
                    if(res.code === 200) {
                        this.$message.success('撤销成功');
                        await this.getList();
                    } else {
                        this.$message.error(res.msg || '撤销失败');
                    }
                } catch(err) {
                    this.$message.error('撤销失败');
                    console.error(err);
                }
            }).catch(() => {});
        },
        /**
         * 打开付款单审批
         * @param status modify 编辑 detail 查看
         * @param row
         * @return {Promise<void>}
         */
        onOpenPaymentApproval(status, row) {
            this.paymentOrderApprovalVisible = true;
            if (status === 'modify') {
                this.paymentOrderApprovalTitle = '';
            } else if (status === 'detail') {
                this.paymentOrderApprovalTitle = '付款单查看';
            }
            this.paymentOrderApprovalStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.paymentOrderApprovalId = row.id;
            }
        },
        /**
         * 打开预付款充值修改
         * @param status modify 编辑 detail 查看
         * @param row
         */
        onOpenPrepaymentRechargeModification(status, row) {
            this.prepaymentRechargeModificationVisible = true;
            if (status === 'modify') {
                this.prepaymentRechargeModificationTitle = '编辑';
            } else if (status === 'detail') {
                this.prepaymentRechargeModificationTitle = '查看';
            }
            this.prepaymentRechargeModificationStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.prepaymentRechargeModificationId = row.id;
            }
        },
        /**
         * 重置查询
         */
        resetQuery() {
            this.$refs.queryForm.resetFields();
            this.handleQuery();
        }
    }
};
</script>
<style lang="scss" scoped></style>
