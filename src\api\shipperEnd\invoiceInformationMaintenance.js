import request from '@/utils/request';

export default {
    // 列表
    getList: function (params) {
        return request.get('/tms/cooperate/invoiceHead/list', params);
    },
    // 新增&编辑保存发票抬头
    save: function (params) {
        return request.post('/tms/cooperate/invoiceHead/save', params);
    },
    // 删除发票抬头
    delete: function (params) {
        return request.delete('/tms/cooperate/invoiceHead/delete', params);
    },
    // 发票抬头下拉
    getInvoiceHeadList: function (params) {
        return request.get('/tms/cooperate/invoiceHead/getInvoiceHeadSelect', params);
    }
};
