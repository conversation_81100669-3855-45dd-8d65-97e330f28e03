<template>
    <div v-loading="fullLoading">
        <!--  <div style="background-color: rgb(255, 255, 255); margin-top: 4px">-->
        <!--    <el-tabs v-model="queryParams.tabtype" @tab-click="handleTabsClick">-->
        <!--      <el-tab-pane label="全部订单" name="0"></el-tab-pane>-->
        <!--      <el-tab-pane label="超时订单" name="1"></el-tab-pane>-->
        <!--      <el-tab-pane label="滞留订单" name="2"></el-tab-pane>-->
        <!--    </el-tabs>-->
        <!--  </div>-->

        <div class="app-container customer-auto-height-container">
            <!-- s 搜索项-->
            <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
                <el-form ref="queryForm" :inline="true" :label-width="isShow ? 'auto' : ''" :model="queryParams" class="seache-form" @submit.native.prevent>
                    <el-form-item label="订单号" prop="orderNo" style="width: 195px">
                        <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号" @keyup.enter.native="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="运单号" prop="transOrderNo" style="width: 280px">
                        <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单号" @keyup.enter.native="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item label="货主公司" prop="companyId" style="width: 280px">
                        <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">
                            <el-option v-for="(dict, index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="订单状态" prop="status">
                        <el-select v-model="queryParams.status" clearable placeholder="请选择订单状态" @change="handleQuery">
                            <el-option v-for="dict in statusDicts" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="发件公司" prop="sendCompany" style="width: 280px">
                        <el-input v-model="queryParams.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter.native="handleQuery" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="发件地址" prop="sendAddress">
                        <el-cascader v-model="queryParams.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择发件地址" @change="handleQuery" @visible-change="visibleChange" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="收件公司" prop="receiverCompany" style="width: 280px">
                        <el-input v-model="queryParams.receiverCompany" clearable placeholder="请输入收件公司" @keyup.enter.native="handleQuery"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="收件地址" prop="receiverAddress">
                        <el-cascader v-model="queryParams.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择收件地址" @change="handleQuery" @visible-change="visibleChange" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="产品分类" prop="productClass">
                        <el-select v-model="queryParams.productClass" clearable filterable placeholder="请选择产品分类" @change="handleQuery">
                            <el-option v-for="dict in fourplProductClassOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="运输类型" prop="productType">
                        <el-select v-model="queryParams.productType" clearable filterable placeholder="请选择运输类型" @change="handleQuery">
                            <el-option v-for="dict in productTypeDicts" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="温层类型" prop="temperatureTypeTemp" style="width: 280px">
                        <el-select v-model="queryParams.temperatureTypeTemp" clearable collapse-tags collapse-tags-tooltip filterable multiple placeholder="请选择温层类型" @change="handleQuery">
                            <el-option v-for="(dict, index) in temperatureTypeDicts" :key="index" :label="dict.describtion" :value="dict.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="付款方式" prop="paymentMethod">
                        <el-select v-model="queryParams.paymentMethod" clearable filterable placeholder="请选择付款方式" @change="handleQuery">
                            <el-option v-for="(dict, index) in fourplPaymentMethodOptions" :key="index" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" class="item-l-0" label="揽收方式" prop="orderType">
                        <el-select v-model="queryParams.orderType" clearable placeholder="请选择揽收方式" @change="handleQuery">
                            <el-option v-for="dict in collectionMethod" :key="dict.value" :label="dict.name" :value="dict.value * 1" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="件数" prop="goodsPackages">
                        <div style="width: 220px">
                            <el-select v-model="queryParams.prePackageOption" style="width: 45%">
                                <el-option v-for="(item, index) in prePackageOptions" :key="index" :label="item.name" :value="item.value"> </el-option>
                            </el-select>
                            <el-input v-model="queryParams.goodsPackages" clearable placeholder="请输入件数" style="width: 55%" @keyup.enter.native="handleQuery" />
                        </div>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="货值金额" prop="goodsAmount">
                        <div style="width: 220px">
                            <el-select v-model="queryParams.preAmountOption" style="width: 45%">
                                <el-option v-for="(item, index) in prePackageOptions" :key="index" :label="item.name" :value="item.value"> </el-option>
                            </el-select>
                            <el-input v-model="queryParams.goodsAmount" clearable placeholder="请输入货值金额" style="width: 55%" @keyup.enter.native="handleQuery" />
                        </div>
                    </el-form-item>
                    <el-form-item class="date-screening" label="下单时间" prop="queryTime" style="width: 320px">
                        <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="随货同行单号" prop="externalOrderNo">
                        <el-input v-model="queryParams.externalOrderNo" clearable placeholder="请输入随货同行单号" @keyup.enter.native="handleQuery"></el-input>
                    </el-form-item>
                    <search-button :loading="loading" :is-show-all="isShow" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
                </el-form>
            </el-card>
            <!--  /统计行  -->
            <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <div class="flex justify-around" v-loading="loading">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <el-statistic :value="statisticalData.freezeCount || 0" :value-style="{ color: '#3cccca', fontWeight: 'bold' }" group-separator="," title="冷藏件数："></el-statistic>
                        <span>占比：{{ statisticalData.totalCount > 0 ? (statisticalData.freezeCount / statisticalData.totalCount * 100).toFixed(1) + '%' : '0.0%' }}</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <el-statistic :value="statisticalData.refrigerateCount || 0" :value-style="{ color: '#ac86ff', fontWeight: 'bold' }" group-separator="," title="冷冻件数："></el-statistic>
                        <span>占比：{{ statisticalData.totalCount > 0 ? (statisticalData.refrigerateCount / statisticalData.totalCount * 100).toFixed(1) + '%' : '0.0%' }}</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <el-statistic :value="statisticalData.tempControlCount || 0" :value-style="{ color: '#feb119', fontWeight: 'bold' }" group-separator="," title="控温件数："></el-statistic>
                        <span>占比：{{ statisticalData.totalCount > 0 ? (statisticalData.tempControlCount / statisticalData.totalCount * 100).toFixed(1) + '%' : '0.0%' }}</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <el-statistic :value="statisticalData.normalCount || 0" :value-style="{ color: '#53c2ff', fontWeight: 'bold' }" group-separator="," title="常温件数："></el-statistic>
                        <span>占比：{{ statisticalData.totalCount > 0 ? (statisticalData.normalCount / statisticalData.totalCount * 100).toFixed(1) + '%' : '0.0%' }}</span>
                    </div>
                    <el-statistic :value="statisticalData.totalCount" :value-style="{ color: '#fbb1a1', fontWeight: 'bold' }" group-separator="," title="总件数："></el-statistic>
                    <el-statistic :value="statisticalData.cancelCount" :value-style="{ color: '#ff6b6b', fontWeight: 'bold' }" group-separator="," title="已取消件数："></el-statistic>
                </div>
            </el-card>

            <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
                <div class="mb10" style="display: flex">
                    <el-button v-if="queryParams.tabtype == '0'" v-hasPermi="['fourPL:order:add']" icon="el-icon-plus" type="primary" @click="openTheNewOrderSlider">下单 </el-button>
                    <el-button v-if="queryParams.tabtype == '0'" v-hasPermi="['fourpl:order:batchConfirm']" :disabled="multiple" icon="el-icon-check" type="success" @click="batchConfirmationOrder">暂存单提交 </el-button>
                    <el-button v-if="queryParams.tabtype == '0'" v-hasPermi="['fourpl:order:batchSignature']" :disabled="multiple" icon="el-icon-finished" type="success" @click="batchTransferCustomer">批量签收 </el-button>
                    <el-button :loading="loading" icon="el-icon-download" type="warning" @click="handleExport">导出</el-button>
                    <right-toolbar :loading="loading" v-model:columns="columns" v-model:showSearch="showSearch" style="margin-left: auto" table-i-d="ordeList0" @queryTable="getList"></right-toolbar>
                </div>

                <column-table
                    key="ordeList0"
                    ref="ColumnTable"
                    :loading="loading"
                    :columns="columns"
                    :data="alList"
                    class="customer-auto-height-table"
                    max-height="null"
                    :defaultSort="{ prop: 'createDate', order: 'descending' }"
                    :show-check-box="true"
                    @select="handleSelectionChange"
                    @select-all="selectAll"
                    @row-dblclick="(row) => getDetail(row)"
                >
                    <template #status="{ row }">
                        <span v-if="row.status == 1" style="color: #5670fe">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 2" style="color: #ff2a2a">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 3" style="color: #ff2a2a">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 4" style="color: #1acd7e">{{ statusFormater(row) }}</span>
                        <span v-else-if="row.status == 5" style="color: #b1b1b1">{{ statusFormater(row) }}</span>
                        <span v-else>{{ statusFormater(row) }}</span>
                    </template>
                    <template #sendAddress="{ row }">
                        <span>{{ row?.sendTown?.province || '' }}{{ row?.sendTown?.city || '' }}{{ row?.sendTown?.county || '' }}{{ row?.sendTown?.town || '' }}{{ row?.sendAddress || '' }}</span>
                    </template>
                    <template #receiverAddress="{ row }">
                        <span>{{ row?.receiverTown?.province || '' }}{{ row?.receiverTown?.city || '' }}{{ row?.receiverTown?.county || '' }}{{ row?.receiverTown?.town || '' }}{{ row?.receiverAddress || '' }}</span>
                    </template>
                    <template #orderType="{ row }">
                        <span>{{ dictionaryFormatting(collectionMethod, row.orderType) }}</span>
                    </template>
                    <template #productType="{ row }">
                        <span>{{ fourplProductTypeFormat(row) }}</span>
                    </template>
                    <template #paymentMethod="{ row }">
                        <span>{{ fourplPaymentMethodFormat(row) }}</span>
                    </template>
                    <template #productClass="{ row }">
                        <span>{{ fourplProductClassFormat(row) }}</span>
                    </template>
                    <template #paymentStatus="{ row }">
                        <span>{{ fourplOrderPaymentStatusFormat(row) }}</span>
                    </template>
                    <template #temperatureType="{ row }">
                        <span>{{ row.temperatureType.describtion || '' }}</span>
                    </template>

                    <template #createDate="{ row }">
                        <span>{{ timeFormatting(row.createDate) }}</span>
                    </template>
                    <template #cancelType="{ row }">
                        <span>{{ row.cancelType == '0' ? row.cancelReason : fourplCancelAnOrderTypeFormat(row) }}</span>
                    </template>
                    <template #cancelUser="{ row }">
                        <span>{{ row?.cancelUser?.name || '' }}</span>
                    </template>
                    <template #printDeliveryFlag="{ row }">
                        <span :style="row.printDeliveryFlag == '1' ? 'color: #1acd7e' : 'color: #ff2a2a'">{{ row.printDeliveryFlag == '1' ? '是' : '否' }}</span>
                    </template>
                    <template #opt="{ row }">
                        <el-button v-if="queryParams.tabtype == '0' && row.status == 2 && row.orderType == '2'" v-hasPermi="['fourpl:order:confirm']" icon="el-icon-check" link size="small" type="success" @click="batchConfirmOrder(row)">确认订单</el-button>
                        <el-button v-if="row.status == '3' || row.status == '4' || row.status == '6'" icon="el-icon-printer" link size="small" type="warning" @click="cancelOrder(row)">打印箱签</el-button>
                        <el-button v-if="queryParams.tabtype == '1'" icon="el-icon-paperclip" link size="small" type="primary" @click="dispatch(row)">派单</el-button>
                        <el-dropdown>
                            <el-button v-if="queryParams.tabtype == '0'" icon="el-icon-arrow-down" link>更多</el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item v-if="queryParams.tabtype == '0' && row.status == 3" v-hasPermi="['fourpl:order:signature']" icon="el-icon-finished" style="color: #67c23a" @click.native="transferCustomer(row)">签收</el-dropdown-item>
                                    <el-dropdown-item icon="el-icon-info-filled" style="color: #67c23a" @click.native="getDetail(row)">详情</el-dropdown-item>
                                    <el-dropdown-item v-if="queryParams.tabtype == '0' && (row.status == 1 || row.status == 0 || row.status == 2 || row.status == 3)" icon="el-icon-close" style="color: #f56c6c" @click.native="cancelAnOrderClick(row)">取消</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </column-table>

                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
            </el-card>
        </div>
        <!--s 确认订单 el-drawer-->
        <confirm-an-order v-if="addOrderSwitch" :order-info="form" :title="orderSliderTitle" @close="onClose"></confirm-an-order>
        <driverSelect v-if="driverOpen" :driverData="driverData" :type="'flow_driver'" :values="values" @changeShow="changeShow" @onConfirm="binding"></driverSelect>
        <!--    改派页面-->
        <el-dialog v-model="changeDriverOpen" v-dialogDrag append-to-body title="改派" width="850px">
            <el-form key="changeDriver" ref="formChangeDriver" :model="formChangeDriver" :rules="rulesChangeDriver" label-width="110px">
                <el-form-item label="运单号" prop="transOrderNo">
                    <el-input v-model="formChangeDriver.transOrderNo" disabled placeholder="请输入运单号" />
                </el-form-item>
                <el-form-item label="转给司机" prop="newDdriverCode">
                    <el-select v-model="formChangeDriver.newDdriverCode" clearable filterable placeholder="请选择司机" size="small">
                        <el-option v-for="(item, idx) in driverList" :key="item.driverCode" :label="item.driverName" :value="item.driverCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="转单原因" prop="reason">
                    <el-input v-model="formChangeDriver.reason" placeholder="请输入转单原因" type="textarea" />
                </el-form-item>
                <div class="dialog-footer">
                    <el-button
                        @click="
                            changeDriverOpen = false;
                            getList();
                        "
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="changeDriver">保存</el-button>
                </div>
            </el-form>
        </el-dialog>
        <!--    打印箱签滑块-->
        <el-drawer v-model="printBox" direction="rtl" size="50%" title="打印箱签">
            <print-box-tag v-if="printBox" :orderInfo="orderInfo" @callbackMethod="closePrintBoxLabel"></print-box-tag>
        </el-drawer>
        <!--    订单详情滑块-->
        <el-drawer v-model="detailOrder" direction="rtl" size="50%" title="订单详情">
            <order-detail-trans v-if="detailOrder" :orderInfo="orderInfo" :parent-data="transportationData"></order-detail-trans>
        </el-drawer>
        <!--  订单轨迹滑块  -->
        <el-drawer v-model="reviewProgressSwitch" size="500px" title="订单轨迹">
            <div class="p10" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <el-timeline :reverse="reverse">
                        <el-timeline-item v-for="(activity, i) in activities" :key="i" :timestamp="timeFormatting(activity.createDate)" :type="i == 0 ? 'success' : ''">
                            <div>{{ activity.content }}</div>
                        </el-timeline-item>
                    </el-timeline>
                </el-card>
            </div>
        </el-drawer>
        <!--    批量确认提醒-->
        <el-dialog v-model="batchConfirmationOrderShow" :show-close="false" append-to-body class="icon-dialog" modal-append-to-body title="批量确认" width="400px">
            <div v-for="(item, index) in receivingModeList" :key="index" style="margin-top: 10px">
                <div style="display: flex; justify-content: space-around; font-size: 16px; color: #333333">
                    <div>{{ item.name }}</div>
                    <div style="color: #ea0008; margin-left: 20px">{{ item.num }}单</div>
                </div>
            </div>
            <template #footer>
                <el-button @click="cancelBatchConfirmationOrder">取消</el-button>
                <el-button type="primary" @click="submitBatchConfirmationOrder">确定</el-button>
            </template>
        </el-dialog>

        <!--    取消订单弹窗-->
        <el-dialog v-model="cancelAnOrderOpen" append-to-body dialogDrag title="取消订单" width="50%" @close="cancelAnOrderOpen = false">
            <el-form key="cancelAnOrder" ref="formCancelAnOrder" :model="formCancelAnOrder" :rules="cancelAnOrderRules" style="width: 100%">
                <el-form-item label="订单编号:" label-width="110px" prop="orderNo">
                    {{ formCancelAnOrder.orderNo }}
                </el-form-item>
                <el-form-item label-width="40px" prop="cancelType">
                    <el-radio-group v-model="formCancelAnOrder.cancelType" style="width: 100%">
                        <div style="display: flex; flex-direction: column; width: 100%">
                            <el-radio v-for="(item, index) in fourplOrderChangeTypeOptions" :key="index" :label="item.value" border>
                                <div style="display: flex; align-items: center; flex-grow: 1">
                                    <div style="margin-right: 10px">{{ item.name }}</div>
                                    <el-form-item v-if="formCancelAnOrder.cancelType == '0' && item.value == '0'" label-width="0px" prop="cancelReason" style="width: 100%">
                                        <el-input v-model="formCancelAnOrder.cancelReason" maxlength="50" placeholder="请输入其他取消原因" show-word-limit type="text" />
                                    </el-form-item>
                                </div>
                            </el-radio>
                        </div>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelAnOrderOpen = false">取消</el-button>
                <el-button type="primary" @click="cancelOrderConfirm">确认取消</el-button>
            </template>
        </el-dialog>
        <!--    客户签收-->
        <el-drawer v-model="signatureShow" :title="signatureTitle" direction="rtl" size="50%">
            <transfer-customer v-if="signatureShow" :signatureList="signatureList" @callbackMethod="closeSignature"></transfer-customer>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar';
import driverSelect from '@/components/driverSelect';
import ConfirmAnOrder from '@/views/orderComponents/ConfirmAnOrder.vue';
import TransferCustomer from '@/views/logisticsManagement/orderManagement/TransferCustomer.vue';
import OrderDetailTrans from '@/views/orderComponents/OrderDetailTrans.vue';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation.js'; // 合作配置
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration.js'; // 其他配置
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
import PrintBoxTag from '@/views/orderComponents/PrintBoxTag.vue';
import { ElLoading } from 'element-plus';
import moment from 'moment';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'OrderList',
    components: {
        ConfirmAnOrder,
        TransferCustomer,
        SearchButton,
        ColumnTable,
        RightToolbar,
        driverSelect,
        PrintBoxTag,
        OrderDetailTrans
    },
    data() {
        return {
            fourplProductClassOptions: [],
            //冷链明细 数据
            coldChainProductData: [
                {
                    name: '',
                    specifications: '',
                    manufacturer: '',
                    batchNumber: '',
                    quantity: 1,
                    completeInformation: ''
                }
            ],
            // // 订单信息 数据
            form: {
                pickupAddress: '', //发件地址
                shippingAddress: '', //收件地址
                sendAddress: '', //发件地址 详细地址
                receiverAddress: '', //收件地址 详细地址
                sendUser: '', //取件联系人
                receiverUser: '', //收件人名称
                sendUserPhone: '', //取件人电话
                receiverUserPhone: '', //收件人电话
                remark: '', //备注
                sendCompany: '', //发件公司
                receiverCompany: '', //收件公司
                productType: '', //货品类型
                temperatureType: '', //温层类型
                goodsName: '', //货品名称
                vehicleSelection: '', //车型选择
                goodsPackages: '', //件数
                productProperties: '', //货品属性
                productWeight: 0, //重量
                externalOrderNo: '', //随货同行单号
                productVolume: 0, //容积
                paymentMethod: '', //付款方式
                deliveryTime: '', //送达时间
                appointmentTime: '', //取件时间
                addedServices: [], //附加服务
                agreeToTermsCarrier: false, //阅读并同意
                orderCost: 0.0, // 预估费用
                costData: [] // 费用明细
            },
            ownerList: [], // 货主公司下拉选

            addOrderSwitch: false, //新增订单开关
            // 搜索项
            queryParams: {
                tabtype: '0',
                current: 1,
                size: 10,
                orderNo: null,
                transOrderNo: null,
                orderType: null,
                receiverCompany: null,
                createDate: null,
                receiverUser: null,
                receiverUserPhone: null,
                receiveraddress: null,
                productType: null,
                paymentStatus: null,
                sysCompanyName: null,
                sysCompanyPhone: null,
                sysCompanyDept: null,
                sendAddress: [],
                receiverAddress: [],
                queryTime: [],
                prePackageOption: null,
                temperatureTypeTemp: null,
                preAmountOption: null,
                goodsAmount: null,
                externalOrderNo:undefined
            },
            //承运商表格数据
            alList: [],
            reverse: true, //排序方向
            reviewProgressSwitch: false,
            multiple: true,
            //订单列表
            orderList: [],
            total: 0, // 总条数
            loading: false,
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', width: '120px', fixed: 'left', columnShow: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '120px', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收方式', key: 'orderType', align: 'center', width: '120px', columnShow: true },
                { title: '订单状态', key: 'status', align: 'center', columnShow: true },
                { title: '下单时间', key: 'createDate', width: '200px', align: 'center', columnShow: true, sortable: true },
                { title: '随货同行单号', key: 'externalOrderNo', width: '200px', align: 'center', columnShow: true, sortable: true },
                { title: '是否打印', key: 'printDeliveryFlag', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件人', key: 'sendUser', width: '120px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件人电话', key: 'sendUserPhone', width: '120px', align: 'center', columnShow: true },
                { title: '发件公司', key: 'sendCompany', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendAddress', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件人', key: 'receiverUser', width: '120px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件人电话', key: 'receiverUserPhone', width: '120px', align: 'center', columnShow: true },
                { title: '收件公司', key: 'receiverCompany', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', width: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '件数', key: 'goodsPackages', align: 'center', columnShow: true },
                { title: '公里数', key: 'kilometre', align: 'center', columnShow: true },
                { title: '运输类型', key: 'productType', align: 'center', columnShow: true },
                { title: '产品分类', key: 'productClass', align: 'center', columnShow: true },
                { title: '保价费（元）', key: 'insureFee', width: '110px', align: 'center', columnShow: true },
                { title: '温层类型', key: 'temperatureType', width: '200px', align: 'center', columnShow: true },
                { title: '付款方式', key: 'paymentMethod', width: '100px', align: 'center', columnShow: true },
                { title: '付款状态', key: 'paymentStatus', width: '100px', align: 'center', columnShow: true },
                { title: '货值金额', key: 'goodsAmount', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '详情说明', key: 'detailDesc', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '取消原因', key: 'cancelType', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '取消操作人', key: 'cancelUser', width: '100px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', fixed: 'right', width: '200px', hideFilter: true, columnShow: true }
            ],
            values: {},
            driverData: [],
            driverOpen: false,
            formChangeDriver: {},
            driverList: [], //司机列表
            rulesChangeDriver: {
                newDdriverCode: [{ required: true, message: '转给司机不能为空' }]
            },
            activities: [],
            detailOrder: false,
            detail: false,
            OrderDetails: false,
            printBox: false,
            // change: false,
            appraiseOrder: false,
            overOpen: false,
            statusDicts: [], //状态字典
            productAttributeDictionary: [], // 商品属性 字典
            productTypeDicts: [], // 运输类型 字典
            sysAreas: [], //省市区数据
            belongUserId: '95', //订单所属人ID（租户/货主公司ID）
            createUserId: '5', //下单人ID
            pickupAddressDefaultAddress: '0', //发件地址默认地址
            shippingAddressDefaultAddress: '0', //收件地址默认地址
            addServiceList: [], //新增订单增值服务
            changeDriverOpen: false, // 改派
            // 发件地址
            sendAddressParams: {
                total: 0,
                type: 1,
                current: 1,
                size: 10,
                searchValue: ''
            },
            receiverAddressBook: [],
            sendAddressBook: [],
            // 收件地址
            receiverAddressParams: {
                total: 0,
                type: 2,
                current: 1,
                size: 10,
                searchValue: ''
            },
            orderSliderTitle: '', //订单详情弹窗标题
            // 显示搜索条件
            showSearch: true,
            temperatureTypeDicts: [], //温层类型字典
            orderDetailCheck: false, //冷链详情开关
            orderInfo: {
                addedServices: [],
                fileList: [],
                orderDetailList: []
            },
            title: '',
            // 时间段选择开关
            timePeriodSelectorSwitch: false,
            costCreakdownShow: false, // 费用明细弹窗
            orderBranchData: {}, // 获取网点等信息
            costDataList: [], // 结算费用展示
            addedServiceShow: false, // 是否显示增值服务弹窗
            formService: {}, // 增值服务修改项
            serviceRules: {
                inputValue: [
                    {
                        required: true,
                        message: '请输入值',
                        trigger: 'blur'
                    }
                ]
            },
            collectionMethod: [],
            selectRows: [], // 选中的值
            selectData: [], // 可批量确认订单数据
            fourplPaymentMethodOptions: [], // 付款方式
            batchConfirmationOrderShow: false, // 批量确认提示
            receivingModeList: [], // 揽收方式
            prePackageOptions: [],
            fourplOrderPaymentStatusDicts: [], // 付款状态
            fullLoading: false,
            shortcuts: setDatePickerShortcuts(),
            // formula: null, //计算公式
            // 取消订单的订单号
            cancelAnOrderOpen: false, // 取消订单弹窗展示
            formCancelAnOrder: {}, // 取消订单参数
            fourplOrderChangeTypeOptions: [], // 取消原因
            cancelAnOrderRules: {
                cancelType: [{ required: true, message: '请选择取消原因', trigger: 'blur' }],
                cancelReason: [
                    {
                        validator: (rule, value, callback) => {
                            if (this.formCancelAnOrder.type == '0' && (value == '' || value == undefined)) {
                                callback(new Error('请输入其他取消原因'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ]
            },
            ownerAccountInfo: {},
            signatureList: [], // 客户签收数据
            signatureShow: false, // 客户签收显示
            signatureTitle: '客户签收', // 客户签收标题
            isShow: false,
            typeOptions: [],
            transportationData: {},
            carrierWayDicts: [], // 运输方式 字典值
            statisticalData: {
                freezeCount: undefined, // 冷藏件数
                refrigerateCount: undefined, // 冷冻件数
                tempControlCount: undefined, // 控温件数
                normalCount: undefined, // 常温件数
                totalCount: undefined // 总件数
            }
        };
    },
    computed: {
        // 字典翻译
        dictionaryFormatting() {
            return (data, value) => {
                return this.selectDictLabel(data, value);
            };
        },
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },

    async created() {
        // 获取货主公司
        this.getCompanySelect();
        /** 产品分类 */
        this.fourplProductClassOptions = await this.getDictList('fourpl_product_class');
        // 订单状态字典
        this.statusDicts = await this.getDictList('fourpl_order_status');
        // 运输类型4PL
        this.productTypeDicts = await this.getDictList('fourpl_product_type');
        // // 揽收任务状态字典
        // this.fourplLanTaskStatusOptions = await this.getDictList('fourpl_lan_task_status');
        // 商品属性 字典
        this.productAttributeDictionary = await this.getDictList('fourpl_product_attributes');
        /** 交接方式 */
        // this.fourplHandWayOptions = await this.getDictList('fourpl_hand_way');
        /** 付款方式 */
        let fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
        this.fourplPaymentMethodOptions = fourplPaymentMethodOptions.filter((item) => item.value != '4' && item.value != '5' && item.value != '6');
        /** 揽收方式 */
        this.collectionMethod = await this.getDictList('fourpl_mail_service');
        this.receivingModeList = JSON.parse(JSON.stringify(this.collectionMethod));
        /** 付款状态 */
        this.fourplOrderPaymentStatusDicts = await this.getDictList('fourpl_order_payment_status');
        /** 付款状态订单件数搜索条件 */
        this.prePackageOptions = await this.getDictList('fourpl_order_equals');
        /** 运输方式 */
        this.carrierWayDicts = await this.getDictList('fourpl_transportation_mode');

        // 获取温层类型字典
        this.getTemperatureType();
        // 默认设置当天
        let now = moment(new Date()).format('YYYY-MM-DD');
        this.queryParams.queryTime = [now, now];
        this.handleQuery();
        this.getCarType(); // 车辆类型
    },
    methods: {
        //确认订单显示窗口
        batchConfirmOrder(row) {
            const { id } = row;
            this.orderSliderTitle = '确认订单';
            orderManagement.queryOrderDrugById({ id }).then((res) => {
                if (res.code === 200 && res.data) {
                    const loading = ElLoading.service({
                        lock: true,
                        text: '生成订单信息中...',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    setTimeout(() => {
                        loading.close();
                        // 打开弹窗
                        this.$nextTick(() => {
                            // res.data赋值给form 不是替换
                            // this.form.agreeToTermsCarrier = [];

                            Object.assign(this.form, res.data);
                            // 数据回显
                            this.form.pickupAddress = [res.data.sendProvinceId, res.data.sendCityId, res.data.sendCountyId, res.data.sendTown.id] || [];
                            this.form.shippingAddress = [res.data.receiverProvinceId, res.data.receiverCityId, res.data.receiverCountyId, res.data.receiverTown.id] || [];

                            this.form.productTypeDesc = this.dictionaryFormatting(this.productTypeDicts, this.form.productType);
                            this.form.temperatureTypeDesc = this.form.temperatureType.describtion;
                            this.form.productClassDesc = this.fourplProductClassFormat({ productClass: this.form.productClass });
                            this.form.orderTypeDesc = this.dictionaryFormatting(this.collectionMethod, this.form.orderType);
                            this.form.paymentMethodDesc = this.dictionaryFormatting(this.fourplPaymentMethodOptions, this.form.paymentMethod);
                            this.form.productPropertiesDesc = this.selectDictLabels(this.productAttributeDictionary, this.form.productProperties);
                            if (this.form.carType) {
                                this.form.carTypeName = this.selectDictLabel(this.typeOptions, this.form.carType);
                            }
                            this.form.carrierWay = this.form.carrierWay || '1';
                            this.form.carrierWayDesc = this.selectDictLabel(this.carrierWayDicts, this.form.carrierWay);
                            this.form.costData = [];
                            orderManagement.orderDetailCostInfo({ orderId: row.id }).then((response) => {
                                if (response.code == 200 && response.data.addedServiceDTOList) {
                                    this.orderInfo.addedServices = response.data.addedServiceDTOList;
                                }
                            });
                            // this.form.orderCost = Number(res.data.orderCost);
                            // this.$delete(this.form, 'orderCost');
                            // this.temperatureType = res.data.tempType;
                            // 获取温层类型字典
                            // this.getAllAvailableAddService();
                            // this.getOrderCost();
                            // this.getAdvanceByCompany(); // 货主公司货主公司账号信息
                            this.addOrderSwitch = true;
                        });
                    }, 500);
                } else if (res.code == 200 && !res.data) {
                    this.msgError('数据获取失败！');
                }
            });
        },
        //批量确认订单
        batchConfirmationOrder() {
            this.selectData = [];
            let flag = false;
            try {
                this.selectRows.forEach((item) => {
                    if (item.status == 1) {
                        this.selectData.push(item);
                    } else {
                        this.msgError('【暂存单】状态的订单才能批量确认！');
                        flag = true;
                        throw Error();
                    }
                });
            } catch (e) {}
            if (flag) return;
            this.receivingModeList.forEach((item, index) => {
                let num = 0;
                this.selectData.forEach((order) => {
                    if (order.orderType == item.value + '') {
                        num = num + 1;
                    }
                });
                this.receivingModeList[index].num = num;
            });
            this.batchConfirmationOrderShow = true;
        },
        // 客户批量签收
        batchTransferCustomer() {
            this.signatureList = [];
            let flag = false;
            try {
                this.selectRows.forEach((item) => {
                    if (item.status == 3) {
                        this.signatureList.push(item);
                    } else {
                        this.msgError('【运输中】状态的订单才能批量签收！');
                        flag = true;
                        throw Error();
                    }
                });
            } catch (e) {}
            if (flag) return;
            this.signatureTitle = '客户批量签收';
            this.signatureShow = true;
        },
        // 绑定司机
        binding(data) {
            let params = data.data;
            params.driverCode = data.driverList[0].driverCode;
            params.driverName = data.driverList[0].driverName;
            updateLanTask(params).then((response) => {
                this.msgSuccess('派单成功');
                this.values.open = false;
                this.driverOpen = false;
                this.getList();
            });
        },

        // 取消订单展示
        async cancelAnOrderClick(row) {
            if (!this.fourplOrderChangeTypeOptions || this.fourplOrderChangeTypeOptions.length == 0) {
                /** 异动类型4PL */
                this.fourplOrderChangeTypeOptions = await this.getDictList('fourpl_order_change_type');
            }
            this.formCancelAnOrder = {
                ...row,
                cancelReason: null,
                cancelType: '0'
            };
            this.cancelAnOrderOpen = true;
        },
        // 取消批量确认
        cancelBatchConfirmationOrder() {
            this.batchConfirmationOrderShow = false;
        },
        //打印箱签显示窗口
        cancelOrder(row) {
            this.orderInfo = row;
            this.printBox = true;
        },
        // 取消订单
        cancelOrderConfirm() {
            this.$refs.formCancelAnOrder.validate((result) => {
                if (result) {
                    if (this.formCancelAnOrder.cancelType != '0') {
                        this.formCancelAnOrder.cancelReason = this.selectDictLabel(this.fourplOrderChangeTypeOptions, this.formCancelAnOrder.cancelType);
                    }
                    orderManagement.cancelOrder(this.formCancelAnOrder).then((res) => {
                        if (res.code == 200) {
                            this.msgSuccess('取消成功');
                            this.$refs['formCancelAnOrder'].resetFields();
                            this.cancelAnOrderOpen = false;
                            this.getList();
                        }
                    });
                }
            });
        },
        // 改派
        changeDriver() {
            this.formChangeDriver.oldDriverCode = this.orderInfo.driverCode;
            changeDriver(this.formChangeDriver).then((response) => {
                this.msgSuccess('改派成功');
                this.changeDriverOpen = false;
                this.getList();
            });
        },
        //派单
        changeShow(value) {
            this.values.open = value;
            this.driverOpen = value;
        },
        // 更改温层选择后的操作
        checkTemperature(data) {
            this.temperatureTypeDicts.forEach((t) => {
                if (t.id * 1 === this.form.temperatureType * 1) {
                    // this.form.lowTemperature = t.lowTemperature;
                    // this.form.highTemperature = t.highTemperature;
                    this.orderDetailCheck = t.type !== '1' && t.type !== '2';
                }
            });
        },
        //关闭打印箱签
        closePrintBoxLabel() {
            this.printBox = false;
            this.getList();
        },
        // 关闭弹窗并刷新
        closeSignature() {
            this.signatureShow = false;
            this.getList();
        },
        // 订单确认取消
        confirmOrderCancel() {
            this.addOrderSwitch = false;
        },

        dispatch(row) {
            const id = row.id || this.ids;
            this.driverData = [];
            getLanTask(id).then((response) => {
                if (response.data.driverName && response.data.driverCode) {
                    this.driverData = [{ driverName: response.data.driverName, driverCode: response.data.driverCode }];
                }
                this.values = {
                    minNum: 1, // 最少绑定一个
                    maxNum: 1,
                    title: '派单',
                    open: true,
                    data: response.data,
                    type: 2,
                    taskCode: row.taskCode
                };
                this.driverOpen = true;
            });
        },
        /** 异动类型4PL字典转换 */
        fourplCancelAnOrderTypeFormat(val) {
            return this.selectDictLabel(this.fourplOrderChangeTypeOptions, val.cancelType);
        },
        /** 付款状态4PL字典转换 */
        fourplOrderPaymentStatusFormat(val) {
            return this.selectDictLabel(this.fourplOrderPaymentStatusDicts, val.paymentStatus);
        },
        /** 付款方式4PL字典转换 */
        fourplPaymentMethodFormat(val) {
            return this.selectDictLabel(this.fourplPaymentMethodOptions, val.paymentMethod);
        },
        /** 产品分类 */
        fourplProductClassFormat(val) {
            return this.selectDictLabel(this.fourplProductClassOptions, val.productClass);
        },
        /** 运输类型4PL字典转换 */
        fourplProductTypeFormat(val) {
            return this.selectDictLabel(this.productTypeDicts, val.productType);
        },
        // 查询车辆类型
        getCarType() {
            operationConfiguration.listCarType({ carrierId: this.orderInfo.carrierId, status: '0', size: 1000 }).then((response) => {
                this.typeOptions = [];
                if (response.code == 200 && response?.data?.records.length > 0) {
                    this.typeOptions = response?.data?.records.map((item) => {
                        return { name: item.typeName, value: item.typeCode };
                    });
                }
            });
        },
        // 获取货主公司下拉
        getCompanySelect() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },

        getDetail(row) {
            // 将row.id传给子组件 order-detail-trans 用于查询 运输记录
            this.transportationData.isEdit = false;
            this.transportationData.orderNo = row.orderNo;
            this.transportationData.transOrderNo = row.transOrderNo;
            this.transportationData.isDownloadAllImages = true;
            this.detailOrder = true;
            this.orderInfo = { ...row, orderId: row.id };
        },
        //获取承运商列表
        async getList() {
            this.loading = true;
            this.alList = [];
            let params = { ...this.queryParams };
            params.queryType = '1'; // 0-货主 1-承运商
            params.tempId = params.temperatureTypeTemp.toString();
            delete params.queryTime;
            delete params.sendAddress;
            delete params.receiverAddress;
            delete params.temperatureTypeTemp;
            
            try {
                // 并行发起两个请求
                const [listRes, statsRes] = await Promise.all([
                    orderManagement.listOrderDrug(params),
                    this.getStatistics(true) // 传递 true 表示在内部不处理 loading 状态
                ]);
                
                if (listRes.code === 200) {
                    this.alList = listRes.data.records || [];
                    this.total = listRes.data.total || 0;
                }
            } catch (error) {
                console.error('Error fetching data:', error);
            } finally {
                this.loading = false;
            }
        },
        /**
         * 获取统计数据
         * @param {boolean} isParallel - 是否并行调用，用于控制 loading 状态
         */
        getStatistics(isParallel = false) {
            const { queryTime, sendAddress, receiverAddress, temperatureTypeTemp, ...params } = this.queryParams;
            params.queryType = '1'; // 0-货主 1-承运商
            params.tempId = temperatureTypeTemp.toString();
            
            // 返回 Promise 以便可以并行调用
            return orderManagement.statisticsOfItems(params).then((res) => {
                if (res.code === 200 && res.data) {
                    this.statisticalData = res.data;
                } else {
                    this.statisticalData = {};
                }
                return res;
            });
        },
        getOrderInfo(id) {
            orderManagement.queryOrderDrugById({ id }).then((res) => {
                if (res.code === 200) {
                    // 数据回显
                    Object.assign(this.orderInfo, res.data);
                    this.title = '订单详情';
                    this.infoOpen = true;
                }
            });
        },

        // 获取温层类型
        getTemperatureType() {
            otherConfiguration.getTemperatureTypeList({ status: '0' }).then((res) => {
                if (res.code === 200 && res?.data?.records.length > 0) {
                    this.temperatureTypeDicts = res.data.records;
                }
            });
        },
        // 获取时间段
        getTimePeriod(data) {
            this.form.appointmentTime = data;
        },
        /** 导出按钮操作 */
        handleExport() {
            this.fullLoading = true;
            let params = { ...this.queryParams };
            params.queryType = '1'; // 0-货主 1-承天商
            params.tempId = params.temperatureTypeTemp.toString();
            delete params.queryTime;
            delete params.sendAddress;
            delete params.receiverAddress;
            delete params.temperatureTypeTemp;
            orderManagement
                .newExportOrderData({ filename: '订单列表.xls', ...params }, '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '订单列表.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                        this.msgSuccess('订单导出任务已生成！');
                    } else {
                        this.msgError('导出异常请联系管理员');
                    }
                    this.fullLoading = false;
                })
                .catch((e) => {
                    this.fullLoading = false;
                });
        },
        handleQuery(e) {
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = null;
                this.queryParams.endCreateDate = null;
            }

            if (this.queryParams.sendAddress) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = this.queryParams.sendAddress;
                this.queryParams.sendProvinceId = sendProvinceId || null;
                this.queryParams.sendCityId = sendCityId || null;
                this.queryParams.sendCountyId = sendCountyId || null;
                this.queryParams['sendTown.id'] = sendTownId || null;
            } else {
                this.queryParams.sendProvinceId = null;
                this.queryParams.sendCityId = null;
                this.queryParams.sendCountyId = null;
                this.queryParams['sendTown.id'] = null;
            }
            if (this.queryParams.receiverAddress) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = this.queryParams.receiverAddress;

                this.queryParams.receiverProvinceId = receiverProvinceId || null;
                this.queryParams.receiverCityId = receiverCityId || null;
                this.queryParams.receiverCountyId = receiverCountyId || null;
                this.queryParams['receiverTown.id'] = receiverTownId || null;
            } else {
                this.queryParams.receiverProvinceId = null;
                this.queryParams.receiverCityId = null;
                this.queryParams.receiverCountyId = null;
                this.queryParams['receiverTown.id'] = null;
            }

            this.queryParams.current = 1;
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection, row) {
            this.selectRows = [];
            this.selectRows = selection;
            this.multiple = !this.selectRows.length;
        },
        // tab切换
        handleTabsClick(tab, event) {
            this.queryParams.tabtype = tab.props.name;
            let status = null;
            switch (this.queryParams.tabtype) {
                case '0':
                    status = null;
                    break;
                case '1':
                    status = 3;
                    break;
                case '2':
                    status = 4;
                    break;
            }
            this.queryParams.status = status;
            this.queryParams.current = 1;
            this.getList();
        },
        // 关闭滑块
        onClose() {
            this.timePeriodSelectorSwitch = false;
            this.addOrderSwitch = false;
            this.$emit('closeSlider');
            this.getList();
        },
        // 新建订单 滑块
        openTheNewOrderSlider() {
            // this.addOrderShow = true;
            this.$router.push({
                name: 'PlaceAnOrder',
                query: { orderType: '1' }
            });
        },

        // 重置表单
        resetForm(formName) {
            this.coldChainProductData = [];
            this.addServiceList = [];
            this.$refs[formName].resetFields();
        },
        resetQuery() {
            this.resetForm('queryForm');
            let now = moment(new Date()).format('YYYY-MM-DD');
            this.queryParams.queryTime = [now, now];
            this.queryParams.prePackageOption = null;
            this.queryParams.preAmountOption = null;
            this.handleQuery();
        },
        // 全选
        selectAll(selection) {
            this.selectRows = selection;
            this.multiple = !this.selectRows.length;
        },
        // 展开或者合上
        showAllClick() {
            this.isShow = !this.isShow;
        },

        // 显示改派
        showChangeDriver(row) {
            this.orderInfo = row;
            this.resetForm('formChangeDriver');
            var params = { taskCode: this.orderInfo.taskCode, driverCode: this.orderInfo.driverCode };
            this.formChangeDriver = row;
            getDriver(params).then((response) => {
                this.driverList = response.data;
                this.changeDriverOpen = true;
            });
        },
        statusFormater(row) {
            return this.selectDictLabel(this.statusDicts, row.status);
        },
        // 确认批量操作
        submitBatchConfirmationOrder() {
            this.loading = true;
            orderManagement
                .batchSubmitOrderDrug({ orderList: this.selectData })
                .then((response) => {
                    if (response.code == 200) {
                        this.msgSuccess('批量确认订单成功');
                        this.getList();
                        this.selectData = [];
                        this.multiple = true;
                        this.batchConfirmationOrderShow = false;
                    }
                    this.loading = false;
                })
                .catch((e) => {
                    this.loading = false;
                });
        },

        // 查看轨迹
        trajectoryViewClick(row) {
            if (!row.transOrderNo) {
                this.msgError('此订单没有运输轨迹');
                return false;
            }
            orderManagement.getTransRecord({ transOrderNo: row.transOrderNo }).then((response) => {
                if (response.code == 200 && response.data.records) {
                    this.activities = response.data.records;
                    this.reviewProgressSwitch = true;
                }
            });
        },
        // 客户签收
        transferCustomer(row) {
            this.signatureList = [row];
            this.signatureTitle = '客户签收';
            this.signatureShow = true;
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
::v-deep {
    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .el-radio:last-child {
        margin-right: 30px;
    }

    .el-radio {
        margin-bottom: 10px;

        .el-radio__label {
            .el-input__wrapper {
                background: none;
                box-shadow: none;
            }
        }
    }
    .el-statistic {
        display: flex;
        flex-wrap: wrap;
        align-items: baseline;
    }
}
</style>
