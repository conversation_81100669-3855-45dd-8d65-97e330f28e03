/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-06-15 11:24:34
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-10 17:41:19
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\warehouseNumberManagement\warehouseNumberManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-11 10:08:03
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-19 15:14:10
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\manufacturerManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
  getList: function (inputForm) {
    return http.get('/erp/housenum/erpWarehouseNumberSet/listJsr',inputForm)
  },
  getUser: function (inputForm) {
    return http.get('/sys/user/listByCurrentOrg',{...inputForm,size:1000,current:1})
  },
  save: function (data) {
    return http.post('/erp/housenum/erpWarehouseNumberSet/save',data)
  },
  saveWarehouseNumber: function (data) {
    return http.post('/erp/housenum/erpWarehouseNumberHandledBySet/save',data)
  },
  exportFile: function (params) {
    return http.get('/erp/housenum/erpWarehouseNumberHandledBySet/export',params,{responseType: 'blob'},true)
  },
  getWarehouseNumberId: function (inputForm) {
    return http.get('/erp/housenum/erpWarehouseNumberHandledBySet/queryByWarehouseNumberId',inputForm)
  },
  delete: function (ids) {
    return http.delete('/erp/housenum/erpWarehouseNumberSet/delete',ids)
  },
}
