/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-06 15:21:34
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-05-06 15:24:42
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\utils\dictLabel.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export function selectDictLabel(datas, value) {
	var actions = [];
	Object.keys(datas).some((key) => {
		if (datas[key].value == ('' + value)) {
			actions.push(datas[key].name);
			return true;
		}
	})
	return actions.join('');
}
// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
	var actions = [];
	if(value?.length > 0){
		var currentSeparator = undefined === separator ? "," : separator;
		var temp = value.split(currentSeparator);
		Object.keys(value.split(currentSeparator)).some((val) => {
			Object.keys(datas).some((key) => {
				if (datas[key].value == ('' + temp[val])) {
					actions.push(datas[key].name + currentSeparator);
				}
			})
		})
	}
	return actions.join('').substring(0, actions.join('').length - 1);
}
