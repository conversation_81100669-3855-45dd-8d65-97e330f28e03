<template>
    <div v-loading="loading" class="app-container customer-auto-height-container" element-loading-text="加载中...">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form">
                <el-form-item label="订单号" prop="orderNo" style="width: 220px">
                    <el-input v-model="queryForm.orderNo" clearable placeholder="请输入订单号" @clear="handleChangesTheOrderType" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单号" prop="transOrderNo" style="width: 220px">
                    <el-input v-model="queryForm.transOrderNo" clearable placeholder="请输入运单号" @clear="handleChangesTheOrderType" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item label="费用状态" prop="status" style="width: 230px">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择订单状态" @change="handleChangesTheOrderType">
                        <el-option v-for="item in orderFeeStatusList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item class="self-label-color" label="订单创建时间" prop="queryTime" style="width: 345px">
                    <el-date-picker v-model="queryForm.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleChangesTheOrderType"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单类型" prop="type">
                    <el-select v-model="queryForm.type" clearable disabled placeholder="请选择订单类型" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in settlementManagementOrderTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="queryForm.type === '2'" v-show="isShowAll" class="self-label-color" label="货主公司" prop="companyId">
                    <el-select v-model="queryForm.companyId" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="结算公司" prop="settlementCompanyId">
                    <el-select v-model="queryForm.settlementCompanyId" clearable filterable placeholder="请选择结算公司" style="width: 100%" @change="handleChangesTheOrderType('queryForm')">
                        <el-option v-for="item in settlementCompanyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运输类型" prop="transType">
                    <el-select v-model="queryForm.transType" clearable placeholder="请选择运输类型" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in productTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="产品分类" prop="productType">
                    <el-select v-model="queryForm.productType" clearable placeholder="请选择产品分类" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="温区类型" prop="temperatureType">
                    <el-select v-model="queryForm.temperatureType" clearable placeholder="请选择温区类型" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in temperatureTypeDicts" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" class="self-label-color" label="付款方式" prop="settlementMethod">
                    <el-select v-model="queryForm.settlementMethod" clearable placeholder="请选择付款方式" @change="handleChangesTheOrderType">
                        <el-option v-for="item in settlementMethodList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件公司" prop="sendCompany" style="width: 250px">
                    <el-input v-model="queryForm.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件地址" prop="sendAddress">
                    <el-cascader v-model="queryForm.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable placeholder="请选择发件地址" @change="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件公司" prop="receiverCompany" style="width: 250px">
                    <el-input v-model="queryForm.receiverCompany" :disabled="query && query.hasOwnProperty('receiverCompany') && query.receiverCompany != ''" clearable placeholder="请输入收件公司" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件地址" prop="receiverAddress">
                    <el-cascader v-model="queryForm.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable placeholder="请选择收件地址" @change="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="操作员" prop="adjustName" style="width: 250px">
                    <el-input v-model="queryForm.adjustName" clearable placeholder="请输入操作员" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="容积" prop="productVolume">
                    <div style="width: 194px">
                        <el-select v-model="queryForm.preVolumeOption" clearable style="width: 45%">
                            <el-option v-for="(item, index) in preVolumeOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
                        </el-select>
                        <el-input v-model="queryForm.productVolume" class="productVolume" clearable placeholder="请输入容积" style="width: 55%" @keyup.enter="handleChangesTheOrderType" />
                    </div>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleChangesTheOrderType" @resetQuery="resetQuery('queryForm')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <div class="flex justify-around">
                <el-statistic :precision="2" :value="overview.totalEstimateCost" :value-style="{ color: '#5670FE' }" group-separator="," title="预估费用合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalAdjustCost" :value-style="{ color: '#F4AC00' }" group-separator="," title="调整金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalContractPrice" :value-style="{ color: '#1ACD7E' }" group-separator="," title="合同价合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalDiscountCost" :value-style="{ color: '#FF6B6B' }" group-separator="," title="折扣金额合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalReceivableCost" :value-style="{ color: '#8A2BE2' }" group-separator="," title="应收费用合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalCollectCost" :value-style="{ color: '#FF69B4' }" group-separator="," title="揽收费用合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.totalAddedServiceCost" :value-style="{ color: '#00BFFF' }" group-separator=",">
                    <template #title>
                        <div style="display: inline-flex; align-items: center">
                            增值服务总费用
                            <el-tooltip content="此费用包含在应收费用合计中" effect="dark" placement="top">
                                <el-icon :size="12" style="margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </el-tooltip>
                        </div>
                    </template>
                </el-statistic>
                <el-statistic :precision="2" :value="overview.totalPremiumCost" :value-style="{ color: '#FF8C00' }" group-separator=",">
                    <template #title>
                        <div style="display: inline-flex; align-items: center">
                            保费
                            <el-tooltip content="此费用包含在应收费用合计中" effect="dark" placement="top">
                                <el-icon :size="12" style="margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </el-tooltip>
                        </div>
                    </template>
                </el-statistic>
                <el-statistic :precision="2" :value="overview.totalVerifiedCost" :value-style="{ color: '#32CD32' }" group-separator=",">
                    <template #title>
                        <div style="display: inline-flex; align-items: center">
                            已核对金额
                            <el-tooltip content="包含费用状态：已核对、待付款、支付待确认、结算完成。" effect="dark" placement="top">
                                <el-icon :size="12" style="margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </el-tooltip>
                        </div>
                    </template>
                </el-statistic>
                <el-statistic :precision="2" :value="overview.totalUnverifiedCost" :value-style="{ color: '#DC143C' }" group-separator=",">
                    <template #title>
                        <div style="display: inline-flex; align-items: center">
                            未核对金额
                            <el-tooltip content="包含费用状态：待生成、待核对。" effect="dark" placement="top">
                                <el-icon :size="12" style="margin-left: 4px">
                                    <Warning />
                                </el-icon>
                            </el-tooltip>
                        </div>
                    </template>
                </el-statistic>
            </div>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button v-if="Object.values(query).length == 0" :disabled="multiple" type="primary" @click="setExpenseAdjustment">费用调整</el-button>
                <el-button v-if="Object.values(query).length == 0" :disabled="multiple" type="primary" @click="createStatement">生成对账单</el-button>
                <el-button v-if="Object.values(query).length == 0" :disabled="!dataList || dataList.length === 0" icon="el-icon-download" type="warning" @click="handleExportAll">导出全部</el-button>
                <el-button type="primary" @click="getExportRecordsList">导出记录</el-button>
                <el-tooltip content="请选择下单时间、货主公司、付款单类型再执行该操作" placement="top">
                    <el-button v-if="Object.values(query).length == 0" :disabled="!dataList || dataList.length === 0" type="primary" @click="createStatementAll">全部生成对账单</el-button>
                </el-tooltip>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="customerOrderExpenseManagement" @queryTable="getList" />
            </div>
            <column-table ref="customerOrderExpenseManagement" :columns="columns" :data="dataList" :show-summary="true" :showCheckBox="true" class="customer-auto-height-table" max-height="null" @selection-change="handleSelectionChange">
                <template #adjustSerialNumber="{ row }">
                    <span>{{ row.adjustSerialNumber || '-' }}</span>
                </template>
                <template #temperatureType="{ row }">
                    <span>{{ formatDictionaryData('temperatureTypeDicts', row.temperatureType) }}</span>
                </template>
                <template #settlementMethod="{ row }">
                    <span>{{ formatDictionaryData('settlementMethodList', row.settlementMethod) }}</span>
                </template>
                <template #type="{ row }">
                    <span>{{ formatDictionaryData('settlementManagementOrderTypeList', row.type) }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                </template>
                <template #transType="{ row }">
                    <span>{{ formatDictionaryData('productTypeList', row.transType) }}</span>
                </template>
                <template #orderStatus="{ row }">
                    <div v-html="formatOrderStatus(row.orderStatus)"></div>
                </template>
                <template #status="{ row }">
                    <div v-html="formatStatus(row.status)"></div>
                </template>
                <template #opt="{ row }">
                    <!--                    <el-button v-if="Object.values(query).length == 0" icon="el-icon-edit" link plain size="small" type="warning" @click="openOrderExpenseChange(row)">费用异动 </el-button>-->
                    <el-button icon="el-icon-info-filled" link plain size="small" type="primary" @click="handleClickFeeDetails(row)">费用详情</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>

        <!-- /订单费用异动 弹窗  -->
        <el-drawer v-model="orderExpenseChangeVisible" size="500px" title="订单费用异动" @close="hideOrderExpenseChange">
            <div v-loading="loadingOrderExpenseChange" :element-loading-text="lodingTextOrderExpenseChange" class="p16" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="orderExpenseChange" :model="orderExpenseChange" :rules="rulesOrderExpenseChange" label-width="auto">
                        <el-form-item label="货主名称" prop="companyName">
                            <el-input v-model="orderExpenseChange.companyName" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="订单号" prop="orderNo">
                            <el-input v-model="orderExpenseChange.orderNo" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="发件地址" prop="sendAddress">
                            <el-input v-model="orderExpenseChange.sendAddress" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="收件地址" prop="receiverAddress">
                            <el-input v-model="orderExpenseChange.receiverAddress" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="异动类型" prop="changeType">
                            <el-radio-group v-model="orderExpenseChange.changeType">
                                <el-radio-button label="1">增加费用</el-radio-button>
                                <el-radio-button label="2">减少费用</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="异动金额" prop="changeAmount">
                            <!-- el-input 只能输入小数且保留小数点后四位 -->
                            <el-input-number v-model="orderExpenseChange.changeAmount" :min="0" :precision="2" style="width: 100%"></el-input-number>
                        </el-form-item>
                        <el-form-item label="异动原因" prop="reason">
                            <el-input v-model="orderExpenseChange.reason" maxlength="60" placeholder="请输入异动原因" show-word-limit type="textarea"></el-input>
                        </el-form-item>
                        <div class="box__PromptText">
                            <el-icon><el-icon-warning /></el-icon>
                            <span>费用增加用正数，费用扣减用负数</span>
                        </div>
                    </el-form>
                </el-card>
                <div style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="hideOrderExpenseChange">取消</el-button>
                    <el-button type="primary" @click="saveOrderCostChange">确定</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  /导出记录 抽屉 -->
        <el-drawer v-model="exportRecordsVisible" size="700px" title="导出记录" @close="hideExportRecords">
            <div v-loading="exportRecordsLoading" :element-loading-text="exportRecordsLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <column-table ref="exportRecords" :columns="exportRecordsColumns" :data="exportRecordsData">
                        <template #status="{ row }">
                            <span>{{ formatDictionaryData('exportRecordsStatusList', row.status) }}</span>
                        </template>
                        <template #opt="{ row }">
                            <el-button v-if="row.status === '3'" icon="el-icon-download" link size="small" type="primary" @click="downloadExportRecords(row)">下载</el-button>
                        </template>
                    </column-table>
                    <pagination v-show="exportRecordsTotal > 0" v-model:limit="exportRecords.size" v-model:page="exportRecords.current" :pageSizes="[10, 20, 30, 50, 100]" :total="exportRecordsTotal" @pagination="getExportRecordsList" />
                </el-card>
            </div>
        </el-drawer>

        <!--  /费用调整 弹窗 -->
        <el-drawer v-if="expenseAdjustmentVisible" v-model="expenseAdjustmentVisible" size="90vw" title="费用调整" @close="hideExpenseAdjustment">
            <div v-loading="loadingExpenseAdjustment" :element-loading-text="lodingTextExpenseAdjustment" style="background-color: #f2f2f2; padding: 10px">
                <el-card v-if="errorList && errorList.length > 0" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <div class="box__error_prompt">
                        <el-result :title="errorMsg" icon="error"></el-result>
                        <div class="box-period">
                            <el-scrollbar>
                                <el-alert v-for="(item, index) in errorList" :key="index" :class="index === 0 ? '' : 'mt10'" :closable="false" :title="item" show-icon type="error" />
                            </el-scrollbar>
                        </div>
                    </div>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" class="affix-container" shadow="never">
                    <el-affix :offset="80">
                        <div style="margin-bottom: 10px">
                            <el-button type="primary" @click="submitExpenseAdjustment">提交</el-button>
                        </div>
                        <div class="mb10">
                            <el-button v-if="isExpandStatus" link type="primary" @click.prevent="setExpandStatus(1)"
                                >展开全部 <el-icon><el-icon-arrow-down /></el-icon
                            ></el-button>
                            <el-button v-else link type="primary" @click.prevent="setExpandStatus(0)"
                                >收起全部<el-icon><el-icon-arrow-up /></el-icon
                            ></el-button>
                        </div>
                    </el-affix>
                    <el-table v-loading="feeAdjustmentsDataLoading" :data="feeAdjustmentsData" :element-loading-text="feeAdjustmentsDataLodingText" :expand-row-keys="expends" :row-key="getRowKeys" border row-key="id" style="width: 100%">
                        <el-table-column fixed type="expand">
                            <template #default="{ row, $index }">
                                <el-form class="feeAdjustmentsData__form" inline label-position="left">
                                    <el-form-item v-for="item in row.costList" :key="item.id" class="form__item">
                                        <div v-if="item.isContractPrice === '0'" class="form__item__grid">
                                            <div class="form__item__title">
                                                <span class="form__item__title__name">费用科目</span>
                                                <span>{{ formatDictionaryData('expenseAccountList', item.costType) }}</span>
                                            </div>
                                            <div class="form__item__title">
                                                <span class="form__item__title__name">原价</span>
                                                <span>{{ item.costData }}</span>
                                            </div>
                                            <div class="form__item__title">
                                                <span class="form__item__title__name">合同价</span>
                                                <el-input-number v-model.lazy="item.costContractPrice" :min="0" :precision="2" :step="0.01" style="width: 100%" @change="() => calculateTheContractPrice(row, $index)"></el-input-number>
                                            </div>
                                        </div>
                                    </el-form-item>
                                    <el-form-item v-for="item in row.addedServiceList" :key="item.id" class="mb0 form__item">
                                        <div class="form__item__title">
                                            <span class="form__item__title__name">费用科目</span>
                                            <span>{{ item.addedServicesName }}</span>
                                        </div>
                                        <div class="form__item__title">
                                            <span class="form__item__title__name">原价</span>
                                            <span>{{ item.addedServicesCost }}</span>
                                        </div>
                                        <div class="form__item__title">
                                            <span class="form__item__title__name">合同价</span>
                                            <el-input-number v-model.lazy="item.addedServicesContractPrice" :min="0" :precision="2" :step="0.01" style="width: 100%" @change="() => calculateTheContractPrice(row, $index)"></el-input-number>
                                        </div>
                                    </el-form-item>
                                </el-form>
                                <div class="feeBreakdown__total">
                                    <div v-if="row.estimateCost" class="total__item">
                                        <span>预估费用</span>
                                        <span class="total__num">{{ row.estimateCost }}</span>
                                    </div>
                                    <div class="total__item">
                                        <span>合同价合计</span>
                                        <span class="total__num">{{ totalContractPrice[$index] || '-' }}</span>
                                    </div>
                                    <div class="total__item">
                                        <span>应收费用合计</span>
                                        <span class="total__num">{{ totalReceivedCost[$index] || '-' }}</span>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" fixed label="订单号" prop="orderNo" width="140"></el-table-column>
                        <el-table-column align="center" label="温区类型" prop="temperatureType" width="120">
                            <template #default="{ row }">
                                <span>{{ formatDictionaryData('temperatureTypeDicts', row.temperatureType) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="件数" prop="goodsPackages" width="120"></el-table-column>
                        <el-table-column align="center" label="收件公司" prop="receiverCompany" show-overflow-tooltip width="200"></el-table-column>
                        <el-table-column align="center" label="发件地址" minWidth="200" prop="sendAddress" show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" label="收件地址" minWidth="200" prop="receiverAddress" show-overflow-tooltip></el-table-column>
                        <el-table-column align="center" label="揽收费用" prop="collectCost" width="120"></el-table-column>
                        <el-table-column align="center" label="干线费用" prop="transportCost" width="120"></el-table-column>
                        <el-table-column align="center" label="配送费用" prop="deliveryCost" width="120"></el-table-column>
                        <el-table-column align="center" label="超区费用" prop="exceedCountyCost" width="120"></el-table-column>
                        <el-table-column align="center" label="增值服务总费用" prop="addedServicesCost" width="120"></el-table-column>
                        <el-table-column align="center" label="折扣金额" prop="discountCost" width="120"></el-table-column>
                        <el-table-column align="center" label="异动费用" prop="abnormalCost" width="120"></el-table-column>
                        <el-table-column align="center" label="调整金额" prop="adjustCost" width="120"></el-table-column>
                        <el-table-column align="center" fixed="right" label="预估费用合计" prop="estimateCost" width="120"></el-table-column>
                    </el-table>
                </el-card>
            </div>
        </el-drawer>

        <!--  /订单费用详情  -->
        <order-fee-details-with-details v-if="orderCostVisible" v-model="orderCostVisible" :detail-data="detailData" :fee-breakdown-data="feeBreakdownData" :order-cost-loading="orderCostLoading" :order-cost-loading-text="orderCostLoadingText" />
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import moment from 'moment';
import { selectDictLabel } from '@/utils/dictLabel';
import OrderFeeDetailsWithDetails from '@/views/carrierFunction/OrderFeeDetailsWithDetails';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import customerOrderExpenseManagement from '@/api/carrierEnd/customerOrderExpenseManagement';
import { downloadNoData } from '@/utils';
import orderCostQueryStatistics from '@/api/carrierEnd/orderCostQueryStatistics';
import settlementCompany from '@/api/carrierEnd/settlementCompany';
import { Warning } from '@element-plus/icons-vue';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'CustomerOrderExpenseManagement',
    components: {
        SearchButton,
        RightToolbar,
        ColumnTable,
        OrderFeeDetailsWithDetails,
        Warning
    },
    data() {
        return {
            query: {},
            queryForm: {
                current: 1,
                size: 10,
                orderNo: undefined,
                transOrderNo: undefined,
                type: '2',
                companyId: null,
                transType: null,
                productType: null,
                temperatureType: null,
                status: null,
                settlementMethod: null,
                sendCompany: null,
                sendAddress: [],
                sendProvinceId: undefined,
                sendCityId: undefined,
                sendCountyId: undefined,
                sendTownId: undefined,
                receiverCompany: null,
                receiverAddress: [],
                receiverProvinceId: undefined,
                receiverCityId: undefined,
                receiverCountyId: undefined,
                adjustName: null,
                preVolumeOption: undefined,
                productVolume: undefined,
                queryTime: []
            },
            settlementMethodList: [],
            orderStatusList: [],
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', width: '120px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '150px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '温区类型', key: 'temperatureType', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '件数', key: 'goodsPackages', labelClassName: 'isShowSummary', align: 'center', width: '70px', columnShow: true, showOverflowTooltip: true },
                { title: '合同价合计', labelClassName: 'isShowSummary', key: 'totalContractPrice', align: 'center', width: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '发件县区', key: 'sendCounty', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '收件县区', key: 'receiverCounty', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '付款方式', key: 'settlementMethod', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '调整流水号', key: 'adjustSerialNumber', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '订单类型', key: 'type', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '订单创建时间', key: 'orderDate', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '运输类型', key: 'transType', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '产品分类', key: 'productType', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '容积', key: 'productVolume', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '车型', key: 'carTypeDesc', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '公里数', key: 'kilometre', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '发件省', key: 'sendProvince', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '发件地市', key: 'sendCity', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '发件乡镇/街道', key: 'sendTown', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '发件详细地址', key: 'sendAddress', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件省', key: 'receiverProvince', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地市', key: 'receiverCity', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '收件乡镇/街道', key: 'receiverTown', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '收件详细地址', key: 'receiverAddress', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收费用', labelClassName: 'isShowSummary', key: 'collectCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '干线费用', labelClassName: 'isShowSummary', key: 'transportCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '配送费用', labelClassName: 'isShowSummary', key: 'deliveryCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '超区费用', labelClassName: 'isShowSummary', key: 'exceedCountyCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '增值服务总费用', labelClassName: 'isShowSummary', key: 'addedServicesCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '保价金额', key: 'insuranceAmount', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '保费', key: 'premium', align: 'center', labelClassName: 'isShowSummary', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '预估费用合计', labelClassName: 'isShowSummary', key: 'estimateCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '调整金额', labelClassName: 'isShowSummary', key: 'adjustCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '折扣金额', labelClassName: 'isShowSummary', key: 'discountCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '应收费用合计', labelClassName: 'isShowSummary', key: 'totalReceivableCost', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '操作员', key: 'adjustName', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '调整日期', key: 'adjustTime', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '对账人', key: 'checkedName', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '对账时间', key: 'checkedTime', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '费用单创建时间', key: 'createDate', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '订单状态', key: 'orderStatus', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '费用状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            showSearch: true,
            loading: false,
            dataList: [],
            total: 0,
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 订单费用异动 弹窗状态
            orderExpenseChangeVisible: false,
            orderExpenseChange: {
                orderUserName: '',
                orderNo: '',
                sendAddress: '',
                receiverAddress: '',
                changeType: '1',
                changeAmount: 0,
                reason: ''
            },
            rulesOrderExpenseChange: {},
            expenseAdjustmentVisible: false,
            feeAdjustmentsData: [],
            rulesExpenseAdjustment: {},
            loadingOrderExpenseChange: false,
            orderExpenseChangeRow: {},
            lodingTextOrderExpenseChange: '加载中',
            loadingExpenseAdjustment: false,
            lodingTextExpenseAdjustment: '加载中',
            temperatureTypeDicts: [],
            customerList: [],
            productTypeList: [],
            goodsTypeList: [],
            orderCostVisible: false,
            orderCostLoading: false,
            orderCostLoadingText: '加载中...',
            detailData: {},
            feeBreakdownColumns: [
                { title: '费用科目', key: 'costType', align: 'center', width: '180px', columnShow: true },
                { title: '预估金额', key: 'costData', align: 'center', width: '120px', columnShow: true },
                { title: '合同价格', key: 'costContractPrice', align: 'center', width: '120px', columnShow: true },
                { title: '费用计算明细', key: 'textFormula', align: 'center', minWidth: '120px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            expenseAccountList: [],
            feeBreakdownData: [],
            settlementManagementOrderTypeList: [],
            feeAdjustmentsDataLoading: false,
            feeAdjustmentsDataLodingText: '加载中...',
            totalContractPrice: [],
            totalReceivedCost: [],
            errorList: [],
            errorMsg: null,
            sysAreas: [],
            vehicleTypeList: [],
            orderFeeStatusList: [],
            isShowAll: false,
            shortcuts: setDatePickerShortcuts(),
            expends: [],
            isExpandStatus: true,
            // 导出记录
            exportRecordsVisible: false,
            exportRecordsLoading: false,
            exportRecordsLoadingText: '加载中...',
            exportRecordsColumns: [
                { title: '操作人', key: 'operateName', align: 'center', minWidth: '180px', columnShow: true },
                { title: '操作时间', key: 'operateTime', align: 'center', width: '180px', columnShow: true },
                { title: '状态', key: 'status', align: 'center', width: '80px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            exportRecordsData: [],
            exportRecordsTotal: 0,
            exportRecords: {
                current: 1,
                size: 10
            },
            exportRecordsStatusList: [],
            settlementCompanyList: [],
            overview: {
                totalEstimateCost: 0,
                totalContractPrice: 0,
                totalReceivableCost: 0,
                totalCollectCost: 0,
                totalAddedServiceCost: 0,
                totalPremiumCost: 0,
                totalAdjustCost: 0,
                totalDiscountCost: 0,
                totalVerifiedCost: 0,
                totalUnverifiedCost: 0
            },
            preVolumeOptions: [
                {
                    label: '等于',
                    value: '= '
                },
                {
                    label: '大于',
                    value: '> '
                },
                {
                    label: '大于等于',
                    value: '>= '
                },
                {
                    label: '小于',
                    value: '< '
                },
                {
                    label: '小于等于',
                    value: '<= '
                }
            ]
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 格式化订单状态
         * @returns {(function(*): (string|string|string))|*}
         */
        formatOrderStatus() {
            return (value) => {
                if (this.orderStatusList && value) {
                    const statusText = selectDictLabel(this.orderStatusList, value) || '-';
                    if (value === '0') {
                        return `<span style="color: #B1B1B1">${statusText}</span>`;
                    } else if (value === '1') {
                        return `<span style="color: #F4AC00">${statusText}</span>`;
                    } else if (value === '2') {
                        return `<span style="color: #5670FE">${statusText}</span>`;
                    } else if (value === '3') {
                        return `<span style="color: #1ACD7E">${statusText}</span>`;
                    } else {
                        return statusText;
                    }
                }
            };
        },
        /**
         * 格式化费用单状态
         * @returns {(function(*): (string|string|string))|*}
         */
        formatStatus() {
            return (value) => {
                if (this.orderFeeStatusList?.length && value) {
                    const statusText = selectDictLabel(this.orderFeeStatusList, value) || '-';
                    if (value === '0') {
                        return `<span style="color: #B1B1B1">${statusText}</span>`;
                    } else if (value === '1') {
                        return `<span style="color: #F4AC00">${statusText}</span>`;
                    } else if (value === '2') {
                        return `<span style="color: #5670FE">${statusText}</span>`;
                    } else if (value === '3') {
                        return `<span style="color: #1ACD7E">${statusText}</span>`;
                    } else {
                        return statusText;
                    }
                } else {
                    return '-';
                }
            };
        },
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },
    created() {
        this.getDict();
        this.getSettlementCompanyList();
        // 将默认设置调整为7天
        this.queryForm.startDate = moment().subtract(7, 'days').format('YYYY-MM-DD');
        this.queryForm.endDate = moment().format('YYYY-MM-DD');
        this.queryForm.queryTime = [this.queryForm.startDate, this.queryForm.endDate];

        if (Object.values(this.$route.params).length == 0) {
            this.handleChangesTheOrderType();
        }
        // 获取地址级联数据
        this.sysAreas = this.getSysAreas;
    },
    activated() {
        if (this.$route.params) {
            this.query = this.$route.params;
            if (this.$route.params.type) {
                this.queryForm.companyId = this.$route.params.companyId;
                this.queryForm.type = this.$route.params.type;
                this.queryForm.receiverCompany = this.$route.params.receiverCompany;
                this.getList();
            }
        }
    },
    methods: {
        /**
         * 计算合同价格
         * @param {Object} value - 包含费用信息的行对象
         * @param {Number} index - 行索引
         */
        calculateTheContractPrice(value, index) {
            if (value) {
                const { costList, addedServiceList, formulaId, orderId } = value;
                if (costList && costList.length > 0) {
                    const collectCost = costList.filter((item) => item.costType === '1');
                    const transportCost = costList.filter((item) => item.costType === '2');
                    const deliveryCost = costList.filter((item) => item.costType === '3');
                    const exceedCountyCost = costList.filter((item) => item.costType === '4');
                    const addedServiceCostList = addedServiceList ? addedServiceList.map((item) => item.addedServicesContractPrice) : [];
                    const params = {
                        collectCost: collectCost[0]?.costContractPrice ?? 0,
                        transportCost: transportCost[0]?.costContractPrice ?? 0,
                        deliveryCost: deliveryCost[0]?.costContractPrice ?? 0,
                        exceedCountyCost: exceedCountyCost[0]?.costContractPrice ?? 0,
                        addedServiceCostList,
                        formulaId,
                        orderId
                    };
                    customerOrderExpenseManagement
                        .calculateContractCost(params)
                        .then((res) => {
                            if (res.code === 200) {
                                const { cost, finalCost } = res.data;
                                this.totalContractPrice.splice(index, 1, cost);
                                this.totalReceivedCost.splice(index, 1, finalCost);
                            }
                        })
                        .catch(() => {});
                }
            }
        },
        /**
         * 生成对账单
         * 检查选中的订单，排除已取消的订单，并生成对账单
         */
        createStatement() {
            // 检查是否有已取消的订单
            const hasCancelledOrder = this.selectData.some((item) => item.orderStatus === '5');

            if (hasCancelledOrder) {
                // 取消选中已取消的订单
                this.selectData.forEach((item) => {
                    if (item.orderStatus === '5') {
                        this.$refs.customerOrderExpenseManagement.$refs.ColumnTable.toggleRowSelection(item, false);
                    }
                });

                // 如果取消选中后没有选中的订单，则提示用户
                if (this.selectData.length === 0) {
                    this.$message({
                        type: 'warning',
                        message: '已取消订单不允许生成对账单',
                        offset: 80
                    });
                    return;
                }
            }

            this.$confirm('是否确认生成对账单？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    // 遍历 this.selectData id 赋值为 ids
                    let ids = [];
                    this.selectData.forEach((item) => {
                        ids.push(item.id);
                    });
                    const loading = this.$loading({
                        lock: true,
                        text: '生成中...',
                        spinner: 'el-icon-loading'
                    });
                    customerOrderExpenseManagement
                        .createStatement(ids)
                        .then((res) => {
                            if (res.code === 200) {
                                if (res.data.successNum) {
                                    this.$message({
                                        type: 'success',
                                        message: res.data.successNum
                                    });
                                }
                                if (res.data.errorNum) {
                                    this.$message({
                                        type: 'error',
                                        message: res.data.errorNum,
                                        offset: 80
                                    });
                                }
                                this.handleChangesTheOrderType();
                            }
                        })
                        .finally(() => {
                            loading.close();
                        })
                        .catch(() => {});
                })
                .catch(() => {});
        },
        /**
         * 全部生成对账单
         */
        createStatementAll() {
            // 验证必选筛选条件
            if (!this.queryForm.queryTime || this.queryForm.queryTime.length !== 2) {
                this.$message({
                    type: 'warning',
                    message: '请选择订单创建时间',
                    offset: 80
                });
                return;
            }

            if (!this.queryForm.companyId) {
                this.$message({
                    type: 'warning',
                    message: '请选择货主公司',
                    offset: 80
                });
                return;
            }

            if (!this.queryForm.settlementMethod) {
                this.$message({
                    type: 'warning',
                    message: '请选择付款方式',
                    offset: 80
                });
                return;
            }

            this.$confirm('是否确认全部生成对账单？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const loading = this.$loading({
                    lock: true,
                    text: '生成中...',
                    spinner: 'el-icon-loading'
                });
                // eslint-disable-next-line no-unused-vars
                const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;
                customerOrderExpenseManagement
                    .createStatementAll(params)
                    .then((res) => {
                        if (res.code === 200) {
                            this.$message({ type: 'success', message: '生成对账单成功' });
                        }
                    })
                    .finally(() => {
                        loading.close();
                    });
            });
        },
        /**
         * 下载导出记录
         * @param {Object} row - 导出记录行数据
         */
        downloadExportRecords(row) {
            if ('fileId' in row) {
                customerOrderExpenseManagement.downloadExportRecords({ filename: '客户订单费用管理.xls', recordId: row.fileId }, '', '', 'blob').then((res) => {
                    downloadNoData(res, 'application/vnd.ms-excel', '客户订单费用管理.xlsx');
                });
            }
        },
        /**
         * 获取字典数据
         * 初始化各种下拉选项的字典数据
         */
        async getDict() {
            this.orderStatusList = await this.getDictList('fourpl_order_status');
            this.orderFeeStatusList = await this.getDictList('4pl_order_expense_status');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
            this.temperatureTypeDicts = await this.getDictList('fourpl_temperature_type');
            this.productTypeList = await this.getDictList('fourpl_product_type');
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.expenseAccountList = await this.getDictList('cost_order_type');
            this.settlementManagementOrderTypeList = await this.getDictList('cost_settlement_management_order_type');
            this.exportRecordsStatusList = await this.getDictList('export_record_status');
        },
        /**
         * 获取导出记录列表
         * 查询并显示导出记录
         */
        getExportRecordsList() {
            this.exportRecordsLoading = true;
            this.exportRecordsLoadingText = '加载中...';
            customerOrderExpenseManagement
                .exportRecords({ type: '1', ...this.exportRecords })
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.exportRecordsVisible = true;
                        this.exportRecordsData = res.data.records || [];
                        this.exportRecordsTotal = res.data.total || 0;
                    } else {
                        this.$message({
                            type: 'warning',
                            message: '暂无导出记录'
                        });
                    }
                })
                .finally(() => {
                    this.exportRecordsLoading = false;
                });
        },
        /**
         * 获取订单费用列表和统计数据
         * 同时发起两个请求获取列表数据和统计数据
         */
        getList() {
            this.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;

            // 同时发起两个请求
            const listPromise = customerOrderExpenseManagement.listOrderCost(params);
            const statsPromise = customerOrderExpenseManagement.costStatistics(params);

            // 使用Promise.all同时处理两个请求
            Promise.all([listPromise, statsPromise])
                .then(([listRes, statsRes]) => {
                    // 处理列表数据
                    if (listRes.code === 200 && listRes.data.records) {
                        this.dataList = listRes.data.records || [];
                        this.total = listRes.data.total || 0;
                    } else {
                        this.dataList = [];
                        this.total = 0;
                    }

                    // 处理统计数据
                    if (statsRes.code === 200 && statsRes.data) {
                        const { totalEstimateCost, totalContractPrice, totalReceivableCost, totalCollectCost, totalAddedServiceCost, totalPremiumCost, totalAdjustCost, totalDiscountCost, totalVerifiedCost, totalUnverifiedCost } = statsRes.data;
                        this.overview.totalEstimateCost = totalEstimateCost * 1 || 0.0;
                        this.overview.totalContractPrice = totalContractPrice * 1 || 0.0;
                        this.overview.totalReceivableCost = totalReceivableCost * 1 || 0.0;
                        this.overview.totalCollectCost = totalCollectCost * 1 || 0.0;
                        this.overview.totalAddedServiceCost = totalAddedServiceCost * 1 || 0.0;
                        this.overview.totalPremiumCost = totalPremiumCost * 1 || 0.0;
                        this.overview.totalAdjustCost = totalAdjustCost * 1 || 0.0;
                        this.overview.totalDiscountCost = totalDiscountCost * 1 || 0.0;
                        this.overview.totalVerifiedCost = totalVerifiedCost * 1 || 0.0;
                        this.overview.totalUnverifiedCost = totalUnverifiedCost * 1 || 0.0;
                    } else {
                        this.resetOverview();
                    }
                })
                .catch(() => {
                    this.dataList = [];
                    this.total = 0;
                    this.resetOverview();
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 获取行的唯一键
         * @param {Object} row - 行数据
         * @returns {*} - 行的唯一标识
         */
        getRowKeys(row) {
            return row.id;
        },
        /**
         * 获取结算公司列表
         * 初始化结算公司下拉选项
         */
        async getSettlementCompanyList() {
            const res = await settlementCompany.getSettlementCompanySelectList();
            if (res.code === 200) {
                this.settlementCompanyList = res.data || [];
            } else {
                this.settlementCompanyList = [];
            }
        },
        /**
         * 改变订单类型
         * 根据订单类型获取相应的客户列表
         */
        handleChangesTheOrderType() {
            this.handleQuery();
            // eslint-disable-next-line no-unused-vars
            const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;
            orderCostQueryStatistics
                .customerNameList({ ...params })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.customerList = res.data || [];
                    } else {
                        this.customerList = [];
                        this.queryForm.companyId = null;
                    }
                })
                .catch(() => {
                    this.customerList = [];
                    this.queryForm.companyId = null;
                });
        },
        /**
         * 查看费用详情
         * @param {Object} data - 订单费用行数据
         */
        handleClickFeeDetails(data) {
            this.orderCostVisible = true;
            this.orderCostLoading = true;
            this.orderCostLoadingText = '加载中...';

            const { id } = data;
            customerOrderExpenseManagement
                .getOrderCostDetail({ id })
                .then((res) => {
                    if (res.code === 200) {
                        this.detailData = res.data;
                        const { addedServiceList, costList } = res.data;
                        let addedServicesFilter = [];
                        if (addedServiceList) {
                            // addedServiceList addedServicesContractPrice 变为 costContractPrice, addedServicesCost 变为 costData,addedServicesName 变为 costType 赋值给 addedServiceList
                            // 使用 for of 循环遍历 addedServiceList
                            for (const item of addedServiceList) {
                                item.costContractPrice = item.addedServicesContractPrice;
                                item.costData = item.addedServicesCost;
                                item.costType = item.addedServicesName;
                            }
                            // 去除 addedServiceList 中 costData 和 costContractPrice 都为 '0' 的数据 其中一个为 '0' 的数据不去除
                            // 赋值给 addedServicesFilter
                            addedServicesFilter = addedServiceList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
                        }
                        // 筛选 costList 中 costData 和 costContractPrice 都不等于0的数据
                        const costListFilter = costList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
                        costListFilter.sort((a, b) => {
                            return a.costType - b.costType;
                        });

                        // addedServiceList 为 null 解构赋值给 this.feeBreakdownData []
                        this.feeBreakdownData = [...(addedServicesFilter || []), ...(costListFilter || [])];
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.orderCostLoading = false;
                });
        },
        /**
         * 导出全部数据
         * 确认后导出全部订单费用数据
         */
        handleExportAll() {
            // 提示框 询问是否导出全部
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    // eslint-disable-next-line no-unused-vars
                    const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;
                    customerOrderExpenseManagement
                        .exportOrderCost(params)
                        .then((res) => {
                            if (res.code === 200) {
                                // 提示正在导出
                                this.$message({ type: 'warning', message: '正在导出，请稍后在导出记录中下载...' });
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 处理查询
         * 准备查询参数并获取列表数据
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { queryTime } = this.queryForm;

            // 验证queryTime是否包含两个合法的日期字符串
            const isValidDateRange = queryTime && queryTime.length === 2 && !queryTime.some((date) => date === 'Invalid Date');

            if (isValidDateRange) {
                this.queryForm.startDate = queryTime[0] + ' 00:00:00';
                this.queryForm.endDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startDate = null;
                this.queryForm.endDate = null;
            }

            if (this.queryForm.sendAddress) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = this.queryForm.sendAddress;
                this.queryForm.sendProvinceId = sendProvinceId || undefined;
                this.queryForm.sendCityId = sendCityId || undefined;
                this.queryForm.sendCountyId = sendCountyId || undefined;
                this.queryForm.sendTownId = sendTownId || undefined;
            } else {
                this.queryForm.sendProvinceId = undefined;
                this.queryForm.sendCityId = undefined;
                this.queryForm.sendCountyId = undefined;
                this.queryForm.sendTownId = undefined;
            }

            if (this.queryForm.receiverAddress) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = this.queryForm.receiverAddress;
                this.queryForm.receiverProvinceId = receiverProvinceId || undefined;
                this.queryForm.receiverCityId = receiverCityId || undefined;
                this.queryForm.receiverCountyId = receiverCountyId || undefined;
                this.queryForm.receiverTownId = receiverTownId || undefined;
            } else {
                this.queryForm.receiverProvinceId = undefined;
                this.queryForm.receiverCityId = undefined;
                this.queryForm.receiverCountyId = undefined;
                this.queryForm.receiverTownId = undefined;
            }

            this.getList();
        },
        /**
         * 多选框选中数据处理
         * @param {Array} selection - 选中的行数据数组
         */
        handleSelectionChange(selection) {
            // 遍历 selection中的 id status 赋值给 this.selectData
            this.selectData = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /**
         * 隐藏费用调整弹窗
         * 关闭弹窗并重置相关数据
         */
        hideExpenseAdjustment() {
            this.expenseAdjustmentVisible = false;
            this.totalContractPrice = [];
            this.totalReceivedCost = [];
            this.errorList = [];
            this.errorMsg = null;
            this.isExpandStatus = true;
            this.expends = [];
        },
        /**
         * 隐藏导出记录弹窗
         */
        hideExportRecords() {
            this.exportRecordsVisible = false;
        },
        /**
         * 隐藏订单费用异动弹窗
         * 重置表单并关闭弹窗
         */
        hideOrderExpenseChange() {
            this.$refs.orderExpenseChange.resetFields();
            this.orderExpenseChangeVisible = false;
        },
        /**
         * 打开订单费用异动弹窗
         * @param {Object} row - 订单费用行数据
         */
        openOrderExpenseChange(row) {
            this.orderExpenseChangeVisible = true;
            this.orderExpenseChange = Object.assign(this.orderExpenseChange, row);
            // row存储到 this.orderExpenseChange
            this.orderExpenseChangeRow = row;
        },
        /**
         * 重置统计数据
         * 将所有统计数据重置为0
         */
        resetOverview() {
            this.overview = {
                totalEstimateCost: 0,
                totalContractPrice: 0,
                totalReceivableCost: 0,
                totalCollectCost: 0,
                totalAddedServiceCost: 0,
                totalPremiumCost: 0,
                totalAdjustCost: 0,
                totalDiscountCost: 0,
                totalVerifiedCost: 0,
                totalUnverifiedCost: 0
            };
        },
        /**
         * 重置查询条件
         * @param {String} formName - 表单名称
         */
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.queryForm.sendAddress = [];
            this.queryForm.sendProvinceId = undefined;
            this.queryForm.sendCityId = undefined;
            this.queryForm.sendCountyId = undefined;
            this.queryForm.sendTownId = undefined;
            this.queryForm.receiverAddress = [];
            this.queryForm.receiverProvinceId = undefined;
            this.queryForm.receiverCityId = undefined;
            this.queryForm.receiverCountyId = undefined;
            this.queryForm.receiverTownId = undefined;
            this.handleChangesTheOrderType();
        },
        /**
         * 订单费用异动数据保存
         * 提交费用异动信息
         */
        saveOrderCostChange() {
            this.loadingOrderExpenseChange = true;
            this.lodingTextOrderExpenseChange = '保存中';
            const { orderUserId, transOrderNo, sendCompany, receiverCompany, changeAmount, changeType, reason } = this.orderExpenseChange;
            const params = {
                id: this.orderExpenseChangeRow.id,
                orderNo: this.orderExpenseChangeRow.orderNo,
                orderUserId,
                transOrderNo,
                sendCompany,
                receiverCompany,
                changeAmount: changeAmount.toString(),
                changeType,
                reason
            };
            customerOrderExpenseManagement
                .saveOrderCostChange(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.$message({ type: 'success', message: '费用异动成功' });
                        this.orderExpenseChangeRow = {};
                        this.handleChangesTheOrderType();
                        this.hideOrderExpenseChange();
                    } else {
                        this.$message({ type: 'error', message: '费用异动失败' });
                    }
                })
                .finally(() => {
                    this.loadingOrderExpenseChange = false;
                })
                .catch(() => {});
        },
        /**
         * 设置展开/折叠状态
         * @param {Number} type - 状态值：0表示收起，1表示展开
         */
        setExpandStatus(type) {
            this.isExpandStatus = type === 0; // 当 type 为 0 时，表示收起（即不展开）
            this.expends = type === 0 ? [] : this.feeAdjustmentsData.map((item) => item.id);
        },
        /**
         * 打开费用调整弹窗
         * 筛选可调整的订单并获取费用调整数据
         */
        setExpenseAdjustment() {
            // table置空
            this.feeAdjustmentsData = [];
            // 遍历 this.selectData id 赋值为 idList
            let idList = [];
            this.selectData.forEach((item) => {
                // item status 为 0 和 1 时才能调整 其他情况 不选中 toggleRowSelection 只有第一次提示 之后不提示
                if (item.status !== '0' && item.status !== '1') {
                    this.$refs.customerOrderExpenseManagement.$refs.ColumnTable.toggleRowSelection(item, false);
                } else {
                    idList.push(item.id);
                }
            });
            // idList 为空时 不打开 费用调整 弹窗 并且提示
            if (idList.length === 0) {
                this.$message({
                    message: '只有待生成和待核对的订单才能调整费用！',
                    type: 'warning',
                    offset: 80
                });
                return;
            }
            this.expenseAdjustmentVisible = true;
            this.loadingExpenseAdjustment = true;
            this.lodingTextExpenseAdjustment = '加载中...';
            customerOrderExpenseManagement
                .getOrderCostAdjustment(idList)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        // 将 res.data 数组中 costList 数组的 costContractPrice 字符串转为 数值类型
                        this.feeAdjustmentsData = res.data.map((item) => {
                            return {
                                ...item,
                                costList: item.costList.map((cost) => {
                                    return {
                                        ...cost,
                                        costContractPrice: parseFloat(cost.costContractPrice)
                                    };
                                })
                            };
                        });
                    }
                })
                .finally(() => {
                    this.loadingExpenseAdjustment = false;
                });
        },
        /**
         * 切换显示全部搜索条件
         * 展开或收起高级搜索选项
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 提交费用调整
         * 保存费用调整数据并处理结果
         */
        submitExpenseAdjustment() {
            this.loadingExpenseAdjustment = true;
            this.lodingTextExpenseAdjustment = '提交中...';
            customerOrderExpenseManagement
                .orderExpenseAdjustment({ data: this.feeAdjustmentsData })
                .then((res) => {
                    if (res.code === 200) {
                        const { errorList, errorMsg, successMsg } = res.data;
                        if (errorList.length !== 0) {
                            this.errorList = errorList;
                            this.errorMsg = errorMsg;
                        } else {
                            this.$message.success(successMsg);
                            this.expenseAdjustmentVisible = false;
                            this.handleChangesTheOrderType();
                        }
                    }
                })
                .finally(() => {
                    this.loadingExpenseAdjustment = false;
                });
        }
    }
};
</script>

<style lang="scss" scoped>
:deep {
    thead th {
        border-right: none !important;
    }

    .el-drawer__header {
        margin-bottom: 20px;
    }

    label.el-radio {
        margin-right: 8px;
    }

    .box-footer {
        padding-left: 8px;
        display: flex;
        justify-content: space-between;

        .el-form-item__content {
            margin-left: 0 !important;
        }
    }

    .el-button-group > .el-button:not(:last-child) {
        margin-right: 8px;
    }

    .el-tabs__header.is-top {
        margin-bottom: 0;
    }

    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .card-pb-10 .el-card__body {
        padding-bottom: 10px;
    }

    .el-input.is-disabled .el-input__inner {
        color: #666666;
        background-color: #f5f7fa;
    }

    .el-table__footer-wrapper .cell.isShowSummary {
        font-weight: bold;
        color: #5670fe;
    }
}

.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.box__PromptText {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #999999;

    > span {
        font-size: 14px;
    }
}

.expenseAdjustment__top {
    display: flex;
    align-items: center;
    gap: 30px;

    .top__title {
        color: #666666;
    }
}

.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}

.card__two__column {
    // display: grid;
    // grid-template-columns: 1fr 1fr;
    // gap: 16px;
    .d-box-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        font-size: 14px;

        .d-box-column {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            .d-box-info {
                display: flex;
                justify-content: space-between;

                :nth-child(1) {
                    flex-shrink: 0;
                    flex-grow: 0;
                    text-align: left;
                    margin-right: 10px;
                    color: #999999;
                }

                :nth-child(2) {
                    color: #333333;
                }
            }
        }

        .d-box-column:first-child {
            border-right: 1px solid #e6ebf5;
            padding-right: 10px;
        }

        .d-box-column:last-child {
            padding-left: 10px;
        }
    }
}

.feeBreakdown__total {
    padding: 10px;
    display: flex;
    gap: 15px 30px;
    flex-wrap: wrap;

    .total__item {
        display: flex;
        gap: 10px;
        align-items: baseline;
        font-size: 14px;
    }

    .total__title {
        white-space: nowrap;
    }

    .total__num {
        font-weight: bold;
        font-size: 18px;
        color: #5670fe;
    }
}

.feeAdjustmentsData__form {
    width: 1260px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    padding: 0 10px;
    gap: 10px;

    .form__item {
        margin-right: 0;
        margin-bottom: 0;
        background-color: #f5f7fa;
        padding: 7px 15px;

        :deep {
            .el-form-item__content {
                display: flex;
                gap: 15px;
                justify-content: space-between;

                .form__item__title {
                    display: flex;
                    flex-direction: column;

                    .form__item__title__name {
                        color: #5670fe;
                    }
                }

                :last-child {
                    flex: 1;
                }
            }
        }

        .form__item__grid {
            display: flex;
            gap: 15px;
        }
    }
}

.box-period {
    height: 200px;

    :deep {
        .el-scrollbar {
            height: 100%;
        }

        .el-alert__content {
            display: flex;
            margin-top: 5px;
            margin-bottom: 5px;
            justify-content: space-between;
            align-items: center;
            flex: 1;

            .el-alert__title {
                line-height: inherit;
            }
        }

        .el-alert__icon {
            line-height: inherit;
            font-size: 20px;
            width: auto;
        }

        .el-alert__description {
            font-size: 14px;
            margin: 0;

            button {
                padding: 0;
                color: #ff2a2a;
            }
        }

        .el-scrollbar__wrap {
            //overflow: scroll;
            width: 102%;
        }
    }
}

:deep(.self-label-color) {
    .el-form-item__label {
        color: #ff8c00;
    }
}
.box__error_prompt {
    display: grid;
    grid-template-columns: 0.5fr 1fr;
}
</style>
