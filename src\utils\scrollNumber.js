import {
  defineComponent,
  reactive,
  computed,
  watch,
  onMounted,
  unref
} from "vue";
import { isNumber } from "@pureadmin/utils";

export default defineComponent({
  name: "ReNormalCountTo",
  props:{
    startVal: { type: Number, default: 0 },
    endVal:{ type: Number, default: 0 }, 
    duration:{ type: Number, default: 3000 }, 
    autoplay: { type: Boolean, default: true },
    decimals: { type: Number , default: 0 },
    color: { type: String , default: '#409EFF' },
    fontSize: { type: String , default: "2em" },
    decimal: { type: String , default: "." },
    separator: { type: String , default: "," },
    prefix: { type: String , default: "" },
    suffix: { type: String , default: "" },
    useEasing: { type: Boolean , default: true},
    easingFn: { type: Function , default: (t, b, c, d)=>{
      return (c * (-Math.pow(2, (-10 * t) / d) + 1) * 1024) / 1023 + b;
    }},
    formatCost:{type:<PERSON>olean,default:false},
    decimalNumber: { type: String , default: "" },
  },

  emits: ["mounted", "callback"],
  setup(props, { emit }) {
    const state = reactive({
      localStartVal: props.startVal,
      displayValue: formatNumber(props.startVal),
      printVal: null,
      paused: false,
      localDuration: props.duration,
      startTime: null,
      timestamp: null,
      remaining: null,
      rAF: null,
      color: null,
      fontSize: "16px",
      decimalNumber:''
    });

    const getCountDown = computed(() => {
      return props.startVal > props.endVal;
    });

    watch([() => props.startVal, () => props.endVal], () => {
      if (props.autoplay) {
        start();
      }
    });

    function start() {
      const { startVal, duration, color, fontSize } = props;
      state.localStartVal = startVal;
      state.startTime = null;
      state.localDuration = duration;
      state.paused = false;
      state.color = color;
      state.fontSize = fontSize;
      state.rAF = requestAnimationFrame(count);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars,no-unused-vars
    function pauseResume() {
      if (state.paused) {
        resume();
        state.paused = false;
      } else {
        pause();
        state.paused = true;
      }
    }

    function pause() {
      cancelAnimationFrame(state.rAF);
    }

    function resume() {
      state.startTime = null;
      state.localDuration = +(state.remaining);
      state.localStartVal = +(state.printVal);
      requestAnimationFrame(count);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars,no-unused-vars
    function reset() {
      state.startTime = null;
      cancelAnimationFrame(state.rAF);
      state.displayValue = formatNumber(props.startVal);
    }

    function count(timestamp) {
      const { useEasing, easingFn, endVal } = props;
      if (!state.startTime) state.startTime = timestamp;
      state.timestamp = timestamp;
      const progress = timestamp - state.startTime;
      state.remaining = (state.localDuration) - progress;
      if (useEasing) {
        if (unref(getCountDown)) {
          state.printVal =
            state.localStartVal -
            easingFn(
              progress,
              0,
              state.localStartVal - endVal,
              state.localDuration
            );
        } else {
         
          state.printVal = easingFn(
            progress,
            state.localStartVal,
            endVal - state.localStartVal,
            state.localDuration
          );
        }
      } else {
        if (unref(getCountDown)) {
          state.printVal =
            state.localStartVal -
            (state.localStartVal - endVal) *
              (progress / (state.localDuration));
        } else {
          state.printVal =
            state.localStartVal +
            (endVal - state.localStartVal) *
              (progress / (state.localDuration));
        }
      }
      if (unref(getCountDown)) {
        state.printVal = state.printVal < endVal ? endVal : state.printVal;
      } else {
        state.printVal = state.printVal > endVal ? endVal : state.printVal;
      }
      state.displayValue = formatNumber(state.printVal);
      if (progress < (state.localDuration)) {
        state.rAF = requestAnimationFrame(count);
      } else {
        emit("callback");
      }
    }

    function formatNumber(num) {
      const { decimals, decimal, separator, suffix, prefix,formatCost,decimalNumber } = props;
      num = Number(num).toFixed(decimals);
      num += "";
      const x = num.split(".");
      let x1 = x[0];
      const x2 = x.length > 1 ? decimal + x[1] : "";
      const rgx = /(\d+)(\d{3})/;
      if (separator && !isNumber(separator)) {
        while (rgx.test(x1)) {
          x1 = x1.replace(rgx, "$1" + separator + "$2");
        }
      }

      return prefix  +  (formatCost && !x1.includes('.') ? x1+`.${decimalNumber}` : x1) + x2 + suffix;
    }

    onMounted(() => {
      if (props.autoplay) {
        start();
      }
      emit("mounted");
    });

    return () => (
      <>
        <span
          style={{
            color: props.color,
            fontSize: props.fontSize
          }}
        >
          {state.displayValue}
        </span>
      </>
    );
  }
});
