import request from '@/utils/request'
export default {
    // 签收审核列表
    signList: function (params) {
        return request.get('/tms/order/signAuditRecord/list', params);
    },
    // 签收审核详情
    signDetali: function (id) {
        return request.get('/tms/order/signAuditRecord/queryById', id);
    },
    // 签收审核驳回
    signAuditRefuse: function (params) {
        return request.post('/tms/order/signAuditRecord/auditRefuse', params);
    },
     // 签收审核列表导出
    exportRecord: function (params,config,resDetail,responseType) {
        return request.get('/tms/order/signAuditRecord/export', params,config,resDetail,responseType);
    },
    // 签收审核确认
    signAuditConfirm: function (params) {
        return request.post('/tms/order/signAuditRecord/auditConfirm', params);
    },
    //运单详情
    getTransDetail: function (params) {
        return request.get('/tms/orderDrug/queryById', params);
    },
    // 订单轨迹
    getRecordByTransOrderNo: function (params) {
        return request.get('/tms/transRecord/getTransRecord', params);
    },
    // 订单异动记录
    getOrderChangeDetails: function (params) {
        return request.get('/tms/orderChange/list', params);
    },
}
