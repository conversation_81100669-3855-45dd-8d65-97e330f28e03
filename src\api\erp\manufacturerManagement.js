/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-11 10:08:03
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-09-08 10:59:39
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\manufacturerManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from "@/utils/request"

export default {
  getList: function (inputForm) {
    return http.get('/erp/factory/erpManufacturerInformation/list', inputForm)
  },

  delete: function (ids) {
    return http.delete('/erp/factory/erpManufacturerInformation/delete', ids)
  },

  detail: function (id) {
    return http.get('/erp/factory/erpManufacturerInformation/queryById', id)
  },

  save: function (data) {
    return http.post('/erp/factory/erpManufacturerInformation/save', data)
  },

  delFlag: function (params) {
    return http.get('/erp/factory/erpManufacturerInformation/update', params)
  },

  exportFile: function (params) {
    return http.get('/file/importDownloadFile', params, { responseType: 'blob' }, true)
  },

  importExcel: function (data) {
    return http.post('/sys/notice/import', data)
  },
  release: function ({ id, status }) {
    return http.get(`/sys/notice/updateStatusById?id=${id}&status=${status}`)
  },
  getLicenseScopeList: function (params) {
    return http.get(`/erp/assist/erpBaseCommonValues/list`, params)
  },
  getFileLists: function (params) {
    return http.get(`/erp/common/erpCommonFile/list`, params)
  },
  // 删除质量信息
  erpManufacturerLicence: function (ids) {
    return http.delete('/erp/factory/erpManufacturerLicence/delete', ids)
  },
  // 删除GMP
  erpManufacturerGmpCertificate: function (ids) {
    return http.delete('/erp/factory/erpManufacturerGmpCertificate/delete', ids)
  },
  // 审核记录
  erpManufacturerInformationApproval: function (params) {
    return http.get(`/erp/factory/erpManufacturerInformationApproval/list`, params)
  },
  // 校验信用码
  checkLicenseCode: function (params) {
    return http.get(`/erp/factory/erpManufacturerInformation/checkLicenseCode`, params)
  },
  // 获取质量信息列表
  erpBaseCommonValues: function (params) {
    return http.get(`/erp/assist/erpBaseCommonValues/list`, params)
  },
  // 获取附件
  getFileList: function (params) {
    return http.get(`/sys/sysQualificationDocumentSet/list`, {...params,isFile:'1'})
  },
  // 获取自编码
  getSelfCodeByType: function (params) {
    return http.get(`/erp/factory/erpManufacturerInformation/getSelfCodeByType`, params)
  },
  
}
