<template>
    <div>
        <el-dialog v-model="dialogVisible" :before-close="close" :title="title" width="60%" class="progress">
            <div class="flex justify-center">
                <el-empty v-if="dataList.length == 0" description="暂无数据" />
                <el-timeline v-else>
                    <el-timeline-item v-for="(item, index) in dataList" :key="index" :color="setNodeColor(item.id, index)" :hollow="true" :size="setNodeSize(index)">
						<div class="progress-item" :style="setNodeStyle(index)">
							<div class="left-progress">
								<p class="text-black">{{ item.nodeName }}</p>
							</div>
							<div class="right-progress">
								<p class="text-black">{{ item.auditUserName }}<span v-if="item.status && item.type != '1'">({{selectDictLabel(auditStatusOptions,item.status)}})</span></p>
							</div>
						</div>
                    </el-timeline-item>
                </el-timeline>
            </div>
            <template #footer>
                <el-button @click="close">取 消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'ApprovalProgress',
    model: {
        prop: 'visible',
        event: 'update:visible'
    },
    props: {
        title: {
            type: String,
            default: undefined
        },
        visible: {
            type: Boolean,
            default: false
        },
		currentIndex: {
        	type: Number,
            default: null
        },
		dataList: {
        	type:Array,
			default: () => []
		},

    },
    data() {
        return {
            dialogVisible: this.visible,
			auditStatusOptions: [],
        };
    },
    computed: {
        /**
         * 设置节点颜色
         */
        setNodeColor() {
            return (id, index) => {
                return index == this.currentIndex ? '#14D076' : index == 0 ? '#0199FE' : '#EAEAEA';
            };
        },
        /**
         * 设置节点大小
         */
        setNodeSize() {
            return (index) => {
                return index == this.currentIndex ? 'large' : 'normal';
            };
        },
		setNodeStyle() {
			return (index) => {
				return index == this.currentIndex ? 'font-size:16px;font-weight:bold' : '';
			};
		}
    },
    created() {
        this.getDict();
    },
    methods: {
		/**
		 * 获取字典数据
		 */
		async getDict() {
			// 审核状态
			this.auditStatusOptions = await this.getDictList('audit_status');
		},
        /**
         * 关闭弹窗
         */
        close() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style scoped lang="scss">
.progress {
	.progress-item{
		position: relative;
		.left-progress {
			position: absolute;
			top: 0;
			left: -250px;
			width: 200px;
			text-align: right;
		}
	}

}
</style>
