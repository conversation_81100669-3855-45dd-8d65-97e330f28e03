﻿/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */
// import {treeList} from "@/api/system/areas";
import printLabelView  from "@/api/print/printLabelView";
// import { Message } from 'element-ui'

const baseURL = process.env.VUE_APP_BASE_API
Date.prototype.parseTime = function (pattern){
  console.log(this)
  return parseTime(this,pattern)
}

/**
 * pdf预览
 * @param templateId
 * @param ids
 */
export function pdfLabelView(templateId,ids){
	printLabelView.labelView({
    templateId:templateId,
    params:ids.toString()
  }).then(res=>{
    const binaryData = [];
    binaryData.push(res);
    //获取blob链接
    let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
    window.open(pdfUrl)
  })
}


/**
 * pdf预览(打印箱码)
 * @param data
 */
 export function pdfLabelViewNew(data){
	printLabelView.printTransBoxWithData(data).then(res=>{
    console.log("-------pdf文件:{}",res.data);
    // window.open(res.data+"#toolbar=0")
    let mywin = window.open('','_blank');
    mywin.location.href = res.data;
  })
}

/**
 * pdf箱码与付款方式预览
 * @param templateId
 * @param ids
 */
export function printPdfLabelViewNew(data){
	printLabelView.printLabelViewNew(data).then(res=>{
    const binaryData = [];
    binaryData.push(res);
    //获取blob链接
    let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
    window.open(pdfUrl)
  })
}

/**
 * pdf打印保温箱绑定记录码
 * @param templateId
 * @param ids
 */
export function batchPrintPdfLabelViewIncubator(data){
  console.log('12312');
	printLabelView.batchPrintPdfWithDataUrl(data).then(res=>{
    console.log(data);
    const binaryData = [];
    binaryData.push(res);
    //获取blob链接
    let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
    window.open(pdfUrl)
  })
}

