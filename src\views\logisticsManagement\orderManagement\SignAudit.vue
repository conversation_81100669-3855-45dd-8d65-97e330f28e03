<template>
    <div class="app-container customer-auto-height-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryParams" :inline="true" :model="queryParams" class="seache-form" @submit.prevent>
                <el-form-item v-show="isShowAll" label="货主公司" prop="companyId" style="width: 260px">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">
                        <el-option v-for="item in companyList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审核时间" prop="auditTime" style="width: 320px">
                    <el-date-picker v-model="queryParams.auditTime" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"> </el-date-picker>
                </el-form-item>
                <el-form-item label="订单号/运单号" prop="keyword" style="width: 260px">
                    <el-input v-model="queryParams.keyword" clearable placeholder="请输入订单号/运单号" @clear="handleQuery" @keyup.enter="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="审核状态" prop="status" style="width: 190px">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择审核状态" @change="handleQuery">
                        <el-option v-for="item in costTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="上传状态" prop="isUploadHzd" style="width: 250px">
                    <el-select v-model="queryParams.isUploadHzd" clearable placeholder="请选择上传状态" @change="handleQuery">
                        <el-option v-for="item in isUploadHzdList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="签收时间" prop="queryTime" style="width: 320px">
                    <el-date-picker v-model="queryParams.queryTime" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" :loading="loading" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button :loading="loading" icon="el-icon-download" type="warning" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :loading="loading" table-i-d="PriceCalculatedByTheFormulaTable" @queryTable="getList" />
            </div>
            <column-table ref="SignAudit" v-loading="loading" :columns="columns" :data="dataList" class="column-table customer-auto-height-table" element-loading-text="数据量较大，请稍后..." max-height="null">
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ fourplProductTypeFormat(row) }}</span>
                </template>
                <template #auditTime="{ row }">
                    <span>{{ timeFormatting(row.auditTime) }}</span>
                </template>
                <template #signDate="{ row }">
                    <span>{{ timeFormatting(row.signDate) }}</span>
                </template>
                <template #isUploadHzd="{ row }">
                    <span :class="row.isUploadHzd === 0 ? 'text-red-500' : 'text-green-500'">{{ selectDictLabel(isUploadHzdList, row.isUploadHzd) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-if="row.status != '1'" icon="el-icon-check" link plain size="small" type="warning" @click="openTheReviewPopUp(row)">审核</el-button>
                    <el-button icon="el-icon-info-filled" link plain size="small" type="primary" @click="openTheDetailsWindow(row)">运单详情</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- 审核 -->
        <el-drawer v-if="auditShow" v-model="auditShow" size="60vw" title="签收审核" @close="auditShow = false">
            <div style="background-color: #f2f2f2; padding: 10px">
                <transportation-records :parent-data="transportationData" @close="closeAuditShow" />
            </div>
        </el-drawer>

        <!-- 订单详情滑块-->
        <el-drawer v-if="infoOpen" v-model="infoOpen" :title="title" direction="rtl" size="1000px" @close="closeInfoOpen">
            <order-detail-trans :orderInfo="orderInfo" :parent-data="transportationData" :timeFormatting="timeFormatting"></order-detail-trans>
        </el-drawer>
        <el-image-viewer v-if="dialogVisible" :initial-index="initialIndex" :url-list="dialogImageUrl" :z-index="9999" @close="close" />
    </div>
</template>

<script>
import moment from 'moment';
import ModifyTransportationRecords from '@/views/logisticsManagement/orderManagement/ModifyTransportationRecords.vue';
import TransportationRecords from '@/views/logisticsManagement/orderManagement/TransportationRecords.vue';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import sign from '@/api/signAudit/sign.js';
import ColumnTable from '@/components/ColumnTable';
import OrderDetailTrans from '@/views/orderComponents/OrderDetailTrans';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'SignAudit',
    components: {
        ColumnTable,
        ModifyTransportationRecords,
        OrderDetailTrans,
        RightToolbar,
        SearchButton,
        TransportationRecords
    },
    data() {
        return {
            title: '',
            showSearch: true,
            isShowAll: false,
            auditShow: false,
            infoOpen: false,
            dialogVisible: false,
            transInfoOpen: false,
            costTypeList: [],
            companyList: [],
            dialogImageUrl: [],
            orderInfo: {},
            queryParams: {
                current: 1,
                size: 10,
                queryTime: [],
                companyId: undefined,
                isUploadHzd: undefined
            },
            loading: false,
            dataList: [],
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '180px', columnShow: true, fixed: 'left' },
                { title: '货主公司', key: 'companyName', minWidth: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '运单号', key: 'transOrderNo', minWidth: '180px', align: 'center', columnShow: true },
                { title: '审核状态', key: 'status', minWidth: '120px', align: 'center', columnShow: true },
                { title: '上传状态', key: 'isUploadHzd', minWidth: '120px', align: 'center', columnShow: true ,isShowHelpIcon:true, helpText: '回执单的上传状态'},
                { title: '驳回次数', key: 'refuseTime', align: 'center', columnShow: true },
                { title: '驳回原因', key: 'reason', minWidth: '280px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '签收时间', key: 'signDate', minWidth: '170px', align: 'center', columnShow: true },
                { title: '经办人', key: 'operator', minWidth: '170px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '经办人电话', key: 'operatorPhone', minWidth: '160px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '签收数量', key: 'signNum', minWidth: '120px', align: 'center', columnShow: true },
                { title: '拒收数量', key: 'refuseNum', minWidth: '170px', align: 'center', columnShow: true },
                { title: '审核人', key: 'auditName', minWidth: '170px', align: 'center', columnShow: true },
                { title: '审核时间', key: 'auditTime', minWidth: '170px', align: 'center', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '180px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            total: 0,
            initialIndex: 0,
            transportationData: {},
            isUploadHzdList: [] // 上传状态
        };
    },
    computed: {
        setStatusColor() {
            return (status) => {
                switch (status) {
                    case '0': // 未审核
                        return { color: '#f56c6c' };
                    case '1': // 已审核
                        return { color: '#67c23a' };
                    default:
                        return { color: '#909399' };
                }
            };
        },
        timeFormatting() {
            return (val) => {
                // 检查val是否为undefined、null或空字符串
                if (!val) {
                    return ''; // 如果val不存在，返回空字符串
                }
                // 使用moment格式化时间
                return moment(val).format('YYYY-MM-DD HH:mm:ss') || '';
            };
        }
    },
    created() {
        this.getDict();
        this.getCompanyList();
        // 将默认设置调整为当天
        this.queryParams.beginCreateDate = moment().format('YYYY-MM-DD');
        this.queryParams.endCreateDate = moment().format('YYYY-MM-DD');
        this.queryParams.queryTime = [this.queryParams.beginCreateDate, this.queryParams.endCreateDate];
        this.handleQuery();
    },
    methods: {
        /**
         * 关闭图片预览
         */
        close() {
            this.dialogVisible = false;
        },
        /**
         * 关闭审核弹窗
         */
        closeAuditShow() {
            this.auditShow = false;
            this.transportationData = [];
            this.getList();
        },
        /**
         * 关闭详情弹窗
         */
        closeInfoOpen() {
            this.infoOpen = false;
            this.orderInfo = {};
            this.transportationData = {};
        },
        fourplProductTypeFormat(val) {
            return this.selectDictLabel(this.costTypeList, val.status);
        },
        // 获取货主公司列表
        async getCompanyList() {
            try {
                const res = await enterpriseCooperation.cooperateSelect({ status: '1' }); // 请替换为实际的API接口
                if (res.code === 200) {
                    this.companyList = res.data;
                }
            } catch (error) {
                this.msgError('获取货主公司列表失败');
            }
        },
        async getDict() {
            this.costTypeList = await this.getDictList('signature_review_status');
            this.isUploadHzdList = await this.getDictList('fourpl_is_upload_hzd');
        },
        getList() {
            this.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { queryTime, auditTime, ...params } = this.queryParams;
            sign.signList(params)
                .then((response) => {
                    if (response.code === 200) {
                        this.dataList = response.data.records;
                        this.total = response.data.total;
                    }
                })
                .catch(() => {
                    this.msgError('获取数据失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 导出
        handleExport() {
            if (this.queryParams.queryTime) {
                if (this.queryParams.queryTime.length > 0) {
                    this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                    this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
                }
            }
            if (this.queryParams.auditTime && this.queryParams.auditTime.length === 2) {
                this.queryParams.beginSignDate = this.queryParams.auditTime[0] + ' 00:00:00';
                this.queryParams.endSignDate = this.queryParams.auditTime[1] + ' 23:59:59';
            }
            const { queryTime, auditTime, ...params } = this.queryParams;
            // this.queryParams['keyword'] = this.queryParams.code || this.queryParams.key;
            sign.exportRecord({ filename: '签收审核列表.xls', ...params }, '', '', 'blob').then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '签收审核列表.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                    this.msgSuccess('签收审核列表导出成功！');
                } else {
                    this.msgError('导出异常请联系管理员');
                }
            });
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = [];
            this.dialogImageUrl.push(file.url);
            this.dialogVisible = true;
        },
        handleQuery() {
            const { queryTime, auditTime, companyId, keyword, status, isUploadHzd } = this.queryParams;

            // 如果没有任何搜索条件，显示提示信息
            if (!companyId && !keyword && !status && !isUploadHzd && (!queryTime || queryTime.length === 0) && (!auditTime || auditTime.length === 0)) {
                this.$message({
                    message: '搜索条件不能全部为空，请最少保留一个搜索',
                    type: 'warning'
                });
                return;
            }

            this.queryParams.current = 1;

            // 如果有选择时间范围，设置开始和结束时间
            if (queryTime && queryTime.length === 2) {
                this.queryParams.beginCreateDate = queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = null;
                this.queryParams.endCreateDate = null;
            }
            if (auditTime && auditTime.length === 2) {
                this.queryParams.beginSignDate = auditTime[0] + ' 00:00:00';
                this.queryParams.endSignDate = auditTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginSignDate = null;
                this.queryParams.endSignDate = null;
            }

            this.getList();
        },
        /**
         * 打开详情弹窗
         * @param row
         */
        openTheDetailsWindow(row) {
            // 将row.id传给子组件 order-detail-trans 用于查询 运输记录
            this.transportationData.isEdit = false;
            this.transportationData.orderNo = row.orderNo || '';
            this.transportationData.transOrderNo = row.transOrderNo || '';
            this.transportationData.isDownloadAllImages = true;
            this.infoOpen = true;
            this.orderInfo = { ...row };
            this.title = '运单详情';
        },
        /**
         * 打开审核弹窗
         * @param row
         */
        openTheReviewPopUp(row) {
            this.transportationData.isEdit = true;
            this.transportationData.id = row.id;
            this.transportationData.isToExamine = true;
            this.transportationData.isDownloadAllImages = false;
            this.auditShow = true;
        },
        // 重置
        resetQuery() {
            this.$refs.queryParams.resetFields();
            this.handleQuery();
        },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}

.app-container {
    .reject_form {
        margin-top: 15px;
    }
}

:deep(.el-upload-list--picture-card .el-upload-list__item-actions:hover span) {
    display: contents !important;
    margin-left: 50px;
}

.number__unit__element {
    position: relative;

    :deep(.el-input__inner) {
        text-align: left;
    }

    &::after {
        content: '℃';
        position: absolute;
        right: 10px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
