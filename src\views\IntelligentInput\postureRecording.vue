<template>
  <div class="posture">
    <postureSearch ref="searchRef" @handleQuery="handleQuery"/>
    <el-card body-style="padding-top:0;padding-bottom:0;" class="box-card last-card" style="margin-top: 10px">
      <template #header>
        <div class="card-header">
          <span><el-button class="button" type="primary" @click="newAddFn">
              智能录入</el-button></span>
        </div>
      </template>
      <div class="item" style="margin-top:10px">
        <DragTableColumn v-loading="loadingFlag" v-model:queryParams="searchRef.searchForm" :columns="columns"
                         :getList="handleQuery"
                         :row-style="tableRowStyle" :tableData="tableList"
                         className="IntelligentInput_postureRecording">
          <template v-slot:operate="{ scopeData }">
            <el-button link type="primary" @click="detailFn(scopeData.row)"><img
                src="@/assets/icons/detail.png"
                style="margin: 2px 5px 0 0"/>详情
            </el-button>
          </template>
        </DragTableColumn>
        <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                       :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
                       layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                       @size-change="handleQuery"
                       @current-change="handleQuery"/>
      </div>
    </el-card>
    <el-dialog v-model="dialogVisible" :before-close="typeNum == 2 ? handleClose : null" :center="typeNum != 1"
               :fullscreen="typeNum != 1"
               :title="typeNum == 1 ? '智能录入' : echo(formRef.bigType, formRef.smallType, formRef.searchForm.n1, formRef.searchForm.n2)"
               :width="typeNum == 1 ? '40%' : null">
      <div v-loading="formLoading">
        <postureForm v-if="dialogVisible" ref="formRef" :typeNum="typeNum" @closeAll="closeAll"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="typeNum == 2 ? handleClose() : dialogVisible = false">取消</el-button>
          <el-button v-if="formRef&&typeNum == 1"
                     :disabled="typeNum == 1 && (!formRef.searchForm.n1 || !formRef.searchForm.n2)"
                     type="primary" @click="typeNum == 1 ? rightType() : null">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <Manufacturer v-if="visible" :beforeClose="beforeClose" :data="detailValue"
                  :deFlag="false" :getList="handleQuery" :visible="visible"/>
    <Custom v-if="visible2" :beforeClose="beforeClose2" :data="detailValue"
            :deFlag="false" :getList="handleQuery" :visible="visible2"/>
    <Supplier v-if="visible3" :beforeClose="beforeClose3" :data="detailValue"
              :deFlag="false" :getList="handleQuery" :visible="visible3"/>
    <compile ref="detailShop"/>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import postureSearch from './components/postureSearch.vue'
import postureForm from './components/postureForm.vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {applianceApi as postureApi} from "@/api/erp/IntelligentInput/postureApi";
import Manufacturer from "@/views/approve/components/manufacturer.vue";
import Compile from "@/components/detailsForm/compile.vue";
import Custom from "@/views/approve/components/custom.vue";
import Supplier from "@/views/approve/components/supplier.vue";

const {proxy} = getCurrentInstance();
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const loadingFlag = ref(false)
const dialogVisible = ref(false)
const visible = ref(false);
const visible2 = ref(false);
const visible3 = ref(false);
const detailValue = ref();
const dialogVisible2 = ref(false)
const statusType = ref()
const bigType = ref()
const detailShop = ref()
const formRef = ref(null)
const typeNum = ref(1)
const formLoading = ref(false)
const data = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const beforeClose = () => {
  visible.value = false;
  detailValue.value = {};
}
const beforeClose2 = () => {
  visible2.value = false;
  detailValue.value = {};
}
const beforeClose3 = () => {
  visible3.value = false;
  detailValue.value = {};
}
const emit = defineEmits([])
const props = defineProps({})
const searchRef = ref()
const tableList = ref()
const handleClose = () => {
  ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        dialogVisible.value = false
        emptyFn()
      })
      .catch(() => {
      });
}
const closeAll = () => {
  dialogVisible.value = false
  emptyFn()
  handleQuery()
}
const tableRowStyle = ({row}) => {
  if (row.status == "6") {
    return {
      color: '#e6a23c'
    }
  } else if (row.status == "1") {
    return {
      color: '#409eff'
    }
  } else if (row.status == "2") {
    return {
      color: '#67c23a'
    }
  } else if (row.status == "4") {
    return {
      color: '#ff4800'
    }
  } else if (row.status == "7") {
    return {
      color: '#ff4800'
    }
  }
}
const emptyFn = () => {
  formRef.value.searchForm.n1 = ''
  formRef.value.searchForm.n2 = ''
  formRef.value.dialogRow = {}
  formRef.value.formList = []
  typeNum.value = 1
}
const newAddFn = () => {
  dialogVisible.value = true
}
const echo = (arr1, arr2, value1, value2) => {
  let str = ''
  if (typeof value2 === "object") {
    value2.forEach(items => {
      if (str !== '') {
        str += '/' + arr2.find(item => item.value == items)?.name
      } else {
        str += arr2.find(item => item.value == items)?.name
      }
    })
  } else {
    str = arr2.find(item => item.value == value2)?.name
  }
  formRef.value.title = arr1.find(item => item.value == value1)?.name + '-' + str
  return formRef.value.title
}
const detailFn = (row) => {
  if (row.bigClass == 1) {
    row.documentId = row.extId
    visible.value = true;
    detailValue.value = row;
  } else if (row.bigClass == 2) {
    detailShop.value.details(4, row.extId, '', true, true);
  } else if (row.bigClass == 3) {
    detailShop.value.details(6, row.extId, '', true, true);
  } else if (row.bigClass == 4) {
    detailShop.value.details(5, row.extId, '', true, true);
  } else if (row.bigClass == 5) {
    detailShop.value.details(7, row.extId, '', true, true);
  } else if (row.bigClass == 6) {
    row.documentId = row.extId
    visible3.value = true;
    detailValue.value = row;
  } else if (row.bigClass == 7) {
    row.documentId = row.extId
    visible2.value = true;
    detailValue.value = row;
  }
}
const rightType = async () => {
  formLoading.value = true
  let res = await postureApi.getViewList({
    bigType: formRef.value.searchForm.n1,
    smallType: formRef.value.searchForm.n2.toString(),
    size: 1000
  })
  if (res.code == 200) {
    if (res.data.records.length <= 0) {
      ElMessage.error('无资质文件配置')
    } else {
      formRef.value.formList = res.data.records
      formRef.value.formList.forEach(item => {
        item.fileArr = []
      })
      typeNum.value = 2
      if (formRef.value.searchForm.n1 == 2) {
        formRef.value.drugOn()
      } else if (formRef.value.searchForm.n1 == 3) {
        formRef.value.insOn()
      } else if (formRef.value.searchForm.n1 == 4) {
        formRef.value.foodFn()
      } else if (formRef.value.searchForm.n1 == 5) {
        formRef.value.disFn()
      }
        formRef.value.searchForm.n2 = formRef.value.searchForm.n2.toString()
    }
  } else {
    ElMessage.error(res.msg)
  }
  formLoading.value = false
}

const handleQuery = async () => {
  let res = await postureApi.getList({
    name: searchRef.value.searchForm.n1,
    autoCode: searchRef.value.searchForm.n2,
    bigClass: searchRef.value.searchForm.n3,
    current: data.pageNum,
    size: data.pageSize
  })
  if (res.code === 200) {
    tableList.value = res.data.records
    data.total = res.data.total
  } else {
    ElMessage.error(res.msg)
  }
}
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  let statusKey = JSON.parse(localStorage.getItem('erp_review_status'))
  if (statusKey) {
    statusType.value = statusKey
  } else {
    statusType.value = await proxy.getDictList("erp_review_status")
    localStorage.setItem('status_sales', JSON.stringify(statusType.value))
  }
  let bigKey = JSON.parse(localStorage.getItem('directory_category'))
  if (bigKey) {
    bigType.value = bigKey
  } else {
    bigType.value = await proxy.getDictList("directory_category")
    localStorage.setItem('directory_category', JSON.stringify(bigType.value))
  }
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  handleQuery()
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})
const columns = ref([
  {label: '名称', prop: 'name'},
  {label: '自编码', prop: 'autoCode'},
  {
    label: '大类', prop: 'bigClass', type: 'status',
    filters: bigType, searchKey: 'n3'
  },
  {
    label: '审核状态', prop: 'status', type: 'status',
    filters: statusType, isFilter: false,
  },
    {label: '创建日期', prop: 'createDate', type: "date"},
  {label: '创建人', prop: 'createBy.name'},
  {label: '操作', prop: 'operate', minWidth: "100px", type: 'operate', fixed: 'right'},
])
</script>
<style lang='scss' scoped>
.posture {
  padding: 10px;
}

.item {
  margin-bottom: 18px;
  margin-top: -10px;
}
</style>
