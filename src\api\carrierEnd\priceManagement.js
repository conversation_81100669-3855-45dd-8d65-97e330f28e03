import request from '@/utils/request';
export default {
    // 新建价格
    priceBookGenerate: function (params) {
        return request.post('/cost/priceBook/save', params);
    },
    // 获取价格列表
    priceBookList: function (params) {
        return request.get('/cost/priceBook/list', params);
    },
    // 修改状态
    priceBookActive: function (params) {
        return request.get('/cost/priceBook/updateStatus', params);
    },
    // 获取车辆类型列表
    getCarTypeTree: function (params) {
        return request.get('/tms/carType/treeList', params);
    },
    // 查询价格本公式列表
    getPriceBookFormulaList: function (params) {
        return request.get('/cost/priceBook/formula/getList', params);
    },
    // 生成价格数据
    generatePriceData: function (params) {
        return request.post('/cost/priceBookData/genPriceData', params);
    },
    // 删除价格本
    deletePriceBook: function (params) {
        return request.delete('/cost/priceBook/delete', params);
    },
    // 发布价格本
    priceBookPublish: function (params) {
        return request.get('/cost/priceBook/updateReleaseStatus', params);
    },
    // 价格本明细列表
    priceDataList: function (params) {
        return request.get('/cost/priceBookData/list', params);
    },
    // 单个添加价格数据
    addPriceData: function (params) {
        return request.post('/cost/priceBookData/addPriceData', params);
    },
    // 批量添加价格明细
    batchAddPriceData: function (params) {
        return request.post('/cost/priceBookData/batchAddPriceData', params);
    },
    // 批量修改价格数据
    changePrice: function (params) {
        return request.post('/cost/priceBookData/bulkUpdatePrice', params);
    },
    // 校验是否存在可撤销批量修改数据
    getRevokeCount: function (params) {
        return request.get('/cost/priceBookData/isRevoke', params);
    },
    // 撤销批量修改价格数据
    revokePrice: function (params) {
        return request.get('/cost/priceBookData/cancelBatchPrice', params);
    },
    // 批量修改价格数据
    batchUpdatePriceData: function (params) {
        return request.post('/cost/priceBookData/priceDataEdit', params);
    },
    // 导出价格数据
    exportPriceData: function (params, config, resDetail, responseType) {
        return request.get('/cost/priceBookData/export', params, config, resDetail, responseType);
    },
    // 复制价格本
    priceBookCopy: function (params) {
        return request.get('/cost/priceBook/copy', params);
    },
    // 下载价格本模板
    downloadPriceBookTemplate: function (params, config, resDetail, responseType) {
        return request.get('/cost/priceBookData/import/template', params, config, resDetail, responseType);
    },
    // 导入价格数据
    checkImportPriceBook: function (params) {
        return request.post('/cost/priceBookData/import', params);
    },
	// 删除价格本明细
	deletePriceData: function (params) {
		return request.delete('/cost/priceBookData/delete', params);
	},
	// 公式列表
	getFormulaList: function (params) {
		return request.get('/cost/priceBookData/formulaList', params);
	}
};
