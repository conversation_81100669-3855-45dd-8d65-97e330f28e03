import http from "@/utils/request"

/*
 *@description: 角色菜单权限
 *@author: 路正宁
 *@date: 2023-03-17 11:58:54
 *@version: V1.0
*/
export default {
  save: function (inputForm) {
    return http.post(
       '/sys/roleMenu/save',
       inputForm
    )
  },

  delete: function (ids) {
    return http.delete(
       '/sys/roleMenu/delete',
       {ids: ids}
    )
  },
  /*
   * 配置菜单权限
   * @author: 路正宁
   * @date: 2023-04-04 09:31:01
  */
  configMenu: function (roleId,menuIds) {
    return http.get(
       '/sys/roleMenu/configMenu',
       {roleId: roleId,
        menuIds:menuIds
      }
    )
  },
  queryById: function (id) {
    return http.get(
       '/sys/roleMenu/queryById',
       {id: id}
    )
  },

  list: function (params) {
    return http.get(
       '/sys/roleMenu/list',
       params
    )
  },

  exportTemplate: function () {
    return http.get(
       '/sys/roleMenu/import/template',
       'blob'
    )
  },

  exportExcel: function (params) {
    return http.get(
       '/sys/roleMenu/export',
       params,
       'blob'
    )
  },

  importExcel: function (data) {
    return http.post(
       '/sys/roleMenu/import',
       data
    )
  }
}
