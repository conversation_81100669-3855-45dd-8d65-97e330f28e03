<template>
    <div v-loading="loading" class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form">
                <el-form-item label="结算公司" prop="name">
                    <el-input v-model="queryForm.name" clearable placeholder="请输入结算公司" @clear="handleQuery" @keyup.enter="handleQuery" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery()" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button type="primary" @click="handleAddSettlementCompany">添加结算公司</el-button>
                <el-button :disabled="!dataList || dataList.length === 0" icon="el-icon-download" type="warning" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="settlementCompanyTable" @queryTable="getList" />
            </div>
            <column-table ref="settlementCompanyTable" :columns="columns" :data="dataList" :max-height="600">
                <template #isShipper="{ row }">
                    <el-tag v-if="row.isShipper === '1'" type="success">是</el-tag>
                    <el-tag v-else type="info">否</el-tag>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-edit" link size="small" type="warning" @click="handleEdit(row)">修改</el-button>
                    <el-button icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(row)">删除</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>

        <!-- / 新增 抽屉 -->
        <el-drawer v-model="addSettlementCompanyVisible" :title="settlementCompanyTitle" size="500px" @close="closeAddSettlementCompany">
            <div v-loading="addSettlementCompanyLoading" :element-loading-text="addSettlementCompanyLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <el-form ref="addSettlementCompanyForm" :model="addSettlementCompanyForm" :rules="addSettlementCompanyFormRules" label-width="auto">
                        <el-form-item label="是否货主公司" prop="isShipper">
                            <el-radio-group v-model="addSettlementCompanyForm.isShipper" :disabled="isEdit">
                                <el-radio label="1">是</el-radio>
                                <el-radio label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="addSettlementCompanyForm.isShipper === '1'" label="合作货主" prop="id">
                            <el-select v-model="addSettlementCompanyForm.id" :disabled="isEdit" class="w-full" clearable filterable placeholder="请选择合作货主">
                                <el-option v-for="item in cooperativeShipperList" :key="item.companyId" :label="item.companyName" :value="item.companyId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="addSettlementCompanyForm.isShipper === '0'" label="结算公司" prop="name">
                            <el-input 
                                v-model="addSettlementCompanyForm.name" 
                                clearable 
                                placeholder="请输入结算公司"
                                @input="val => addSettlementCompanyForm.name = val.replace(/\s/g, '')"
                            />
                        </el-form-item>
                        <el-form-item label="税号" prop="taxIdNumber">
                            <el-input v-model="addSettlementCompanyForm.taxIdNumber" clearable placeholder="请输入税号（纳税人识别号）" />
                        </el-form-item>
                        <el-form-item label="地址" prop="address">
                            <el-cascader v-model="addSettlementCompanyForm.address" :options="sysAreas" class="w-full" clearable placeholder="请选择发件地址" />
                        </el-form-item>
                        <el-form-item label="详细地址" prop="detailAddress">
                            <el-input v-model="addSettlementCompanyForm.detailAddress" clearable maxlength="200" placeholder="请输入详细地址" show-word-limit type="textarea" />
                        </el-form-item>
                        <el-form-item label="电话" prop="phone">
                            <el-input v-model="addSettlementCompanyForm.phone" clearable placeholder="请输入电话" />
                        </el-form-item>
                        <el-form-item label="开户银行" prop="openBank">
                            <el-input v-model="addSettlementCompanyForm.openBank" clearable placeholder="请输入开户银行" />
                        </el-form-item>
                        <el-form-item label="银行账号" prop="bankAccount">
                            <el-input v-model="addSettlementCompanyForm.bankAccount" clearable maxlength="20" placeholder="请输入银行账号" />
                        </el-form-item>
                        <el-form-item label="备注" prop="remarks">
                            <el-input v-model="addSettlementCompanyForm.remarks" :rows="4" clearable maxlength="60" placeholder="请输入备注" show-word-limit type="textarea" />
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
            <template #footer>
                <el-button @click="closeAddSettlementCompany">取 消</el-button>
                <el-button v-if="!isEdit" type="primary" @click="onNewSettlementCompanyAdded">确 定</el-button>
                <el-button v-else type="warning" @click="onModifySettlementCompany">修 改</el-button>
            </template>
        </el-drawer>
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import settlementCompany from '@/api/carrierEnd/settlementCompany';

export default {
    name: 'SettlementCompany',
    components: { RightToolbar, ColumnTable, SearchButton },
    data() {
        return {
            loading: false,
            queryForm: {
                current: 1,
                size: 10,
                name: undefined
            },
            showSearch: true,
            dataList: [],
            columns: [
                { title: '结算公司', key: 'name', align: 'center', width: '150px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '是否货主公司', key: 'isShipper', align: 'center', width: '110px', columnShow: true },
                { title: '税号', key: 'taxIdNumber', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '地址', key: 'address', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '详细地址', key: 'detailAddress', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '电话', key: 'phone', align: 'center', width: '120px', columnShow: true },
                { title: '开户银行', key: 'openBank', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '银行账号', key: 'bankAccount', align: 'center', width: '180px', columnShow: true },
                { title: '备注', key: 'remarks', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', width: '150px', columnShow: true },
                { title: '创建时间', key: 'createDate', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '140px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            total: 0,
            addSettlementCompanyVisible: false,
            addSettlementCompanyLoading: false,
            addSettlementCompanyLoadingText: '加载中...',
            addSettlementCompanyForm: {
                id: undefined,
                name: undefined,
                isShipper: undefined,
                taxIdNumber: undefined,
                address: [],
                detailAddress: undefined,
                phone: undefined,
                openBank: undefined,
                bankAccount: undefined,
                remarks: undefined
            },
            addSettlementCompanyFormRules: {
                isShipper: [{ required: true, message: '请选择是否货主公司', trigger: 'change' }],
                id: [{ required: true, message: '请选择合作货主', trigger: 'change' }],
                name: [{ required: true, message: '请选择结算公司', trigger: 'change' }],
                bankAccount: [
                    { pattern: /^[0-9]*$/, message: '请输入数字', trigger: 'blur' },
                    { max: 20, message: '最多输入20位数字', trigger: 'blur' }
                ],
                phone: [{ pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
                detailAddress: [{ max: 200, message: '最多输入200个字符', trigger: 'blur' }]
            },
            sysAreas: [],
            cooperativeShipperList: [],
            settlementCompanyTitle: '新增结算公司',
            isEdit: false
        };
    },
    created() {
        this.sysAreas = this.getSysAreasThird;
        // 获取合作货主列表
        this.getCooperativeShipper();
        this.getList();
    },
    methods: {
        /**
         * 关闭新增结算公司抽屉
         */
        closeAddSettlementCompany() {
            this.addSettlementCompanyVisible = false;
            this.$refs.addSettlementCompanyForm.resetFields();
            this.isEdit = false;
            this.addSettlementCompanyForm = {
                id: undefined,
                name: undefined,
                isShipper: undefined,
                taxIdNumber: undefined,
                address: [],
                detailAddress: undefined,
                phone: undefined,
                openBank: undefined,
                bankAccount: undefined,
                remarks: undefined
            };
        },
        /**
         * 获取合作货主列表
         */
        getCooperativeShipper() {
            enterpriseCooperation
                .cooperateSelect({ status: '1' })
                .then((res) => {
                    if (res.code === 200) {
                        this.cooperativeShipperList = res.data;
                    }
                })
                .catch(() => {
                    this.cooperativeShipperList = [];
                });
        },
        /**
         * 获取列表
         */
        getList() {
            this.loading = true;
            settlementCompany
                .getSettlementCompanyList(this.queryForm)
                .then((res) => {
                    if (res.code === 200) {
                        this.dataList = res.data.records;
                        this.total = res.data.total;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 新增结算公司
         */
        handleAddSettlementCompany() {
            this.settlementCompanyTitle = '新增结算公司';
            this.addSettlementCompanyVisible = true;
        },
        /**
         * 删除
         * @param row
         */
        handleDelete(row) {
            if ('id' in row) {
                this.$confirm('是否删除该结算公司, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(async () => {
                        const res = await settlementCompany.deleteSettlementCompany({ ids: row.id });
                        if (res.code === 200) {
                            this.$message.success('删除成功');
                            this.getList();
                        }
                    })
                    .catch(() => {});
            }
        },
        /**
         * 打开修改
         * @param row
         */
        handleEdit(row) {
            this.settlementCompanyTitle = '修改结算公司';
            this.addSettlementCompanyVisible = true;
            this.isEdit = true;
            this.$nextTick(() => {
                this.addSettlementCompanyForm = { ...row };
                this.addSettlementCompanyForm.address = [row.provinceId, row.cityId, row.countyId];
            });
        },
        /**
         * 导出
         */
        handleExport() {
            settlementCompany.exportSettlementCompany({ filename: '结算公司列表.xls' }, '', '', 'blob').then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '结算公司列表.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    this.$message.error('导出异常请联系管理员');
                }
            });
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            this.getList();
        },
        /**
         * 修改结算公司
         */
        async onModifySettlementCompany() {
            // 表单验证
            const valid = await this.$refs.addSettlementCompanyForm.validate();
            if (!valid) {
                return; // 如果验证失败，直接返回
            }

            // 构建参数对象
            const params = {
                ...this.addSettlementCompanyForm,
                provinceId: this.addSettlementCompanyForm.address[0],
                cityId: this.addSettlementCompanyForm.address[1],
                countyId: this.addSettlementCompanyForm.address[2]
            };
            // 删除不必要的字段
            ['address', 'carrierId', 'createBy', 'createName', 'createDate', 'delFlag', 'updateBy', 'updateDate'].forEach((key) => {
                delete params[key];
            });

            // 异步提交修改请求
            const res = await settlementCompany.updateSettlementCompany(params);
            if (res.code === 200) {
                this.$message.success('修改成功');
                this.closeAddSettlementCompany();
                this.getList(); // 假设getList是刷新列表的方法
            }
        },
        /**
         * 新增结算公司
         */
        onNewSettlementCompanyAdded() {
            this.$refs.addSettlementCompanyForm.validate((valid) => {
                if (valid) {
                    // 根据isShipper的值处理名称和ID
                    const isShipper = this.addSettlementCompanyForm.isShipper;
                    if (isShipper === '1') {
                        // 如果是承运商，清除名称
                        this.addSettlementCompanyForm.name = '';
                    } else {
                        // 如果不是承运商，清除ID
                        this.addSettlementCompanyForm.id = undefined;
                    }

                    // provinceId, cityId, countyId
                    const params = {
                        ...this.addSettlementCompanyForm,
                        provinceId: this.addSettlementCompanyForm.address[0],
                        cityId: this.addSettlementCompanyForm.address[1],
                        countyId: this.addSettlementCompanyForm.address[2]
                    };
                    // 删除 params 中的 address 对象
                    delete params.address;

                    settlementCompany
                        .addSettlementCompany(params)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('新增成功');
                                this.closeAddSettlementCompany();
                                this.getList();
                            }
                        })
                        .catch(() => {
                            // 网络错误或其他异常处理
                            this.$message.error('新增失败，请稍后再试。');
                        });
                }
            });
        },
        /**
         * 重置
         */
        resetQuery() {
            this.$refs.queryForm.resetFields();
            this.handleQuery();
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
    .el-input.is-disabled .el-input__inner {
        -webkit-text-fill-color: #000 !important;
    }
}
</style>
