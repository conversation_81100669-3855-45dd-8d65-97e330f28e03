<template>
    <div style="background-color: rgb(245, 247, 253); padding-top: 10px">
        <el-form key="form" ref="form" :model="form" :rules="rules" label-width="80px" size="small">
            <el-card v-for="(item, idx) in form.orderList" :key="idx" shadow="never" style="margin: 0 10px 10px 10px">
                <div class="item-top">
                    <div class="item-text">
                        <div class="item-text-label">订单编号：</div>
                        <div class="item-text-content">{{ item.orderNo }}</div>
                    </div>
                    <div class="item-text">
                        <div class="item-text-label">运单编号：</div>
                        <div class="item-text-content">{{ item.transOrderNo }}</div>
                    </div>
                    <div class="item-text">
                        <div class="item-text-label">发件公司：</div>
                        <div class="item-text-content">{{ item.sendCompany }}</div>
                    </div>
                    <div class="item-text">
                        <div class="item-text-label">收件公司：</div>
                        <div class="item-text-content">{{ item.receiverCompany }}</div>
                    </div>
                    <div class="item-num">总共{{ item.goodsPackages }}件，交接{{ singNum }}件</div>
                </div>
                <el-form-item :prop="'orderList' + '.' + idx + '.' + 'imgReturnOrderList'" :rules="[{ required: true, validator: (rule, value, callback) => validateReturnOrderList(rule, value, callback), trigger: ['blur', 'change'] }]" class="is-required" label="签收返单">
                    <the-file-upload :accept="accept" :actions="actions" :data="setFileName('imgReturnOrderList', item.transOrderNo, idx)" :multiple="true" :value="item.imgReturnOrderList" list-type="picture-card" @input="handUploadSuccess($event, 'imgReturnOrderList', idx)" />
                </el-form-item>
                <el-form-item :prop="'orderList' + '.' + idx + '.' + 'imgAutographList'" :rules="[{ required: true, validator: (rule, value, callback) => validateAutographList(rule, value, callback), trigger: ['blur', 'change'] }]" class="is-required" label="客户签名">
                    <the-file-upload :accept="accept" :actions="actions" :data="setFileName('imgAutographList', item.transOrderNo, idx)" :value="item.imgAutographList" list-type="picture-card" @input="handUploadSuccess($event, 'imgAutographList', idx)" />
                </el-form-item>
            </el-card>
        </el-form>
        <div class="foot__flex">
            <el-button type="primary" @click="submitForm">确认</el-button>
            <el-button @click="cancel">取消</el-button>
        </div>
        <el-dialog v-model="payCodeOpen" :show-close="false" title="确认收款" width="650px">
            <el-form ref="payCodeForm" :model="payCodeForm" :rules="payCodeRules" label-width="100px">
                <el-form-item>
                    <img :src="payCodeImg" alt="" style="width: 200px" />
                </el-form-item>
                <el-form-item label="支付金额" prop="amount">
                    <el-input v-model="payCodeForm.amount" placeholder="请输入支付金额" type="number" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="payCodeSubmitForm">确 定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration.js';
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
import TheFileUpload from '@/components/theFileUpload/TheFileUpload.vue';

export default {
    name: 'TransferCustomer',
    components: { TheFileUpload },
    props: {
        signatureList: {
            required: true,
            type: Array
        }
    },
    data() {
        return {
            accept: 'image/*',
            actions: ['preview', 'remove'],
            form: {},
            rules: {},
            singNum: 0, // 签收总数
            isArrivePay: '0', // 是否到付
            amount: '0',
            payCodeOpen: false, // 付款弹窗
            payCodeForm: {
                amount: ''
            }, // 付款参数
            payCodeImg: null, // 付款图片
            payCodeRules: {
                amount: [
                    { required: true, message: '请输入支付金额', trigger: ['change', 'blur'] },
                    { pattern: /(^[1-9]{1}[0-9]*$)|(^[0-9]*\.[0-9]{1,2}$)/, message: '请输入正确的支付金额', trigger: ['change', 'blur'] }
                ]
            },
            handNum: 0 // 总件数
        };
    },
    computed: {
        setFileName() {
            return (type, transOrderNo) => {
                let fileName = '';
                if (transOrderNo) {
                    fileName += transOrderNo + '_';
                }
                if (type === 'imgReturnOrderList') {
                    fileName += '回执单';
                } else if (type === 'imgAutographList') {
                    fileName += '客户签名';
                }
                return { fileName };
            };
        }
    },
    created() {
        this.getPayCode();
        this.singNum = 0;
        this.form.orderList = this.signatureList.map((item) => {
            if (item.paymentMethod == '2') {
                this.isArrivePay = '1';
                this.payCodeForm = {
                    amount: ''
                }; // 付款参数
                this.payCodeOpen = true;
            }
            this.singNum = this.singNum + item.goodsPackages;
            this.handNum = this.handNum + item.goodsPackages;
            return { ...item, imgReturnOrderList: [], imgAutographList: [], fileList: [] };
        });
    },
    methods: {
        // 关闭
        cancel() {
            this.$emit('callbackMethod');
        },
        // 获取付款信息
        getPayCode() {
            otherConfiguration.getCarrierCodeFile().then((response) => {
                if (response.code === 200) {
                    this.payCodeImg = response.data.fileUrl;
                } else {
                    this.msgError('获取支付二维码失败');
                }
            });
        },
        handUploadSuccess(data, type, index) {
            this.form.orderList[index][type] = data;
        },
        // 确认收款
        payCodeSubmitForm() {
            this.$refs['payCodeForm'].validate((valid) => {
                if (valid) {
                    this.payCodeOpen = false;
                    this.amount = this.payCodeForm.amount;
                }
            });
        },
        // 提交
        submitForm() {
            // 将orderList中的imgReturnOrderList与imgAutographList赋值给fileList
            this.form.orderList.forEach((item) => {
                let imgReturnOrderList = item.imgReturnOrderList.map((file) => {
                    return {
                        fileType: 1,
                        fileName: file.name,
                        fileUrl: file.url
                    };
                });
                let imgAutographList = item.imgAutographList.map((file) => {
                    return {
                        fileType: 2,
                        fileName: file.name,
                        fileUrl: file.url
                    };
                });
                item.fileList = [...imgReturnOrderList, ...imgAutographList];
            });
            this.form.singNum = this.singNum;
            this.form.refuseNum = this.handNum - this.singNum;
            this.form.amount = this.amount;
            this.form.isArrivePay = this.isArrivePay;
            this.form.handWay = '4';
            this.form.taskType = '';
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    orderManagement.batchSignOrder(this.form).then((response) => {
                        if (response.code == 200) {
                            this.msgSuccess('签收成功');
                            this.cancel();
                        } else {
                            this.msgError('签收失败');
                        }
                    });
                }
            });
        },
        // 验证客户签名
        validateAutographList(rule, value, callback) {
            var idx = rule.field.substring(rule.field.indexOf('.') + 1, rule.field.lastIndexOf('.'));
            if (value == '' || value == undefined || value == []) {
                callback(new Error('至少上传一张客户签名'));
            } else {
                callback();
            }
        },
        // 验证签收返单
        validateReturnOrderList(rule, value, callback) {
            var idx = rule.field.substring(rule.field.indexOf('.') + 1, rule.field.lastIndexOf('.'));
            if (value == '' || value == undefined || value == []) {
                callback(new Error('至少上传一张签收返单'));
            } else {
                callback();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-tabs__nav-wrap {
        position: initial;
    }

    .el-tabs__nav-scroll {
        padding-left: 15px;
    }

    //.el-upload {
    //  .el-button {
    //    border: 1px dashed #d9d9d9;
    //    background: #FFFFFF;
    //    width: 128px;
    //    height: 128px;
    //    font-size: 28px;
    //    color: #8c939d;
    //  }
    //}

    //.upload-demo {
    //  display: flex;
    //}

    //  .el-upload-list {
    //    display: flex;
    //
    //    li {
    //      width: 128px;
    //      height: 128px;
    //      border: 1px dashed #d9d9d9;
    //      margin-top: 0;
    //      margin-left: 10px;
    //
    //      img {
    //        width: 108px;
    //        height: 108px;
    //      }
    //
    //      .el-upload-list__item-name {
    //        display: none;
    //      }
    //
    //      .el-upload-list__item-status-label, .el-icon-close, .el-icon-close-tip {
    //        z-index: 99;
    //      }
    //    }
    //  }
    //}
    .foot__flex {
        display: flex;
        justify-content: center;
        padding-bottom: 15px;
    }

    .item-top {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .item-text {
            width: 45%;
            display: flex;
            margin: 5px;
            justify-content: space-between;

            .item-text-label {
                color: #999999;
            }

            .item-text-content {
                color: #333333;
            }
        }

        .item-text:nth-child(2n + 1) {
            margin-left: 10px;
        }

        .item-num {
            color: #0f87ff;
            margin: 10px;
        }
    }

    .el-upload-list--picture-card {
        .el-upload-list__item-actions {
            span + span {
                margin-left: 10px;
            }
        }

        .el-icon--close-tip {
            display: none !important;
        }
    }

    .el-form-item--small {
        margin-bottom: 30px;
    }
    .el-upload-list--picture-card {
        .el-upload-list__item-actions {
            width: var(--el-upload-list-picture-card-size);
            height: var(--el-upload-list-picture-card-size);

            span + span {
                margin-left: 10px;
            }
        }
    }
}
</style>
