import request from '@/utils/request';
export default {
    // 下载到款记录导入模板
    downloadTemplate: function (params, config, resDetail, responseType) {
        return request.get('/tms/arrive/moneyRecord/downloadTemplate', params, config, resDetail, responseType, 1);
    },
    // 确认导入数据
    batchImport: function (data) {
        return request.post('/tms/arrive/moneyRecord/batchImport', data);
    },
    // 到款记录列表
    getMoneyRecordList: function (params) {
        return request.get('/tms/arrive/moneyRecord/list', params);
    },
    // 到款记录导出
    exportList: function (params, config, resDetail, responseType) {
        return request.post('/tms/arrive/moneyRecord/exportList', {}, config, resDetail,params);
    },
    // 查询合计金额
    getTotalMoney: function (params, config, resDetail, responseType) {
        return request.get('/tms/arrive/moneyRecord/getTotalMoney', params);
    },
    // 删除到款记录
    delMoneyRecord: function (params) {
        return request.delete('/tms/arrive/moneyRecord/delete', params);
    },
    // 到款记录-月度汇总-分页查询
    getMonthPageList: function (params) {
        return request.get('/tms/arrive/group/getMonthPageList', params);
    },
    // 到款记录-月度汇总-导出
    exportMonthList: function (params, config, resDetail, responseType) {
        return request.post('/tms/arrive/group/export-MonthList', {}, config, resDetail,params);
    },
};
