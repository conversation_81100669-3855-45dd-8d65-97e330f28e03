<template>
    <div class="container">
        <div class="left">
            <div class="left-top">
                <div class="data-block block-1">
                    <div class="data-content">
                        <div class="data-text">
                            <div class="number">{{ orderNum }}</div>
                            <div class="title">订单总数量</div>
                        </div>
                    </div>
                </div>
                <div class="data-block block-2">
                    <div class="data-content">
                        <div class="data-text">
                            <div class="number">{{ payOrderNum }}</div>
                            <div class="title">结算订单数量</div>
                        </div>
                    </div>
                </div>
                <div class="data-block block-3">
                    <div class="data-content">
                        <div class="data-text">
                            <div class="number">¥{{ orderAmountNum }}</div>
                            <div class="title">订单总金额</div>
                        </div>
                    </div>
                </div>
                <div class="data-block block-4">
                    <div class="data-content">
                        <div class="data-text">
                            <div class="number">{{ finishRate }}%</div>
                            <div class="title">订单完成率</div>
                        </div>
                    </div>
                </div>
                <!--
                <div class="data-block block-5">
                    <div class="data-content">
                        <div class="data-text">
                            <div class="number">{{ onTimeRate }}%</div>
                            <div class="title">按时送达率</div>
                        </div>
                    </div>
                </div>
-->
            </div>
            <div class="left-middle">
                <OrderTrendChart :isCarrier="isCarrier" />
            </div>
            <div class="left-bottom">
                <div class="bottom-left">
                    <div class="header">
                        <div class="text-black text-base">市/县订单量排行 TOP 10</div>
                        <div class="filter-wrapper">
                            <el-radio-group v-model="queryTypeCityCounty" size="small">
                                <el-radio-button label="3">本月</el-radio-button>
                                <el-radio-button label="4">全年</el-radio-button>
                            </el-radio-group>
                            <el-date-picker v-model="dateRangeCityCounty" end-placeholder="结束月份" range-separator="至" size="small" start-placeholder="开始月份" style="width: 180px" type="monthrange" value-format="YYYY-MM-DD" />
                        </div>
                    </div>
                    <div class="rank-table">
                        <div class="table-header">
                            <div class="text-center">
                                <img alt="排名" src="../../assets/home/<USER>" />
                            </div>
                            <span class="col-name">市区名称</span>
                            <span class="col-orders">订单数量</span>
                            <span class="col-count">件数</span>
                        </div>
                        <div class="table-body">
                            <div v-for="(item, index) in rankingList" :key="index" class="table-row">
                                <span class="col-index">{{ index + 1 }}</span>
                                <span class="col-name">{{ item.name }}</span>
                                <div class="col-orders">
                                    <div class="progress-bar">
                                        <div :style="{ width: item.percentage + '%' }" class="progress"></div>
                                    </div>
                                    <span class="order-number">{{ item.orders }}</span>
                                </div>
                                <span class="col-count">{{ item.count }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom-right">
                    <div class="header">
                        <div class="text-black text-base">全国订单量排行 TOP 10</div>
                        <div class="filter-wrapper">
                            <el-radio-group v-model="queryTypeNational" size="small">
                                <el-radio-button label="3">本月</el-radio-button>
                                <el-radio-button label="4">全年</el-radio-button>
                            </el-radio-group>
                            <el-date-picker v-model="dateRangeNational" end-placeholder="结束月份" range-separator="至" size="small" start-placeholder="开始月份" style="width: 180px" type="monthrange" value-format="YYYY-MM-DD" />
                        </div>
                    </div>
                    <div class="rank-table">
                        <div class="table-header">
                            <div class="text-center">
                                <img alt="排名" src="../../assets/home/<USER>" />
                            </div>
                            <span class="col-name">省份名称</span>
                            <span class="col-orders">订单数量</span>
                            <span class="col-count">件数</span>
                        </div>
                        <div class="table-body">
                            <div v-for="(item, index) in nationalRankingList" :key="index" class="table-row">
                                <span class="col-index">{{ index + 1 }}</span>
                                <span class="col-name">{{ item.name }}</span>
                                <div class="col-orders">
                                    <div class="progress-bar">
                                        <div :style="{ width: item.percentage + '%' }" class="progress"></div>
                                    </div>
                                    <span class="order-number">{{ item.orders }}</span>
                                </div>
                                <span class="col-count">{{ item.count }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="right-top">
                <div class="chart-container h-100">
                    <template v-if="isCarrier">
                        <DriverTaskChart />
                    </template>
                    <template v-else>
                        <div class="good-news">
                            <div class="news-header">
                                <img alt="好消息" class="news-title-img" src="../../assets/home/<USER>" />
                            </div>
                            <div class="news-content">
                                <p>1、为满足广大客户的配送要求，新增兰州一西安兰州一深圳，兰州一乌鲁木齐市药械配送路线，冷藏、冷冻零担及整车均可;</p>
                                <p>2、调整兰州一新区药械冷藏、常温发车班次为周一至周五;</p>
                                <p>有上述区域发货需求的客户,请详询</p>
                                <p>吕经理：***********</p>
                                <p>王经理：***********</p>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            <div class="right-middle">
                <div class="shortcut-header">
                    <span class="title">快捷入口</span>
                    <el-icon class="el-icon--upload" size="16" @click="openTheCustomizedQuickBusinessPopUpWindow()"><el-icon-plus /></el-icon>
                </div>
                <div v-if="showQuickServiceData.length" class="quick-service-grid">
                    <router-link v-for="(item, index) in showQuickServiceData.slice(0, 6)" :key="index" :to="item.path" class="quick-service-item">
                        <img :src="getQuickServiceIcon(index)" alt="{{ item.meta.title }}" class="quick-service-icon" width="40" />
                        <span class="quick-service-title">{{ item.meta.title }}</span>
                    </router-link>
                </div>
                <el-empty v-else :image-size="2" description="右上角添加快捷业务"></el-empty>
                <!--                <div class="item__content" style="position: absolute; bottom: 0; width: calc(100% - 40px)">
                    <router-link v-for="(item, index) in recentVisits" :key="index" :to="item.path">
                        <svg-icon :icon-class="item.meta.icon" class="mr10" />
                        {{ item.meta.title }}
                    </router-link>
                </div>-->
            </div>
            <div class="right-bottom">
                <div class="notice-header">
                    <span class="title">公告</span>
                    <!--                    <span class="more">更多<el-icon><arrow-right /></el-icon></span>-->
                </div>
                <div class="notice-list">
                    <div v-for="(item, index) in noticeList" :key="index" class="notice-item">
                        <span class="notice-title">【{{ item.title }}】</span>
                        <span class="notice-content">{{ item.content }}</span>
                    </div>
                </div>
            </div>
            <div v-if="!isCarrier" class="right-guide">
                <div class="guide-header">
                    <span class="title">新手入门</span>
                </div>
                <div class="notice__content">
                    <div class="content__item">
                        <div class="item__title">
                            <span>【订单如何下单】</span>
                            <a href="http://doc.lzsacc.com/web/#/11/98" target="_blank">批量下单、</a>
                            <a href="http://doc.lzsacc.com/web/#/11/101" target="_blank">冷链下单、</a>
                            <a href="http://doc.lzsacc.com/web/#/11/102" target="_blank" title="订单下错如何处理">订单下错如何处理</a>
                        </div>
                    </div>
                    <div class="content__item">
                        <div class="item__title">
                            <span>【运单配载】</span>
                            <a href="http://doc.lzsacc.com/web/#/11/107" target="_blank">运单自动配载、</a>
                            <a href="http://doc.lzsacc.com/web/#/11/108" target="_blank">非同城运单能否配载</a>
                        </div>
                    </div>
                    <div class="content__item">
                        <div class="item__title">
                            <span>【运单配载】</span>
                            <a href="http://doc.lzsacc.com/web/#/11/109" target="_blank">配载时怎样添加或删除运单、</a>
                            <a href="http://doc.lzsacc.com/web/#/11/110" target="_blank" title="配载时怎样配置线路司机">配载时怎样配置线路司机</a>
                        </div>
                    </div>
                    <div class="content__item">
                        <div class="item__title">
                            <span>【运单配载】</span>
                            <a href="http://doc.lzsacc.com/web/#/11/111" target="_blank" title="配载时没有承运网点或线路司机时怎样添加">配载时没有承运网点或线路司机时怎样添加</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--  /自定义快捷业务 弹窗   -->
        <el-dialog v-model="customizedQuickServiceVisible" class="dialog__customizedQuickService" title="自定义快捷业务" width="840px">
            <!--    /已选快捷业务    -->
            <div class="customizedQuickService__selectedExpressBusiness">
                <div class="selectedExpressBusiness__describe">已选快捷业务：最多选择12个自定义快捷业务</div>
                <div v-if="customizedQuickServiceData.length" class="selectedExpressBusiness__content">
                    <div v-for="(i, index) in customizedQuickServiceData" :key="index" class="content__item">
                        <div>{{ i.meta.title }}</div>
                        <div class="item__delete" @click="deleteCustomizedQuickService(i, index)"></div>
                    </div>
                </div>
                <el-empty v-else :image-size="50" description="请选快捷业务" style="padding: 0"></el-empty>
            </div>
            <div class="customizedQuickService__splitLine">
                <div class="splitLine__search">
                    <el-input v-model="searchBusinessValue" clearable placeholder="请输入业务名称" prefix-icon="el-icon-search" @blur="searchBusiness" @clear="openTheCustomizedQuickBusinessPopUpWindow('1')" @keyup.enter.native="searchBusiness"></el-input>
                </div>
            </div>
            <!--    /快捷业务    -->
            <div class="box-period">
                <el-scrollbar>
                    <div class="customizedQuickService__expressBusiness">
                        <div v-for="(item, index) in expressBusinessData" v-show="!item.checked && !item.meta.hidden" :key="index">
                            <div v-if="!item.checked && !item.meta.hidden" class="expressBusiness__item">
                                <div class="item__title">{{ item.meta.title }}</div>
                                <div v-if="item.children && item.children.length > 0" class="item__content">
                                    <div v-for="(i, index_i) in item.children" v-show="!i.checked && !i.meta.hidden" :key="index_i">
                                        <div v-if="!i.checked && !i.meta.hidden && !i.component" class="content__i" style="display: flex; gap: 15px">
                                            <div>{{ i.meta.title }}</div>
                                            <div v-if="i.children && i.children.length > 0" class="content__i__flex">
                                                <div v-for="(j, index_j) in i.children" v-show="!j.checked && !j.meta.hidden" :key="index_j">
                                                    <div v-if="!j.checked && !j.meta.hidden" class="content__item">
                                                        <div>{{ j.meta.title }}</div>
                                                        <div class="item__add" @click="addCustomizedQuickService(j, index, index_i, index_j)"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else-if="!i.checked && !i.meta.hidden && i.component" class="content__i" style="display: flex; gap: 15px">
                                            <div>{{ i.meta.title }}</div>
                                            <div class="content__i__flex">
                                                <div class="content__item">
                                                    <div>{{ i.meta.title }}</div>
                                                    <div class="item__add" @click="addCustomizedQuickService(i, index, index_i, null)"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
            <div class="customizedQuickService__footer">
                <el-button @click="customizedQuickServiceVisible = false">取 消</el-button>
                <el-button type="primary" @click="setCustomShortcutBusiness">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import OrderTrendChart from '@/components/OrderTrendChart.vue';
import DriverTaskChart from '@/components/DriverTaskChart.vue';
import home from '@/api/home/<USER>';
import sysUserService from '@/api/model/sys/sysUserService.js';
import tool from '@/utils/tool';

export default {
    name: 'Home',
    components: {
        OrderTrendChart,
        DriverTaskChart
    },
    data() {
        return {
            orderNum: 0,
            payOrderNum: 0,
            orderAmountNum: '0',
            finishRate: 0,
            // onTimeRate: this.generateRandomNumber(90, 99),
            queryTypeCityCounty: '3',
            queryTypeNational: '3',
            dateRangeCityCounty: [],
            dateRangeNational: [],
            months: this.generateMonths(),
            rankingList: [],
            selectedMonthNational: '',
            nationalRankingList: [],
            showQuickServiceData: [],
            customizedQuickServiceData: [],
            // 自定义快捷业务 弹窗
            customizedQuickServiceVisible: false,
            searchBusinessValue: null,
            expressBusinessData: [],
            showQuickServiceId: null,
            recentVisits: [],
            noticeList: [],
            isCarrier: false, // 是否为承运端
            userTypeChecked: false // 添加标记用户类型是否已确认
        };
    },
    watch: {
        dateRangeCityCounty: {
            async handler(newVal) {
                // 确保用户类型已经确认
                if (!this.userTypeChecked) {
                    return;
                }

                if ((!newVal?.[0] || !newVal?.[1]) && !this.isQueryTypeChanging) {
                    this.setCityRanking();
                    return;
                }

                if (newVal?.[0] && newVal?.[1]) {
                    const endDate = this.getMonthLastDay(newVal[1]);
                    const params = this.getDateRangeParams(new Date(newVal[0]), endDate);
                    this.getCityRankingData(params);
                }
            },
            immediate: true,
            deep: true
        },
        dateRangeNational: {
            handler(newVal) {
                // 确保用户类型已经确认
                if (!this.userTypeChecked) {
                    return;
                }

                if ((!newVal?.[0] || !newVal?.[1]) && !this.isNationalQueryTypeChanging) {
                    this.setNationalRanking();
                    return;
                }

                if (newVal?.[0] && newVal?.[1]) {
                    const endDate = this.getMonthLastDay(newVal[1]);
                    const params = this.getDateRangeParams2(new Date(newVal[0]), endDate);
                    this.getNationalRankingData(params);
                }
            },
            immediate: true,
            deep: true
        },
        queryTypeCityCounty: {
            handler() {
                this.isQueryTypeChanging = true;
                this.dateRangeCityCounty = [];
                this.setCityRanking();
                this.$nextTick(() => {
                    this.isQueryTypeChanging = false;
                });
            }
        },
        queryTypeNational: {
            handler() {
                this.isNationalQueryTypeChanging = true;
                this.dateRangeNational = [];
                this.setNationalRanking();
                this.$nextTick(() => {
                    this.isNationalQueryTypeChanging = false;
                });
            }
        }
    },
    async created() {
        // 先检查用户类型
        await this.checkUserType();

        // 确认用户类型后再执行其他初始化操作
        this.getTopFiveData();
        this.getCustomizedQuickServiceData();
        this.getCommonBusiness();
        this.getNotice();
    },
    methods: {
        // 添加自定义快捷业务
        addCustomizedQuickService(val, index, index_i, index_j) {
            // 判断this.customizedQuickServiceData的长度是否大于等于12
            if (this.customizedQuickServiceData.length >= 12) {
                ElMessage({
                    message: '最多添加12个快捷业务',
                    type: 'warning'
                });
                return;
            }

            // 添加选择路由
            this.customizedQuickServiceData.push({ ...val, index, index_i, index_j });
            // 判断如果是三级路由还是二级路由
            if (this.expressBusinessData[index].children[index_i].children) {
                this.expressBusinessData[index].children[index_i].children[index_j].checked = true;
            } else {
                this.expressBusinessData[index].children[index_i].checked = true;
            }
            this.upwardVerification(index, index_i);
        },
        // 已选快捷业务 删除
        deleteCustomizedQuickService(data, del_index) {
            // 添加的时候保存了选择的路由，这里需要恢复显示，并且删除保存的索引值
            const { index, index_i, index_j } = data;
            // 恢复显示 上一级 checked false
            if (this.expressBusinessData[index].children[index_i].children) {
                this.expressBusinessData[index].children[index_i].children[index_j].checked = false;
            }
            this.expressBusinessData[index].children[index_i].checked = false;
            this.expressBusinessData[index].checked = false;

            // 删除
            this.customizedQuickServiceData.splice(del_index, 1);
        },
        /**
         * 格式化日期为 'YYYY-MM-DD' 格式
         * @param {Date} date - 要格式化的日期对象
         * @returns {string} 格式化后的日期字符串
         */
        formatDate(date) {
            return date
                .toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                })
                .replace(/\//g, '-');
        },
        /**
         * 格式化金额
         */
        formatMoney(number) {
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
        /**
         * 生成月份选项列表
         */
        generateMonths() {
            const months = [{ value: '', label: '全部' }];
            const currentDate = new Date();
            const startYear = 2024;
            const startMonth = 0; // 从1月开始 (0-11)

            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth();

            for (let year = startYear; year <= currentYear; year++) {
                const monthStart = year === startYear ? startMonth : 0;
                const monthEnd = year === currentYear ? currentMonth : 11;

                for (let month = monthStart; month <= monthEnd; month++) {
                    const value = `${year}-${String(month + 1).padStart(2, '0')}`;
                    const label = `${year}年${month + 1}月`;
                    months.push({ value, label });
                }
            }

            return months;
        },
        /**
         * 生成随机数
         */
        generateRandomNumber(min, max) {
            return Math.floor(Math.random() * (max - min + 1) + min);
        },
        /**
         * 获取城市排名数据
         */
        getCityRankingData(params) {
            // 根据用户类型调用不同的接口
            const api = this.isCarrier ? home.getCityTop : home.getCityTopOwner;
            api(params).then((res) => {
                if (res.code === 200 && res.data) {
                    this.processRankingData(res.data);
                }
            });
        },
        getCommonBusiness() {
            // 从vuex获取所有业务信息
            const allFastBusinesses = JSON.parse(JSON.stringify(this.$router.sc_getMenu()));
            const expressBusinessData = allFastBusinesses.filter((item) => item.name !== 'app' && item.name !== 'home' && item.name !== 'xmlTemplate');
            // commonAccessLinks children children name OwnerAddOrder FourplOrderList FouplAddressBook FourplBulkOrder CarrierAddOoder FourplCollectionService OrderManagement TransOrder
            const commonAccessLinks = [];
            expressBusinessData.forEach((item) => {
                if (item.children) {
                    item.children.forEach((item_i) => {
                        if (item_i.children) {
                            item_i.children.forEach((item_j) => {
                                if (
                                    item_j.name === 'OwnerOrders' ||
                                    item_j.name === 'OrderList' ||
                                    item_j.name === 'CompanyAddressBook' ||
                                    item_j.name === 'BatchOrderPlacement' ||
                                    item_j.name === 'PlaceAnOrder' ||
                                    item_j.name === 'collectTasks' ||
                                    item_j.name === 'OwnerOrderList' ||
                                    item_j.name === 'OwnerOrderList'
                                ) {
                                    commonAccessLinks.push({ ...item_j });
                                }
                            });
                        }
                    });
                }
            });
            // commonAccessLinks 如果长度大于4，那么截取前4个
            if (commonAccessLinks.length > 4) {
                commonAccessLinks.splice(4);
            }
            this.recentVisits = commonAccessLinks;
        },
        getCustomizedQuickServiceData() {
            this.showQuickServiceData = [];
            let info = JSON.parse(localStorage.getItem('USER_INFO'));
            sysUserService.userSettingList({ userId: info.content.id, attribute: 'customizedQuickServiceData' }).then((res) => {
                if (res.code == 200 && res.data.records && res.data.records.length >= 1) {
                    this.showQuickServiceData = JSON.parse(res.data.records[0].value);
                    this.customizedQuickServiceData = JSON.parse(res.data.records[0].value);
                    this.showQuickServiceId = res.data.records[0].id;
                }
            });
        },
        /**
         * 获取日期范围参数
         * @param {Date} startDate - 开始日期
         * @param {Date} endDate - 结束日期
         * @returns {Object} 查询参数对象
         */
        getDateRangeParams(startDate, endDate) {
            return {
                queryType: this.queryTypeCityCounty,
                startQueryDate: this.formatDate(startDate),
                endQueryDate: this.formatDate(endDate)
            };
        },
        getDateRangeParams2(startDate, endDate) {
            return {
                queryType: this.queryTypeNational,
                startQueryDate: this.formatDate(startDate),
                endQueryDate: this.formatDate(endDate)
            };
        },
        /**
         * 获取月份最后一天
         * @param {string} dateStr - 日期字符串 YYYY-MM-DD
         * @returns {Date} 月份最后一天的日期对象
         */
        getMonthLastDay(dateStr) {
            const date = new Date(dateStr.substring(0, 7) + '-01');
            date.setMonth(date.getMonth() + 1);
            date.setDate(0);
            return date;
        },
        /**
         * 获取全国排名数据
         */
        getNationalRankingData(params) {
            // 根据用户类型调用不同的接口
            const api = this.isCarrier ? home.getNationalTop : home.getNationalTopOwner;
            api(params).then((res) => {
                if (res.code === 200 && res.data) {
                    this.processNationalRankingData(res.data);
                }
            });
        },
        getNotice() {
            home.getNotice().then((res) => {
                if (res.code === 200) {
                    this.noticeList = res.data.records.map((i) => ({
                        title: i.noticeTitle,
                        content: i.noticeContent
                    }));
                }
            });
        },
        /**
         * 获取首页数据-顶部五个数量
         */
        getTopFiveData() {
            // 根据用户类型调用不同的接口
            const api = this.isCarrier ? home.getTopFiveData : home.getTopFiveDataOwner;
            api().then((res) => {
                if (res.code === 200 && res.data) {
                    this.orderNum = res.data.orderNum;
                    this.payOrderNum = res.data.payOrderNum;
                    this.orderAmountNum = this.formatMoney(res.data.orderAmountNum);
                    this.finishRate = Math.round(res.data.finishRate * 100);
                }
            });
        },
        handleShortcutClick(item) {
            this.$router.push(item.path);
        },
        // 打开 自定义快捷业务 弹窗
        openTheCustomizedQuickBusinessPopUpWindow(val) {
            this.customizedQuickServiceVisible = true;
            //从localStorage中获取自定义快捷业务
            // val 代表 入口是搜索点击清除
            // if (val !== '1') {
            // 	this.customizedQuickServiceData = JSON.parse(localStorage.getItem('customizedQuickServiceData')) || [];
            // }
            // 从vuex获取所有业务信息
            const allFastBusinesses = JSON.parse(JSON.stringify(this.$router.sc_getMenu()));
            // allFastBusinesses 中过滤出 name为Fourpl和 name为Carrier 的数据
            this.expressBusinessData = allFastBusinesses.filter((item) => item.name !== 'app' && item.name !== 'home' && item.name !== 'xmlTemplate');
            // this.expressBusinessData 排序 name为Fourpl的排在前面
            this.expressBusinessData.sort((a, b) => {
                return a.name === '物流管理' ? -1 : 1;
            });
            // 遍历 this.customizedQuickServiceData
            this.customizedQuickServiceData.forEach((item) => {
                if (this.expressBusinessData[item.index].children[item.index_i].children) {
                    this.expressBusinessData[item.index].children[item.index_i].children[item.index_j].checked = true;
                } else {
                    this.expressBusinessData[item.index].children[item.index_i].checked = true;
                }
            });
            // 遍历 this.expressBusinessData
            // this.expressBusinessData 下的 children 下的 所有 children 都为checked时，this.expressBusinessData[index].children[index_i].checked = true;
            // this.expressBusinessData 下的 所有 children 都为checked时，this.expressBusinessData[index].checked = true;
            this.expressBusinessData.forEach((item, index) => {
                if (item.children) {
                    item.children.forEach((item_i, index_i) => {
                        let checked = true;
                        if (item_i.children) {
                            item_i.children.forEach((item_j, index_j) => {
                                if (!item_j.checked) {
                                    checked = false;
                                }
                            });
                            if (checked) {
                                item_i.checked = true;
                            }
                        }
                    });
                }
                let checked = true;
                if (item.children) {
                    item.children.forEach((item_i, index_i) => {
                        if (!item_i.checked) {
                            checked = false;
                        }
                    });
                    if (checked) {
                        item.checked = true;
                    }
                }
            });
            // 打开弹窗
            this.customizedQuickServiceVisible = true;
        },
        processNationalRankingData(data) {
            const maxOrders = Math.max(...data.map((item) => item.orderNum));
            this.nationalRankingList = data.map((item) => ({
                name: item.receiverProvinceName,
                orders: item.orderNum,
                count: item.boxCodeNum,
                percentage: Math.round((item.orderNum / maxOrders) * 100)
            }));
        },

        /**
         * 处理排名数据
         * @param {Array} data - 原始排名数据
         */
        processRankingData(data) {
            const maxOrders = Math.max(...data.map((item) => item.orderNum));
            this.rankingList = data.map((item) => ({
                name: item.receiverCityName,
                orders: item.orderNum,
                count: item.boxCodeNum,
                percentage: Math.round((item.orderNum / maxOrders) * 100)
            }));
        },
        // 搜索业务
        searchBusiness() {
            if (this.searchBusinessValue) {
                let str = ['', ...this.searchBusinessValue, ''].join('.*'); //转化成正则格式字符串
                let reg = new RegExp(str); //正则
                this.expressBusinessData.forEach((item, index) => {
                    item.children?.forEach((item_i, index_i) => {
                        if (item_i.children) {
                            item_i.children?.forEach((item_j) => {
                                if (reg.test(item_j.meta.title)) {
                                    item_j.checked = false;
                                } else {
                                    item_j.checked = true;
                                }
                            });
                        } else {
                            if (reg.test(item_i.meta.title)) {
                                item_i.checked = false;
                            } else {
                                item_i.checked = true;
                            }
                        }
                        this.upwardVerification(index, index_i);
                    });
                });
                // 刷新视图
                this.$forceUpdate();

                // 遍历 this.expressBusinessData
                // this.expressBusinessData 下的 children 下的 所有 children 都为checked时，this.expressBusinessData[index].children[index_i].checked = true;
                // this.expressBusinessData 下的 所有 children 都为checked时，this.expressBusinessData[index].checked = true;
            } else {
                this.openTheCustomizedQuickBusinessPopUpWindow('1');
            }
        },
        /**
         * 设置城市排名数据
         */
        setCityRanking() {
            const now = new Date();
            let startDate, endDate;

            if (this.queryTypeCityCounty === '3') {
                // 本月
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            } else {
                // 本年
                startDate = new Date(now.getFullYear(), 0, 1);
                endDate = new Date(now.getFullYear(), 11, 31);
            }

            const params = this.getDateRangeParams(startDate, endDate);
            this.getCityRankingData(params);
        },
        // 确定 添加定义快捷业务
        setCustomShortcutBusiness() {
            if (this.customizedQuickServiceData.length == 0) {
                return this.msgError('请至少选择一个快捷业务');
            }
            // this.customizedQuickServiceVisible = false;
            // this.showQuickServiceData = this.customizedQuickServiceData;
            // 本地存储 this.name
            // localStorage.setItem('userName', this.name);
            // // 将this.customizedQuickServiceData保存到localStorage中
            // localStorage.setItem('customizedQuickServiceData', JSON.stringify(this.customizedQuickServiceData));
            // 提交保存配置
            let info = JSON.parse(localStorage.getItem('USER_INFO'));
            if (!info.content.id) {
                return this.msgError('未找到用户数据');
            }
            sysUserService.userSettingSave({ id: this.showQuickServiceId, userId: info.content.id, attribute: 'customizedQuickServiceData', value: JSON.stringify(this.customizedQuickServiceData) }).then((res) => {
                if (res.code == 200) {
                    this.msgSuccess('保存成功');
                    this.getCustomizedQuickServiceData();
                    this.customizedQuickServiceVisible = false;
                }
            });
        },
        /**
         * 设置全国排名数据
         */
        setNationalRanking() {
            const now = new Date();
            let startDate, endDate;
            if (this.queryTypeNational === '3') {
                // 本月
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            } else {
                // 本年
                startDate = new Date(now.getFullYear(), 0, 1);
                endDate = new Date(now.getFullYear(), 11, 31);
            }
            const params = this.getDateRangeParams2(startDate, endDate);
            this.getNationalRankingData(params);
        },
        upwardVerification(index, index_i) {
            // 如果this.expressBusinessData[index].children[index_i].children里所有的checked都为true，那么this.expressBusinessData[index].children[index_i].checked = true
            if (this.expressBusinessData[index].children[index_i].children) {
                const checkedAll = this.expressBusinessData[index].children[index_i].children.every((item) => item.checked === true);
                if (checkedAll) {
                    this.expressBusinessData[index].children[index_i].checked = true;
                }
            }

            // 如果this.expressBusinessData[index].children里所有的checked都为true，那么this.expressBusinessData[index].checked = true
            const checkedAll2 = this.expressBusinessData[index].children.every((item) => item.checked === true);
            if (checkedAll2) {
                this.expressBusinessData[index].checked = true;
            }
        },
        /**
         * 检查用户类型
         */
        async checkUserType() {
            const Organization = tool.data?.get('Organization');
            if (!Organization || !Organization.length || !Organization[0]?.type) {
                this.isCarrier = false;
            } else {
                const roleType = Organization[0].type;
                this.isCarrier = roleType === '1';
            }
            this.userTypeChecked = true; // 标记用户类型已确认

            // 用户类型确认后，手动触发一次数据加载
            this.setCityRanking();
            this.setNationalRanking();
        },
        getQuickServiceIcon(index) {
            const icons = [require('@/assets/home/<USER>'), require('@/assets/home/<USER>'), require('@/assets/home/<USER>'), require('@/assets/home/<USER>'), require('@/assets/home/<USER>'), require('@/assets/home/<USER>')];
            return icons[index];
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: grid;
    grid-template-columns: 3fr 1.2fr;
    gap: 15px;
    padding: 10px;
    height: calc(100vh - 105px);
    background-color: #f5f7fd;
    box-sizing: border-box;
}

.left {
    display: grid;
    grid-template-rows: auto 2fr 2fr;
    gap: 10px;
    height: 100%;
}

.left-top,
.left-middle {
    background-color: #ffffff;
    padding: 10px;
}

.left-bottom {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.right {
    display: grid;
    grid-template-rows: v-bind('isCarrier ? "auto auto 1fr" : "auto auto auto auto"');
    gap: 10px;
    height: 100%;

    .right-top {
        background-color: #ffffff;
    }
    .right-middle,
    .right-bottom {
        background-color: #ffffff;
        padding: 10px;
    }
}

.left-top {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    background-color: transparent;
    padding: 0;
}

.data-block {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 120px;
    padding: 15px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

/* 可以为每个块设置不同的背景图片 */
.data-block:nth-child(1) {
    background-image: url('../../assets/home/<USER>');
}
.data-block:nth-child(2) {
    background-image: url('../../assets/home/<USER>');
}
.data-block:nth-child(3) {
    background-image: url('../../assets/home/<USER>');
}
.data-block:nth-child(4) {
    background-image: url('../../assets/home/<USER>');
}
.data-block:nth-child(5) {
    background-image: url('../../assets/home/<USER>');
}

.data-content {
    height: 100%;
}

.data-text {
    display: flex;
    flex-direction: column;
    gap: 8px;
    text-align: left;
    width: 100%;
}

.number {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
}

.title {
    font-size: 16px;
    white-space: nowrap;
    font-weight: bold;
    color: #fff;
}

/* 为金额添加特殊样式 */
.block-3 .number {
    font-size: 22px; /* 由于带有货币符号，稍微调小字号 */
}

.left-middle {
    background-color: #ffffff;
    padding: 10px;
    overflow: hidden;
    height: 100%;
}

/* 如果OrderTrendChart组件内部有根元素，建议添加以下样式 */
.left-middle :deep(.chart-container) {
    height: 100%;
    width: 100%;
}

.bottom-left,
.bottom-right {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    background-color: #fff;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rank-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 60px 1fr 2fr auto;
    align-items: center;
}

.table-header {
    color: #999;
    font-size: 14px;
}

.table-body {
    display: flex;
    flex-direction: column;
    gap: 4px;
    .col-index {
        text-align: center;
    }
}

.table-row {
    font-size: 14px;
    color: #333;
    .col-count {
        min-width: 45px;
        text-align: right;
    }
}

.col-orders {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: #409eff;
    border-radius: 3px;
}

.order-number {
    min-width: 60px;
}

.right-top {
    .section-header {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
    }

    .filter-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .chart-container {
        .no-data-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
    }
}

.filter-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
}

.right-middle {
    .shortcut-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
    }
}
.dialog__customizedQuickService {
    ::v-deep {
        .el-dialog__header {
            border-bottom: 1px solid #f1f1f1;
            background: #ffffff;
            padding: 20px;
        }

        .el-dialog__body {
            padding: 20px 30px;
        }
    }

    .customizedQuickService__selectedExpressBusiness {
        .selectedExpressBusiness__describe {
            color: #999999;
            font-size: 14px;
        }

        .selectedExpressBusiness__content {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 25px;
            margin-top: 15px;

            .content__item {
                display: flex;
                align-items: center;
                background: #e6ecff;
                padding: 10px 25px;
                position: relative;
                color: #333333;

                .item__delete {
                    position: absolute;
                    right: -7px;
                    top: -8px;
                    cursor: pointer;
                    background: url(@/assets/images/delete.png) no-repeat;
                    background-size: 100% 100%;
                    width: 18px;
                    height: 18px;
                }
            }
        }
    }

    .customizedQuickService__splitLine {
        width: 100%;
        height: 1px;
        background: #f1f1f1;
        margin: 30px 0 60px 0;
        position: relative;

        .splitLine__search {
            position: absolute;
            top: 15px;
            right: 8px;
        }
    }

    .customizedQuickService__expressBusiness {
        display: flex;
        flex-wrap: wrap;
        gap: 25px;
        position: relative;
        flex-direction: column;

        .expressBusiness__item {
            display: flex;
            flex-direction: column;

            .item__title {
                color: #333333;
                font-size: 18px;
                margin-bottom: 20px;
            }

            .item__content {
                display: flex;
                flex-wrap: wrap;
                gap: 25px;

                .content__i {
                    display: flex;
                    gap: 15px;
                    flex-direction: column;

                    .content__i__flex {
                        display: flex;
                        gap: 15px;
                        flex-wrap: wrap;
                    }
                }

                .content__item {
                    display: flex;
                    align-items: center;
                    background: #f5f5f6;
                    padding: 10px 25px;
                    position: relative;
                    color: #333333;
                    white-space: nowrap;

                    .item__add {
                        position: absolute;
                        right: -7px;
                        top: -8px;
                        cursor: pointer;
                        background: url(@/assets/images/add.png) no-repeat;
                        background-size: 100% 100%;
                        width: 18px;
                        height: 18px;
                    }
                }
            }
        }
    }

    .customizedQuickService__footer {
        display: flex;
        justify-content: flex-end;
        padding: 20px 0 0 30px;
    }
}
.box-period {
    height: 300px;

    ::v-deep {
        .el-scrollbar {
            height: 100%;
        }

        .el-scrollbar__wrap {
            //overflow: scroll;
            width: 102%;
        }
    }
}
.quick-service-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    padding: 10px 10px 5px 10px;
}

.quick-service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
    text-decoration: none;
    background-color: #fff;

    .quick-service-title {
        width: 100%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 5px;
        box-sizing: border-box;
    }

    &:hover {
        border-color: #409eff;
        color: #409eff;
        transform: translateY(-2px);
    }

    .svg-icon {
        margin-right: 8px;
        font-size: 18px;
    }
}
.right-bottom {
    .notice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .more {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #999;
            font-size: 14px;
            cursor: pointer;
        }
    }
    .notice-list {
        .notice-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding: 10px;
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            background-color: #fff;

            .notice-title {
                font-weight: bold;
            }
            .notice-content {
                color: #999;
                font-size: 14px;
            }
        }
    }
}
.owner-tip {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 4px;
}

.right-guide {
    background-color: #ffffff;
    padding: 15px;

    .guide-header {
        margin-bottom: 15px;

        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
    }
    .notice__content {
        display: flex;
        flex-direction: column;
        gap: 14px;

        .content__item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 15px;

            .item__title {
                color: #333333;
                // white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;
            }

            .item__time {
                color: #57626e;
                white-space: nowrap;
            }
        }
    }
}
.good-news {
    background-image: url('../../assets/home/<USER>');
    height: 100%;
    padding: 10px 20px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;

    .news-header {
        .news-title-img {
            max-width: 200px;
            height: auto;
        }
    }

    .news-content {
        color: #333;
        line-height: 1.8;

        p {
            margin-bottom: 5px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>
