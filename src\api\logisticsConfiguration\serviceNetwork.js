import request from '@/utils/request'

export default {
  /** s 集散网点 */
  // 查询服务网点列表
  listBranch:function (params) {
    return request.get('/tms/branch/list',params);
  },
  // 根据网点id获取网点详情
  getBranch:function (params) {
    return request.get('/tms/branch/queryById',params);
  },
  // 网点新增或修改
  saveBranch:function(data){
    return request.post( '/tms/branch/save', data)
  },
  // 获取承运商的网点数据
  getBranchList:function (params) {
    return request.get('/tms/branch/getBranchList',params);
  },
  // 删除网点信息
  delBranch: function (params) {
    return request.delete("/tms/branch/delete", params);
  },
  // 根据网点编码获取当前网点人员列表
  getBranchEmpList:function (params) {
    return request.get('/tms/branchEmp/getBranchEmpList',params);
  },
  // 根据用户id获取所有网点信息
  getBranchByUserId:function (params) {
    return request.get('/tms/branchEmp/getBranchEmpList',params);
  },
  // 网点配置人员信息保存
  saveBranchEmp:function(data){
    return request.post( '/tms/branchEmp/save', data)
  },
  /** e 集散网点 */
  /** s 区域 */
  // 区域列表数据 areaType 区域分类 1-揽收区域 2-地配区域 3-集货区域
  listArea:function (params) {
    return request.get('/tms/area/list',params);
  },
  // 根据网点编码获取区域
  getBranchAreaList:function (params) {
    return request.get('/tms/area/getBranchAreaList',params);
  },
  // 新增或修改区域保存
  saveArea:function(data){
    return request.post( '/tms/area/save', data)
  },
  // 根据区域id获取区域信息
  queryAreaById:function (params) {
    return request.get('/tms/area/queryById',params);
  },
  // 根据区域编码获取区域范围
  getAreaWideList:function (params) {
    return request.get('/tms/areaWide/getAreaWideList',params);
  },
  // 删除网点信息
  delArea: function (params) {
    return request.delete("/tms/area/delete", params);
  },
  /** e 区域 */
  /** s 三方物流 */
  // 三方物流列表
  listThreePartFlow:function (params) {
    return request.get('/tms/threePartFlow/list',params);
  },
  // 根据id获取三方物流详情
  queryThreePartFlowById:function (params) {
    return request.get('/tms/threePartFlow/queryById',params);
  },
  // 新增会修改三方物流保存
  saveThreePartFlow:function(data){
    return request.post( '/tms/threePartFlow/save', data)
  },
  // 删除三方物流
  delThreePartFlow: function (params) {
    return request.delete("/tms/threePartFlow/delete", params);
  },
  /** e 三方物流 */
}


