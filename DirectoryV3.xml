<?xml version="1.0" encoding="UTF-8"?>
 <trees>
     <tree path="/src/views/platformConfiguration/BillingFactorSetting.vue" title="计费因子设置"/>
     <tree path="/src/views/platformConfiguration/BillingParameterSettings.vue" title="计费参数设置"/>
     <tree path="/src/views/platformConfiguration/BillingFactorSelection.vue" title="计费变量选择"/>
     <tree path="/src/views/platformConfiguration/ValueAddedServiceChargeConfiguration.vue" title="增值服务费配置"/>
	 <tree path="/src/views/platformConfiguration" title="平台功能"/>
	 <tree path="/src/views/carrierFunction" title="承运端功能"/>
	 <tree path="/src/views/carrierFunction/ValueAddedServices.vue" title="增值服务配置"/>
	 <tree path="/src/views/carrierFunction/PriceCalculatedByTheFormula.vue" title="价格本计算公式配置"/>
     <tree path="/src/components/searchModule" title="搜索模块"/>
     <tree path="/src/components/searchModule/SearchButton.vue" title="搜索按钮"/>
     <tree path="/src/views/carrierFunction/PriceManagement.vue" title="价格本管理"/>
    <tree path="/src/views/carrierFunction/CombinedOfficialArrangement.vue" title="合算公式配置"/>
	<tree path="/src/views/carrierFunction/OrderCostRecalculated.vue" title="订单费用重新计算"/>
    <tree path="/src/views/carrierFunction/CustomerOrderExpenseManagement.vue" title="客户订单费用管理"/>
    <tree path="/src/views/carrierFunction/OrderFeeDetailsWithDetails.vue" title="订单费用详情 带明细"/>
    <tree path="/src/api/platformFeatures" title="平台功能"/>
	<tree path="/src/api/platformFeatures/billingFactorSettings.js" title="计费因子设置"/>
    <tree path="/src/api/platformFeatures/billingParameterSettings.js" title="计费参数设置"/>
    <tree path="/src/api/platformFeatures/billingFactorSelection.js" title="计费变量选择"/>
    <tree path="/src/api/platformFeatures/valueAddedServiceChargeConfiguration.js" title="增值服务费配置"/>
    <tree path="/src/api/carrierEnd" title="承运端"/>
    <tree path="/src/api/carrierEnd/valueAddedServices.js" title="增值服务配置"/>
    <tree path="/src/api/carrierEnd/priceCalculatedByTheFormula.js" title="价格本计算公式配置"/>
	<tree path="/src/api/carrierEnd/priceManagement.js" title="价格本管理"/>
    <tree path="/src/api/carrierEnd/combinedOfficialArrangement.js" title="合算公式配置"/>
    <tree path="/src/api/carrierEnd/orderCostRecalculated.js" title="订单费用重新计算"/>
    <tree path="/src/views/logisticsConfiguration/prepaymentFromTheShipper" title="货主端预付款"/>
    <tree path="/src/views/logisticsConfiguration/customerPrepayment" title="客户预付款"/>
    <tree path="/src/views/logisticsConfiguration/prepaymentFromTheShipper/CustomerPrepaymentBalance.vue" title="货主端预付款" extension="" presentableText="" tooltipTitle="" icon=""
          textColor="" backgroundColor=""/>
    <tree path="/src/views/logisticsConfiguration/prepaymentFromTheShipper/CustomerPrepaymentBalanceDetail.vue" title="货主端预付款账户明细"/>
    <tree path="/src/views/shipperEnd" title="货主端"/>
    <tree path="/src/views/shipperEnd/billingManagement/PaymentDoc.vue" title="付款单"/>
    <tree path="/src/views/shipperEnd/billingManagement/AccountStatement.vue" title="对账单"/>
    <tree path="/src/views/logisticsManagement/taskManagement" title="任务管理"/>
    <tree path="/src/views/logisticsManagement/taskManagement/CollectTasks.vue" title="揽收任务"/>
    <tree path="/src/views/logisticsManagement/taskManagement/DeliveryTasks.vue" title="配送任务"/>
    <tree path="/src/api/logisticsConfiguration/prepaymentFromTheShipper.js" title="货主端预付款"/>
    <tree path="/src/views/carrierFunction/BillingParameterConfiguration.vue" title="计费参数配置"/>
    <tree path="/src/api/carrierEnd/billingParameterConfiguration.js" title="计费参数配置"/>
	<tree path="/src/api/carrierEnd/customerOrderExpenseManagement.js" title="客户订单费用管理"/>
    <tree path="/src/api/shipperEnd" title="货主端"/>
    <tree path="/src/views/carrierFunction/CustomerStatement.vue" title="客户对账单"/>
    <tree path="/src/api/carrierEnd/customerStatement.js" title="客户对账单"/>
    <tree path="/src/views/carrierFunction/CustomerPaymentDocument.vue" title="客户收款单"/>
    <tree path="/src/api/carrierEnd/customerPaymentDocument.js" title="客户收款单"/>
    <tree path="/src/views/carrierFunction/OrderCostQueryStatistics.vue" title="客户订单费用查询统计"/>
    <tree path="/src/api/carrierEnd/orderCostQueryStatistics.js" title="客户订单费用查询统计"/>
	<tree path="/src/api/carrierEnd/collectTasks.js" title="揽收任务"/>
    <tree path="/src/api/carrierEnd/deliveryTasks.js" title="配送任务"/>
    <tree path="/src/views/home/<USER>" title="司机端下载"/>
	<tree path="/src/views/home/<USER>" title="集散端下载"/>
	<tree path="/src/components/qrcode" title="生成二维码带logo"/>
	<tree path="/src/components/qrcode/QrCodeWithLogo.vue" title="二维码带logo"/>
    <tree path="/src/views/carrierFunction/SuperZonePriceBook.vue" title="超区价格本"/>
	<tree path="/src/api/carrierEnd/superZonePriceBook.js" title="超区价格本"/>
	<tree path="/src/views/inboundSorting" title="入库分拣管理"/>
	<tree path="/src/views/inboundSorting/KeyCustomerSorting.vue" title="大客户分拣"/>
	<tree path="/src/api/carrierEnd/keyCustomerSorting.js" title="大客户分拣"/>
    <tree path="/src/views/logisticsManagement/orderManagement/TransportationRecordsPage.vue" title="运输记录"/>
	<tree path="/src/views/logisticsManagement/orderManagement/TransportationRecords.vue" title="运输记录组件"/>
	<tree path="/src/api/carrierEnd/transportationRecordsPage.js" title="运输记录"/>
    <tree path="/src/views/logisticsManagement/orderManagement/ModifyTransportationRecords.vue" title="修改运输记录"/>
    <tree path="/src/views/carrierFunction/SettlementCompany.vue" title="结算公司"/>
    <tree path="/src/api/carrierEnd/settlementCompany.js" title="结算公司"/>
	<tree path="/src/views/logisticsConfiguration/InvoiceInformationMaintenance.vue" title="发票信息维护"/>
    <tree path="/src/views/logisticsConfiguration/PaymentApplication.vue" title="支付申请"/>
	<tree path="/src/api/shipperEnd/paymentApplication.js" title="支付申请"/>
    <tree path="/src/views/carrierFunction/PaymentApproval.vue" title="支付审批"/>
    <tree path="/src/views/logisticsConfiguration/enterpriseCooperation/CooperativeCargoOwners.vue" title="合作货主"/>
	<tree path="/src/views/carrierFunction/PaymentOrderApproval.vue" title="付款单审批、修改"/>
	<tree path="/src/views/carrierFunction/PrepaymentRechargeModification.vue" title="预付款充值-审批、修改" extension="" presentableText="" tooltipTitle="" icon="" textColor=""
		  backgroundColor=""/>
    <tree path="/src/api/carrierEnd/paymentOrderApproval.js" title="付款单审批、修改"/>
    <tree path="/src/views/logisticsConfiguration/InvoiceApplication.vue" title="开票申请"/>
    <tree path="/src/views/carrierFunction/InvoiceApproval.vue" title="开票审批"/>
    <tree path="/src/api/shipperEnd/InvoiceApplication.js" title="开票申请"/>
	<tree path="/src/views/carrierFunction/PrepaymentRechargeModificationInvoicingDetail.vue" title="开票详情-预付款"/>
	<tree path="/src/views/carrierFunction/PaymentOrderApprovalInvoicingDetail.vue" title="开票详情-付款单"/>
	<tree path="/src/api/carrierEnd/InvoiceApproval.js" title="开票审批"/>
    <tree path="/src/views/carrierFunction/MonthlyStatisticsOfLogisticsCosts.vue" title="物流费用月度统计"/>
	<tree path="/src/api/carrierEnd/monthlyStatisticsOfLogisticsCosts.js" title="物流费用月度统计"/>
	<tree path="/src/views/carrierFunction/MonthlyStatisticsOnPaymentStatus.vue" title="收款情况月度统计"/>
	<tree path="/src/api/carrierEnd/monthlyStatisticsOnPaymentStatus.js" title="收款情况月度统计"/>
	<tree path="/src/views/logisticsManagement/orderTransaction/OrderTransactionForm.vue" title=""/>
    <tree path="/src/views/logisticsManagement/orderManagement/StorageOrder.vue" title="仓储订单"/>
    <tree path="/src/views/logisticsConfiguration" title="物流配置"/>
    <tree path="/src/views/logisticsConfiguration/operationConfiguration" title="运营配置"/>
    <tree path="/src/views/logisticsConfiguration/operationConfiguration/CarType.vue" title="车辆类型" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/operationConfiguration/CarManagement.vue" title="车辆管理" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/operationConfiguration/DriverManagement.vue" title="司机管理" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/serviceNetwork/DistributionBranch.vue" title="集散网点" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/serviceNetwork/CollectionArea.vue" title="集货区域" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/serviceNetwork" title="服务网点"/>
    <tree path="/src/views/logisticsConfiguration/serviceNetwork/collectArea.vue" title="揽收区域" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/serviceNetwork/LandAllocationArea.vue" title="地配区域"
          extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/serviceNetwork/TripartiteLogistics.vue" title="三方物流" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/otherConfiguration" title="其他配置"/>
    <tree path="/src/views/logisticsConfiguration/serviceNetwork/CollectArea.vue" title="揽收区域" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/enterpriseCooperation" title="企业合作管理"/>
    <tree path="/src/views/logisticsConfiguration/enterpriseCooperation/CooperativeCargoOwners.vue" title="合作货主"
          extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/otherConfiguration/PaymentAllocation.vue" title="支付配置"
          extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/otherConfiguration/ThermosphereManagement.vue" title="温层配置"
          extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/customerPrepayment" title="客户预付款"/>
    <tree path="/src/views/logisticsConfiguration/customerPrepayment/CustomerPrepaymentBalance.vue" title="客户预付款"
          extension="vue" presentableText="" tooltipTitle="" icon="" textColor="" backgroundColor=""/>
    <tree path="/src/views/logisticsConfiguration/customerPrepayment/CustomerPrepaymentBalanceDetail.vue" title="交易明细"
          extension="vue"/>
    <tree path="/src/views/logisticsManagement" title="物流管理"/>
    <tree path="/src/views/logisticsManagement/orderManagement/PlaceAnOrder.vue" title="下单" extension="vue"/>
    <tree path="/src/views/logisticsManagement/orderManagement/OrderList.vue" title="订单列表" extension="vue"/>
    <tree path="/src/views/orderComponents/ConfirmAnOrder.vue" title="确认订单" extension="vue"/>
    <tree path="/src/api/logisticsConfiguration" title="物流配置"/>
    <tree path="/src/api/logisticsConfiguration/customerPrepayment.js" title="客户预存款" extension="js"/>
    <tree path="/src/api/logisticsConfiguration/enterpriseCooperation.js" title="企业合作" extension="js"/>
    <tree path="/src/api/logisticsConfiguration/operationConfiguration.js" title="运营配置" extension="js"/>
    <tree path="/src/api/logisticsConfiguration/otherConfiguration.js" title="其他配置" extension="js"/>
    <tree path="/src/api/logisticsConfiguration/serviceNetwork.js" title="服务网点" extension="js"/>
    <tree path="/src/api/logisticsManagement" title="物流管理"/>
    <tree path="/src/api/logisticsManagement/orderManagement.js" title="订单管理" extension="js"/>
    <tree path="/src/views/logisticsManagement/orderManagement/transferCustomer.vue" title="客户签收" extension="vue"/>
    <tree path="/src/views/orderComponents/OrderDetailTrans.vue" title="订单详情" extension="vue"/>
    <tree path="/src/views/logisticsManagement/orderManagement/TransferCustomer.vue" title="签收" extension="vue"/>
    <tree path="/src/views/orderComponents/PrintBoxTag.vue" title="打印箱签" extension="vue"/>
    <tree path="/src/views/orderComponents/OrderDetails" title="订单明细"/>
    <tree path="/src/views/logisticsManagement/orderManagement" title="订单管理"/>
    <tree path="/src/views/logisticsManagement/orderTransaction" title="订单异动"/>
    <tree path="/src/views/logisticsManagement/orderTransaction/OrderTransactionList.vue" title="订单异动列表"
          extension="vue"/>
    <tree path="/src/views/shipperEnd/ownersOrderList" title="货主端订单管理"/>
    <tree path="/src/views/shipperEnd" title="货主端"/>
    <tree path="/src/views/logisticsManagement/waybillTracking" title="运单跟踪"/>
    <tree path="/src/views/logisticsManagement/waybillTracking/WaybillTrackingDetails.vue" title="操作记录"
          extension="vue"/>
    <tree path="/src/views/logisticsManagement/waybillTracking/TrackingListOfWaybills.vue" title="运单跟踪"
          extension="vue"/>
    <tree path="/src/views/logisticsManagement/waybillTracking/TransportRecord.vue" title="运输记录" extension="vue"/>
    <tree path="/src/views/orderComponents" title="订单相关组件"/>
    <tree path="/src/views/orderComponents/OrderTransactionDetails.vue" title="订单异动记录" extension="vue"/>
    <tree path="/src/views/shipperEnd/ownersOrderList/ModifyOrder.vue" title="修改订单" extension="vue"/>
    <tree path="/src/views/orderComponents/TemperatureRecording.vue" title="温度记录" extension="vue"/>
    <tree path="/src/views/carrierFunction" title="承运端功能"/>
    <tree path="/src/views/carrierFunction/cabineBoxRecord.vue" title="保温箱记录" extension="vue"/>
    <tree path="/src/views/carrierFunction/packingDetails.vue" title="装箱详情" extension="vue"/>
    <tree path="/src/views/hzAddress" title="货主地址簿"/>
    <tree path="/src/views/logisticsManagement/orderManagement/SignAudit.vue" title="签收审核" extension="vue"/>
    <tree path="/src/views/orderComponents/OrderModificationDetails.vue" title="订单修改明细" extension="vue"/>
    <tree path="/src/views/logisticsManagement/outboundManagement" title="出库管理"/>
    <tree path="/src/views/logisticsManagement/outboundManagement/WarehouseOutSelfLifting.vue" title="出库登记"
          extension="vue"/>
    <tree path="/src/views/logisticsManagement/outboundManagement/WarehouseRecords.vue" title="出库记录" extension="vue"/>
    <tree path="/src/views/logisticsManagement/outboundManagement/WarehouseDetail.vue" title="登记详情" extension="vue"/>
    <tree path="/src/views/logisticsManagement/orderManagement/collectOrder.vue" title="收藏订单" extension="vue"/>
    <tree path="/src/components/AdvertisingComponent" title="广告"/>
    <tree path="/src/views/logisticsConfiguration/prepaymentFromTheShipper" title="货主预付款"/>
    <tree path="/src/views/platformConfiguration" title=""/>
    <tree path="/src/views/carrierFunction/BillingRecord.vue" title="百望云开票记录" extension="vue"/>
    <tree path="/src/views/carrierFunction/BaiwangCloudBillingRecord.vue" title="百望云开票记录" extension="vue"/>
    <tree path="/src/views/carrierFunction/PrepaymentCollectionInvoicingStatistics.vue" title="预付款收款开票统计"
          extension="vue"/>
    <tree path="/src/api/carrierEnd/dataQuery.js" title="数据查询" extension="js"/>
    <tree path="/src/views/carrierFunction/companyAddressBook.vue" title="承运端地址簿" extension="vue"/>
    <tree path="/src/views/carrierFunction/ValueAddedServices.vue" extension="vue"/>
    <tree path="/src/views/carrierFunction/HandoverOrderConfiguration.vue" title="交接单配置" extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/operationConfiguration/DriverConfiguration.vue" title="司机配置"
          extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/operationConfiguration/TripartiteDriverArrangement.vue" title="三方司机配置"
          extension="vue"/>
    <tree path="/src/api/carrierEnd/handoverOrderConfiguration.js" title="冷链交接单配置" extension="js"/>
    <tree path="/src/views/auditManagement" title="审批管理"/>
    <tree path="/src/api/auditManagement" title="审批流"/>
    <tree path="/src/views/auditManagement/AuditAndFlow.vue" title="系统审批流配置" extension="vue"/>
    <tree path="/src/views/auditManagement/CarrierAuditAndFlow.vue" title="组织审批流配置" extension="vue"/>
    <tree path="/src/views/auditManagement/approvalTaskList.vue" title="审批任务列表" extension="vue"/>
    <tree path="/src/views/auditManagement/PreDepositTopUpAudit.vue" title="预存款充值审批" extension="vue" presentableText="" tooltipTitle="" icon="" textColor="" backgroundColor=""/>
    <tree path="/src/views/auditManagement/PaymentOrderAudit.vue" title="付款单支付" extension="vue"/>
    <tree path="/src/views/carrierFunction/receiveThePayment" title="到款记录到款认款"/>
    <tree path="/src/views/carrierFunction/receiveThePayment/ReceiptRecords.vue" title="到款记录" extension="vue"/>
    <tree path="/src/views/carrierFunction/receiveThePayment/ReceiptOfPayment.vue" title="到款认款" extension="vue"/>
    <tree path="/src/components/operationRecord" title="操作记录"/>
    <tree path="/src/components/approvalProgress" title="审批进度"/>
    <tree path="/src/views/carrierFunction/receiveThePayment/components/SubscriptionApply.vue" title="认款申请"
          extension="vue"/>
    <tree path="/src/views/auditManagement/ReceiptApplicationAudit.vue" title="认款审批" extension="vue"/>
    <tree path="/src/components/PaymentSubmission.vue" title="提交付款信息"/>
    <tree path="/src/views/auditManagement/AuditAndFlowRecords.vue" title="操作记录"/>
    <tree path="/src/components/InvoiceDrawer.vue" title="开票申请组件"/>
    <tree path="/src/components/theFileUpload" title="文件上传"/>
    <tree path="/src/components/thePreviewDialog" title="文件预览"/>
    <tree path="/src/views/carrierFunction/InvoiceRedInvoicingDetail.vue" title="红冲发票申请弹窗"/>
	<tree path="/src/views/carrierFunction/RedLetterInvoiceApplication.vue" title="红字发票申请"/>
	<tree path="/src/api/carrierEnd/redLetterInvoiceApplication.js" title="红字发票申请"/>
	<tree path="/src/views/carrierFunction/ApprovalRecord.vue" title="审批记录弹窗"/>
    <tree path="/src/components/theFileUpload" title="文件上传"/>
    <tree path="/src/components/thePreviewDialog" title="文件预览"/>
    <tree path="/src/views/auditManagement/RedPunchApproval.vue" title="红冲审批"/>
    <tree path="/src/components/MismatchDialog.vue" title="汇款金额与收款单金额不匹配指定"/>
    <tree path="/src/views/logisticsConfiguration/operationConfiguration/CarSensor.vue" title="车辆传感器"/>
    <tree path="/src/components/ApplicationCompanyInquiry.vue" title="申请公司查询弹窗"/>
    <tree path="/src/views/carrierFunction/ShipperValueAddedServices.vue" title="货主增值服务"/>
    <tree path="/src/views/management" title="设备管理"/>
    <tree path="/src/views/logisticsConfiguration/otherConfiguration/ThreeWayLogisticsConfiguration.vue" title="三方物流配置"/>
	<tree path="/src/api/logisticsConfiguration/threeWayLogisticsConfiguration.js" title="三方物流配置"/>
    <tree path="/src/components/SelectReceipt.vue" title="选择收款单"/>
    <tree path="/src/views/carrierFunction/InvoiceTypeManage.vue" title="货主发票信息维护"/>
	<tree path="/src/api/carrierEnd/invoiceTypeManage.js" title="货主发票信息维护"/>
    <tree path="/src/views/carrierFunction/receiveThePayment/ReceiptSummary.vue" title="认款汇总"/>
    <tree path="/src/utils/config-store.js" title="配置"/>
</trees>
