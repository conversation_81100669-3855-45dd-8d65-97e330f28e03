/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-06 15:41:20
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-06-07 11:16:24
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\utils\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */
import printLabel from '@/api/print/printLabelView';
import areas from '@/assets/areas/areas.json';

/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
    if (cellValue == null || cellValue == '') return '';
    var date = new Date(cellValue);
    var year = date.getFullYear();
    var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}
// 表单重置
export function resetForm(refName) {
    if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
    }
}
export function deepClone(obj) {
    if (typeof obj !== 'object' || obj == null) {
        return obj;
    }
    let res;
    if (obj instanceof Array) {
        res = [];
    } else {
        res = {};
    }
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            res[key] = deepClone(obj[key]);
        }
    }
    return res;
}
export function download(data, type, fileName) {
    let blob = new Blob([data.data], { type });
    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a'); // 创建a标签
    link.href = url;

    // 尝试从 content-disposition 头获取文件名
    let finalFileName = fileName;
    if (data?.headers && data.headers['content-disposition']) {
        const parsedFileName = parseContentDispositionFilename(data.headers['content-disposition']);
        if (parsedFileName) {
            finalFileName = parsedFileName;
        }
    }

    link.download = finalFileName;
    link.click();
    URL.revokeObjectURL(url); // 释放内存
}

/**
 * 解析 content-disposition 头中的文件名
 * @param {string} contentDisposition - content-disposition 头的值
 * @returns {string|null} - 解析出的文件名，如果解析失败返回 null
 */
function parseContentDispositionFilename(contentDisposition) {
    if (!contentDisposition) return null;

    // 处理 filename*=utf-8''encoded_filename 格式
    const filenameStarMatch = contentDisposition.match(/filename\*=utf-8''([^;]+)/i);
    if (filenameStarMatch) {
        try {
            return decodeURIComponent(filenameStarMatch[1]);
        } catch (e) {
            console.warn('Failed to decode filename from content-disposition:', e);
        }
    }

    // 处理 filename="filename" 或 filename=filename 格式
    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/i);
    if (filenameMatch) {
        let filename = filenameMatch[1];
        // 移除引号
        if (filename.startsWith('"') && filename.endsWith('"')) {
            filename = filename.slice(1, -1);
        }
        try {
            return decodeURIComponent(filename);
        } catch (e) {
            return filename;
        }
    }

    return null;
}

/**
 * 下载文件
 * @param data 文件流数据或包含 headers 的响应对象
 * @param type MIME 类型
 * @param fileName 默认文件名
 */
export function downloadNoData(data, type, fileName) {
    // 如果 data 是响应对象，提取文件流和 headers
    let fileData = data;
    let headers = null;

    if (data && typeof data === 'object' && data.headers) {
        fileData = data.data || data;
        headers = data.headers;
    }

    let blob = new Blob([fileData], { type });
    let url = window.URL.createObjectURL(blob);
    const link = document.createElement('a'); // 创建a标签
    link.href = url;

    // 尝试从 content-disposition 头获取文件名
    let finalFileName = fileName;
    if (headers && headers['content-disposition']) {
        const parsedFileName = parseContentDispositionFilename(headers['content-disposition']);
        if (parsedFileName) {
            finalFileName = parsedFileName;
        }
    }

    link.download = finalFileName;
    link.click();
    URL.revokeObjectURL(url); // 释放内存
}

/**
 * @param {Function} func  防抖的回调
 * @param {number} wait  等待时间
 * @param {boolean} immediate   是否立即执行
 * @return {*}
 * 防抖函数
 */
export function debounce(func, wait, immediate) {
    let timeout, args, context, timestamp, result;

    const later = function () {
        // 据上一次触发时间间隔
        const last = +new Date() - timestamp;

        // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
        if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last);
        } else {
            timeout = null;
            // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
            if (!immediate) {
                result = func.apply(context, args);
                if (!timeout) context = args = null;
            }
        }
    };

    return function (...args) {
        context = this;
        timestamp = +new Date();
        const callNow = immediate && !timeout;
        // 如果延时不存在，重新设定延时
        if (!timeout) timeout = setTimeout(later, wait);
        if (callNow) {
            result = func.apply(context, args);
            context = args = null;
        }

        return result;
    };
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, groupId, children) {
    let config = {
        id: id || 'id',
        parentId: groupId || 'parentId',
        childrenList: children || 'children'
    };

    var childrenListMap = {};
    var nodeIds = {};
    var tree = [];

    for (let d of data) {
        let parentId = d[config.parentId];
        if (childrenListMap[parentId] == null) {
            childrenListMap[parentId] = [];
        }
        nodeIds[d[config.id]] = d;
        childrenListMap[parentId].push(d);
    }

    for (let d of data) {
        let parentId = d[config.parentId];
        if (nodeIds[parentId] == null) {
            tree.push(d);
        }
    }
    for (let t of tree) {
        adaptToChildrenList(t);
    }

    function adaptToChildrenList(o) {
        if (childrenListMap[o[config.id]] !== null) {
            o[config.childrenList] = childrenListMap[o[config.id]];
        }
        if (o[config.childrenList]) {
            for (let c of o[config.childrenList]) {
                adaptToChildrenList(c);
            }
        }
    }
    return tree;
}

/**
 * pdf预览
 * @param templateId
 * @param ids
 */
export function pdfLabelView(templateId, ids) {
    printLabel
        .labelView({
            templateId: templateId,
            params: ids.toString()
        })
        .then((res) => {
            const binaryData = [];
            binaryData.push(res);
            //获取blob链接
            let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
            window.open(pdfUrl);
        });
}

/**
 * pdf箱码与付款方式预览
 * @param templateId
 * @param ids
 */
export function printPdfLabelViewNew(data) {
    printLabel.printLabelViewNew(data).then((res) => {
        const binaryData = [];
        binaryData.push(res);
        //获取blob链接
        let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
        window.open(pdfUrl);
    });
}

/**
 * 打印
 * @param templateId
 */
export function printLabelView(data) {
    printLabel.printLabel(data).then((res) => {
        const binaryData = [];
        binaryData.push(res);
        //获取blob链接
        let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
        window.open(pdfUrl);
    });
}

/**
 * 前端导入excel、xlsx文件日期被转为数字问题处理
 * @param numb
 * @param format
 * @returns {*}
 */
export function formatDateToExcel(numb, format) {
    const old = numb - 1;
    const t = Math.round((old - Math.floor(old)) * 24 * 60 * 60);
    const time = new Date(1900, 0, old, 0, 0, t);
    const year = time.getFullYear();
    const month = time.getMonth() + 1;
    const date = time.getDate();
    return year + format + (month < 10 ? '0' + month : month) + format + (date < 10 ? '0' + date : date);
}

/**
 * 冷链明细验证
 * @param data
 */
export function coldChainDetailVerification(data) {
    const fields = {
        name: '通用名称',
        specifications: '规格/型号',
        manufacturer: '生产厂家',
        basicUnit: '单位',
        quantity: '数量',
        batchNumber: '批号/序列号',
        registrationNumber: '批准文号/注册证号/备案证号'
    };
    let msg = null;
    try {
        data.forEach((item, index) => {
            // 循环对象fields
            Object.keys(fields).forEach((field) => {
                if (item[field] == null || item[field] == '') {
                    msg = '第' + (index + 1) + '条冷链明细【' + fields[field] + '】不能为空';
                    throw 'error';
                }
            });
        });
    } catch (e) {}
    return msg;
}

/**
 * 递归查询值是否存在
 * @param data
 * @param value
 * @param key
 * @param children
 * @returns {boolean}
 */
export function recursivelyQueryWhetherValueExists(data, value, key = 'id', children = 'children') {
    if (data && data.length > 0) {
        for (const item of data) {
            if (item[key] === value) {
                return true;
            }
            if (item[children] && item[children].length > 0) {
                const flag = recursivelyQueryWhetherValueExists(item[children], value, key);
                if (flag) {
                    return true;
                }
            }
        }
    }
    return false;
}

/**
 * 校验地址是否一致
 * @param {string} detailAddress - 详细地址
 * @param {Array} regionalAddress - 省市区街道数组 [省,市,区,街道]
 * @returns {boolean} - 地址是否一致
 */
export function verifyThatTheAddressesAreConsistent(detailAddress, regionalAddress = []) {
    if (!detailAddress || !Array.isArray(regionalAddress) || regionalAddress.length === 0) {
        return true;
    }

    // 移除地址中的空格
    detailAddress = detailAddress.replace(/\s+/g, '');
    // 移除特殊符号
    detailAddress = detailAddress.replace(/[\\\/:"*?<>|=#%\s]+/g, '');
    // 构建地址正则表达式
    const addressPattern = /^(.*?(省|自治区|市))(.*?(市|自治州|地区|盟))(.*?(经济技术开发区|开发区|自治县|区|县|市|旗))?/;

    const match = detailAddress.match(addressPattern);
    // 1.有输入省市区的情况
    if (match && match.length > 0) {
        // 提取匹配的地址部分
        const matchedAddress = {
            province: match[1] || '',
            city: match[3] || '',
            district: match[5] || ''
        };
        // 根据regionalAddress的长度进行不同级别的匹配
        switch (regionalAddress.length) {
            case 3: // 省市区
                return matchedAddress.province === regionalAddress[0] && matchedAddress.city === regionalAddress[1] && matchedAddress.district === regionalAddress[2];
            case 2: // 省市
                return matchedAddress.province === regionalAddress[0] && matchedAddress.city === regionalAddress[1];
            case 1: // 省
                return matchedAddress.province === regionalAddress[0];
            default:
                return false;
        }
    }

    // 没有输入省市区的情况，用regionalAddress中的省市区街道去掉关键字在字符串detailAddress中匹配
    // 根据层级获取对应的areas数据
    let currentAreas = areas;
    let found = false;
    let detailArr = [];
    // 遍历areas数据查找匹配
    currentAreas.forEach((province, x) => {
        const p = province.label.replace(/(省|自治区|直辖市|市)/g, '');
        if (detailAddress.indexOf(p) > -1) {
            detailArr.push(province.label);
            province.children.forEach((city, y) => {
                const c = city.label.replace(/(市|自治州|地区|盟)/g, '');
                if (detailAddress.indexOf(c) > -1) {
                    detailArr.push(city.label);
                    city.children.forEach((county, x) => {
                        const c2 = county.label.replace(/(经济技术开发区|开发区|自治县|区|县|市|旗)/g, '');
                        if (detailAddress.indexOf(c2) > -1) {
                            detailArr.push(county.label);
                        }
                    });
                }
            });
        }
    });
    if (detailArr.length > 0) {
        switch (detailArr.length) {
            case 3: // 省市区
                return detailArr[0] === regionalAddress[0] && detailArr[1] === regionalAddress[1] && detailArr[2] === regionalAddress[2];
            case 2: // 省市
                return detailArr[0] === regionalAddress[0] && detailArr[1] === regionalAddress[1];
            case 1: // 省
                return detailArr[0] === regionalAddress[0];
            default:
                return false;
        }
    }

    return true;
}
/**
 * 获取完整域名
 * @returns {string}
 */
export function  getterFullDomainName () {
    try {
        const url = new URL(window.location.href);
        return `${url.protocol}//${url.host}`;
    } catch (error) {
        // 如果URL解析失败，使用备用方案
        const href = window.location.href;
        const protocolMatch = href.match(/^https?:\/\//);
        const protocol = protocolMatch ? protocolMatch[0] : 'http://';
        const host = window.location.host || window.location.hostname;
        return `${protocol}${host}`;
    }
}
