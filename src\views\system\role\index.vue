<template>
  <el-container>
    <el-aside width="300px" v-if="true">
      <el-container v-loading="leftListLoading">
        <el-header>
          <el-input
            placeholder="输入关键字进行过滤"
            v-model="leftName"
            clearable @keyup.enter="getleftDataList" @clear="getleftDataList"
          ></el-input>
        </el-header>
        <el-main class="nopadding">
          <ytzhTable
            ref="dataTable"
            :data="leftDataList"
            row-key="id"
            size="small"
            stripe
            highlight-current-row
            @current-change="leftHandleCurrentChange"
            :tablePage="leftTablePage"
            :pageChangeHandle="getleftDataList"
            :refreshDataListHandle="getleftDataList"
            :treeLoadHandle="leftTreeLoadHandle"
            paginationLayout="total, sizes, prev, pager, next"
            hideDo="false"
            style="cursor: pointer"
          >
            <el-table-column label="机构名称" prop="name"></el-table-column>
            <el-table-column label="是否启用" prop="enable" width="80">
              <template #default="scope">
                <el-tag v-if="scope.row.enable == true" type="success">启用</el-tag>
                <el-tag v-if="scope.row.enable == false">停用</el-tag>
              </template>
            </el-table-column>
          </ytzhTable>
        </el-main>
      </el-container>
    </el-aside>
    <el-container class="is-vertical" v-loading="listLoading">
      <el-header>
        <div class="left-panel">
          <el-button type="primary" icon="el-icon-plus" @click="addForm"></el-button>
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            :disabled="selection.length == 0"
            @click="deleteDatas"
          ></el-button>
          <el-input
              v-model="roleName" style="margin-left: 20px;"
              clearable @keyup.enter="getDataList" @clear="getDataList"
              placeholder="输入角色名称进行过滤"
          ></el-input>
        </div>
      </el-header>
      <el-main class="nopadding">
        <ytzhTable
          ref="dataTable"
          :data="dataList"
          row-key="id"
          @selection-change="selectionChange"
          stripe
          :tablePage="tablePage"
          :pageChangeHandle="getDataList"
          :refreshDataListHandle="getDataList"
        >
          <el-table-column type="selection" width="50"></el-table-column>
          <el-table-column label="角色名称" prop="name" minWidth="150"></el-table-column>
          <el-table-column label="英文标识" prop="enName" minWidth="150"></el-table-column>
          <el-table-column label="角色类型" prop="roleType">
            <template #default="scope">
               {{ formateRole(scope.row.roleType) }}
              </template>
          </el-table-column>
          <el-table-column label="系统数据" prop="sys" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.sys == true" type="success">是</el-tag>
              <el-tag v-if="scope.row.sys == false">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="共享角色" prop="common" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.common == true" type="success">是</el-tag>
              <el-tag v-if="scope.row.common == false">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否可用" prop="useable" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.useable == true" type="success">可用</el-tag>
              <el-tag v-if="scope.row.useable == false">不可用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="所在机构" prop="sysOrg.name" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" fixed="right" align="right" width="300">
            <template #default="scope">
              <el-button plain size="small" @click="viewForm(scope.row)">查看</el-button>
              <el-button type="primary" plain size="small" @click="editForm(scope.row)"
                >编辑</el-button
              >
              <el-button plain size="small" @click="openSysMenuDialog(scope.row)"
                >角色授权</el-button
              >
              <el-popconfirm
                title="确定删除吗？"
                @confirm="deleteData(scope.row, scope.$index)"
              >
                <template #reference>
                  <el-button plain type="danger" size="small">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </ytzhTable>
      </el-main>
    </el-container>
  </el-container>
  <form-dialog
    v-if="formDiaogDisplay"
    ref="formDialog"
    :callback="formCallback"
    @success="handleSaveSuccess"
    @closed="formDiaogDisplay = false"
  ></form-dialog>
  <sysMenuSelect
    ref="sysMenuSelected"
    v-if="sysMenuSelectDialog"
    :isMultiple="false"
    draggable
    @closed="sysMenuSelectDialog = false"
    :selectChange="menuSelectChange"
  ></sysMenuSelect>
</template>

<script>
import formDialog from "./form";

export default {
  components: {
    formDialog,
  },
  data() {
    return {
      //数据列表
      leftDataList: {},
      // 角色类型
      roleTypeList: [],
      //分页参数
      leftTablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
      },
      //查询表单
      leftSearchForm: {},
      //列表加载
      leftListLoading: false,
      //选中的组织机构数据
      leftSelectData: {},
      //////////////////////////////////////////////////////////////////////////////////////
      //数据列表
      dataList: {},
      //分页参数
      tablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
        //排序
        //orders: [{ column: "createDate", asc: false }],
      },
      //查询表单
      searchForm: {
        name: "",
        url: "",
      },
      roleName:'',
      //数据列选中行
      selection: [],
      leftName:'',
      //列表加载
      listLoading: false,
      //表单显示隐藏
      formDiaogDisplay: false,
      //
      sysMenuSelectDialog: false,
    };
  },
  mounted() {
    //刷新数据列表
    this.getleftDataList();
    this.getDict();
  },
  methods: {
    async getDict() {
      // 角色类型
      this.roleTypeList = await this.getDictList('role_type');
    },
    formateRole(val){
      return this.selectDictLabel(this.roleTypeList, val);
    },
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getleftDataList() {
      //初始化数据列表
      this.leftDataList = [];
      //请求接口
      var res = await this.getReqeustList({name:this.leftName});
      if (res.code == 200) {
        //总数据条数
        this.leftTablePage.total = res.data.total;
        //数据列表
        this.leftDataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
    },
    /*
     * 列表请求接口
     * @author: 路正宁
     * @date: 2023-03-30 11:20:01
     */
    async getReqeustList(form={},pageSize = this.leftTablePage.pageSize) {
      //页面加载
      this.leftListLoading = true;
      var res = await this.$API.sysOrgService.list({
        //当前页码
        current: this.leftTablePage.currentPage,
        //每页条数
        size: pageSize,
        //排序查询
        orders: this.leftTablePage.orders,
        //查询参数
        ...form,
      });
      this.leftListLoading = false;
      return res;
    },
    /*
     * 表格选择后回调事件，单选
     * @author: 路正宁
     * @date: 2023-03-31 17:38:42
     */
    leftHandleCurrentChange(val) {
      this.leftSelectData = val;
      this.getDataList();
    },
    /*
     * 头部搜索框
     * @author: 路正宁
     * @date: 2023-03-24 14:58:47
     */
    leftSearch() {
      this.getleftDataList();
    },
    /*
     * 树表格点击展开
     * @author: 路正宁
     * @date: 2023-03-30 11:16:33
     */
    async leftTreeLoadHandle(tree, treeNode, resolve) {
      //查询当前节点下的子节点
      var form = {
        "parent.id": tree.id,
      };
      //请求接口
      var res = await this.getReqeustList(form,100);
      if (res.code == 200) {
        if (res.data.records == null || res.data.records.length == 0) {
          this.$message.warning("无数据");
        } else {
          //同步本地
          tree.children = res.data.records;
        }
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      resolve(res.data.records);
    },
    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      //页面加载
      this.listLoading = true;
      this.dataList = null;
      //查询条件处理
      if (this.$ObjectUtils.isNotEmpty(this.leftSelectData)) {
        this.searchForm = {
          "sysOrg.id": this.leftSelectData.id,
        };
      }
      var res = await this.$API.sysRoleService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: this.tablePage.pageSize,
        //排序查询
        orders: this.tablePage.orders,
        ...this.searchForm,
        name:this.roleName
      });
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        //数据列表
        this.dataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      this.listLoading = false;
    },
    /*
     * 添加数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:27
     */
    addForm() {
      if (this.$ObjectUtils.isEmpty(this.leftSelectData)) {
        this.$message.warning("请选择左侧组织机构");
        return;
      }
      this.formDiaogDisplay = true;
      this.$nextTick(() => {
        this.$refs.formDialog.addView(this.leftSelectData);
      });
    },
    /*
     * 编辑数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:41
     */
    editForm(row) {
      this.formDiaogDisplay = true;
      this.$nextTick(() => {
        this.$refs.formDialog.editView(row);
      });
    },
    /*
     * 查看数据
     * @author: 路正宁
     * @date: 2023-03-24 14:32:55
     */
    viewForm(row) {
      this.formDiaogDisplay = true;
      this.$nextTick(() => {
        this.$refs.formDialog.view(row);
      });
    },
    /*
     * 删除数据，行内删除
     * @author: 路正宁
     * @date: 2023-03-24 14:35:00
     */
    async deleteData(row, index) {
      this.listLoading = true;
      var res = await this.$API.sysRoleService.delete(row.id);
      if (res.code == 200) {
        this.$refs.dataTable.removeIndex(index);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      this.listLoading = false;
    },
    /*
     * 批量删除
     * @author: 路正宁
     * @date: 2023-03-24 14:36:11
     */
    async deleteDatas() {
      //确认删除弹框
      var confirmRes = await this.$confirm(
        `确定删除选中的 ${this.selection.length} 项吗？`,
        "提示",
        {
          type: "warning",
          confirmButtonText: "删除",
          confirmButtonClass: "el-button--danger",
        }
      ).catch(() => {});
      //确认结果处理
      if (!confirmRes) {
        return false;
      }
      //要删除的id数组
      var ids = this.selection.map((v) => v.id);
      //拼接的数组字符串，接口传参
      var idStr = this.selection.map((v) => v.id).join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysRoleService.delete(idStr);
      if (res.code == 200) {
        //从列表中移除已删除的数据
        this.$refs.dataTable.removeKeys(ids);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      //释放页面加载中
      this.listLoading = false;
    },
    /*
     * 表格选择后回调事件
     * @author: 路正宁
     * @date: 2023-03-24 14:51:09
     */
    selectionChange(selection) {
      this.selection = selection;
    },
    /*
     * 数据表单回调函数，表单提交成功以后会调用此方法
     * 为了减少网络请求，直接变更表格内存数据
     * @author: 路正宁
     * @date: 2023-03-24 14:57:49
     */
    formCallback(data, mode) {
      if (mode == "add") {
        this.$refs.dataTable.unshiftRow(data);
      } else if (mode == "edit") {
        this.$refs.dataTable.updateKey(data);
      }
    },
    /*
     * 头部搜索框
     * @author: 路正宁
     * @date: 2023-03-24 14:58:47
     */
    search() {
      this.getDataList();
    },
    /*
     * 打开系统菜单选择框
     * @author: 路正宁
     * @date: 2023-04-03 10:27:55
     */
    async openSysMenuDialog(row) {
      //页面加载
      this.listLoading = true;
      var res = await this.$API.sysRoleMenuService.list({
        "sysRole.id": row.id,
        //当前页码
        current: 1,
        //每页条数
        size: 1000,
      });
      var menuIds = [];
      if (res.code == 200) {
        for (var i = 0; i < res.data.records.length; i++) {
          menuIds[i] = res.data.records[i].sysMenu.id;
        }
      } else {
        this.$Response.errorNotice(res, "查询失败");
        return;
      }
      this.listLoading = false;
      //打开系统菜单弹框
      this.sysMenuSelectDialog = true;
      this.$nextTick(() => {
        this.$refs.sysMenuSelected.selecteds(row, menuIds);
      });
    },
    /*
     * 系统菜单弹框回调事件
     * @author: 路正宁
     * @date: 2023-04-03 10:02:56
     */
    async menuSelectChange(row, menus) {
      var roleId = row.id;
      var menuIds = menus.join(",");
      //页面加载
      this.listLoading = true;
      var res = await this.$API.sysRoleMenuService.configMenu(roleId, menuIds);
      if (res.code == 200) {
        this.$message.success("配置成功");
      } else {
        this.$Response.errorNotice(res, "配置失败");
      }
      this.listLoading = false;
    },
  },
};
</script>

<style></style>
