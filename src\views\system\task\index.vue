<!--
 * @Descripttion: 系统计划任务配置
 * @version: 1.2
 * @Author: sakuya
 * @Date: 2021年7月7日09:28:32
 * @LastEditors: sakuya
 * @LastEditTime: 2021年7月10日20:56:47
-->

<template>
    <el-main v-loading="listLoading">
        <el-row :gutter="15">
            <el-col v-for="item in list" :key="item.id" :lg="6" :md="8" :sm="12" :xl="6" :xs="24">
                <el-card class="task task-item" shadow="hover">
                    <h2>{{ item.name }}</h2>
                    <ul>
                        <li>
                            <h4>执行类</h4>
                            <p>{{ item.excuteClass }}</p>
                        </li>
                        <li>
                            <h4>定时规则</h4>
                            <p>{{ item.timingRules }}</p>
                        </li>
                    </ul>
                    <div class="bottom">
                        <div class="state">
                            <el-tag v-if="item.isEnable == '1'" size="small">准备就绪</el-tag>
                            <el-tag v-if="item.isEnable == '0'" size="small" type="info">停用</el-tag>
                        </div>
                        <div class="handler">
                            <el-popconfirm title="确定立即执行吗？" @confirm="run(item)">
                                <template #reference>
                                    <el-button circle icon="el-icon-caret-right" type="primary"></el-button>
                                </template>
                            </el-popconfirm>
                            <el-dropdown trigger="click">
                                <el-button circle icon="el-icon-more" plain type="primary"></el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="edit(item)">编辑</el-dropdown-item>
                                        <el-dropdown-item @click="logs(item)">日志</el-dropdown-item>
                                        <el-dropdown-item divided @click="del(item)">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :lg="6" :md="8" :sm="12" :xl="6" :xs="24">
                <el-card class="task task-add" shadow="never" @click="add">
                    <el-icon><el-icon-plus /></el-icon>
                    <p>添加计划任务</p>
                </el-card>
            </el-col>
        </el-row>
    </el-main>

    <save-dialog v-if="dialog.save" ref="saveDialog" style="width: 800px" @closed="dialog.save = false" @success="handleSuccess"></save-dialog>

    <el-drawer v-model="dialog.logsVisible" :size="600" destroy-on-close direction="rtl" title="计划任务日志">
        <logs></logs>
    </el-drawer>
</template>

<script>
import saveDialog from './save';
import logs from './logs';
import planTasks from '@/api/platformFeatures/planTasks';

export default {
    name: 'task',
    components: {
        saveDialog,
        logs
    },
    provide() {
        return {
            list: this.list
        };
    },
    data() {
        return {
            dialog: {
                save: false,
                logsVisible: false
            },
            list: [],
            listLoading: false
        };
    },
    mounted() {
        this.getDataList();
    },
    methods: {
        /*
         * 获取计划任务列表
         * @author: 路正宁
         * @date: 2023-03-29 11:27:54
         */
        async getDataList() {
            //页面加载
            this.listLoading = true;
            this.list = null;
            var res = await this.$API.sysTaskService.list({
                //当前页码
                current: 1,
                //每页条数
                size: 1000
            });
            if (res.code == 200) {
                //数据列表
                this.list = res.data.records;
            } else {
                this.$Response.errorNotice(res, '查询失败');
            }
            this.listLoading = false;
        },
        /*
         * 添加任务计划
         * @author: 路正宁
         * @date: 2023-03-29 11:29:56
         */
        add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.addView();
            });
        },
        /*
         * 编辑任务计划
         * @author: 路正宁
         * @date: 2023-03-29 11:30:44
         */
        edit(task) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.editView(task);
            });
        },
        /*
         * 删除任务计划
         * @author: 路正宁
         * @date: 2023-03-29 11:34:04
         */
        async del(task) {
            //确认删除弹框
            var confirmRes = await this.$confirm(`确定删除选中的  ${task.title}  计划任务吗？`, '提示', {
                type: 'warning',
                confirmButtonText: '删除',
                confirmButtonClass: 'el-button--danger'
            }).catch(() => {});
            //确认结果处理
            if (!confirmRes) {
                return false;
            }
            var res = await this.$API.sysTaskService.delete(task.id);
            if (res.code == 200) {
                this.list.splice(
                    this.list.findIndex((item) => item.id === task.id),
                    1
                );
                this.$message.success('删除成功');
            } else {
                this.$Response.errorNotice(res, '删除失败');
            }
        },
        logs() {
            this.dialog.logsVisible = true;
        },
        /**
         * 执行计划任务
         * @param task
         */
        run(task) {
            planTasks
                .executeTask({ id: task.id })
                .then((res) => {
                    if (res.code === 200) {
                        this.$message.success(res.data);
                    } else {
                        this.$Response.errorNotice(res, '执行失败');
                    }
                })
                .catch((err) => {
                    this.$Response.errorNotice(err, '执行失败');
                });
        },
        //本地更新数据
        handleSuccess(data, mode) {
            if (mode == 'add') {
                this.list.push(data);
            } else if (mode == 'edit') {
                this.list
                    .filter((item) => item.id === data.id)
                    .forEach((item) => {
                        Object.assign(item, data);
                    });
            }
        }
    }
};
</script>

<style scoped>
.task {
    height: 210px;
}

.task-item h2 {
    font-size: 15px;
    color: #3c4a54;
    padding-bottom: 15px;
}

.task-item li {
    list-style-type: none;
    margin-bottom: 10px;
}

.task-item li h4 {
    font-size: 12px;
    font-weight: normal;
    color: #999;
}

.task-item li p {
    margin-top: 5px;
}

.task-item .bottom {
    border-top: 1px solid #ebeef5;
    text-align: right;
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-add {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    color: #999;
}

.task-add:hover {
    color: #409eff;
}

.task-add i {
    font-size: 30px;
}

.task-add p {
    font-size: 12px;
    margin-top: 20px;
}

.dark .task-item .bottom {
    border-color: var(--el-border-color-light);
}
</style>
