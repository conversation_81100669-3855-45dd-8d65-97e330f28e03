<template>
    <div>
        <!-- 表头 -->
        <el-card  class="box-card Botm">
            <TopTitle title="筛选">
            </TopTitle>
            <el-form :model="queryParams" ref="queryForm" :inline="true"  class="form_130">
                <el-form-item label="消杀分类" prop="valueName" class="label">
                    <el-input v-model="queryParams.valueName" placeholder="请输入消杀分类" clearable class="form_225"/>
                </el-form-item>
                <el-form-item label="状态" prop="isStop">
                    <el-select v-model="queryParams.isStop" placeholder="请选择状态" clearable class="form_225">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表单区域 -->
        <el-card style="margin:10px;">
            <TopTitle title="查询列表">
                <el-button type="primary" @click="handleAdd(form)" class="creatSpan">新增</el-button>
            </TopTitle>
            <el-table v-loading="loading" :data="curingList" @selection-change="handleSelectionChange" class="el-table" border
                >
                <el-table-column label="消杀分类" align="left" prop="valueName" />
                <el-table-column label="状态" align="left" prop="isStop">
                    <template #default="scope">
                        {{ scope.row.isStop === '1' ? '正常' : '停用' }}
                    </template>
                </el-table-column>
                <el-table-column label="备注" align="left" prop="remark" />
                <el-table-column label="创建日期" align="left" prop="createDate">
                    <template #default="scope">
                        {{ formatDate(scope.row.createDate) }}
                    </template>
                </el-table-column>
                <el-table-column label="修改日期" align="left" prop="updateDate">
                    <template #default="scope">
                        {{ formatDate(scope.row.updateDate) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row, form)"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                        <el-button link type="danger" @click="handleDelete(scope.row)"><img src="@/assets/icons/delete.png"
                                style="margin-right:5px" />删除</el-button>
                    </template>
                </el-table-column>

            </el-table>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 点击增加的弹窗 -->
        <el-dialog v-model="dialogFormVisible" :title="!queryParams.boxFlag ? '添加消杀分类标签管理' : '修改消杀分类标签管理'" width="30%">
            <div v-show="!queryParams.boxFlag">
                <el-form :model="addForm" ref="form" :rules="rules" label-position="right" label-width="80px" size="normal">
                    <el-form-item label="消杀分类" prop="valueName">
                        <el-input v-model="addForm.valueName" placeholder="请输入消杀分类" size="normal" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-radio-group v-model="addForm.isStop">
                            <el-radio label="1" size="large">正常</el-radio>
                            <el-radio label="0" size="large">停用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="addForm.remark" :rows="4" type="textarea" placeholder="请输入备注" />
                    </el-form-item>
                </el-form>
                <div class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="creat(form)">确定</el-button>
                </div>
            </div>
            <!-- 编辑信息 -->
            <div v-show="queryParams.boxFlag">
                <el-form :model="addForm2" ref="form2" :rules="rules2" label-position="right" label-width="80px"
                    size="normal">
                    <el-form-item label="消杀分类" prop="valueName">
                        <el-input v-model="addForm2.valueName" placeholder="请输入消杀分类" size="normal" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-radio-group v-model="addForm2.isStop">
                            <el-radio label="1" size="large">正常</el-radio>
                            <el-radio label="0" size="large">停用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input v-model="addForm2.remark" :rows="4" type="textarea" placeholder="请输入备注" />
                    </el-form-item>
                </el-form>
                <div class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="edit(form2)">确定</el-button>
                </div>
            </div>


        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, toRefs, onBeforeMount, onMounted, watchEffect, computed, getCurrentInstance, nextTick } from 'vue';
import erpBaseCommonValuesService from '@/api/erp/assist/erpBaseCommonValuesService';
// import diagnosis from "@/api/erp/diagnosis"
// import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import TopTitle from '@/components/topTitle'
import { ElMessage } from "element-plus";
const loading = ref(false);
const { proxy } = getCurrentInstance();
const form = ref();
const form2 = ref();
const total = ref(0);
const options = [
    {
        value: '1',
        label: '正常',
    },
    {
        value: '0',
        label: '停用',
    }
]
const queryForm = ref();
const ids = ref();
const addForm = reactive({
    valueName: '',
    isStop: '1',
    remark: '',
    item: {
        id:12
    },
})
const addForm2 = reactive({
    valueName: '',
    isStop: '1',
    remark: '',
    item: {
        id: 12
    },
})
const queryParams = reactive({
    current: 1,
    size: 10,
    valueName: null,
    isStop: null,
    boxFlag: false,
})
const small = ref(false);
const disabled = ref(false);
const curingList = ref([])
const rules = reactive({
    valueName: [{ required: true, message: '请输入消杀分类', trigger: 'blur' },],
})
const rules2 = reactive({
    valueName: [{ required: true, message: '请输入消杀分类', trigger: 'blur' },],
})
const dialogFormVisible = ref(false);
// 搜索
function handleQuery() {
    getList()
}
//消杀分类列表
function getList() {
    // console.log( {...queryParams});
    loading.value = true
    erpBaseCommonValuesService.list({
        "item.id":12,
        ...queryParams
    }).then(res => {
        if (res.code == 200) {
            curingList.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}
getList()

// 增加按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    queryParams.boxFlag = false
    formEl.resetFields()
    addForm.isStop = "1"
}

const creat = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            erpBaseCommonValuesService.save(addForm)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        dialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "添加失败，请稍后重试",
                        });
                    }
                })
        }
    });
};
// 格式化日期
function formatDate(d) {
    var date = new Date(d);
    var YY = date.getFullYear() + "-";
    var MM =
        (date.getMonth() + 1 < 10
            ? "0" + (date.getMonth() + 1)
            : date.getMonth() + 1) + "-";
    var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    return YY + MM + DD;
}
// 重置
function resetQuery(formEl) {
    formEl.resetFields()
    getList()
}
// 编辑按钮
function handleEdit(row, formEl) {
    ids.value = row.id
    if (formEl !== undefined) {
        formEl.resetFields()

    }
    dialogFormVisible.value = true
    queryParams.boxFlag = true
    addForm2.valueName = row.valueName
    addForm2.isStop = row.isStop
    addForm2.remark = row.remark
}
// 编辑请求
const edit = async (formEl) => {
    console.log(ids.value);
    if (!formEl) return;
    await formEl.validate((valid, fields) => {
        if (valid) {
            var params = {
                valueName: addForm2.valueName,
                isStop: addForm2.isStop,
                remark: addForm2.remark,
                item: {
                    id: 12
                },
                id: ids.value
            }
            erpBaseCommonValuesService.save(params).then(res => {
                if (res.code == 200) {
                    ElMessage({
                        message: "修改成功",
                        type: "success",
                    });
                    dialogFormVisible.value = false
                    getList()
                }
            })
        }
    });
};
//删除
function handleDelete(row) {
    proxy.$confirm('是否确认删除此消杀分类?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        erpBaseCommonValuesService.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
/**
* 仓库
*/
// const store = useStore();
/**
* 路由对象
*/
const route = useRoute();
/**
* 路由实例
*/
const router = useRouter();
//console.log('1-开始创建组件-setup')
/**
* 数据部分
*/
const data = reactive({})
onBeforeMount(() => {
    //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
    //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) } 
defineExpose({
    ...toRefs(data)
})

</script>
<style scoped lang='scss'>
.commonTopBox {
    margin: 20px 0px 0px 20px;
}

.el-table {
    margin-top: 20px;
}

.dialog-footer {
   display: flex;
   justify-content: end;
    //   float:right;
}

.topSpan {
    width: 45px;
    height: 23px;
    font-size: 22px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    display: block;
    line-height: 30px;
    margin-bottom: 20px;
}

.searchSpan {
    width: 138px;
    height: 23px;
    font-size: 22px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    // display: block;
    line-height: 30px;
    // margin-right:100px;
}

.creatSpan {
    // margin-left:1000px;
    // position: ;
    float: right;
}
::v-deep .Botm {
    margin:10px;
    .el-card__body {
        padding-bottom:0px
        
    }
}

</style>