import request from '@/utils/request';

export default {
    // 列表
    paymentListOwner: function (params) {
        return request.get('/cost/payment/company/list', params);
    },
    // 获取承运商列表
    getCarrierList: function (params) {
        return request.post('/cost/payment/queryCarrierSelect', params);
    },
    // 预付款/付款单发起申请
    applyPayment: function (params) {
        return request.post('/tms/advancepayment/apply/submitApply', params);
    },
    // 发起开票申请
    applyInvoice: function (params) {
        return request.post('/tms/payment/invoiceApply/submitApply', params);
    },
	// 导出
    exportPayment: function (params, config, resDetail, responseType) {
        return request.get('/cost/payment/company/export', params, config, resDetail, responseType);
    },
};
