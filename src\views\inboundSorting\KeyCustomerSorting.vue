<template>
    <div>
        <!--	集货区搜索和列表	-->
        <div v-if="displayType === '1'" v-loading="loadingCollectingArea" class="app-container" element-loading-text="加载中...">
            <el-card v-show="showSearchWaybill" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <el-form ref="queryFormCollectingArea" :inline="true" :model="queryFormCollectingArea" class="seache-form" @submit.native.prevent>
                    <el-form-item label="产品分类" prop="productClass" style="width: 230px">
                        <el-select v-model="queryFormCollectingArea.productClass" clearable filterable placeholder="请选择" @change="handleChangesCollectingArea">
                            <el-option v-for="item in productClassList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="集货区" prop="areaCode" style="width: 230px">
                        <el-select v-model="queryFormCollectingArea.areaCode" clearable filterable placeholder="请选择" @change="handleChangesCollectingArea">
                            <el-option v-for="item in collectingAreaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="分拣时间" prop="createDate" style="width: 320px">
                        <el-date-picker
                            v-model="queryFormCollectingArea.createDate"
                            :shortcuts="shortcuts"
                            end-placeholder="结束日期"
                            range-separator="至"
                            start-placeholder="开始日期"
                            type="daterange"
                            unlink-panels
                            value-format="YYYY-MM-DD"
                            @change="handleChangesCollectingArea"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item v-show="isShowAllCollectingArea" label="分拣人" prop="createName" style="width: 240px">
                        <el-input v-model="queryFormCollectingArea.createName" clearable placeholder="请输入" />
                    </el-form-item>
                    <search-button :is-show-all="isShowAllCollectingArea" @handleQuery="handleChangesCollectingArea" @resetQuery="resetQuery('queryFormCollectingArea')" @showAllClick="showAllClick('queryFormCollectingArea')"
                /></el-form>
            </el-card>

            <el-card :body-style="{ padding: '10px' }" shadow="never">
                <div class="mb10">
                    <el-button icon="el-icon-top" type="success" @click="openListingVisible">上架</el-button>
                    <el-button icon="el-icon-refresh" type="primary" @click="setDisplayType">按运单显示</el-button>
                    <right-toolbar v-model:columns="columnsCollectingArea" v-model:show-search="showSearchCollectingArea" table-i-d="tableCollectingArea" @queryTable="getListCollectingArea" />
                </div>
                <column-table ref="tableCollectingArea" :max-height="600" :columns="columnsCollectingArea" :data="dataListCollectingArea">
                    <template #opt="{ row }">
                        <el-button link size="small" icon="el-icon-info-filled" type="primary" @click="handleShowRecord(row)">上架记录</el-button>
                    </template>
                </column-table>
                <pagination v-show="totalCollectingArea > 0" v-model:limit="queryFormCollectingArea.size" v-model:page="queryFormCollectingArea.current" :pageSizes="[10, 20, 30, 50, 100]" :total="totalCollectingArea" @pagination="getListCollectingArea" />
            </el-card>
        </div>

        <!--	运单搜索和列表	-->
        <div v-if="displayType === '2'" v-loading="loadingWaybill" class="app-container" element-loading-text="加载中...">
            <el-card v-show="showSearchWaybill" ref="searchCard2" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <el-form ref="queryFormWaybill" :inline="true" :model="queryFormWaybill" class="seache-form" @submit.native.prevent>
                    <el-form-item label="产品分类" prop="productClass" style="width: 240px">
                        <el-select v-model="queryFormWaybill.productClass" clearable filterable placeholder="请选择" @change="handleChangesWaybill">
                            <el-option v-for="item in productClassList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="集货区" prop="areaCode" style="width: 240px">
                        <el-select v-model="queryFormWaybill.areaCode" clearable filterable placeholder="请选择" @change="handleChangesWaybill">
                            <el-option v-for="item in collectingAreaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="分拣时间" prop="createDate" style="width: 320px">
                        <el-date-picker
                            v-model="queryFormWaybill.createDate"
                            :shortcuts="shortcuts"
                            end-placeholder="结束日期"
                            range-separator="至"
                            start-placeholder="开始日期"
                            type="daterange"
                            unlink-panels
                            value-format="YYYY-MM-DD"
                            @change="handleChangesWaybill"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item v-show="isShowAllWaybill" label="分拣人" prop="createName" style="width: 240px">
                        <el-input v-model="queryFormWaybill.createName" clearable placeholder="请输入" />
                    </el-form-item>
                    <el-form-item v-show="isShowAllWaybill" label="订单号" prop="orderNo" style="width: 240px">
                        <el-input v-model="queryFormWaybill.orderNo" clearable placeholder="请输入订单号" @clear="handleChangesWaybill" @keyup.enter.native="handleChangesWaybill"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAllWaybill" label="运单号" prop="transOrderNo" style="width: 240px">
                        <el-input v-model="queryFormWaybill.transOrderNo" clearable placeholder="请输入运单号" @clear="handleChangesWaybill" @keyup.enter.native="handleChangesWaybill"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAllWaybill" label="货主公司" prop="companyId" style="width: 240px">
                        <el-select v-model="queryFormWaybill.companyId" clearable filterable placeholder="请选择" @change="handleChangesWaybill">
                            <el-option v-for="item in accountList" :key="item.companyId" :label="item.companyName" :value="item.companyId"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShowAllWaybill" label="发件公司" prop="sendCompany" style="width: 240px">
                        <el-input v-model="queryFormWaybill.sendCompany" clearable placeholder="请输入发件公司" @clear="handleChangesWaybill" @keyup.enter.native="handleChangesWaybill"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAllWaybill" label="收件公司" prop="receiverCompany" style="width: 240px">
                        <el-input v-model="queryFormWaybill.receiverCompany" clearable placeholder="请输入收件公司" @clear="handleChangesWaybill" @keyup.enter.native="handleChangesWaybill"></el-input>
                    </el-form-item>
                    <el-form-item v-show="isShowAllWaybill" label="运输类型" prop="productType" style="width: 240px">
                        <el-select v-model="queryFormWaybill.productType" clearable filterable placeholder="请选择" @change="handleChangesWaybill">
                            <el-option v-for="item in productTypeList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
                        </el-select>
                    </el-form-item>
                    <search-button :is-show-all="isShowAllWaybill" @handleQuery="handleChangesWaybill" @resetQuery="resetQuery('queryFormWaybill')" @showAllClick="showAllClick('queryFormWaybill')"
                /></el-form>
            </el-card>

            <el-card :body-style="{ padding: '10px' }" shadow="never">
                <div class="mb10">
                    <el-button icon="el-icon-top" type="success" @click="openListingVisible">上架</el-button>
                    <el-button icon="el-icon-refresh" type="primary" @click="setDisplayType">按集货区显示</el-button>
                    <right-toolbar v-model:columns="columnsWaybill" v-model:show-search="showSearchWaybill" table-i-d="tableWaybill" @queryTable="getListWaybill" />
                </div>
                <column-table ref="tableWaybill" :columns="columnsWaybill" :data="dataListWaybill" :maxHeight="tableHeight">
                    <template #productClass="{ row }">
                        <span>{{ formatDictionaryData('productClassList', row.productClass) }}</span>
                    </template>
                    <template #productType="{ row }">
                        <span>{{ formatDictionaryData('productTypeList', row.productType) }}</span>
                    </template>
                    <template #opt="{ row }">
                        <el-button v-if="row.delFlag === '0'" link size="small" icon="el-icon-delete" type="danger" @click="handleListingRecord(row)">下架</el-button>
                        <span v-else>已下架</span>
                    </template>
                </column-table>
                <pagination v-show="totalWaybill > 0" v-model:limit="queryFormWaybill.size" v-model:page="queryFormWaybill.current" :pageSizes="[10, 20, 30, 50, 100]" :total="totalWaybill" @pagination="getListWaybill" />
            </el-card>
        </div>

        <!-- 上架抽屉 -->
        <el-drawer v-if="listingVisible" v-model="listingVisible" size="1140px" title="上架" @close="handleCloseListing">
            <div v-loading="listingLoading" :element-loading-text="listingLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="listingForm" :inline="true" :model="listingForm" class="seache-form" @submit.native.prevent>
                        <el-form-item label="货主公司" prop="companyId">
                            <el-select v-model="listingForm.companyId" clearable filterable placeholder="请选择" @change="handleChangesListing">
                                <el-option v-for="item in accountList" :key="item.companyId" :label="item.companyName" :value="item.companyId"> </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="收件公司" prop="receiverCompany">
                            <el-input v-model="listingForm.receiverCompany" clearable placeholder="请输入收件公司" @clear="handleChangesListing" @keyup.enter.native="handleChangesListing"></el-input>
                        </el-form-item>
                        <el-form-item label="下单时间" prop="createDate" style="width: 320px">
                            <el-date-picker
                                v-model="listingForm.createDate"
                                :shortcuts="shortcuts"
                                end-placeholder="结束日期"
                                range-separator="至"
                                start-placeholder="开始日期"
                                type="daterange"
                                unlink-panels
                                value-format="YYYY-MM-DD"
                                @change="handleChangesListing"
                            ></el-date-picker>
                        </el-form-item>
                        <search-button :is-show-all-switch="false" @handleQuery="handleChangesListing" @resetQuery="resetQuery('listingForm')" />
                    </el-form>
                </el-card>
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <div class="box__listing__tree">
                        <span class="listing__tree__title">（已选：{{ numberOfSelectedParts }}件）</span>
                        <el-checkbox v-model="isSelectAll" style="margin-left: 10px" @change="handleSelectAll">
                            <span>全选</span>
                        </el-checkbox>
                        <el-button link style="margin-left: 10px" @click="handleClear">
                            <el-icon>
                                <el-icon-delete />
                            </el-icon>
                            清除已选
                        </el-button>
                        <el-button @click="handleCloseListing">取消</el-button>
                        <el-button type="primary" @click="handleListing">上架</el-button>
                    </div>
                    <el-tree ref="treeListing" :data="listingData" :props="{ children: 'orderInfoList' }" node-key="transOrderNo" show-checkbox @check-change="handCheckChange"></el-tree>
                </el-card>
            </div>
        </el-drawer>

        <!-- 选择集货区对话框 -->
        <el-dialog v-if="collectingAreaVisible" v-model="collectingAreaVisible" title="选择集货区" width="30%" @close="handleCollectingAreaClose">
            <el-form ref="collectingAreaForm" :model="collectingAreaForm" :rules="collectingAreaRules">
                <el-form-item label="集货区" prop="areaCode">
                    <el-select v-model="collectingAreaForm.areaCode" clearable filterable placeholder="请选择">
                        <el-option v-for="item in collectingAreaList" :key="item.areaCode" :label="item.areaName" :value="item.areaCode"> </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div style="display: flex; justify-content: center">
                <el-button @click="handleCollectingAreaClose">取消</el-button>
                <el-button type="primary" @click="handleCollectingArea">确定</el-button>
            </div>
        </el-dialog>

        <!-- 上架记录抽屉 -->
        <el-drawer v-if="listingRecordVisible" v-model="listingRecordVisible" size="1140px" title="上架记录" @close="handleCloseListingRecord">
            <div v-loading="listingRecordLoading" :element-loading-text="listingRecordLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="listingRecordForm" :inline="true" :model="listingRecordForm" class="seache-form" @submit.native.prevent>
                        <el-form-item label="运单号" prop="transOrderNo" style="width: 240px">
                            <el-input v-model="listingRecordForm.transOrderNo" clearable placeholder="请输入运单号" @clear="handleChangesListingRecord" @keyup.enter.native="handleChangesListingRecord"></el-input>
                        </el-form-item>
                        <el-form-item label="收件公司" prop="receiverCompany" style="width: 240px">
                            <el-input v-model="listingRecordForm.receiverCompany" clearable placeholder="请输入收件公司" @clear="handleChangesListingRecord" @keyup.enter.native="handleChangesListingRecord"></el-input>
                        </el-form-item>
                        <el-form-item label="产品分类" prop="productClass">
                            <el-select v-model="listingRecordForm.productClass" clearable filterable placeholder="请选择" @change="handleChangesListingRecord">
                                <el-option v-for="item in productClassList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
                            </el-select>
                        </el-form-item>
                        <search-button :is-show-all-switch="false" @handleQuery="handleChangesListingRecord" @resetQuery="resetQuery('listingRecordForm')" />
                    </el-form>
                </el-card>

                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <column-table ref="tableListingRecord" :columns="columnsListingRecord" :data="dataListListingRecord">
                        <template #productClass="{ row }">
                            <span>{{ formatDictionaryData('productClassList', row.productClass) }}</span>
                        </template>
                        <template #transOrderNo="{ row }">
                            <el-button link size="small" type="primary" @click="handleOrderDetail(row)">{{ row.transOrderNo }}</el-button>
                        </template>
                        <template #opt="{ row }">
                            <el-button link size="small" type="primary" @click="handleListingRecord(row)">下架</el-button>
                        </template>
                    </column-table>
                    <pagination v-show="totalWaybill > 0" v-model:limit="queryFormWaybill.size" v-model:page="queryFormWaybill.current" :pageSizes="[10, 20, 30, 50, 100]" :total="totalWaybill" @pagination="getListingRecord" />
                </el-card>
            </div>
        </el-drawer>

        <!-- 订单详情抽屉 -->
        <el-drawer v-if="orderDetailVisible" v-model="orderDetailVisible" size="1140px" title="订单详情">
            <div style="background-color: #f2f2f2; padding: 10px">
                <order-details :order-info="orderInfo"></order-details>
            </div>
        </el-drawer>
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import moment from 'moment';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import OrderDetails from '@/views/orderComponents/OrderDetails/index.vue';
import keyCustomerSorting from '@/api/carrierEnd/keyCustomerSorting';
import { selectDictLabel } from '@/utils/dictLabel';

export default {
    name: 'KeyCustomerSorting',
    components: { OrderDetails, RightToolbar, ColumnTable, SearchButton },
    data() {
        return {
            showSearchCollectingArea: true,
            showSearchWaybill: true,
            queryFormCollectingArea: {
                current: 1,
                size: 10,
                productType: undefined,
                areaCode: undefined,
                createName: undefined,
                createDate: []
            },
            queryFormWaybill: {
                current: 1,
                size: 10,
                productType: undefined,
                areaCode: undefined,
                createName: undefined,
                createDate: []
            },
            productTypeList: [],
            collectingAreaList: [],
            shortcuts: [
                {
                    text: '无',
                    value: (e) => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: (e) => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ],
            isShowAllCollectingArea: false,
            isShowAllWaybill: false,
            columnsCollectingArea: [
                { title: '集货区', key: 'areaName', align: 'center', minWidth: '110px', columnShow: true, showOverflowTooltip: true },
                { title: '总单数', key: 'transNum', align: 'center', width: '70px', columnShow: true },
                { title: '总件数', key: 'goodsNum', align: 'center', width: '70px', columnShow: true },
                { title: '分拣人 ', key: 'createName', align: 'center', minWidth: '110px', columnShow: true, showOverflowTooltip: true },
                { title: '分拣时间 ', key: 'createDate', align: 'center', width: '100px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '110px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            columnsWaybill: [
                { title: '订单号', key: 'orderNo', align: 'center', width: '110px', columnShow: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '150px', columnShow: true },
                { title: '集货区', key: 'areaName', align: 'center', width: '110px', columnShow: true, showOverflowTooltip: true },
                { title: '总件数', key: 'goodsPackages', align: 'center', width: '70px', columnShow: true },
                { title: '运输类型', key: 'productType', align: 'center', width: '100px', columnShow: true },
                { title: '产品分类', key: 'productClass', align: 'center', width: '100px', columnShow: true },
                { title: '温区', key: 'temperatureDesc', align: 'center', width: '130px', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收件人', key: 'receiverUser', align: 'center', width: '120px', columnShow: true },
                { title: '收件人电话', key: 'receiverUserPhone', align: 'center', width: '110px', columnShow: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '发件人', key: 'sendUser', align: 'center', width: '120px', columnShow: true },
                { title: '发件人电话', key: 'sendUserPhone', align: 'center', width: '110px', columnShow: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '分拣人 ', key: 'createName', align: 'center', width: '130px', columnShow: true, showOverflowTooltip: true },
                { title: '分拣时间 ', key: 'createDate', align: 'center', width: '100px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '110px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            dataListCollectingArea: [],
            dataListWaybill: [],
            totalCollectingArea: 0,
            totalWaybill: 0,
            loadingCollectingArea: false,
            loadingWaybill: false,
            // 显示类型 1 按集货区显示 2 按运单显示
            displayType: '1',
            productClassList: [],
            listingVisible: false,
            listingLoading: false,
            listingLoadingText: '加载中...',
            listingForm: {
                companyId: undefined,
                receiverCompany: undefined,
                createDate: []
            },
            listingData: [],
            listingSelectAll: false,
            listingRecordVisible: false,
            listingRecordLoading: false,
            listingRecordLoadingText: '加载中...',
            listingRecordForm: {
                transOrderNo: undefined,
                delFlag: '0'
            },
            columnsListingRecord: [
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '150px', columnShow: true },
                { title: '产品分类', key: 'productClass', align: 'center', width: '100px', columnShow: true },
                { title: '温区', key: 'temperatureDesc', align: 'center', width: '130px', columnShow: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '70px', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '上架时间', key: 'createDate', align: 'center', width: '100px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            dataListListingRecord: [],
            orderDetailVisible: false,
            orderInfo: null,
            accountList: [],
            collectingAreaForm: {
                areaCode: undefined
            },
            collectingAreaVisible: false,
            collectingAreaRules: {
                areaCode: [{ required: true, message: '请选择集货区', trigger: 'blur' }]
            },
            tableHeight: 400,
            isSelectAll: false,
            numberOfSelectedParts: 0
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    created() {
        this.getDict();
        // 将默认设置调整为当天
        this.queryFormCollectingArea.beginCreateDate = moment().format('YYYY-MM-DD');
        this.queryFormCollectingArea.endCreateDate = moment().format('YYYY-MM-DD');
        this.queryFormCollectingArea.createDate = [this.queryFormCollectingArea.beginCreateDate, this.queryFormCollectingArea.endCreateDate];
        // 将默认设置调整为当天
        this.queryFormWaybill.beginCreateDate = moment().format('YYYY-MM-DD');
        this.queryFormWaybill.endCreateDate = moment().format('YYYY-MM-DD');
        this.queryFormWaybill.createDate = [this.queryFormWaybill.beginCreateDate, this.queryFormWaybill.endCreateDate];
        // 将默认设置调整为当天
        this.listingForm.beginCreateDate = moment().format('YYYY-MM-DD');
        this.listingForm.endCreateDate = moment().format('YYYY-MM-DD');
        this.listingForm.createDate = [this.listingForm.beginCreateDate, this.listingForm.endCreateDate];
        // 查询承运商合作的大客户列表
        this.getKeyAccountList();
        this.handleChangesCollectingArea();
        // 获取集货区列表
        this.getBranchCollectAreaList();
    },
    methods: {
        /**
         * 检查是否所有复选框都被选中
         * @param nodes
         * @returns {*}
         */
        flattenNodes(nodes) {
            return nodes.reduce((acc, node) => {
                acc.push(node);
                if (node.orderInfoList && node.orderInfoList.length > 0) {
                    acc = acc.concat(this.flattenNodes(node.orderInfoList));
                }
                return acc;
            }, []);
        },
        /**
         * 获取集货区列表
         */
        getBranchCollectAreaList() {
            keyCustomerSorting.getBranchCollectAreaList().then((res) => {
                if (res.code === 200 && res.data) {
                    this.collectingAreaList = res.data;
                }
            });
        },

        /**
         * 获取字典数据
         */
        async getDict() {
            this.productClassList = await this.getDictList('fourpl_product_class');
            this.productTypeList = await this.getDictList('fourpl_product_type');
        },
        /**
         * 查询承运商合作的大客户列表
         */
        async getKeyAccountList() {
            keyCustomerSorting
                .getCustomerByCarrier()
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.accountList = res.data;
                    }
                })
                .catch(() => {})
                .finally(() => {});
        },
        /**
         * 获取集货区列表
         */
        getListCollectingArea() {
            this.loadingCollectingArea = true;
            const { createDate, ...params } = this.queryFormCollectingArea;
            keyCustomerSorting
                .getCustomerByArea(params)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.tableHeight = window.innerHeight - this.$refs.searchCard.$el.offsetHeight - 240;
                        this.dataListCollectingArea = res.data.records || [];
                        this.totalCollectingArea = res.data.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loadingCollectingArea = false;
                });
        },
        /**
         * 获取运单按省市区分组未分拣订单列表
         */
        getListListingRecord() {
            this.listingLoading = true;
            this.listingLoadingText = '加载中...';
            const { createDate, ...params } = this.listingForm;
            keyCustomerSorting
                .getCustomerByWaybillNoSort(params)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        // 假设res.data是包含数据的数组
                        this.listingData = res.data.map((item) => {
                            const label = `${item.provinceName} ${item.cityName} ${item.countyName}`;

                            const orderInfoListWithLabel = item.orderInfoList.map((order) => {
                                const orderLabel = `运单号：${order.transOrderNo}，件数：${order.goodsPackages}件，收件公司：${order.receiverCompany}`;
                                return { ...order, label: orderLabel };
                            });

                            return { ...item, label, orderInfoList: orderInfoListWithLabel };
                        });
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.listingLoading = false;
                });
        },
        /**
         * 获取运单列表
         */
        getListWaybill() {
            this.loadingWaybill = true;
            const { createDate, ...params } = this.queryFormWaybill;
            keyCustomerSorting
                .getCustomerByWaybill(params)
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.tableHeight = window.innerHeight - this.$refs.searchCard2.$el.offsetHeight - 240;
                        this.dataListWaybill = res.data.records || [];
                        this.totalWaybill = res.data.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loadingWaybill = false;
                });
        },
        /**
         * 获取上架记录列表
         */
        getListingRecord() {
            keyCustomerSorting.getCustomerByWaybill({ ...this.listingRecordForm }).then((res) => {
                if (res.code === 200 && res.data) {
                    this.dataListListingRecord = res.data.records || [];
                    this.totalWaybill = res.data.total || 0;
                }
            });
        },
        /**
         * 获取已选运单数量
         */
        handCheckChange() {
            const checkedNodes = this.$refs.treeListing.getCheckedNodes();

            // 使用 filter 过滤出包含 orderNo 的节点，然后使用 map 获取 goodsPackages，并将其求和
            this.numberOfSelectedParts = checkedNodes
                .filter((node) => node.orderNo)
                .map((node) => node.goodsPackages || 0)
                .reduce((acc, cur) => acc + cur, 0);

            const allNodes = this.flattenNodes(this.listingData);

            // 检查是否所有复选框都被选中
            this.isSelectAll = checkedNodes.length === allNodes.length;
        },
        /**
         * 修改集货区条件
         */
        handleChangesCollectingArea() {
            this.queryFormCollectingArea.current = 1;
            const { createDate } = this.queryFormCollectingArea;

            // 验证queryTime是否包含两个合法的日期字符串
            const isValidDateRange = createDate && createDate.length === 2 && !createDate.some((date) => date === 'Invalid Date');

            if (isValidDateRange) {
                this.queryFormCollectingArea.beginCreateDate = createDate[0] + ' 00:00:00';
                this.queryFormCollectingArea.endCreateDate = createDate[1] + ' 23:59:59';
            } else {
                this.queryFormCollectingArea.beginCreateDate = null;
                this.queryFormCollectingArea.endCreateDate = null;
            }
            this.getListCollectingArea();
        },
        /**
         * 修改上架条件
         */
        handleChangesListing() {
            const { createDate } = this.listingForm;

            // 验证queryTime是否包含两个合法的日期字符串
            const isValidDateRange = createDate && createDate.length === 2 && !createDate.some((date) => date === 'Invalid Date');

            if (isValidDateRange) {
                this.listingForm.beginCreateDate = createDate[0] + ' 00:00:00';
                this.listingForm.endCreateDate = createDate[1] + ' 23:59:59';
            } else {
                this.listingForm.beginCreateDate = null;
                this.listingForm.endCreateDate = null;
            }
            this.numberOfSelectedParts = 0;
            // 取消所有的选中状态
            this.$refs.treeListing.setCheckedKeys([]);
            this.getListListingRecord();
        },
        /**
         * 上架记录
         */
        handleChangesListingRecord() {
            this.listingRecordForm.current = 1; // 直接设置当前页码为1
            this.dataListListingRecord = [];
            this.getListingRecord();
        },
        /**
         * 运单搜索
         */
        handleChangesWaybill() {
            let { current, createDate, ...rest } = this.queryFormWaybill;
            current = 1; // 直接设置当前页码为1

            const formatDateTime = (date, time) => (date && !isNaN(new Date(date).getTime()) ? `${date} ${time}` : null);

            if (createDate && createDate.length === 2) {
                rest.beginCreateDate = formatDateTime(createDate[0], '00:00:00');
                rest.endCreateDate = formatDateTime(createDate[1], '23:59:59');
            } else {
                rest.beginCreateDate = null;
                rest.endCreateDate = null;
            }

            this.queryFormWaybill = { current, createDate, ...rest }; // 合并更新后的对象
            this.getListWaybill();
        },
        /**
         * 清除已选
         */
        handleClear() {
            this.$refs.treeListing.setCheckedKeys([]);
            this.isSelectAll = false;
        },
        /**
         * 关闭上架记录抽屉
         */
        handleCloseListing() {
            this.listingVisible = false;
            this.resetQuery('listingForm');
            this.numberOfSelectedParts = 0;
            this.isSelectAll = false;
            this.listingData = [];
            if (this.displayType === '1') {
                this.handleChangesCollectingArea();
            } else {
                this.handleChangesWaybill();
            }
        },
        /**
         * 关闭上架记录抽屉
         */
        handleCloseListingRecord() {
            this.listingRecordVisible = false;
            this.resetQuery('listingRecordForm');
        },
        /**
         * 确定上架
         */
        handleCollectingArea() {
            this.$refs.collectingAreaForm.validate((valid) => {
                if (valid) {
                    this.listingLoading = true;
                    this.listingLoadingText = '上架中...';
                    keyCustomerSorting
                        .putCustomerByWaybill({
                            areaCode: this.collectingAreaForm.areaCode,
                            transOrderNoList: this.$refs.treeListing.getCheckedKeys().filter((item) => item)
                        })
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('上架成功');
                                this.handleChangesListing();
                                this.handleCollectingAreaClose();
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.listingLoading = false;
                        });
                } else {
                    return false;
                }
            });
        },
        /**
         * 关闭选择集货区对话框
         */
        handleCollectingAreaClose() {
            this.$refs.collectingAreaForm.resetFields();
            this.collectingAreaVisible = false;
        },
        /**
         * 上架
         */
        handleListing() {
            // 添加校验 如果没有选中任何运单，提示用户
            if (!this.$refs.treeListing.getCheckedKeys().length) {
                this.$message.warning('请先选择运单');
                return;
            }
            this.collectingAreaVisible = true;
        },
        /**
         * 下架
         * @param row
         */
        handleListingRecord(row) {
            this.$confirm('确定下架该运单吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    keyCustomerSorting
                        .putCustomerByWaybillDown({ transOrderNo: row.transOrderNo })
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('下架成功');
                                this.handleChangesListingRecord();
                                this.handleChangesWaybill();
                            }
                        })
                        .catch(() => {})
                        .finally(() => {});
                })
                .catch(() => {});
        },
        /**
         * 查看运单详情
         * @param row
         */
        handleOrderDetail(row) {
            this.orderDetailVisible = true;
            this.orderInfo = row;
            this.orderInfo.id = row.orderId;
        },
        /**
         * 上架全选
         */
        handleSelectAll() {
            if (this.isSelectAll) {
                const checkedKeys = this.listingData.map((item) => {
                    // item.orderInfoList 是一个数组，里面的每一项都是一个对象，对象中有一个 transOrderNo 属性
                    return item.orderInfoList.map((item) => item.transOrderNo);
                });
                // 将二维数组转换为一维数组
                this.$refs.treeListing.setCheckedKeys(checkedKeys.flat());
            } else {
                this.$refs.treeListing.setCheckedKeys([]);
            }
        },
        /**
         * 查看上架记录
         * @param row
         */
        handleShowRecord(row) {
            this.listingRecordVisible = true;
            const { areaCode } = row;
            if (areaCode) {
                this.listingRecordForm.areaCode = areaCode;
                this.handleChangesListingRecord(areaCode);
            }
        },
        /**
         * 打开上架记录
         */
        openListingVisible() {
            this.listingVisible = true;
            this.handleChangesListing();
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            if (formName === 'queryFormCollectingArea') {
                this.handleChangesCollectingArea();
            }
            if (formName === 'queryFormWaybill') {
                this.handleChangesWaybill();
            }
            if (formName === 'listingForm') {
                this.handleChangesListing();
            }
            if (formName === 'listingRecordForm') {
                this.handleChangesListingRecord();
            }
        },
        /**
         * 切换显示类型
         */
        setDisplayType() {
            this.displayType = this.displayType === '1' ? '2' : '1';
            if (this.displayType === '1') {
                this.handleChangesCollectingArea();
            } else {
                this.handleChangesWaybill();
            }
        },
        /**
         * 显示全部
         * @param formName
         */
        showAllClick(formName) {
            if (formName === 'queryFormCollectingArea') {
                this.isShowAllCollectingArea = !this.isShowAllCollectingArea;
            }
            if (formName === 'queryFormWaybill') {
                this.isShowAllWaybill = !this.isShowAllWaybill;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
}
.box__listing__tree {
    .listing__tree__title {
        font-size: 14px;
        font-weight: bold;
    }
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
</style>
