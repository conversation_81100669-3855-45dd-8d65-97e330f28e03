<template>
    <div style="background-color: rgb(255, 255, 255); margin: 10px">
        <el-card :body-style="{ padding: '10px 20px' }" class="mb10" shadow="never">
            <el-tabs v-model="activeTab" @tab-click="tabClick">
                <el-tab-pane label="发货地址" name="1" />
                <el-tab-pane label="收货地址" name="2" />
            </el-tabs>
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" style="margin: 0 0 10px 0">
                <el-form-item label="地址信息" prop="user">
                    <el-input v-model="queryParams.searchValue" clearable placeholder="请输入用户名/公司/电话" style="width: 260px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
            <div>
                <el-button icon="el-icon-plus" size="mini" type="primary" @click="handleAdd"> 新增 </el-button>
                <right-toolbar v-model:show-search="showSearch" @queryTable="getList"></right-toolbar>
                <column-table v-loading="loading" :columns="columns" :data="dataList" :rowClassName="rowClassName" class="column-table" style="margin-top: 10px">
                    <template #addresscon="{ row }">
                        <span>{{ row?.town?.province || '' }}{{ row?.town?.city || '' }}{{ row?.town?.county || '' }}{{ row?.town?.town || '' }}{{ row.address || '' }}</span>
                    </template>
                    <template #isDefaults="scope">
                        <el-switch v-model="scope.row.isDefaults" :active-value="true" size="small" :inactive-value="false" active-color="#1ACD7E" active-text="是" class="switch__label" inactive-text="否" @change="changeDefaults($event, scope.row)"> </el-switch>
                    </template>
                    <template #opt="{ row }">
                        <el-button icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(row)">修改</el-button>
                        <el-button icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(row)">删除</el-button>
                    </template>
                </column-table>
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
            </div>
        </el-card>
        <!-- 添加或修改货主地址簿对话框 -->
        <el-drawer v-model="open" :title="title" size="35vw" @close="cancel">
            <div class="p-16">
                <el-form ref="form" :model="form" :rules="rules" label-width="90px" style="margin: 30px">
                    <el-form-item label="用户名" prop="user">
                        <el-input v-model.trim="form.user" maxlength="20" placeholder="请输入用户名" show-word-limit />
                    </el-form-item>
                    <el-form-item label="公司" prop="company">
                        <el-input v-model="form.company" maxlength="60" placeholder="请输入公司" show-word-limit />
                    </el-form-item>
                    <el-form-item label="联系电话" prop="phone">
                        <el-input v-model.trim="form.phone" placeholder="请输入联系电话" />
                    </el-form-item>
                    <el-form-item label="省市区" prop="addressPCC">
                        <el-cascader ref="addressPCC" v-model="form.addressPCC" :options="sysAreas" clearable placeholder="请选择省市区" style="width: 100%" />
                    </el-form-item>
                    <el-form-item label="详细地址" prop="address">
                        <el-input v-model.trim="form.address" maxlength="200" placeholder="请输入详细地址" show-word-limit style="width: 100%" />
                    </el-form-item>
                    <el-form-item label="默认地址" prop="isDefaults">
                        <el-switch v-model="form.isDefaults" :active-value="true" :inactive-value="false" active-color="#1ACD7E" active-text="是" class="switch__label" inactive-text="否"> </el-switch>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 16px; margin-right: 30px">
                    <el-button @click="open = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>
<script>
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable';
import address from '@/api/addressBook/address.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import { verifyThatTheAddressesAreConsistent } from '@/utils';

export default {
    name: 'HzAddressBook',
    components: {
        SearchButton,
        RightToolbar,
        ColumnTable
    },
    data() {
        return {
            activeTab: '1',
            queryParams: {
                current: 1,
                size: 10,
                queryType: 0,
                type: '1'
            },
            open: false,
            disabled: false,
            showSearch: true,
            conpanySelectList: [],
            title: '',
            // 表单参数
            form: {
                user: '',
                company: '',
                phone: '',
                addressPCC: [],
                address: '',
                isDefaults: false
            },
            sysAreas: this.getSysAreas, //省市区数据
            // 表单校验
            rules: {
                companyId: [{ required: true, message: '请选择货主', trigger: 'blur' }],
                user: [{ required: true, message: '用户名必填', trigger: 'blur' }],
                // type:[{required:true,message:"类型必选",trigger:"blur"}],
                phone: [
                    {
                        required: true,
                        message: '请输入正确的联系电话',
                        pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/,
                        trigger: 'blur'
                    }
                ],
                address: [
                    { required: true, message: '详细地址必填', trigger: 'blur' },
                    { max: 200, message: '详细地址不能超过200个字符', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (verifyThatTheAddressesAreConsistent(value, this.$refs.addressPCC.getCheckedNodes()[0]?.pathLabels.slice(0,3))) {
                                callback();
                            } else {
                                callback(new Error('详细地址与省市区不符'));
                            }
                        },
                        trigger:  ['blur','change']
                    }
                ],
                company: [{ required: true, message: '公司必填', trigger: 'blur' }],
                addressPCC: [{ required: true, message: '省市区必选', trigger: 'blur' }]
            },

            dataList: [],
            columns: [
                { title: '所属货主', key: 'companyName', align: 'center', minWidth: '180px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '用户名', key: 'user', width: '180px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '公司', key: 'company', minWidth: '260px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '电话号码', key: 'phone', width: '180px', align: 'center', columnShow: true },
                { title: '客户地址', key: 'addresscon', align: 'center', columnShow: true, minWidth: '280px', showOverflowTooltip: true },
                { title: '默认地址', key: 'isDefaults', align: 'center', columnShow: true, minWidth: '140px' },
                { title: '操作', key: 'opt', align: 'center', width: '160px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            total: 0,
            loading: false,
            isSearch: false
        };
    },
    created() {
        this.getList();
        this.getCompanySelectList();
    },
    methods: {
        /**
         * 默认地址添加样式
         * @param row
         * @returns {string}
         */
        rowClassName({ row }) {
            if (row.isDefaults) {
                return 'is-defalut';
            } else {
                return '';
            }
        },
        /**
         * 改变默认地址状态
         * @param e
         */
        changeDefaults(e, row) {
            this.loading = true;
            address
                .updateDefaultAddress({ companyId: row.companyId, id: row.id, type: row.type, isDefalut: e })
                .then((response) => {
                    if (response.code === 200) {
                        this.msgSuccess('操作成功！');
                        this.getList();
                    }
                    this.loading = false;
                })
                .catch((e) => {
                    this.loading = false;
                });
        },
        getList() {
            this.loading = true;
            address
                .listBook(this.queryParams)
                .then((response) => {
                    if (response.code === 200) {
                        this.dataList = response.data.records;
                        this.total = response.data.total;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        handleQuery() {
            // this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.resetForm('form');
            this.open = true;
            this.title = '添加地址';
            // this.form.addressPCC = [];
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.queryParams.searchValue = '';
            this.queryParams.companyId = '';
            // this.queryParams = {
            //     pageNum: 1,
            //     pageSize: 10,
            //     queryType: 1,
            //     type: 1
            // },
            // this.resetForm('queryForm');
            this.handleQuery();
        },
        getCompanySelectList() {
            address.getCompanySelect({ status: '1' }).then((response) => {
                this.conpanySelectList = response.data;
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.form.queryType = '0';
            this.form.type = this.queryParams.type;
            this.form.provinceId = this.form.addressPCC[0];
            this.form.cityId = this.form.addressPCC[1];
            this.form.countyId = this.form.addressPCC[2];
            this.form.town = {
                id: this.form.addressPCC[3]
            };
            if (this.form.addressPCC) {
                delete this.form.addressPCC;
            }
            if (this.form.id != null) {
                if (this.form.id) {
                    delete this.form.createBy;
                    delete this.form.createDate;
                    delete this.form.delFlag;
                    delete this.form.updateBy;
                    delete this.form.updateDate;
                }
                console.log(this.form);
                address.addBook(this.form).then((response) => {
                    this.msgSuccess('修改成功');
                    this.open = false;
                    this.getList();
                });
            } else {
                address.addBook(this.form).then((response) => {
                    this.msgSuccess('新增成功');
                    this.open = false;
                    this.getList();
                });
            }
            // this.form.type = this.queryParams.type;
            // this.$refs['form'].validate((valid) => {
            //     if (valid) {
            //         if (this.form.id != null) {
            //             updateBook(this.form).then((response) => {
            //                 this.msgSuccess('修改成功');
            //                 this.open = false;
            //                 this.getList();
            //             });
            //         } else {
            //             addBook(this.form).then((response) => {
            //                 this.msgSuccess('新增成功');
            //                 this.open = false;
            //                 this.getList();
            //             });
            //         }
            //     }
            // });
        },
        handleUpdate(row) {
            this.disabled = true;
            this.open = true;
            address.getBook({ id: row.id }).then((response) => {
                if (response.code === 200) {
                    this.open = true;
                    this.title = '修改地址';
                    this.form = response.data;
                    this.$nextTick(() => {
                        this.form.addressPCC = [response.data.provinceId, response.data.cityId, response.data.countyId, response.data.town.id];
                    });
                } else {
                    this.$message.error(response.message);
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            return this.$confirm('是否确认删除货主端地址簿编号为"' + row.id + '"的数据项?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    address.delBook({ ids: row.id }).then((res) => {
                        if (res.code == 200) {
                            this.getList();
                            this.$message.success(res.data);
                        }
                    });
                })
                .catch(() => {});
        },
        tabClick(tab) {
            console.log(tab.props.name);
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                queryType: 0,
                type: tab.props.name
            };
            this.getList();
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep {
    .is-defalut {
        color: #eaaa16;
        font-weight: bold;
    }
}
</style>
