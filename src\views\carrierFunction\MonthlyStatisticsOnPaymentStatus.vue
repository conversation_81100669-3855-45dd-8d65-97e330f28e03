<template>
    <div class="app-container">
        <!-- 搜索 -->
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="收款公司" prop="receiveCompany" style="width: 240px">
                    <el-select v-model="queryForm.receiveCompany" clearable filterable placeholder="请选择收款公司" @change="handleQuery">
                        <el-option v-for="item in companyList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="收款类型" prop="businessType" style="width: 230px">
                    <el-select v-model="queryForm.businessType" clearable filterable placeholder="请选择收款类型" @change="handleQuery" style="width: 180px">
                        <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="付款公司" prop="companyName" style="width: 230px">
                    <el-input v-model="queryForm.companyName" clearable placeholder="请输入付款公司" style="width: 200px" @change="handleQuery" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收款日期" prop="remitTime" style="width: 320px">
                    <el-date-picker v-model="queryForm.remitTime" :shortcuts="shortcuts" clearable end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"> </el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10">
                <el-button :disabled="!dataList || dataList.length === 0" icon="el-icon-download" type="warning" @click="exportAll">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="MonthlyStatisticsOnPaymentStatus" @queryTable="getList" />
            </div>
            <column-table
                v-loading="loading"
                :columns="columns"
                :data="dataList"
                :expandRowKeys="expandRowKeys"
                :maxHeight="tableHeight"
                :row-class-name="getRowClass"
                :tree-props="{ children: 'checkRecordList', hasChildren: 'hasChildren' }"
                highlight-current-row
                highlight-selection-row
                rowKey="id"
                show-expand
                @expand-change="getDetails"
            >
                <template #expand="{ props }">
                    <column-table v-loading="childLoading" :columns="columnsChildren" :data="checkRecordList" highlight-current-row highlight-selection-row>
                        <template #discountType="{ row }">
                            <span>{{ selectDictLabel(discountTypeOptions, row.discountType) }}</span>
                        </template>
                        <template #paymentDocType="{ row }">
                            <span>{{ selectDictLabel(paymentDocTypeOptions, row.paymentDocType) }}</span>
                        </template>
                    </column-table>
                </template>
                <template #businessType="{ row }">
                    <span :style="setBusinessTypeColor(row.businessType)">{{ selectDictLabel(businessTypeOptions, row.businessType) }}</span>
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ selectDictLabel(statusOptions, row.status) }}</span>
                </template>
            </column-table>
            <div v-if="total > 0" class="flex justify-end items-center mt-10" style="gap: 0 15px">
                <div class="flex text-red-500 text-base">
                    <span>收款总金额：</span>
                    <span>{{ totalAmount }}</span>
                    <span>元</span>
                </div>
                <pagination v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import { downloadNoData } from '@/utils';
import MonthlyStatisticsOnPaymentStatus from '@/api/carrierEnd/monthlyStatisticsOnPaymentStatus';
import { selectDictLabel } from '@/utils/dictLabel';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'MonthlyStatisticsOnPaymentStatus',
    components: {
        RightToolbar,
        SearchButton,
        ColumnTable
    },
    data() {
        return {
            queryForm: {
                current: 1,
                size: 10,
                receiveCompany: undefined,
                businessType: undefined,
                companyName: undefined,
                remitTime: [],
                beginRemitTime: undefined,
                endRemitTime: undefined
            },
            companyList: [],
            companyIdList: [],
            showSearch: true,
            loading: false,
            dataList: [],
            total: 0,
            isShowAll: false,
            tableHeight: 400,
            totalAmount: 0,
            businessTypeOptions: [],
            statusOptions: [],
            discountTypeOptions: [],
            paymentDocTypeOptions: [],
            expandRowKeys: [],
            columns: [
                { title: '收款公司名称', key: 'receiveCompany', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收款类型', key: 'businessType', align: 'center', minWidth: '90px', columnShow: true },
                { title: '收款日期', key: 'remitTime', align: 'center', minWidth: '150px', columnShow: true },
                { title: '收款金额', key: 'remitAmount', align: 'center', minWidth: '100px', columnShow: true },
                { title: '付款公司', key: 'companyName', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '审批状态', key: 'status', align: 'center', minWidth: '80px', columnShow: true },
                { title: '备注', key: 'remark', minWidth: '80px', columnShow: true, showOverflowTooltip: true }
            ],
            childLoading: false,
            columnsChildren: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '120px', columnShow: true },
                { title: '', key: 'paymentDocType', align: 'center', minWidth: '90px', columnShow: true },
                { title: '月度', key: 'billDate', align: 'center', minWidth: '100px', columnShow: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '100px', columnShow: true },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '100px', columnShow: true },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '100px', columnShow: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '120px', columnShow: true },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '120px', columnShow: true },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '100px', columnShow: true },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true }
            ],
            checkRecordList: [],
            currentBusinessType: '',
            shortcuts: setDatePickerShortcuts()
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 获取付款单类型标题
         */
        paymentDocTypeTitle() {
            return this.currentBusinessType === '1' ? '' : this.currentBusinessType === '2' ? '付款单类型' : '收款单类型';
        },
        /**
         * 设置业务类型颜色
         */
        setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': {}
                    }[businessType] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' }
                    }[status] || { color: '#999' }
                );
            };
        }
    },
    created() {
        this.getDict();
        // 货主下拉接口
        const orgKey = this.$TOOL.data.get('orgKey');
        this.getApplyCompanySelect(this.$TOOL.data.get('Organization')[orgKey].id);
        this.handleQuery();
    },
    methods: {
        /**
         * 导出
         */
        exportAll() {
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    const { remitTime, ...params } = this.queryForm;
                    MonthlyStatisticsOnPaymentStatus.export({ filename: '收款情况月度统计.xls', ...params }, '', '', 'blob')
                        .then((res) => {
                            downloadNoData(res, 'application/vnd.ms-excel', '收款情况月度统计.xlsx');
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 货主下拉接口
         */
        async getApplyCompanySelect(val) {
            const res = await paymentOrderApproval.getApplyCompanySelect(val);
            if (res.code === 200) {
                this.companyIdList = res.data;
            } else {
                this.companyIdList = [];
            }
        },
        /**
         * 展开行
         * @param row
         * @param expandedRows
         */
        getDetails(row, expandedRows) {
            this.childLoading = true;
            this.checkRecordList = [];
            if (expandedRows.length > 0) {
                this.expandRowKeys = [row.id];
                this.currentBusinessType = row.businessType;
                this.columnsChildren = this.columnsChildren.map((col) => {
                    if (col.key === 'paymentDocType') {
                        return { ...col, title: this.paymentDocTypeTitle };
                    }
                    return col;
                });
                if ('id' in row) {
                    MonthlyStatisticsOnPaymentStatus.getDetail(row.id)
                        .then((res) => {
                            if (res.code === 200 && res.data?.length) {
                                this.checkRecordList = res.data;
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.childLoading = false;
                        });
                }
            }
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
            this.businessTypeOptions = await this.getDictList('payment_type');
            this.statusOptions = await this.getDictList('payment_approval_type');
            this.discountTypeOptions = await this.getDictList('discount_type');
            this.paymentDocTypeOptions = await this.getDictList('cost_payment_doc_type');
        },
        /**
         * 获取列表
         */
        getList() {
            this.loading = true;
            const { remitTime, ...params } = this.queryForm;
            MonthlyStatisticsOnPaymentStatus.getList(params)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.tableHeight = window.innerHeight - 227;
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 设置行样式
         */
        getRowClass({ row }) {
            return row.businessType === '1' ? 'expand-hidden' : '';
        },
        /**
         * 获取 收款总金额统计
         */
        getSum() {
            const { remitTime, ...params } = this.queryForm;
            MonthlyStatisticsOnPaymentStatus.getSum(params)
                .then((res) => {
                    if (res.code === 200 && res.data?.totalAmount) {
                        this.totalAmount = res.data.totalAmount;
                    }
                })
                .catch(() => {});
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { remitTime } = this.queryForm;
            this.queryForm.beginRemitTime = remitTime?.[0] ? `${remitTime[0]} 00:00:00` : undefined;
            this.queryForm.endRemitTime = remitTime?.[1] ? `${remitTime[1]} 23:59:59` : undefined;
            this.getList();
            this.getSum();
        },
        /**
         * 重置查询
         */
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.queryForm = {
                current: 1,
                size: 10,
                receiveCompany: undefined,
                businessType: undefined,
                companyName: undefined,
                remitTime: [],
                beginRemitTime: undefined,
                endRemitTime: undefined
            };
            this.handleQuery();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    thead th {
        border-right: none !important;
    }
    .expand-hidden .cell .el-table__expand-icon {
        display: none;
    }
}
</style>
