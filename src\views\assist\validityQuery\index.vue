<template>
    <div>
        <!-- 表头 -->
        <el-card class="box-card Botm" style="margin:10px">
            <el-form :model="queryParams" ref="queryRef" :inline="true" class="form_130">
                <TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
                    <el-form-item label="供应商" prop="suppier">
                        <el-input v-model="queryParams.suppier" placeholder="请输入供应商" size="normal" clearable
                            class="form_225"></el-input>
                    </el-form-item>
                    <el-form-item label="生产厂家" prop="factory">
                        <el-input v-model="queryParams.factory" placeholder="请输入生产厂家" size="normal" clearable
                            class="form_225"></el-input>
                    </el-form-item>
                    <el-form-item label="商品名称" prop="tradeName">
                        <el-input v-model="queryParams.tradeName" placeholder="请输入商品名称" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="经手人" prop="creator">
                        <el-input v-model="queryParams.creator" placeholder="请输入经手人" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="库号" prop="warehouseNumber">
                        <el-input v-model="queryParams.warehouseNumber" placeholder="请输入库号" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="查询方式" prop="updateDate" class="searchItem">
                        <div class="box_date">
                            <div style="display: flex; flex-direction: column;">
                                <el-radio-group v-model="radio1" class="ml-4" @change="handlerDisabled">
                                    <el-radio label="1" size="large">按天数
                                        <el-input v-model="queryParams.validate" class="form_225" :disabled="day"
                                            style="margin-left:11%;" @focus="handleFocus" clearable />
                                        天内到效期的商品
                                    </el-radio>
                                    <el-radio label="2" size="large">按月数
                                        <el-input v-model="queryParams.validate2" class="form_225" :disabled="mounth"
                                            style="margin-left:11%;" @focus="handleFocus2" clearable />
                                        月内到效期的商品
                                    </el-radio>
                                    <el-radio label="3" size="large">指定效期日期
                                        <el-date-picker v-model="queryParams.validate3" type="date" :disabled="appoint"
                                            placeholder="请选择日期" format="YYYY/MM/DD" class="form_225"
                                            style="margin-left:2px;" @focus="handleFocus3" clearable />
                                        前到效期的商品
                                    </el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                    </el-form-item>
                </TopTitle>
            </el-form>
        </el-card>
        <!-- 表单部分 -->
        <el-card style="margin:10px;">
            <RightToptipBarV2 @handleRefresh="getList" className="validityQuery"
                style="float:right;margin-top:0px;margin-bottom:15px;" />
            <DragTableColumn :columns="columns" :tableData="queryList" className="validityQuery"
                v-model:queryParams="queryParams" :getList="getList" v-if="categoryOptions.length">
            </DragTableColumn>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import validityQuery from '@/api/erp/validityQuery'
import TopTitle from '@/components/topTitle';

import moment from 'moment';
import purchasingManagement from '@/api/erp/purchasingManagement'
import manufacturerManagement from '@/api/erp/manufacturerManagement'
const queryList = ref([])
const supplierList = ref([])
const creatList = ref([])
const { proxy } = getCurrentInstance();
const queryRef = ref()
const day = ref(false)
const categoryOptions = ref([])
const mounth = ref(false)
const appoint = ref(false)
// const numDisabled = false
const radio1 = ref('0')
const total = ref(0)
const queryParams = ref({
    current: 1,
    size: 10
})
const columns = ref(
    [
        {
            label: '商品名称',
            prop: 'commodity.tradeName',
            fixed: 'left'
        },
        {
            label: '商品自编码',
            prop: 'commodity.commoditySelfCode'
        }, {
            label: '商品编号',
            prop: 'commodity.commodityCode'
        }, {
            label: '产地',
            prop: 'commodity.producingArea'
        }, {
            label: '生产厂家',
            prop: 'commodity.manufactureName'
        }, {
            label: '供应商',
            prop: 'suppier'
        }
        , {
            label: '有效期(月)',
            prop: 'commodity.validityTime'
        }, {
            label: '生产日期',
            prop: 'produceDate',
            type: 'date'
        },
        {
            label: '有效期',
            prop: 'validate',
            type: 'date'
        },
        {
            label: '到期天数',
            prop: 'endDay'
        }, {
            label: '入库日期',
            prop: 'intoTime',
            type: 'date'
        }, {
            label: '批号',
            prop: 'batchNumber',
        }, {
            label: '入库数量',
            prop: 'intoQuantity',
        }, {
            label: '当前数量',
            prop: 'currentInventory'
        },
        {
            label: '仓库',
            prop:'warehouse',
        },
        {
            label: '库号',
            prop:'warehouseNumber'
        },
        {
            label:'货位',
            prop:'goodsShelves',
        },
        {
            label: '经手人',
            prop: 'creator',
        }, {
            label: '成本单价',
            prop: 'unitPrice'
        }, {
            label: '总金额',
            prop: 'money'
        }, {
            label: '禁用状态',
            prop: 'commodityStatus',
            type: 'status',
            filters: categoryOptions
        },
    ]
)
//效期查询列表
const getList = () => {
    const query = proxy.$route?.query
    if (query && query.id && query.type == 'abnormalTask') {
        validityQuery.getList({id: query.id}).then(res => {
            if (res.code == 200) {
                queryList.value = res.data.records
                total.value = res.data.total
            }
        })
    } else {
        const params = { ...queryParams.value };
        if (params.validate) {
            var now = new Date(); // 获取当前时间
            now.setDate(now.getDate() + Number(params.validate)); // 将用户输入的天数加到当前日期上
            var year = now.getFullYear(); // 年份
            var month = now.getMonth() + 1; // 月份（0-11）
            var date = now.getDate(); // 天数（1到31）
            params.validate = year + "-" + month + "-" + date
            params.validate = moment(params.validate).format('YYYY-MM-DD')
        }
        if (params.validate2) {
            var now = new Date(); // 获取当前时间
            now.setMonth(now.getMonth() + Number(params.validate2)); // 将用户输入的数字加到当前月份上
            var year = now.getFullYear(); // 年份
            var month = now.getMonth() + 1; // 月份（0-11）
            var date = now.getDate(); // 天数（1到31）
            params.validate = year + "-" + month + "-" + date
            params.validate = moment(params.validate).format('YYYY-MM-DD')
            delete params.validate2
        }
        if (params.validate3) {
            params.validate = moment(params.validate3).format('YYYY-MM-DD')
            delete params.validate3
        }
        validityQuery.getList({ ...params }).then(res => {
            if (res.code == 200) {
                queryList.value = res.data.records
                total.value = res.data.total
            }
        })
    }

}
// 重置
const resetQuery = () => {
    proxy.resetForm("queryRef");
    queryParams.value.validate = ''
    queryParams.value.validate2 = ''
    queryParams.value.validate3 = ''
    handleQuery();
}
//搜索
const handleQuery = () => {
	delete proxy.$route?.query
    getList()
}
//供应商
const getSupplier = () => {
    purchasingManagement.getSupplierProduction({ status: 3, customLabel: 2, size: 9999, current: 1 }).then(res => {
        if (res.code == 200) {
            supplierList.value = res.data?.records
        }
    })
}
//生产厂家
const getCreat = () => {
    manufacturerManagement.getList({ status: '3' }).then(res => {
        if (res.code == 200) {
            creatList.value = res.data?.records
        }
    })
}
getCreat()
// 输入框聚焦
const handleFocus = () => {
    day.value = false
    mounth.value = true
    appoint.value = true
}
const handleFocus2 = () => {
    day.value = true
    mounth.value = false
    appoint.value = true
}
const handleFocus3 = () => {
    day.value = true
    mounth.value = true
    appoint.value = false
}
//禁用
const handlerDisabled = (item) => {
    if (item == '1') {
        day.value = false
        mounth.value = true
        appoint.value = true
    } else if (item == '2') {
        day.value = true
        mounth.value = false
        appoint.value = true
    } else {
        day.value = true
        mounth.value = true
        appoint.value = false
    }
}
// 字典
async function dict() {
    categoryOptions.value = await proxy.getDictList('incubator_validity_status')
}
dict()
getList()
getSupplier()
</script>

<style  lang="scss" scoped>
::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

.searchItem {
    margin-top: 20px;
    margin-left: 10px;
}
</style>
