<template>
    <div>
        <el-dialog v-model="visible" :title="auditTitle" top="5vh" width="90%" @close="closeVisible">
            <el-form ref="paymentOrderAuditForm" :model="paymentOrderAuditForm" label-width="auto" style="max-height: 70vh">
                <el-descriptions :column="3" border size="small" title="申请信息">
                    <el-descriptions-item label="收款公司">
                        <span class="whitespace-nowrap">{{ selectDictLabel(companyList, taskInfo.paymentApplyDto.receiveCompany) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="汇款金额">{{ taskInfo.paymentApplyDto.remitAmount }}</el-descriptions-item>
                    <el-descriptions-item label="汇款时间">
                        <span class="whitespace-nowrap">{{ taskInfo.paymentApplyDto.remitTime }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item :span="5" label="付款凭证">
                        <el-image v-for="(item, index) in taskInfo.paymentApplyDto.remitFile" :key="index" :preview-src-list="[item.fileUrl]" :src="item.fileUrl" class="mr-5" fit="cover" style="width: 100px; height: 100px" />
                    </el-descriptions-item>
                </el-descriptions>
                <el-descriptions class="mt-10" size="small" title="付款单明细"></el-descriptions>
                <column-table :columns="paymentOrderAuditColumns" :data="taskInfo.docList" :show-summary="true">
                    <template #companyName="{ row }">
                        {{ setCompanyName(row) }}
                    </template>
                    <template #startDate="{ row }"> {{ row.startDate }} ~ {{ row.endDate }} </template>
                    <template #discountType="{ row }">
                        {{ selectDictLabel(discountTypeList, row.discountType) }}
                    </template>
                    <template #paymentDocType="{ row }">
                        {{ selectDictLabel(paymentDocTypeList, row.paymentDocType) }}
                    </template>
                </column-table>
                <div class="grid items-start" style="padding-bottom: 10px">
                    <div class="flex flex-col">
                        <el-descriptions size="small" border title="审批" style="padding-bottom: 10px">
                            <el-descriptions-item label="申请人">{{ taskInfo.paymentApplyDto.createBy.name }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ taskInfo.paymentApplyDto.createDate }}</el-descriptions-item>
                        </el-descriptions>
                        <audit-and-flow-records v-show="taskInfo?.approveList?.length > 0" ref="AuditAndFlowRecords"></audit-and-flow-records>
                    </div>
                </div>
                <div class="grid items-start" style="grid-template-columns: minmax(0, 1fr) minmax(0, 1fr); padding-bottom: 10px">
                    <div class="flex flex-col">
                        <div v-if="auditInfo.auditStatus == '0' && type === 'examine'">
                            <el-form-item :rules="[{ required: true, message: '请选择审批意见', trigger: 'change' }]" label="审批意见" prop="status">
                                <el-radio-group v-model="paymentOrderAuditForm.status">
                                    <el-radio label="1">通过</el-radio>
                                    <el-radio label="2">驳回</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item v-if="paymentOrderAuditForm.status === '2'" :rules="[{ required: true, message: '请输入驳回原因', trigger: 'blur' }]" label="驳回原因" prop="auditIdea">
                                <el-input v-model="paymentOrderAuditForm.auditIdea" :rows="4" maxlength="500" placeholder="请输入驳回原因" show-word-limit type="textarea"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </el-form>
            <template #footer v-if="auditInfo.auditStatus == '0'">
                <div v-loading="paymentOrderAuditLoading" style="text-align: center">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm()">提 交</el-button>
                </div>
            </template>
        </el-dialog>
        <el-image-viewer v-if="viewerVisible" v-model="viewerVisible" :url-list="viewerList" @close="viewerVisible = false" />
    </div>
</template>
<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import { tasks } from '@/api/auditManagement/approvalTask';
import AuditAndFlowRecords from '@/views/auditManagement/AuditAndFlowRecords';

export default {
    name: 'PaymentOrderAudit',
    components: { AuditAndFlowRecords, ColumnTable },
    model: {
        prop: 'paymentOrderAuditVisible',
        event: 'update:paymentOrderAuditVisible'
    },
    props: {
        auditInfo: {},
        auditTitle: {
            type: String,
            default: undefined
        },
        paymentOrderAuditVisible: {
            type: Boolean,
            default: false
        },
        taskInfo: {},
        type: {
            type: String,
            default: 'details'
        }
    },
    data() {
        return {
            paymentOrderAuditForm: {
                status: undefined,
                auditIdea: undefined
            },
            paymentOrderAuditLoading: false,
            visible: this.paymentOrderAuditVisible,
            viewerVisible: false,
            viewerList: [],
            paymentOrderAuditColumns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'paymentDocType', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'startDate', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '纸箱费用', key: 'cartonFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '垫付费用', key: 'advanceFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '租箱费用', key: 'rentalBoxFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '其他费用', key: 'otherFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '费用说明', key: 'feeDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单未收合计', key: 'unpaidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '坏账总金额', key: 'badDebtCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true }
            ],
            companyList: [],
            discountTypeList: [],
            paymentDocTypeList: [] // 付款方式
        };
    },
    computed: {
        /**
         * 设置公司名称
         */
        setCompanyName() {
            return (row) => {
                const Organization = this.$TOOL.data.get('Organization');
                const orgKey = this.$TOOL.data.get('orgKey');
                const { carrierId, companyId } = row;
                if (carrierId == Organization[orgKey].id) {
                    return row.companyName;
                } else if (companyId == Organization[orgKey].id) {
                    return row.carrierName;
                }
            };
        }
    },
    watch: {
        paymentOrderAuditVisible: {
            handler(val) {
                this.visible = val;
            },
            immediate: true
        }
    },
    created() {
        this.getDict();
        this.$nextTick(() => {
            if (this.taskInfo.approveList.length > 0) {
                this.$refs.AuditAndFlowRecords.timeFns(this.taskInfo.approveList);
            }
        });
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:paymentOrderAuditVisible', false);
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
            this.discountTypeList = await this.getDictList('discount_type');
            this.paymentDocTypeList = await this.getDictList('cost_payment_doc_type'); //结算-收款单类型
        },
        /**
         * 图片预览
         * @param file
         */
        handlePictureCardPreview(file) {
            this.viewerVisible = true;
            this.viewerList = [];
            this.viewerList.push(file.url);
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.paymentOrderAuditForm.validate(async (valid) => {
                if (valid) {
                    const params = {
                        taskId: this.auditInfo.id,
                        auditIdea: this.paymentOrderAuditForm.auditIdea,
                        status: this.paymentOrderAuditForm.status
                    };
                    tasks
                        .auditSubmit(params)
                        .then((res) => {
                            if (res.code == 200) {
                                this.$message.success('操作成功');
                                this.$emit('success');
                                this.closeVisible();
                            }
                        })
                        .finally(() => {
                            this.preDepositTopUpAuditLoading = false;
                        });
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep {
    .el-descriptions__header {
        margin-bottom: 10px;
    }
    .el-descriptions__cell.el-descriptions__label {
        white-space: nowrap;
    }
}
.number__unit__element {
    position: relative;
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
