<template>
  <div v-loading="loading" style="background-color: rgb(245, 247, 253); padding: 10px">
    <el-card class="mb10" shadow="never">
      <div style="display: flex;justify-content: space-between">
        <span>订单号：{{ mainInfo.orderNo }}</span>
        <div>货物总件数：<span class="hy-font-24 hy-weight hy-c-red">{{ mainInfo.goodsPackages }}</span></div>
        <span v-if="mainInfo.transOrderNo">运单号：{{ mainInfo.transOrderNo }}</span>
      </div>
    </el-card>
    <el-collapse v-model="activeName" accordion @change="collapseChange">
    <el-timeline>
      <el-timeline-item v-for="(record, index) in recordInfo" :timestamp="record.transMainStatusDesc" placement="top">
        <el-card>

          <el-timeline>

            <el-timeline-item :hide-timestamp="true" v-for="(detail, i) in record.recordDetailList"
              :key="'child_'+i"
              :timestamp="detail.transDetailStatusDesc + ' ' +formatDate(detail.businessTime)"
              placement="top"
              :class="i == record.recordDetailList.length-1?'':'hy-pass-node'"
            >
              <el-collapse-item  :name="index+'_'+i">
              <template slot="title">
                <span style="color: #999999;font-size: 13px;">{{ detail.transDetailStatusDesc + ' ' +formatDate(detail.businessTime) }}</span>
              </template>
              <p style="color: #999999;font-size: 12px;">{{ detail.detailStatusDesc }}</p>
              <el-card class="mb10" shadow="never"  v-loading="detail.loading">
                <el-row :gutter="24">
                  <el-col :span="12" v-if="detail.belongUserName"><div>经办人：<span class="hy-font-18 hy-weight">{{ detail.belongUserName }}</span></div></el-col>
                  <el-col :span="12" v-if="detail.branchName"><div>网点：<span class="hy-font-18 hy-weight">{{ detail.branchName }}</span></div></el-col>
                  <el-col :span="12" v-if="detail.areaName"><div>集货区：<span class="hy-font-18 hy-weight">{{ detail.areaName }}</span></div></el-col>
                  <el-col :span="12" v-if="detail.incubatorNo"><div>保温箱号：<span class="hy-font-18 hy-weight">{{ detail.incubatorNo }}</span></div></el-col>
                  <el-col :span="12" v-if="detail.deviceNo"><div>传感器号：<span class="hy-font-18 hy-weight">{{ detail.deviceNo }}</span></div></el-col>
                  <el-col :span="12" v-if="detail.temperatureLower"><div>低温阈值：<span class="hy-font-18 hy-weight hy-c-red">{{ detail.temperatureLower }}</span>℃</div></el-col>
                  <el-col :span="12" v-if="detail.temperatureCeiling"><div>高温阈值：<span class="hy-font-18 hy-weight hy-c-red">{{ detail.temperatureCeiling }}</span>℃</div></el-col>
                  <el-col :span="12" v-if="detail.driverName"><div>司机：<span class="hy-font-18 hy-weight">{{ detail.driverName }}</span></div></el-col>
                  <el-col :span="12" v-if="detail.goodsPackages"><div>货物件数：<span class="hy-font-24 hy-weight hy-c-red">{{ detail.boxCodeList&&detail.boxCodeList.length>0?detail.boxCodeList.length:detail.goodsPackages }}</span>件</div></el-col>
                  <el-col :span="12" v-if="detail.carCode"><div>车牌号：<span class="hy-font-18 hy-weight">{{ detail.carCode }}</span></div></el-col>
                  <el-col :span="12" v-if="detail.taskCode"><div>业务编码：{{ detail.taskCode }}</div></el-col>
                  <el-col :span="12" v-if="detail.taskSource"><div>任务来源：{{ driverTaskSourceFormatting(detail) }}</div></el-col>
                  <el-col :span="12" v-if="detail.taskType"><div>任务类型：{{ taskTypeFormatting(detail) }}</div></el-col>
                  <el-col :span="12" v-if="detail.transWay"><div>运输方式：{{ mainRecordTransWayFormatting(detail) }}</div></el-col>
                  <el-col :span="12" v-if="detail.temperatureTypeDesc"><div>温层类型：{{ detail.temperatureTypeDesc }}</div></el-col>
                  <el-col :span="12" v-if="detail.productTypeDesc"><div>产品类型：{{ detail.productTypeDesc }}</div></el-col>
                  <el-col :span="12" v-if="detail.orderTypeDesc"><div>货物揽收方式：{{ detail.orderTypeDesc }}</div></el-col>
                  <el-col :span="12" v-if="detail.productClassDesc"><div>产品分类：{{ detail.productClassDesc }}</div></el-col>
                </el-row>
                <el-row :gutter="24" style="background: #eae9e9;" v-if="detail.incubatorList && detail.incubatorList.length > 0" v-for="row in detail.incubatorList">
                  <el-col :span="6" v-if="row.incubatorNo">
                    <div>保温箱号：<span class="hy-font-18 hy-weight">{{ row.incubatorNo }}</span></div>
                  </el-col>
                  <el-col :span="6" v-if="row.deviceNo">
                    <div>温湿度设备号：<span class="hy-font-18 hy-weight">{{ row.deviceNo }}</span></div>
                  </el-col>
                  <el-col :span="6" v-if="row.temperatureCeiling && row.temperatureLower">
                    <div>温度阈值：<span class="hy-font-18 hy-weight hy-c-red">{{ row.temperatureCeiling }}</span>℃-<span class="hy-font-18 hy-weight hy-c-red">{{ row.temperatureLower }}</span>℃</div>
                  </el-col>
                  <el-col :span="6" v-if="row.createBy">
                    <div>操作员：<span class="hy-font-18 hy-weight">{{ row.createBy }}</span></div>
                  </el-col>
                  <el-col :span="24" v-if="row.countyList" v-for="county in row.countyList" style="background: #eae9e9;">
                    <div>地区:<span>{{ county?.province || ''}}{{ county?.city|| '' }}{{ county?.county|| '' }}</span></div>
                    <el-row :gutter="24" style="background: #f8f8f8;" v-if="county.countyOrderList && county.countyOrderList.length > 0" v-for="order in county.countyOrderList">
                      <el-col :span="10" v-if="order.transOrderNo">
                        <div>运单号：<span class="hy-font-18 hy-weight">{{ order.transOrderNo }}</span></div>
                      </el-col>
                      <el-col :span="10" v-if="order.orderNo">
                        <div>订单号：<span class="hy-font-18 hy-weight">{{ order.orderNo }}</span></div>
                      </el-col>
                      <el-col :span="22" v-if="order.bindDetailList">
                        <el-table
                          :data="order.bindDetailList"
                          style="width: 80%;margin: auto;" v-if="order.bindDetailList && order.bindDetailList.length > 0">
                          <el-table-column
                            label="箱码"
                            prop="code">
                          </el-table-column>
                        </el-table>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                <el-table
                  :data="detail.boxCodeList"
                  v-if="detail.boxCodeList && detail.boxCodeList.length > 0"
                  style="width: 100%;margin-top: 10px;">
                  <el-table-column
                    prop="code"
                    label="箱码">
                  </el-table-column>
<!--                  <el-table-column-->
<!--                    prop="name"-->
<!--                    label="货物描述">-->
<!--                  </el-table-column>-->
                </el-table>
              </el-card>
              </el-collapse-item>
            </el-timeline-item>

          </el-timeline>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    </el-collapse>
  </div>
</template>

<script>
// import { getTransRecordDetail, getMainStatusSelect, getAllDetailSelect,getRecordDetails } from '@/api/fourpl/trajectoryTracking'
import Template from '@/views/print/xmlTemplate/template';
// import Template from "../../../print/template";

export default {
  name: 'trajectoryTrackingDetail',
  components: {Template},
  props: {
    mainInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      recordInfo: [],
      fourplDriverTaskSourceOptions: [], // 4PL配送任务来源字典值
      fourplMainRecordTransWayOptions: [], // 运输方式字典值
      fourplTaskTypeOptions: [], // 任务类型字典值
      mainStatusOptions: [], // 大状态数据
      detailStatusOptions: [], // 细状态数据
      activeName:'0_0',
    }
  },
  created() {
    /** 4PL运单记录详情来源字典 */
    this.getDicts('fourpl_trans_detail_source').then((response) => {
      this.fourplDriverTaskSourceOptions = response.data
    })
    /** 运输方式 */
    this.getDicts('fourpl_main_record_trans_way').then((response) => {
      this.fourplMainRecordTransWayOptions = response.data
    })

    /** 任务类型 */
    this.getDicts('fourpl_task_type').then((response) => {
      this.fourplTaskTypeOptions = response.data
    })
    this.getMainStatusSelect()
    this.getAllDetailSelect()
    this.getTransRecordDetail()
  },
  methods: {
    collapseChange(){
      if(this.activeName){
        let arr = this.activeName.split('_');
        let x = arr[0],y = arr[1];
        if(!this.recordInfo[x].recordDetailList[y].boxCodeList && !this.recordInfo[x].recordDetailList[y].belongUserName ){
          this.getRecordDetails(x,y)
        }
      }

    },
    // 运单状态明细详情
    getRecordDetails(index,i){
      this.$set(this.recordInfo[index].recordDetailList[i],'loading',true);
      getRecordDetails({...this.recordInfo[index].recordDetailList[i]}).then((response) => {
        if (response.code === 200 && response.data) {
          this.$set(this.recordInfo[index].recordDetailList,i,{...this.recordInfo[index].recordDetailList[i],...response.data,loading:false});
        }
      })
    },
    // 获取细状态
    getAllDetailSelect() {
      getAllDetailSelect({}).then((response) => {
        if (response.code === 200 && response.data) {
          this.detailStatusOptions = response.data
        }
      })
    },
    /*查询大状态*/
    getMainStatusSelect() {
      getMainStatusSelect().then((response) => {
        if (response.code === 200 && response.data) {
          this.mainStatusOptions = response.data
        }
      })
    },
    /*获取轨迹跟踪详情*/
    getTransRecordDetail() {
      this.loading = true
      getTransRecordDetail({ mainId: this.mainInfo.id }).then((response) => {
        if (response.code === 200 && response.data) {
          let collapseIndex = 0;
          this.recordInfo = response.data;
          this.getRecordDetails(0,0);
        }
        setTimeout(() => {
          this.loading = false
        }, 500)
      }).catch(e => {
        this.loading = false
      })
    },
    // 标准时间格式化
    formatDate(cellValue) {
      return formatDate(cellValue)
    },
    /** 4PL配送任务来源 翻译*/
    driverTaskSourceFormatting(row) {
      return this.selectDictLabel(this.fourplDriverTaskSourceOptions, row.taskSource)
    },
    /** 运输方式 翻译*/
    mainRecordTransWayFormatting(row) {
      return this.selectDictLabel(this.fourplMainRecordTransWayOptions, row.transWay)
    },
    /** 任务类型 翻译*/
    taskTypeFormatting(row) {
      return this.selectDictLabel(this.fourplTaskTypeOptions, row.taskType)
    }
  }

}
</script>

<style scoped lang="scss">
.el-timeline .hy-pass-node ::v-deep.el-timeline-item__tail{
    display: block;
  }
.el-collapse{
  border:0;
}
::v-deep .el-collapse-item__header{
  height: auto;
  line-height:initial;
  border-bottom:0;
}
::v-deep .el-timeline-item{
  padding: 10px 0;
}
::v-deep .el-collapse-item__wrap{
  border: 0;
}
::v-deep{
  .hy-font-18{
    font-size: 18px;
  }
  .hy-font-24{
    font-size: 24px;
  }
  .hy-weight{
    font-weight: bold;
  }
  .hy-c-red{
    color: red;
  }
}
</style>
