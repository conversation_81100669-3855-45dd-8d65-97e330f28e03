import http from "@/utils/request"

export default {
	/*
	 * 用户登录
	 * inputForm:{username,password}
	 * @author: 路正宁
	 * @date: 2023-03-21 10:40:15
	 * @version: V1.0.5
	*/
	login: function (inputForm) {
		return http.post(
		  `/auth/sys/login`,
		  inputForm
		)
	},
	// 获取下载app地址
	getAppDownloadUrl: function (appId) {
		return http.get(
		  '/auth/getAppUrl?appId=' + appId
		)
	},
	// 获取图片验证码
	getCode: function () {
		return http.get(
			'/auth/user/getCode'
		)
	},
}
