import request from '@/utils/request'
export default {
	// 查询可用增值服务下拉框数据
	addedServiceAllList: function (params) {
		return request.get('/cost/addedService/config/usable', params);
	},
	// 获取增值服务列表
	addedServiceListCarrier: function (params) {
		return request.get('/cost/addedService/carrier/list', params);
	},
	// 翻译公式
	translateCarrierPriceBookFormula: function (params) {
		return request.post('/cost/addedService/carrier/translateFormulas', params);
	},
	// 新增增值服务
	addAddedServiceCarrier: function (params) {
		return request.post('/cost/addedService/carrier/save', params);
	},
	// 修改状态
	updateCarrierAddedServiceStatus: function (params) {
		return request.get('/cost/addedService/carrier/updateStatus', params);
	},
	// 获取增值服务详情
	addedServiceDetailCarrier: function (params) {
		return request.get('/cost/addedService/carrier/queryById', params);
	},
	// 修改增值服务
	updateAddedServiceCarrier: function (params) {
		return request.post('/cost/addedService/carrier/edit', params);
	},
	// 发布增值服务
	releaseAddedService:function (params) {
		return request.post('/cost/addedService/carrier/published', params);
	},
	// 注销增值服务
	logoutAddedService:function (params) {
		return request.get('/cost/addedService/carrier/loggedout', params);
	},
	// 删除增值服务
	deleteAddedServiceCarrier:function (params) {
		return request.delete('/cost/addedService/carrier/delete', params);
	},
	// 修改已发布
	editAddedServiceFormula:function (params) {
		return request.post('/cost/addedService/carrier/update/condition', params);
	},
	// 查询已配置增值服务的货主
	queryShippersByAddedService:function (addedServiceId) {
		return request.get(`/cost/owner/added/service/owner/list/${addedServiceId}`);
	},
	// 增加货主
	addShipper:function (params) {
		return request.post('/cost/owner/added/service/add', params);
	}
}
