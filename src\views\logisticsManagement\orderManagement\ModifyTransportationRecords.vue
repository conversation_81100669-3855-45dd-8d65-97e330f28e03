<template>
    <div>
        <el-card v-loading="loading" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="reviewAndModifyForm" :model="reviewAndModifyForm" :rules="reviewAndModifyRules" label-width="auto">
                <el-form-item v-if="reviewAndModifyForm.carrierWay === '1'" label="运输工具" prop="tool">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px">
                        <el-tag v-for="(tag, index) in reviewAndModifyForm.tool" :key="index" :disable-transitions="false" closable size="large" @close="(e) => handleClose(e, index, 'tool')">{{ tag }}</el-tag>
                        <el-input
                            v-if="inputVisibleTransportVehicles"
                            ref="inputRefTransportVehicles"
                            v-model="inputValueTransportVehicles"
                            maxlength="20"
                            show-word-limit
                            style="width: auto"
                            @blur="handleInputConfirm('inputRefTransportVehicles')"
                            @keyup.enter="handleInputConfirm('inputRefTransportVehicles')"
                        />
                        <el-button v-else @click="showInput('inputRefTransportVehicles')">+ 添加</el-button>
                    </div>
                </el-form-item>
                <el-form-item v-if="reviewAndModifyForm.carrierWay === '1'" label="设备号" prop="deviceNo">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px">
                        <el-tag v-for="(tag, index) in reviewAndModifyForm.deviceNo" :key="index" :disable-transitions="false" closable size="large" @close="(e) => handleClose(e, index, 'deviceNo')">{{ tag }}</el-tag>
                        <el-input
                            v-if="inputVisibleEquipmentNumber"
                            ref="inputRefEquipmentNumber"
                            v-model="inputValueEquipmentNumber"
                            maxlength="20"
                            show-word-limit
                            style="width: auto"
                            @blur="handleInputConfirm('inputRefEquipmentNumber')"
                            @keyup.enter="handleInputConfirm('inputRefEquipmentNumber')"
                        />
                        <el-button v-else @click="showInput('inputRefEquipmentNumber')">+ 添加</el-button>
                    </div>
                </el-form-item>
                <!-- carrierWay 运输方式 1 自行运输 2 委托运输 -->
                <!-- driverType 司机类型 0 正式 1 外包 -->
                <!-- 自行运输时，如果运输人是外包司机，需要维护填写三方公司和三方单号 -->
                <el-form-item :rules="carrierWayDriverTypeValidation" label="承运单位" prop="flowName">
                    <el-select v-model="reviewAndModifyForm.flowName" :disabled="isCarrierWayDisabled" :placeholder="placeholderForFlowName" class="w-50" clearable filterable>
                        <el-option v-for="item in flowNameList" :key="item.id" :label="item.flowName" :value="item.flowName" />
                    </el-select>
                </el-form-item>
                <el-form-item :rules="carrierNoValidation" label="承运单编号" prop="thirdWaybillNo">
                    <el-input v-model="reviewAndModifyForm.thirdWaybillNo" :disabled="isCarrierWayDisabled" :placeholder="carrierNoPlaceholder" class="w-50" clearable />
                </el-form-item>

                <div style="display: flex; gap: 0 10px">
                    <el-form-item label="启动时间" prop="startShipmentTime">
                        <el-date-picker v-model="reviewAndModifyForm.startShipmentTime" :disabled-date="disabledDate" placeholder="选择启动时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="启动温度" prop="startShipmentTemp">
                        <el-input-number v-model="reviewAndModifyForm.startShipmentTemp" :max="300" :min="-100" :precision="1" class="number__unit__element" controls-position="right" placeholder="请输入" style="width: 160px"></el-input-number>
                    </el-form-item>
                </div>
                <div style="display: flex; gap: 0 10px">
                    <el-form-item label="送达时间" prop="deliveryTime">
                        <el-date-picker v-model="reviewAndModifyForm.deliveryTime" :disabled-date="disabledDate" placeholder="选择送达时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
                    </el-form-item>
                    <el-form-item label="送达温度" prop="deliveryTemp">
                        <el-input-number v-model="reviewAndModifyForm.deliveryTemp" :max="300" :min="-100" :precision="1" class="number__unit__element" controls-position="right" placeholder="请输入" style="width: 160px"></el-input-number>
                    </el-form-item>
                </div>
                <el-form-item label="随货同行单" prop="shdFileList">
                    <the-file-upload :accept="accept" :actions="actions" :data="setFileName('shdFileList')" :value="reviewAndModifyForm.shdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'shdFileList')" />
                </el-form-item>
                <el-form-item label="回执单" prop="hzdFileList">
                    <the-file-upload :accept="accept" :actions="actions" :data="setFileName('hzdFileList')" :value="reviewAndModifyForm.hzdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'hzdFileList')" />
                </el-form-item>
                <el-form-item label="客户签名" prop="khqmFileList">
                    <the-file-upload :accept="accept" :actions="actions" :data="setFileName('khqmFileList')" :value="reviewAndModifyForm.khqmFileList" list-type="picture-card" @input="receiptUploadChange($event, 'khqmFileList')" />
                </el-form-item>
                <el-form-item label="运输温度" prop="yswdFileList">
                    <the-file-upload :accept="accept" :actions="['remove']" :data="setFileName('yswdFileList')" :value="reviewAndModifyForm.yswdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'yswdFileList')" />
                </el-form-item>
                <el-form-item label="提货温度" prop="thwdFileList">
                    <the-file-upload :accept="accept" :actions="['remove']" :data="setFileName('thwdFileList')" :value="reviewAndModifyForm.thwdFileList" list-type="picture-card" @input="receiptUploadChange($event, 'thwdFileList')" />
                </el-form-item>
            </el-form>
        </el-card>
        <el-affix position="bottom">
            <el-card v-loading="loading" :body-style="{ padding: '10px' }" shadow="hover">
                <div style="display: flex; justify-content: end">
                    <el-button @click="close">取 消</el-button>
                    <el-button type="primary" @click="saveData">保 存</el-button>
                </div>
            </el-card>
        </el-affix>
    </div>
</template>
<script>
import transportationRecordsPage from '@/api/carrierEnd/transportationRecordsPage';
import pharmaceuticalloading from '@/api/waybillManagement/pharmaceuticalloading';
import TheFileUpload from '@/components/theFileUpload/TheFileUpload.vue';

export default {
    name: 'ModifyTransportationRecords',
    components: { TheFileUpload },
    props: {
        parameters: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            reviewAndModifyForm: {
                tool: [],
                deviceNo: [],
                flowName: undefined,
                thirdWaybillNo: undefined,
                startShipmentTime: undefined,
                deliveryTime: undefined,
                startShipmentTemp: undefined,
                deliveryTemp: undefined,
                shdFileList: [],
                hzdFileList: [],
                khqmFileList: [],
                yswdFileList: [],
                thwdFileList: []
            },
            reviewAndModifyRules: {
                tool: [{ required: true, message: '请输入运输工具', trigger: 'blur' }],
                deviceNo: [{ required: true, message: '请输入设备号', trigger: 'blur' }],
                startShipmentTime: [
                    { required: false, message: '请选择启动时间', trigger: 'blur,change' },
                    {
                        validator: (rule, value, callback) => {
                            if (value && this.reviewAndModifyForm.deliveryTime) {
                                if (value > this.reviewAndModifyForm.deliveryTime) {
                                    callback(new Error('启动时间不能大于送达时间'));
                                } else {
                                    callback();
                                }
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                deliveryTime: [
                    { required: false, message: '请选择送达时间', trigger: 'blur,change' },
                    {
                        validator: (rule, value, callback) => {
                            if (value && this.reviewAndModifyForm.startShipmentTime) {
                                if (value < this.reviewAndModifyForm.startShipmentTime) {
                                    callback(new Error('送达时间不能小于启动时间'));
                                } else {
                                    callback();
                                }
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                startShipmentTemp: [{ required: false, message: '请输入启动温度', trigger: 'blur' }],
                deliveryTemp: [{ required: false, message: '请输入送达温度', trigger: 'blur' }]
            },
            inputVisibleTransportVehicles: false,
            inputValueTransportVehicles: undefined,
            inputVisibleEquipmentNumber: false,
            inputValueEquipmentNumber: undefined,
            loading: false,
            accept: 'image/*,.pdf',
            actions: ['preview', 'remove'],
            flowNameList: []
        };
    },
    computed: {
        carrierNoPlaceholder() {
            return this.reviewAndModifyForm.carrierWay === '1' && this.reviewAndModifyForm.driverType === '2' ? '请输入承运单编号' : '';
        },
        carrierNoValidation() {
            return this.reviewAndModifyForm.carrierWay === '1' && this.reviewAndModifyForm.driverType === '2' ? [{ required: true, message: '请输入承运单编号', trigger: 'blur' }] : [];
        },
        carrierWayDriverTypeValidation() {
            return this.reviewAndModifyForm.carrierWay === '1' && this.reviewAndModifyForm.driverType === '2' ? [{ required: true, message: '请选择承运单位', trigger: 'change' }] : [];
        },
        /**
         * 是否禁用承运单位
         * @returns {boolean}
         */
        isCarrierWayDisabled() {
            const { carrierWay, driverType } = this.reviewAndModifyForm;

            // 检查 driverType 是否存在
            const hasDriverType = 'driverType' in this.reviewAndModifyForm;

            return carrierWay === '2' || (carrierWay === '1' && (!hasDriverType || driverType === '0'));
        },
        placeholderForFlowName() {
            return this.reviewAndModifyForm.carrierWay === '1' && this.reviewAndModifyForm.driverType === '2' ? '请选择承运单位' : ' ';
        },
        setFileName() {
            return (type) => {
                let fileName = '';
                if (this.parameters?.transOrderNo) {
                    fileName += this.parameters.transOrderNo + '_';
                }

                if (type === 'shdFileList') {
                    fileName += '随货同行单';
                } else if (type === 'hzdFileList') {
                    fileName += '回执单';
                } else if (type === 'khqmFileList') {
                    fileName += '客户签名';
                } else if (type === 'yswdFileList') {
                    fileName += '运输温度';
                } else if (type === 'thwdFileList') {
                    fileName += '提货温度';
                }
                return { fileName };
            };
        }
    },
    created() {
        if (this.parameters?.id) {
            // 获取编辑数据
            this.getEditData();
        } else if (this.parameters.transportationRecordData) {
            this.checkWhether(this.parameters?.transportationRecordData?.isDevice || '0', this.parameters?.transportationRecordData?.tempType);
            this.reviewAndModifyForm = this.parameters.transportationRecordData;
            this.reviewAndModifyForm = {
                ...this.parameters.transportationRecordData,
                yswdFileList: this.parameters.transportationRecordData.yswdFileList.map((item) => {
                    return {
                        ...item,
                        url: item.fileUrl,
                        name: item.fileName
                    };
                }),
                thwdFileList: this.parameters.transportationRecordData.thwdFileList.map((item) => {
                    return {
                        ...item,
                        url: item.fileUrl,
                        name: item.fileName
                    };
                })
            };
            if (this.reviewAndModifyForm.tempType === '1') {
                this.reviewAndModifyRules.startShipmentTemp = [];
                this.reviewAndModifyRules.deliveryTemp = [];
                this.reviewAndModifyRules.yswdFileList = [];
                this.reviewAndModifyRules.thwdFileList = [];
            }
        }
        // 获取承运单位列表
        this.thirdLogistics();
    },
    methods: {
        checkWhether(isDevice, tempType) {
            if (isDevice === '1' || (tempType != '3' && tempType != '4')) {
                this.reviewAndModifyRules.startShipmentTime[0].required = false;
                this.reviewAndModifyRules.deliveryTime[0].required = false;
                this.reviewAndModifyRules.startShipmentTemp[0].required = false;
                this.reviewAndModifyRules.deliveryTemp[0].required = false;
            }
            if(tempType !== '3' && tempType !== '4'){
                this.reviewAndModifyRules.deviceNo[0].required = false;
            }
        },
        /**
         * 关闭弹窗
         */
        close() {
            this.$emit('close');
        },
        disabledDate(time) {
            return time.getTime() > Date.now();
        },
        /**
         * 获取编辑数据
         */
        getEditData() {
            // 如果 parameters.tempType 为 1 修改 this.reviewAndModifyRules startShipmentTemp deliveryTemp yswdFileList thwdFileList 为非必填
            if (this.parameters.tempType === '1') {
                this.reviewAndModifyRules.startShipmentTemp = [];
                this.reviewAndModifyRules.deliveryTemp = [];
                this.reviewAndModifyRules.yswdFileList = [];
                this.reviewAndModifyRules.thwdFileList = [];
            }

            this.loading = true;
            transportationRecordsPage
                .getEditData({
                    id: this.parameters.id
                })
                .then((response) => {
                    if (response.code === 200) {
                        this.checkWhether(response.data?.isDevice || '0', response.data?.tempType);
                        // 处理 response.data tool 和 deviceNo
                        // 1. 将 tool 和 deviceNo 转换为数组
                        // 2. 如果是空字符串，转换为 []
                        if (response.data) {
                            response.data.tool = response.data?.tool ? response.data.tool.split(',') : [];
                            response.data.deviceNo = response.data?.deviceNo ? response.data.deviceNo.split(',') : [];
                            const { shdFileList, khqmFileList, hzdFileList, yswdFileList, thwdFileList } = response.data;
                            this.reviewAndModifyForm = {
                                ...response.data,
                                shdFileList: shdFileList.map((item) => {
                                    return {
                                        ...item,
                                        url: item.fileUrl,
                                        name: item.fileName
                                    };
                                }),
                                khqmFileList: khqmFileList.map((item) => {
                                    return {
                                        ...item,
                                        url: item.fileUrl,
                                        name: item.fileName
                                    };
                                }),
                                hzdFileList: hzdFileList.map((item) => {
                                    return {
                                        ...item,
                                        url: item.fileUrl,
                                        name: item.fileName
                                    };
                                }),
                                yswdFileList: yswdFileList.map((item) => {
                                    return {
                                        ...item,
                                        url: item.fileUrl,
                                        name: item.fileName
                                    };
                                }),
                                thwdFileList: thwdFileList.map((item) => {
                                    return {
                                        ...item,
                                        url: item.fileUrl,
                                        name: item.fileName
                                    };
                                })
                            };
                        }
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 删除标签
         * @param tag
         * @param index
         * @param type
         */
        handleClose(tag, index, type) {
            if (type === 'tool') {
                this.reviewAndModifyForm.tool.splice(index, 1);
            } else if (type === 'deviceNo') {
                this.reviewAndModifyForm.deviceNo.splice(index, 1);
            }
        },
        /**
         * 确定输入
         * @param type
         */
        handleInputConfirm(type) {
            if (type === 'inputRefTransportVehicles') {
                const inputValue = this.inputValueTransportVehicles;
                if (inputValue) {
                    this.reviewAndModifyForm.tool.push(inputValue);
                }
                this.inputVisibleTransportVehicles = false;
                this.inputValueTransportVehicles = undefined;
            } else if (type === 'inputRefEquipmentNumber') {
                const inputValue = this.inputValueEquipmentNumber;
                if (inputValue) {
                    this.reviewAndModifyForm.deviceNo.push(inputValue);
                }
                this.inputVisibleEquipmentNumber = false;
                this.inputValueEquipmentNumber = undefined;
            }
        },
        /**
         * 上传成功
         * @param data
         * @param type
         */
        receiptUploadChange(data, type) {
            this.reviewAndModifyForm[type] = data;
        },
        /**
         * 保存数据
         */
        saveData() {
            this.$refs['reviewAndModifyForm'].validate((valid) => {
                if (valid) {
                    this.loading = true;

                    // 1. 处理 this.reviewAndModifyForm 的 tool 和 deviceNo 将数组转换为字符串
                    // 2. 如果是空数组，转换为 ''
                    const tool = this.reviewAndModifyForm.tool.length ? this.reviewAndModifyForm.tool.join(',') : '';
                    const deviceNo = this.reviewAndModifyForm.deviceNo.length ? this.reviewAndModifyForm.deviceNo.join(',') : '';
                    // 3. 处理 this.reviewAndModifyForm 的 hzdFileList shdFileList yswdFileList thwdFileList
                    // 4. 将 url 和 name 转换为 fileUrl 和 fileName
                    // 5. fileType 1-hzdFileList 3-shdFileList 4-yswdFileList 5-thwdFileList
                    const hzdFileList = this.reviewAndModifyForm.hzdFileList.map((item) => {
                        return {
                            transOrderNo: this.reviewAndModifyForm.transOrderNo,
                            fileUrl: item.url,
                            fileName: item.name,
                            fileType: '1'
                        };
                    });
                    const khqmFileList = this.reviewAndModifyForm.khqmFileList.map((item) => {
                        return {
                            transOrderNo: this.reviewAndModifyForm.transOrderNo,
                            fileUrl: item.url,
                            fileName: item.name,
                            fileType: '2'
                        };
                    });

                    const shdFileList = this.reviewAndModifyForm.shdFileList.map((item) => {
                        return {
                            transOrderNo: this.reviewAndModifyForm.transOrderNo,
                            fileUrl: item.url,
                            fileName: item.name,
                            fileType: '3'
                        };
                    });
                    const yswdFileList = this.reviewAndModifyForm.yswdFileList.map((item) => {
                        return {
                            transOrderNo: this.reviewAndModifyForm.transOrderNo,
                            fileUrl: item.url,
                            fileName: item.name,
                            fileType: '4'
                        };
                    });
                    const thwdFileList = this.reviewAndModifyForm.thwdFileList.map((item) => {
                        return {
                            transOrderNo: this.reviewAndModifyForm.transOrderNo,
                            fileUrl: item.url,
                            fileName: item.name,
                            fileType: '5'
                        };
                    });
                    const fileList = [...shdFileList, ...yswdFileList, ...hzdFileList, ...khqmFileList, ...thwdFileList];

                    transportationRecordsPage
                        .saveData({
                            id: this.parameters.id ? this.parameters.id : this.parameters?.modifyId,
                            tool,
                            deviceNo,
                            flowName: this.reviewAndModifyForm.flowName,
                            thirdWaybillNo: this.reviewAndModifyForm.thirdWaybillNo,
                            transOrderNo: this.reviewAndModifyForm.transOrderNo,
                            startShipmentTime: this.reviewAndModifyForm.startShipmentTime,
                            deliveryTime: this.reviewAndModifyForm.deliveryTime,
                            startShipmentTemp: this.reviewAndModifyForm.startShipmentTemp,
                            deliveryTemp: this.reviewAndModifyForm.deliveryTemp,
                            fileList,
                            // editSource --------1-签收审核 2-运输记录
                            editSource: this.parameters.transportationRecordData ? '1' : '2'
                        })
                        .then((response) => {
                            if (response.code === 200) {
                                this.$message.success('保存成功');
                                this.close();
                            }
                        })
                        .finally(() => {
                            this.loading = false;
                        });
                }
            });
        },
        /**
         * 变为输入框
         * @param type
         */
        showInput(type) {
            if (type === 'inputRefTransportVehicles') {
                this.inputVisibleTransportVehicles = true;
            } else if (type === 'inputRefEquipmentNumber') {
                this.inputVisibleEquipmentNumber = true;
            }
            this.$nextTick(() => {
                this.$refs[type].focus();
            });
        },
        /**
         * 获取承运单位列表
         */
        thirdLogistics() {
            pharmaceuticalloading.threePartFlowFlowList().then((res) => {
                if (res.code === 200) {
                    this.flowNameList = res.data.records;
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-upload-list--picture-card .el-upload-list__item-actions:hover span) {
    display: contents !important;
}
.number__unit__element {
    position: relative;

    &::after {
        content: '℃';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #000 !important;
}
</style>
