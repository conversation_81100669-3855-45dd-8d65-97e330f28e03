<template>
  <div>
    <h4 v-if="props.obj" class="titleH4" style="font-size: 13px;margin-left: 35px;">
      申请单据编号: <span style="font-weight: 400;color: #505050">{{ props.obj?.applyNo }}</span>&emsp;
      单据日期: <span style="font-weight: 400;color: #505050">{{
        functionIndex.transformTimestamp(props.obj.createDate)
      }}</span>&emsp;
      申请人: <span style="font-weight: 400;color: #505050">{{ props.obj.createBy?.name }}</span>&emsp;
      类型: <span style="font-weight: 400;color: #505050">{{
        props.obj?.billingType == 0 ? '销售出库' : "销退入库"
      }}</span>&emsp;
      审核状态: <span style="font-weight: 400;color: #505050">{{ echoStatus(props.obj?.auditStatus) }}</span>&emsp;
      开票状态: <span style="font-weight: 400;color: #505050;position: relative">{{
        props.obj?.status == 1 ? '已开票' : "未开票"
      }}</span>&emsp;
      <el-button v-if="props.obj.status == 2" size="small" type="primary" @click="errorFn">查看详情</el-button>
    </h4>
    <el-dialog
        v-model="dialogVisible"
        title="开票失败详情"
        width="50%"
    >
      <div v-if="props.obj.auditStatus==3&&props.obj.status == 2">
        <p>开票失败原因：开票失败原因：网络问题</p>
        <p style="margin-top: 10px;color: red">请点击下方“重试”按钮，尝试重新开票</p>
      </div>
      <div v-else>
        <p>开票失败原因：参数异常（{{ props.obj.bwIssueErrorMsg }}）</p>
        <p style="margin-top: 10px;color: red">请编辑该开票申请后，重新提交审核。审核通过后，系统将再次尝试开票</p>
        <el-table :data="tableData" style="margin-top: 10px ;width: 100%">
          <el-table-column align="center" label="商品编码" prop="goodsCode"/>
          <el-table-column align="center" label="商品名称" prop="goodsName"/>
          <el-table-column align="center" label="规格型号" prop="goodsUnit"/>
          <el-table-column align="center" label="商品数量" prop="goodsQuantity"/>
          <el-table-column align="center" label="商品单价" prop="goodsPrice"/>
          <el-table-column align="center" label="金额" prop="goodsTotalPrice"/>
          <el-table-column align="center" label="税额" prop="goodsTotalTax"/>
          <el-table-column align="center" label="税率" prop="goodsTaxRate"/>
        </el-table>
      </div>
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-if="props.obj.auditStatus==3&&props.obj.status == 2" type="primary" @click="redirectFn">
          重试
        </el-button>
      </span>
      </template>
    </el-dialog>
    <h4 class="el-dialog__title" style="margin-bottom: 15px">发票信息</h4>
    <h4 v-if="props.obj" class="titleH4" style="font-size: 13px;margin-left: 35px;">
      发票客户: <span style="font-weight: 400;color: #505050">{{ props.obj?.invoiceCustomer }}</span>&emsp;
      纳税人识别号: <span style="font-weight: 400;color: #505050">{{ props.obj?.taxpayerNo }}</span>&emsp;
      开票金额: <span style="font-weight: 400;color: #505050">{{
        Number(props.obj?.invoiceAmount).toFixed(2)
      }}</span>&emsp;
      <b v-if="types == 0">开票金额(含税):</b> <span v-if="types == 0" style="font-weight: 400;color: #505050">{{
        Number(props.obj?.totalPriceTax).toFixed(2)
      }}</span>&emsp;
      发票类型: <span style="font-weight: 400;color: #505050">{{ echo(props.obj?.invoiceType) }}</span>
    </h4>
    <el-table :data="props.table1" style="width: 100%">
      <el-table-column :show-overflow-tooltip="true" fixed label="发票号码" prop="invoiceNo"/>
      <el-table-column :show-overflow-tooltip="true" label="发票代码" prop="invoiceCode"/>
      <el-table-column :show-overflow-tooltip="true" label="来源" prop="source">
        <template #default="scope">
          {{ scope.row.source == 0 ? '人工录入' : "同步接口" }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="发票客户" prop="customerName"/>
      <el-table-column :show-overflow-tooltip="true" label="纳税人识别号" prop="taxpayerNo"/>
      <el-table-column :label="types == 0 ? '价税合计' : '开票金额'" :show-overflow-tooltip="true"
                       prop="invoicingAmount">
        <template #default="scope">
          {{ Number(scope.row.invoicingAmount).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column label="开票日期" prop="invoicingDate">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.invoicingDate) }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="开票附件" prop="">
        <template #default="scope">
          <div v-if="scope.row.files">
            <p v-for="(item, index) in JSON.parse(scope.row.files)" :key="index"
               style="color: rgb(34, 130, 255);cursor: pointer;" @click="check(item)">
              {{ item.name }}
            </p>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">单据信息</h4>
    <el-table :border="true" :data="props.table2" :header-cell-style="{ 'text-align': 'center' }"
              style="width: 100%">
      <el-table-column :show-overflow-tooltip="true" align="center" fixed label="序号" prop="" width="60">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="单据编号" property="docNum" width="190px"/>
      <el-table-column align="center" label="单据创建日期" property="" width="130">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.createDate) }}
        </template>
      </el-table-column>
      <el-table-column :label="types == 0 ? '出库日期' : '入库日期'" align="center" property="" width="100">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.salesOutBound.outTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="客户" property="customer" width="130px"/>
      <el-table-column align="center" label="商品名称" property="commodity.commonName" width="150px"/>
      <el-table-column align="center" label="自编码" property="commodity.commoditySelfCode" width="120px"/>
      <el-table-column align="center" label="规格" property="commodity.packageSpecification"/>
      <el-table-column align="center" label="生产厂家" property="commodity.manufactureName" width="170px"/>
      <el-table-column align="center" label="产地" property="commodity.producingArea"/>
      <el-table-column align="center" label="生产日期" property="produceDate" width="120px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="批号" property="batchNumber"/>
      <el-table-column align="center" label="有效期" property="commodity.validityTime" width="120px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.commodity.validityTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="单位" property="commodity.basicUnit"/>
      <el-table-column align="center" label="单价" property="unitPrice"/>
      <el-table-column :label="types == 0 ? '出库数量' : '入库数量'" align="center" property="outQuantity"/>
      <el-table-column v-if="types == 0" align="left" fixed="right" label="开票信息" property="" width="150">
        <template #default="scope">
          <div v-if="scope.row.invoiceFormPriceList">
            <div v-for="(item, index) in scope.row.invoiceFormPriceList" :key="index">
              <p v-if="index <= 2 || scope.row.checkFlag">{{
                  item.invoicePrice?.toFixed(2) + ' * ' + item.invoiceQuantity + ' = ' + item.totalAmount?.toFixed(2)
                }}</p>
              <p style="text-align: center">
                <el-button
                    v-if="((index == 3 && !scope.row.checkFlag) || ((index + 1) == scope.row.invoiceFormPriceList.length && scope.row.checkFlag) && scope.row.invoiceFormPriceList.length > 3)"
                    size="small" text type="primary" @click="checkFn(scope.$index)">
                  {{ scope.row.checkFlag ? '收起' : '展开' }}
                </el-button>
              </p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="types == 1" align="center" fixed="right" label="开票数量" property="num"
                       width="100"/>
      <el-table-column align="center" fixed="right" label="开票金额" property="" width="80">
        <template #default="scope">
          {{ Number(scope.row.totalAmount)?.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column v-if="types == 0" align="center" fixed="right" label="税率" property="taxRate" width="80">
        <template #default="scope">
          {{ scope.row.taxRate }} %
        </template>
      </el-table-column>
      <el-table-column v-if="types == 0" align="center" fixed="right" label="税额" property="amount" width="80">
        <template #default="scope">
          {{ Number(scope.row.totalTax).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column v-if="types == 0" align="center" fixed="right" label="价税合计" property="" width="80">
        <template #default="scope">
          {{ Number(scope.row.totalPriceTax).toFixed(2) }}
        </template>
      </el-table-column>
    </el-table>
    <p class="textStyle" style="margin: 15px 0 0px 0"><b>开票金额合计:</b> <span style="color:red">{{
        Number(props.obj?.invoiceAmount).toFixed(2)
      }}元</span>
      <b v-if="types == 0" style="margin-left: 20px">税额合计:</b> <span v-if="types == 0" style="color:red">{{
          Number(props.obj?.totalTax).toFixed(2)
        }}元</span>
      <b v-if="types == 0" style="margin-left: 20px">价税合计:</b> <span v-if="types == 0" style="color:red">{{
          Number(props.obj?.totalPriceTax).toFixed(2)
        }}元</span>
    </p>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import {functionIndex} from "@/views/commodity/functionIndex";
import {applocation} from "@/api/model/invoice";
import {ElMessage} from "element-plus";

const {proxy} = getCurrentInstance();
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const invoiceType = ref()
const data = reactive({
  checkFlag: false,
  imgUrl: []
})
const dialogVisible = ref()
const tableData = ref([])
const emit = defineEmits([])
const close = () => {
  data.checkFlag = false
  data.imgUrl = []
}
const echo = (value) => {
  return invoiceType.value.find(item => item.value == value)?.name
}
const check = (obj) => {
  data.imgUrl = []
  data.imgUrl.push(obj.resFileUrl)
  data.checkFlag = true
}

const props = defineProps({
  table1: {
    default: []
  },
  table2: {
    default: []
  },
  obj: {
    default: []
  },
  types: {
    default: null
  }
})
const {table2, types} = toRefs(props)
const statusType = ref(null)
const checkFn = (ind) => {
  table2.value[ind].checkFlag = !table2.value[ind].checkFlag
}
const errorFn = () => {
  dialogVisible.value = true
  if (props.obj.auditStatus != 3) {
    applocation.getError({
      salesInvoiceId: props.obj.id,
      size: 1000
    }).then(res => {
      if (res.code == 200) {
        tableData.value = res.data.records
      }
    })
  }
}
const redirectFn = () => {
  applocation.redirect({
    id: props.obj.id
  }).then(res => {
    if (res.code == 200) {
      ElMessage.success('开票成功')
      dialogVisible.value = false
    } else {
      ElMessage.error('开票失败，错误信息：' + res.msg)
    }
  })
}
const echoStatus = (status) => {
  let searchValue = statusType.value.find(item => item.value == status).name
  return searchValue ? searchValue : '未知'
}
onBeforeMount(async () => {
  invoiceType.value = await proxy.getDictList("invoice_type");
  let statusList = localStorage.getItem('salesType')
  if (statusList) {
    statusType.value = JSON.parse(statusList)
  } else {
    statusType.value = await proxy.getDictList("status_sales")
    localStorage.setItem("salesType", JSON.stringify(statusType.value))
  }
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})

</script>
<style lang='scss' scoped>
.titleH4 {
  margin-bottom: 20px;
  color: #000;
  font-weight: bolder;
  font-size: 15px;
}

.textStyle {
  font-size: 15px;
  color: #000;
  font-weight: bolder;
}
</style>
