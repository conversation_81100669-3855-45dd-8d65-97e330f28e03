<template xmlns="http://www.w3.org/1999/html">
  <div>
    <el-form v-if="typeNum == 1" :inline="true" label-width="50px" style="margin-top: 20px">
      <el-form-item label="大类">
        <el-select v-model="searchForm.n1" clearable filterable placeholder="请选择大类" @change="smallGet">
          <el-option v-for="(item, index) in bigType" :key="index" :label="item.name" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="小类">
        <el-select v-model="searchForm.n2" :multiple="searchForm.n1==1" clearable filterable placeholder="请选择小类">
          <el-option v-for="(item, index) in smallType" :key="index" :label="item.name" :value="item.value"/>
        </el-select>
      </el-form-item>
    </el-form>
    <div v-else style="min-height: 600px">
      <p style="color: red;font-size: 12px;margin-left: 20px">
        以下为待上传文件类型，请依次进行上传，支持jpg/jpeg/png/bmp/pdf，
        大小不超过2M，最长边最大4096px；点击已上传文件可预览并进行智能录入，
        标*为必须上传文件
      </p>
      <div v-if="!tableFlag" style="position: fixed;z-index: 9999;top: 14%;right: 0">
        <el-button v-if="formBas||drugRef||InstrumentRef||foodRef||disRef||customRef1||supplierRef1" plain size="small"
                   style="font-size: 14px;writing-mode: vertical-lr;height: 185px;"
                   type="primary" @click="test()">
          点 击 查 看 完 整 表 单 &nbsp;<el-icon :size='18'>
          <CaretLeft/>
        </el-icon>
        </el-button>
      </div>
      <el-form ref="creatform" :model="formList" :rules="creatRules" label-width="150px" style="margin-top: 20px">
        <el-form-item v-for="(item, index) in formList" :key="index" :label="echoFile(item.categoryName)"
                      :prop="item.isUpload==1?'n1':null">
          <el-upload ref="uploadRef" v-model:file-list="formList[index].fileArr"
                     :before-upload="(file) => beforeFile(file, item)" :data="dataUpload" :headers="headers"
                     :limit="item.isMultiPage == 0 ? 1 : 0"
                     :on-preview="(uploadFile) => handlePictureCardPreview(uploadFile, item, index)"
                     :on-remove="handleRemove" :action="process.env.VUE_APP_API_UPLOAD"
                     drag
                     list-type="picture-card" multiple>
            <el-tooltip v-if="item.isMultiPage == 0 && formList[index].fileArr.length == 1" class="box-item"
                        content="已达上传数量限制"
                        effect="dark" placement="top">
              <el-icon>
                <Warning/>
              </el-icon>
            </el-tooltip>
            <el-icon v-else>
              <Plus/>
            </el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-dialog v-model="dialogVisible" :before-close="() => cancalFooter()" :title="title" width="80%">
        <h4 slot="title" class="stateTitle">
          <el-button v-if="dialogRow.isTemplate == 1" :icon="Camera" plain type="primary" @click="orcFns()">
            智能识别
          </el-button>
        </h4>
        <div v-loading="uploadLoading"
            :style="dialogRow.formSign.split(',').indexOf('4') == -1||dialogRow.formSign.split(',').length>1?'display: flex':'display: flex;justify-content: center'">
          <div v-loading="instanceLoading" style="width: 900px;height: 620px;position: relative">
            <p v-if="screenshotStatus&&dialogVisible"
               style="color: red;position: absolute;top: -25px;left: 50%;transform: translate(-50%,0)">
              请框选需要识别的区域</p>
            <ScreenshotBox v-if="screenshotStatus&&dialogVisible" ref="screenRef"
                           @destroyComponent="destroyComponent"
                           @getImg="getImg"/>
            <postureImage v-if="(!screenshotStatus)&&dialogVisible" ref="instance"
                          :imageUrl="dialogImageUrl"/>
            <p style="text-align: center;margin-top: 0.05rem">
              <el-button
                  v-if="(!screenshotStatus)&&dialogVisible" plain
                  type="primary"
                  @click="saveOCR()">
                保存图片
              </el-button>
            </p>
          </div>
          <div style="padding:0 0 20px 20px;display: flex;justify-content: center;flex: 1">
            <postureBasicsForm v-if="searchForm.n1 == 1" :key="'postureBasicsForm1'" ref="formBas"
                               :formRow="dialogRow.formSign" style="width: 500px;"
                               @iconBtn="orcFn"/>
            <customForm v-if="searchForm.n1 == 7" ref="customRef1" :formRow="dialogRow.formSign" style="width: 500px;"
                        :smallTypeForm="searchForm.n2" @cusSaveFn="cusSaveFn" @iconBtn="orcFn"/>
            <supplierForm v-if="searchForm.n1 == 6" ref="supplierRef1" :formRow="dialogRow.formSign"
                          :smallTypeForm="searchForm.n2"
                          style="width: 500px;" @cusSaveFn="cusSaveFn" @iconBtn="orcFn"/>
            <drugDialog v-if="searchForm.n1 == 2" ref="drugRef" v-model:formInline="formInline"
                        v-model:formInline2="formInline2" v-model:formInline3="formInline3"
                        v-model:formInline4="formInline4"
                        :data='data' :formRow="dialogRow.formSign" :title="data.title"
                        style="width: 500px" @Pinyin="Pinyin"
                        @changeGsp="changeGspDrug" @checkFile="checkFile" @delFile="delFile"
                        @errorFile="errorFile"
                        @iconBtn="orcFn" @newAddFile="newAddFile" @successFile="successFile"
                        @venterSearch="venterSearch"/>
            <instrumentDialog v-if="searchForm.n1 == 3" ref="InstrumentRef" v-model:formInline="formInline"
                              v-model:formInline2="formInline2" v-model:formInline3="formInline3"
                              v-model:formInline4="formInline4" :data='data' :formRow="dialogRow.formSign"
                              :title="data.title" style="width: 500px"
                              @Pinyin="Pinyin" @changeGsp="changeGspIns" @checkFile="checkFile"
                              @delFile="delFile"
                              @errorFile="errorFile" @iconBtn="orcFn"
                              @newAddFile="newAddFile" @successFile="successFile"
                              @venterSearch="venterSearch"/>
            <foodDialog v-if="searchForm.n1 == 4" ref="foodRef" v-model:formInline="formInline"
                        v-model:formInline2="formInline2" v-model:formInline3="formInline3"
                        v-model:formInline4="formInline4" :data='data' :formRow="dialogRow.formSign"
                        :title="data.title" style="width: 500px"
                        @Pinyin="Pinyin" @checkFile="checkFile" @delFile="delFile" @errorFile="errorFile"
                        @iconBtn="orcFn" @newAddFile="newAddFile"
                        @successFile="successFile" @venterSearch="venterSearch"/>
            <disDialog v-if="searchForm.n1 == 5" ref="disRef" v-model:formInline="formInline"
                       v-model:formInline2="formInline2"
                       v-model:formInline3="formInline3" v-model:formInline4="formInline4" :data='data'
                       :formRow="dialogRow.formSign" :title="data.title" style="width: 500px" @Pinyin="Pinyin"
                       @checkFile="checkFile" @delFile="delFile" @errorFile="errorFile" @iconBtn="orcFn"
                       @newAddFile="newAddFile" @successFile="successFile" @venterSearch="venterSearch"/>
          </div>
        </div>
        <template #footer>
          <p style="text-align: right">
            <el-button v-if="searchForm.n1==7&&dialogRow.formSign.split(',').indexOf('3') != -1" type="primary"
                       @click="customRef1.entrustSubmit()">
              确认
            </el-button>
            <el-button v-if="searchForm.n1==7&&dialogRow.formSign.split(',').indexOf('4') != -1" type="primary"
                       @click="customRef1.warrantySubmit()">
              确认
            </el-button>
            <el-button v-if="searchForm.n1==6&&dialogRow.formSign.split(',').indexOf('3') != -1" type="primary"
                       @click="supplierRef1.entrustSubmit()">
              确认
            </el-button>
            <el-button v-if="searchForm.n1==6&&dialogRow.formSign.split(',').indexOf('4') != -1" type="primary"
                       @click="supplierRef1.warrantySubmit()">
              确认
            </el-button>
          </p>
        </template>
      </el-dialog>
      <el-drawer v-model="tableFlag" :title="title" direction="rtl" size="90%">
        <div v-if="formBas||drugRef||InstrumentRef||foodRef||disRef||customRef1||supplierRef1">
          <div v-if="searchForm.n1 == 1" slot="header" style="position: absolute;right: 0.3rem;top: 0.1rem">
            <el-button @click="() => formBass.submitForm('save')">保存草稿
            </el-button>
            <el-button type="primary" @click="() => formBass.submitForm('submit')">提交审核
            </el-button>
          </div>
          <div v-if="searchForm.n1 == 2" slot="header" style="position: absolute;right:  0.3rem;top: 0.1rem">
            <el-button @click=" submitFormDrug('save')">保存草稿</el-button>
            <el-button type="primary" @click=" submitFormDrug( 'submit')">
              提交审核
            </el-button>
          </div>
          <div v-if="searchForm.n1 == 3" slot="header" style="position: absolute;right:  0.3rem;top: 0.1rem">
            <el-button @click=" submitFormIns('save')">保存草稿</el-button>
            <el-button type="primary" @click=" submitFormIns( 'submit')">
              提交审核
            </el-button>
          </div>
          <div v-if="searchForm.n1 == 4" slot="header" style="position: absolute;right:  0.3rem;top: 0.1rem">
            <el-button @click=" submitFormFood('save')">保存草稿</el-button>
            <el-button type="primary" @click=" submitFormFood( 'submit')">
              提交审核
            </el-button>
          </div>
          <div v-if="searchForm.n1 == 5" slot="header" style="position: absolute;right:  0.3rem;top: 0.1rem">
            <el-button @click=" submitFormDis('save')">保存草稿</el-button>
            <el-button type="primary" @click=" submitFormDis( 'submit')">
              提交审核
            </el-button>
          </div>
          <div v-if="searchForm.n1 == 6" slot="header" style="position: absolute;right:  0.3rem;top: 0.1rem">
            <el-button @click=" submitFormSupplier(1)">保存草稿</el-button>
            <el-button type="primary" @click=" submitFormSupplier(2)">
              提交审核
            </el-button>
          </div>
          <div v-if="searchForm.n1 == 7" slot="header" style="position: absolute;right:  0.3rem;top: 0.1rem">
            <el-button @click=" submitFormCustom(1)">保存草稿</el-button>
            <el-button type="primary" @click=" submitFormCustom(2)">
              提交审核
            </el-button>
          </div>
          <postureBasicsForm v-if="searchForm.n1 == 1" :key="'postureBasicsForm2'" ref="formBass"
                             :allFlag="true"
                             :formList="formList" :searchN2="searchForm.n2" @closeAll="closeAll"
                             @watchForm1="watchForm1" @watchForm2="watchForm2"
                             @watchForm3="watchForm3"/>
          <customForm v-if="searchForm.n1 == 7" ref="customRef2" :formList="formList" :ocrFlag="true"
                      :smallTypeForm="searchForm.n2" @closeAll="closeAll"/>
          <supplierForm v-if="searchForm.n1 == 6" ref="supplierRef2" :formList="formList" :ocrFlag="true"
                        :smallTypeForm="searchForm.n2" @closeAll="closeAll"/>
          <div v-if="searchForm.n1 == 2" style="padding: 20px">
            <drugDialog ref="drugRef" v-model:formInline="formInline" v-model:formInline2="formInline2"
                        v-model:formInline3="formInline3" v-model:formInline4="formInline4" :data='data'
                        :ocrFlag="true" :smallTypeForm="searchForm.n2"
                        :title="data.title" @Pinyin="Pinyin" @changeGsp="changeGspDrug"
                        @checkFile="checkFile"
                        @delFile="delFile" @errorFile="errorFile"
                        @newAddFile="newAddFile" @successFile="successFile"
                        @venterSearch="venterSearch"/>
          </div>
          <div v-if="searchForm.n1 == 3" style="padding: 20px">
            <instrumentDialog ref="InstrumentRef" v-model:formInline="formInline"
                              v-model:formInline2="formInline2" v-model:formInline3="formInline3"
                              v-model:formInline4="formInline4" :data='data' :ocrFlag="true"
                              :smallTypeForm="searchForm.n2" :title="data.title" @Pinyin="Pinyin"
                              @changeGsp="changeGspIns" @checkFile="checkFile" @delFile="delFile"
                              @errorFile="errorFile"
                              @newAddFile="newAddFile" @successFile="successFile"
                              @venterSearch="venterSearch"/>
          </div>
          <div v-if="searchForm.n1 == 4" style="padding: 20px">
            <foodDialog ref="foodRef" v-model:formInline="formInline" v-model:formInline2="formInline2"
                        v-model:formInline3="formInline3" v-model:formInline4="formInline4" :data='data'
                        :ocrFlag="true" :smallTypeForm="searchForm.n2" :title="data.title"
                        @Pinyin="Pinyin" @checkFile="checkFile" @delFile="delFile" @errorFile="errorFile"
                        @newAddFile="newAddFile"
                        @successFile="successFile" @venterSearch="venterSearch"/>
          </div>
          <div v-if="searchForm.n1 == 5" style="padding: 20px">
            <disDialog ref="disRef" v-model:formInline="formInline" v-model:formInline2="formInline2"
                       v-model:formInline3="formInline3" v-model:formInline4="formInline4" :data='data'
                       :ocrFlag="true" :smallTypeForm="searchForm.n2" :title="data.title"
                       @Pinyin="Pinyin" @checkFile="checkFile" @delFile="delFile" @errorFile="errorFile"
                       @newAddFile="newAddFile" @successFile="successFile" @venterSearch="venterSearch"/>
          </div>
        </div>
        <el-empty v-else description='暂无数据'/>
      </el-drawer>
    </div>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
    <el-dialog
        v-model="ocrSuccess"
        title="模板识别结果"
        width="30%"
    >
      <p style="font-size: 16px">识别成功!</p>
      <p style="margin-top: 15px;font-size: 16px">
        若识别结果不符合您的预期，建议您调整图片后再次进行智能识别；或使用右侧表单中的’框选识别’功能</p>
      <template #footer>
        <p style="text-align: right">
        <el-button type="primary" @click="ocrSuccess = false">
          确认
        </el-button>
        </p>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, toRefs, watch, watchEffect} from 'vue';
import {Camera, CaretLeft, Plus, Warning} from '@element-plus/icons-vue'
import tool from "@/utils/tool";
import postureImage from './postureImage.vue'
import {uuid} from 'vue-uuid'
import Compressor from 'compressorjs';
import customForm from "./customForm.vue";
import {ElLoading, ElMessage, ElMessageBox} from 'element-plus'
import postureBasicsForm from './postureBasicsForm.vue'
import {applianceApi as postureApi} from "@/api/erp/IntelligentInput/postureApi";
import drugDialog from "@/views/commodity/components/drug/allDialog.vue";
import {drugApi} from "@/api/model/commodity/drug";
import {typeList} from "@/views/commodity/indexApi";
import {disappApi} from "@/api/model/commodity/disappear";
import instrumentDialog from "@/views/commodity/components/instrument/allDialog.vue";
import disDialog from "@/views/commodity/components/disappear/allDialog.vue";
import {applianceApi} from "@/api/model/commodity/appliance";
import foodDialog from "@/views/commodity/components/food/allDialog.vue";
import {foodApi} from "@/api/model/commodity/food";
import ScreenshotBox from './ScreenshotBox.vue'
import html2canvas from 'html2canvas';
import drugEdit from "@/views/commodity/editList/drugList";
import {functionIndex} from "@/views/commodity/functionIndex";
import instrument from "@/views/commodity/editList/instrument";
import foodList from "@/views/commodity/editList/foodList";
import disappear from "@/views/commodity/editList/disappear";
import SupplierForm from "@/views/IntelligentInput/components/supplierForm.vue";

const {proxy} = getCurrentInstance();
const drugRef = ref()
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const InstrumentRef = ref()
const uploadLoading = ref(false)
const foodRef = ref()
const supplierRef1 = ref()
const supplierRef2 = ref()
const childRef = ref(null)
const ocrSuccess = ref(false)
const dataUpload = ref()
const screenRef = ref()
const tableFlag = ref(false)
const data = reactive({
  venderDrug: {
    pageNum: 1,
    pageSize: 1000,
    total: 0,
  },
})
const dialogFile = ref()
const uploadRef = ref()
const title = ref('')
const fileName = ref()
const emit = defineEmits(['closeAll'])
const bigType = ref(null)
const disRef = ref()
const instance = ref(null)
const instanceLoading = ref(false)
const orcInd = ref(null)
const uuids = ref()
const formBas = ref(null)
const formBass = ref(null)
const customRef1 = ref(null)
const customRef2 = ref(null)
const dialogRow = ref({})
const formList = ref([])
const smallType = ref(null)
const formType = ref(null)
const creatform = ref(null)
const props = defineProps({
  typeNum: {
    default: 1
  }
})
const formInline = reactive({
  n26: "",
});
const formInline2 = reactive({
  n26: "",
});
const formInline3 = reactive({
  n25: "",
});
const formInline4 = reactive({
  table: [],
  delAll: [],
});
const Pinyin = () => {
  if (searchForm.value.n1 == 2) {
    drugApi.getPinyin({tradeName: formInline.n1}).then((res) => {
      formInline.n3 = res.data;
    });
  } else if (searchForm.value.n1 == 3) {
    applianceApi.getPinyin({tradeName: formInline.n1}).then((res) => {
      formInline.n3 = res.data;
    });
  } else if (searchForm.value.n1 == 4) {
    foodApi.getPinyin({tradeName: formInline.n1}).then((res) => {
      formInline.n3 = res.data;
    });
  } else if (searchForm.value.n1 == 5) {
    disappApi.getPinyin({tradeName: formInline.n1}).then((res) => {
      formInline.n3 = res.data;
    });
  }
};
const closeAll = () => {
  emit('closeAll')
}
const blobSupplier = ref()
watch(() => supplierRef1.value.licensedata, () => {
  let ind = -1
  supplierRef1.value.modalTableList?.forEach((item, index) => {
    if (item.type == supplierRef1.value.licensedata.type) {
      ind = index
    }
  })
  if (ind !== -1) {
    for (let key in supplierRef1.value.licensedata) {
      supplierRef1.value.modalTableList[ind][key] = supplierRef1.value.licensedata[key]
    }
  } else {
    supplierRef1.value.modalTableList.push({...supplierRef1.value.licensedata})
  }
  blobSupplier.value = [...supplierRef1.value.modalTableList]
}, {deep: true})
const customBlob = ref()
watch(() => customRef1.value.licensedata, () => {
  let ind = -1
  customRef1.value.modalTableList?.forEach((item, index) => {
    if (item.type == customRef1.value.licensedata.type) {
      ind = index
    }
  })
  if (ind !== -1) {
    for (let key in customRef1.value.licensedata) {
      customRef1.value.modalTableList[ind][key] = customRef1.value.licensedata[key]
    }
  } else {
    customRef1.value.modalTableList.push({...customRef1.value.licensedata})
  }
  customBlob.value = [...customRef1.value.modalTableList]
}, {deep: true})
const formBasBlob = ref()
watch(() => formBas.value.licensedata, () => {
  let ind = -1
  formBas.value.modalTableList?.forEach((item, index) => {
    if (item.type == formBas.value.licensedata.type) {
      ind = index
    }
  })
  if (ind !== -1) {
    for (let key in formBas.value.licensedata) {
      formBas.value.modalTableList[ind][key] = formBas.value.licensedata[key]
    }
  } else {
    formBas.value.modalTableList.push({...formBas.value.licensedata})
  }
  formBasBlob.value = [...formBas.value.modalTableList]
}, {deep: true})

watch(() => formList.value, () => {
  formList.value.forEach((item) => {
    if (item.fileArr) {
      item.fileArr.forEach((item2, index) => {
        item2.url = item2.response.data.url
        // let strFileName = item2.response.data.name.replace(/(.*\/)*([^.]+).*/ig, "$2");
        // let fileType = '.' + item2.response.data.name.split('.')[item2.response.data.name.split('.').length - 1]
        // let blobName = strFileName + `(${index + 1})` + fileType
        // item2.name = blobName
        item2.name = item2.response.data.name
      })
    }
  })
}, {deep: true})
watch(() => formBas.value.modalTableList_GMPRef, () => {
  let ind = -1
  formBas.value.modalTableList_GMP?.forEach((item, index) => {
    if (item.id === formBas.value.modalTableList_GMPRef.id) {
      ind = index
    }
  })
  if (ind === -1) {
    if (formBas.value.modalTableList_GMPRef.id) {
      formBas.value.modalTableList_GMP.push(formBas.value.modalTableList_GMPRef)
    }
  } else {
    for (let key in formBas.value.modalTableList_GMPRef) {
      formBas.value.modalTableList_GMP[ind][key] = formBas.value.modalTableList_GMPRef[key]
    }
  }

}, {deep: true})
const watchForm1 = (obj) => {
  formBas.value.form = obj
}
const watchForm2 = (obj) => {
  let ind = -1
  formBas.value.modalTableList.forEach((item, index) => {
    if (item.type == obj.type) {
      ind = index
    }
  })
  formBas.value.modalTableList[ind] = {...obj}
}
const watchForm3 = (arr) => {
  formBas.value.modalTableList_GMP = [...arr]
}
const close = () => {
  data.checkFlag = false;
  data.imgUrl = []
}
const changeGspDrug = () => {
  drugApi.SelfCode({type: 1, GspAttribute: formInline2.n1}).then((res) => {
    formInline.n4 = res.data.substr(2);
    formInline.n26 = res.data.substr(0, 2);
    data.numReg = res.data.substr(2).split("-")[0].length;
    ElMessage({
      showClose: true,
      duration: 0,
      message: "自编码已经发生改变",
      type: "warning",
    });
  });
};
const changeGspIns = () => {
  drugApi.SelfCode({type: 2, extParam: formInline2.n15}).then((res) => {
    let num = 0
    res.data.split('').find((item, index) => {
      if (item <= 9 && item >= 0) {
        num = index
        return index
      }
    })
    formInline.n4 = res.data.substr(num);
    formInline.n26 = res.data.substr(0, num);
    ElMessage({
      message: "自编码已经发生改变",
      type: "warning",
    });
  });
};
const delFile = () => {
  if (formInline4.delAll.length) {
    ElMessageBox.confirm("确认删除文件吗？", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    })
        .then(() => {
          let newarr = formInline4.table.filter(
              (item) =>
                  !formInline4.delAll.some(
                      (subItem) => subItem.n1 === item.n1
                  )
          );
          let ids = [];
          formInline4.delAll.forEach((item) => {
            if (item.id) {
              ids.push(item.id);
            }
          });
          if (ids.length > 0) {
            ElLoading.service();
            drugApi.delFiles({ids: ids.toString()}).then((res) => {
              if (res.code == 200) {
                ElMessage.success("删除成功");
                formInline4.table = newarr;
                formInline4.table.forEach((item, index) => {
                  item.n1 = index;
                });
              } else {
                ElMessage.error("删除失败");
              }
              const loadingInstance = ElLoading.service();
              loadingInstance.close();
            });
          } else {
            ElMessage.success("删除成功");
            formInline4.table = newarr;
            formInline4.table.forEach((item, index) => {
              item.n1 = index;
            });
          }
        })
        .catch(() => {
        });
  } else {
    ElMessage.error("请先选择");
  }
};
const lunFile = (item, items) => {
  setTimeout(() => {
    if (formBass.value.modalTableList_file.length > 0) {
      formBass.value.modalTableList_file.forEach(item2 => {
        if (item.categoryName == item2.categoryName) {
          item2.fileList.push(items)
        }
      })
    } else {
      lunFile(item, items)
    }
  }, 1000)
}
const test = async () => {
  tableFlag.value = true
  if (formBas.value || drugRef.value || InstrumentRef.value || foodRef.value || disRef.value || customRef1.value || supplierRef1) {
    setTimeout(() => {
      if (searchForm.value.n1 == 1) {
        basTest()
      } else if (searchForm.value.n1 == 2 || searchForm.value.n1 == 3 || searchForm.value.n1 == 4 || searchForm.value.n1 == 5) {
        drugTest()
      } else if (searchForm.value.n1 == 6) {
        supplierTest()
      } else if (searchForm.value.n1 == 7) {
        customTest()
      }
    })
  }
}
const supplierTest = () => {
  if (supplierRef1.value) {
    for (let key in supplierRef1.value.form) {
      supplierRef2.value.form[key] = supplierRef1.value.form[key]
    }
    supplierRef1.value.modalTableList = [...blobSupplier.value]
    supplierRef2.value.modalTableList = [...supplierRef1.value.modalTableList]
    supplierRef2.value.licensedata = {...supplierRef1.value.licensedata}
    supplierRef2.value.tabKey = supplierRef1.value.licensedata.type
    supplierRef1.value.modalTableList_entrust.forEach(item => {
      item.remark = []
    })
    supplierRef1.value.modalTableList_warranty.forEach(item => {
      item.qualityCode = []
    })
    supplierRef1.value.modalTableList_GMP.forEach(item => {
      item.gmpCertificatePicture = []
    })
    formList.value.forEach(fileListItem => {
      if (fileListItem.formSign.split(',').indexOf('3') !== -1) {
        if (fileListItem.fileArr) {
          fileListItem.fileArr.forEach((item) => {
            let ind = -1
            supplierRef1.value.modalTableList_entrust.forEach((enItem, enIndex) => {
              if (item.listId == enItem.id) {
                ind = enIndex
              }
            })
            if (ind !== -1) {
              supplierRef1.value.modalTableList_entrust[ind].remark = []
              supplierRef1.value.modalTableList_entrust[ind].remark.push({...item})
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('4') !== -1) {
        if (fileListItem.fileArr) {
          fileListItem.fileArr.forEach((item) => {
            let ind = -1
            supplierRef1.value.modalTableList_warranty.forEach((enItem, enIndex) => {
              if (item.listId == enItem.id) {
                ind = enIndex
              }
            })
            if (ind !== -1) {
              supplierRef1.value.modalTableList_warranty[ind].qualityCode = []
              supplierRef1.value.modalTableList_warranty[ind].qualityCode.push({...item})
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('5') !== -1) {
        if (fileListItem.fileArr) {
          fileListItem.fileArr.forEach((item) => {
            let ind = -1
            supplierRef1.value.modalTableList_GMP.forEach((enItem, enIndex) => {
              if (item.uid == enItem.uuid) {
                ind = enIndex
              }
            })
            if (ind !== -1) {
              supplierRef1.value.modalTableList_GMP[ind].gmpCertificatePicture = []
              supplierRef1.value.modalTableList_GMP[ind].gmpCertificatePicture.push({...item})
            } else {
              supplierRef1.value.modalTableList_GMP.push({uuid: item.uid, gmpCertificatePicture: [item], isAdd: true})
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('6') !== -1) {
        if (fileListItem.fileArr) {
          lunFile3(fileListItem)
        }
      }
      if (searchForm.value.n2 == 1 || searchForm.value.n2 == 3 || searchForm.value.n2 == 5 || searchForm.value.n2 == 7) {
        if (fileListItem.formSign.split(',').indexOf('2') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 1) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 1) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('7') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 3) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 3) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('8') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 4) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 4) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('9') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 2) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 2) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('10') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 5) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 5) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('11') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 6) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 6) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
      } else {
        if (fileListItem.formSign.split(',').indexOf('2') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 1) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 1) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('7') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 4) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 4) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('8') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 2) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 2) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
        if (fileListItem.formSign.split(',').indexOf('9') !== -1) {
          if (fileListItem.fileArr) {
            supplierRef2.value.modalTableList.forEach((item) => {
              if (item.type == 5) {
                item.licenseImg = fileListItem.fileArr
                if (supplierRef2.value.tabKey == 5) {
                  supplierRef2.value.licensedata.licenseImg = fileListItem.fileArr
                }
              }
            })
          }
        }
      }
    })
    let newArr = []
    newArr = supplierRef1.value.modalTableList_entrust.filter(enItem => enItem.remark.length > 0)
    supplierRef2.value.modalTableList_entrust = [...newArr]
    let newArr2 = []
    newArr2 = supplierRef1.value.modalTableList_warranty.filter(enItem => enItem.qualityCode.length > 0)
    supplierRef2.value.modalTableList_warranty = [...newArr2]
    let newArr3 = []
    newArr3 = supplierRef1.value.modalTableList_GMP.filter(enItem => enItem.gmpCertificatePicture?.length > 0)
    supplierRef2.value.modalTableList_GMP = [...newArr3]
    let newArr4 = []
    newArr4 = supplierRef2.value.modalTableList.filter(enItem => enItem.licenseImg?.length > 0)
    supplierRef2.value.modalTableList = [...newArr4]
    if (newArr4.length <= 0) {
      supplierRef2.value.licensedata = {licenseImg: [], licenceScopes: [], type: supplierRef2.value.tabKey}
    }
  }
}
const lunFile2 = (item) => {
  setTimeout(() => {
    if (customRef2.value.modalTableList_file.length > 0) {
      customRef2.value.modalTableList_file.forEach(item2 => {
        if (item.categoryName == item2.categoryName) {
          item2.fileList = item.fileArr
        }
      })
    } else {
      lunFile2(item)
    }
  }, 1000)
}
const lunFile3 = (item) => {
  setTimeout(() => {
    if (supplierRef2.value.modalTableList_file.length > 0) {
      supplierRef2.value.modalTableList_file.forEach(item2 => {
        if (item.categoryName == item2.categoryName) {
          item2.fileList = item.fileArr
        }
      })
    } else {
      lunFile3(item)
    }
  }, 1000)
}
const customTest = () => {
  if (customRef1.value) {
    for (let key in customRef1.value.form) {
      customRef2.value.form[key] = customRef1.value.form[key]
    }
    customRef1.value.modalTableList = [...customBlob.value]
    customRef2.value.modalTableList = [...customRef1.value.modalTableList]
    customRef2.value.licensedata = {...customRef1.value.licensedata}
    customRef2.value.tabKey = customRef1.value.licensedata.type
    customRef1.value.modalTableList_entrust.forEach(item => {
      item.remark = []
    })
    customRef1.value.modalTableList_warranty.forEach(item => {
      item.qualityCode = []
    })
    customRef1.value.modalTableList_GMP = [...gspRef.value]
    customRef1.value.modalTableList_GMP.forEach(item => {
      item.gmpCertificatePicture = []
    })
    //文件导入
    formList.value.forEach(fileListItem => {
      if (fileListItem.formSign.split(',').indexOf('2') !== -1) {
        if (fileListItem.fileArr) {
          customRef2.value.modalTableList.forEach((item) => {
            if (item.type == 1) {
              item.licenseImg = fileListItem.fileArr
              if (customRef2.value.tabKey == 1) {
                customRef2.value.licensedata.licenseImg = fileListItem.fileArr
              }
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('3') !== -1) {
        if (fileListItem.fileArr) {
          fileListItem.fileArr.forEach((item) => {
            let ind = -1
            customRef1.value.modalTableList_entrust.forEach((enItem, enIndex) => {
              if (item.listId == enItem.id) {
                ind = enIndex
              }
            })
            if (ind !== -1) {
              customRef1.value.modalTableList_entrust[ind].remark = []
              customRef1.value.modalTableList_entrust[ind].remark.push({...item})
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('4') !== -1) {
        if (fileListItem.fileArr) {
          fileListItem.fileArr.forEach((item) => {
            let ind = -1
            customRef1.value.modalTableList_warranty.forEach((enItem, enIndex) => {
              if (item.listId == enItem.id) {
                ind = enIndex
              }
            })
            if (ind !== -1) {
              customRef1.value.modalTableList_warranty[ind].qualityCode = []
              customRef1.value.modalTableList_warranty[ind].qualityCode.push({...item})
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('5') !== -1) {
        if (fileListItem.fileArr) {
          fileListItem.fileArr.forEach((item) => {
            let ind = -1
            customRef1.value.modalTableList_GMP.forEach((enItem, enIndex) => {
              if (item.uid == enItem.uuid) {
                ind = enIndex
              }
            })
            if (ind !== -1) {
              customRef1.value.modalTableList_GMP[ind].gmpCertificatePicture = []
              customRef1.value.modalTableList_GMP[ind].gmpCertificatePicture.push({...item})
            } else {
              customRef1.value.modalTableList_GMP.push({uuid: item.uid, gmpCertificatePicture: [item], isAdd: true})
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('6') !== -1) {
        if (fileListItem.fileArr) {
          lunFile2(fileListItem)
        }
      }
      if (fileListItem.formSign.split(',').indexOf('7') !== -1) {
        if (fileListItem.fileArr) {
          customRef2.value.modalTableList.forEach((item) => {
            if (item.type == 4) {
              item.licenseImg = fileListItem.fileArr
              if (customRef2.value.tabKey == 4) {
                customRef2.value.licensedata.licenseImg = fileListItem.fileArr
              }
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('8') !== -1) {
        if (fileListItem.fileArr) {
          customRef2.value.modalTableList.forEach((item) => {
            if (item.type == 2) {
              item.licenseImg = fileListItem.fileArr
              if (customRef2.value.tabKey == 2) {
                customRef2.value.licensedata.licenseImg = fileListItem.fileArr
              }
            }
          })
        }
      }
      if (fileListItem.formSign.split(',').indexOf('9') !== -1) {
        if (fileListItem.fileArr) {
          customRef2.value.modalTableList.forEach((item) => {
            if (item.type == 5) {
              item.licenseImg = fileListItem.fileArr
              if (customRef2.value.tabKey == 5) {
                customRef2.value.licensedata.licenseImg = fileListItem.fileArr
              }
            }
          })
        }
      }
    })
    let newArr = []
    newArr = customRef1.value.modalTableList_entrust.filter(enItem => enItem.remark.length > 0)
    customRef2.value.modalTableList_entrust = [...newArr]
    let newArr2 = []
    newArr2 = customRef1.value.modalTableList_warranty.filter(enItem => enItem.qualityCode.length > 0)
    customRef2.value.modalTableList_warranty = [...newArr2]
    let newArr3 = []
    newArr3 = customRef1.value.modalTableList_GMP.filter(enItem => enItem.gmpCertificatePicture?.length > 0)
    customRef2.value.modalTableList_GMP = [...newArr3]
    let newArr4 = []
    newArr4 = customRef2.value.modalTableList.filter(enItem => enItem.licenseImg?.length > 0)
    customRef2.value.modalTableList = [...newArr4]
    if (newArr4.length <= 0) {
      customRef2.value.licensedata = {licenseImg: [], licenceScopes: [], type: customRef2.value.tabKey}
    }
    console.log(customRef2.value.modalTableList)
  }
}
const checkFile = (item) => {
  data.imgUrl = []
  data.checkFlag = true;
  data.imgUrl.push(item.url)
};
const lunShop = () => {
  setTimeout(() => {
    if (formInline4.table.length > 0) {
      formList.value.forEach(item => {
        formInline4.table.forEach(item2 => {
          if (item.categoryName == item2.n3) {
            item2.n4.file = [...item.fileArr]
          }
        })
      })
    } else {
      lunShop()
    }
  }, 1000)
}
const drugTest = () => {
  lunShop()
}
const basTest = () => {
  if (formBas.value) {
    for (let key in formBas.value.form) {
      formBass.value.form[key] = formBas.value.form[key]
    }
    formBas.value.modalTableList = [...formBasBlob.value]
    formBas.value.modalTableList.forEach((item, index) => {
      let ind = -1
      formBass.value.modalTableList.forEach(items => {
        if (items.type == item.type) {
          ind = index
        }
      })
      if (ind !== -1) {
        formBass.value.modalTableList[ind] = {...item}
      } else {
        formBass.value.modalTableList.push({...item})
      }
    })
    formBass.value.licensedata = {...formBass.value.modalTableList[0]}
    formBass.value.tabKey = '1'
  }
  //文件清空
  formBass.value.form.businessLicensePicturt = []
  formBass.value.modalTableList.forEach(item2 => {
    item2.licenseImg = []
  })
  formBass.value.modalTableList_GMP = []
  formBass.value.modalTableList_file.forEach(modalTableList_file_item => {
    modalTableList_file_item.fileList = []
  })
  //文件导入
  formList.value.forEach(item => {
        item.fileArr.forEach(items => {
          if (item.formSign.split(',').indexOf('1') !== -1) {
            formBass.value.form.businessLicensePicturt.push(items)
          }
          if (item.formSign.split(',').indexOf('2') !== -1) {
            let obj = formBass.value.modalTableList.find(item => item.type == 1)
            if (!obj) {
              formBass.value.modalTableList.push(
                  {licenseImg: [], licenceScopes: [], type: '1'}
              )
            }
            formBass.value.modalTableList.forEach(item2 => {
              if (item2.type == 1) {
                item2.licenseImg.push(items)
                formBass.value.tabKey = 1
                formBass.value.licensedata = {...item2}
              }
            })
          }
          if (item.formSign.split(',').indexOf('3') !== -1) {
            formBass.value.modalTableList_GMP.push(
                {id: items.uid, gmpCertificatePicture: [items], isAdd: true}
            )
          }
          if (item.formSign.split(',').indexOf('4') !== -1) {
            lunFile(item, items)
          }
          if (item.formSign.split(',').indexOf('5') !== -1) {
            let obj = formBass.value.modalTableList.find(item => item.type == 3)
            if (!obj) {
              formBass.value.modalTableList.push(
                  {licenseImg: [], licenceScopes: [], type: '3'}
              )
            }
            formBass.value.modalTableList.forEach(item2 => {
              if (item2.type == 3) {
                item2.licenseImg.push(items)
                formBass.value.tabKey = 3
                formBass.value.licensedata = {...item2}
              }
            })
          }
          if (item.formSign.split(',').indexOf('6') !== -1) {
            let obj = formBass.value.modalTableList.find(item => item.type == 4)
            if (!obj) {
              formBass.value.modalTableList.push(
                  {licenseImg: [], licenceScopes: [], type: '4'}
              )
            }
            formBass.value.modalTableList.forEach(item2 => {
              if (item2.type == 4) {
                item2.licenseImg.push(items)
                formBass.value.tabKey = 4
                formBass.value.licensedata = {...item2}
              }
            })
          }
          if (item.formSign.split(',').indexOf('7') !== -1) {
            let obj = formBass.value.modalTableList.find(item => item.type == 2)
            if (!obj) {
              formBass.value.modalTableList.push(
                  {licenseImg: [], licenceScopes: [], type: '2'}
              )
            }
            formBass.value.modalTableList.forEach(item2 => {
              if (item2.type == 2) {
                item2.licenseImg.push(items)
                formBass.value.tabKey = 2
                formBass.value.licensedata = {...item2}
              }
            })
          }
          if (item.formSign.split(',').indexOf('8') !== -1) {
            let obj = formBass.value.modalTableList.find(item => item.type == 5)
            if (!obj) {
              formBass.value.modalTableList.push(
                  {licenseImg: [], licenceScopes: [], type: '5'}
              )
            }
            formBass.value.modalTableList.forEach(item2 => {
              if (item2.type == 5) {
                item2.licenseImg.push(items)
                formBass.value.tabKey = 5
                formBass.value.licensedata = {...item2}
              }
            })
          }
          if (item.formSign.split(',').indexOf('9') !== -1) {
            let obj = formBass.value.modalTableList.find(item => item.type == 6)
            if (!obj) {
              formBass.value.modalTableList.push(
                  {licenseImg: [], licenceScopes: [], type: '6'}
              )
            }
            formBass.value.modalTableList.forEach(item2 => {
                  if (item2.type == 6) {
                    item2.licenseImg.push(items)
                    formBass.value.tabKey = 6
                    formBass.value.licensedata = {...item2}
                  }
                }
            )
          }
        })
      }
  )
  if (formBas.value) {
    formBass.value.modalTableList_GMP.forEach(item => {
      formBas.value.modalTableList_GMP?.forEach(item2 => {
        if (item2.id == item.id) {
          item.gmpCertificateNo = item2.gmpCertificateNo
          item.gmpCertificateAddress = item2.gmpCertificateAddress
          item.gmpExpiredTime = item2.gmpExpiredTime
        }
      })
    })
  }
}
const drugOn = async () => {
  drugApi.SelfCode({type: 1, GspAttribute: 0}).then((res) => {
    formInline.n4 = res.data.substr(2);
    data.numReg = res.data.substr(2).split("-")[0].length;
    formInline.n26 = res.data.substr(0, 2);
  });
  let arr = await typeList(2)
  for (let key in arr) {
    data[key] = arr[key];
  }
  venterSearch()
  disappApi.numberDrug({"item.id": 9, size: 1000}).then((res) => {
    data.ratifyType = res.data.records;
  });
  disappApi.natureDrug({"item.id": 6, size: 1000}).then((res) => {
    data.drugType = res.data.records;
  });
  drugApi.natureDrug({"item.id": 7, size: 1000}).then((res) => {
    data.typecos = res.data.records;
  });
  drugApi.TreesDrug({}).then((res) => {
    res.data.forEach((item) => {
      if (new RegExp('药品').test(item['massName'])) {
        data.treesType = [];
        data.treesType.push(item);
      }
    });
  });
}
const insOn = async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  let arr = await typeList(4)
  for (let key in arr) {
    data[key] = arr[key];
  }
  venterSearch();
  await drugApi.natureDrug({"item.id": 13, size: 1000}).then((res) => {
    data.typecos = res.data.records;
  });
  applianceApi.TreesDrug({}).then((res) => {
    res.data.forEach((item) => {
      if (new RegExp('器械').test(item['massName'])) {
        data.treesType = [];
        data.treesType.push(item);
      }
    });
  });
  formInline2.n1 = '医疗器械'
  formInline2.n6 = '国家标准'
  applianceApi.SelfCode({type: 2}).then((res) => {
    let num = 0
    res.data.split('').find((item, index) => {
      if (item <= 9 && item >= 0) {
        num = index
        return index
      }
    })
    formInline.n4 = res.data.substr(num);
    formInline.n26 = res.data.substr(0, num);
  });
}
const foodFn = async () => {
  foodApi.SelfCode({type: 4}).then((res) => {
    formInline.n4 = res.data.substr(2);
    formInline.n26 = res.data.substr(0, 2);
  });
  let arr = await typeList(3)
  for (let key in arr) {
    data[key] = arr[key];
  }
  await disappApi.natureDrug({"item.id": 11, size: 1000}).then((res) => {
    data.drugType = res.data.records;
  });
  await drugApi.natureDrug({"item.id": 14, size: 1000}).then((res) => {
    data.typecos = res.data.records;
  });
  venterSearch();
  foodApi.TreesDrug({}).then((res) => {
    res.data.forEach((item) => {
      if (new RegExp('食品').test(item['massName'])) {
        data.treesType = [];
        data.treesType.push(item);
      }
    });
  });
}
const disFn = async () => {
  disappApi.SelfCode({type: 3}).then((res) => {
    formInline.n4 = res.data.substr(2);
    formInline.n26 = res.data.substr(0, 2);
  });
  let arr = await typeList(1)
  for (let key in arr) {
    data[key] = arr[key];
  }
  await disappApi.natureDrug({"item.id": 1, size: 1000}).then((res) => {
    data.drugType = res.data.records;
  });
  await drugApi.natureDrug({"item.id": 12, size: 1000}).then((res) => {
    data.typecos = res.data.records;
  });
  venterSearch();
  disappApi.TreesDrug({}).then((res) => {
    res.data.forEach((item) => {
      if (new RegExp('消杀').test(item['massName'])) {
        data.treesType = [];
        data.treesType.push(item);
      }
    });
  });
}
const errorFile = (scope) => {
  const loadingInstance = ElLoading.service();
  loadingInstance.close();
  ElMessage.error("上传失败");
  formInline4.table[scope.$index].n4.name = "";
  formInline4.table[scope.$index].n4.url = "";
  formInline4.table[scope.$index].n4.str = "";
  formInline4.table[scope.$index].n4.type = "";
};

const newAddFile = () => {
  formInline4.table.push({
    n1: formInline4.table.length,
    n2: {
      str: "",
      flag: false,
    },
    n3: {
      str: "",
      flag: false,
    },
    n4: {
      str: "请先上传文件",
      flag: true,
      type: "",
      name: "",
      url: "",
    },
    n5: "",
    n6: {
      str: "",
      flag: false,
    },
  });
};
const venterSearch = () => {
  if (searchForm.value.n1 == 2) {
    drugApi
        .venderDrug({
          current: data.venderDrug.pageNum,
          size: data.venderDrug.pageSize,
          enterpriseName: data.venderDrug.value,
          customLabel: 2,
          status: 3,
        })
        .then((res) => {
          data.venderDrug.pageNum = res.data.current;
          data.venderDrug.pageSize = res.data.size;
          data.venderDrug.total = res.data.total;
          data.venderDrug.records = res.data.records;
        });
  } else if (searchForm.value.n1 == 3) {
    applianceApi
        .venderDrug({
          current: data.venderDrug?.pageNum,
          size: data.venderDrug?.pageSize,
          enterpriseName: data.venderDrug?.value,
          customLabel: 2
        })
        .then((res) => {
          data.venderDrug.pageNum = res.data.current;
          data.venderDrug.pageSize = res.data.size;
          data.venderDrug.total = res.data.total;
          data.venderDrug.records = res.data.records;
        });
  } else if (searchForm.value.n1 == 4) {
    foodApi
        .venderDrug({
          current: data.venderDrug.pageNum,
          size: data.venderDrug.pageSize,
          enterpriseName: data.venderDrug.value,
          customLabel: 2
        })
        .then((res) => {
          data.venderDrug.pageNum = res.data.current;
          data.venderDrug.pageSize = res.data.size;
          data.venderDrug.total = res.data.total;
          data.venderDrug.records = res.data.records;
        });
  } else if (searchForm.value.n1 == 5) {
    disappApi
        .venderDrug({
          current: data.venderDrug.pageNum,
          size: data.venderDrug.pageSize,
          enterpriseName: data.venderDrug.value,
          customLabel: 2
        })
        .then((res) => {
          data.venderDrug.pageNum = res.data.current;
          data.venderDrug.pageSize = res.data.size;
          data.venderDrug.total = res.data.total;
          data.venderDrug.records = res.data.records;
        });
  }
};
const cancalFooter = () => {
  if (searchForm.value.n1 == 7 && dialogRow.value.formSign.split(',').indexOf('3') != -1) {
    customRef1.value.handleImgEntrustClose()
  } else if (searchForm.value.n1 == 7 && dialogRow.value.formSign.split(',').indexOf('4') != -1) {
    customRef1.value.handleImgWarrantyClose()
  }
  dialogVisible.value = false
}
const successFile = (response, scope) => {
  const loadingInstance = ElLoading.service();
  loadingInstance.close();
  ElMessage.success("上传成功");
  formInline4.table[scope.$index].n4.file.push({
    name: response.data.name,
    url: response.data.url
  })
};
const headers = {
  Authorization: "Bearer " + tool.cookie.get("TOKEN"),
  ContentType: "multipart/form-data",
  clientType:'pc',
};
const formArr = ref({})
const formObj = ref({})
const {typeNum} = toRefs(props)
const searchForm = ref({
  n1: "",
  n2: ""
})

const smallGet = async () => {
  if (searchForm.value.n1 == '1') {
    smallType.value = await proxy.getDictList("manufacturer_type");
  }
  if (searchForm.value.n1 == '2') {
    smallType.value = await proxy.getDictList("drug_type");
  }
  if (searchForm.value.n1 == '3') {
    smallType.value = await proxy.getDictList("instrument_type");
  }
  if (searchForm.value.n1 == '4') {
    smallType.value = await proxy.getDictList("food_type");
  }
  if (searchForm.value.n1 == '5') {
    smallType.value = await proxy.getDictList("kill_type");
  }
  if (searchForm.value.n1 == '6') {
    smallType.value = await proxy.getDictList("supplier_type");
  }
  if (searchForm.value.n1 == '7') {
    smallType.value = await proxy.getDictList("customer_type");
  }
}
const creatRules = reactive({
  n1: [{required: true, message: "请上传", trigger: "blur"}],
});
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handleRemove = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}
const echoFile = (value) => {
  return formType.value.find(item => item.value == value)?.name
}
const echoTime = (times) => {
  let obj = []
  let arr = times?.split('') || []
  arr.forEach(item => {
    if (item == '年' || item == '月') {
      obj.push('-')
    } else if (item == '日') {
    } else {
      obj.push(item)
    }
  })
  var reg = /[\u4e00-\u9fa5]/g;
  var str = obj.join('')
  let time = new Date(str.replace(reg, ""))
  if (time != 'Invalid Date') {
    return time
  }
  return ''
}
const handlePictureCardPreview = (uploadFile, row, ind) => {
  screenshotStatus.value = false
  dialogImageUrl.value = uploadFile.url
  dialogFile.value = uploadFile.raw
  fileName.value = uploadFile.name
  dialogRow.value = row
  setTimeout(() => {
    if (searchForm.value.n1 == 1) {
      manPre(uploadFile, row, ind)
    } else if (searchForm.value.n1 == 6) {
      supPre(uploadFile, row, ind)
    } else if (searchForm.value.n1 == 7) {
      cusPre(uploadFile, row, ind)
    }
  })
  dialogRow.value.uid = uploadFile.uid
  dialogRow.value.ind = ind
  dialogVisible.value = true
}
const supPre = (uploadFile, row, ind) => {
  if (row.formSign.split(',').indexOf('3') !== -1) {
    if (uploadFile.listId) {
      supplierRef1.value.modalTableList_entrust.forEach(item => {
        if (item.id == uploadFile.listId) {
          supplierRef1.value.handleAdd_entrust(item)
          supplierRef1.value.entrustVisible = false
        }
      })
    } else {
      supplierRef1.value.entrust_title = "新增"
      supplierRef1.value.handleAdd_entrust()
      supplierRef1.value.entrustVisible = false
    }
  }
  if (row.formSign.split(',').indexOf('4') !== -1) {
    if (uploadFile.listId) {
      supplierRef1.value.modalTableList_warranty.forEach(item => {
        if (item.id == uploadFile.listId) {
          supplierRef1.value.handleAdd_warranty(item)
          supplierRef1.value.warrantyVisible = false
        }
      })
    } else {
      supplierRef1.value.handleAdd_warranty()
      supplierRef1.value.warrantyVisible = false
    }
  }
  if (row.formSign.split(',').indexOf('5') !== -1) {
    let ind = -1
    supplierRef1.value.modalTableList_GMP.forEach((item, index) => {
      if (item.uuid == uploadFile.uid) {
        ind = index
      }
    })
    if (ind != -1) {
      supplierRef1.value.modalTableList_GMPRef = {...supplierRef1.value.modalTableList_GMP[ind]}
    } else {
      supplierRef1.value.modalTableList_GMP.push({uuid: uploadFile.uid, gmpCertificatePicture: [], isAdd: true})
      supplierRef1.value.modalTableList_GMPRef = {uuid: uploadFile.uid, gmpCertificatePicture: [], isAdd: true}
    }
  }
  if (searchForm.value.n2 == 1 || searchForm.value.n2 == 3 || searchForm.value.n2 == 5 || searchForm.value.n2 == 7) {
    if (row.formSign.split(',').indexOf('2') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 1)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '1'}
      }
      supplierRef1.value.tabKey = '1'
    }
    if (row.formSign.split(',').indexOf('7') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 3)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '3'}
      }
      supplierRef1.value.tabKey = '3'
    }
    if (row.formSign.split(',').indexOf('8') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 4)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '4'}
      }
      supplierRef1.value.tabKey = '4'
    }
    if (row.formSign.split(',').indexOf('9') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 2)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '2'}
      }
      supplierRef1.value.tabKey = '2'
    }
    if (row.formSign.split(',').indexOf('10') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 5)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '5'}
      }
      supplierRef1.value.tabKey = '5'
    }
    if (row.formSign.split(',').indexOf('11') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 6)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '6'}
      }
      supplierRef1.value.tabKey = '6'
    }
  } else {
    if (row.formSign.split(',').indexOf('2') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 1)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '1'}
      }
      supplierRef1.value.tabKey = '1'
    }
    if (row.formSign.split(',').indexOf('7') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 4)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '4'}
      }
      supplierRef1.value.tabKey = '4'
    }
    if (row.formSign.split(',').indexOf('8') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 2)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '2'}
      }
      supplierRef1.value.tabKey = '2'
    }
    if (row.formSign.split(',').indexOf('9') !== -1) {
      let obj = supplierRef1.value.modalTableList.find(item => item.type == 5)
      if (obj) {
        supplierRef1.value.licensedata = {...obj}
      } else {
        supplierRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '5'}
      }
      supplierRef1.value.tabKey = '5'
    }
  }
}
const cusPre = (uploadFile, row, ind) => {
  if (row.formSign.split(',').indexOf('2') !== -1) {
    let obj = customRef1.value.modalTableList.find(item => item.type == 1)
    if (obj) {
      customRef1.value.licensedata = {...obj}
    } else {
      customRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '1'}
    }
    customRef1.value.tabKey = '1'
  }
  if (row.formSign.split(',').indexOf('3') !== -1) {
    if (uploadFile.listId) {
      customRef1.value.modalTableList_entrust.forEach(item => {
        if (item.id == uploadFile.listId) {
          customRef1.value.handleAdd_entrust(item)
          customRef1.value.entrustVisible = false
        }
      })
    } else {
      customRef1.value.entrust_title = "新增"
      customRef1.value.handleAdd_entrust()
      customRef1.value.entrustVisible = false
    }
  }
  if (row.formSign.split(',').indexOf('4') !== -1) {
    if (uploadFile.listId) {
      customRef1.value.modalTableList_warranty.forEach(item => {
        if (item.id == uploadFile.listId) {
          customRef1.value.handleAdd_warranty(item)
          customRef1.value.warrantyVisible = false
        }
      })
    } else {
      customRef1.value.handleAdd_warranty()
      customRef1.value.warrantyVisible = false
    }
  }
  if (row.formSign.split(',').indexOf('5') !== -1) {
    let ind = -1
    customRef1.value.modalTableList_GMP.forEach((item, index) => {
      if (item.uuid == uploadFile.uid) {
        ind = index
      }
    })
    if (ind != -1) {
      customRef1.value.modalTableList_GMPRef = {...customRef1.value.modalTableList_GMP[ind]}
    } else {
      customRef1.value.modalTableList_GMP.push({uuid: uploadFile.uid, gmpCertificatePicture: [], isAdd: true})
      customRef1.value.modalTableList_GMPRef = {uuid: uploadFile.uid, gmpCertificatePicture: [], isAdd: true}
    }
  }
  if (row.formSign.split(',').indexOf('7') !== -1) {
    let obj = customRef1.value.modalTableList.find(item => item.type == 4)
    if (obj) {
      customRef1.value.licensedata = {...obj}
    } else {
      customRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '4'}
    }
    customRef1.value.tabKey = '4'
  }
  if (row.formSign.split(',').indexOf('8') !== -1) {
    let obj = customRef1.value.modalTableList.find(item => item.type == 2)
    if (obj) {
      customRef1.value.licensedata = {...obj}
    } else {
      customRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '2'}
    }
    customRef1.value.tabKey = '2'
  }
  if (row.formSign.split(',').indexOf('9') !== -1) {
    let obj = customRef1.value.modalTableList.find(item => item.type == 5)
    if (obj) {
      customRef1.value.licensedata = {...obj}
    } else {
      customRef1.value.licensedata = {licenseImg: [], licenceScopes: [], type: '5'}
    }
    customRef1.value.tabKey = '5'
  }
}
watch(() => formBass.value.licensedata, () => {
  let ind = -1
  formBas.value.modalTableList.forEach((item, index) => {
    if (item.type == formBass.value.licensedata.type) {
      ind = index
    }
  })
  if (ind != -1) {
    formBas.value.modalTableList[ind] = {...formBass.value.licensedata}
  } else {
    formBas.value.modalTableList.push({...formBass.value.licensedata})
  }
}, {deep: true})
const manPre = (uploadFile, row, ind) => {
  if (row.formSign.split(',').indexOf('2') !== -1) {
    let obj = formBas.value.modalTableList.find(item => item.type == 1)
    if (obj) {
      formBas.value.licensedata = {...obj}
    } else {
      formBas.value.licensedata = {licenseImg: [], licenceScopes: [], type: '1'}
    }
  }
  if (row.formSign.split(',').indexOf('3') !== -1) {
    if (formBas.value.modalTableList_GMP.length) {
      let obj = formBas.value.modalTableList_GMP.find((item) => item.id == row.uid)
      if (obj) {
        formBas.value.modalTableList_GMPRef = {...obj}
      } else {
        for (let key in formBas.value.modalTableList_GMPRef) {
          formBas.value.modalTableList_GMPRef[key] = ''
        }
        formBas.value.modalTableList_GMPRef = {id: row.uid, gmpCertificatePicture: [], isAdd: true}
      }
    } else {
      formBas.value.modalTableList_GMPRef = {id: row.uid, gmpCertificatePicture: [], isAdd: true}
    }
  }
  if (row.formSign.split(',').indexOf('5') !== -1) {
    let obj = formBas.value.modalTableList.find(item => item.type == 3)
    if (obj) {
      formBas.value.licensedata = {...obj}
    } else {
      formBas.value.licensedata = {licenseImg: [], licenceScopes: [], type: '3'}
    }
  }
  if (row.formSign.split(',').indexOf('6') !== -1) {
    let obj = formBas.value.modalTableList.find(item => item.type == 4)
    if (obj) {
      formBas.value.licensedata = {...obj}
    } else {
      formBas.value.licensedata = {licenseImg: [], licenceScopes: [], type: '4'}
    }
  }
  if (row.formSign.split(',').indexOf('7') !== -1) {
    let obj = formBas.value.modalTableList.find(item => item.type == 2)
    if (obj) {
      formBas.value.licensedata = {...obj}
    } else {
      formBas.value.licensedata = {licenseImg: [], licenceScopes: [], type: '2'}
    }
  }
  if (row.formSign.split(',').indexOf('8') !== -1) {
    let obj = formBas.value.modalTableList.find(item => item.type == 5)
    if (obj) {
      formBas.value.licensedata = {...obj}
    } else {
      formBas.value.licensedata = {licenseImg: [], licenceScopes: [], type: '5'}
    }
  }
  if (row.formSign.split(',').indexOf('9') !== -1) {
    let obj = formBas.value.modalTableList.find(item => item.type == 6)
    if (obj) {
      formBas.value.licensedata = {...obj}
    } else {
      formBas.value.licensedata = {licenseImg: [], licenceScopes: [], type: '6'}
    }
  }
}
const saveOCR = (ids) => {
  var arr = instance.value.instance.toDataURL().split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  let file = new File([u8arr], fileName.value, {type: mime})
  new Compressor(file, {
    quality: 0.3,
    success(result) {
      let fn = async () => {
        let base64 = await readFile(result)
        var arr = base64.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        let file = new File([u8arr], fileName.value, {type: mime})
        let url = URL.createObjectURL(file);
        let bigTypes = title.value.split('-')[0]
        let smallTypes = title.value.split('-')[1]
        let messTypes = echoFile(formList.value[dialogRow.value.ind].categoryName)
        const formData = new FormData()
        formData.append('file', file)
        formData.append('fileGroup', 'IntelligentInput')
        formData.append('zhType', bigTypes)
        formData.append('smallClass', smallTypes)
        formData.append('fjType', messTypes)
        formData.append('businessType', "postureRecording")
        uploadLoading.value = true
        postureApi.fileUpload(formData).then(res => {
          if (res.code == 200) {
            formList.value[dialogRow.value.ind].fileArr.forEach(item => {
              if (item.uid == dialogRow.value.uid) {
                item.url = res.data.url
                item.size = file.size
                item.raw = file
                item.status = 'success'
                item.name = res.data.name
                item.response = res
                item.listId = ids ? ids : null
              }
            })
            uploadLoading.value = false
            dialogVisible.value = false
            dialogImageUrl.value = ''
            dialogFile.value = null
            // dialogRow.value = {}
            // dialogRow.value.uid = ''
            // dialogRow.value.ind = null
            fileName.value = ''
          } else {
            ElMessage.error('图片上传失败')
          }
        })
      }
      fn()
    },
    error(err) {
      console.log(err.message);
    },
  });
}
const handleClose = () => {
  ElMessageBox.confirm("信息未保存确认取消吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        dialogVisible.value = false
        // emptyFn()
      })
      .catch(() => {
      });
}
const orcFn = (ind) => {
  if (!screenshotStatus.value) {
    instanceLoading.value = true
    screenshotStatus.value = true
    const elementToCapture = document.getElementById('edior'); // 替换成你的元素ID
    html2canvas(elementToCapture).then(function (canvas) {
      // 创建一个新的Image元素来显示截图
      const screenshotImage = new Image();
      screenshotImage.src = canvas.toDataURL('image/png'); // 将Canvas导出为PNG格式的图像
      screenRef.value.outClick(screenshotImage.src)
      instanceLoading.value = false
    });
    orcInd.value = ind
  }
}
const submitFormSupplier = (num) => {
  supplierRef2.value.submitForm(num)
}
const submitFormCustom = (num) => {
  customRef2.value.submitForm(num)
}
const submitFormDis = (status) => {
  rightFormDis(disRef.value.creatform1, disRef.value.creatform2, disRef.value.creatform3, status)
};
const rightFormDis = async (formEl1, formEl2, formEl3, status) => {
  if (!formEl1 && !formEl2 && !formEl3) return;
  let flag1 = false;
  let flag2 = false;
  let flag3 = false;
  let flag4 = true;
  let flag5 = false;
  await formEl1.validate((valid, fields) => {
    if (valid) {
      flag1 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          disRef.value["creatform1"].scrollToField(propName)
        }
      })
      if (disRef.value.activeNames.indexOf('1') == -1) {
        disRef.value.activeNames.push('1')
      }
    }
  });
  await formEl2.validate((valid, fields) => {
    if (valid) {
      flag2 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          disRef.value["creatform2"].scrollToField(propName)
        }
      })
      if (disRef.value.activeNames.indexOf('2') == -1) {
        disRef.value.activeNames.push('2')
      }
    }
  });
  await formEl3.validate((valid, fields) => {
    if (valid) {
      flag3 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          disRef.value["creatform3"].scrollToField(propName)
        }
      })
      if (disRef.value.activeNames.indexOf('3') == -1) {
        disRef.value.activeNames.push('3')
      }
    }
  });
  const reg = /^[0-9]{5}[-][0-9]{2}$/;
  const reg2 = /^[0-9]{5}$/;
  flag5 = reg.test(formInline.n4) || reg2.test(formInline.n4);
  if (!flag5) {
    ElMessage.error("自编码格式不正确");
  }
  formInline4.table.forEach(item => {
    if (item.n5 == 1 && item.n4.file.length <= 0) {
      flag4 = false;
    }
  })
  if (!flag4) {
    ElMessage.error("必传文件请上传");
  }
  if (flag1 && flag2 && flag3 && flag4 && flag5) {
    const fileArr = [];
    formInline4.table.forEach((item) => {
      let fileArrs = []
      item.n4.file.forEach(fileItem => {
        fileArrs.push({
          filesName: fileItem.name,
          filesUrl: fileItem.url
        })
      })
      fileArr.push({
        status: "1",
        isMultiPage: item.n6,
        smallType: item.n2,
        categoryName: item.n3,
        isUpload: item.n5,
        remark: item.n7.str,
        files: fileArrs,
        id: data.state ? item.id : null,
      });
    });
    ElLoading.service();
    disappear.sendApi(formInline, formInline2, formInline3, formInline4, status, data.editStr, data.state, fileArr, true)
        .then((res) => {
          if (res.code == 200) {
            if (status == "save") {
              ElMessage.success("新增成功");
            } else if (status == "submit") {
              ElMessage.success("提交审核成功");
            } else {
              ElMessage.success("编辑成功");
            }
            closeAll()
            dialogVisible.value = false;
            data.editStr = null;
            for (let key in formInline) {
              formInline[key] = "";
            }
            for (let key in formInline2) {
              formInline2[key] = "";
            }
            for (let key in formInline3) {
              formInline3[key] = "";
            }
            formInline4.table = [];
            formInline4.delAll = [];
            const query = proxy.$route?.query
            if (query && query.type == 'abnormalTask') {
              proxy.$router.push('/abnormalTask')
            }
          } else {
            ElMessage.error(res.msg);
          }
          const loadingInstance = ElLoading.service();
          loadingInstance.close();
        });
  }
}
const submitFormFood = (status) => {
  rightFormFood(foodRef.value.creatform1, foodRef.value.creatform2, foodRef.value.creatform3, status)
};
const rightFormFood = async (formEl1, formEl2, formEl3, status) => {
  if (!formEl1 && !formEl2 && !formEl3) return;
  let flag1 = false;
  let flag2 = false;
  let flag3 = false;
  let flag4 = true;
  let flag5 = false;
  await formEl1.validate((valid, fields) => {
    if (valid) {
      flag1 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          foodRef.value["creatform1"].scrollToField(propName)
        }
      })
      if (foodRef.value.activeNames.indexOf('1') == -1) {
        foodRef.value.activeNames.push('1')
      }
    }
  });
  await formEl2.validate((valid, fields) => {
    if (valid) {
      flag2 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          foodRef.value["creatform2"].scrollToField(propName)
        }
      })
      if (foodRef.value.activeNames.indexOf('2') == -1) {
        foodRef.value.activeNames.push('2')
      }
    }
  });
  await formEl3.validate((valid, fields) => {
    if (valid) {
      flag3 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          foodRef.value["creatform3"].scrollToField(propName)
        }
      })
      if (foodRef.value.activeNames.indexOf('3') == -1) {
        foodRef.value.activeNames.push('3')
      }
    }
  });
  const reg = /^[0-9]{5}[-][0-9]{2}$/;
  const reg2 = /^[0-9]{5}$/;
  flag5 = reg.test(formInline.n4) || reg2.test(formInline.n4);
  if (!flag5) {
    ElMessage.error("自编码格式不正确");
  }
  formInline4.table.forEach(item => {
    if (item.n5 == 1 && item.n4.file.length <= 0) {
      flag4 = false;
    }
  })
  if (!flag4) {
    ElMessage.error("必传文件请上传");
  }
  if (flag1 && flag2 && flag3 && flag4 && flag5) {
    const fileArr = [];
    formInline4.table.forEach((item) => {
      let fileArrs = []
      item.n4.file.forEach(fileItem => {
        fileArrs.push({
          filesName: fileItem.name,
          filesUrl: fileItem.url
        })
      })
      fileArr.push({
        status: "1",
        isMultiPage: item.n6,
        smallType: item.n2,
        categoryName: item.n3,
        isUpload: item.n5,
        remark: item.n7.str,
        files: fileArrs,
        id: data.state ? item.id : null,
      });
    });
    ElLoading.service();
    foodList.sendApi(formInline, formInline2, formInline3, formInline4, status, data.editStr, data.state, fileArr, true)
        .then((res) => {
          if (res.code == 200) {
            if (status == "save") {
              ElMessage.success("新增成功");
            } else if (status == "submit") {
              ElMessage.success("提交审核成功");
            } else {
              ElMessage.success("编辑成功");
            }
            closeAll()
            dialogVisible.value = false;
            data.editStr = null;
            for (let key in formInline) {
              formInline[key] = "";
            }
            for (let key in formInline2) {
              formInline2[key] = "";
            }
            for (let key in formInline3) {
              formInline3[key] = "";
            }
            formInline4.table = [];
            formInline4.delAll = [];
          } else {
            ElMessage.error(res.msg);
          }
          const loadingInstance = ElLoading.service();
          loadingInstance.close();
          const query = proxy.$route?.query
          if (query && query.type == 'abnormalTask') {
            proxy.$router.push('/abnormalTask')
          }
        });
  }
};
const submitFormIns = (status) => {
  rightFormIns(InstrumentRef.value.creatform1, InstrumentRef.value.creatform2, InstrumentRef.value.creatform3, status)
};
const rightFormIns = async (formEl1, formEl2, formEl3, status) => {
  if (!formEl1 && !formEl2 && !formEl3) return;
  let flag1 = false;
  let flag2 = false;
  let flag3 = false;
  let flag4 = true;
  let flag5 = false;
  await formEl1.validate((valid, fields) => {
    if (valid) {
      flag1 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          InstrumentRef.value["creatform1"].scrollToField(propName)
        }
      })
      if (InstrumentRef.value.activeNames.indexOf('1') == -1) {
        InstrumentRef.value.activeNames.push('1')
      }
    }
  });
  await formEl2.validate((valid, fields) => {
    if (valid) {
      flag2 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          InstrumentRef.value["creatform2"].scrollToField(propName)
        }
      })
      if (InstrumentRef.value.activeNames.indexOf('2') == -1) {
        InstrumentRef.value.activeNames.push('2')
      }
    }
  });
  await formEl3.validate((valid, fields) => {
    if (valid) {
      flag3 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          InstrumentRef.value["creatform3"].scrollToField(propName)
        }
      })
      if (InstrumentRef.value.activeNames.indexOf('3') == -1) {
        InstrumentRef.value.activeNames.push('3')
      }
    }
  });
  const reg = /^[0-9]{5}[-][0-9]{2}$/;
  const reg2 = /^[0-9]{5}$/;
  flag5 = reg.test(formInline.n4) || reg2.test(formInline.n4);
  if (!flag5) {
    ElMessage.error("自编码格式不正确");
  }
  formInline4.table.forEach(item => {
    if (item.n5 == 1 && item.n4.file.length <= 0) {
      flag4 = false;
    }
  })
  if (!flag4) {
    ElMessage.error("必传文件请上传");
  }
  if (flag1 && flag2 && flag3 && flag4 && flag5) {
    const fileArr = [];
    formInline4.table.forEach((item) => {
      let fileArrs = []
      item.n4.file.forEach(fileItem => {
        fileArrs.push({
          filesName: fileItem.name,
          filesUrl: fileItem.url
        })
      })
      fileArr.push({
        status: "1",
        isMultiPage: item.n6,
        smallType: item.n2,
        categoryName: item.n3,
        isUpload: item.n5,
        remark: item.n7.str,
        files: fileArrs,
        id: data.state ? item.id : null,
      });
    });
    ElLoading.service();
    instrument.sendApi(formInline, formInline2, formInline3, formInline4, status, data.editStr, data.state, fileArr, true)
        .then((res) => {
          if (res.code == 200) {
            if (status == "save") {
              ElMessage.success("新增成功");
            } else if (status == "submit") {
              ElMessage.success("提交审核成功");
            } else {
              ElMessage.success("编辑成功");
            }
            closeAll()
            dialogVisible.value = false;
            data.editStr = null;
            for (let key in formInline) {
              formInline[key] = "";
            }
            for (let key in formInline2) {
              formInline2[key] = "";
            }
            for (let key in formInline3) {
              formInline3[key] = "";
            }
            formInline4.table = [];
            formInline4.delAll = [];
            const query = proxy.$route?.query
            if (query && query.type == 'abnormalTask') {
              proxy.$router.push('/abnormalTask')
            }
          } else {
            ElMessage.error(res.msg);
          }
          const loadingInstance = ElLoading.service();
          loadingInstance.close();
        });
  }
};
const submitFormDrug = (status) => {
  rightFormDrug(drugRef.value.creatform1, drugRef.value.creatform2, drugRef.value.creatform3, status)
};
const rightFormDrug = async (formEl1, formEl2, formEl3, status) => {
  if (!formEl1 && !formEl2 && !formEl3) return;
  let flag1 = false;
  let flag2 = false;
  let flag3 = false;
  let flag4 = true;
  let flag5 = false;
  await formEl1.validate((valid, fields) => {
    if (valid) {
      flag1 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          drugRef.value["creatform1"].scrollToField(propName)
        }
      })
      if (drugRef.value.activeNames.indexOf('1') == -1) {
        drugRef.value.activeNames.push('1')
      }
    }
  });
  await formEl2.validate((valid, fields) => {
    if (valid) {
      flag2 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          drugRef.value["creatform2"].scrollToField(propName)
        }
      })
      if (drugRef.value.activeNames.indexOf('2') == -1) {
        drugRef.value.activeNames.push('2')
      }
    }
  });
  await formEl3.validate((valid, fields) => {
    if (valid) {
      flag3 = true;
    } else {
      Object.keys(fields).forEach((key, i) => {
        const propName = fields[key][0].field
        if (i == 0) {
          drugRef.value["creatform3"].scrollToField(propName)
        }
      })
      if (drugRef.value.activeNames.indexOf('3') == -1) {
        drugRef.value.activeNames.push('3')
      }
    }
  });

  let reg1 = /^[0-9]{4}[-][0-9]{2}$/;
  let reg2 = /^[0-9]{5}[-][0-9]{2}$/;
  let reg3 = /^[0-9]{4}$/;
  let reg4 = /^[0-9]{5}$/;
  if (data.numReg == 5) {
    flag5 = reg2.test(formInline.n4) || reg4.test(formInline.n4);
  } else {
    flag5 = reg1.test(formInline.n4) || reg3.test(formInline.n4);
  }
  formInline4.table.forEach(item => {
    if (item.n5 == 1 && item.n4.file.length <= 0) {
      flag4 = false;
    }
  })
  if (!flag4) {
    ElMessage.error("必传文件请上传");
  }
  if (!flag5) {
    ElMessage.error("自编码格式不正确");
  }
  if (flag1 && flag2 && flag3 && flag4 && flag5) {
    const fileArr = [];
    formInline4.table.forEach((item) => {
      let fileArrs = []
      item.n4.file.forEach(fileItem => {
        fileArrs.push({
          filesName: fileItem.name,
          filesUrl: fileItem.url
        })
      })
      fileArr.push({
        status: "1",
        isMultiPage: item.n6,
        smallType: item.n2,
        categoryName: item.n3,
        isUpload: item.n5,
        remark: item.n7.str,
        files: fileArrs,
        id: data.state ? item.id : null,
      });
    });
    ElLoading.service();
    drugEdit.sendApi(formInline, formInline2, formInline3, formInline4, status, data.editStr, data.state, fileArr, true)
        .then((res) => {
          if (res.code == 200) {
            if (status == "save") {
              ElMessage.success("新增成功");
            } else if (status == "submit") {
              ElMessage.success("提交审核成功");
            } else {
              ElMessage.success("编辑成功");
            }
            // tableListFn();
            closeAll()
            dialogVisible.value = false;
            data.editStr = null;
            for (let key in formInline) {
              formInline[key] = "";
            }
            for (let key in formInline2) {
              formInline2[key] = "";
            }
            for (let key in formInline3) {
              formInline3[key] = "";
            }
            formInline4.table = [];
            formInline4.delAll = [];
          } else {
            ElMessage.error(res.msg);
          }
          const loadingInstance = ElLoading.service();
          loadingInstance.close();
          const query = proxy.$route?.query
          if (query && query.type == 'abnormalTask') {
            proxy.$router.push('/abnormalTask')
          }
        });
  }
};

function readFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
  });
}


const orcFns = async () => {
  instanceLoading.value = true
  var arr = instance.value.instance.toDataURL().split(','),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  let file = new File([u8arr], fileName.value, {type: mime})
  new Compressor(file, {
    quality: 0.4,
    success(result) {
      let fn = async () => {
        let base64 = await readFile(result)
        const res = await postureApi.getOrcs(
            {
              "imgeByte": base64.split(",")[1],
              "templateSign": dialogRow.value.templateSign,
              "smallType": searchForm.value.n2,
              "zhType": dialogRow.value.categoryName,
              'bigType': searchForm.value.n1
            }
        )
        instanceLoading.value = false
        if (res.code == 200) {
          if (!res.data.errorCode) {
            ocrSuccess.value = true
            //生产厂家
            if (searchForm.value.n1 == 1) {
              //生产厂家-营业执照
              if (dialogRow.value.formSign.split(',').indexOf('1') != -1) {
                formBas.value.form.enterpriseName = res.data.enterpriseName
                formBas.value.form.businessLicenseCode = res.data.socialCreditCode
                formBas.value.form.businessScope = res.data.natureBusiness
                formBas.value.form.enterpriseStartTime = echoTime(res.data.issuingTime)
                if (res.data.businessTerm == '长期') {
                  formBas.value.form.businessLicenseExpiredTime = new Date('2099-12-31')
                } else {
                  formBas.value.form.businessLicenseExpiredTime = echoTime(res.data.businessTerm)
                }
                formBas.value.form.enterpriseOffice = res.data.regeisterOffice
              }
              //生产厂家-药品
              else if (dialogRow.value.formSign.split(',').indexOf('2') != -1 || dialogRow.value.formSign.split(',').indexOf('5') != -1 || dialogRow.value.formSign.split(',').indexOf('6') != -1 || dialogRow.value.formSign.split(',').indexOf('7') != -1 || dialogRow.value.formSign.split(',').indexOf('8') != -1 || dialogRow.value.formSign.split(',').indexOf('9') != -1) {
                formBas.value.licensedata.licenseNo = res.data.licenseNo
                formBas.value.licensedata.registerAddress = res.data.regiterAddr || res.data.residence
                formBas.value.licensedata.licenseAddress = res.data.prodAddress || res.data.prodAddrScope
                formBas.value.licensedata.licenseStartTime = echoTime(res.data.licenseStartTime)
                formBas.value.licensedata.licenseValidity = echoTime(res.data.licenseValidity)
                formBas.value.licensedata.licenseOffice = res.data.regeisterOffice
                formBas.value.licensedata.licenseDirector = res.data.responsibler
                formBas.value.licensedata.qualityDirector = res.data.qualityDirector
                formBas.value.licensedata.licenseLegalPerson = res.data.licenseLegalPerson
              }
            }
            //商品-药品
			else if (searchForm.value.n1 == 2 || searchForm.value.n1 == 3 || searchForm.value.n1 == 4 || searchForm.value.n1 == 5) {
              formInline.n1 = res.data.commonName
              formInline.n2 = res.data.commonName
              formInline.n3 = res.data.pinyinCode
              formInline.n10 = res.data.packageSpecification
              formInline.n8 = echoNum(res.data.validityTime) //提取数字
              formInline.n15 = res.data.producingArea
              formInline2.n26 = res.data.approvalNumber
              formInline2.n3 = res.data.approvalValidity
              formInline2.n5 = res.data.registrationNo
              formInline2.n6 = res.data.drugStandard
            }
            //资质-供应商
            else if (searchForm.value.n1 == 6) {
              //供应商-营业执照
              if (dialogRow.value.formSign.split(',').indexOf('1') != -1) {
                supplierRef1.value.form.enterpriseName = res.data.enterpriseName
                supplierRef1.value.form.socialCreditCode = res.data.socialCreditCode
                if (res.data.businessTerm == '长期') {
                  supplierRef1.value.form.businessTerm = new Date('2099-12-31')
                } else {
                  supplierRef1.value.form.businessTerm = echoTime(res.data.businessTerm)
                }
                supplierRef1.value.form.residence = res.data.residence
                supplierRef1.value.form.businessNature = res.data.natureBusiness
                supplierRef1.value.form.issuingOffice = res.data.regeisterOffice
                supplierRef1.value.form.issuingTime = res.data.issuingTime
              }
              //供应商-生产许可
              if (dialogRow.value.formSign.split(',').indexOf('2') != -1 || dialogRow.value.formSign.split(',').indexOf('7') != -1 || dialogRow.value.formSign.split(',').indexOf('8') != -1 || dialogRow.value.formSign.split(',').indexOf('9') != -1 || dialogRow.value.formSign.split(',').indexOf('10') != -1 || dialogRow.value.formSign.split(',').indexOf('11') != -1) {
                supplierRef1.value.licensedata.licenseNo = res.data.licenseNo
                supplierRef1.value.licensedata.registerAddress = res.data.regiterAddr || res.data.residence
                supplierRef1.value.licensedata.licenseAddress = res.data.prodAddress || res.data.prodAddrScope
                supplierRef1.value.licensedata.licenseStartTime = echoTime(res.data.licenseStartTime)
                supplierRef1.value.licensedata.licenseValidity = echoTime(res.data.licenseValidity)
                supplierRef1.value.licensedata.licenseOffice = res.data.regeisterOffice
                supplierRef1.value.licensedata.licenseDirector = res.data.responsibler
                supplierRef1.value.licensedata.qualityDirector = res.data.qualityDirector
                supplierRef1.value.licensedata.licenseLegalPerson = res.data.licenseLegalPerson
              }
            } else if (searchForm.value.n1 == 7) {
              //客户-营业执照
              if (dialogRow.value.formSign.split(',').indexOf('1') != -1) {
                customRef1.value.form.enterpriseName = res.data.enterpriseName
                customRef1.value.form.socialCreditCode = res.data.socialCreditCode
                if (res.data.businessTerm == '长期') {
                  customRef1.value.form.businessTerm = new Date('2099-12-31')
                } else {
                  customRef1.value.form.businessTerm = echoTime(res.data.businessTerm)
                }
                customRef1.value.form.residence = res.data.residence
                customRef1.value.form.businessNature = res.data.natureBusiness
                customRef1.value.form.issuingOffice = res.data.regeisterOffice
                customRef1.value.form.issuingTime = res.data.issuingTime
              }
              //客户-生产许可
              if (searchForm.value.n2 == 9) {
                customRef1.value.form.zylicenseName = res.data.enterpriseName
                customRef1.value.form.zylicenseAddress = res.data.residence
                if (res.data.business == '非营利性') {
                  customRef1.value.form.business = '2'
                } else if (res.data.business == '营利性') {
                  customRef1.value.form.business = '1'
                }
                customRef1.value.form.zylicenseLegalPerson = res.data.licenseLegalPerson
                customRef1.value.form.zylicenseDirector = res.data.licenseDirector
                customRef1.value.form.zylicenseStartTime = echoTime(res.data.licenseStartTime)
                customRef1.value.form.zylicenseValidity = echoTime(res.data.licenseValidity)
                customRef1.value.form.zylicenseNo = res.data.licenseNo
                customRef1.value.form.zylicenseOffice = res.data.licenseOffice
              } else {
                if (dialogRow.value.formSign.split(',').indexOf('2') != -1 || dialogRow.value.formSign.split(',').indexOf('7') != -1 || dialogRow.value.formSign.split(',').indexOf('8') != -1 || dialogRow.value.formSign.split(',').indexOf('9') != -1 || dialogRow.value.formSign.split(',').indexOf('10') != -1 || dialogRow.value.formSign.split(',').indexOf('11') != -1) {
                  customRef1.value.licensedata.licenseNo = res.data.licenseNo
                  customRef1.value.licensedata.registerAddress = res.data.regiterAddr || res.data.residence
                  customRef1.value.licensedata.licenseAddress = res.data.prodAddress || res.data.prodAddrScope
                  customRef1.value.licensedata.licenseStartTime = echoTime(res.data.licenseStartTime)
                  customRef1.value.licensedata.licenseValidity = echoTime(res.data.licenseValidity)
                  customRef1.value.licensedata.licenseOffice = res.data.regeisterOffice
                  customRef1.value.licensedata.licenseDirector = res.data.responsibler
                  customRef1.value.licensedata.qualityDirector = res.data.qualityDirector
                  customRef1.value.licensedata.licenseLegalPerson = res.data.licenseLegalPerson
                }
              }
            }
          } else {
            ElMessage.error(res.data.errorMsg)
          }
        } else {
          ElMessage.error(res.msg)
        }
      }
      fn()
    },
    error(err) {
      console.log(err.message);
    },
  });

}
const cusSaveFn = (id) => {
  saveOCR(id)
}
const echoNum = (str) => {
  console.log(str.replace(/[^\d]/g, " "))
  return str.replace(/[^\d]/g, " ");
}
const screenshotStatus = ref(false);
// 销毁组件函数
const destroyComponent = function (status) {
  screenshotStatus.value = status;
}
const beforeFile = (file, row) => {
  let fileType = file.name.split('.')[file.name.split('.').length - 1];
  if (file.size > 2097152) {
    ElMessage.error("文件不能大于2M");
    return false;
  } else {
    if (fileType != 'jpg' && fileType != 'jpeg' && fileType != 'png' && fileType != 'bmp' && fileType != 'pdf') {
      ElMessage.error("不支持此文件类型");
      return false;
    } else {
      let bigTypes = title.value.split('-')[0]
      let smallTypes = title.value.split('-')[1]
      let messTypes = echoFile(row.categoryName)
      dataUpload.value = {
        fileGroup: "IntelligentInput",
        zhType: bigTypes,
        smallClass: smallTypes,
        businessType: "postureRecording",
        fjType: messTypes
      }
    }
  }
}
// 获取裁剪区域图片信息
const getImg = async function (base64) {
  screenshotStatus.value = false;
  const res = await postureApi.getORCDan({
    "imgeByte": base64
  })
  if (res.code == 200) {
    if (orcInd.value.time) {
      if (orcInd.value.ind == 'businessLicenseExpiredTime' && res.data.word == '长期') {
        formBas.value.form[orcInd.value.ind] = new Date('2099-12-31')
        return
      }
      let obj = []
      let arr = res.data.word.split('')
      arr.forEach(item => {
        if (item == '年' || item == '月') {
          obj.push('-')
        } else if (item == '日') { /* empty */
        } else {
          obj.push(item)
        }
      })
      let time = new Date(obj.join(''))
      if (time != 'Invalid Date') {
        if (searchForm.value.n1 == 1) {
          if (orcInd.value.type == 1) {
            formBas.value.form[orcInd.value.ind] = time
          } else if (orcInd.value.type == 2) {
            formBas.value.licensedata[orcInd.value.ind] = time
          } else if (orcInd.value.type == 3) {
            formBas.value.modalTableList_GMPRef[orcInd.value.ind] = time
          }
        } else if (searchForm.value.n1 == 2 || searchForm.value.n1 == 3 || searchForm.value.n1 == 4 || searchForm.value.n1 == 5) {
          if (orcInd.value.type == 1) {
            formInline[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 2) {
            formInline2[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else {
            formInline3[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          }
        } else if (searchForm.value.n1 == 6) {
          if (orcInd.value.type == 1) {
            supplierRef1.value.form[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 2) {
            supplierRef1.value.licensedata[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 3) {
            supplierRef1.value.entrust[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 4) {
            supplierRef1.value.warranty[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 5) {
            supplierRef1.value.modalTableList_GMPRef[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          }
        } else if (searchForm.value.n1 == 7) {
          if (orcInd.value.type == 1) {
            customRef1.value.form[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 2) {
            customRef1.value.licensedata[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 3) {
            customRef1.value.entrust[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 4) {
            customRef1.value.warranty[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          } else if (orcInd.value.type == 5) {
            customRef1.value.modalTableList_GMPRef[orcInd.value.ind] = functionIndex.transformTimestamp(time)
          }
        }
      }
    } else {
      if (searchForm.value.n1 == 1) {
        if (orcInd.value.type == 1) {
          formBas.value.form[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 2) {
          formBas.value.licensedata[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 3) {
          formBas.value.modalTableList_GMPRef[orcInd.value.ind] = res.data.word
        }
      } else if (searchForm.value.n1 == 2 || searchForm.value.n1 == 3 || searchForm.value.n1 == 4 || searchForm.value.n1 == 5) {
        if (orcInd.value.type == 1) {
          formInline[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 2) {
          formInline2[orcInd.value.ind] = res.data.word
        } else {
          formInline3[orcInd.value.ind] = res.data.word
        }
      } else if (searchForm.value.n1 == 6) {
        if (orcInd.value.type == 1) {
          supplierRef1.value.form[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 2) {
          supplierRef1.value.licensedata[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 3) {
          supplierRef1.value.entrust[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 4) {
          supplierRef1.value.warranty[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 5) {
          supplierRef1.value.modalTableList_GMPRef[orcInd.value.ind] = res.data.word
        }
      } else if (searchForm.value.n1 == 7) {
        if (orcInd.value.type == 1) {
          customRef1.value.form[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 2) {
          customRef1.value.licensedata[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 3) {
          customRef1.value.entrust[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 4) {
          customRef1.value.warranty[orcInd.value.ind] = res.data.word
        } else if (orcInd.value.type == 5) {
          customRef1.value.modalTableList_GMPRef[orcInd.value.ind] = res.data.word
        }
      }
    }
  } else {
    ElMessage.error(res.msg)
  }
}

onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  bigType.value = await proxy.getDictList("directory_category")
  formType.value = await proxy.getDictList("directory_file_name")
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  uuids.value = uuid.v1()
})
watchEffect(() => {

})
watch(
    () => formInline.n12,
    (newValue) => {
      let name = "";
      data.venderDrug.records.forEach((record) => {
        if (record.id == newValue) {
          name = record.enterpriseName;
        }
      });
      disappApi
          .siteDrug({
            name: name,
          })
          .then((res) => {
            data.siteType = res.data.licenseList;
          });
    }
);
watch(() => supplierRef2.value.form, () => {
  if (supplierRef1.value) {
    for (let key in supplierRef2.value.form) {
      supplierRef1.value.form[key] = supplierRef2.value.form[key]
    }
  }
}, {deep: true})
watch(() => customRef2.value.form, () => {
  if (customRef1.value) {
    for (let key in customRef2.value.form) {
      customRef1.value.form[key] = customRef2.value.form[key]
    }
  }
}, {deep: true})
watch(() => customRef2.value.modalTableList_entrust, () => {
  if (customRef1.value) {
    customRef1.value.modalTableList_entrust = [...customRef2.value.modalTableList_entrust]
  }
}, {deep: true})
watch(() => supplierRef2.value.modalTableList_entrust, () => {
  if (customRef1.value) {
    supplierRef1.value.modalTableList_entrust = [...supplierRef2.value.modalTableList_entrust]
  }
}, {deep: true})
watch(() => supplierRef2.value.modalTableList, () => {
  if (supplierRef1.value) {
    supplierRef1.value.modalTableList = [...supplierRef2.value.modalTableList]
  }
}, {deep: true})
watch(() => supplierRef2.value.licensedata, () => {
  if (supplierRef1.value) {
    let ind = -1
    supplierRef1.value.modalTableList.forEach((item, index) => {
      if (item.type == supplierRef2.value.licensedata.type) {
        ind = index
      }
    })
    if (ind != -1) {
      supplierRef1.value.modalTableList[ind] = {...supplierRef2.value.licensedata}
    } else {
      supplierRef1.value.modalTableList.push({...supplierRef1.value.licensedata})
    }
  }
}, {deep: true})
watch(() => customRef2.value.licensedata, () => {
  if (customRef1.value) {
    let ind = -1
    customRef1.value.modalTableList.forEach((item, index) => {
      if (item.type == customRef2.value.licensedata.type) {
        ind = index
      }
    })
    if (ind != -1) {
      customRef1.value.modalTableList[ind] = {...customRef2.value.licensedata}
    } else {
      customRef1.value.modalTableList.push({...customRef1.value.licensedata})
    }
  }
}, {deep: true})
const gspRef = ref()
watch(() => supplierRef1.value.modalTableList_GMPRef, () => {
  if (supplierRef1.value) {
    let ind = -1
    supplierRef1.value.modalTableList_GMP.forEach((item, index) => {
      if (item.uuid == supplierRef1.value.modalTableList_GMPRef.uuid) {
        ind = index
      }
    })
    if (ind != -1) {
      supplierRef1.value.modalTableList_GMP[ind] = {...supplierRef1.value.modalTableList_GMPRef}
    } else {
      supplierRef1.value.modalTableList_GMP.push({...supplierRef1.value.modalTableList_GMPRef})
    }
  }
}, {deep: true})
watch(() => searchForm.value.n1, () => {
  searchForm.value.n2 = null
})
watch(() => customRef1.value.modalTableList_GMPRef, () => {
  if (customRef1.value) {
    let ind = -1
    customRef1.value.modalTableList_GMP.forEach((item, index) => {
      if (item.uuid == customRef1.value.modalTableList_GMPRef.uuid) {
        ind = index
      }
    })
    if (ind != -1) {
      customRef1.value.modalTableList_GMP[ind] = {...customRef1.value.modalTableList_GMPRef}
    } else {
      customRef1.value.modalTableList_GMP.push({...customRef1.value.modalTableList_GMPRef})
    }
  }
  gspRef.value = [...customRef1.value.modalTableList_GMP]
}, {deep: true})
watch(() => customRef2.value.modalTableList_GMP, () => {
  if (customRef1.value) {
    customRef1.value.modalTableList_GMP = [...customRef2.value.modalTableList_GMP]
  }
}, {deep: true})
watch(() => supplierRef2.value.modalTableList_GMP, () => {
  if (supplierRef1.value) {
    supplierRef1.value.modalTableList_GMP = [...supplierRef2.value.modalTableList_GMP]
  }
}, {deep: true})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  searchForm,
  formList,
  creatRules,
  dialogRow,
  bigType,
  smallType,
  title,
  drugOn,
  insOn,
  foodFn,
  disFn,
})
</script>
<style lang='scss' scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
  display: contents !important;
}

.el-form-item {
  margin-bottom: 20px;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
  display: contents !important;
}

::v-deep .el-upload-dragger {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stateTitle {
  position: absolute;
  font-size: 15px;
  top: 35px;
  right: 65px;
}
</style>
