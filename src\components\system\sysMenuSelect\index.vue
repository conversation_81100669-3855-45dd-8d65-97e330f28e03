<template>
  <el-dialog
    v-model="visible"
    style="max-height: 600px"
    title="系统菜单选择"
    @closed="$emit('closed')"
    top="1vh"
  >
    <el-row style="cursor: pointer">
      <el-col :span="16">
        <el-container v-loading="listLoading">
          <!-- <el-header style="padding-left: 0px">
            <div class="right-panel-search">
              <el-input
                v-model="searchForm.name"
                placeholder="输入机构名称"
                clearable
              ></el-input>
              <el-button type="primary" icon="el-icon-search" @click="search"></el-button>
            </div>
          </el-header> -->
          <el-main class="nopadding" style="height: 360px">
            <el-tree
              :data="dataList"
              show-checkbox
              default-expand-all
              node-key="id"
              ref="tree"
              check-strictly="true"
              highlight-current
              :props="menuProps"
              :default-checked-keys="selects"
              @node-click="menuNodeClick"
            >
            </el-tree>

            <div class="buttons">
              <!-- <el-button @click="resetChecked">清空</el-button> -->
            </div>
          </el-main>
        </el-container>
      </el-col>
      <el-col :span="1"> </el-col>
      <el-col :span="7">
        <el-header style="padding-top: 0px; padding-bottom: 0px"
          ><h4>数据权限列表</h4></el-header
        >
        <el-main style="height: 360px; padding: 0">
          <el-table
            :data="dataRuleList"
            v-loading="dataRuleLoading"
            ref="dataRuleTable"
            :row-key="id"
            @select="dataRuleSelectClick"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="ruleName" label="权限名称"> </el-table-column>
          </el-table>
        </el-main>
      </el-col>
    </el-row>

    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submitComplet()">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import sysMenuService from "@/api/model/sys/sysMenuService";
import sysDataRulesService from "@/api/model/sys/sysDataRulesService";
import sysRoleDataruleService from "@/api/model/sys/sysRoleDataruleService";
import select from "@/config/select";
export default {
  components: {},
  props: {
    //是否多选
    isMultiple: { type: Boolean, default: false },
    //回调函数
    selectChange: { type: Function },
  },
  data() {
    return {
      //数据列表
      dataList: {},
      menuProps: {
        children: "children",
        label: "name",
      },
      //查询表单
      searchForm: {},
      //数据列选中行
      selection: [],
      //列表加载
      listLoading: false,
      //已选择的数据
      selectData: [],
      selects: [],
      visible: true,
      //当前角色
      role: {},
      //当前菜单
      menu: {},

      //菜单权限列表
      dataRuleList: [],
      dataRuleLoading: false,
    };
  },
  mounted() {
    setTimeout(() => {
      //刷新数据列表
      this.getDataList();
    }, 0);
  },
  methods: {
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      //初始化数据列表
      this.dataList = [];
      //请求接口
      this.listLoading = true;
      var res = await sysMenuService.treeData({
        //查询参数
        ...this.searchForm,
      });
      this.listLoading = false;
      if (res.code == 200) {
        //数据列表
        this.dataList = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
    },

    /*
     * 选中的数据
     * @author: 路正宁
     * @date: 2023-03-31 17:26:29
     */
    selecteds(role, menuIds) {
      //选中指定的系统菜单
      this.selects = menuIds;
      this.role = role;
    },
    /*
     * 提交选择结果
     * @author: 路正宁
     * @date: 2023-04-03 09:55:23
     */
    submitComplet() {
      this.selectChange(this.role, this.$refs.tree.getCheckedKeys());
      this.visible = false;
    },
    /*
     * 系统菜单点击事件
     * @author: 路正宁
     * @date: 2023-04-04 10:30:34
     */
    async menuNodeClick(row, node, rootNode) {
      //初始化数据列表
      this.dataRuleList = [];
      this.menu = row;
      //请求接口
      this.dataRuleLoading = true;
      var res = await sysDataRulesService.list({
        "sysMenu.id": row.id,
        //当前页码
        current: 1,
        //每页条数
        size: 1000,
      });

      if (res.code == 200) {
        //数据列表
        this.dataRuleList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
        return;
      }
      //角色数据权限查询
      var result = await sysRoleDataruleService.list({
        "sysRole.id": this.role.id,
        menuId: row.id,
      });
      if (result.code != 200) {
        this.$Response.errorNotice(res, "查询失败");
        return;
      }
      this.dataRuleLoading = false;
      //遍历数据权限列表
      this.dataRuleList.forEach((item) => {
        //选中已配置的数据权限
        for (var j = 0; j < result.data.records.length; j++) {
          if (result.data.records[j].sysDataRules.id == item.id) {
            this.$nextTick(() => {
              this.$refs.dataRuleTable.toggleRowSelection(item);
            });
          }
        }
      });
    },
    /*
     * 数据权限勾选事件
     * @author: 路正宁
     * @date: 2023-04-04 13:27:13
     */
    async dataRuleSelectClick(selection, row) {
      var ruleData = [];
      for (var j = 0; j < selection.length; j++) {
        ruleData[j] = selection[j].id;
      }
      //数据权限ID
      var ruleDataIds = ruleData.join(",");
      //角色
      var roleId = this.role.id;
      //菜单id
      var menuId = this.menu.id;
      this.dataRuleLoading = true;
      var result = await sysRoleDataruleService.configDataRule(
        roleId,
        menuId,
        ruleDataIds
      );
      if (result.code == 200) {
        this.$message.success("配置成功");
      } else {
        this.$Response.errorNotice(result, "查询失败");
        return;
      }
      this.dataRuleLoading = false;
    },
  },
};
</script>

<style>
.el-dialog__body {
  padding: 10px 20px;
}
</style>
