import config from '@/config';
import http from '@/utils/request';

export default {
    /*
     * 获取系统菜单
     * @author: 路正宁
     * @date: 2023-04-07 10:48:24
     */
    menuTree: function () {
        return http.get(`/auth/user/menuTree`);
    },
    menuTreeByOrg: function (params) {
        return http.get(`/auth/user/switchOrg`, params);
    },
    // 网点切换
    switchBranch: function (params) {
        return http.get(`/auth/user/switchBranch`, params);
    },
    queryById: function (id) {
        return http.get('/sys/task/queryById', { id: id });
    },
    getMessageList: function (params) {
        return http.get('/sys/sysMessage/list', params);
    },
    updateStatus: function (params) {
        return http.get('/sys/sysMessage/updateStatus', params);
    },
    getCompanyMesssage: function (params) {
        return http.get('/tms/index/company/getMessage', params);
    },
    menu: {
        myMenus: {
            url: `${config.API_URL}/system/menu/my/1.6.1`,
            name: '获取我的菜单',
            get: async function () {
                return await http.get(this.url);
            }
        },
        list: {
            url: `${config.API_URL}/system/menu/list`,
            name: '获取菜单',
            get: async function () {
                return await http.get(this.url);
            }
        }
    },
    dic: {
        tree: {
            url: `${config.API_URL}/system/dic/tree`,
            name: '获取字典树',
            get: async function () {
                return await http.get(this.url);
            }
        },
        list: {
            url: `${config.API_URL}/system/dic/list`,
            name: '字典明细',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        },
        get: {
            url: `${config.API_URL}/system/dic/get`,
            name: '获取字典数据',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    role: {
        list: {
            url: `${config.API_URL}/system/role/list2`,
            name: '获取角色列表',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    dept: {
        list: {
            url: `${config.API_URL}/system/dept/list`,
            name: '获取部门列表',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    user: {
        list: {
            url: `${config.API_URL}/system/user/list`,
            name: '获取用户列表',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    app: {
        list: {
            url: `${config.API_URL}/system/app/list`,
            name: '应用列表',
            get: async function () {
                return await http.get(this.url);
            }
        }
    },
    log: {
        list: {
            url: `${config.API_URL}/system/log/list`,
            name: '日志列表',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    table: {
        list: {
            url: `${config.API_URL}/system/table/list`,
            name: '表格列管理列表',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        },
        info: {
            url: `${config.API_URL}/system/table/info`,
            name: '表格列管理详情',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    tasks: {
        list: {
            url: `${config.API_URL}/system/tasks/list`,
            name: '系统任务管理',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    }
};
