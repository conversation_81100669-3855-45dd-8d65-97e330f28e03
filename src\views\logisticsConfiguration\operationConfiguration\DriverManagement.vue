<template>
    <div class="app-container">
        <!--搜索-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item label="司机姓名" prop="driverName">
                    <el-input v-model="queryParams.driverName" clearable placeholder="请输入司机姓名" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="驾驶证类型" prop="driverCardType">
                    <el-select v-model="queryParams.driverCardType" clearable placeholder="请选择驾驶证类型" @change="handleQuery">
                        <el-option v-for="(item, idx) in cardTypeOptions" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="司机状态" prop="postStatus">
                    <el-select v-model="queryParams.postStatus" clearable placeholder="请选择状态" @change="handleQuery">
                        <el-option v-for="(item, idx) in statusOptions" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <el-button v-hasPermi="['transport:driver:add']" icon="el-icon-plus" type="primary" @click="toDriver">员工转司机</el-button>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" :tableID="'DriverManagement'" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="DriverManagement" v-loading="loading" :columns="columns" :data="infoList">
                <template #postStatus="{ row }">
                    <span>{{ statusFormat(row) }}</span>
                </template>
                <template #driverCardType="{ row }">
                    <span>{{ driverCardTypeFormat(row) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button v-hasPermi="['transport:driver:edit']" icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(row)">修改</el-button>
                    <el-button v-hasPermi="['transport:driver:remove']" icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(row)">注销</el-button>
                </template>
            </column-table>

            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
            </div>
        </el-card>

        <!-- 添加或修改司机对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="650px">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="司机姓名" prop="driverName">
                    <el-input v-model="form.driverName" disabled placeholder="请输入司机姓名" />
                </el-form-item>
                <el-form-item label="司机电话" prop="driverPhone">
                    <el-input v-model="form.driverPhone" disabled placeholder="请输入司机电话" />
                </el-form-item>
                <el-form-item label="驾驶证类型" prop="driverCardType">
                    <el-select v-model="form.driverCardType" clearable placeholder="请选择驾驶证类型">
                        <el-option v-for="(item, idx) in cardTypeOptions" :key="idx" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="驾驶证号" prop="driverCardCode">
                    <el-input v-model="form.driverCardCode" placeholder="请输入驾驶证号" />
                </el-form-item>
                <el-form-item label="司机状态">
                    <el-radio-group v-model="form.postStatus">
                        <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.name }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="当前位置" prop="place">
                    <el-cascader v-model="place" :options="areaOptions" filterable placeholder="请选择当前位置" style="width: 100%" @change="addressChange" />
                </el-form-item>
                <el-form-item label="加油卡卡号" prop="fuelCardNo">
                    <el-input v-model="form.fuelCardNo" placeholder="请输入加油卡卡号" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="60" placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </template>
        </el-dialog>
        <userSelect v-if="userOpen" v-model:show="userOpen" :type="'flow_user'" :userData="userData" :values="values" @changeShow="changeShow" @onConfirm="addDriver"></userSelect>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import userSelect from '@/components/userSelect';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
export default {
    name: 'DriverManagement',
    components: { SearchButton, ColumnTable, RightToolbar, userSelect },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 司机表格数据
            infoList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                driverTypeCode: null,
                driverCode: null,
                driverName: null,
                driverCardType: null,
                driverCardCode: null,
                postStatus: null,
                longitude: null,
                latitude: null
            },
            // 表单参数
            form: {
                fuelCardNo: undefined
            },
            // 表单校验
            rules: {
                driverCardType: [{ required: true, message: '驾驶证类型不能为空', trigger: 'blur' }],
                driverCardCode: [
                    { required: true, message: '驾驶证号不能为空', trigger: 'blur' },
                    { pattern: /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}(\d|x|X)$/, message: '请输入正确的驾驶证号', trigger: 'blur' }
                ]
            },
            values: {
                title: '',
                roleType: '3',
                minNum: 1
            },
            userOpen: false,
            statusOptions: [],
            areaOptions: [],
            cardTypeOptions: [],
            place: '',
            userData: [],
            columns: [
                { title: '司机姓名', key: 'driverName', width: '140px', fixed: 'left', columnShow: true, showOverflowTooltip: true },
                { title: '司机编号', key: 'driverCode', width: '180px', columnShow: true },
                { title: '司机电话', key: 'driverPhone', width: '140px', columnShow: true },
                { title: '驾驶证类型', key: 'driverCardType', width: '140px', columnShow: true },
                { title: '驾驶证号', key: 'driverCardCode', width: '180px', columnShow: true },
                { title: '当前位置', key: 'complateAddress', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '司机状态', key: 'postStatus', width: '180px', columnShow: true },
                { title: '加油卡卡号', key: 'fuelCardNo', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '备注', key: 'remark', columnShow: true, width: '180px', showOverflowTooltip: true },
                { title: '操作', key: 'opt', width: '140px', fixed: 'right', hideFilter: true, columnShow: true }
            ]
        };
    },
    async created() {
        this.getList();
        // 状态
        this.statusOptions = await this.getDictList('sys_normal_disable');
        // 驾驶证类型
        this.cardTypeOptions = await this.getDictList('driver_card_type');
    },
    methods: {
        // 新增司机
        addDriver(data) {
            let driverList = data.userList.map((item) => {
                return { driverName: item.name, driverCode: item.id, driverPhone: item.phone };
            });
            operationConfiguration.addDriver(driverList).then((response) => {
                if (response.code == 200) {
                    this.msgSuccess('员工转司机成功');
                    this.values.open = false;
                    this.userOpen = false;
                    this.getList();
                }
            });
        },
        // 选择省市区
        addressChange(value) {
            this.form.provinceId = value[0];
            this.form.cityId = value[1];
            this.form.countyId = value[2];
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 隐藏组件弹窗
        changeShow(value) {
            this.values.open = value;
            this.userOpen = value;
        },
        /** 驾驶证类型字典转换 */
        driverCardTypeFormat(val) {
            return this.selectDictLabel(this.cardTypeOptions, val.driverCardType);
        },
        /** 查询司机列表 */
        getList() {
            this.loading = true;
            this.infoList = [];
            operationConfiguration.listdDiverInfo(this.queryParams).then((response) => {
                if (response.code === 200 && response.data) {
                    this.infoList = response.data.records || [];
                    this.total = response.data.total || 0;
                }
                this.loading = false;
            });
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.areaOptions = this.getSysAreasThird;
            this.reset();
            this.open = true;
            this.title = '添加司机';
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids.join(',');
            const name = row.driverName || ids;
            this.$confirm('是否确认删除司机"' + name + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return operationConfiguration.delDiverInfo({ ids: ids });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                '/fourPL/transport/driver/export',
                {
                    ...this.queryParams
                },
                `driver_info.xlsx`
            );
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.areaOptions = this.getSysAreasThird;
            this.reset();
            const id = row.id || this.ids;
            operationConfiguration.queryDiverInfoById({ id }).then((response) => {
                this.form = response.data;
                this.place = [this.form.provinceId, this.form.cityId, this.form.countyId];
                this.open = true;
                this.title = '修改司机';
            });
        },
        // 表单重置
        reset() {
            this.place = '';
            this.form = {
                id: null,
                driverName: null,
                driverCardType: null,
                driverCardCode: null,
                postStatus: '0',
                provinceId: null,
                cityId: null,
                countyId: null,
                remark: null
            };
            this.$refs['form'] ? this.$refs['form'].resetFields() : '';
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.handleQuery();
        },
        /** 状态字典转换 */
        statusFormat(val) {
            return this.selectDictLabel(this.statusOptions, val.postStatus);
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        console.log(this.form);
                        operationConfiguration.updateDriver(this.form).then((response) => {
                            if (response.code === 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        operationConfiguration.updateDriver(this.form).then((response) => {
                            if (response.code === 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
        // 显示员工转司机界面
        toDriver() {
            this.userData = [];
            operationConfiguration.getDriverList().then((response) => {
                if (response.code == 200 && response.data && response.data) {
                    this.userData = response.data.map((item) => {
                        return { id: item.driverCode, name: item.driverName, phone: item.driverPhone };
                    });
                    this.values = {
                        title: '员工转司机',
                        roleType: '3',
                        data: null,
                        minNum: 1
                    };
                    this.userOpen = true;
                }
            });
        }
    }
};
</script>

<style scoped>
.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
</style>
