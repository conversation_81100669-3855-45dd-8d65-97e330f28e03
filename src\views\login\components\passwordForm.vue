<template>
	<el-form ref="loginForm" :model="form" :rules="rules" hide-required-asterisk="true" label-position="top" label-width="0"
		size="large" @keyup.enter="submitLogin">
		<el-form-item class="el-form-item__label" label="手机号" prop="user">
			<el-input v-model="form.user" type="number" maxlength="11" class="input" clearable placeholder="请输入手机号"></el-input>
		</el-form-item>
		<el-form-item class="el-form-item__label2" label="密码" prop="password">
			<el-input v-model="form.password" class="input2" clearable placeholder="请输入密码" show-password></el-input>
			<!-- show-password  -->
		</el-form-item>
		<el-form-item v-if="captchaEnabled" label="验证码" prop="code">
			<el-input v-model="form.code" type="number" maxlength="4" placeholder="请输入验证码" style="width: 71%"> </el-input>
			<div class="login-code">
				<img :src="codeImg" class="login-code-img" @click="getCode" />
			</div>
		</el-form-item>
		<el-form-item>
			<el-button :loading="islogin" class="login" type="primary" @click="submitLogin">{{
				$t("login.signIn")
			}}
			</el-button>
		</el-form-item>
<!--		<div class="login-reg">-->
<!--			新用户-->
<!--			<router-link class="regist" to="/user_register">注册</router-link>-->
<!--			<a class="forget" @click="handlerChange">忘记密码？</a>-->
<!--		</div>-->
	</el-form>
</template>

<script>
import tool from '@/utils/tool';
// import singlePointLogin from "@/mixins/singlePointLogin.vue";
import homeApi from '@/api/sys/home'
export default {
	// mixins:[singlePointLogin],
	props: {
		handlerReset: Function,
	},
	data() {
		return {
			form: {
				user: "",
				password: "",
				autologin: false,
				code: '',
				uuid: null,
			},
			rules: {
				user: [
					{
						required: true,
						message: '请输入手机号',
						trigger: "blur",
					},
					// {
					// 	pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/,
					// 	message: '请输入正确的手机号',
					// 	trigger: 'blur'
					// }
				],
				password: [
					{
						required: true,
						message: this.$t("login.PWError"),
						trigger: "blur",
					},
				],
				code: [{ required: true, trigger: ['change', 'blur'], message: '请输入验证码' }]
			},
			//登录按钮锁
			islogin: false,
			orgList: [],
			codeImg: null,
			captchaEnabled: false,
		};
	},
	watch: {},
	mounted() {
		this.$TOOL.cookie.remove("TOKEN");
		this.$TOOL.data.remove("USER_INFO");
		this.$TOOL.data.remove("ROLE_LIST");
		this.$TOOL.data.remove("Organization");
		this.$TOOL.data.remove("MENU");
		this.$TOOL.data.remove("PERMISSIONS");
		this.$TOOL.data.remove("orgKey");
		this.$TOOL.data.remove("DASHBOARDGRID");
		this.$TOOL.data.remove("grid");
	},
	created() {
		this.getCode();
	},
	methods: {
		/**
		 * 获取图片验证码
		 */
		getCode() {
			const timeoutPromise = new Promise((_, reject) => 
				setTimeout(() => reject(new Error('请求超时')), 10000)
			);
			
			Promise.race([
				this.$API.auth.getCode(),
				timeoutPromise
			])
			.then((res) => {
				this.captchaEnabled = res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled;
				if (this.captchaEnabled) {
					this.codeImg = res.data.codeImg;
					this.form.uuid = res.data.uuid;
				}
			})
			.catch(error => {
				console.error('获取验证码失败:', error);
				// 失败时的降级处理
				this.captchaEnabled = false; // 临时禁用验证码
			});
		},
		parseUrlParams(url) {
			var params = {};

			// 判断 URL 中是否存在参数
			if (url.indexOf('?') !== -1) {
				// 获取参数部分
				var paramString = url.split('?')[1];

				// 分割参数字符串，得到参数数组
				var paramArray = paramString.split('&');

				// 遍历参数数组，解析每个参数并存入 params 对象中
				for (var i = 0; i < paramArray.length; i++) {
					var param = paramArray[i].split('=');
					var paramName = decodeURIComponent(param[0]);
					var paramValue = decodeURIComponent(param[1]);

					// 判断参数名是否已存在，如果已存在则将当前参数值添加到已存在的参数值的数组中
					if (params[paramName]) {
						params[paramName] = [].concat(params[paramName], paramValue);
					} else {
						params[paramName] = paramValue;
					}
				}
			}

			return params;
		},
		/*
		 * 提交登录事件
		 * @author: 路正宁
		 * @date: 2023-03-21 11:17:03
		 */
		async submitLogin() {
			//表单校验
			try {
				await this.$refs.loginForm.validate();
			} catch (error) {
				return false;
			}

			//登录请求体
			var loginBody = {
				username: this.form.user,
				password: this.$TOOL.crypto.MD5(this.form.password),
				code: this.form.code,
				uuid: this.form.uuid,
			};
			//锁定登录按钮
			this.islogin = true;

			try {
				//执行登录
				const loginUser = await this.$API.auth.login(loginBody);
				
				if (loginUser.code === 401) {
					this.$message.error(loginUser.msg || "用户名或密码不正确");
					if (this.captchaEnabled) {
						this.getCode();
					}
					this.islogin = false;
					return false;
				}

				if (loginUser.code !== 200) {
					if (this.captchaEnabled) {
						this.getCode();
					}
					this.islogin = false;
					return false;
				}

				//登录成功处理
				this.$TOOL.cookie.set("TOKEN", loginUser.data.token);
				this.$TOOL.data.set("USER_INFO", loginUser.data.userInfo);
				this.$TOOL.data.set("ROLE_LIST", loginUser.data.roleList);
				this.$TOOL.data.set("Organization", loginUser.data.orgList);
				this.$TOOL.data.set("BranchList", loginUser.data.branchList);

				//获取系统菜单
				await this.getMenuTree();
				this.$message.success("登录成功");
			} catch (error) {
				console.error(error);
			} finally {
				this.islogin = false;
			}

			//获取菜单
			this.$router.replace({
				path: "/home",
			});
		},
		/*
		 *@functionName: 登录接口
		 *@params1: loginBody:登录参数
		 *@author: 路正宁
		 *@date: 2023-03-21 15:14:51
		 */
		async login(loginBody) {
			//执行登录
			var loginUser = await this.$API.auth.login(loginBody);
			//校验对象是否为空
			if (this.$ObjectUtils.isEmpty(loginUser)) {
				this.$MessageBox.errorNotie("登录失败，请重试");
				return null;
			}
			//校验状态码是否成功
			if (loginUser.code == 200) {
				//登录成功处理
				//保存token到cookie中
				this.$TOOL.cookie.set("TOKEN", loginUser.data.token, {
					expires: this.form.autologin ? 24 * 60 * 60 : 0,
				});
				//保存用户信息到cookie中
				this.$TOOL.data.set("USER_INFO", loginUser.data.userInfo);
				this.$TOOL.data.set("ROLE_LIST", loginUser.data.roleList);
				this.$TOOL.data.set("Organization", loginUser.data.orgList);
				this.$TOOL.data.set("BranchList", loginUser.data.branchList);
				// if (loginUser?.data?.orgList.length) {
				//   let res = await this.$API.system.menuTreeByOrg({ orgId: loginUser?.data?.orgList[0]?.id })
				//   this.$TOOL.data.set("USER_INFO", res);
				//   this.$TOOL.data.set("Organization", loginUser.data.orgList)
				// }

				return loginUser;
			} else {
				//登录失败处理
				// this.$Response.errorNotice(loginUser, "登录失败，请重试");
				return null;
			}
		},
		/*
		 * 获取系统菜单
		 * @author: 路正宁
		 * @date: 2023-04-07 10:51:24
		 */
		async getMenuTree() {
			const orgList = this.$TOOL.data.get("Organization")
			if (orgList?.length) {
				let response = await this.$API.system.menuTreeByOrg({ orgId: orgList[0]?.id })
				var menuTree = this.sysMenuToUiTree(response.data);
				var permissions = this.getPermissions(response.data);
				//菜单数据写入本地
				this.$TOOL.data.set("MENU", menuTree);
				//权限标识写入本地
				this.$TOOL.data.set("PERMISSIONS", permissions);
				this.$TOOL.data.set("orgKey", 0);
				this.$TOOL.data.set("braKey", 0);
				return
			}
			var res = await this.$API.system.menuTree();
			if (res.code != 200) {
				this.$Response.errorNotice(res, "当前用户无任何菜单权限，请联系系统管理员");
				return false;
			}
			var menuTrees = this.sysMenuToUiTree(res.data);
			var permissionss = this.getPermissions(res.data);
			//菜单数据写入本地
			this.$TOOL.data.set("MENU", menuTrees);
			//权限标识写入本地
			this.$TOOL.data.set("PERMISSIONS", permissionss);
			return true
		},

		/*
		 * 菜单数据转树UI数据结构
		 * @author: 路正宁
		 * @date: 2023-04-07 11:20:31
		 */
		sysMenuToUiTree(sysMenu) {
			var menuTree = [];
			for (var i = 0; i < sysMenu.length; i++) {
				menuTree[i] = {
					name: sysMenu[i].alias,
					path: sysMenu[i].path,
					meta: {
						title: sysMenu[i].name,
						icon: sysMenu[i].logo,
						type: sysMenu[i].type,
						hidden: sysMenu[i].hide,
						color: sysMenu[i].affix,
						fullpage: sysMenu[i].wholePageRoute,
					},
					component: sysMenu[i].view,
				};
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					menuTree[i].children = this.sysMenuToUiTree(sysMenu[i].children);
				}
			}
			return menuTree;
		},
		/*
		 *提取权限标识
		 * @author: 路正宁
		 * @date: 2023-04-07 15:21:52
		 */
		getPermissions(sysMenu) {
			var permissions = [];
			for (var i = 0; i < sysMenu.length; i++) {
				if (this.$ObjectUtils.isNotEmpty(sysMenu[i].permission)) {
					permissions.push(sysMenu[i].permission);
				}
				if (this.$ObjectUtils.isEmpty(sysMenu[i].children) == false) {
					var pe = this.getPermissions(sysMenu[i].children);
					for (var j = 0; j < pe.length; j++) {
						if (this.$ObjectUtils.isNotEmpty(pe[j])) {
							permissions.push(pe[j]);
						}
					}
				}
			}
			return permissions;
		},
		handlerChange() {
			this.handlerReset()
		}
	},
};
</script>

<style scoped lang="scss">
.regist {
	color: #2878ff;
}

.forget {
	color: #2878ff;
	cursor: pointer;
	margin-left: auto;
}

.el-form--large.el-form--label-top .el-form-item .el-form-item__label {
	width: 120px;
	height: 16px;
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #666666;
	margin-bottom: 15px;
	line-height: 22px;
}
.el-form-item__label{
	padding-right: 0;
}

.el-form-item__label2 {
	margin-top: 65px;
}

.login {
	margin-top: 10px;
	width: 417px;
	height: 47px;
	background: linear-gradient(-5deg, #6394FF, #2878FF);
	border-radius: 4px;
}

.input {
	width: 418px;
	height: 48px;
	background: #FFFFFF;
	border: 1px solid #D5D5D5;
	border-radius: 4px;
}

.input2 {
	width: 418px;
	height: 48px;
	/* margin-top: 20px; */
	background: #FFFFFF;
	border: 1px solid #D5D5D5;
	border-radius: 4px;
}

.login-reg {
	margin-top: -10px;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	width: 420px;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
}
::v-deep input[type='number'] {
	-moz-appearance: textfield !important;
}
::v-deep input[type=number].el-input__inner{
	line-height: 1px !important;
}
.login-code {
	width: 28%;
	height: 40px;
	float: right;
	border: 1px solid #D5D5D5;
	padding: 2px;
	margin-left: 2px;
	display: flex;
	flex-direction: column;
	align-items: center;
	img.login-code-img {
		cursor: pointer;
		vertical-align: middle;
		height: 100%;
		width: 100%;
	}
}
</style>
