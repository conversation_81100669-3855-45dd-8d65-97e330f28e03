import request from '@/utils/request';

export default {
    // 列表
    getList: function (params) {
        return request.get('/tms/advancepayment/apply/receipt/month/page', params);
    },
    // 导出
    export: function (params, config, resDetail, responseType) {
        return request.get('/tms/advancepayment/apply/receipt/month/export', params, config, resDetail, responseType);
    },
    // 收款总金额统计
    getSum: function (params) {
        return request.get('/tms/advancepayment/apply/receipt/month/stats', params);
    },
    // 根据付款单申请id查询
    getDetail: function (applyId) {
        return request.get('/cost/payment/getByPaymentApplyId/' + applyId);
    }
};
