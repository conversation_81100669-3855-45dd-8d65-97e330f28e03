<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-18 10:06:52
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-28 09:30:28
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\views\assist\purchasingManagement\component\fineSingle.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="app-container">
            <el-table :data="list" border >
                <el-table-column label="自编码" prop="commodity.commoditySelfCode" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                    <!-- // TODO -->
                <el-table-column label="入库单编号" prop="commodityType" :show-overflow-tooltip="true" align="center"
                    :formatter="(row) => row.nodeName" min-width="120" />
                <el-table-column label="入库日期" prop="intoTime" :show-overflow-tooltip="true" align="center"
                    min-width="120" :formatter="(row)=>row.intoTime ? moment(row.intoTime).format('YYYY-MM-DD') : '--'"/>
                     <!-- // TODO -->
                <el-table-column label="入库数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="批号" prop="intoNo" :show-overflow-tooltip="true" align="center"
                    min-width="140" />
                     <!-- // TODO -->
                <el-table-column label="单据状态" prop="nodeName" align="center" min-width="120" />
            </el-table>   
    </div>
</template>
 
<script setup >
import { reactive, ref, getCurrentInstance, toRefs,watch,defineProps } from 'vue'
import moment from 'moment'
const { proxy } = getCurrentInstance();
const props = defineProps({
    list: {
        type: Array,
        default: ()=>{[]}
    },
})

const { list } = toRefs(props);
watch(() => list.value, (newValue, oldValue) => {
   console.log(list.value);
},{deep:true})
</script>
 <style lang="scss" scoped>
    .box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    ::v-deep .el-form-item {
        width: 22%;
    }
}
::v-deep .labelStyle {
     .el-form-item__label {
        margin-left: 10px;
    }
}
 </style>