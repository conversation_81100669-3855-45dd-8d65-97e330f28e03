<template>
<div class="app-container">
<!--	&lt;!&ndash;搜索&ndash;&gt;-->
<!--	<el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">-->
<!--		<el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>-->
<!--			<el-form-item label="用户" prop="companyId" style="width: 240px">-->
<!--				<el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">-->
<!--					<el-option v-for="(dict, index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />-->
<!--				</el-select>-->
<!--			</el-form-item>-->
<!--			<search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />-->
<!--		</el-form>-->
<!--	</el-card>-->
	<!-- 表格 -->
	<el-card :body-style="{ padding: '10px' }" shadow="never">
		<div class="mb10" style="display: flex; justify-content: space-between">
			<el-button  v-hasPermi="['tms:three:driverConfig:add']" icon="el-icon-plus" size="mini" type="primary" @click="handleAdd(0, 0)">新增 </el-button>
			<right-toolbar v-model:showSearch="showSearch" v-model:columns="columns" :search="false" table-i-d="TripartiteDriverArrangement" @queryTable="getList"></right-toolbar>
		</div>
		<column-table key="TripartiteDriverArrangement" ref="ColumnTable" v-loading="loading" :columns="columns" :data="dataList" :defaultSort="{ prop: 'createDate', order: 'descending' }">
			<template #user="{row}">
				<span>{{  row?.user?.name||'' }}</span>
			</template>
			<template #carrier="{row}">
				<span>{{  row?.carrier?.name||'' }}</span>
			</template>
			<template #createBy="{row}">
				<span>{{  row?.createBy?.name||'' }}</span>
			</template>
			<template #menuId="{ row }">
				<span>{{ MenuFormatting(driverMenuList, row.menuId) }}</span>
			</template>
			<template #status="{ row }">
				<span>{{ dictionaryFormatting(statusOptions, row.status) }}</span>
			</template>
			<template #opt="scope">
				<el-button v-hasPermi="['tms:three:driverConfig:view','tms:three:driverConfig:edit']" icon="el-icon-edit" link plain size="mini" type="success" @click="handleUpdate(scope.row)">编辑 </el-button>
				<el-button v-hasPermi="['tms:three:driverConfig:delete']" icon="el-icon-delete" link plain size="mini" type="danger" @click="handleDelete(scope.row)">删除 </el-button>
			</template>
		</column-table>
	</el-card>
	<!-- 添加或编辑模板配置 -->
	<el-dialog v-model="open" :title="title" append-to-body width="500px">
		<el-form ref="form" :model="form" :rules="rules" label-width="150px">
			<el-form-item label="三方司机" prop="userId" >
				<el-select v-model="form.userId" clearable style="width: 100%;" filterable placeholder="请选择三方司机">
					<el-option v-for="(dict, index) in userList" :key="index" :label="dict.name" :value="dict.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="菜单" prop="menuId">
				<el-select v-model="form.menuId" clearable style="width: 100%;" filterable placeholder="请选择菜单">
					<el-option v-for="(dict, index) in driverMenuList" :key="index" :label="dict.name" :value="dict.code"/>
				</el-select>
			</el-form-item>
			<el-form-item label="司机状态">
				<el-radio-group v-model="form.status">
					<el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.name }}</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="cancel">取 消</el-button>
			<el-button type="primary" @click="submitForm">确 定</el-button>
		</template>
	</el-dialog>
</div>


</template>

<script>
import RightToolbar from '@/components/RightToolbar';
import SearchButton from '@/components/searchModule/SearchButton';
import ColumnTable from '@/components/ColumnTable';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js';
import {handleTree} from "@/utils";
export default {
	name: "TripartiteDriverArrangement",
	components: { SearchButton, RightToolbar, ColumnTable },
	data() {
		return {
			// 遮罩层
			loading: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			driverMenuList: [], // 司机端菜单
			statusOptions:[], // 状态
			userList: [], // 用户列表
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 查询参数
			queryParams: {
				current: 1,
				size: 10,
			},
			// 总条数
			total: 0,
			dataList: [],
			columns: [
				{ title: '用户', key: 'user', align: 'center', minWidth: '120px', columnShow: true },
				{ title: '承运商', key: 'carrier', align: 'center', minWidth: '180px',columnShow: true, showOverflowTooltip: true },
				{ title: '菜单', key: 'menuId', align: 'center', minWidth: '120px', columnShow: true },
				{ title: '状态', key: 'status', align: 'center', minWidth: '180px', columnShow: true },
				{ title: '创建人', key: 'createBy', align: 'center', minWidth: '180px', columnShow: true },
				{ title: '创建时间', key: 'createDate', align: 'center', minWidth: '180px', columnShow: true },
				{ title: '操作', key: 'opt', align: 'center', fixed: 'right', width: '200px', hideFilter: true, columnShow: true }
			],
			// 表单参数
			form: {
				userId: null,
				menuId: null,
				status:'0',
				user:{
					id: null,
				}
			},
			// 表单校验
			rules: {
				userId: [{ required: true, message: '请选择三方司机', trigger: 'blur' }],
				menuId: [{ required: true, message: '请选择菜单', trigger: 'blur' }]
			}
		};
	},
	computed: {
		// 字典翻译
		dictionaryFormatting() {
			return (data, value) => {
				return this.selectDictLabel(data, value);
			};
		},
		// 菜单翻译
		MenuFormatting() {
			return (datas, value) => {
				var actions = [];
				Object.keys(datas).some((key) => {
					if (datas[key].code == ('' + value)) {
						actions.push(datas[key].name);
						return true;
					}
				})
				return actions.join('');
			};
		},
	},
	async created() {
		this.getUserList();
		this.getList();
		this.driverMenuList = await this.getDictList('fourpl_driver_menu');
		// 状态
		this.statusOptions = await this.getDictList('sys_normal_disable');
	},
	methods: {
		/**
		 * 获取用户列表
		 */
		getUserList(){
			this.userList = [];
			operationConfiguration.listUser({current: 1,size: 100000,}).then((response) => {
				if (response.code == 200) {
					this.userList = response?.data?.records || [];
				}
			});
		},
		/** 查询三方司机配置列表 */
		getList() {
			this.loading = true;
			this.dataList = [];
			operationConfiguration.listDriverConfig(this.queryParams).then((response) => {
				if (response.code == 200) {
					this.dataList = response?.data?.records || [];
					this.total = response.data.total || 0;
				}
				this.loading = false;
			}).catch(e=>{
				this.loading = false;
			});
		},
		// 取消按钮
		cancel() {
			this.open = false;
			this.reset();
		},
		// 表单重置
		reset() {
			this.form = {
				userId: null,
				menuId: null,
				status:'0',
				user:{
					id: null,
				}
			};
			this.$refs['form'] ? this.$refs['form'].resetFields() : '';
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.getList();
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.$refs['queryForm'].resetFields();
			this.handleQuery();
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset();
			this.open = true;
			this.title = '添加三方司机配置';
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset();
			const id = row.id || this.ids;
			operationConfiguration.queryDriverConfigById({ id }).then((response) => {
				this.form = {...response.data,userId: response.data?.user?.id};
				this.open = true;
				this.title = '修改三方司机配置';
			});
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					const {userId, ...param} = this.form;
					param.user.id = userId;
					operationConfiguration.saveDriverConfig(param).then((response) => {
						if (response.code == 200) {
							this.msgSuccess('保存成功');
							this.open = false;
							this.getList();
						}
					});
				}
			});
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const ids = row.id ;
			const name = row?.user?.name;
			this.$confirm('是否确认删除三方司机"' + name + '"的配置数据项?', '警告', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(function () {
					return operationConfiguration.delDriverConfig({ ids: ids });
				})
				.then(() => {
					this.getList();
					this.msgSuccess('删除成功');
				})
				.catch(() => {});
		}
	}
}
</script>

<style scoped>

</style>
