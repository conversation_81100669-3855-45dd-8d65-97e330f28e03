import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/sys/sysQualificationDocumentSet/list', params)
  },
  delete: function (ids) {
    return http.delete('/sys/sysQualificationDocumentSet/delete', ids)
  },
  save: function (params) {
    return http.post('/sys/sysQualificationDocumentSet/save', params)
  },
  details: function (id) {
    return http.get('/sys/sysQualificationDocumentSet/queryById', id)
  },
  // clientType: function (params) {
  //   return http.get("/erp/customer/erpCustomers/list", params);
  // },
  // getUser: function (id) {
  //   return http.get('/sys/user/list', id)
  // },
}