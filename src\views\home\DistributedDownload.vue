<template>
    <div class="bg-main">
        <div class="btn-down">
            <el-button style="font-size: 0.5rem; height: 1.2rem; padding: 0.5rem 0.7rem" type="primary" @click="getUrl">安卓手机下载</el-button>
        </div>

        <div v-if="show" class="jump-to-default-browser">
            <el-image :src="require('../../assets/images/ysjjt.png')" class="img-jt" fit="contain"></el-image>
            <el-image :src="require('../../assets/images/qdjysjxz.png')" class="img-wz" fit="contain"></el-image>
        </div>
    </div>
</template>

<script>

export default {
    name: 'DistributedDownload',
    data() {
        return {
            show: false
        };
    },
    methods: {
        /**
         * 获取下载地址
         */
        getUrl() {
            // 判断是否是微信
            const ua = navigator.userAgent.toLowerCase();
            if (ua.match(/MicroMessenger/i) == 'micromessenger') {
                this.show = true;
            } else {
                this.show = false;
                // 调用API获取应用下载地址
				this.$API.auth.getAppDownloadUrl('__UNI__248CFF0')
                    .then((res) => {
                        // 如果返回值code为0且data存在
                        if (res.code === 200) {
                            const url = res.data;
                            // 跳转到下载地址
                            if (url) {
                                window.location.href = url;
                            } else {
                                // 显示错误消息
                                this.$message({
                                    message: '下载地址获取失败',
                                    type: 'error'
                                });
                            }
                        }
                    })
                    .catch(() => {});
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.bg-main {
    background-image: url('../../assets/images/bg-wxsm.webp');
    background-repeat: no-repeat;
    background-size: 100%;
    height: 100%;
}

.jump-to-default-browser {
    height: 100vh;
    background-color: #000000b8;
    position: absolute;
    width: 100%;
    top: 0;
    display: flex;
    flex-direction: column;
    padding-top: 80px;
    align-items: center;

    .img-jt {
        width: 140px;
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .img-wz {
        width: 180px;
        position: absolute;
        top: 180px;
        right: 90px;
    }
}

.btn-down {
    position: fixed;
    bottom: 20%;
    left: 50%;
    transform: translate(-50%, -50%);
}
</style>
