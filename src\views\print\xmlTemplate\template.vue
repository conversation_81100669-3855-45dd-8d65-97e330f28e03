<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto">
                <el-form-item label="报表名称" prop="fName">
                    <el-input v-model="queryParams.fname" clearable placeholder="请输入报表名称" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <el-button icon="el-icon-setting" size="mini" type="primary" @click="handleDesigner">报表设计</el-button>
                <right-toolbar v-model:showSearch="showSearch" table-i-d="xmlTemplate" @queryTable="getList"></right-toolbar>
            </div>
            <el-table v-loading="loading" :data="xmlTemplateList" row-key="fid">
                <el-table-column align="center" label="报表名称" prop="fname" width="300" />
                <el-table-column align="center" label="报表类型" width="80">
                    <template #default="scope">
                        <span>{{ scope.row.fisGroup == '1' ? '分组' : '模板' }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="报表编号" min-width="400" show-overflow-tooltip>
                    <template #default="scope">
                        <span v-if="scope.row.fisGroup == '0'">
                            {{ scope.row.fid }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column :formatter="fTypeFormatter" align="center" label="报表类型" prop="ftype" width="100" />
                <el-table-column align="center" label="操作" width="200" show-overflow-tooltip fixed="right">
                    <template #default="scope">
                        <el-button v-if="scope.row.fisGroup == '0'" icon="el-icon-setting" link size="small" type="primary" @click="handleTest(scope.row)">测试</el-button>
                        <el-button v-if="scope.row.fisGroup == '0'" icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(scope.row)">修改</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!--修改打印模板对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="650px">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="模板名称" prop="fName">
                    <el-input v-model="form.fname" placeholder="请输入模板名称" />
                </el-form-item>
                <el-form-item label="模板类型" prop="ftype">
                    <el-select v-model="form.ftype">
                        <el-option v-for="item in fTypeDict" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="执行SQL" prop="sql">
                    <el-input v-model="form.fsql" :rows="10" placeholder="请输入SQL模板" type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div style="text-align: right">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import xmltemplate from '@/api/print/xmltemplate';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { handleTree } from '@/utils/index.js';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'Xmtemplate',
    components: {
        SearchButton,
        RightToolbar,
        Treeselect
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // XM报表管理表格数据
            xmlTemplateList: [],
            // 冷藏箱状态字典
            fTypeDict: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                fId: null,
                fName: null,
                current: 1,
                size: 1000
            },
            //报表中心地址
            ReportCenterAddress: 'http://*************:8009',
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                fname: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }]
            }
        };
    },
    created() {
        this.getList();
        this.getConfigKey('report.center.address').then((response) => {
            this.ReportCenterAddress = response.msg;
        });
        this.getDicts('print_xmreport_type').then((response) => {
            this.fTypeDict = response.data;
        });
    },
    methods: {
        /** 查询XM报表管理列表 */
        getList() {
            this.loading = true;
            xmltemplate.listXmltemplate(this.queryParams).then((resp) => {
                this.xmlTemplateList = handleTree(resp.data.records, 'fid', 'fgroupId');
                this.loading = false;
            });
        },
        // 报表类型字典翻译
        fTypeFormatter(row, column, cellValue) {
            return this.selectDictLabel(this.fTypeDict, cellValue);
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                fid: null,
                fname: null,
                ftype: null
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /** 测试按钮操作 */
        handleTest(row) {
            window.open(this.ReportCenterAddress + '/printer/xmlTemplate/printer?templateId=' + row.fid, '_blank');
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            xmltemplate.getXmltemplate({ fid: row.fid }).then((resp) => {
                this.form = resp.data;
                this.open = true;
                this.title = '修改打印模板';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.fid != null) {
                        xmltemplate.updateXmltemplate(this.form).then((response) => {
                            this.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 新增按钮操作 */
        handleDesigner() {
            window.open(this.ReportCenterAddress + '/index.html', '_blank');
        }
    }
};
</script>
<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
</style>
