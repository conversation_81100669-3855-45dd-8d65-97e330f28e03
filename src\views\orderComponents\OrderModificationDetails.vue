<template>
<div v-loading="loading" class="" style="background-color: rgb(245, 247, 253); padding: 10px">
  <el-card class="mb20 box-mb5" shadow="never" style="overflow: inherit">
    <card-header title="修改记录"/>
    <el-form ref="form"  label-width="80px">
      <div class="item-box">
        <div v-if="recordList && recordList.length > 0" class="item-line">
          <el-form-item class="item-line-box" label="修改前" label-width="80px">
            <div class="item-content">
              <div v-for="(record, idx) in recordList" class="mb5">
                <el-form-item v-if="record.name" :class="record.properties == 'orderDetailList'?'item-line-box':''" :label="`${idx + 1}、${record.name}：`" class="childlabel"  label-width="200px">
                  <div v-if="record.properties == 'temperatureType'" class="lineFeed">
                    {{ getTemperatureTypeVal(record.oldValue.id) }}
                  </div>
                  <div v-else-if="record.properties == 'productProperties'" class="lineFeed">
                    {{ getDictVals(productAttributeDictionary, record.oldValue) }}
                  </div>
                  <div v-else-if="record.properties == 'productType'" class="lineFeed">
                    {{ fourplFormat(productTypeDicts, record.oldValue) }}
                  </div>
                  <div v-else-if="record.properties == 'productClass'" class="lineFeed">
                    {{ fourplFormat(fourplProductClassDicts, record.oldValue) }}
                  </div>
                  <div v-else-if="record.properties == 'paymentMethod'" class="lineFeed">
                    {{ fourplFormat(fourplPaymentMethodOptions, record.oldValue) }}
                  </div>
                  <div v-else-if="record.properties == 'addServiceList'" class="lineFeed">
                    {{ fourplAddedServices(record.oldValue) }}
                  </div>
                  <div v-else-if="record.properties == 'orderType'" class="lineFeed">
                    {{ fourplFormat(orderTypeList, record.oldValue) }}
                  </div>
					<div v-else-if="record.properties == 'carrierWay'" class="lineFeed">
						{{ fourplFormat(carrierWayDicts, record.oldValue) }}
					</div>
                  <div v-else-if="record.properties == 'carType'" class="lineFeed">
                    {{ getTreeVal( record.oldValue) }}
                  </div>
                  <div v-else-if="record.properties == 'orderDetailList'" style="width:100%;">
                    <el-table :data="stringToObject(record.oldValue)" :fit="true" class="coldChainTable" >
                      <el-table-column align="center" label="序号" prop="index" width="50">
                        <template #default="scope">{{ scope.$index + 1 }}</template>
                      </el-table-column>
						<el-table-column :show-overflow-tooltip="true" align="center" label="通用名称" prop="name" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="规格/型号" prop="specifications" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" prop="manufacturer" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="单位" prop="basicUnit" />
						<el-table-column align="center" label="数量" prop="quantity" width="160"/>
						<el-table-column :show-overflow-tooltip="true" align="center" label="批号/序列号" min-width="100" prop="batchNumber" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="使用期限/失效日期" min-width="160" prop="validityDate" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="批准文号/注册证号/备案证号" min-width="200" prop="registrationNumber" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="上市许可持有人/注册人/备案人" min-width="200" prop="listPermitHolder" />
                      <el-table-column align="center" label="资料是否齐全" prop="completeInformation">
                        <template #default="scope">
                          {{ scope.row.completeInformation == '1' ? '是' : '否' }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <div v-else class="lineFeed">{{ record.oldValueDesc ? record.oldValueDesc : record.oldValue != 'null' ? record.oldValue : '' }}</div>
                </el-form-item>
              </div>
            </div>
          </el-form-item>
        </div>
        <el-divider direction="vertical"></el-divider>
        <div v-if="recordList && recordList.length > 0" class="item-line">
          <el-form-item class="item-line-box" label="修改后" label-width="80px">
            <div class="item-content">
              <div v-for="(record, idx) in recordList" class="mb5">
                <el-form-item v-if="record.name" :class="record.properties == 'orderDetailList'?'item-line-box':''" :label="`${idx + 1}、${record.name}：`" class="childlabel updated" label-width="200px">
                  <div v-if="record.properties == 'temperatureType' " class="lineFeed">
                    {{ getTemperatureTypeVal(record.value.id) }}
                  </div>
                  <div v-else-if="record.properties == 'productProperties'" class="lineFeed">
                    {{ getDictVals(productAttributeDictionary, record.value) }}
                  </div>
                  <div v-else-if="record.properties == 'productType'" class="lineFeed">
                    {{ fourplFormat(productTypeDicts, record.value) }}
                  </div>
                  <div v-else-if="record.properties == 'productClass'" class="lineFeed">
                    {{ fourplFormat(fourplProductClassDicts, record.value) }}
                  </div>
                  <div v-else-if="record.properties == 'paymentMethod'" class="lineFeed">
                    {{ fourplFormat(fourplPaymentMethodOptions, record.value) }}
                  </div>
                  <div v-else-if="record.properties == 'addServiceList'" class="lineFeed">
                    {{ fourplAddedServices(record.value) }}
                  </div>
                  <div v-else-if="record.properties == 'orderType'" class="lineFeed">
                    {{ fourplFormat(orderTypeList, record.value) }}
                  </div>
					<div v-else-if="record.properties == 'carrierWay'" class="lineFeed">
						{{ fourplFormat(carrierWayDicts, record.value) }}
					</div>
                  <div v-else-if="record.properties == 'carType'" class="lineFeed">
                    {{ getTreeVal(record.value) }}
                  </div>
                  <div v-else-if="record.properties == 'orderDetailList'" style="width:100%;">
                    <el-table :data="stringToObject(record.value)" :fit="true" class="coldChainTable">
                      <el-table-column align="center" label="序号" prop="index" width="50">
                        <template #default="scope">{{ scope.$index + 1 }}</template>
                      </el-table-column>
						<el-table-column :show-overflow-tooltip="true" align="center" label="通用名称" prop="name" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="规格/型号" prop="specifications" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="生产厂家" prop="manufacturer" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="单位" prop="basicUnit" />
						<el-table-column align="center" label="数量" prop="quantity" width="160"/>
						<el-table-column :show-overflow-tooltip="true" align="center" label="批号/序列号" min-width="100" prop="batchNumber" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="使用期限/失效日期" min-width="160" prop="validityDate" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="批准文号/注册证号/备案证号" min-width="200" prop="registrationNumber" />
						<el-table-column :show-overflow-tooltip="true" align="center" label="上市许可持有人/注册人/备案人" min-width="200" prop="listPermitHolder" />
                      <el-table-column align="center" label="资料是否齐全" prop="completeInformation">
                        <template #default="scope">
                          {{ scope.row.completeInformation == '1' ? '是' : '否' }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <div v-else class="lineFeed">{{ record.valueDesc ? record.valueDesc : record.value != 'null' ? record.value : '' }}</div>
                </el-form-item>
              </div>
            </div>
          </el-form-item>

        </div>
      </div>
    </el-form>
  </el-card>
</div>


</template>

<script>
import CardHeader from '@/components/CardHeader';
import orderManagement from '@/api/logisticsManagement/orderManagement.js';
import operationConfiguration from "@/api/logisticsConfiguration/operationConfiguration";
import otherConfiguration from "@/api/logisticsConfiguration/otherConfiguration"; // 订单管理
export default {
  name: "OrderModificationDetails",
  props: {
    transRecordId: {
      required: true,
      type: String
    }
  },
  components: {
    CardHeader,
  },
  data() {
    return {
      recordList: [], // 异动记录数据
      productTypeDicts: [], // 产品类型
      productAttributeDictionary: [], // 商品属性
      fourplPaymentMethodOptions: [], // 付款方式
      fourplOrderChangeTypeOptions: [], // 异动类型
      temperatureTypeDicts: [], // 温层类型
      typeOptions: [], //车辆类型输
      addedServiceDicts: [], // 增值服务
      loading: false,
      orderTypeList: [],
      fourplProductClassDicts:[],
		carrierWayDicts: [] // 运输方式 字典值
    };
  },
  async created() {
    this.loading = true;
    /** 产品分类 */
    this.fourplProductClassDicts = await this.getDictList('fourpl_product_class');
    // 运输类型4PL
    this.productTypeDicts = await this.getDictList('fourpl_product_type');
    // 货品属性
    this.productAttributeDictionary = await this.getDictList('fourpl_product_attributes');
    /** 付款方式 */
    let  fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
    this.fourplPaymentMethodOptions = fourplPaymentMethodOptions.filter(item=>item.value !='4' && item.value!='5' && item.value!='6');
    /** 揽收方式 */
    this.orderTypeList = await this.getDictList('fourpl_mail_service');
    /** 异动类型4PL */
    this.fourplOrderChangeTypeOptions = await this.getDictList('fourpl_order_change_type');
	  /** 运输方式 */
	  this.carrierWayDicts = await this.getDictList('fourpl_transportation_mode');
    this.getTemperatureType();
    this.getAllAvailableAddService();
    this.getTreeSelect();

    orderManagement.orderModificationDetails({transRecordId:this.transRecordId})
        .then((res) => {
          if (res.code === 200 && res.data) {
            this.recordList = res.data.map(item=>{
            	if(item.properties == 'orderDetailList' && typeof item.oldValue != 'string'){
					item.oldValue = JSON.stringify(item.oldValue);
					item.value = JSON.stringify(item.value);
				}
            	return {...item}
			});
            setTimeout(() => {
              this.loading = false;
            }, 500);
          }
        })
        .catch((e) => {
          this.loading = false;
        });

  },
  methods: {
    // 查询车辆类型下拉树结构
    getTreeSelect() {
      operationConfiguration.listCarType({ status: '0',size:1000}).then((response) => {
        this.typeOptions = [];
        if (response.code == 200 && response?.data?.records.length > 0) {
          this.typeOptions  = response?.data?.records;
        }
      });
    },
    // 车辆类型翻译
    getTreeVal(val) {
      let index = this.typeOptions.findIndex((t) => t.typeCode == val);
      if (index > -1) {
        return this.typeOptions[index].typeName;
      }
      return '';
    },
    // 增值服务格式化
    fourplAddedServices(vals){
      if(typeof vals == 'string' ){
        vals = JSON.parse(vals);
      }
      let arr = [];
      if(Array.isArray(vals)){
        if(vals.length > 0){
          vals.forEach(item=>{
            let text = '';
            let index = this.addedServiceDicts.findIndex((a) => a.id == item.id);
            if (index > -1 ) {
              text += this.addedServiceDicts[index].name + ':' + item.inputValue + this.addedServiceDicts[index].unit
            }
            arr.push(text);
          })
        }
      }
      return arr.join(',')
    },
    /**
     * 增值服务
     */
    getAllAvailableAddService() {
      orderManagement.getOrderAddedService().then((response) => {
        this.addedServiceDicts = [];
        this.addedServiceDicts = response.data;
      });
    },
    // 温层类型翻译
    getTemperatureTypeVal(val) {
      let index = this.temperatureTypeDicts.findIndex((t) => t.id == parseInt(val));
      if (index > -1) {
        return this.temperatureTypeDicts[index].describtion;
      }
      return '';
    },
    // 字符串转对象
    stringToObject(data) {
      return JSON.parse(data);
    },
    // 多选字典翻译
    getDictVals(data, vals, sep = ',') {
      var actions = [];
      if (vals.indexOf(sep) > -1) {
        let arr = vals.split(sep);
        arr.forEach((item) => {
          let index = data.findIndex((t) => t.value == '' + item);
          if (index > -1) {
            actions.push(data[index].name);
          }
        });
      } else {
        let index = data.findIndex((t) => t.value == '' + vals);
        if (index > -1) {
          actions.push(data[index].name);
        }
      }
      return actions.join(',');
    },
    /** 4PL字典转换 */
    fourplFormat(data, val) {
      return this.selectDictLabel(data, val);
    },
    // 关闭弹窗
    cancel() {
      this.$emit('callbackMethod');
    },
    // 标准时间格式化
    formatDate(cellValue) {
      return moment(cellValue).format('YYYY-MM-DD HH:mm:ss');
    },
    // 获取温层类型
    getTemperatureType() {
      otherConfiguration.getTemperatureTypeList().then((res) => {
        if (res.code === 200 && res?.data?.records.length > 0) {
          this.temperatureTypeDicts = res.data.records;
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.item-box{
	display: flex;
	.item-line{
		width: calc((100% - 10px)/2);
	}
}
::v-deep{
  .el-divider--vertical{
    height: auto;
  }
  .item-line-box{
    flex-direction: column;
  }
}
::v-deep .el-timeline-item__node--normal {
  border: 2px solid #1989fa;
  background-color: #fff;
}

::v-deep .el-form-item {
  margin: 0;
  text-align: left;

  .el-form-item__label {
    color: #999;
  }

  .el-form-item__content {
    font-size: 12px;
    color: #333;
    padding-left: 20px;
	  width: 100%;
  }
	.item-content{
		width: 100%;
	}
}

.img-list {
  display: flex;
  flex-wrap: wrap;
  .img {
    width: 128px;
    height: 128px;
    border: 1px dashed #d9d9d9;
    margin-top: 0;
    margin-left: 10px;
    img {
      width: 108px;
      height: 108px;
      margin: 10px;
    }
  }
}
.childlabel {
  ::v-deep .el-form-item__label {
    color: #333;
    text-align: left;
    align-items:start ;
  }

  ::v-deep .el-form-item__label-wrap {
    margin-left: 0;
  }

  ::v-deep .el-form-item__content {
    display: flex;
    align-items: center;
    padding-left: 0;
    width: auto;
  }
}

.updated {
  ::v-deep .el-form-item__content {
    color: red;
  }
}

.lineFeed{
  word-break: break-all;
}
</style>
