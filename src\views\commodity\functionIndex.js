export const functionIndex = {
	//时间转换
	transformTimestamp: (timestamp) => {
		let a = new Date(timestamp).getTime();
		const date = new Date(a);
		if (date == 'Invalid Date') return '--'
		const Y = date.getFullYear() + "-";
		const M =
			(date.getMonth() + 1 < 10
				? "0" + (date.getMonth() + 1)
				: date.getMonth() + 1) + "-";
		const D =
			(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + "  ";
		const h =
			(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
		const m =
			date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
		// const s = date.getSeconds(); // 秒
		const dateString = Y + M + D;
		// console.log('dateString', dateString); // > dateString 2021-07-06 14:23
		return dateString;
	},
	//时间转换
	transformTimestampSearch: (timestamp) => {
		let a = new Date(timestamp).getTime();
		const date = new Date(a);
		if (date == 'Invalid Date') return '--'
		const Y = date.getFullYear() + "-";
		const M =
			(date.getMonth() + 1 < 10
				? "0" + (date.getMonth() + 1)
				: date.getMonth() + 1) + "-";
		const D =
			(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
		const h =
			(date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
		const m =
			(date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) + ':';
		const s =
			date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
		// const s = date.getSeconds(); // 秒
		const dateString = Y + M + D + h + m + s;
		// console.log('dateString', dateString); // > dateString 2021-07-06 14:23
		return dateString;
	},
	tableRowStyle: ({row}) => {
		if (row.status == "2") {
			return {
				color: '#409eff'
			}
		} else if (row.status == "3") {
			return {
				color: '#67c23a'
			}
		} else if (row.status == "4") {
			return {
				color: '#ff4800'
			}
		} else if (row.status == "6") {
			return {
				color: '#e6a23c'
			}
		} else if (row.status == '7') {
			return {
				color: '#ff4800'
			}
		} else if (row.status == "0") {
			return {
				color: '#f56c6c'
			}
		}
	},
	echo: (row) => {
		let n = "";
		if (row.status == "1") {
			n = `状态: 审核通过`;
		} else if (row.status == "2") {
			n = `状态: 待审核`;
		} else if (row.status == "3") {
			n = `状态: 审核中`;
		} else if (row.status == "4") {
			n = `状态: 驳回`;
		} else if (row.status == "5") {
			n = `状态: 撤销`;
		} else if (row.status == "6") {
			n = `状态: 草稿`;
		} else if (row.status == "7") {
			n = `状态: 驳回`;
		} else {
			n = `状态: 无效`;
		}
		return n;
	}
}
