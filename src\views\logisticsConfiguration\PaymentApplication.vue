<template>
    <div v-loading="loading" class="app-container" element-loading-text="加载中...">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="收款公司" prop="receiveCompany">
                    <el-select v-model="queryForm.receiveCompany" clearable filterable placeholder="请选择收款公司" @change="handleQuery">
                        <el-option v-for="item in companyList" :key="item.id" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="时间" prop="queryDate">
                    <el-date-picker v-model="queryForm.queryDate" clearable end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"> </el-date-picker>
                </el-form-item>
                <el-form-item label="审批状态" prop="status">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择审批状态" @change="handleQuery">
                        <el-option v-for="item in statusList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb-40">
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="PaymentApplicationList" @queryTable="getList"></right-toolbar>
            </div>
            <column-table ref="PaymentApplicationList" :columns="columns" :data="dataList" :max-height="600" show-summary>
                <template #invoiceApply="{ row }">
                    <span v-if="row.businessType === '2'">-</span>
                    <span v-else :style="setInvoiceApplyColor(row.invoiceApply)">{{ row.invoiceApply == '1' ? '已发起' : '未发起' }}</span>
                </template>
                <template #receiveCompany="{ row }">
                    {{ formatDictionaryData('companyList', row.receiveCompany) }}
                </template>
                <template #isInvoice="{ row }">
                    <div v-if="row.businessType === '2'">
                        <span v-if="row.isInvoice === '1'">是</span>
                        <span v-if="row.isInvoice === '0'">否</span>
                        <el-button v-if="row.isInvoice === '0' || row?.invoiceResult == '0'" link size="small" type="primary" @click="onOpenInvoice(row)">开票</el-button>
                    </div>
                    <span v-else>--</span>
                </template>
                <template #businessType="{ row }">
                    <span :style="setBusinessTypeColor(row.businessType)">{{ formatDictionaryData('businessTypeList', row.businessType) }}</span>
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ formatDictionaryData('statusList', row.status) }}</span>
                </template>
                <template #opt="{ row }">
                    <div>
                        <!-- 1-预存款充值 2-付款单支付 -->
                        <el-button v-if="(row.businessType === '2' || row.businessType === '3') && row.status === '2'" icon="el-icon-edit" link size="small" type="warning" @click="onOpenPaymentApproval('modify', row)">修改</el-button>
                        <el-button v-if="row.businessType === '1' && row.status === '2'" icon="el-icon-edit" link size="small" type="warning" @click="onOpenPrepaymentRechargeModification('modify', row)">修改</el-button>
                        <el-button v-if="row.status === '2'" icon="el-icon-delete" link size="small" type="danger" @click="onDelete(row)">撤销</el-button>
                        <el-button v-if="(row.businessType === '2' || row.businessType === '3') && row.status !== '2'" icon="el-icon-info-filled" link size="small" type="primary" @click="onOpenPaymentApproval('detail', row)">查看详情</el-button>
                        <el-button v-if="row.businessType === '1' && row.status !== '2'" icon="el-icon-info-filled" link size="small" type="primary" @click="onOpenPrepaymentRechargeModification('detail', row)">查看详情</el-button>
                        <el-button v-if="row.businessType === '1' && row.status === '1' && !row.invoiceApplyId && row.invoiceApply == '0'" icon="el-icon-edit" link size="small" type="warning" @click="onOpenInvoice(row)">开票申请</el-button>
                    </div>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- / 付款单审批、修改	-->
        <payment-order-approval
            v-if="paymentOrderApprovalVisible"
            v-model:payment-order-approval-visible="paymentOrderApprovalVisible"
            :payment-order-approval-id="paymentOrderApprovalId"
            :payment-order-approval-status="paymentOrderApprovalStatus"
            :payment-order-approval-title="paymentOrderApprovalTitle"
            :query-type="queryForm.applyWay"
            @getList="getList"
        />

        <!--  / 预付款充值审批、修改  -->
        <prepayment-recharge-modification
            v-if="prepaymentRechargeModificationVisible"
            v-model:prepayment-recharge-modification-visible="prepaymentRechargeModificationVisible"
            :prepayment-recharge-modification-id="prepaymentRechargeModificationId"
            :prepayment-recharge-modification-status="prepaymentRechargeModificationStatus"
            :prepayment-recharge-modification-title="prepaymentRechargeModificationTitle"
            @getList="getList"
        />
        <!--  / 开票  -->
        <el-drawer v-model="invoiceVisible" size="600px" title="申请开票" @close="closeInvoice">
            <div v-loading="invoiceLoading" element-loading-text="加载中..." style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-form ref="invoiceForm" :model="invoiceForm" label-width="auto">
                        <el-form-item label="开票金额" prop="invoiceAmount">
                            <el-input v-model="invoiceForm.invoiceAmount" class="number__unit__element w-full" disabled></el-input>
                        </el-form-item>
                        <el-form-item label="预付款账号" prop="advanceAccount">
                            <el-input v-model="invoiceForm.advanceAccount" class="w-full" disabled></el-input>
                        </el-form-item>
                        <el-form-item :rules="[{ required: true, message: '请选择发票抬头', trigger: 'change' }]" class="w-full" label="发票抬头" prop="invoiceData">
                            <el-select v-model="invoiceForm.invoiceData" class="w-full" clearable filterable placeholder="请选择发票抬头" value-key="id">
                                <el-option v-for="item in invoiceList" :key="item.id" :label="item.invoiceHead" :value="item">
                                    <div class="flex justify-between">
                                        <span>{{ item.invoiceHead }}</span>
                                        <span style="color: #8492a6; font-size: 12px">{{ item.taxNo }}</span>
                                    </div>
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <div v-if="invoiceForm.invoiceData">
                            <el-form-item label="税号" prop="taxNo">
                                <span>{{ invoiceForm.invoiceData.taxNo }}</span>
                            </el-form-item>
                            <el-form-item label="地址" prop="address">
                                <span>{{ invoiceForm.invoiceData.address }}</span>
                            </el-form-item>
                            <el-form-item label="电话" prop="phone">
                                <span>{{ invoiceForm.invoiceData.phone }}</span>
                            </el-form-item>
                            <el-form-item label="开户银行" prop="openBank">
                                <span>{{ invoiceForm.invoiceData.openBank }}</span>
                            </el-form-item>
                            <el-form-item label="银行账号" prop="bankAccount">
                                <span>{{ invoiceForm.invoiceData.bankAccount }}</span>
                            </el-form-item>
                            <el-form-item :rules="[{ required: true, message: '请选择签约公司名称', trigger: 'change' }]" label="签约公司名称" prop="signCompany">
                                <el-select v-model="invoiceForm.signCompany" class="w-full" clearable disabled filterable placeholder="请选择签约公司名称" @change="companyNameChange">
                                    <el-option v-for="dict in companyList" :key="dict.code" :label="dict.name" :value="dict.code" />
                                </el-select>
                            </el-form-item>
                            <el-form-item :rules="[{ required: true, message: '请选择项目名称', trigger: 'change' }]" label="项目名称" prop="projectType">
                                <el-select v-model="invoiceForm.projectType" :disabled="invoiceForm.invoiceList.length === 1" clearable filterable style="width: 130px" @change="projectIdChange">
                                    <el-option v-for="item in availableProjectList" :key="item.value" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                                <el-select v-if="invoiceForm.projectType === '运输服务'" v-model="invoiceForm.projectFeeType" :disabled="invoiceForm.invoiceList.length === 1" clearable filterable placeholder=" " style="width: 130px" @change="projectFeeTypeChange">
                                    <el-option v-for="item in availableTransportationCostsList" :key="item.label" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                                <el-select v-if="invoiceForm.projectType === '物流辅助服务'" v-model="invoiceForm.projectFeeType" :disabled="invoiceForm.invoiceList.length === 1" clearable filterable placeholder=" " style="width: 130px" @change="projectFeeTypeChange">
                                    <el-option v-for="item in availableServiceChargeList" :key="item.label" :label="item.label" :value="item.label"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :rules="[{ required: true, message: '请选择税点', trigger: 'change' }]" label="税点" prop="taxPoint">
                                <el-select v-model="invoiceForm.taxPoint" class="tax_point_unit" clearable disabled filterable placeholder=" " style="width: 80px">
                                    <el-option v-for="dict in taxPointList" :key="dict.code" :label="dict.name" :value="dict.code"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="开票备注" prop="remark">
                                <el-input v-model="invoiceForm.remark" class="w-full" clearable maxlength="150" placeholder="请输入开票备注" show-word-limit type="textarea" />
                            </el-form-item>
                        </div>
                    </el-form>
                </el-card>
            </div>
            <template #footer>
                <div class="flex justify-end">
                    <el-button @click="closeInvoice">取消</el-button>
                    <el-button type="primary" @click="submitInvoice">提交</el-button>
                </div>
            </template>
        </el-drawer>
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import paymentApplication from '@/api/shipperEnd/paymentApplication';
import { selectDictLabel } from '@/utils/dictLabel';
import PaymentOrderApproval from '@/views/carrierFunction/PaymentOrderApproval.vue';
import PrepaymentRechargeModification from '@/views/carrierFunction/PrepaymentRechargeModification.vue';
import invoiceInformationMaintenance from '@/api/shipperEnd/invoiceInformationMaintenance';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import paymentDoc from '@/api/shipperEnd/paymentDoc';
import customerPrepaymentBalance from '@/api/shipperEnd/customerPrepaymentBalance';
import { QuestionFilled } from '@element-plus/icons-vue';

export default {
    name: 'PaymentApplication',
    components: { PrepaymentRechargeModification, PaymentOrderApproval, ColumnTable, RightToolbar, SearchButton, QuestionFilled },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                taskType: '1',
                applyWay: '1', // // 货主端1 承运端2
                status: undefined,
                queryDate: []
            },
            columns: [
                { title: '汇款时间', key: 'remitTime', align: 'center', minWidth: '180px', fixed: 'left', columnShow: true },
                { title: '付款类型', key: 'businessType', align: 'center', minWidth: '120px', columnShow: true },
                { title: '收款公司', key: 'receiveCompany', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '汇款金额（元）', key: 'remitAmount', align: 'center', minWidth: '120px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '备注', key: 'remark', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '审批状态', key: 'status', align: 'center', minWidth: '100px', columnShow: true },
                { title: '是否发起开票申请', key: 'invoiceApply', align: 'center', minWidth: '150px', columnShow: true, isShowHelpIcon: true, helpText: '此状态仅用于预付款充值支付是否已发起开票申请' },
                { title: '操作', key: 'opt', align: 'center', width: '170px', fixed: 'right', columnShow: true, showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            companyList: [],
            businessTypeList: [],
            userInfo: null,
            statusList: [],
            paymentOrderApprovalVisible: false,
            paymentOrderApprovalId: undefined,
            paymentOrderApprovalTitle: '付款单审批',
            paymentOrderApprovalStatus: undefined,
            prepaymentRechargeModificationVisible: false,
            prepaymentRechargeModificationId: undefined,
            prepaymentRechargeModificationTitle: undefined,
            prepaymentRechargeModificationStatus: undefined,
            invoiceTypeList: [],
            invoiceVisible: false,
            invoiceLoading: false,
            invoiceForm: {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined,
                signCompany: undefined,
                invoiceType: undefined,
                projectFeeType: undefined,
                projectType: undefined,
                taxPoint: undefined,
                invoiceList: []
            },
            invoiceList: [],
            projectList: [
                { value: '1', label: '运输服务' },
                { value: '2', label: '物流辅助服务' }
            ],
            transportationCostsList: [
                { value: '1', label: '运输服务费' },
                { value: '2', label: '国内运输费' }
            ],
            serviceChargeList: [
                { value: '1', label: '收派服务费' },
                { value: '2', label: '仓储服务费' }
            ],
            taxPointList: []
        };
    },
    computed: {
        availableProjectList() {
            // 从invoiceList中获取可用的项目类型
            const projectTypes = [...new Set(this.invoiceForm.invoiceList.map((item) => item.projectType))];
            return this.projectList.filter((item) => projectTypes.includes(item.label));
        },
        availableServiceChargeList() {
            // 从invoiceList中获取可用的物流辅助服务费用类型
            const feeTypes = [...new Set(this.invoiceForm.invoiceList.filter((item) => item.projectType === '物流辅助服务').map((item) => item.projectFeeType))];
            return this.serviceChargeList.filter((item) => feeTypes.includes(item.label));
        },
        availableTransportationCostsList() {
            // 从invoiceList中获取可用的运输服务费用类型
            const feeTypes = [...new Set(this.invoiceForm.invoiceList.filter((item) => item.projectType === '运输服务').map((item) => item.projectFeeType))];
            return this.transportationCostsList.filter((item) => feeTypes.includes(item.label));
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置业务类型颜色
         */
        setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': { color: '#5cb85c' },
                        '3': { color: '#409EFF' }
                    }[businessType] || { color: '#999' }
                );
            };
        },
        /**
         * 设置开票申请颜色
         */
        setInvoiceApplyColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' }
                    }[status] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' },
                        '3': { color: '#999' }
                    }[status] || { color: '#999' }
                );
            };
        }
    },
    created() {
        this.getDict();
        this.userInfo = this.$TOOL.data.get('USER_INFO');
        const Organization = this.$TOOL.data.get('Organization');
        this.queryForm['company.id'] = Organization[0].id;
        this.handleQuery();
    },
    methods: {
        /**
         * 关闭开票
         */
        closeInvoice() {
            this.invoiceVisible = false;
            this.$refs.invoiceForm.resetFields();
            this.invoiceForm = {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined,
                signCompany: undefined,
                invoiceType: undefined,
                projectFeeType: undefined,
                projectType: undefined,
                taxPoint: undefined,
                invoiceList: []
            };
        },
        companyNameChange(val) {
            if (val === '1') {
                this.invoiceForm.projectFeeType = '运输服务';
                this.invoiceForm.projectType = '运输服务费';
                this.invoiceForm.taxPoint = '9';
            } else {
                this.invoiceForm.projectFeeType = '运输服务';
                this.invoiceForm.projectType = '运输服务费';
                this.invoiceForm.taxPoint = '1';
            }
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
            this.businessTypeList = await this.getDictList('payment_type');
            this.statusList = await this.getDictList('payment_approval_type');
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
            this.taxPointList = await this.getDictList('tax_points_for_cooperative_shippers');
        },
        /**
         * 获取付款单审批列表
         */
        async getInvoiceHeaderList() {
            const ownerId = this.$TOOL.data.get('Organization')[0].id;
            const res = (await invoiceInformationMaintenance.getInvoiceHeadList({ ownerId })) || {};
            if (res.code === 200) {
                this.invoiceList = res.data || [];
                if (this.invoiceList.length === 0) {
                    this.$message.warning('未查询到发票抬头信息,请先前往【发票信息维护】新增发票抬头');
                }
            } else {
                this.invoiceList = [];
            }
        },
        /**
         * 获取列表
         */
        async getList() {
            this.loading = true;
            const { ...params } = this.queryForm;
            const res = await paymentApplication.getList(params);
            if (res.code === 200) {
                this.dataList = res.data.records;
                this.total = res.data.total;
            } else {
                this.dataList = [];
                this.total = 0;
            }
            this.loading = false;
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { queryDate } = this.queryForm;
            if (queryDate && queryDate.length) {
                this.queryForm.queryStartDate = queryDate[0] ? queryDate[0] + ' 00:00:00' : '';
                this.queryForm.queryEndDate = queryDate[1] ? queryDate[1] + ' 23:59:59' : '';
            } else {
                this.queryForm.queryStartDate = undefined;
                this.queryForm.queryEndDate = undefined;
            }
            this.getList();
        },
        /**
         * 撤销
         * @param row
         */
        onDelete(row) {
            this.$confirm(`<span style="color: red">确认撤销？</span><br/>撤销后付款单将被全部释放为"未申请"状态!`, '', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '确 认',
                cancelButtonText: '取 消',
                showClose: false,
                center: true,
                customClass: 'dialog-confirm',
                confirmButtonClass: 'el-button--large',
                cancelButtonClass: 'el-button--large'
            })
                .then(async () => {
                    try {
                        const res = await paymentOrderApproval.deletePaymentOrder({ ids: row.id });
                        if (res.code === 200) {
                            this.$message.success('撤销成功');
                            await this.getList();
                        } else {
                            this.$message.error(res.msg || '撤销失败');
                        }
                    } catch (err) {
                        this.$message.error('撤销失败');
                        console.error(err);
                    }
                })
                .catch(() => {});
        },
        /**
         * 打开开票
         * @param row
         */
        onOpenInvoice(row) {
            this.invoiceVisible = true;
            this.getInvoiceHeaderList();
            if ('remitAmount' in row) {
                this.invoiceForm.paymentApplyId = row.id;
                this.invoiceForm.carrierId = row.carrier.id;
                this.invoiceForm.companyId = row.companyId;
                this.invoiceForm.companyName = row.companyName;
                this.invoiceForm.receiveCompany = row.receiveCompany;
                this.invoiceForm.invoiceAmount = row.remitAmount;
                this.invoiceForm.remitTime = row.remitTime;
                this.invoiceForm.remark = row.remark;
                this.invoiceForm.advanceAccount = row.advanceAccount;
            }
            // 默认收款公司
            const Organization = this.$TOOL.data.get('Organization');
            // 获取付款类型
            customerPrepaymentBalance.getCompanyInfo(Organization[0].id, row.carrier.id).then((res) => {
                if (res.code === 200 && res.data?.signCompany) {
                    this.invoiceForm.signCompany = res.data.signCompany;
                    this.invoiceForm.invoiceList = res.data.invoiceList;
                    if (res.data.invoiceList && res.data.invoiceList.length > 0) {
                        const firstItem = res.data.invoiceList[0];
                        this.invoiceForm.invoiceType = firstItem.invoiceType;
                        this.invoiceForm.projectType = firstItem.projectType;
                        this.invoiceForm.projectFeeType = firstItem.projectFeeType;
                        this.invoiceForm.taxPoint = firstItem.taxPoint;
                    }
                } else {
                    this.$message.warning('未获取到签约公司信息');
                    this.invoiceForm.signCompany = undefined;
                    this.invoiceForm.invoiceType = undefined;
                    this.invoiceForm.projectFeeType = undefined;
                    this.invoiceForm.projectType = undefined;
                    this.invoiceForm.taxPoint = undefined;
                }
            });
        },
        /**
         * 打开付款单审批
         * @param status approval 审批 modify 修改 detail 查看
         * @param row
         * @return {Promise<void>}
         */
        onOpenPaymentApproval(status, row) {
            this.paymentOrderApprovalVisible = true;
            if (status === 'approval') {
                this.paymentOrderApprovalTitle = '付款单审批';
            } else if (status === 'modify') {
                this.paymentOrderApprovalTitle = '付款单修改';
            } else if (status === 'detail') {
                this.paymentOrderApprovalTitle = '付款单查看';
            }
            this.paymentOrderApprovalStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.paymentOrderApprovalId = row.id;
            }
        },
        /**
         * 打开预付款充值修改
         * @param status approval 审批 modify 修改 detail 查看
         * @param row
         */
        onOpenPrepaymentRechargeModification(status, row) {
            this.prepaymentRechargeModificationVisible = true;
            if (status === 'approval') {
                this.prepaymentRechargeModificationTitle = '审批';
            } else if (status === 'modify') {
                this.prepaymentRechargeModificationTitle = '修改';
            } else if (status === 'detail') {
                this.prepaymentRechargeModificationTitle = '查看';
            }
            this.prepaymentRechargeModificationStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.prepaymentRechargeModificationId = row.id;
            }
        },
        /**
         * 费用类型变更
         * @param val
         */
        projectFeeTypeChange(val) {
            if (this.invoiceForm.invoiceList.length > 1) {
                // 找到匹配的项目配置
                const matchedItem = this.invoiceForm.invoiceList.find((item) => item.projectType === this.invoiceForm.projectType && item.projectFeeType === val);
                if (matchedItem) {
                    this.invoiceForm.invoiceType = matchedItem.invoiceType;
                    this.invoiceForm.taxPoint = matchedItem.taxPoint;
                }
            }
        },
        projectIdChange(val) {
            if (val === '运输服务') {
                this.invoiceForm.projectFeeType = '运输服务费';
                if (this.invoiceForm.signCompany === '1') {
                    this.invoiceForm.taxPoint = '9';
                } else {
                    this.invoiceForm.taxPoint = '1';
                }
            } else {
                this.invoiceForm.projectFeeType = '收派服务费';
                if (this.invoiceForm.signCompany === '1') {
                    this.invoiceForm.taxPoint = '6';
                } else {
                    this.invoiceForm.taxPoint = '1';
                }
            }
        },
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
        },
        /**
         * 重置查询条件
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryForm = {
                current: 1,
                size: 10,
                taskType: '1',
                applyWay: '1',
                status: undefined,
                queryDate: []
            };
            this.handleQuery();
        },
        /**
         * 提交开票
         */
        submitInvoice() {
            this.$refs.invoiceForm.validate(async (valid) => {
                if (valid) {
                    this.invoiceLoading = true;
                    const params = {
                        applyWay: '1', // 货主端1 承运端2
                        businessType: '1', // 1-预存款开票 2-付款单开票
                        paymentApplyId: this.invoiceForm.paymentApplyId || '',
                        companyId: this.invoiceForm.companyId || '',
                        companyName: this.invoiceForm.companyName || '',
                        carrierId: this.invoiceForm.carrierId || '',
                        receiveCompany: this.invoiceForm.receiveCompany || '',
                        invoiceId: this.invoiceForm.invoiceData?.id || '',
                        taxNo: this.invoiceForm.invoiceData?.taxNo || '',
                        invoiceHead: this.invoiceForm.invoiceData?.invoiceHead || '',
                        address: this.invoiceForm.invoiceData?.address || '',
                        phone: this.invoiceForm.invoiceData?.phone || '',
                        openBank: this.invoiceForm.invoiceData?.openBank || '',
                        bankAccount: this.invoiceForm.invoiceData?.bankAccount || '',
                        invoiceAmount: this.invoiceForm.invoiceAmount || 0,
                        remitTime: this.invoiceForm.remitTime || '',
                        remark: this.invoiceForm.remark || '',
                        advanceAccount: this.invoiceForm.advanceAccount || '',
                        projectFeeType: this.invoiceForm.projectFeeType || '',
                        projectType: this.invoiceForm.projectType || '',
                        openInvoiceType: '1', // 1-预付款开票 2-月结&现付开票 3-月结开票 4-到付开票 5-现付开票
                        invoiceType: this.invoiceForm.invoiceType || '',
                        projectName: this.invoiceForm.projectFeeType || '',
                        signCompany: this.invoiceForm.signCompany || '',
                        taxPoint: this.invoiceForm.taxPoint || ''
                    };
                    paymentDoc
                        .applyInvoice(params)
                        .then((res) => {
                            if (res.code === 200) {
                                this.$message.success('开票申请成功');
                                this.closeInvoice();
                                this.getList();
                            }
                        })
                        .finally(() => {
                            this.invoiceLoading = false;
                        });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #000 !important;
}
.number__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 10px;
        top: 47%;
        transform: translateY(-50%);
    }
}
.tax_point_unit {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '%';
        position: absolute;
        right: 30px;
        top: 51%;
        transform: translateY(-50%);
    }
}
</style>
