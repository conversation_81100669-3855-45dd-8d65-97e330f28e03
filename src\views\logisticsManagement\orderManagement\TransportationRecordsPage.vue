<template>
    <div v-loading="loading" class="app-container customer-auto-height-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryParams" :inline="true" :model="queryParams" class="seache-form" @submit.prevent>
                <el-form-item label="订单编号" prop="orderNo" style="width: 230px">
                    <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单编号" @clear="handleQuery" @keyup.enter="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="运单号" prop="transOrderNo" style="width: 230px">
                    <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单号" @clear="handleQuery" @keyup.enter="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="发货日期" prop="orderTime" style="width: 320px">
                    <el-date-picker v-model="queryParams.orderTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="货主公司" prop="companyId" style="width: 320px">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="handleQuery">
                        <el-option v-for="(dict, index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收货公司" prop="receiverCompany" style="width: 260px">
                    <el-input v-model="queryParams.receiverCompany" clearable placeholder="请输入收货公司" @clear="handleQuery" @keyup.enter="handleQuery"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运输方式" prop="carrierWay" style="width: 240px">
                    <el-select v-model="queryParams.carrierWay" clearable filterable placeholder="请选择运输方式">
                        <el-option v-for="item in carrierWayList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="温层类型" prop="temperatureList">
                    <el-select v-model="queryParams.temperatureList" clearable collapse-tags filterable multiple placeholder="请选择温层类型" style="width: 250px" @change="handleQuery">
                        <el-option v-for="(dict, index) in thermosphereTypeList" :key="index" :label="dict.describtion" :value="dict.id" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单状态" prop="transStatus" style="width: 240px">
                    <el-select v-model="queryParams.transStatus" clearable filterable placeholder="请选择运单状态">
                        <el-option v-for="item in transStatusList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" :loading="loading" @handleQuery="handleQuery" @resetQuery="resetQuery('queryParams')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div class="mb10">
                <el-button :disabled="!dataList || dataList.length === 0" icon="el-icon-download" type="warning" @click="exportList">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="tableTransportationRecordsPage" @queryTable="handleQuery" />
            </div>
            <column-table ref="tableTransportationRecordsPage" :columns="columns" :data="dataList" :show-index="true" class="customer-auto-height-table" max-height="null">
                <template #tool="{ row }">
                    <el-tag v-for="(item, index) in row.tool ? row.tool.split(',') : []" :key="index" size="small" style="margin-right: 5px">{{ item }}</el-tag>
                </template>
                <template #deviceNo="{ row }">
                    <el-tag v-for="(item, index) in row.deviceNo ? row.deviceNo.split(',') : []" :key="index" size="small" style="margin-right: 5px">{{ item }}</el-tag>
                </template>
                <template #startShipmentTemp="{ row }">
                    {{ row.startShipmentTemp ? row.startShipmentTemp + '℃' : '-' }}
                </template>
                <template #deliveryTemp="{ row }">
                    {{ row.deliveryTemp ? row.deliveryTemp + '℃' : '-' }}
                </template>
                <template #transStatus="{ row }">
                    {{ row.transStatus ? transStatusList.find((item) => item.code === row.transStatus)?.name : '-' }}
                </template>
                <template #carrierWay="{ row }">
                    {{ row.carrierWay ? carrierWayList.find((item) => item.code === row.carrierWay)?.name : '-' }}
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="getViewDetails(row)">查看</el-button>
                    <el-button v-if="row.status !== '1'" icon="el-icon-edit" link size="small" type="warning" @click="setViewDetails(row)">修改</el-button>
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleOperationLog(row)">操作日志</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>

        <!-- 查看详情 -->
        <el-drawer v-if="reviewAndModifyShow" v-model="reviewAndModifyShow" size="60vw" title="查看" @close="closeReviewAndModifyShow">
            <div style="background-color: #f2f2f2; padding: 10px">
                <transportation-records :parent-data="transportationData" />
            </div>
        </el-drawer>

        <!-- 修改 -->
        <el-drawer v-if="modifyPopupShow" v-model="modifyPopupShow" size="60vw" title="修改" @close="closeModifyShow">
            <div style="background-color: #f2f2f2; padding: 10px">
                <modify-transportation-records :parameters="transportationData" @close="closeModifyShow" />
            </div>
        </el-drawer>

        <!-- 操作日志弹窗 -->
        <el-dialog v-model="operationLogVisible" :before-close="handleOperationLogClose" title="操作日志" width="80%">
            <div class="operation-log">
                <h4>修改内容</h4>
                <el-timeline>
                    <el-timeline-item v-for="(item, index) in formattedLogs" :key="index">
                        <div class="log-content">
                            <div class="operator">
                                <span class="time">{{ item.operateTime }}</span>
                                操作人：{{ item.operateName }}
                            </div>
                            <div class="before-after">
                                <div class="before">
                                    <h5>修改前</h5>
                                    <div v-for="(change, idx) in item.changes" :key="'before' + idx">
                                        {{ change.fieldName }}:
                                        <template v-if="isImageArray(change.oldValue)">
                                            <div class="image-preview-list">
                                                <div v-for="(img, imgIdx) in change.oldValue" :key="img.fileUrl" class="image-preview" @click="handlePreview(change.oldValue, imgIdx)">
                                                    <img v-if="getFileType(img.fileUrl) === 'pdf'" :title="img.fileName" class="preview-thumbnail" src="/img/pdf.png" />
                                                    <img v-else :src="img.fileUrl" :title="img.fileName" class="preview-thumbnail" />
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else>{{ change.oldValue || '-' }}</template>
                                    </div>
                                </div>
                                <div class="after">
                                    <h5>修改后</h5>
                                    <div v-for="(change, idx) in item.changes" :key="'after' + idx">
                                        {{ change.fieldName }}:
                                        <template v-if="isImageArray(change.newValue)">
                                            <div class="image-preview-list">
                                                <div v-for="(img, imgIdx) in change.newValue" :key="img.fileUrl" class="image-preview" @click="handlePreview(change.newValue, imgIdx)">
                                                    <img v-if="getFileType(img.fileUrl) === 'pdf'" :title="img.fileName" class="preview-thumbnail" src="/img/pdf.png" />
                                                    <img v-else :src="img.fileUrl" :title="img.fileName" class="preview-thumbnail" />
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else>
                                            <span style="color: #f56c6c">{{ change.newValue || '-' }}</span>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </el-dialog>

        <!-- 图片预览组件 -->
        <component :is="initComponent" v-if="dialogVisible" :current="initialIndex" :url-list="fileUrlList" @close="dialogVisible = false"></component>
    </div>
</template>
<script>
import moment from 'moment';
import ModifyTransportationRecords from '@/views/logisticsManagement/orderManagement/ModifyTransportationRecords.vue';
import TransportationRecords from '@/views/logisticsManagement/orderManagement/TransportationRecords.vue';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation.js'; // 合作配置
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration';
import transportationRecordsPage from '@/api/carrierEnd/transportationRecordsPage';
import { downloadNoData } from '@/utils';
import { selectDictLabel } from '@/utils/dictLabel';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'TransportationRecordsPage',
    components: { ModifyTransportationRecords, TransportationRecords, RightToolbar, ColumnTable, SearchButton },
    data() {
        return {
            queryParams: {
                current: 1,
                size: 10,
                orderNo: undefined,
                transOrderNo: undefined,
                orderTime: [],
                beginOrderTime: undefined,
                endOrderTime: undefined,
                receiverCompany: undefined,
                carrierWay: undefined,
                temperatureList: [],
                transStatus: undefined,
                companyId: undefined
            },
            showSearch: true,
            isShowAll: false,
            thermosphereTypeList: [],
            shortcuts: [
                {
                    text: '无',
                    value: () => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: () => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ],
            columns: [
                { title: '发货日期', key: 'orderTime', align: 'center', width: '155px', columnShow: true, showOverflowTooltip: true },
                { title: 'ERP订单号', key: 'externalOrderNo', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '130px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收货公司', key: 'receiverCompany', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收货地址', key: 'receiverAddress', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收货电话', key: 'receiverUserPhone', align: 'center', width: '110px', columnShow: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '80px', columnShow: true },
                { title: '运输方式', key: 'carrierWay', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '运输工具', key: 'tool', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '设备号', key: 'deviceNo', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '温层类型', key: 'temperatureDesc', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '运单状态', key: 'transStatus', align: 'center', width: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '运输人', key: 'transportName', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '收货人', key: 'receiverUser', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '启运时间', key: 'startShipmentTime', align: 'center', width: '180px', columnShow: true },
                { title: '启运温度', key: 'startShipmentTemp', align: 'center', width: '100px', columnShow: true },
                { title: '送达时间', key: 'deliveryTime', align: 'center', width: '180px', columnShow: true },
                { title: '送达温度', key: 'deliveryTemp', align: 'center', width: '100px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '220px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            dataList: [],
            total: 0,
            loading: false,
            reviewAndModifyShow: false,
            modifyPopupShow: false,
            transportationData: {},
            transStatusList: [],
            carrierWayList: [],
            ownerList: [], // 货主列表
            operationLogVisible: false,
            operationLogs: [],
            dialogVisible: false,
            fileUrlList: [],
            initialIndex: 0,
            initComponent: null
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        formattedLogs() {
            return this.operationLogs.map((log) => {
                const changes = JSON.parse(log.content);
                return {
                    ...log,
                    changes: Array.isArray(changes) ? changes : []
                };
            });
        }
    },
    created() {
        this.getDict();
        this.getTemperatureType();
        this.getCompanySelect();

        // 将默认设置调整为当天
        this.queryParams.beginOrderTime = moment().format('YYYY-MM-DD');
        this.queryParams.endOrderTime = moment().format('YYYY-MM-DD');
        this.queryParams.orderTime = [this.queryParams.beginOrderTime, this.queryParams.endOrderTime];

        this.handleQuery();
    },
    methods: {
        /**
         * 关闭修改弹窗
         */
        closeModifyShow() {
            this.modifyPopupShow = false;
            this.getList();
            this.transportationData = {};
        },
        /**
         * 关闭查看弹窗
         */
        closeReviewAndModifyShow() {
            this.reviewAndModifyShow = false;
            this.getList();
            this.transportationData = {};
        },
        /**
         * 导出
         */
        exportList() {
            // 提示框 询问是否导出全部
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    // eslint-disable-next-line no-unused-vars
                    const { orderTime, ...params } = this.queryParams;
                    transportationRecordsPage
                        .exportData({ filename: '运输记录.xls', ...params }, '', '', 'blob')
                        .then((res) => {
                            downloadNoData(res, 'application/vnd.ms-excel', '运输记录.xlsx');
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        // 获取货主公司下拉
        getCompanySelect() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.transStatusList = await this.getDictList('delivery_task_waybill_status');
            this.carrierWayList = await this.getDictList('fourpl_transportation_mode');
        },
        /**
         * 获取文件类型
         * @param {string} fileUrl
         * @returns {string}
         */
        getFileType(fileUrl) {
            if (fileUrl && fileUrl.toLowerCase().includes('.pdf')) {
                return 'pdf';
            }
            if (fileUrl && /\.(jpg|jpeg|png|gif|bmp)$/i.test(fileUrl)) {
                return 'image';
            }
            return 'unknown';
        },
        getList() {
            this.loading = true;
            // eslint-disable-next-line no-unused-vars
            const { orderTime, ...params } = this.queryParams;

            // 处理 temperatureList 转为字符串
            if (params.temperatureList && params.temperatureList.length) {
                params.temperatureList = params.temperatureList.join(',');
            }

            transportationRecordsPage
                .getList(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    } else {
                        this.$message.error(res.message || '获取数据失败');
                    }
                })
                .finally(() => {
                    this.loading = false;
                })
                .catch(() => {});
        },
        /**
         * 获取温层类型
         */
        getTemperatureType() {
            otherConfiguration.getTemperatureTypeList({ status: '0' }).then((res) => {
                if (res.code === 200 && res?.data?.records.length > 0) {
                    this.thermosphereTypeList = res.data.records;
                }
            });
        },
        /**
         * 查看详情
         * @param row
         */
        getViewDetails(row) {
            if (!row.id) {
                this.$message.error('无效的记录');
                return;
            }
            this.transportationData.orderNo = row.orderNo;
            this.transportationData.transOrderNo = row.transOrderNo;
            this.transportationData.isDownloadAllImages = true;
            // 不可编辑
            this.transportationData.isEdit = false;
            this.reviewAndModifyShow = true;
        },
        /**
         * 打开操作日志弹窗
         * @param row
         */
        handleOperationLog(row) {
            transportationRecordsPage.getChangeRecord({ transportId: row.id }).then((res) => {
                if (res.code === 200 && res.data.length > 0) {
                    this.operationLogs = res.data;
                    this.operationLogVisible = true;
                } else if (res.code === 200 && res.data.length === 0) {
                    this.$message.error('暂无修改记录');
                } else {
                    this.$message.error(res.message || '获取数据失败');
                }
            });
        },
        /**
         * 关闭操作日志弹窗
         */
        handleOperationLogClose() {
            this.operationLogVisible = false;
            this.operationLogs = [];
        },
        /**
         * 预览图片
         * @param {Array} images 图片数组
         * @param {number} index 当前图片索引
         */
        handlePreview(images, index = 0) {
            import('@/components/thePreviewDialog/ThePreviewDialog.vue').then((module) => {
                this.initComponent = module.default;
                this.dialogVisible = true;
                this.fileUrlList = images.map((item) => item.fileUrl);
                this.initialIndex = index;
            });
        },
        handleQuery() {
            this.queryParams.current = 1;

            const { orderTime } = this.queryParams;

            // 验证queryTime是否包含两个合法的日期字符串
            const isValidDateRange = orderTime && orderTime.length === 2 && !orderTime.some((date) => date === 'Invalid Date');

            if (isValidDateRange) {
                this.queryParams.beginOrderTime = orderTime[0] + ' 00:00:00';
                this.queryParams.endOrderTime = orderTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginOrderTime = null;
                this.queryParams.endOrderTime = null;
            }

            this.getList();
        },
        /**
         * 判断是否为图片数组
         * @param {any} value
         * @returns {boolean}
         */
        isImageArray(value) {
            return Array.isArray(value) && value.length > 0 && value.every((item) => item.fileUrl);
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleQuery();
        },
        /**
         * 修改
         * @param row
         */
        setViewDetails(row) {
            if (!row.id) {
                this.$message.error('无效的记录');
                return;
            }
            this.transportationData.id = row.id;
            this.transportationData.temperatureType = row.temperatureType;
            this.transportationData.transOrderNo = row.transOrderNo;
            this.modifyPopupShow = true;
        },
        /**
         * 显示全部
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}

.operation-log {
    h4 {
        margin-bottom: 20px;
        font-size: 16px;
        color: #333;
        position: relative;
        padding-left: 12px;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: #409eff;
            border-radius: 2px;
        }
    }

    .log-content {
        .operator {
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;

            .time {
                color: #333;
                font-weight: normal;
                margin-right: 20px;
            }
        }

        .before-after {
            display: flex;
            gap: 40px;
            margin-bottom: 10px;
            border: 1px solid #dcdfe6;
            padding: 16px;
            border-radius: 4px;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                left: 50%;
                top: 16px;
                bottom: 16px;
                width: 1px;
                background-color: #dcdfe6;
            }

            .before,
            .after {
                flex: 1;

                h5 {
                    margin-bottom: 8px;
                    color: #666;
                    font-weight: normal;
                }

                div {
                    line-height: 1.8;
                    color: #333;
                    margin-bottom: 4px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            .after {
                div {
                    &:not(h5) {
                        color: #333;

                        &::after {
                            content: attr(data-value);
                            color: #f56c6c;
                        }
                    }
                }
            }
        }
    }
}

:deep(.el-timeline-item__timestamp) {
    color: #333;
    font-size: 14px;
    font-weight: bold;
}

:deep(.el-timeline-item__node) {
    background-color: #fff;
    border: 2px solid #409eff;
}

:deep(.el-timeline-item__wrapper) {
    padding-left: 28px;
}

.image-preview-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 4px 0;
}

.image-preview {
    display: inline-block;
    cursor: pointer;

    .preview-thumbnail {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
    }

    &:hover {
        .preview-thumbnail {
            border-color: #409eff;
        }
    }
}
</style>
