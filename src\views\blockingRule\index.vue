<template>
    <div>
        <!-- 查询表头 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item label="规则名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入规则名称" size="normal" clearable @change=""
                        class="form_225" maxlength="30"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchQuery">搜索</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表单区域 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(addForm)" class="creatSpan">新增</el-button>
            <el-table v-loading="loading" :data="configeList" style=" margin-top: 10px;" border>
                <el-table-column label="规则名称" align="left" prop="name">
                </el-table-column>
                <el-table-column label="有效期" align="left" prop="categoryName">
                    <template #default="scope">
                        {{ scope.row.beginValidDate.slice(0, 10) + '至' + scope.row.endValidDate.slice(0, 10) }}
                    </template>
                </el-table-column>
                <el-table-column label="创建日期" align="left" prop="createDate"
                    :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')">
                </el-table-column>
                <el-table-column label="修改日期" align="left" prop="updateDate"
                    :formatter="(row) => moment(row.updateDate).format('YYYY-MM-DD')">
                </el-table-column>
                <el-table-column label="创建人" align="left" prop="alarmName">
                    <template #default="scope">
                        {{ alarmName }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="350">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row, editForm)"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                        <el-button link type="danger" @click="handleDelete(scope.row)"><img src="@/assets/icons/delete.png"
                                style="margin-right:5px" />删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增 -->
        <el-dialog v-model="dialogFormVisible" title="创建销售拦截规则" :before-close="() => handlerClose()">
            <el-form :model="dialogform" ref="addForm" :rules="rules" size="normal">
                <el-collapse v-model="activeNames" @change="handleChange">
                    <el-collapse-item title="基本信息" name="1">
                        <template #title>
                            <span class="col_title">基本信息</span>
                        </template>
                        <div style="display:flex">
                            <el-form-item label="规则名称" prop="name">
                                <el-input v-model="dialogform.name" placeholder="请输入规则名称" size="normal" clearable
                                    style="width:1.5rem" maxlength="30"></el-input>
                            </el-form-item>
                            <el-form-item label="有效期" prop="createDate" style="margin-left:30px">
                                <el-date-picker v-model="dialogform.createDate" type="daterange" range-separator="至"
                                    start-placeholder="开始日期" end-placeholder="结束日期" />
                            </el-form-item>
                        </div>
                    </el-collapse-item>
                    <el-collapse-item title="详细信息" name="2">
                        <template #title>
                            <span class="col_title">详细信息</span>
                        </template>
                        <el-card class="box-card Botm">
                            <p style="margin-bottom:5px;">指定商品</p>
                            <el-button @click="() => productAdd()" type="primary">添加</el-button>
                            <el-button type="danger" @click="deleteEntrust">删除</el-button>
                            <p style="margin-top:10px;">说明：若为空，则默认对全部商品生效</p>
                            <el-table :data="entrustList" border @selection-change="changeEntru"
                                style="margin-top:5px;margin-bottom:20px;">
                                <el-table-column type="selection" min-width="55" align="center" />
                                <el-table-column label="自编码" prop="commoditySelfCode" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="商品名称" prop="tradeName" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="规格" prop="packageSpecification" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="剂型" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="产地" prop="originPlace" :show-overflow-tooltip="true" align="center"
                                    min-width="140" />
                                <el-table-column label="生产厂家" prop="manufacture.enterpriseName" align="center"
                                    min-width="120" :show-overflow-tooltip="true" />
                            </el-table>
                        </el-card>
                    </el-collapse-item>
                    <el-collapse-item title="拦截规则" name="3">
                        <template #title>
                            <span class="col_title">拦截规则</span>
                        </template>
                        <el-card class="box-card Botm">
                            <p style="margin-bottom:5px;">指定客户</p>
                            <el-button @click="() => customerAdd()" type="primary">添加</el-button>
                            <el-button type="danger" @click="deleteCustomers">删除</el-button>
                            <el-table :data="customerList" border @selection-change="changeCustom"
                                style="margin-top:5px;margin-bottom:20px;">
                                <el-table-column type="selection" min-width="55" align="center" />
                                <el-table-column label="自编码" prop="selfCoding" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="客户名称" prop="enterpriseName" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="资质类别" prop="credentialType" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="注册地址" prop="residence" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="所在区域" prop="province" :show-overflow-tooltip="true" align="center"
                                    min-width="140">
                                    <template #default="scope">
                                        {{ filterAddress(scope.row.province) || '--' }}
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="margin:10px;">
                                <p>指定客户业务类型</p>
                                <el-radio-group v-model="busType">
                                    <el-radio label="1" size="large">医院业务</el-radio>
                                    <el-radio label="2" size="large">商业业务</el-radio>
                                    <!-- <el-checkbox v-model="checked1" label="医院业务" size="large" />
                                    <el-checkbox v-model="checked2" label="商业业务" size="large" /> -->
                                </el-radio-group>
                            </div>
                        </el-card>
                        <el-card class="box-card Botm">
                            <p style="margin-bottom:5px;">指定区域</p>
                            <el-form-item label="限制类型" prop="type">
                                <el-select v-model="type" placeholder="请选择限制类型" @change="restriction">
                                    <el-option :label="item.name" :value="item.value" v-for=" item  in  typeList"
                                        :key="item.id" />
                                </el-select>
                            </el-form-item>
                            <div v-if="displayArea">
                                <el-button @click="() => regionAdd()" type="primary">添加</el-button>
                                <el-button type="danger" @click="deleteAreas">删除</el-button>
                                <p style="margin-top:5px;">说明：若销售区域为空，则默认无区域限制；一行数据中“市”、“区”可为空，为空即默认全部</p>
                                <el-table :data="regionList" border @selection-change="changeAre"
                                    style="margin-top:5px;margin-bottom:20px;">
                                    <el-table-column type="selection" min-width="55" align="center" />
                                    <el-table-column label="省" prop="commodityCode" :show-overflow-tooltip="true"
                                        align="center" min-width="120">
                                        <template #default="scope">
                                            <el-select v-if='regionList.length' @change="fuzhi(scope.row.provinces, 1)"
                                                v-model="scope.row.provinces" placeholder="请选择省份" clearable filterable>
                                                <el-option v-for="item in sysAreas" :key="item.value" :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="市" prop="tradeName" :show-overflow-tooltip="true" align="center"
                                        min-width="120">
                                        <template #default="scope">
                                            <el-select @change="fuzhi(scope.row.city, 2)" v-if='regionList.length'
                                                v-model="scope.row.city" placeholder="请选择市" clearable filterable>
                                                <el-option v-for="item in shi" :key="item.value" :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="区" prop="packageSpecification" :show-overflow-tooltip="true"
                                        align="center" min-width="120">
                                        <template #default="scope">
                                            <el-select v-if='regionList.length' v-model="scope.row.area" placeholder="请选择区"
                                                clearable filterable>
                                                <el-option v-for="item in qu" :key="item.value" :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-card>
                    </el-collapse-item>
                </el-collapse>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="saveAlarm(addForm)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 编辑 -->
        <el-dialog v-model="editdialogFormVisible" title="修改销售拦截规则" :before-close="() => handlerClose()">
            <el-form :model="editdialogform" ref="editForm" :rules="rules" size="normal">
                <el-collapse v-model="activeNames" @change="handleChange">
                    <el-collapse-item title="基本信息" name="1">
                        <template #title>
                            <span class="col_title">基本信息</span>
                        </template>
                        <div style="display:flex">
                            <el-form-item label="规则名称" prop="name">
                                <el-input v-model="editdialogform.name" placeholder="请输入规则名称" size="normal" clearable
                                    style="width:1.5rem" maxlength="30"></el-input>
                            </el-form-item>
                            <el-form-item label="有效期" prop="createDate" style="margin-left:30px">
                                <el-date-picker v-model="editdialogform.createDate" type="daterange" range-separator="至"
                                    start-placeholder="开始日期" end-placeholder="结束日期" />
                            </el-form-item>
                        </div>
                    </el-collapse-item>
                    <el-collapse-item title="详细信息" name="2">
                        <template #title>
                            <span class="col_title">详细信息</span>
                        </template>
                        <el-card class="box-card Botm">
                            <p style="margin-bottom:5px;">指定商品</p>
                            <el-button @click="() => productAdd()" type="primary">添加</el-button>
                            <el-button type="danger" @click="editdeletecom">删除</el-button>
                            <p style="margin-top:10px;">说明：若为空，则默认对全部商品生效</p>
                            <el-table :data="editentrustList" border @selection-change="editdeletecommodity"
                                style="margin-top:5px;margin-bottom:20px;">
                                <el-table-column type="selection" min-width="55" align="center" />
                                <el-table-column label="自编码" prop="commoditySelfCode" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="商品名称" prop="tradeName" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="规格" prop="packageSpecification" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="剂型" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="产地" prop="originPlace" :show-overflow-tooltip="true" align="center"
                                    min-width="140" />
                                <el-table-column label="生产厂家" prop="manufacture.enterpriseName" align="center"
                                    min-width="120" :show-overflow-tooltip="true" />
                            </el-table>
                        </el-card>
                    </el-collapse-item>
                    <el-collapse-item title="拦截规则" name="3">
                        <template #title>
                            <span class="col_title">拦截规则</span>
                        </template>
                        <el-card class="box-card Botm">
                            <p style="margin-bottom:5px;">指定客户</p>
                            <el-button @click="() => customerAdd()" type="primary">添加</el-button>
                            <el-button type="danger" @click="editdeletecustomer">删除</el-button>
                            <el-table :data="editcustomerList" border @selection-change="editdeletecus"
                                style="margin-top:5px;margin-bottom:20px;">
                                <el-table-column type="selection" min-width="55" align="center" />
                                <el-table-column label="自编码" prop="selfCoding" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="客户名称" prop="enterpriseName" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="资质类别" prop="credentialType" :show-overflow-tooltip="true"
                                    align="center" min-width="120" />
                                <el-table-column label="注册地址" prop="residence" :show-overflow-tooltip="true" align="center"
                                    min-width="120" />
                                <el-table-column label="所在区域" prop="province" :show-overflow-tooltip="true" align="center"
                                    min-width="140">
                                    <template #default="scope">
                                        {{ filterAddress(scope.row.province) || '--' }}
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="margin:10px;">
                                <p>指定客户业务类型</p>
                                <el-radio-group v-model="radio1">
                                    <el-radio label="1" size="large">医院业务</el-radio>
                                    <el-radio label="2" size="large">商业业务</el-radio>
                                </el-radio-group>
                            </div>
                        </el-card>
                        <el-card class="box-card Botm">
                            <p style="margin-bottom:5px;">指定区域</p>
                            <el-form-item label="限制类型" prop="type">
                                <el-select v-model="editType" placeholder="请选择限制类型" @change="restriction">
                                    <el-option :label="item.name" :value="item.value" v-for=" item  in  typeList"
                                        :key="item.id" />
                                </el-select>
                            </el-form-item>
                            <div v-if="displayArea">
                                <el-button @click="() => regionAdd()" type="primary">添加</el-button>
                                <el-button type="danger" @click="editdaletepro">删除</el-button>
                                <p style="margin-top:5px;">说明：若销售区域为空，则默认无区域限制；一行数据中“市”、“区”可为空，为空即默认全部</p>
                                <el-table :data="editregionList" border @selection-change="editdeleteare"
                                    style="margin-top:5px;margin-bottom:20px;">
                                    <el-table-column type="selection" min-width="55" align="center" />
                                    <el-table-column label="省" prop="commodityCode" :show-overflow-tooltip="true"
                                        align="center" min-width="120">
                                        <template #default="scope">
                                            <el-select v-if='editregionList.length' @change="fuzhi(scope.row.provinces, 1)"
                                                v-model="scope.row.provinces" placeholder="请选择省份" clearable filterable>
                                                <el-option v-for="item in sysAreas" :key="item.value" :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="市" prop="tradeName" :show-overflow-tooltip="true" align="center"
                                        min-width="120">
                                        <template #default="scope">
                                            <el-select @change="fuzhi(scope.row.city, 2)" v-if='editregionList.length'
                                                v-model="scope.row.city" placeholder="请选择市" clearable filterable>
                                                <el-option v-for="item in shi" :key="item.value" :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="区" prop="packageSpecification" :show-overflow-tooltip="true"
                                        align="center" min-width="120">
                                        <template #default="scope">
                                            <el-select v-if='editregionList.length' v-model="scope.row.area"
                                                placeholder="请选择区" clearable filterable>
                                                <el-option v-for="item in qu" :key="item.value" :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-card>
                    </el-collapse-item>
                </el-collapse>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => handlerClose()">取消</el-button>
                    <el-button type="primary" @click="editAlarm(editForm,)">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 商品弹框 -->
        <el-dialog title="添加商品" v-model="productVisible" style="width:40%;">
            <el-input style="width:40%;" v-model="entrustparams.name" placeholder="请输入商品名称/拼音码/自编码" size="normal" clearable
                @clear="productAdd"></el-input>
            <el-button type="primary" @click.stop="searchEntrust" style="margin-left:20px;">搜索</el-button>
            <el-table :data="addentrustList" border @selection-change="addentrustListChange" style="margin-top:20px;">
                <el-table-column type="selection" min-width="55" align="center" />
                <el-table-column label="自编码" prop="commoditySelfCode" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="商品名称" prop="tradeName" :show-overflow-tooltip="true" align="center"
                    min-width="100" />
                <el-table-column label="规格" prop="packageSpecification" :show-overflow-tooltip="true" align="center"
                    min-width="100" />
                <el-table-column label="剂型" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                    min-width="100" />
                <el-table-column label="产地" prop="originPlace" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="生产厂家" prop="manufacture.enterpriseName" align="center" min-width="120"
                    :show-overflow-tooltip="true" />
            </el-table>
            <div style="float: right;">
                <pagination :total="total2" v-model:page="entrustparams.current" v-model:limit="entrustparams.size"
                    @pagination="productAdd" />
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="productVisible = false" style="margin-top:-40px;">取消</el-button>
                    <el-button type="primary" @click="pushEntrust(addForm)" style="margin-top:-40px;">
                        添加
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 客户弹框 -->
        <el-dialog title="添加客户" v-model="customerVisible" style="width:40%;">
            <el-input style="width:40%;" v-model="entrustparams2.commonValue" placeholder="请输入客户名称/拼音码/自编码" size="normal"
                clearable @clear="customerAdd"></el-input>
            <el-button type="primary" @click.stop="searchCustomer" style="margin-left:20px;">搜索</el-button>
            <el-table :data="addcustomerList" border @selection-change="addcustomerListChange" style="margin-top:20px;">
                <el-table-column type="selection" min-width="55" align="center" />
                <el-table-column label="自编码" prop="selfCoding" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="客户名称" prop="enterpriseName" :show-overflow-tooltip="true" align="center"
                    min-width="100" />
                <el-table-column label="资质类别" prop="credentialType" :show-overflow-tooltip="true" align="center"
                    min-width="100" />
                <el-table-column label="注册地址" prop="residence" :show-overflow-tooltip="true" align="center"
                    min-width="100" />
                <el-table-column label="所在区域" prop="province" :show-overflow-tooltip="true" align="center" min-width="120">
                    <template #default="scope">
                        {{ filterAddress(scope.row.province) || '--' }}
                    </template>
                </el-table-column>
            </el-table>
            <div style="float: right;">
                <pagination :total="total2" :pager-count="4" v-model:page="entrustparams2.current"
                    v-model:limit="entrustparams2.size" @pagination="customerAdd" />
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="customerVisible = false" style="margin-top:-40px;">取消</el-button>
                    <el-button type="primary" @click="pushcustomer(addForm)" style="margin-top:-40px;">
                        添加
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import blockingRule from "@/api/erp/blockingRule";
import { regionData } from "element-china-area-data";
import areas_third from "@/assets/areas/areas_third.json";
const { proxy } = getCurrentInstance();
import { uuid } from 'vue-uuid';
import { ElMessage, ElMessageBox } from "element-plus";
import moment from 'moment';
import tool from '@/utils/tool';
const rules = reactive({
    name: [{ required: true, message: '请输入规则名称', trigger: 'blur' },],
    createDate: [{ required: true, message: '请选择创建日期', trigger: 'blur' },],
})
const queryParams = ref({
    current: 1,
    size: 10,
})
const entrustparams = ref({
    current: 1,
    size: 10,
})
const entrustparams2 = ref({
    current: 1,
    size: 10,
})
const regionparams = ref({})
const busType = ref('0')
const regionDatas = ref(regionData)
const displayArea = ref(false)
const total = ref(0)
const total2 = ref(0)
const addentrustList = ref([])
const typeList = ref([])
const params4 = ref([])
const editregionList = ref([])
const regionList = ref([])
const type = ref('')
const editType = ref('')
const entrustList = ref([])
const editentrustList = ref([])
const addcustomerList = ref([])
const customerList = ref([])
const editcustomerList = ref([])
const sysAreas = ref([])
const changeentrustList = ref([])
const changecustomerList = ref([])
const salesRuleCommoditysId = ref([])
const salesRuleCustomersId = ref([])
const salesRuleAreasId = ref([])
const filtration = ref(false)
const customerId = ref([])
const entrustId = ref([])
const addForm = ref()
const editForm = ref()
const queryForm = ref()
const name = ref('')
const customerName = ref('')
const activeNames = ref(['1'])
const dialogform = reactive({})
const ruleId = ref('')
const editdialogform = reactive({})
const configeList = ref([])
const editchangeList = ref([])
const changelist3 = ref([])
const editchangeList2 = ref([])
const flag = ref(false)
const changelist = ref([])
const deleteIds = ref([])
const deleteIds2 = ref([])
const deleteIds3 = ref([])
const changelist2 = ref([])
// const changelist3 = ref([])
const editchangeentrustList = ref([])
const params1 = ref([])
const params2 = ref([])
const params3 = ref([])
const alarmName = ref('')
const id2 = ref('')
const rulerids = ref('')
const dialogFormVisible = ref(false)
const editdialogFormVisible = ref(false)
const customerVisible = ref(false)
const productVisible = ref(false)
const radio1 = ref('1')

const handleChange = (value) => {
    activeNames.value = value
}
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
//限制类型
const restriction = (value) => {
    if (value == '0') {
        displayArea.value = false
    } else {
        displayArea.value = true
        regionList.value = []
        editregionList.value = []
    }
}

//增加按钮
const handleAdd = (formEl) => {
    type.value = '0'
    filtration.value = false
    displayArea.value = false
    dialogFormVisible.value = true
    activeNames.value = '1'
    entrustList.value = []
    customerList.value = []
    regionList.value = []
    formEl.resetFields()
}
//重置
const resetQuery = (formEl) => {
    formEl.resetFields()
    getList();
}
const cityData = ref(JSON.parse(window.localStorage.getItem('cityData')))
const shi = ref()
const qu = ref()
// areas_third
const fuzhi = (str, num) => {
    if (num == 1) {
        shi.value = areas_third.find((item) => item.value == str).children
    }
    else {
        qu.value = shi.value.find(item => item.value == str).children
    }
}
const filterAddress = (province) => {
    const addressArr = []
    sysAreas.value?.forEach(x => {
        if (x?.value == province) {
            addressArr?.push(x.label)
        }
    })
    return addressArr?.toString()
}
//编辑选中商品
const editdeletecommodity = (key) => {
    editchangeentrustList.value = key
}
//编辑删除商品
const editdeletecom = () => {
    if (editchangeentrustList.value.length !== 0) {
        proxy.$confirm('是否确认删除选中商品?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            //删除商品
            const id1 = []
            deleteIds.value = []
            editchangeentrustList.value.forEach((i) => {
                id1.push(i.id)
            })
            params2.value.forEach((v) => {
                id1.forEach((i) => {
                    if (v.commodity.id == i) {
                        deleteIds.value.push(v.id)
                    }
                })
            })
            params2.value = params2.value.filter(v => v.id !== undefined)
            params2.value = params2.value.filter((item) => !(deleteIds.value.some((ele) => ele === item.id)));
            editentrustList.value = editentrustList.value.filter((item) => !(editchangeentrustList.value.some((ele) => ele.id === item.id)));
            ElMessage({
                message: "删除成功",
                type: "success",
            });
        }).catch(() => {
        })
    }
}
//编辑选中客户
const editdeletecus = (key) => {
    editchangeList.value = key
}
//编辑删除客户
const editdeletecustomer = () => {
    if (editchangeList.value.length !== 0) {
        proxy.$confirm('是否确认删除选中客户?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            const id2 = []
            deleteIds2.value = []
            editchangeList.value.forEach((i) => {
                id2.push(i.id)
            })
            params1.value.forEach((v) => {
                id2.forEach((i) => {
                    if (v.customer.id == i) {
                        deleteIds2.value.push(v.id)
                    }
                })
            })
            params1.value = params1.value.filter(v => v.id !== undefined)
            params1.value = params1.value.filter((item) => !(deleteIds2.value.some((ele) => ele === item.id)));
            editcustomerList.value = editcustomerList.value.filter((item) => !(editchangeList.value.some((ele) => ele.id === item.id)));
            ElMessage({
                message: "删除成功",
                type: "success",
            });
        }).catch(() => {
        })
    }
}
//新增选中商品
const changeEntru = (v) => {
    changelist.value = v
}
//新增删除商品
const deleteEntrust = () => {
    if (changelist.value.length !== 0) {
        proxy.$confirm('是否确认删除选中商品?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            entrustList.value = entrustList.value.filter((item) => !(changelist.value.some((ele) => ele.id === item.id)));
            ElMessage({
                message: "删除成功",
                type: "success",
            });
        }).catch(() => {
        })
    }
}
//新增选中客户
const changeCustom = (v) => {
    changelist2.value = v
}
//新增删除客户
const deleteCustomers = () => {
    if (changelist2.value.length !== 0) {
        proxy.$confirm('是否确认删除选中客户?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            customerList.value = customerList.value.filter((item) => !(changelist2.value.some((ele) => ele.id === item.id)));
            ElMessage({
                message: "删除成功",
                type: "success",
            });
        }).catch(() => {
        })
    }
}
//新增选中区域
const changeAre = (v) => {
    changelist3.value = v
}
//新增删除区域
const deleteAreas = () => {
    if (changelist3.value.length !== 0) {
        proxy.$confirm('是否确认删除选中区域?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            regionList.value = regionList.value.filter((item) => !(changelist3.value.some((ele) => ele.id === item.id)));
            ElMessage({
                message: "删除成功",
                type: "success",
            });
        }).catch(() => {
        })
    }
}
//编辑选中区域
const editdeleteare = (key) => {
    editchangeList2.value = key
}
//编辑删除区域
const editdaletepro = () => {
    if (editchangeList2.value.length !== 0) {
        proxy.$confirm('是否确认删除选中区域?', '提示', {
            type: 'warning',
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            deleteIds3.value = []
            params3.value.forEach((v) => {
                editchangeList2.value.forEach((i) => {
                    if (v.provincial == i.proValue) {
                        deleteIds3.value.push(v.id)
                    }
                })
            })
            params3.value = params3.value.filter((item) => !(deleteIds3.value.some((ele) => ele === item.id)));
            editregionList.value = editregionList.value.filter((item) => !(editchangeList2.value.some((ele) => ele.proValue === item.proValue)));
            ElMessage({
                message: "删除成功",
                type: "success",
            });
        }).catch(() => {
        })
    }
}
//商品列表
const productAdd = () => {
    productVisible.value = true
    const params = {
        ...entrustparams.value
    }
    blockingRule.productlist(params).then(res => {
        if (res.code == 200) {
            addentrustList.value = res.data.records
            total2.value = res.data.total
        }
    })
}
const product = () => {
    const params = {
        current: 1,
        size: 10000000,
    }
    blockingRule.productlist().then(res => {
        if (res.code == 200) {
            addentrustList.value = res.data.records
        }
    })
}
product()
//客户列表
const customerAdd = () => {
    customerVisible.value = true
    const params = {
        ...entrustparams2.value
    }
    blockingRule.customerList(params).then(res => {
        if (res.code == 200) {
            addcustomerList.value = res.data.records
            total2.value = res.data.total
        }
    })
}
const custome = () => {
    const params = {
        current: 1,
        size: 10000000,
    }
    blockingRule.customerList().then(res => {
        if (res.code == 200) {
            addcustomerList.value = res.data.records
        }
    })
}
custome()
// customerAdd
//商品弹框选中数据
const addentrustListChange = (key) => {
    changeentrustList.value = key
}
// 商品弹框确定按钮
const pushEntrust = () => {
    if (filtration.value == false) {
        const ids = []
        entrustList.value.forEach(x => ids.push(x.id))
        changeentrustList.value.forEach((v) => {
            entrustId.value.push(v.id)
            if (ids.includes(v.id)) {
                proxy.msgError('检测到商品名称为' + v.tradeName + '已添加，已将该条数据过滤！')
            } else if (entrustList.value.length > 49) {
                proxy.msgError('商品添加上限为50个！')

            } else {
                entrustList.value.push(v)
            }
        })
    }
    if (filtration.value == true) {
        const ids = []
        editentrustList.value.forEach(x => ids.push(x.id))
        changeentrustList.value.forEach((v) => {
            // entrustId.value.push(v.id)
            if (ids.includes(v.id)) {
                proxy.msgError('检测到商品名称为' + v.tradeName + '已添加，已将该条数据过滤！')
            } else if (entrustList.value.length > 49) {
                proxy.msgError('商品添加上限为50个！')

            } else {
                editentrustList.value.push(v)
                params2.value.push({
                    salesRule: { id: id2.value },
                    commodity: { id: v.id }
                })
            }
        })
    }
    productVisible.value = false
}
//客户弹框选中数据
const addcustomerListChange = (key) => {
    changecustomerList.value = key
}
// 客户弹框确定按钮
const pushcustomer = () => {
    if (filtration.value == false) {
        const ids = []
        customerList.value.forEach(x => ids.push(x.id))
        changecustomerList.value.forEach((v) => {
            customerId.value.push(v.id)
            if (ids.includes(v.id)) {
                proxy.msgError('检测到客户名称为' + v.enterpriseName + '已添加，已将该条数据过滤！')
            } else if (customerList.value.length > 49) {
                proxy.msgError('客户添加上限为50个！')

            } else {
                customerList.value.push(v)
            }
        })
    }
    if (filtration.value == true) {
        const ids = []
        editcustomerList.value.forEach(x => ids.push(x.id))
        changecustomerList.value.forEach((v) => {
            // customerId.value.push(v.id)
            if (ids.includes(v.id)) {
                proxy.msgError('检测到客户名称为' + v.enterpriseName + '已添加，已将该条数据过滤！')
            } else if (editcustomerList.value.length > 49) {
                proxy.msgError('客户添加上限为50个！')

            } else {
                editcustomerList.value.push(v)
                params1.value.push({
                    salesRule: { id: id2.value },
                    customer: { id: v.id }
                })
            }
        })
    }
    customerVisible.value = false
}
//商品搜索
const searchEntrust = () => {
    productAdd()
}
//客户搜索
const searchCustomer = () => {
    customerAdd()
}
//编辑按钮
const handleEdit = (row, status) => {
    filtration.value = true
    displayArea.value = false
    deleteIds.value = []
    deleteIds2.value = []
    deleteIds3.value = []
    editdialogFormVisible.value = true
    editentrustList.value = []
    editcustomerList.value = []
    editregionList.value = []
    params4.value = []
    activeNames.value = '1'
    id2.value = row.id
    blockingRule.details({ salesRuleId: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.name = res.data.salesRule.name
            rulerids.value = res.data.salesRule.id
            radio1.value = res.data.salesRule.busType
            let timeArr = []
            timeArr.push(new Date(res.data.salesRule.beginValidDate))
            timeArr.push(new Date(res.data.salesRule.endValidDate))
            editdialogform.createDate = timeArr
            params2.value = res.data.salesRuleCommoditys
            res.data.salesRuleCommoditys.forEach((v) => {
                salesRuleCommoditysId.value.push(
                    {
                        id: { id: v.id },
                    }
                )
                addentrustList.value.forEach((i) => {
                    if (v.commodity.id == i.id) {
                        editentrustList.value.push(i)
                    }
                })
            })
            params1.value = res.data.salesRuleCustomers
            res.data.salesRuleCustomers.forEach((v) => {
                salesRuleCustomersId.value.push(
                    {
                        id: { id: v.id },
                    }
                )
                addcustomerList.value.forEach((i) => {
                    if (v.customer.id == i.id) {
                        editcustomerList.value.push(i)
                    }
                })
            })
            params3.value = res.data.salesRuleAreas
            if (res.data.salesRuleAreas.length == 0) {
                editType.value = '0'
            } else {
                res.data.salesRuleAreas.forEach((v) => {
                    if (v.limitType == '1' || v.limitType == '2') {
                        displayArea.value = true
                        editType.value = v.limitType
                    }
                    salesRuleAreasId.value.push(v.id)
                    let pro = ''
                    let cit = ''
                    let are = ''
                    let proValue = ''
                    // let id5 = ''
                    areas_third.forEach((i) => {
                        if (v.provincial == i.value) {
                            pro = i.label
                            proValue = i.value
                            if (v.city !== undefined) {
                                i.children?.forEach((x) => {
                                    if (v.city == x.value) {
                                        cit = x.label
                                        x.children?.forEach((c) => {
                                            if (v.area == c.value) {
                                                are = c.label
                                            }
                                        })
                                    }
                                })
                            }
                        }
                    })

                    if (v.city !== undefined) {
                        shi.value = areas_third.find((item) => item.label == pro).children
                        qu.value = shi.value.find(item => item.label == cit).children
                    }
                    editregionList.value.push({
                        provinces: pro,
                        city: cit,
                        area: are,
                        proValue: proValue
                        // id: id5
                    })
                })
            }

        }
    })
}
//编辑请求
const editAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            // 删除商品
            if (deleteIds.value.length !== 0) {
                blockingRule.deleteCommoditys({ ruleCommodityIds: deleteIds.value.toString() })
            }
            // //删除客户
            if (deleteIds2.value.length !== 0) {
                blockingRule.deleteCustomers({ ruleCustomerIds: deleteIds2.value.toString() })
            }

            // 删除区域
            if (deleteIds3.value.length !== 0) {
                blockingRule.deleteAreas({ ruleAreaIds: deleteIds3.value.toString() })
            }
            const params = {
                id: rulerids.value,
                ...editdialogform
            }
            if (params?.createDate?.length) {
                params.beginValidDate = moment(params?.createDate[0]).format('YYYY-MM-DD HH:mm:ss')
                params.endValidDate = moment(params?.createDate[1]).format('YYYY-MM-DD HH:mm:ss')
                delete params.createDate
            }
            params.busType = radio1.value
            const save = async () => {
                await blockingRule.saveRule(params)
                await blockingRule.saveCommoditys(params2.value)
                await blockingRule.saveCustomers(params1.value)
                editregionList.value.forEach((v) => {
                    if (v.id) {
                        params3.value.push({
                            salesRule: { id: id2.value },
                            provincial: v.provinces,
                            city: v.city,
                            area: v.area,
                            limitType: editType.value
                        })
                    }
                })
                params3.value.forEach((i) => {
                    if (i.limitType) {
                        i.limitType = editType.value
                    }

                })
                await blockingRule.saveAreas(params3.value)
                editdialogFormVisible.value = false
                ElMessage({
                    message: "修改成功",
                    type: "success",
                });
                getList();
            }
            save()
        }
    });
}
//搜索
function searchQuery() {
    getList();
}
//拦截规则列表
function getList() {
    const params = {
        ...queryParams.value,
    }
    blockingRule.list(params).then(res => {
        if (res.code == 200) {
            configeList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
//删除
function handleDelete(row) {
    proxy.$confirm('是否确认删除此拦截规则?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        blockingRule.delete({ salesRuleIds: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
//新增拦截规则
const saveAlarm = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            const params = {
                ...dialogform
            }
            if (params?.createDate?.length) {
                params.beginValidDate = moment(params?.createDate[0]).format('YYYY-MM-DD HH:mm:ss')
                params.endValidDate = moment(params?.createDate[1]).format('YYYY-MM-DD HH:mm:ss')
                delete params.createDate
            }
            params.busType = busType.value
            // params.limitType = type.value
            const save = async () => {
                await blockingRule.saveRule(params).then(res => {
                    if (res.code == 200) {
                        ruleId.value = res.data.id
                    }
                })
                const commoditysParams = []
                entrustId.value.forEach((v) => {
                    commoditysParams.push({
                        salesRule: { id: ruleId.value },
                        commodity: { id: v }
                    })
                })
                await blockingRule.saveCommoditys(commoditysParams)
                const customerParams = []
                customerId.value.forEach((v) => {
                    customerParams.push({
                        salesRule: { id: ruleId.value },
                        customer: { id: v }
                    })
                })
                await blockingRule.saveCustomers(customerParams)
                const arearParams = []
                regionList.value.forEach((v) => {
                    arearParams.push({
                        salesRule: { id: ruleId.value },
                        limitType: type.value,
                        provincial: v.provinces,
                        city: v.city,
                        area: v.area
                    })
                })
                await blockingRule.saveAreas(arearParams)
                dialogFormVisible.value = false
                ElMessage({
                    message: "保存成功",
                    type: "success",
                });
                getList();
            }
            let arr = []
            regionList.value.forEach((v) => {
                arr.push(v.provinces)
            })
            arr.forEach((i) => {
                if (i == undefined) {
                    flag.value = true
                } else {
                    flag.value = false
                }
            })
            if (customerList.value.length == 0 && busType.value == '0' && type.value == '0') {
                proxy.msgError('请至少填写一个拦截规则，否则不允许保存')
            }
            else if (type.value !== '0') {
                if (regionList.value.length == 0 || flag.value == true) {
                    proxy.msgError('指定区域省份不能为空，请修改后提交')
                } else {
                    save()
                }
            } else {
                save()
            }
        }
    });
}
//区域添加按钮
const regionAdd = () => {
    regionList.value.push({ id: uuid.v1() })
    editregionList.value.push({ id: uuid.v1() })
}
//字典请求
const getDict = async () => {
    alarmName.value = tool.data.get("USER_INFO")?.name
    typeList.value = await proxy.getDictList('restriction_type')
    //省份数据
    sysAreas.value = areas_third.map(v => {
        return { value: v.value, label: v.label }
    })
}
getDict()
</script>

<style lang="scss" scoped>
::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.el-divider {
    height: 20px;
    margin-top: 0px;
    margin-bottom: 5px;
}

.dialog-footer {
    margin-top: -30px;
}

::v-deep .el-divider--horizontal {
    border-top: 1px dashed #686666;
}
</style>