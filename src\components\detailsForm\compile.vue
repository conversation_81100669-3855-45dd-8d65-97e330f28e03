<template>
  <div>
    <el-dialog v-model="dialogVisible" title="查看详情" width="80%">
      <h4 v-if="data.editStr" slot="title" class="stateTitle">
        {{ data.editStr == null ? "" : data.editStr.state }}
      </h4>
      <div class="demo-collapse">
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item name="1">
            <template #title>
              <span class="col_title">基本信息</span>
            </template>
            <table border="0" cellpadding="0" cellspacing="1" class="messTable">
              <tr>
                <td>通用名</td>
                <td>{{ formInline.n1 }}</td>
              </tr>
              <tr>
                <td>商品名</td>
                <td>{{ formInline.n2 }}</td>
              </tr>
              <tr>
                <td>拼音码</td>
                <td>{{ formInline.n3 }}</td>
              </tr>
              <tr>
                <td>自编码</td>
                <td>{{ formInline.n4 }}</td>
              </tr>
              <tr>
                <td>商品编码</td>
                <td>{{ formInline.n17 }}</td>
              </tr>
              <tr>
                <td>基本单位</td>
                <td>{{ echoType(data.basicType, formInline.n5, 'name', 'name') }}</td>
              </tr>
              <tr>
                <td>整件单位</td>
                <td>{{ echoType(data.basicType, formInline.n6, 'name', 'name') }}</td>
              </tr>
              <tr>
                <td>装箱规格</td>
                <td>{{ formInline.n7 }}</td>
              </tr>
              <tr>
                <td>包装规格</td>
                <td>{{ formInline.n10 }}</td>
              </tr>
              <tr>
                <td>生产厂家</td>
                <td>{{ formInline.n12 }}</td>
              </tr>
              <tr>
                <td>生产地址</td>
                <td>{{ echoType(data.siteType, formInline.n13, 'licenseAddress', 'licenseAddress') }}
                </td>
              </tr>
              <tr v-if="data.types != '6'">
                <td>{{ echoTitle() }}</td>
                <td>{{ echoType(data.drugType, formInline.n9, 'valueName', 'valueName') }}</td>
              </tr>
              <tr>
                <td>有效期(月)</td>
                <td>{{ formInline.n8 }}</td>
              </tr>
              <tr>
                <td>经营范围</td>
                <td>{{
                    echoType(data.treesType, formInline.n14, 'id', 'massName')
                  }}
                </td>
              </tr>
              <tr>
                <td>上市许可持有人</td>
                <td>{{ formInline.n11 }}</td>
              </tr>
              <tr>
                <td>产地</td>
                <td>{{ formInline.n15 }}</td>
              </tr>
              <tr>
                <td>备注</td>
                <td>{{ formInline.n16 }}</td>
              </tr>
              <tr v-if="data.types != '6'" style="border-left: 1px solid #e9ecf1"></tr>
              <tr v-if="data.types != '6'" style="border-left: 1px solid #e9ecf1"></tr>
              <tr v-if="data.types != '6'" style="border-left: 1px solid #e9ecf1"></tr>
            </table>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template #title>
              <span class="col_title">质量信息</span>
            </template>
            <table border="0" cellpadding="0" cellspacing="1" class="messTable">
              <tr>
                <td>GSP属性</td>
                <td>{{ echoType(data.GSPType, formInline2.n1, 'valueName', 'valueName') }}</td>
              </tr>
              <tr v-if="data.types == '7'">
                <td>生产许可证号</td>
                <td>{{ formInline2.n24 }}</td>
              </tr>
              <tr v-if="data.types == '7'">
                <td>许可证号有效期</td>
                <td>{{ functionIndex.transformTimestamp(formInline2.n25) }}</td>
              </tr>
              <tr v-if="data.types == '6'">
                <td>类别</td>
                <td>{{ echoType(data.manageType, formInline2.n15, 'name', 'name') }}</td>
              </tr>
              <tr v-if="data.types == '6'">
                <td>商品分类</td>
                <td>{{ echoType(data.goodsType, formInline2.n16, 'name', 'name') }}</td>
              </tr>
              <tr v-if="data.types == '6'">
                <td>阳光采购码</td>
                <td>{{ formInline2.n18 }}</td>
              </tr>
              <tr v-if="data.types == '6'">
                <td>质管分类</td>
                <td>{{ echoType(data.conType, formInline2.n17, 'name', 'name') }}</td>
              </tr>
              <tr v-if="data.types == 5">
                <td>注册/备案号</td>
                <td>{{ echoType(data.ratifyType, formInline2.n24, 'valueName', 'valueName') }}</td>
              </tr>
              <tr v-if="data.types == 5">
                <td>注册/备案有效期</td>
                <td>{{ functionIndex.transformTimestamp(formInline2.n25) }}</td>
              </tr>
              <tr v-if="data.types == 4">
                <td>批准文号</td>
                <td>
                  {{
                    echoType(data.ratifyType, formInline2.n2, 'valueName', 'valueName') + ' ' + formInline2.n26
                  }}
                </td>
              </tr>
              <tr v-if="data.types == 4">
                <td>批准文号有效期</td>
                <td>{{ functionIndex.transformTimestamp(formInline2.n3) }}</td>
              </tr>
              <tr v-if="data.types == 4">
                <td>处方类型</td>
                <td>{{ echoType(data.recipeType, formInline2.n4, 'valueName', 'valueName') }}</td>
              </tr>
              <tr v-if="data.types == 4">
                <td>注册批件号</td>
                <td>{{ formInline2.n5 }}</td>
              </tr>
              <tr>
                <td>质量标准</td>
                <td>{{ formInline2.n6 }}</td>
              </tr>
              <tr v-if="data.types == 4 || data.types == 6 || data.types == 7">
                <td>养护类型</td>
                <td>{{ echoType(data.maintainType, formInline2.n7, 'name', 'name') }}</td>
              </tr>
              <tr v-if="data.types == '6'">
                <td>注册证号</td>
                <td>{{ formInline2.n19 }}</td>
              </tr>
              <tr v-if="data.types == '6'">
                <td>注册证发证日期</td>
                <td>{{ functionIndex.transformTimestamp(formInline2.n20) }}</td>
              </tr>
              <tr v-if="data.types == '6'">
                <td>注册证有效期</td>
                <td>{{ functionIndex.transformTimestamp(formInline2.n21) }}</td>
              </tr>
              <tr>
                <td>贮藏温区</td>
                <td>{{ echoType(data.storeType, formInline2.n8, 'name', 'name') }}</td>
              </tr>
              <tr>
                <td>贮藏温区范围</td>
                <td>{{ echoType(data.warmType, formInline2.n9, 'name', 'name') }}</td>
              </tr>
              <tr>
                <td>运输温区</td>
                <td>{{ echoType(data.storeType, formInline2.n10, 'name', 'name') }}</td>
              </tr>
              <tr>
                <td>运输温度范围</td>
                <td>{{ echoType(data.warmType, formInline2.n11, 'name', 'name') }}</td>
              </tr>
              <tr v-if="data.types == '4'" style="border-left: 1px solid #e9ecf1"></tr>
              <tr v-if="data.types == '6'" style="border-left: 1px solid #e9ecf1"></tr>
              <tr v-if="data.types == '6'" style="border-left: 1px solid #e9ecf1"></tr>
              <tr v-if="data.types == '7'" style="border-left: 1px solid #e9ecf1"></tr>
              <tr v-if="data.types == '7'" style="border-left: 1px solid #e9ecf1"></tr>
              <tr v-if="data.types == '7'" style="border-left: 1px solid #e9ecf1"></tr>
            </table>
            <div style="margin-top: 20px">
              <el-checkbox v-if="data.types == '6'" v-model="formInline2.n22" :disabled="true" label="唯一标识码"/>
              <el-checkbox v-model="formInline2.n12" :disabled="true" label="首营品种"/>
              <el-checkbox v-model="formInline2.n13" :disabled="true" label="特药控制"/>
              <el-checkbox v-model="formInline2.n14" :disabled="true" label="特殊温区"/>
              <el-checkbox v-model="formInline2.n23" :disabled="true" label="电子监控"/>
              <el-checkbox v-if="data.types == '5'" v-model="formInline2.n27" :disabled="true" label="进口标准"/>
            </div>
          </el-collapse-item>
          <el-collapse-item name="3">
            <template #title>
              <span class="col_title">经营信息</span>
            </template>
            <table border="0" cellpadding="0" cellspacing="1" class="messTable">
              <tr>
                <td>税率</td>
                <td>{{ formInline3.n1 }}</td>
              </tr>
              <tr>
                <td>税务分类编码</td>
                <td>{{ formInline3.n2 }}</td>
              </tr>
              <tr>
                <td>疗程数量</td>
                <td>{{ formInline3.n3 }}</td>
              </tr>
              <tr>
                <td>用药分类</td>
                <td>{{ formInline3.n4 }}</td>
              </tr>
              <tr>
                <td>使用天数</td>
                <td>{{ formInline3.n5 }}</td>
              </tr>
              <tr>
                <td>医保分类</td>
                <td>{{ echoType(data.healthType, formInline3.n6, 'name', 'name') }}</td>
              </tr>
              <tr>
                <td>用法用量</td>
                <td>{{ formInline3.n7 }}</td>
              </tr>
              <tr>
                <td>禁忌</td>
                <td>{{ formInline3.n8 }}</td>
              </tr>
              <tr>
                <td>主要成分</td>
                <td>{{ formInline3.n9 }}</td>
              </tr>
              <tr>
                <td>特殊存储要求</td>
                <td>{{ formInline3.n10 }}</td>
              </tr>
              <tr>
                <td>功能主治</td>
                <td>{{ formInline3.n11 }}</td>
              </tr>
              <tr>
                <td>适应征状</td>
                <td>{{ formInline3.n12 }}</td>
              </tr>
            </table>
            <div style="margin-top: 20px">
              <el-checkbox v-model="formInline3.n13" :disabled="true" label="基本药物"/>
              <el-checkbox v-model="formInline3.n14" :disabled="true" label="注册商标"/>
              <el-checkbox v-model="formInline3.n15" :disabled="true" label="会员积分"/>
              <el-checkbox v-model="formInline3.n16" :disabled="true" label="价格保护"/>
              <el-checkbox v-model="formInline3.n17" :disabled="true" label="贵细药品"/>
              <el-checkbox v-model="formInline3.n18" :disabled="true" label="网络销售"/>
              <el-checkbox v-model="formInline3.n19" :disabled="true" label="独占货位"/>
              <el-checkbox v-model="formInline3.n20" :disabled="true" label="是否易碎"/>
            </div>
          </el-collapse-item>
          <el-collapse-item name="4">
            <template #title>
              <span class="col_title">附件上传</span>
            </template>
            <el-table :cell-style="{ textAlign: 'center' }" :data="formInline4.table"
                      :header-cell-style="{ 'text-align': 'center' }" border style="width: 100%">
              <el-table-column label="序号" prop="n1" width="50">
                <template #default="scope">
                  {{ scope.row.n1 }}
                </template>
              </el-table-column>
              <el-table-column label="类型" prop="n2" width="150">
                <template #default="scope">
                  {{ echo1(scope.row.n2) }}
                </template>
              </el-table-column>
              <el-table-column label="文件类型" prop="n3" width="250">
                <template #default="scope">
                  {{ echo2(scope.row.n3) }}
                </template>
              </el-table-column>
              <el-table-column label="文件名称" prop="n4" width="400">
                <template #default="scope">
                  <div v-if="scope.row.n4.file.length > 0">
                    <div v-for="(item, index) in scope.row.n4.file" :key="index"
                         style="display: flex;justify-content: center">
                      <el-tooltip :content="item.name" class="box-item" effect="dark" placement="top">
                        <p style="text-overflow: ellipsis;overflow: hidden;word-break: break-all;line-height: 30px;white-space: nowrap;font-size:12px;color: #2a76f8;cursor: pointer;"
                           @click="checkFile(item)">
                          {{ item.name }}</p>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否必传" prop="n5" width="100">
                <template #default="scope">
                  {{ scope.row.n5 == 0 ? '否' : '是' }}
                </template>
              </el-table-column>
              <el-table-column label="是否多张" prop="n6" width="100">
                <template #default="scope">
                  {{ scope.row.n6 == 0 ? '否' : '是' }}
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="300" prop="n7">
                <template #default="scope">
                  <p style="width: 100%;min-width:100px">
                    {{ scope.row.n7.str }}&emsp;
                  </p>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item v-if="!logFlag" name="5">
            <template #title>
              <span class="col_title">操作日志</span>
            </template>
            <LogQuery ref="logQueryRef"/>
          </el-collapse-item>
          <el-collapse-item v-if="!data.formFlag" name="6">
            <template #title>
              <span class="col_title">审批意见</span>
            </template>
            <auditForms ref="auditRef" @refresh="refresh"/>
          </el-collapse-item>
        </el-collapse>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false"> 取消 </el-button>
          <el-button v-if="!data.formFlag" type="primary" @click="allRight()">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import {drugApi} from "@/api/model/commodity/drug";
import {ElLoading} from "element-plus";
import {foodApi} from "@/api/model/commodity/food";
import {applianceApi} from "@/api/model/commodity/appliance";
import {disappApi} from "@/api/model/commodity/disappear";
import LogQuery from "@/components/detailsForm/logQuery.vue";
import {typeList} from "@/views/commodity/indexApi";
import {functionIndex} from "../../views/commodity/functionIndex";

const dialogVisible = ref(false);
const activeNames = ref(["1", '2', '3', '4', '5', '6']);

const auditRef = ref(null);
const logQueryRef = ref(null);
const {proxy} = getCurrentInstance();
//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const data = reactive({
  formFlag: false,
  pageNum: 1,
  treesType: null,
  ids: "",
  title: "",
  types: "",
  checkFlag: false,
  imgUrl: [],
  status: "",
  pageSize: 10,
  state: false,
  editStr: null,
  total: 0,
  basicType: null,
  wholeType: null,
  drugType: null,
  yieldType: null,
  GSPType: null,
  ratifyType: null,
  recipeType: null,
  maintainType: null,
  storeType: null,
  warmType: null,
  healthType: null,
  fileType: null,
  siteType: null,
  manageType: null,
  goodsType: null,
  conType: null,
  venderDrug: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
    records: null,
    value: "",
  },
});
const emit = defineEmits(["refresh"]);
const formInline = reactive({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
  n5: "",
  n6: "",
  n7: "",
  n8: "",
  n9: "",
  n10: "",
  n11: "",
  n12: "",
  n13: "",
  n14: [],
  n15: "",
  n16: "",
  n17: "",
  n18: "",
  n19: "",
  n20: "",
  n21: "",
  n22: "",
  n23: "",
  n24: "",
  n25: "",
  n26: "",
  n27: "",
});
const formInline2 = reactive({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
  n5: "",
  n6: "",
  n7: "",
  n8: "",
  n9: "",
  n10: "",
  n11: "",
  n12: "",
  n13: "",
  n14: "",
  n15: "",
  n16: "",
  n17: "",
  n18: "",
  n19: "",
  n20: "",
  n21: "",
  n22: "",
  n23: "",
  n24: "",
  n25: "",
});
const formInline3 = reactive({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
  n5: "",
  n6: "",
  n7: "",
  n8: "",
  n9: "",
  n10: "",
  n11: "",
  n12: "",
  n13: "",
  n14: "",
  n15: "",
  n16: "",
  n17: "",
  n18: "",
  n19: "",
  n20: "",
  n21: "",
  n22: "",
  n23: "",
  n24: "",
  n25: "",
});
const formInline4 = reactive({
  table: [],
  delAll: [],
});
const close = () => {
  data.checkFlag = false
  data.imgUrl = []
}
const checkFile = (item) => {
  data.imgUrl = []
  data.checkFlag = true;
  data.imgUrl.push(item.url)
}
const allRight = () => {
  auditRef.value.formSub(data.ids);
};
const refresh = () => {
  dialogVisible.value = false;
  emit("refresh");
};
const logFlag = ref(false)
const details = (type, id, rowId, mess, log) => {
  data.types = type;
  data.ids = rowId;
  if (mess) {
    data.formFlag = true
  } else {
    data.formFlag = false
  }
  ElLoading.service();
  const loadingInstance = ElLoading.service();
  if (log) {
    logFlag.value = true
  } else {
    logFlag.value = false
    drugApi
        .journalList({
          "commodity.id": id,
        })
        .then((res) => {
          drugApi
              .drugLog({
                masterId: id,
              })
              .then((resg) => {
                setTimeout(() => {
                  logQueryRef.value.timeFns(
                      res.data.records,
                      resg.data.records
                  );
                }, 500);
              });
        });
  }
  if (type == 4) {
    drugApi
        .searchDrug({
          id: id,
        })
        .then((res) => {
          if (res.code == 200) {
            let fileArr = [];
            let row = res.data.erpDrugsCommodityDTO.commodity;
            data.editStr = res.data;
            data.editStr.state = `状态: ` + echoStatus(row.status)

            formInline2.n2 =
                res.data.erpDrugsCommodityDTO.approvalNumber;
            formInline2.n4 =
                res.data.erpDrugsCommodityDTO.proscriptionType;
            formInline2.n3 =
                res.data.erpDrugsCommodityDTO.approvalValidity;
            formInline2.n5 =
                res.data.erpDrugsCommodityDTO.registrationNo;
            formInline2.n26 = res.data.erpDrugsCommodityDTO.approvalNumberSet
            formInline.n12 = row.manufacture.enterpriseName;
            formInline.n17 = row.commodityCode;
            formInline.n3 = row.pinyinCode;
            formInline.n2 = row.tradeName;
            formInline.n1 = row.commonName;
            formInline2.n16 = row.grugsType;
            formInline.n9 = row.dosageForm;
            formInline.n10 = row.packageSpecification;
            formInline.n8 = row.validityTime;
            formInline.n13 = row.originPlace;
            formInline.n15 = row.producingArea;
            formInline.n11 = row.listPermitHolder;
            formInline.n5 = row.basicUnit;
            formInline2.n1 = row.gspAttribute;
            formInline.n14 = row.businessScope;
            formInline2.n6 = row.qualityStandard;
            formInline2.n13 =
                row.specialMedicineControl == "1" ? true : false;
            formInline2.n12 = row.premiereVariety == "1" ? true : false;
            formInline2.n7 = row.curingType;
            formInline2.n8 = row.storageTemperature;
            formInline2.n9 = row.storageRange;
            formInline2.n10 = row.transportTemperature;
            formInline2.n11 = row.transportTemperatureRange;
            formInline2.n14 =
                row.specialTemperature == "1" ? true : false;
            formInline2.n23 =
                row.electronicSupervision == "1" ? true : false;
            formInline.n16 = row.remark;
            formInline.n4 = row.commoditySelfCode;
            formInline.n6 = row.completeUnit;
            formInline.n7 = row.ratio;
            formInline3.n1 = row.taxRate;
            formInline3.n2 = row.taxClassifyCode;
            formInline3.n3 = row.treatmentNumber;
            formInline3.n4 = row.medicationClassify;
            formInline3.n5 = row.usageDays;
            formInline3.n6 = row.medicalInsuranceClassify;
            formInline3.n7 = row.usageDosage;
            formInline3.n8 = row.taboo;
            formInline3.n9 = row.mainComponents;
            formInline3.n10 = row.specialStorage;
            formInline3.n11 = row.functionalIndications;
            formInline3.n12 = row.adaptiveSymptoms;
            formInline3.n13 =
                row.essentialMedicines == "1" ? true : false;
            formInline3.n14 =
                row.registeredTrademark == "1" ? true : false;
            formInline3.n15 = row.memberPoint == "1" ? true : false;
            formInline3.n17 =
                row.preciousMedicines == "1" ? true : false;
            formInline3.n18 = row.onlineSales == "1" ? true : false;
            formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
            formInline3.n20 = row.isFragile == "1" ? true : false;
            formInline3.n16 = row.priceProtection == "1" ? true : false;
            formInline2.n22 = row.onlyCode == "1" ? true : false; //
            dialogVisible.value = true;

            res.data.annexFileDTOS.forEach((item, index) => {
              item.files.forEach(item => {
                item.name = item.filesName
                item.url = item.filesUrl
                item.id = item.filesId
              })
              fileArr.push({
                id: item.id,
                n1: index + 1,
                n2: item.smallType,
                n3: item.categoryName,
                n4: {
                  file: item.files
                },
                n5: item.isUpload,
                n6: item.isMultiPage,
                n7: {
                  str: item.remark,
                  flag: false
                }
              });
            });
            formInline4.table = fileArr;
          }
          loadingInstance.close();
        });
  } else if (type == 5) {
    foodApi
        .searchDrug({
          id: id,
        })
        .then((res) => {
          if (res.code == 200) {
            let row = res.data.erpFoodCommodityDTO.commodity;
            let fileArr = [];
            data.editStr = res.data;
            data.editStr.state = `状态: ` + echoStatus(row.status)
            formInline2.n24 =
                res.data.erpFoodCommodityDTO.registerLicense;
            formInline2.n25 =
                res.data.erpFoodCommodityDTO.registerLicenseDate;
            formInline.n12 = row.manufacture.enterpriseName;

            formInline2.n27 = row.importMark == "1" ? true : false;
            formInline.n17 = row.commodityCode;
            formInline.n3 = row.pinyinCode;
            formInline.n2 = row.tradeName;
            formInline.n1 = row.commonName;
            formInline2.n16 = row.grugsType;
            formInline.n9 = row.dosageForm;
            formInline.n10 = row.packageSpecification;
            formInline.n8 = row.validityTime;
            formInline.n13 = row.originPlace;
            formInline.n15 = row.producingArea;
            formInline.n11 = row.listPermitHolder;
            formInline.n5 = row.basicUnit;
            formInline2.n1 = row.gspAttribute;
            formInline.n14 = row.businessScope;
            formInline2.n6 = row.qualityStandard;
            formInline2.n13 =
                row.specialMedicineControl == "1" ? true : false;
            formInline2.n12 = row.premiereVariety == "1" ? true : false;
            formInline2.n7 = row.curingType;
            formInline2.n8 = row.storageTemperature;
            formInline2.n9 = row.storageRange;
            formInline2.n10 = row.transportTemperature;
            formInline2.n11 = row.transportTemperatureRange;
            formInline2.n14 =
                row.specialTemperature == "1" ? true : false;
            formInline2.n23 =
                row.electronicSupervision == "1" ? true : false;
            formInline.n16 = row.remark;
            formInline.n4 = row.commoditySelfCode;
            formInline.n6 = row.completeUnit;
            formInline.n7 = row.ratio;
            formInline3.n1 = row.taxRate;
            formInline3.n2 = row.taxClassifyCode;
            formInline3.n3 = row.treatmentNumber;
            formInline3.n4 = row.medicationClassify;
            formInline3.n5 = row.usageDays;
            formInline3.n6 = row.medicalInsuranceClassify;
            formInline3.n7 = row.usageDosage;
            formInline3.n8 = row.taboo;
            formInline3.n9 = row.mainComponents;
            formInline3.n10 = row.specialStorage;
            formInline3.n11 = row.functionalIndications;
            formInline3.n12 = row.adaptiveSymptoms;
            formInline3.n13 =
                row.essentialMedicines == "1" ? true : false;
            formInline3.n14 =
                row.registeredTrademark == "1" ? true : false;
            formInline3.n15 = row.memberPoint == "1" ? true : false;
            formInline3.n17 =
                row.preciousMedicines == "1" ? true : false;
            formInline3.n18 = row.onlineSales == "1" ? true : false;
            formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
            formInline3.n20 = row.isFragile == "1" ? true : false;
            formInline3.n16 = row.priceProtection == "1" ? true : false;
            formInline2.n22 = row.onlyCode == "1" ? true : false; //
            formInline2.n5 = row.registrationNo;
            formInline2.n2 = row.approvalNumber;
            formInline2.n3 = row.approvalValidity;
            formInline2.n4 = row.proscriptionType;
            dialogVisible.value = true;

            res.data.annexFileDTOS.forEach((item, index) => {
              item.files.forEach(item => {
                item.name = item.filesName
                item.url = item.filesUrl
                item.id = item.filesId
              })
              fileArr.push({
                id: item.id,
                n1: index + 1,
                n2: item.smallType,
                n3: item.categoryName,
                n4: {
                  file: item.files
                },
                n5: item.isUpload,
                n6: item.isMultiPage,
                n7: {
                  str: item.remark,
                  flag: false
                }
              });
            });
            formInline4.table = fileArr;
          }
          loadingInstance.close();
        });
  } else if (type == 6) {
    applianceApi
        .searchDrug({
          id: id,
        })
        .then((res) => {
          if (res.code == 200) {
            let row = res.data.erpApparatusCommodityDTO.commodity;
            let fileArr = [];
            data.editStr = res.data;
            data.editStr.state = `状态: ` + echoStatus(row.status)
            formInline2.n19 =
                res.data.erpApparatusCommodityDTO.registrationCode; //
            formInline2.n20 =
                res.data.erpApparatusCommodityDTO.registrationStart; //
            formInline2.n21 =
                res.data.erpApparatusCommodityDTO.registrationOverdue; //
            formInline2.n15 =
                res.data.erpApparatusCommodityDTO.manageFamily; //
            formInline2.n18 =
                res.data.erpApparatusCommodityDTO.sunshinePurchaseCode; //
            formInline2.n17 =
                res.data.erpApparatusCommodityDTO.qualityType;
            formInline.n12 = row.manufacture.enterpriseName;

            formInline.n17 = row.commodityCode;
            formInline.n3 = row.pinyinCode;
            formInline.n2 = row.tradeName;
            formInline.n1 = row.commonName;
            formInline2.n16 = row.grugsType;
            formInline.n9 = row.dosageForm;
            formInline.n10 = row.packageSpecification;
            formInline.n8 = row.validityTime;
            formInline.n13 = row.originPlace;
            formInline.n15 = row.producingArea;
            formInline2.n22 = row.onlyCode
            formInline.n11 = row.listPermitHolder;
            formInline.n5 = row.basicUnit;
            formInline2.n1 = row.gspAttribute;
            formInline.n14 = row.businessScope;
            formInline2.n6 = row.qualityStandard;
            formInline2.n13 =
                row.specialMedicineControl == "1" ? true : false;
            formInline2.n12 = row.premiereVariety == "1" ? true : false;
            formInline2.n7 = row.curingType;
            formInline2.n8 = row.storageTemperature;
            formInline2.n9 = row.storageRange;
            formInline2.n10 = row.transportTemperature;
            formInline2.n11 = row.transportTemperatureRange;
            formInline2.n14 =
                row.specialTemperature == "1" ? true : false;
            formInline2.n23 =
                row.electronicSupervision == "1" ? true : false;
            formInline.n16 = row.remark;
            formInline.n4 = row.commoditySelfCode;
            formInline.n6 = row.completeUnit;
            formInline.n7 = row.ratio;
            formInline3.n1 = row.taxRate;
            formInline3.n2 = row.taxClassifyCode;
            formInline3.n3 = row.treatmentNumber;
            formInline3.n4 = row.medicationClassify;
            formInline3.n5 = row.usageDays;
            formInline3.n6 = row.medicalInsuranceClassify;
            formInline3.n7 = row.usageDosage;
            formInline3.n8 = row.taboo;
            formInline3.n9 = row.mainComponents;
            formInline3.n10 = row.specialStorage;
            formInline3.n11 = row.functionalIndications;
            formInline3.n12 = row.adaptiveSymptoms;
            formInline3.n13 =
                row.essentialMedicines == "1" ? true : false;
            formInline3.n14 =
                row.registeredTrademark == "1" ? true : false;
            formInline3.n15 = row.memberPoint == "1" ? true : false;
            formInline3.n17 =
                row.preciousMedicines == "1" ? true : false;
            formInline3.n18 = row.onlineSales == "1" ? true : false;
            formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
            formInline3.n20 = row.isFragile == "1" ? true : false;
            formInline3.n16 = row.priceProtection == "1" ? true : false;
            formInline2.n22 = row.onlyCode == "1" ? true : false; //
            formInline2.n5 = row.registrationNo;
            formInline2.n2 = row.approvalNumber;
            formInline2.n3 = row.approvalValidity;
            formInline2.n4 = row.proscriptionType;
            dialogVisible.value = true;

            res.data.annexFileDTOS.forEach((item, index) => {
              item.files.forEach(item => {
                item.name = item.filesName
                item.url = item.filesUrl
                item.id = item.filesId
              })
              fileArr.push({
                id: item.id,
                n1: index + 1,
                n2: item.smallType,
                n3: item.categoryName,
                n4: {
                  file: item.files
                },
                n5: item.isUpload,
                n6: item.isMultiPage,
                n7: {
                  str: item.remark,
                  flag: false
                }
              });
            });
            formInline4.table = fileArr;
          }
          loadingInstance.close();
        });
  } else if (type == 7) {
    disappApi
        .searchDrug({
          id: id,
        })
        .then((res) => {
          if (res.code == 200) {
            let row = res.data.erpSterilizedCommodityDTO.commodity;
            let fileArr = [];
            data.editStr = res.data;
            data.editStr.state = `状态: ` + echoStatus(row.status)
            data.status = row.status;

            formInline2.n24 =
                res.data.erpSterilizedCommodityDTO.productionLicense;
            formInline2.n25 =
                res.data.erpSterilizedCommodityDTO.productionLicenseDate;
            formInline.n12 = row.manufacture.enterpriseName;
            dialogVisible.value = true;
            formInline.n17 = row.commodityCode;
            formInline.n3 = row.pinyinCode;
            formInline.n2 = row.tradeName;
            formInline.n1 = row.commonName;
            formInline2.n16 = row.grugsType;
            formInline.n9 = row.dosageForm;
            formInline.n10 = row.packageSpecification;
            formInline.n8 = row.validityTime;
            formInline.n13 = row.originPlace;
            formInline.n15 = row.producingArea;
            formInline.n11 = row.listPermitHolder;
            formInline.n5 = row.basicUnit;
            formInline2.n1 = row.gspAttribute;
            formInline.n14 = row.businessScope;
            formInline2.n6 = row.qualityStandard;
            formInline2.n13 =
                row.specialMedicineControl == "1" ? true : false;
            formInline2.n12 = row.premiereVariety == "1" ? true : false;
            formInline2.n7 = row.curingType;
            formInline2.n8 = row.storageTemperature;
            formInline2.n9 = row.storageRange;
            formInline2.n10 = row.transportTemperature;
            formInline2.n11 = row.transportTemperatureRange;
            formInline2.n14 =
                row.specialTemperature == "1" ? true : false;
            formInline2.n23 =
                row.electronicSupervision == "1" ? true : false;
            formInline.n16 = row.remark;
            formInline.n4 = row.commoditySelfCode;
            formInline.n6 = row.completeUnit;
            formInline.n7 = row.ratio;
            formInline3.n1 = row.taxRate;
            formInline3.n2 = row.taxClassifyCode;
            formInline3.n3 = row.treatmentNumber;
            formInline3.n4 = row.medicationClassify;
            formInline3.n5 = row.usageDays;
            formInline3.n6 = row.medicalInsuranceClassify;
            formInline3.n7 = row.usageDosage;
            formInline3.n8 = row.taboo;
            formInline3.n9 = row.mainComponents;
            formInline3.n10 = row.specialStorage;
            formInline3.n11 = row.functionalIndications;
            formInline3.n12 = row.adaptiveSymptoms;
            formInline3.n13 =
                row.essentialMedicines == "1" ? true : false;
            formInline3.n14 =
                row.registeredTrademark == "1" ? true : false;
            formInline3.n15 = row.memberPoint == "1" ? true : false;
            formInline3.n17 =
                row.preciousMedicines == "1" ? true : false;
            formInline3.n18 = row.onlineSales == "1" ? true : false;
            formInline3.n19 = row.monopolizeGoods == "1" ? true : false;
            formInline3.n20 = row.isFragile == "1" ? true : false;
            formInline3.n16 = row.priceProtection == "1" ? true : false;
            formInline2.n22 = row.onlyCode == "1" ? true : false; //
            formInline2.n5 = row.registrationNo;
            formInline2.n2 = row.approvalNumber;
            formInline2.n3 = row.approvalValidity;
            formInline2.n4 = row.proscriptionType;
            res.data.annexFileDTOS.forEach((item, index) => {
              item.files.forEach(item => {
                item.name = item.filesName
                item.url = item.filesUrl
                item.id = item.filesId
              })
              fileArr.push({
                id: item.id,
                n1: index + 1,
                n2: item.smallType,
                n3: item.categoryName,
                n4: {
                  file: item.files
                },
                n5: item.isUpload,
                n6: item.isMultiPage,
                n7: {
                  str: item.remark,
                  flag: false
                }
              });
            });
            formInline4.table = fileArr;
          }
          loadingInstance.close();
        });
  }
};
const echoTitle = () => {
  if (data.types == 4) {
    return '药品剂型'
  } else if (data.types == 5) {
    return '食品剂型'
  } else if (data.types == 7) {
    return '消杀剂型'
  }
}
const handleChange = (val) => {
  console.log(val);
};
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount'
  // )
  let arr = await typeList()
  for (let key in arr) {
    data[key] = arr[key];
  }
  drugApi.TreesDrug({}).then((res) => {
    data.treesType = flattenTree(res.data)
  });
  const res1 = await drugApi.getSys({
    type: 'drug_type',
    size: 1000
  })
  const res2 = await drugApi.getSys({
    type: 'directory_file_name',
    size: 1000
  })

  fileSmallType.value = res1.data
  fileType.value = res2.data
  statusType.value = await proxy.getDictList('product_status')
});

function flattenTree(tree) {
  const flatArray = [];

  function flattenNode(node) {
    flatArray.push(node);
    if (node.children) {
      node.children.forEach(flattenNode);
    }
  }

  tree.forEach(flattenNode);

  return flatArray;
}

onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
});
watchEffect(() => {
});
const echoType = (type, str, find, ind) => {
  if (type) {
    const newStr = type.find(item => item[find] == str)
    return newStr ? newStr[ind] : str
  } else {
    return str
  }
}
const statusType = ref([])
const fileSmallType = ref()
const fileType = ref()
const echoStatus = (status) => {
  return statusType.value.find(item => item.value == status)?.name
};
const echo1 = (value) => {
  return fileSmallType.value.find(item => item.value == value)?.name
}
const echo2 = (value) => {
  return fileType.value.find(item => item.value == value)?.name
}
const treeEcho = (type, str, find, ind,) => {
  if (type) {
    for (let i = 0; i < type.length; i++) {
      let a = type[i]
      if (a[find] === str) {
        return a[ind]
      } else {
        if (a.children && a.children.length > 0) {
          let res = treeEcho(a.children, str, find, ind)
          if (res) {
            return res
          }
        } else {
          return str
        }
      }
    }
  } else {
    return str
  }
}
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  details,
});
</script>
<style lang="scss" scoped>
::v-deep .el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: #fff;
  border-color: #dbdee5;
  cursor: not-allowed;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #2a75f6;
  border-color: #2a75f6;
}

::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #606266;
  cursor: not-allowed;
}

::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #fff;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #2a75f6 !important;
}

::v-deep .el-checkbox__inner {
  width: 20px !important;
  height: 20px !important;
}

::v-deep .el-checkbox__inner::after {
  width: 7px !important;
  height: 13px !important;
}

::v-deep .el-input__suffix-inner {
  display: none !important;
}

.zhe {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.6);

  .imgDiv {
    max-width: 70%;
    max-height: 70%;

    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    img {
      width: 100%;
      height: 100%;
      min-width: 200px;
      color: #fff;
    }

    span {
      position: absolute;
      font-size: 25px;
      border-radius: 50%;
      height: 30px;
      width: 30px;
      line-height: 34px;
      text-align: center;
      color: #fff;
      right: -30px;
      top: -5px;
    }
  }
}

.formBox {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.col_title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 8px;

  &::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 20px;
    background-color: #2878ff;
    border-radius: 2px;
    position: absolute;
    top: 15px;
    left: 0;
  }
}

.stateTitle {
  position: absolute;
  font-size: 15px;
  top: 21px;
  right: 53px;
}

.messTable {
  width: 100%;
  background-color: #eaedf3;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  padding: 1px 1px 0 1px;

  tr {
    margin-bottom: 1px;
    display: flex;
    background: #fff;

    td {
      background-color: white;
      line-height: 40px;
    }

    td:nth-child(1) {
      flex: 1;
      padding: 0 10px;
      font-weight: bold;
      color: #505050;
      background: #f7f7f7;
    }

    td:nth-child(2) {
      color: #606266;
      padding: 0 10px;
      flex: 2
    }
  }
}
</style>
