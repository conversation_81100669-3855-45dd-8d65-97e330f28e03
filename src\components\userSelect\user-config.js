import userRole from '@/api/model/sys/sysUserRoleService.js'

// 需要返回一个promise
async function loadDepOrUser ( username,roleType ) {
  let nodeData = []
  let users =  await userRole.findUserByRoleType({queryUserName:username,queryRoleType:roleType});
  if(users.code == '200' && users.data) {
    nodeData = users.data.records.map(item=>{
      return {id:item.sysUser.id,name:item.sysUser.name,phone:item.sysUser.phone}
    });
  }
  return nodeData
}
//用户选择
export const USER_SELECT_CONFIG = {
  tabName: '指定人员',  // 选项卡名称
  type: 'flow_user', //选项卡键值 传入的selected要和键值保持一致 eg: {dep: [], role: []}
  children: 'children', // 子节点标志
  // 生成每个节点的id 保证唯一
  nodeId: function ( data ) {
    return data.id
  },
  // 生成节点的名称 可选值 string | function
  label: function ( data, node ) {
    return data.label || data.name
  },
  // 判断是否为叶子节点 可选值 string | function
  isLeaf: function ( data, node ) {
    return data.hasOwnProperty( 'id' ) // 含有userId为人员  且为叶子节点
  },
  // 动态请求后台拿到节点数据 返回一个promise
  onload(username,roleType){
    return loadDepOrUser( username ,roleType);
  },
  checkedHandle(data){
    if(data.length == 0){
      return [];
    }
    return data.map(item=>{
      return item.id
    })
  },
};

export const CONFIG_LIST = [USER_SELECT_CONFIG];
