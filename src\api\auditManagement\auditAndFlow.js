import request from '@/utils/request';
// 审核流程  api
export const flows = {
    // 系统主流程-列表
    flowData: function (params) {
        return request.get('/audit/flow/list', params);
    },
    // 系统主流程-删除
    delFLow: function (params) {
        return request.delete('/audit/flow/delete', params);
    },
    // 系统主流程-新增&修改
    saveFlow: function (data) {
        return request.post('/audit/flow/save', data);
    },
    // 绑定组织机构
    saveTheCarrier: function (data) {
        return request.post('/audit/orgFlow/bindOrg', data);
    },
    /*查询主流程绑定和未绑定组织机构*/
    getBindOrgList: function (params) {
        return request.get('/audit/flow/getBindOrgList', params);
    },
    /*初始化角色下拉*/
    getroleList: function (params) {
        return request.get('/sys/role/roleSelect', params);
    },
    /*组织机构审批流程列表*/
    getOrgFlowList: function (params) {
        return request.get('/audit/orgFlow/list', params);
    },
    // 发起配置
    applyConfig: function (data) {
        return request.post('/audit/orgFlow/applyConfig', data);
    }
};
// 节点
export const node = {
    // 根据组织流程id查询节点
    nodeList: function (params) {
        return request.get('/audit/orgFlow/node/queryByOrgFlowId', params);
    },
    // 组织流程节点-新增&保存
    saveNode: function (data) {
        return request.post('/audit/orgFlow/node/save', data);
    },
    // 删除节点
    delNode: function (params) {
        return request.delete('/audit/orgFlow/node/delete', params);
    },
    // 查询节点详情
    queryById: function (params) {
        return request.get('/audit/orgFlow/node/queryById', params);
    }
};
