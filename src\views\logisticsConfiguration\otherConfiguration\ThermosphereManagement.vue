<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item label="温区类型" prop="type">
                    <el-select v-model="queryParams.type" clearable placeholder="请选择温区类型" @change="handleQuery">
                        <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="温层状态" prop="status" @change="handleQuery">
                    <el-select v-model="queryParams.status" clearable placeholder="请选择温层状态">
                        <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <el-button icon="el-icon-plus" type="primary" @click="handleAdd">新增</el-button>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="ThermosphereManagement" v-loading="loading" v-model:columns="columns" :data="temperatureTypeList" :max-height="600">
                <template #type="{ row }">
                    <span>{{ typeFormat(row) }}</span>
                </template>
                <template #lowTemperature="scope"> {{ scope.row.lowTemperature }}℃ </template>
                <template #highTemperature="scope"> {{ scope.row.highTemperature }}℃ </template>
                <template #status="scope">
                    <el-switch v-model="scope.row.status" size="small" active-color="#1ACD7E" active-text="正常" active-value="0" class="switch__label" inactive-text="停用" inactive-value="1" @change="changeStatus(scope.row)"> </el-switch>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(row)">修改</el-button>
                    <el-button icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(row)">删除</el-button>
                </template>
            </column-table>
            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
            </div>
        </el-card>

        <!-- 添加或修改温层类型对话框 -->
        <el-drawer v-model="open" :title="title" append-to-body size="400px" @close="cancel">
            <div class="p-16" style="background-color: #f2f2f2">
                <el-card class="mb10" shadow="never">
                    <el-form ref="form" :model="form" :rules="rules" label-width="auto">
                        <el-form-item label="温区类型" prop="type">
                            <el-select v-model="form.type" placeholder="请选择温区类型" style="width: 100%">
                                <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.name" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="温层描述" prop="describtion">
                            <el-input v-model="form.describtion" maxlength="20" placeholder="请输入温层描述" show-word-limit />
                        </el-form-item>
                        <el-form-item label="低温阈值" prop="lowTemperature">
                            <el-input v-model="form.lowTemperature" maxlength="10" placeholder="请输入低温阈值" show-word-limit>
                                <template #suffix>℃ </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="高温阈值" prop="highTemperature">
                            <el-input v-model="form.highTemperature" maxlength="10" placeholder="请输入高温阈值" show-word-limit>
                                <template #suffix>℃ </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="排序" prop="sort">
                            <el-input-number v-model="form.sort" controls-position="right" :min="1" :max="9999" placeholder="请输入排序" :step="1" :precision="0" />
                        </el-form-item>
                    </el-form>
                </el-card>
                <div class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button @click="cancel">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
export default {
    name: 'ThermosphereManagement',
    components: { SearchButton, ColumnTable, RightToolbar },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 温层类型表格数据
            temperatureTypeList: [],
            columns: [
                { title: '温区类型', key: 'type', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '温层描述', key: 'describtion', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '低温阈值', key: 'lowTemperature', align: 'center', minWidth: '80px', columnShow: true },
                { title: '高温阈值', key: 'highTemperature', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '温层状态', key: 'status', align: 'center', minWidth: '170px', columnShow: true, showOverflowTooltip: true },
                { title: '排序', key: 'sort', align: 'center', minWidth: '70px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '160px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 类型字典
            typeOptions: [],
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                type: null,
                status: null
            },
            // 表单参数
            form: {
                type: null,
                describtion: '',
                lowTemperature: '',
                highTemperature: '',
                sort: undefined
            },
            // 表单校验
            rules: {
                type: [{ required: true, message: '温区类型不能为空', trigger: 'change' }],
                describtion: [{ required: true, message: '温层描述不能为空', trigger: 'blur' }],
                lowTemperature: [
                    { required: true, message: '低温阈值不能为空', trigger: 'blur' },
                    { pattern: /^[+-]?([0-9]+([.][0-9]*)?|[.][0-9]+)$/, message: '请输入正确的低温阈值', trigger: ['blur', 'change'] }
                ],
                highTemperature: [
                    { required: true, message: '高温阈值不能为空', trigger: 'blur' },
                    { pattern: /^[+-]?([0-9]+([.][0-9]*)?|[.][0-9]+)$/, message: '请输入正确的高温阈值', trigger: ['blur', 'change'] }
                ],
                sort: [
                    { required: true, message: '排序不能为空', trigger: 'blur' },
                    { pattern: /^[0-9]*$/, message: '请输入正确的排序', trigger: ['blur', 'change'] }
                ]
            },
            statusOptions: []
        };
    },
    async created() {
        this.getList();
        // 状态
        this.statusOptions = await this.getDictList('sys_normal_disable');
        this.typeOptions = await this.getDictList('fourpl_temperature_type');
    },
    methods: {
        // 取消按钮
        cancel() {
            this.open = false;
            this.form.id = null;
            this.resetForm('form');
        },
        changeStatus(row) {
            if (row?.id) {
                otherConfiguration.getTemperatureTypeById({ id: row.id }).then((response) => {
                    if (response.code === 200) {
                        let param = response.data;
                        param.status = row.status;
                        otherConfiguration.saveTemperatureType(param).then((res) => {
                            if (row.status == '1') {
                                this.msgSuccess('禁用成功');
                            } else {
                                this.msgSuccess('启用成功');
                            }
                            this.getList();
                        });
                    }
                });
            }
        },
        /** 查询温层温层类型列表 */
        getList() {
            this.loading = true;
            otherConfiguration.listTemperatureType(this.queryParams).then((response) => {
                if (response.code === 200 && response.data) {
                    this.temperatureTypeList = response.data.records || [];
                    this.total = response.data.total || 0;
                }
                this.loading = false;
            });
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.$refs['form'] ? this.$refs['form'].resetFields() : '';
            this.open = true;
            this.title = '添加温层类型';
        },
        // 删除按钮操作
        handleDelete(row) {
            const id = row.id || this.ids;
            const name = row.describtion;
            this.$confirm('是否确认删除温层类型为"' + name + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return otherConfiguration.delTemperatureType({ ids: id });
                })
                .then(() => {
                    this.getList();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            const id = row.id || this.ids;
            otherConfiguration.getTemperatureTypeById({ id }).then((response) => {
                if (response.code === 200) {
                    this.open = true;
                    this.title = '修改温层类型';
                    this.form = response.data;
                }
            });
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        resetForm(formName) {
            this.$refs[formName] ? this.$refs[formName].resetFields() : '';
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    otherConfiguration.saveTemperatureType({ ...this.form, status: null }).then((response) => {
                        if (response.code === 200) {
                            this.msgSuccess('添加成功');
                            this.open = false;
                            this.getList();
                        } else {
                            this.msgError('添加失败');
                        }
                    });
                }
            });
        },
        // 类型字典翻译
        typeFormat(row, column) {
            return this.selectDictLabel(this.typeOptions, row.type);
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep {
    thead th {
        border-right: none !important;
    }

    .form-mb0 .el-form-item {
        margin-bottom: 4px;
        margin-top: 4px;
    }

    .el-drawer__header {
        margin-bottom: 20px;
    }

    label.el-radio {
        margin-right: 8px;
    }

    .box-footer {
        padding-left: 8px;
        display: flex;
        justify-content: space-between;

        .el-form-item__content {
            margin-left: 0 !important;
        }
    }

    .el-button-group > .el-button:not(:last-child) {
        margin-right: 8px;
    }

    .el-tabs__header.is-top {
        margin-bottom: 0;
    }

    .el-tabs__nav-scroll {
        padding-left: 32px;
    }
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.mb8 {
    margin-bottom: 8px !important;
}

.mb16 {
    margin-bottom: 16px;
}

.box-table-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.p-16 {
    padding: 16px;
}

.mb0 {
    margin-bottom: 0;
}

.box-search .el-form-item.el-form-item--small {
    margin-right: 24px;
}
</style>
