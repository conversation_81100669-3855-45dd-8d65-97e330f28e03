<template>
  <div class="app-container">
    <el-card class="box-card Botm" style="margin:10px">
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="form_130">
        <TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
          <el-form-item label="标题" prop="document">
            <el-input v-model="queryParams.document" class="form_225" clearable placeholder="请输入标题"/>
          </el-form-item>

          <el-form-item label="审核节点" prop="nodeName">
            <el-input v-model="queryParams.nodeName" class="form_225" clearable placeholder="请输入审核节点"/>
          </el-form-item>
          <el-form-item label="任务类型" prop="documentType">
            <el-select v-model="queryParams.documentType" class="m-2" placeholder="请输入任务类型"
                       style="width: 100%;min-width:100px">
              <el-option v-for="(item, index) in auditForm" :key="index" :label="item.name" :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="申请日期" prop="createDate">
            <el-date-picker v-model="queryParams.createDate" class="form_225" end-placeholder="结束日期"
                            range-separator="至"
                            start-placeholder="开始日期" type="daterange"/>
          </el-form-item>
        </TopTitle>
      </el-form>
    </el-card>
    <!-- 表格数据 -->
    <el-card class="box-card" style="margin:10px">
      <el-row :gutter="10">
        <el-col :span="1.5" style="margin-left: 10px">
          <el-select v-model="batch" placeholder="请选择审核操作" style="width: 150px">
            <el-option label="批量同意" value="1"/>
            <el-option label="批量驳回" value="2"/>
          </el-select>
          <el-button :disabled="!chooseList.length || !batch" style="margin-left: 10px" type="primary"
                     @click="() => handleAllSub()">确定
          </el-button>
        </el-col>
      </el-row>
      <el-table v-loading="loading" :data="list" border style="margin-top: 15px"
                @selection-change="handleSelectionChange_file">
        <el-table-column align="center" type="selection" width="55"/>
        <el-table-column align="center" label="序号" prop="sort" width="80">
          <template #default="scope">
            <span>{{
                (queryParams.current - 1) * queryParams.size +
                scope.$index +
                1
              }}</span>
          </template>
        </el-table-column>

        <el-table-column :show-overflow-tooltip="true" align="left" label="标题" min-width="300" prop="document"/>
        <el-table-column align="left" label="配置主流程名称" min-width="160" prop="auditProcess.name">
          <!-- <template #default="scope">
                        <span>{{ formDict(typeList, scope.row.documentType) }}</span>
                    </template> -->
        </el-table-column>
        <el-table-column align="left" label="上一节点审核人" min-width="150" prop="createBy.name"/>
        <el-table-column align="left" label="当前审核节点" min-width="150" prop="auditNode.nodeName"/>
        <el-table-column align="center" label="申请日期" min-width="130">
          <template #default="scope">
            <span>{{
                moment(scope.row.createDate).format("YYYY-MM-DD")
              }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="状态" min-width="100">
          <template #default="scope">
            <span>审批中</span>
          </template>
        </el-table-column>

        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="300px">
          <template #default="scope">
            <el-button link type="primary" @click="handleAdd(scope.row)"><img src="@/assets/icons/detail.png"
                                                                              style="margin-right: 5px"/>详细信息
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="float: right">
        <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current"
                    :total="total"
                    @pagination="getList"/>
      </div>
    </el-card>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="600px">
      <p style="color:#333;line-height:30px;font-size:16px">
        将对一下{{
          chooseList.length
        }}个审核任务进行批量同意操作，请确定
      </p>
      <div v-for="(item, index) in chooseList" :key="index" style="display: flex;line-height:30px">
        <p style="width:40%">
          <span style="color:#333;font-size:14px;">ID：</span>
          <span style="color:#666">{{ item.id }}</span>
        </p>
        <p>
          <span style="color:#333;font-size:14px;">任务标题：</span>
          <span style="color:#666">{{ item.document }}</span>
        </p>
      </div>
      <div v-show="batch == '2' ? true : false">
        <span>驳回原因</span>
        <el-input v-model="idea" clearable placeholder="请输入驳回原因" style="width: 240px"/>
      </div>
      <template #footer>
        <div class="dialog-footer">
			<el-button @click="() => (open = false)">取 消</el-button>
			<el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <PurchasingManagementDetail v-if="detailValue.documentType == '8' && visible" :beforeClose="beforeClose"
                                :data="detailValue" :getList="refresh"
                                :visible="detailValue.documentType == '8' && visible"/>
    <Custom v-if="detailValue.documentType == '3' && visible" :beforeClose="beforeClose" :data="detailValue"
            :getList="refresh" :visible="detailValue.documentType == '3' && visible"/>
    <Supplier v-if="detailValue.documentType == '2' && visible" :beforeClose="beforeClose" :data="detailValue"
              :getList="refresh" :visible="detailValue.documentType == '2' && visible"/>
    <Manufacturer v-if="detailValue.documentType == '1' && visible" :beforeClose="beforeClose" :data="detailValue"
                  :getList="refresh" :visible="detailValue.documentType == '1' && visible"/>
    <ExitApplication v-if="detailValue.documentType == '10' && visible" :beforeClose="beforeClose" :data="detailValue"
                     :getList="refresh" :visible="detailValue.documentType == '10' && visible"/>
    <TransferApplicationDetail v-if="detailValue.documentType == '12' && visible" :beforeClose="beforeClose"
                               :data="detailValue" :getList="refresh"
                               :visible="detailValue.documentType == '12' && visible"/>
    <purchasePriceAdjustmentDetail v-if="detailValue.documentType == '15' && visible" :beforeClose="beforeClose"
                                   :data="detailValue" :getList="refresh"
                                   :visible="detailValue.documentType == '15' && visible"/>
    <compile ref="compileRef" @refresh="refresh"/>
    <sellDetails ref="childRef" @refresh="refresh"/>
    <el-dialog v-model="dialogVisibleRELE" title="查看开票详情" width="80%">
      <div v-loading="checkFlagRELE">
        <relevancyDetails :detailType="data.detailType" :price="data.price" :strs="data.row" :table1="data.detailTable1"
                          :table2="data.detailTable2"/>
        <div>
          <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">操作记录</h4>
          <LogQuery ref="logRef1"/>
        </div>
        <div>
          <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">审批意见</h4>
          <auditForms ref="auditRef1" @refresh="refresh1"/>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisibleRELE = false">取消</el-button>
          <el-button type="primary" @click="rightRELE">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleBack" title="查看详情" width="80%">
      <div v-loading="loadingBackDetail" style="min-height: 200px">
        <backDetails ref="detailsChild" :flagOut="true" :inTable="data.inTable" :tableStr="data.tableStr"/>
        <div>
          <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">操作记录</h4>
          <LogQuery ref="logRef2"/>
        </div>
        <div>
          <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">审批意见</h4>
          <auditForms ref="auditRef2" @refresh="refresh2"/>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisibleBack = false">取消</el-button>
          <el-button type="primary" @click="rightBack">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisibleOrder" title="查看开票详情" width="80%">
      <div v-loading="detailFlagOrder">
        <ApplicationDetails :obj="data.obj" :table1="data.table1" :table2="data.table2" :types="data.types"/>
        <div>
          <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">操作记录</h4>
          <LogQuery ref="logRef3"/>
        </div>
        <div>
          <h4 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 10px">审批意见</h4>
          <auditForms ref="auditRef3" @refresh="refresh3"/>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisibleOrder = false">取消</el-button>
          <el-button type="primary" @click="rightOrder">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <priceDetail ref="detailRef" @getList="getList"/>
  </div>
</template>

<script setup>
import {getCurrentInstance, reactive, ref, toRefs} from "vue";
import sellDetails from "@/components/detailsForm/sellDetails.vue";
import sysNoticeService from "@/api/model/approve/sysNoticeService";
import moment from "moment";
import PurchasingManagementDetail from "./components/purchasingManagementDetail.vue";
import Custom from "./components/custom.vue";
import Supplier from "./components/supplier.vue";
import Manufacturer from "./components/manufacturer.vue";

import LogQuery from "@/components/detailsForm/logQuery.vue";
import Compile from "@/components/detailsForm/compile.vue";
import TopTitle from "@/components/topTitle/index.vue";
import ExitApplication from "./components/exitApplication.vue";
import TransferApplicationDetail from './components/transferApplicationDetail.vue'
import relevancyDetails from '../invoiceManagement/components/relevancyDetails.vue'
import {applocation, outPut, relevanceInput} from "@/api/model/invoice";
import {ElMessage} from "element-plus";
import priceDetail from '@/views/salesManagement/components/priceDetail.vue'
import BackDetails from "@/views/salesManagement/components/backDetails.vue";
import {backApi, manageApi} from "@/api/model/salesManagement";
import ApplicationDetails from "@/views/invoiceManagement/components/ApplicationDetails.vue";
import purchasePriceAdjustmentDetail from './components/purchasePriceAdjustmentDetail.vue'

const auditRef3 = ref(null)
const logRef3 = ref(null)
const {proxy} = getCurrentInstance();
const auditRef1 = ref(null)
const logRef2 = ref(null)
const list = ref([]);
const auditRef2 = ref(null)
const open = ref(false);
const compileRef = ref(null);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const batch = ref("");
const idea = ref("");
const dialogVisibleBack = ref(false)
const loadingBackDetail = ref(false)
const statusList = ref([]);
const auditForm = ref([]);
const logRef1 = ref(null)
const chooseList = ref([]);
const newFilArr = ref([]);
const visible = ref(false);
const detailValue = ref({});
const childRef = ref(null);
const dialogVisibleRELE = ref(false)
const detailRef = ref(null)
const checkFlagRELE = ref(false)
const data = reactive({
  form: {},
  queryParams: {
    current: 1,
    size: 10,
  },
});

const {queryParams, form, rules} = toRefs(data);
const beforeClose = () => {
  visible.value = false;
  detailValue.value = {};
};
const handleSelectionChange_file = (key) => {
  chooseList.value = key;
};
const rightRELE = () => {
  auditRef1.value.formSub(data.nRow.id);
}
const rightBack = () => {
  auditRef2.value.formSub(data.backId);
}
const rightOrder = () => {
  auditRef3.value.formSub(data.orderId);
}
const handleAllSub = () => {
  if (batch.value == "2") {
    title.value = "批量驳回";
  } else {
    title.value = "批量同意";
  }
  open.value = true;
};

/** 查询角色列表 */
function getList() {
  const params = {...queryParams.value}
  if (params?.createDate?.length) {
    params.beginCreateDate = moment(params?.createDate[0]).format('YYYY-MM-DD 00:00:00')
    params.endCreateDate = moment(params?.createDate[1]).format('YYYY-MM-DD 23:59:59')
    delete params.createDate
  }
  if (params.nodeName) {
    params['auditNode.nodeName'] = params.nodeName
    delete params.nodeName

  }
  loading.value = true;
  list.value = []
  sysNoticeService.list(params).then((res) => {
    if (res.code == 200) {
      list.value = res.data.records;
      total.value = res.data.total;
      loading.value = false;
    }
  });
}

const formDict = (data, val) => {
  return proxy.selectDictLabel(data, val);
};

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

const refresh = () => {
  getList();
};
const refresh1 = () => {
  getList();
  dialogVisibleRELE.value = false
}
const refresh2 = () => {
  getList();
  dialogVisibleBack.value = false
}
const refresh3 = () => {
  getList();
  dialogVisibleOrder.value = false
}
const handleAdd = (row) => {
  console.log(row)
  if (row.documentType >= 4 && row.documentType <= 7) {
    compileRef.value.details(row.documentType, row.documentId, row.id);
  } else if (row.documentType == 9) {
    childRef.value.details(row.documentId, row.id, false);
  } else if (row.documentType == 13) {
    releDetail(row)
  } else if (row.documentType == 11) {
    backDetail(row)
  } else if (row.documentType == 14) {
    orderDetail(row)
  } else if (row.documentType == 16) {
    detailRef.value.dialogVisible = true
    detailRef.value.getList(row.documentId, row.id)
  } else {
    visible.value = true;
    detailValue.value = row;
  }
};
const dialogVisibleOrder = ref(false)
const detailFlagOrder = ref(false)
const orderDetail = async (row) => {
  data.table2 = []
  data.table1 = []
  data.orderId = row.id
  dialogVisibleOrder.value = true
  detailFlagOrder.value = true
  const res = await applocation.detailList({
    'salesInvoiceId': row.documentId
  })
  const res2 = await outPut.detaiInvoices({
    'salesInvoice.id': row.documentId
  })
  if (logRef3.value) {
    logRef3.value.data.list = [];
  }
  const auditList = await applocation.auditList({"salesInvoice.id": row.id})
  const logList = await manageApi.logList({masterId: row.id})
  if (auditList.code == 200 && logList.code == 200) {
    logRef3.value.timeFns(auditList.data.records, logList.data.records);
  } else {
    ElMessage.error('加载失败')
  }
  if (res.code == 200 && res2.code == 200) {
    detailFlagOrder.value = false
    data.types = res.data.salesInvoice.billingType
    res2.data.records.forEach(record => {
      data.table1.push(record.salesInvoiceReceipt)
    })
    if (data.types == 0) {
      res.data.salesInvoiceForms.forEach(item => {
        if (!item.invoiceFormPriceList) {
          item.invoiceFormPriceList = []
          item.invoiceFormPriceList.push({
            invoicePrice: item.unitPrice,
            invoiceQuantity: item.invoicedQuantity,
            totalAmount: item.totalAmount
          })
        }
        data.table2.push({
          'docNum': item.salesNo,
          'erpCustomer': {
            id: item.customer.id
          },
          'editId': item.id,
          'customer': item.customerName,
          'commodity': {
            'commonName': item.commodityName,
            'commoditySelfCode': item.commoditySelfCode,
            'packageSpecification': item.commodityPackageSpecification,
            'manufactureName': item.commodityManufactureName,
            'producingArea': item.commodityProducingArea,
            'validityTime': item.commodityValidityTime,
            'basicUnit': item.commodityBasicUnit,
          },
          'checkFlag': false,
          'invoiceFormPriceList': item.invoiceFormPriceList,
          'totalAmount': item.totalAmount,
          'salesOutBound': {
            'outTime': item.salesOutBound.outTime
          },
          'totalPriceTax': item.totalPriceTax,
          'totalTax': item.totalTax,
          'taxRate': item.taxRate,
          'createDate': item.salesOrder.docDate,
          'produceDate': item.commodityProduceDate,
          'batchNumber': item.batchNo,
          'unitPrice': item.unitPrice,
          'outQuantity': item.boundQuantity,
          'supplier': null,
          'supplierName': null,
          'allPrice': (item.unitPrice * item.boundQuantity).toFixed(2)
        })
        data.obj = res.data.salesInvoice
      })
    } else {
      res.data.salesInvoiceForms.forEach(item => {
        data.table2.push({
          'docNum': item.salesNo,
          'editId': item.id,
          'customer': item.customerName,
          'commodity': {
            'commonName': item.commodityName,
            'commoditySelfCode': item.commoditySelfCode,
            'packageSpecification': item.commodityPackageSpecification,
            'manufactureName': item.commodityManufactureName,
            'producingArea': item.commodityProducingArea,
            'validityTime': item.commodityValidityTime,
            'basicUnit': item.commodityBasicUnit,
          },
          'totalAmount': item.totalAmount,
          'salesOutBound': {
            'outTime': item.salesRetreatInbound.inTime
          },
          'createDate': item.salesInvoice.applyDate,
          'produceDate': item.commodityProduceDate,
          'batchNumber': item.batchNo,
          'unitPrice': item.unitPrice,
          'outQuantity': item.boundQuantity,
          'num': item.invoicedQuantity,
          'supplier': null,
          'supplierName': null,
          'allPrice': item.totalAmount
        })
        data.obj = res.data.salesInvoice
      })
    }

  } else {
    ElMessage.error('获取失败')
  }
}
const backDetail = async (row) => {
  data.tableStr = []
  data.inTable = []
  data.backId = row.id
  dialogVisibleBack.value = true
  loadingBackDetail.value = true
  const res1 = await backApi.getDetails({
    id: row.documentId
  })
  if (logRef2.value) {
    logRef2.value.data.list = [];
  }
  const auditList = await manageApi.auditLists({"salesRetreat.id": row.documentId})
  const logList = await manageApi.logList({masterId: row.documentId})
  if (auditList.code == 200 && logList.code == 200) {
    logRef2.value.timeFns(auditList.data.records, logList.data.records);
  } else {
    ElMessage.error('加载失败')
  }
  const res2 = await backApi.getPut({
    'salesRetreat.id': row.documentId
  })
  if (res1.code == 200 && res2.code == 200) {
    data.tableStr = res1.data
    data.inTable = res2.data.records
  } else {
    ElMessage.error('获取失败')
  }
  loadingBackDetail.value = false
}
const releDetail = (row) => {
  checkFlagRELE.value = true
  dialogVisibleRELE.value = true
  data.detailTable1 = []
  data.price = 0
  data.row = {}
  data.nRow = row
  data.detailTable2 = []
  dialogVisibleRELE.value = true
  relevanceInput.detailList({
    id: row.documentId
  }).then(async (res) => {
    if (res.code == 200) {
      data.row = res.data.erpPurchaseInvoiceDto
      if (logRef1.value) {
        logRef1.value.data.list = [];
      }
      const auditList = await relevanceInput.auditLists({"purchaseInvoice.id": res.data.erpPurchaseInvoiceDto.id})
      const logList = await relevanceInput.logList({masterId: res.data.erpPurchaseInvoiceDto.id})
      if (auditList.code == 200 && logList.code == 200) {
        logRef1.value.timeFns(auditList.data.records, logList.data.records);
      } else {
        ElMessage.error('加载失败')
      }
      data.detailType = res.data.erpPurchaseInvoiceDto.type
      res.data.erpPurchaseInvoiceFormDtos.forEach((item) => {
        data.detailTable2.push(
            {
              id: item.id,
              orderCode: item.purchaseNo,
              commodity: {
                tradeName: item.commodityName,
                commoditySelfCode: item.commoditySelfCode,
                packageSpecification: item.commodityPackageSpecification,
                manufactureName: item.commodityManufactureName,
                producingArea: item.commodityProducingArea,
                commodityCode: item.commodityCode,
                produceDate: item.commodityProduceDate
              },
              checkFlag: false,
              invoicingInfo: item.invoicingInfo?.split(','),
              unitPrice: item.commodityUnitPrice.toFixed(2),
              receivingQuantity: item.inboundQuantity,
              allPrice: item.invoicingAmount.toFixed(2),
              basicUnit: item.commodityBasicUnit,
              validate: item.commodityValidityTime,
              invoicingQuantity: item.invoicingQuantity,
              purchaseCreatedDate: item.purchaseCreatedDate,
              intoNo: item.batchNo,
              produceDate: item.produceDate,
              purchaseCreateDate: item.purchaseCreateDate,
              intoDate: item.intoDate,
              suppier: item.supplierName,
            }
        )
      })
      data.price = res.data.erpPurchaseInvoiceDto.invoiceAmount
      res.data.erpPurchaseInvoiceUnionDtos.forEach((item, index) => {
        data.detailTable1.push({
          id: item.id,
          invoiceNo: item.purchaseInvoiceReceipt.invoiceNo,
          invoiceCode: item.purchaseInvoiceReceipt.invoiceCode,
          invoiceSupplier: item.purchaseInvoiceReceipt.invoiceSupplier,
          taxpayerNo: item.purchaseInvoiceReceipt.taxpayerNo,
          invoicingDate: item.purchaseInvoiceReceipt.invoicingDate,
          invoicingAmount: item.purchaseInvoiceReceipt.invoicingAmount,
          excludingTax: item.purchaseInvoiceReceipt.excludingTax,
          totalTax: item.purchaseInvoiceReceipt.totalTax,
          effectiveTax: item.purchaseInvoiceReceipt.effectiveTax,
          type: item.purchaseInvoiceReceipt.type,
          fileDtos: []
        })
        item.purchaseInvoiceReceipt.fileDtos.forEach(item => {
          data.detailTable1[index].fileDtos.push({
            id: item.id,
            name: item.fileName,
            url: item.fileUrl
          })
        })
      })
      checkFlagRELE.value = false
    }
  })
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function submitForm() {
  let idss = "";
  let status = "";
  if (batch.value == "2") {
    status = "11";
  } else {
    status = "30";
  }
  newFilArr.value = [];
  chooseList.value.filter((item) => {
    newFilArr.value.push(item.id);
  });
  console.log(newFilArr.value);
  idss = newFilArr.value.join(",");
  sysNoticeService
      .handbatch({ids: idss, status: status, idea: idea.value})
      .then((res) => {
        if (res.code == 200) {
          proxy.msgSuccess(`批量审批成功`);
          open.value = false;
          getList();
        } else {
          open.value = false;
          proxy.msgError(res.msg);
          getList();
        }
      });
}

async function dict() {
  statusList.value = await proxy.getDictList("audit_status");
  auditForm.value = await proxy.getDictList('audit_form')
}

dict();
getList();
</script>

<style lang="scss" scoped>
::v-deep .Botm {
  .el-card__body {
    padding-bottom: 0px
  }
}
</style>
