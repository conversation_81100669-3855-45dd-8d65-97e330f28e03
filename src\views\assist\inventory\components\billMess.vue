<template>
  <div>
    <h3 class="el-dialog__title" style="margin-bottom: 10px">发票信息</h3>
    <el-table :data="props.tableList1" style="width: 100%">
      <el-table-column label="发票号码" prop="invoiceNo"/>
      <el-table-column label="发票代码" prop="invoiceCode"/>
      <el-table-column v-if="props.numStatus==2" label="发票客户" prop="customerName"/>
      <el-table-column v-if="props.numStatus==1" label="发票供应商" prop="invoiceSupplier"/>
      <el-table-column label="纳税人识别号" prop="taxpayerNo"/>
      <el-table-column label="开票日期" prop="invoicingDate">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.invoicingDate) }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额" prop="">
        <template #default="scope">
          {{ scope.row.invoicingAmount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column v-if="props.numStatus==1" label="附件" property="">
        <template #default="scope">
          <p v-for="(item, index) in props.tableList1[scope.$index].fileDtos" :key="index"
             style="color: rgb(34, 130, 255);cursor: pointer;" @click="checkImg(item.url)">{{
              item.name
            }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="props.numStatus==2" label="附件" property="">
        <template #default="scope">
          <div v-if="scope.row.files">
            <p v-for="(item,index) in JSON.parse(scope.row.files)" :key="index"
               style="color: rgb(34, 130, 255);cursor: pointer;"
               @click="checkImg(item.resFileUrl)">
              {{ item.name }}
            </p>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <h3 class="el-dialog__title" style="margin: 10px 0">{{
        props.numStatus == 1 ? '关联入库记录' : '关联出库记录'
      }}</h3>
    <el-table v-if="props.numStatus==1" :data="props.tableList2" style="width: 100%">
      <el-table-column :show-overflow-tooltip="true" fixed label="序号" prop="">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="单据编号" property="orderCode" width="170px"/>
      <el-table-column :show-overflow-tooltip="true" label="商品名称" property="commodity.tradeName"
                       width="130px"/>
      <el-table-column :show-overflow-tooltip="true" label="自编码" property="commodity.commoditySelfCode"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" label="规格" property="commodity.packageSpecification"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" label="生产厂家" property="commodity.manufactureName"
                       width="150px"/>
      <el-table-column :show-overflow-tooltip="true" label="产地" property="commodity.packageSpecification"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" label="供应商" property="suppier" width="100px"/>
      <el-table-column :show-overflow-tooltip="true" label="生产日期" property="produceDate" width="100px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="批号" property="intoNo" width="170px"/>
      <el-table-column :show-overflow-tooltip="true" label="有效期" property="validate" width="100px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.validate) }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="单位" property="basicUnit" width="100px"/>
      <el-table-column :show-overflow-tooltip="true" label="商品编号" property="commodity.commodityCode"
                       width="100px"/>
      <el-table-column :show-overflow-tooltip="true" label="单价" property="unitPrice" width="100px"/>
      <el-table-column :show-overflow-tooltip="true" label="入库数量" property="receivingQuantity" width="100px"/>
      <el-table-column :show-overflow-tooltip="true" fixed="right" label="开票金额" property="allPrice"
                       width="100px">
        <template #default="scope">
          {{ Number(scope.row.allPrice).toFixed(2) }}
        </template>
      </el-table-column>
    </el-table>
    <el-table v-else :data="props.tableList2" style="width: 100%">
      <el-table-column :show-overflow-tooltip="true" fixed label="序号" prop="">
        <template #default="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="单据编号" property="docNum" width="190px"/>
      <el-table-column label="客户" property="customer" width="130px"/>
      <el-table-column label="商品名称" property="commodity.commonName" width="150px"/>
      <el-table-column label="自编码" property="commodity.commoditySelfCode" width="120px"/>
      <el-table-column label="规格" property="commodity.packageSpecification"/>
      <el-table-column :show-overflow-tooltip="true" label="生产厂家" property="commodity.manufactureName"
                       width="170px"/>
      <el-table-column label="产地" property="commodity.producingArea"/>
      <el-table-column label="生产日期" property="produceDate" width="120px">
        <template #default="scope">
          {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
        </template>
      </el-table-column>
      <el-table-column label="批号" property="batchNumber"/>
      <el-table-column label="有效期" property="commodity.validityTime" width="120px"/>
      <el-table-column label="单位" property="commodity.basicUnit"/>
      <el-table-column label="单价" property="unitPrice"/>
      <el-table-column label="出库数量" property="outQuantity"/>
      <el-table-column :show-overflow-tooltip="true" fixed="right" label="开票金额" property="allPrice"
                       width="100px">
        <template #default="scope">
          {{ Number(scope.row.allPrice).toFixed(2) }}
        </template>
      </el-table-column>
    </el-table>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import {functionIndex} from "../../../commodity/functionIndex";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const data = reactive({})
const emit = defineEmits([])
const props = defineProps({
  tableList1: {
    default: []
  },
  tableList2: {
    default: []
  },
  numStatus: {
    default: []
  }
})
const close = () => {
  data.checkFlag = false
  data.imgUrl = []
}
const checkImg = (url) => {
  data.imgUrl = []
  data.imgUrl.push(url)
  data.checkFlag = true
}
onBeforeMount(() => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  ...toRefs(data)
})

</script>
<style lang='scss' scoped></style>
