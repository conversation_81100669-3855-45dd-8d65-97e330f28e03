import http from "@/utils/request";

export const inputInvoice = {
    saveList: function (data) {
        return http.post("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/create", data);
    },
    delList: function (params) {
        return http.delete("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/delete", params);
    },
    checkList: function (params) {
        return http.get("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/detailById", params);
    },
    auditList: function (params) {
        return http.get("/erp/invoice/purchase/erpPurchaseInvoiceApproval/list", params);
    },
    getList: function (params) {
        return http.get("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/list", params);
    },
    delFile: function (params) {
        return http.delete("/erp/common/erpCommonFile/delete", params);
    },
    getSupplier: function (params) {
        return http.get("/erp/supplier/erpSupplierProduction/list", params);
    },
	syncInvoice: function () {
		return http.post("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/sync");
	},
};
export const outPut = {
    saveList: function (data) {
        return http.post("/erp/invoice/sales/invoiceReceipt/save", data);
    },
    delFile: function (params) {
        return http.get("/file/deleteFileByUrl", params);
    },
    relevancy: function (data) {
        return http.post("/erp/invoice/sales/invoiceUnion/saveOrUpdateBatch", data);
    },
    getList: function (params) {
        return http.get("/erp/invoice/sales/invoiceReceipt/list", params);
    },
    delList: function (params) {
        return http.delete("/erp/invoice/sales/invoiceReceipt/delete", params);
    },
    detailList: function (params) {
        return http.get("/erp/invoice/sales/invoiceReceipt/queryById", params);
    },
    detaiInvoices: function (params) {
        return http.get("/erp/invoice/sales/invoiceUnion/list", params);
    },
    delInvoice: function (params) {
        return http.delete("/erp/invoice/sales/invoiceUnion/delete", params);
    }
};
export const applocation = {
    getGoodsList: function (params) {
        return http.get("/erp/sales/erpSalesOutboundRecord/list", params);
    },
    redirect: function (params) {
        return http.get("/erp/invoice/sales/invoice/retryIssueApi", params);
    },
    getError: function (params) {
        return http.get("/erp/invoice/sales/bw/issue/fail/list", params);
    },
    getGoodsListOut: function (params) {
        return http.get("/erp/sales/retreat/inboundrecord/list", params);
    },
    auditList: function (params) {
        return http.get("/erp/invoice/sales/erpSalesInvoiceApproval/list", params);
    },
    saveList: function (data) {
        return http.post("/erp/invoice/sales/invoice/createSalesInvoiceApply", data);
    },
    detailList: function (params) {
        return http.get("/erp/invoice/sales/invoice/findSalesInvoiceById", params);
    },
    delList: function (params) {
        return http.delete("/erp/invoice/sales/invoice/delete", params);
    },
    getList: function (params) {
        return http.get("/erp/invoice/sales/invoice/list", params);
    }
};
export const relevanceInput = {
    getReceipts: function (params) {
        return http.get("/erp/procure/erpPurchaseWarehousingRecord/list", params);
    },
    retreat: function (params) {
        return http.get("/erp/procure/retreat/erpPurchaseRetreatOutbound/list", params);
    },
    supplierList: function (params) {
        return http.get("/erp/supplier/erpSupplierProduction/list", params);
    },
    detailsInput: function (params) {
        return http.get("/erp/invoice/purchase/erpPurchaseInvoiceReceipt/detailByinvoiceNo", params);
    },
    saveInput: function (data) {
        return http.post("/erp/invoice/purchase/erpPurchaseInvoice/create", data);
    },
    getList: function (params) {
        return http.get("/erp/invoice/purchase/erpPurchaseInvoice/list", params);
    },
    delList: function (params) {
        return http.delete("/erp/invoice/purchase/erpPurchaseInvoice/delete", params);
    },
    detailList: function (params) {
        return http.get("/erp/invoice/purchase/erpPurchaseInvoice/detailById", params);
    },
    delGoods: function (params) {
        return http.delete("/erp/invoice/purchase/erpPurchaseInvoiceForm/delete", params);
    },
    delInvoice: function (params) {
        return http.delete("/erp/invoice/purchase/erpPurchaseInvoiceUnion/delete", params);
    },
    auditLists: function (params) {
        return http.get("/erp/invoice/purchase/erpPurchaseInvoiceApproval/list", params);
    },
    logList: function (params) {
        return http.get("/erp/log/operatelog/list", params);
    }
}

