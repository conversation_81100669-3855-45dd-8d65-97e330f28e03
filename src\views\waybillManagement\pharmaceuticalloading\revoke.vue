<!--
 * @Author: saya
 * @Date: 2023-10-26 14:42:16
 * @LastEditors: dcx <EMAIL>
 * @LastEditTime: 2023-11-06 09:00:59
 * @FilePath: \zxhy-4pl-font-pc\src\views\waybillManagement\pharmaceuticalloading\incubator.vue
 * @Description: 
-->
<template>
    <div style="width: 700px;margin: 0 auto;">
        <el-card class='box-card mb10' shadow='never'>
            <template #header>
                <div class='card-header' style='color: #5670fe; display: flex; border-bottom: 1px solid #e6ebf5'>
                    <div style='background-color: #5670fe; width: 5px; height: 20px; margin-right: 10px'></div>
                    <span>司机信息</span>
                </div>
            </template>
            <el-descriptions :column='2'>
                <el-descriptions-item label='司机姓名：'>{{ continueLoadingList.driver.driverName }}
                </el-descriptions-item>
                <el-descriptions-item label='司机电话：'>{{ continueLoadingList.driverPhone }}</el-descriptions-item>
                <el-descriptions-item label='车牌号码：'>{{ continueLoadingList.carCode }}</el-descriptions-item>
                <el-descriptions-item label='车辆类型：'>{{ continueLoadingList.carClass ? '普通货车' : '冷藏车' }}
                </el-descriptions-item>
                <el-descriptions-item label='传感器：' v-if='continueLoadingList.carClass === 1'>
                    {{ continueLoadingList.sensor }}
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <el-card class='box-card mb10' shadow='never'>
            <template #header>
                <div class='card-header' style='color: #5670fe; display: flex; border-bottom: 1px solid #e6ebf5'>
                    <span>货物</span>
                </div>
            </template>
            <div style='width: 500px'>
                <div style='display: flex'>
                    <text style='height: 30px; line-height: 30px; font-size: 16px; font-weight: 400;'>集货区域
                        <span style='margin-left: 10px;'>普通件{{ loadingRecordList.countyList.length }}件 {{
                            loadingRecordList.incubatorList.length }}箱</span>
                    </text>
                    <div style='margin-left: 100px;margin-bottom: 10px;'>
                        <el-button plain @click="renewGoodsType">
                            返回
                        </el-button>
                        <el-button type='primary' plain @click="revocationEvent('', 0)">全部撤销</el-button>
                    </div>
                </div>
                <el-collapse v-model='activeNames' @change='handleChange' style="width: 470px;">
                    <div style="display: flex;" v-for="(item, index) in loadingRecordList.countyList" :key="item.id">
                        <el-collapse-item style="width: 500px;">
                            <template #title>
                                <el-icon size="20" color="#D81E06" @click="revocationEvent(item, 2)">
                                    <DeleteFilled />
                                </el-icon>
                                <div @click="areaObtainWaybill(loadingRecordList, item, index)" 
                                style='display: flex; justify-content: space-between;  margin-left: 10px;
                                    height: 30px; line-height: 30px;width: 460px;'>
                                    <div style='font-size: 14px; font-weight: 600'>
                                        {{ item.provinceName }}{{ item.cityName }}{{ item.countyName }}
                                    </div>
                                    <div>( {{ item.transOrderNum }})单 {{ item.goodsTotalNum }}件</div>
                                </div>
                            </template>
                            <div style='display: flex;justify-content: space-between;'
                                v-for="(listItem, index1) in item.list" :key="index1">
                                <el-collapse-item title='Consistency' :name=listItem.transOrderNo>
                                    <template #title>
                                        <el-icon size="20" color="#D81E06" @click="revocationEvent(listItem, 3)">
                                            <DeleteFilled />
                                        </el-icon>
                                        <div @click="areaObtainBoxCode(loadingRecordList, item, index, listItem, index1)"
                                            style='display: flex; justify-content: space-between; margin-left: 20px;
                                                height: 30px; line-height: 30px;width: 430px;'>
                                            <div style='font-size: 14px; font-weight: 500;'>
                                                {{ listItem.transOrderNo }}
                                            </div>
                                            <div>{{ listItem.goodsTotalNum }}件</div>
                                        </div>
                                    </template>
                                    <div v-for="(trans, index2) in listItem.listTrans" :key="index2"
                                        style='font-size: 12px;display: flex; justify-content: space-between;'>
                                        <el-icon size="20" color="#D81E06" @click="revocationEvent(trans, 4)">
                                            <DeleteFilled />
                                        </el-icon>
                                        <div style="display: flex;justify-content: space-between;width: 420px">
                                            <div>
                                                {{ trans.code }}
                                            </div>
                                            <div>
                                                {{ trans.codeDesc }}
                                            </div>
                                        </div>
                                    </div>
                                </el-collapse-item>
                            </div>
                        </el-collapse-item>
                    </div>
                    <el-collapse-item v-for="(item, index) in loadingRecordList.incubatorList" :key="item.id">
                        <template #title>
                            <el-icon size="20" color="#D81E06" @click="revocationEvent(item, 1)">
                                <DeleteFilled />
                            </el-icon>
                            <div style='width: 450px; display: flex; justify-content: space-between;
                                                     height: 30px; line-height: 30px;margin-left: 10px;'>
                                <div style='font-size: 14px; font-weight: 600'>{{ item.incubatorName }}</div>
                                <div>( {{ item.transOrderNum }})单 {{ item.goodsTotalNum }}件</div>
                            </div>
                        </template>
                        <div style='margin-left: 20px' v-for="(coun, index1) in item.countyList" :key="index1">
                            <el-collapse-item name='3'>
                                <template #title>
                                    <div @click="obtainWaybill(item, index, coun, index1)"
                                        style='width: 450px; display: flex; justify-content: space-between; height: 30px; line-height: 30px'>
                                        <div style='font-size: 14px; font-weight: 500'>
                                            {{ coun.provinceName }}{{ coun.cityName }}{{ coun.countyName }}</div>
                                        <div>{{ coun.goodsTotalNum }}件</div>
                                    </div>
                                </template>
                                <div style='margin-left: 20px; font-size: 12px' v-for="(listItem, index2) in coun.list"
                                    :key="index2">
                                    <el-collapse-item name='4'>
                                        <template #title>
                                            <div @click="obtainBoxCode(item, index, coun, index1, listItem, index21)"
                                                style='width: 450px; display: flex; justify-content: space-between; height: 30px; line-height: 30px'>
                                                <div style='font-size: 14px; font-weight: 500'>{{ listItem.transOrderNo
                                                }}
                                                </div>
                                                <div> {{ listItem.goodsTotalNum }}件</div>
                                            </div>
                                        </template>
                                        <div v-for="(trans, index3) in listItem.listTrans" :key="index3"
                                            style='margin-left: 20px; font-size: 12px;display: flex; justify-content: space-between;'>
                                            <div>
                                                {{ trans.code }}
                                            </div>
                                            <div>
                                                {{ trans.codeDesc }}
                                            </div>
                                        </div>
                                    </el-collapse-item>
                                </div>
                            </el-collapse-item>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </el-card>
    </div>
</template>

<script>
import { DeleteFilled } from '@element-plus/icons-vue'
import pharmaceuticalloading from '@/api/waybillManagement/pharmaceuticalloading';
import { ElMessage, ElMessageBox } from 'element-plus';

export default {
    name: "",
    components: {
        DeleteFilled
    },
    props: {
        continueLoadingList: {
            type: Array
        },
        citiesList: {
            type: Array
        },
    },
    data() {
        return {
            loadingRecordList: [],
            assignId: ''
        }
    },
    methods: {
        // 返回事件
        renewGoodsType() {
            this.loadingRecordList = [];
            // this.getLoadingDetails(this.continueLoadingList);
            this.$emit('revocationRetuen')
        },
        // 装车撤销
        revocationEvent(val, type) {
            let data = {}
            if (type === 0) {
                data = {
                    assignId: this.assignId,
                    type: type,
                }
            } else if (type === 1) {
                data = {
                    assignId: this.assignId,
                    type: type,
                    recordId: val.id,
                }
            } else if (type === 2) {
                data = {
                    assignId: this.assignId,
                    type: type,
                    countyId: val.countyId,
                }
            } else if (type === 3) {
                data = {
                    assignId: this.assignId,
                    type: type,
                    transOrderNo: val.transOrderNo,
                }
            } else if (type === 4) {
                data = {
                    assignId: this.assignId,
                    type: type,
                    code: val.code,
                }
            }

            ElMessageBox.confirm('是否进行撤销？', '回退修改', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                pharmaceuticalloading.postAssignRevoke(data).then((res) => {
                    if (res.code == 200) {
                        this.getLoadingDetails(this.continueLoadingList)
                        ElMessage({
                            type: 'success',
                            message: '撤销成功!'
                        });
                    }
                });
            }).catch(() => {
                ElMessage({
                    type: 'info',
                    message: '装车已取消！'
                });
            });


        },
        handleChange(val) {
            console.log(val);
        },
        // 装车记录详情
        getLoadingDetails() {
            pharmaceuticalloading.getassignRecordQueryById({
                id: this.continueLoadingList.id
            }).then((res) => {
                if (res.code == 200) {
                    this.loadingRecordList = res.data;
                    this.assignId = res.data.id
                }
            }).catch(() => {
                ElMessage({
                    type: 'info',
                    message: '查询失败！'
                });
            });
        },
        // 点击集货区获取运单
        areaObtainWaybill(item, coun, index1) {
            pharmaceuticalloading.getDetailGroupOrder({
                batchNo: item.batchNo,
                countyId: coun.countyId
            }).then(res => {
                if (res.code === 200) {
                    const data = res.data.map(item => {
                        item.checked = false;
                        return item;
                    });
                    coun.list = data;
                }
            });
            // console.log(this.sensorList[index].countyList[index1]);
        },
        // 点击集货区运单获取数据
        areaObtainBoxCode(item, coun, index1, listItem, index2) {
            pharmaceuticalloading.getDetailGroupCode({
                batchNo: item.batchNo,
                countyId: coun.countyId,
                transOrderNo: listItem.transOrderNo
            }).then(res => {
                if (res.code === 200) {
                    const data = res.data.map(item => {
                        item.checked = false;
                        return item;
                    });
                    listItem.listTrans = data;
                }
            });
            // console.log(this.sensorList[index].countyList[index1].listItem[index2]);
        },

        // 点击运单获取数据
        obtainBoxCode(item, index, coun, index1, listItem, index2) {
            pharmaceuticalloading.getDetailGroupCode({
                recordId: item.id,
                countyId: coun.countyId,
                transOrderNo: listItem.transOrderNo
            }).then(res => {
                if (res.code === 200) {
                    const data = res.data.map(item => {
                        item.checked = false;
                        return item;
                    });
                    listItem.listTrans = data;
                }
            });
            // console.log(this.sensorList[index].countyList[index1].listItem[index2]);
        },
        // 点击保温箱省市区获取数据
        obtainWaybill(item, index, coun, index1) {
            pharmaceuticalloading.getDetailGroupOrder({
                recordId: item.id,
                countyId: coun.countyId
            }).then(res => {
                if (res.code === 200) {
                    const data = res.data.map(item => {
                        item.checked = false;
                        return item;
                    });
                    coun.list = data;
                }
            });
            // console.log(this.sensorList[index].countyList[index1]);
        },
    }
}
</script>
<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}

::v-deep {
    .el-collapse-item__wrap {
        border-bottom: 0px;
    }

    .el-collapse-item__header {
        border-bottom: 0px;
        height: 30px;
    }

    .el-collapse-item__content {
        padding-bottom: 0px;
    }
}

::v-deep {
    .el-tabs__nav-scroll {
        padding-left: 32px;
    }

    .el-radio:last-child {
        margin-right: 30px;
    }

    .el-radio {
        margin-bottom: 10px;

        .el-radio__label {
            .el-input__wrapper {
                background: none;
                box-shadow: none;
            }
        }
    }
}
</style>