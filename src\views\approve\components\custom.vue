<template>
    <div class="app-container" style="width: 100%;">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog title="客户管理详情" v-model="visible" v-if="visible" width="90%" :before-close="beforeClose"
            :show-close="!modalLoading" :append-to-body="ture" destroy-on-close>
            <div v-loading="modalLoading">
                <!-- <el-button style="margin-top: 3px;position: absolute;right: 50px;top: 10px;"
                    @click="handleFileList">附件列表</el-button> -->
                <div style="display: flex;justify-content: space-between;padding: 20px 20px;">
                    <span style="color:#333;font-size: 18px;">{{ data.document }}</span>
                    <span style="color:#333;font-weight: bold;font-size: 18px;" v-if="type != 'record'">{{ '审批中' }}</span>
                    <span style="color:#333;font-weight: bold;font-size: 18px;" v-if="type == 'record'">{{ '审批完成' }}</span>
                </div>
                <el-collapse v-model="activeNames" @change="handleChange">
                    <el-collapse-item title="基本信息" name="1">
                        <template #title>
                            <span class="col_title">基本信息</span>
                        </template>
                        <el-descriptions :column="4" border>
                            <el-descriptions-item label="企业名称" label-align="left" align="center" label-class-name="my-label"
                                class-name="my-content">{{ listStep1.enterpriseName || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="统一社会信用代码" label-align="left" align="center">{{
                                listStep1.socialCreditCode || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="资质类别" label-align="left" align="center">{{
                                formDict(qualificationList, listStep1.credentialType)
                            }}</el-descriptions-item>
                            <el-descriptions-item label="拼音码" label-align="left" align="center">{{
                                listStep1.pinyinCode || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="自编码" label-align="left" align="center">{{
                                listStep1.selfCoding || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="营业期限" label-align="left" align="center"> {{ listStep1.businessTerm
                                ?
                                moment(listStep1.businessTerm).format('YYYY-MM-DD') : '--' }}</el-descriptions-item>
                            <el-descriptions-item label="住所" label-align="left" align="center">
                                {{ listStep1.residence || '--' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="经营范围" label-align="left" align="center">
                                {{ listStep1.businessScope || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="发证机关" label-align="left" align="center">
                                {{ listStep1.issuingOffice || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="联系电话" label-align="left" align="center">
                                {{ listStep1.contactNumber || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="经营方式" label-align="left" align="center">
                                {{ formDict(modeOfList, listStep1.natureBusiness)
                                }}</el-descriptions-item>
                            <el-descriptions-item label="经营类型" label-align="left" align="center">
                                {{ formDict(businessList, listStep1.businessType)
                                }}</el-descriptions-item>
                            <el-descriptions-item label="业务类型" label-align="left" align="center">
                                {{ formDict(tradeType, listStep1.tradeType)
                                }}</el-descriptions-item>
                            <el-descriptions-item label="开户银行" label-align="left" align="center">
                                {{ listStep1.bankDeposit || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="开户许可证号" label-align="left" align="center">
                                {{ listStep1.accountOpeningLicenseCode || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="银行账号" label-align="left" align="center">
                                {{ listStep1.accountNumber || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="发证日期" label-align="left" align="center">
                                {{ listStep1.issuingTime
                                    ?
                                    moment(listStep1.issuingTime).format('YYYY-MM-DD') : '--' }}</el-descriptions-item>
                            <el-descriptions-item label="开票名称" label-align="left" align="center">
                                {{ listStep1.billName || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="开票地址" label-align="left" align="center">
                                {{ listStep1.billAddress || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="所在区域" label-align="left" align="center">
                                {{ listStep1.provinceName || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="备注" label-align="left" align="center">
                                {{ listStep1.remark || '--'
                                }}</el-descriptions-item>
                        </el-descriptions>
                        <el-descriptions :column="1" border v-if="listStep1.credentialType == '1'"
                            class="licenseImg_listStep1">
                            <el-descriptions-item label="经营范围配置" label-align="left" align="center">
                                <div style="display: flex;">
                                    <div style="flex:1;display: flex;align-items: center;">
                                        <el-checkbox label="一类器械" name="type"
                                            v-model="listStep1.apparatusFirst"></el-checkbox>
                                        <el-button type="primary" size="small" style="margin-left: 50px;"
                                            @click="() => handleDetail(listStep1, 'apparatusFirstScopes')">查看详情</el-button>
                                    </div>
                                    <div style="flex:1;display: flex;align-items: center;">
                                        <el-checkbox label="消杀" name="type" v-model="listStep1.sterilized"></el-checkbox>
                                        <el-button type="primary" size="small" style="margin-left: 50px;"
                                            @click="() => handleDetail(listStep1, 'sterilizedScopes')">查看详情</el-button>
                                    </div>
                                </div>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>
                    <el-collapse-item title="质量信息" name="2" class="step2">
                        <template #title>
                            <span class="col_title">质量信息</span>
                        </template>
                        <div class="btn_cmt" v-if="listStep1.credentialType !== '2'">
                            <el-button
                                :type="tabKey === Object.entries(typeDict).find(([key, val]) => val === item.type)[0] ? 'primary' : 'default'"
                                v-for="item in listStep2" :key="item.id"
                                @click="() => handleTabClick(Object.entries(typeDict).find(([key, val]) => val === item.type)[0])">{{
                                    item.type }}</el-button>
                        </div>
                        <el-descriptions :column="4" border v-if="listStep1.credentialType !== '2'">
                            <el-descriptions-item label="许可证编号" label-align="left" align="center"
                                label-class-name="my-label" class-name="my-content">{{ listStep2_item.licenseNo || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="注册地址" label-align="left" align="center">{{
                                listStep2_item.licenseAddress || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="仓库地址" label-align="left" align="center">{{
                                listStep2_item.warehouseAddress || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="许可证范围" label-align="left" align="center"><el-button type="primary"
                                    size="small"
                                    @click="() => handleDetail(listStep2_item)">查看详情</el-button></el-descriptions-item>
                            <el-descriptions-item label="发证日期" label-align="left" align="center">
                                {{ listStep2_item.licenseStartTime ?
                                    moment(listStep2_item.licenseStartTime).format('YYYY-MM-DD') : '--' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="有效期" label-align="left" align="center">
                                {{ listStep2_item.licenseValidity ?
                                    moment(listStep2_item.licenseValidity).format('YYYY-MM-DD') : '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="发证机关" label-align="left" align="center">{{
                                listStep2_item.licenseOffice || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="法人" label-align="left" align="center">{{
                                listStep2_item.licenseLegalPerson || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="企业负责人" label-align="left" align="center">{{
                                listStep2_item.licenseDirector || '--'
                            }}</el-descriptions-item>
                            <el-descriptions-item label="质量负责人" label-align="left" align="center">{{
                                listStep2_item.qualityDirector || '--'
                            }}</el-descriptions-item>
                        </el-descriptions>
                        <el-descriptions :column="1" border v-if="listStep1.credentialType !== '2'" class="licenseImg">
                            <el-descriptions-item label="许可证图片" label-align="left" align="center">
                                <div class="demo-image__preview">
                                    <el-image class="imgView" :src="item.url" fit="cover" :preview-src-list="[item.url]"
                                        v-for="item in listStep2_item.licenseImg" :key="item" />
                                </div>
                            </el-descriptions-item>
                        </el-descriptions>
                        <el-descriptions :column="4" border v-if="listStep1.credentialType == '2'">
                            <el-descriptions-item label="执业许可证名称" label-align="left" align="center"
                                label-class-name="my-label" class-name="my-content">{{ listStep2_item.zylicenseName || '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="许可证地址" label-align="left" align="center">{{
                                listStep2_item.zylicenseAddress || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="经营性质" label-align="left" align="center">{{
                                formDict(businessNatureList, listStep2_item.business)
                            }}</el-descriptions-item>
                            <el-descriptions-item label="法人" label-align="left" align="center">{{
                                listStep2_item.zylicenseLegalPerson || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="负责人" label-align="left" align="center">{{
                                listStep2_item.zylicenseDirector || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="发证日期" label-align="left" align="center">
                                {{ listStep2_item.zylicenseStartTime ?
                                    moment(listStep2_item.zylicenseStartTime).format('YYYY-MM-DD') : '--' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="有效期" label-align="left" align="center">
                                {{ listStep2_item.zylicenseValidity ?
                                    moment(listStep2_item.zylicenseValidity).format('YYYY-MM-DD') : '--'
                                }}</el-descriptions-item>
                            <el-descriptions-item label="登记号码" label-align="left" align="center">{{
                                listStep2_item.zylicenseNo || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="发证机关" label-align="left" align="center">{{
                                listStep2_item.zylicenseOffice || '--' }}</el-descriptions-item>
                            <el-descriptions-item label="诊疗科目" label-align="left" align="center">{{
                                fy_zylicenseProject(listStep2_item.zylicenseProject)
                            }}</el-descriptions-item>
                            <el-descriptions-item label="质量范围" label-align="left" align="center">{{
                                fy_zylicenseRange(listStep2_item.zylicenseRange)
                            }}</el-descriptions-item>
                        </el-descriptions>
                    </el-collapse-item>
                    <el-collapse-item title="采购授权委托书" name="5">
                        <template #title>
                            <span class="col_title">采购授权委托书</span>
                        </template>
                        <el-table :data="listStep6" border>
                            <el-table-column align="center" label="序号" width="80">
                                <template #default="scope">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <el-table-column label="委托人姓名" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <span>{{ scope.row.delegateName }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="身份证号" align="center" :show-overflow-tooltip="true" min-width="200">
                                <template #default="scope">
                                    <span>{{ scope.row.certificateCard }}</span>
                                </template>
                            </el-table-column>、
                            <el-table-column label="有效期" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <span>{{
                                        (scope.row.effectiveTime ?
                                            moment(scope.row.effectiveTime).format('YYYY-MM-DD') :
                                            undefined) || '--' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="授权日期" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <span>{{
                                        (scope.row.empowerTime ?
                                            moment(scope.row.empowerTime).format('YYYY-MM-DD') :
                                            undefined) || '--' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="区域限制" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <span>{{
                                        filterAddress(scope.row.addressList) || '--' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="委托书" align="center" width="330">
                                <template #default="scope">
                                    <span
                                        style="color:#2A76F8;cursor: pointer;white-space: nowrap;overflow:hidden;text-overflow: ellipsis;display: inline-block;width: 90%;"
                                        v-if="scope.row.remark" @click="() => handleUploadViewFile(scope.row.remark.url)">{{
                                            scope.row.remark.name }}</span>
                                    <span v-else>--</span>
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <el-button link type="primary" @click="handleOrgDetail(scope.row)">详情</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                    <el-collapse-item title="质量保证协议书" name="6">
                        <template #title>
                            <span class="col_title">质量保证协议书</span>
                        </template>
                        <el-table :data="listStep5" border>
                            <el-table-column align="center" label="序号" width="80">
                                <template #default="scope">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <el-table-column label="有效期" align="center" :show-overflow-tooltip="true" min-width="200">
                                <template #default="scope">
                                    <span>{{ scope.row.qualityDate ? moment(scope.row.qualityDate).format('YYYY-MM-DD') :
                                        '--' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="证书图片" align="center" min-width="330">
                                <template #default="scope">
                                    <span
                                        style="color:#2A76F8;cursor: pointer;white-space: nowrap;overflow:hidden;text-overflow: ellipsis;display: inline-block;width: 90%;"
                                        v-if="scope.row.qualityCode"
                                        @click="() => handleUploadViewFile(scope.row.qualityCode.url)">{{
                                            scope.row.qualityCode.name }}</span>
                                    <span v-else>--</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                    <el-collapse-item title="GSP证书" name="3">
                        <template #title>
                            <span class="col_title">GSP证书</span>
                        </template>
                        <el-table :data="listStep3" border>
                            <el-table-column align="center" label="序号" width="80">
                                <template #default="scope">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <el-table-column label="证书编号" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <span>{{ scope.row.gmpCertificateNo }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="证书地址" align="center" :show-overflow-tooltip="true" min-width="200">
                                <template #default="scope">
                                    <span>{{ scope.row.gmpCertificateAddress }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="证书图片" align="center" width="330">
                                <template #default="scope">
                                    <span
                                        style="color:#2A76F8;cursor: pointer;white-space: nowrap;overflow:hidden;text-overflow: ellipsis;display: inline-block;width: 90%;"
                                        v-if="scope.row.gmpCertificatePicture"
                                        @click="() => handleUploadViewFile(scope.row.gmpCertificatePicture.url)">{{
                                            scope.row.gmpCertificatePicture.name }}</span>
                                    <span v-else>--</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="证书有效期" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <span>{{ scope.row.gmpExpiredTime ?
                                        moment(scope.row.gmpExpiredTime).format('YYYY-MM-DD') : '--' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="证书授权范围" align="center" :show-overflow-tooltip="true" width="200">
                                <template #default="scope">
                                    <span>{{ filterArr(licenseScopeList, 'id', scope.row.gmpCertificateScope) ||
                                        '--' }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                    <el-collapse-item title="附件列表" name="4">
                        <template #title>
                            <span class="col_title">附件列表</span>
                        </template>
                        <el-table ref="multipleTableRef_file" :data="listStep4" border>
                            <el-table-column align="center" label="序号" width="80">
                                <template #default="scope">
                                    {{ scope.$index + 1 }}
                                </template>
                            </el-table-column>
                            <el-table-column label="类型" align="center" :show-overflow-tooltip="true" width="120">
                                <template #default="scope">
                                    <span>{{ formDict(manufacturerType, scope.row.fileType) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="文件类型" align="center" :show-overflow-tooltip="true" min-width="330">
                                <template #default="scope">
                                    <span>{{ formDict(directoryFileName, scope.row.cardName) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="文件名称" align="center" width="330">
                                <template #default="scope">
                                    <div>
                                        <div v-if="scope.row.fileNameUrl">
                                            <span v-for="(item, index) in scope.row.fileNameUrl" :key="item.uid"
                                                class="fileName_t"><span
                                                    @click="() => handleUploadViewFile(scope.row.fileNameUrl[index].url)">{{
                                                        item.name }}</span></span>
                                        </div>
                                        <span v-else>--</span>
                                    </div>

                                </template>
                            </el-table-column>
                            <el-table-column label="是否必传" align="center" :show-overflow-tooltip="true" width="120">
                                <template #default="scope">
                                    <span>{{ scope.row.isUpload === '1' ? '是' : scope.row.isUpload === '0' ? '否' : '--'
                                    }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="是否多张" align="center" :show-overflow-tooltip="true" width="120">
                                <template #default="scope">
                                    <span>{{ scope.row.isPage === '1' ? '是' : scope.row.isPage === '0' ? '否' :
                                        '--'
                                    }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="备注" align="center" :show-overflow-tooltip="true" min-width="230">
                                <template #default="scope">
                                    <span>{{ scope.row.remark || '--' }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-collapse-item>
                  <el-collapse-item v-if="deFlag" name="7" title="操作日志" @click="handleChange_msg">
                        <template #title>
                            <span class="col_title">操作日志</span>
                        </template>
                        <div style="max-height:500px;overflow-y:scroll">
                            <LogQuery ref="childRef" />
                        </div>
                    </el-collapse-item>
                  <el-collapse-item v-if="type != 'record'&&deFlag==true" name="8" title="审批意见"
                                    @click="handleChange_msg">
                        <template #title>
                            <span class="col_title">审批意见</span>
                        </template>
                        <Audit ref="auditRef" @refresh="getList" />
                    </el-collapse-item>
                </el-collapse>

            </div>
            <template #footer v-if="!modalLoading">
                <div class="dialog-footer">
                  <el-button v-if="type != 'record'&&deFlag==true" type="primary" @click="handleSubmit">确认</el-button>
                    <el-button @click="beforeClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <el-drawer v-model="fileListVisible" title="附件列表" size="60%" direction="rtl"
            :before-close="() => fileListVisible = false" v-if="fileListVisible">
            <div style="display:flex;justify-content:end">
                <el-button type="primary" style="margin-top: 10px; margin-bottom:20px;margin-right:20px"
                    @click="handleExportFile" :disabled="!fileListChooseList.length">导出</el-button>
            </div>
            <el-table :data="fileListData" border @selection-change="handleDetailFileListChoose">
                <el-table-column type="selection" min-width="55" align="center" />
                <el-table-column label="证件名称" prop="cardName" show-overflow-tooltip :show-overflow-tooltip="true"
                    align="center" :formatter="(row) => row.cardName || '--'" />
                <el-table-column label="编号" prop="fileCoder" show-overflow-tooltip :show-overflow-tooltip="true"
                    align="center" />
                <el-table-column label="证件图片" prop="fileName" show-overflow-tooltip :show-overflow-tooltip="true"
                    align="center" min-width="280">
                    <template #default="scope">
                        <el-button link type="primary" @click="() => handleUploadViewFile(scope.row.fileUrl)">{{
                            scope.row.fileName }}</el-button>
                    </template>
                </el-table-column>>
                <el-table-column label="有效期" prop="fileEndDate" show-overflow-tooltip :show-overflow-tooltip="true"
                    align="center" :formatter="(row) => moment(row.fileEndDate).format('YYYY-MM-DD')" />
                <el-table-column label="范围" prop="range" show-overflow-tooltip :show-overflow-tooltip="true" align="center"
                    :formatter="(row) => row.range || '--'" />
                <el-table-column label="类型" prop="busType" show-overflow-tooltip :show-overflow-tooltip="true"
                    align="center"
                    :formatter="(row) => (row.busType == '1' || row.busType == '2') ? row.busType == '1' ? '普药' : row.busType == '2' ? '特药' : '--' : row.busType" />
                <el-table-column label="备注" prop="remark" show-overflow-tooltip :show-overflow-tooltip="true" align="center"
                    :formatter="(row) => row.remark || '--'" />
                <el-table-column label="归档时间" prop="createDate" show-overflow-tooltip :show-overflow-tooltip="true"
                    align="center" :formatter="(row) => moment(row.createDate).format('YYYY-MM-DD')" />
            </el-table>
            <div style="display:flex;justify-content:end;margin-top:20px">
                <pagination v-show="fileListDataTotal > 0" :total="fileListDataTotal"
                    v-model:page="fileListDataPage.current" v-model:limit="fileListDataPage.size"
                    @pagination="handleFileList" />
            </div>

        </el-drawer>
        <el-dialog v-model="licenseScopeVisible" v-if="licenseScopeVisible" title="许可证范围" width="60%"
            :before-close="() => licenseScopeVisible = false" style="padding:0 30px">
            <el-table :data="licenseScopeListData" style="width: 100%;margin-bottom: 20px" row-key="id" border
                :select-on-indeterminate="true" v-loading="lisloading" :tree-props="{ children: 'children' }">
                <el-table-column label="选择" align=center min-width="140">
                    <template #default="scope">
                        <el-checkbox v-model="checkedKeys" :label="scope.row.id"
                            @change="(isChoose) => handleCheckChange(isChoose, scope.row)">&nbsp;</el-checkbox>
                    </template>
                </el-table-column>
                <!-- <el-table-column type="selection" width="55" :reserve-selection="true"
                    :selectable="selectable"></el-table-column> -->
                <!-- <el-table-column type="selection" width="55" align="center"> </el-table-column> -->
                <el-table-column prop="massName" label="质量名称" min-width="250" align="center">
                </el-table-column>
                <el-table-column prop="massNo" label="质量编号" min-width="180" align="center">
                </el-table-column>
                <el-table-column prop="massType" label="属性分类" align="center">
                    <template #default="scope">
                        {{ scope.row.isStop == 1 ? "新范围" : "旧范围" }}
                    </template>
                </el-table-column>
                <el-table-column prop="isStop" label="状态" align="center">
                    <template #default="scope">
                        {{ scope.row.isStop == 1 ? "正常" : "停用" }}
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" align="center" min-width="150px"
                    :formatter="row => row.remark ? row.remark : '--'" />
            </el-table>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="licenseScopeVisible = false">取消</el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog v-model="entrustVisible" title="委托书详情" width="65%" :before-close="() => entrustVisible = false"
            v-if="entrustVisible">
            <el-descriptions :column="4" border class="dialog_dir">
                <el-descriptions-item label="类型" label-align="left" align="center" label-class-name="my-label"
                    class-name="my-content">{{ formDict(drugTypeList, entrustData.status)
                    }}</el-descriptions-item>
                <el-descriptions-item label="委托人姓名" label-align="left" align="center">{{
                    entrustData.delegateName || '--' }}</el-descriptions-item>
                <el-descriptions-item label="身份证号" label-align="left" align="center">{{
                    entrustData.certificateCard || '--' }}</el-descriptions-item>
                <el-descriptions-item label="有效期" label-align="left" align="center">
                    {{ entrustData.effectiveTime ?
                        moment(entrustData.effectiveTime).format('YYYY-MM-DD') : '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="授权日期" label-align="left" align="center">
                    {{ entrustData.empowerTime ?
                        moment(entrustData.empowerTime).format('YYYY-MM-DD') : '--'
                    }}</el-descriptions-item>
                <el-descriptions-item label="区域限制" label-align="left" align="center">{{
                    filterAddress(entrustData.addressList) || '--' }}</el-descriptions-item>
                <el-descriptions-item label="委托商品范围" label-align="left" align="center">{{ entrustData.isEntrustAll == '1' ?
                    `所有${formDict(drugTypeList,
                        entrustData.status)}药品` : '部分商品' }}</el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="2" border class="licenseImg_listStep2">
                <el-descriptions-item label="委托书" label-align="left" align="center">
                    <div class="demo-image__preview">
                        <el-image class="imgView" :src="entrustData.remark.url" fit="cover"
                            :preview-src-list="[entrustData.remark.url]" />
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="委托人身份证" label-align="left" align="center">
                    <div class="demo-image__preview">
                        <el-image class="imgView" :src="entrustData.delegateCard.url" fit="cover"
                            :preview-src-list="[entrustData.delegateCard.url]" />
                    </div>
                </el-descriptions-item>
            </el-descriptions>
            <!-- 表格数据 -->
            <h4 class="shopDetailTitle" v-if="entrustData.isEntrustAll == '2'">商品详情</h4>
            <el-table :data="entrustData.delegateCommodityDTOS" border style="margin-top: 10px;"
                v-if="entrustData.isEntrustAll == '2'">
                <el-table-column label="自编码" prop="commoditySelfCode" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="商品名" prop="tradeName" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="分类" prop="commodityType" :show-overflow-tooltip="true" align="center"
                    :formatter="(row) => commodityTypeDict[row.commodityType]" min-width="120" />
                <el-table-column label="剂型" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="规格" prop="packageSpecification" :show-overflow-tooltip="true" align="center"
                    min-width="120" />
                <el-table-column label="基本单位" prop="basicUnit" :show-overflow-tooltip="true" align="center"
                    min-width="140" />
                <el-table-column label="生产厂家" prop="manufactureName" align="center" min-width="120"
                    :show-overflow-tooltip="true" />
                <el-table-column label="生产地址" prop="originPlace" align="center" min-width="120" />

            </el-table>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="() => entrustVisible = false">取消</el-button>
                </span>
            </template>
        </el-dialog>
        <viewImg v-if="uploadVisibleFile" :visible="uploadVisibleFile" :src="uploadViewImgUrlFile"
            :beforeClose="() => uploadVisibleFile = false" />
    </div>
</template>

<script setup>

import {defineProps, getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import moment from 'moment'
import manufacturerManagement from '@/api/erp/manufacturerManagement'
import custom from '@/api/erp/custom'
import {isArray} from 'lodash';
import Audit from '@/components/detailsForm/audit.vue'
import LogQuery from '@/components/detailsForm/logQuery.vue'
import {drugApi} from "@/api/model/commodity/drug/index";

const { proxy } = getCurrentInstance();
const typeDict = {
    '1': '药品',
    '2': '三类',
    '3': '一类',
    '4': '二类',
    '5': '食品',
    '6': '消杀',
}
const commodityTypeDict = {
    '1': '药品',
    '2': '器械',
    '3': '消杀',
    '4': '食品'
}
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    beforeClose: {
        type: Object,
        default: () => { }
    },
    data: {
        type: Object,
        default: () => { }
    },
    getList: {
        type: Object,
        default: () => { }
    },
    type: {
        type: String,
        default: ''
    },
  deFlag: {
    type: Boolean,
    default: true
  }
})
const {visible, beforeClose, data, getList, type, deFlag} = toRefs(props)
const childRef = ref(null)
const auditRef = ref(null)
const modalLoading = ref(false)
const activeNames = ref(['1', '2', '3', '4', '5', '6', '7', '8'])
const fileListVisible = ref(false)
const fileDraLoading = ref(false)
const fileListData = ref([])
const uploadViewImgUrlFile = ref(undefined)
const uploadVisibleFile = ref(false)
const fileListDataTotal = ref(0)
const licenseScopeVisible = ref(false)
const licenseScopeListData = ref([])
const lisloading = ref(false)
const checkedKeys = ref([])
const licenseScopeList = ref([])
const listStep1 = ref({})
const listStep2 = ref([])
const listStep3 = ref([])
const listStep4 = ref([])
const listStep5 = ref([])
const listStep6 = ref([])
const delFlagList = ref([])
const reviewStatus = ref([])
const manufacturerType = ref([])
const shopType = ref([])
const directoryFileName = ref([])
const tabKey = ref('1')
const listStep2_item = ref({})
const fileListChooseList = ref([])
const qualificationList = ref([])
const modeOfList = ref([])
const businessList = ref([])
const sysAreas = ref([])
const entrustVisible = ref(false)
const entrustData = ref({})
const drugTypeList = ref([])
const businessNatureList = ref([])
const zylicenseProjectOption = ref([])
const massRangeOption = ref([])
const tradeType = ref([])
const datas = reactive({
    fileListDataPage: {
        current: 1,
        size: 10,
    },
})
const { fileListDataPage } = toRefs(datas)
const handleSubmit = async () => {
    if (!auditRef.value.form?.status) return proxy.msgError('请先选择审批结果！')
    await auditRef.value.formSub(data.value?.id);
    await beforeClose.value()
}
const treeOrArr = []
const tree_arr = (option) => {
    option.forEach(v => {
        treeOrArr.push(v)
        if (v.children?.length) {
            tree_arr(v.children)
        }
    })
    return treeOrArr
}
const getRanges = async () => {
    let res = await custom.getRange()
    if (res.code == 200) {
        massRangeOption.value = tree_arr(res.data)
    }
}

const getTerats = async () => {
    let res = await custom.getTerat()
    if (res.code == 200) {
        zylicenseProjectOption.value = tree_arr(res.data)
    }
}
const fy_zylicenseProject = (ids) => {
    const arr = []
    if (!ids) return '--'
    zylicenseProjectOption.value.forEach(v => {
        if (ids.split(',').includes(v.id)) {
            arr?.push(v.treatmentName)
        }
    })
    return arr?.toString()
}
const fy_zylicenseRange = (ids) => {
    const arr = []
    if (!ids) return '--'
    massRangeOption.value.forEach(v => {
        if (ids.split(',').includes(v.id)) {
            arr?.push(v.massName)
        }
    })
    return arr?.toString()
}

const filterAddress = (val) => {
    if (!val) return '--'
    const addressList = val.split(',')
    const addressArr = []
    sysAreas.value?.forEach(x => {
        if (isArray(addressList)) {
            addressList?.forEach(v => {
                if (x?.value == v) {
                    addressArr?.push(x.label)
                }
            })
        }
    })
    return addressArr?.toString()
}
const regx = (arr) => {
    if(!arr?.length) return
    const filterArr = []
    const regKey = ["licenseNo", "licenseAddress", "licenceScopesName", "licenseStartTime", "licenseValidity", "licenseOffice", "licenseDirector", "qualityDirector", "licenseImg"]
    for (var item of arr) {
        for (var val of regKey) {
            if (Array.isArray(item[val]) ? item[val]?.length : item[val]) {
                if(filterArr.some(x=>x.type === item.type )) break
                filterArr.push(item)
                break
            }
        }
    }
    return filterArr
}
const formDict = (data, val) => {
    if (!val || !data?.length) return '--'
    return proxy.selectDictLabel(data, val)
}
const handleCheckChange = () => {

}
const handleOrgDetail = row => {
    entrustVisible.value = true
    entrustData.value = row
}
const handleFileList = () => {
    fileListVisible.value = true
    fileDraLoading.value = true
    manufacturerManagement.getFileList({ commonId: listStep1.value.id, commonType: '10', ...fileListDataPage.value }).then(res => {
        if (res.code == 200) {
            fileListData.value = res.data?.records
            fileListDataTotal.value = res.data?.total

        }
        fileDraLoading.value = false
    })
}
const handleDetailFileListChoose = (key) => {
    fileListChooseList.value = key
}
const handleExportFile = () => {
    const chooseArr = []
    fileListChooseList.value.forEach(v => {
        chooseArr.push(v.fileUrl)
    })
    manufacturerManagement.exportFile({ fileNames: chooseArr.toString() }).then(res => {
        proxy.download(res)
    })
}
const handleTabClick = (value) => {
    if (tabKey === value) retrun
    tabKey.value = value
    listStep2_item.value = {}
    const key = Object.entries(typeDict).find(([key, val]) => key === tabKey.value)
    listStep2_item.value = listStep2.value?.filter(item => item.type === key[1])?.[0]
}
const handleDetail = (scope, key) => {
    licenseScopeVisible.value = true
    licenseScopeListData.value = []
    let shopKey = shopType.value.filter(item => item.name === scope.type)?.[0]?.id
    if (key && key === 'apparatusFirstScopes') {
        shopKey = shopType.value.filter(item => item.name === '一类')?.[0]?.id
    }
    if (key && key === 'sterilizedScopes') {
        shopKey = shopType.value.filter(item => item.name === '消杀')?.[0]?.id
    }
    if (shopKey) {
        lisloading.value = true
        custom.getWightConfig({ dictid: shopKey }).then(res => {
            if (res.code == 200) {
                lisloading.value = false
                res.data.forEach(v => {
                    if (v.length) {
                        licenseScopeListData.value.push(v?.[0])
                    }

                })
                if (key) {
                    scope[key]?.forEach(v => {
                        checkedKeys.value.push(v?.massRangeSet?.id || v)
                    })
                } else {
                    scope.licenceScopes?.forEach(v => {
                        checkedKeys.value.push(v?.massRangeSet?.id || v)
                    })
                }


            }
        }).finally(() => {
            lisloading.value = false
        })
    } else {
        lisloading.value = false
    }
}
const handleUploadViewFile = (url) => {
    uploadVisibleFile.value = true
    uploadViewImgUrlFile.value = url

}
const filterArr = (option, key, value) => {
    if (!option?.length || !key || !value?.length) return
    const res = option?.filter(v => value?.includes(v?.[key]) === true)
    const NameArr = []
    res?.forEach(item => {
        NameArr.push(item?.valueName)
    })
    return NameArr?.toString()
}
async function dict() {
    delFlagList.value = await proxy.getDictList('erp_delFlag_status')
    reviewStatus.value = await proxy.getDictList('erp_review_status')
    shopType.value = await proxy.getDictList('product_type_config')
    manufacturerType.value = await proxy.getDictList('customer_type')
    directoryFileName.value = await proxy.getDictList('directory_file_name')
    qualificationList.value = await proxy.getDictList('erp_qualification_category')
    modeOfList.value = await proxy.getDictList("erp_mode_of_operation")
    businessList.value = await proxy.getDictList("erp_business_type")
    drugTypeList.value = await proxy.getDictList("erp_drug_type")
    businessNatureList.value = await proxy.getDictList('medical_treatment_business_nature')
    tradeType.value = await proxy.getDictList('trade_type')
}
const getDetail = () => {

    custom.detail({ id: data.value.documentId }).then(res => {
        if (res.code == 200) {
            listStep1.value = res.data?.customersDTO
            listStep1.value.apparatusFirst = res.data?.customersDTO?.apparatusFirst == '1' ? true : false
            listStep1.value.sterilized = res.data?.customersDTO?.sterilized == '1' ? true : false
            // listStep1.value.businessLicensePicturt = listStep1.value?.businessLicensePicturt ? JSON.parse(listStep1.value?.businessLicensePicturt) : []
            if (listStep1.value?.credentialType == '2') {
                listStep2_item.value = res.data?.zylicenceDTOS

            } else {
                listStep2.value = regx(res.data?.licenceDTOS)
                listStep2.value?.forEach(item => {
                    item.licenseImg = item.licenseImg ? JSON.parse(item.licenseImg) : []
                })
                listStep2_item.value = listStep2.value?.[0]
                tabKey.value = Object.entries(typeDict).find(([key, val]) => val === listStep2.value?.[0]?.type)?.[0]
            }


            listStep3.value = res.data?.gmpCertificateDTOList
            listStep3.value?.forEach(item => {
                item.gmpCertificatePicture = item.gmpCertificatePicture ? JSON.parse(item.gmpCertificatePicture) : []
            })
            listStep4.value = res.data?.fileDTOS
            listStep4.value?.forEach(item => {
                item.fileNameUrl = item.fileNameUrl ? JSON.parse(item.fileNameUrl) : []
            })
            listStep5.value = res.data?.qualityDTOS
            listStep5.value?.forEach(item => {
                item.qualityCode = item.qualityCode ? JSON.parse(item.qualityCode) : {}
            })
            listStep6.value = res?.data?.delegateDTOS
            listStep6.value?.forEach(item => {
                item.remark = item.remark ? JSON.parse(item.remark) : {}
                item.delegateCard = item.delegateCard ? JSON.parse(item.delegateCard) : {}
                item.isEntrustAll = item.isEntrustAll === true ? '1' : item.isEntrustAll === false ? '2' : undefined
            })
        } else {
            proxy.msgError(res.msg)
        }
    }).finally(() => {
    })
}
onMounted(async () => {
    modalLoading.value = true
    dict()
    await getRanges()
    await getTerats()
    getDetail()
  if (deFlag.value) {
    let reviewRes = await custom.erpCustomersApproval({ 'customers.id': data.value.documentId })
    let resq = await drugApi.drugLog({ masterId: data.value.documentId })
    if ((reviewRes.code == 200 && reviewRes.data) || (resq.code == 200 && resq.data)) {
      childRef.value.timeFns(reviewRes?.data?.records, resq?.data?.records)
    }
  }
    sysAreas.value = proxy.getSysAreasThird.map(v => {
        return { value: v.value, label: v.label }
    })
    sysAreas.value = [{ label: '全国', value: '1', id: '1' }, ...sysAreas.value]
    let res = await manufacturerManagement.getLicenseScopeList({ 'item.id': '6',isStop: '1', current: 1, size: 999 })
    licenseScopeList.value = res?.data?.records
    modalLoading.value = false
})


</script>
<style lang="scss" scoped>
.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    // grid-template-rows: auto auto;
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-items: stretch;
    align-items: start;
}

.el-tabs {
    display: flex;
    // align-items: center;
}

.el-tabs__nav-wrap::after {
    width: 0 !important;
}

::v-deep .rules {
    position: relative;

    .cell::after {
        content: "*";
        color: red;
        display: inline-block;
        position: absolute;
        top: 30%;
        left: 70px;
    }
}

::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

.box_date {
    width: 220px;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
    display: contents !important;
}

::v-deep .el-upload-dragger {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

::v-deep .step2 {
    .el-button {
        // border-bottom: none;
        border-right: none;
        border-radius: 4px 0 0 4px;

    }

    .el-button+.el-button {
        margin-left: 0;
        border-radius: 0 0 0 0;
        // border-left: none;
    }
}

.btn_cmt {
    margin-bottom: 20px;
}

.last-btn {
    border: 1px solid #dcdfe6 !important;
}

.fileName_t {
    display: flex;
    width: 100%;
    color: #2a76f8;
    cursor: pointer;
    align-items: center;
    margin-top: 4px;

    span:nth-of-type(2) {
        display: none;
        margin-left: 10px;

        ::v-deep .el-icon {
            // margin-top:10px;
            font-size: 12px;
            color: red
        }
    }
}

.fileName_t:hover .fileName_t_icon {
    display: block;
}

::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
    font-weight: bold;
    color: #505050;
    font-size: 13px;
    width: 150px;
}

.imgView {
    width: 100px;
    height: 100px;
    margin-right: 20px
}

::v-deep .demo-image__preview {
    text-align: left;
}

.btn_cmt {
    margin-bottom: 20px;
}

::v-deep .dialog_dir {
    .el-descriptions__label.el-descriptions__cell.is-bordered-label {
        font-weight: bold;
        color: #505050;
        font-size: 13px;
        width: 10%;
    }
}

::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-center {
    width: 15%;
}

::v-deep .el-descriptions__label.el-descriptions__cell.is-bordered-label {
    font-weight: bold;
    color: #505050;
    font-size: 13px;
    width: 10%;
}

.shopDetailTitle {
    padding: 20px 0 0px 10px;
    color: #000;
    font-weight: bold;
}

// ::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions__cell.is-center {
//     width: 25%;
// }
::v-deep .licenseImg {
    .el-descriptions__label.el-descriptions__cell.is-bordered-label {
        width: 1.57%
    }
}

::v-deep .licenseImg_listStep1 {
    .el-descriptions__label.el-descriptions__cell.is-bordered-label {
        width: 1.77%
    }
}

::v-deep .licenseImg_listStep2 {
    .el-descriptions__label.el-descriptions__cell.is-bordered-label {
        width: 3.75%
    }
}</style>
