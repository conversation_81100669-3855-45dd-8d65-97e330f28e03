<template>
    <el-dialog v-model="dialogVisible" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" class="mismatch-dialog" draggable title="汇款金额与实收金额匹配" width="900px" @close="onClose">
        <div class="amount-summary">
            <el-descriptions :column="4" border direction="vertical">
                <el-descriptions-item label="收款单应收合计金额">
                    {{ localFormData.receivableCost.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="收款单未收合计金额">
                    {{ localFormData.unpaidCost.toFixed(2) }}
                </el-descriptions-item>
                <el-descriptions-item label="汇款合计金额">
                    <span style="color: #2a76f8">{{ localFormData.remitAmount.toFixed(2) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="不匹配金额">
                    <span :style="{ color: localFormData.remitAmount - localFormData.unpaidCost === 0 ? '#606266' : localFormData.remitAmount - localFormData.unpaidCost > 0 ? '#1ACD7E' : '#FF4D4F' }">
                        {{ localFormData.remitAmount - localFormData.unpaidCost > 0 ? '+' + (localFormData.remitAmount - localFormData.unpaidCost).toFixed(2) : (localFormData.remitAmount - localFormData.unpaidCost).toFixed(2) }}
                    </span>
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <el-table :data="localFormData.selectedData" :summary-method="getSummaries" border class="mismatch-table" show-summary size="large">
            <el-table-column align="center" label="收款单号" prop="paymentOrderNo" width="140" />
            <el-table-column align="center" label="创建时间" prop="createDate" width="180" />
            <el-table-column align="center" label="收款单应收合计" prop="receivableCost" width="150">
                <template #default="{ row }">
                    {{ row.receivableCost.toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="收款单未收合计" prop="unpaidCost" width="150">
                <template #default="{ row }">
                    {{ row?.unpaidCost?.toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="收款单实收合计" min-width="150" prop="paidCost">
                <template #header>
                    <div style="display: inline-flex; align-items: center">
                        收款单实收合计
                        <el-tooltip content="双击单元格可编辑实收金额" placement="top">
                            <el-icon style="margin-left: 4px; color: #2a76f8"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                </template>
                <template #default="{ row }">
                    <div
                        v-click-outside="
                            () => {
                                row.isEditing = false;
                                row.paidCost = row.paidCost === null ? 0 : Number(row.paidCost);
                            }
                        "
                    >
                        <span v-if="!row.isEditing" :style="{ color: row.paidCost !== row.unpaidCost ? '#2A76F8' : '' }" @dblclick="row.isEditing = true">
                            {{ row.paidCost.toFixed(2) }}
                        </span>
                        <el-input-number v-else v-model="row.paidCost" :min="0" :precision="2" controls-position="right" @change="(val) => (row.paidCost = val === null ? 0 : Number(val.toFixed(2)))" />
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="onClose">取 消</el-button>
                <el-button type="primary" @click="onSubmit">确 认</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import { h } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';
import { ClickOutside, ElMessage } from 'element-plus';

export default {
    name: 'MismatchDialog',
    components: {
        QuestionFilled
    },
    directives: {
        ClickOutside
    },
    props: {
        amount: {
            type: Number,
            default: 0
        },
        initialFormData: {
            type: Object,
            default: () => ({
                receivableCost: 0,
                unpaidCost: 0,
                remitAmount: 0,
                selectedData: []
            })
        },
        modelValue: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            localFormData: null
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    watch: {
        initialFormData: {
            immediate: true,
            handler(val) {
                // 深拷贝并转换数据类型
                const data = JSON.parse(JSON.stringify(val));
                // 转换 selectedData 中的字符串数值为数值类型
                data.selectedData = data.selectedData.map((item) => ({
                    ...item,
                    receivableCost: Number(item.receivableCost),
                    unpaidCost: Number(item.unpaidCost),
                    // 实收合计第一次赋值
                    paidCost: Number(item.unpaidCost)
                }));
                data.receivableCost = Number(data.receivableCost);
                data.unpaidCost = Number(data.unpaidCost);
                data.remitAmount = Number(data.remitAmount);
                this.localFormData = data;
            }
        }
    },
    methods: {
        /*
         *计算合计
         */
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                if (index === 1) {
                    sums[index] = '';
                    return;
                }

                if (['receivableCost', 'paidCost', 'unpaidCost'].includes(column.property)) {
                    const sum = data.reduce((prev, curr) => {
                        return prev + Number(curr[column.property]);
                    }, 0);

                    if (column.property === 'paidCost') {
                        const receivableSum = data.reduce((prev, curr) => prev + Number(curr.unpaidCost), 0);
                        sums[index] = h(
                            'span',
                            {
                                style: {
                                    color: sum !== receivableSum ? '#2A76F8' : ''
                                }
                            },
                            sum.toFixed(2)
                        );
                    } else {
                        sums[index] = sum.toFixed(2);
                    }
                } else {
                    sums[index] = '';
                }
            });
            return sums;
        },
        /*
         *关闭弹窗
         */
        onClose() {
            this.dialogVisible = false;
        },
        /*
        1. 计算实收合计
        2. 检查是否有实收金额为0的记录
        3. 检查实收合计是否等于汇款金额
        */
        async onSubmit() {
            // 计算实收合计
            const totalPaidCost = this.localFormData.selectedData.reduce((sum, item) => sum + Number(item.paidCost), 0);

            // 检查实收合计是否等于汇款金额（允许 0.01 以内的误差）
            if (Math.abs(totalPaidCost - this.localFormData.remitAmount) > 0.01) {
                ElMessage.error({
                    message: `收款单实收合计（${totalPaidCost.toFixed(2)}）与汇款合计金额（${this.localFormData.remitAmount.toFixed(2)}）不匹配，请检查`,
                    duration: 5000
                });
                return;
            }

            // 验证通过，提交数据
            this.$emit('submit', this.localFormData);
            this.onClose();
        }
    },
    emits: ['update:modelValue', 'submit']
};
</script>

<style scoped>
.mismatch-dialog {
    --el-dialog-padding-primary: 20px;
}

.amount-summary {
    margin-bottom: 16px;
    background-color: #fff;
    border-radius: var(--el-border-radius-base);
}

.mismatch-table {
    --el-table-border-color: #ebeef5;
    --el-table-header-bg-color: #f5f7fa;
}

.dialog-footer {
    text-align: right;
}

:deep(.el-descriptions) {
    --el-descriptions-item-bordered-label-background: #f5f7fa;
}

:deep(.el-table th) {
    font-weight: 600;
    color: var(--el-text-color-primary);
}

:deep(.el-input-number) {
    width: 100%;
}

:deep(.el-dialog__body) {
    padding: var(--el-dialog-padding-primary);
}

:deep(.el-dialog__footer) {
    padding: 0 var(--el-dialog-padding-primary) var(--el-dialog-padding-primary);
    border-top: 1px solid var(--el-border-color-lighter);
}
</style>
