<template>
    <div class="app-container">
        <el-card class="box-card Botm" style="margin: 10px;">
            <el-form :model="queryParams" ref="queryRef" :inline="true" class="form_130">
                <el-form-item label="库号" prop="warehouseNumber">
                    <el-input v-model="queryParams.warehouseNumber" placeholder="请输入库号" clearable class="form_225" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="form_225">
                        <el-option :label="item.name" :value="item.value" v-for="item in warehouseNumberStatus"
                            :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="经手人" prop="handledBy">
                    <el-select v-model="queryParams.handledBy" filterable remote reserve-keyword placeholder="请输入经手人"
                        :remote-method="remoteMethod" :loading="optionLoading" clearable class="form_225">
                        <el-option v-for="item in handledByOptions" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button @click="resetQuery">重置</el-button>

                </el-form-item>
            </el-form>

        </el-card>
        <el-card class="box-card" style="margin: 10px;">
            <TopTitle title="查询列表">
                <div>
                    <el-button type="primary" @click="() => handleAdd()">新增</el-button>
                    <el-button type="" @click="handleExport">导出</el-button>
                </div>
            </TopTitle>
            <el-table v-loading="loading" :data="list" border style="margin-top: 30px;">
                <el-table-column label="序号" prop="sort" width="80" align="center">
                    <template #default="scope">
                        <span>{{ (queryParams.current - 1) * queryParams.size + scope.$index + 1 }}</span>
                    </template>
                </el-table-column>

                <!-- <el-table-column label="ID" prop="id" align="left" /> -->
                <el-table-column label="库号" prop="warehouseNumber" align="left" />
                <el-table-column label="状态" align="left">
                    <template #default="scope">
                        <span>{{ formDict(warehouseNumberStatus, scope.row.status) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="备注" prop="remark" :show-overflow-tooltip="true" align="left" />
                <el-table-column label="创建日期" align="left">
                    <template #default="scope">
                        <span>{{ moment(scope.row.createDate).format("YYYY-MM-DD") || '--' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="修改日期" align="left">
                    <template #default="scope">
                        <span>{{ moment(scope.row.updateDate).format("YYYY-MM-DD") || '--' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="200px">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleAdd(scope.row)"><img src="@/assets/icons/update.png"
                                style="margin-right:5px" />编辑</el-button>
                        <el-button link type="danger" @click="handleDelete(scope.row)"
                            :disabled="isDeleteAuthority(scope.row.createBy.id)"> <img src="@/assets/icons/delete.png"
                                style="margin-right:5px" />删除</el-button>
                        <el-button link type="warning" @click="handleAssociation(scope.row)"> <img
                                src="@/assets/icons/correlation.png" style="margin-right:5px" />关联经手人</el-button>
                        <el-button link type="success" @click="handleLog(scope.row)"><img src="@/assets/icons/review.png"
                                style="margin-right:5px" />操作日志</el-button>
                    </template>
                </el-table-column>

            </el-table>
            <div style="float: right;">
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                    v-model:limit="queryParams.size" @pagination="getList" />
            </div>
        </el-card>
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog :title="title" v-model="open" width="600px" append-to-body v-if="open">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px"
                style="margin-top: 0px;padding-right: 20px;">
                <el-form-item label="库号" prop="warehouseNumber">
                    <el-input v-model="form.warehouseNumber" placeholder="请输入库号" />
                </el-form-item>
                <el-form-item label="是否隐藏" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="item.value" v-for="item in warehouseNumberStatus" :key="item.value">{{ item.name
                        }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="是否停用" prop="isStop">
                    <el-radio-group v-model="form.isStop">
                        <el-radio label="0" > 未停用
                        </el-radio>
                        <el-radio label="1" > 停用
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" maxlength="100" show-word-limit
                        clearable />
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="() => open = false">取 消</el-button>
					<el-button type="primary" @click="submitForm">确 定</el-button>
				</div>
            </template>
        </el-dialog>
        <!-- 关联骑手人对话框 -->
        <el-dialog title="关联经手人" v-model="associationVisible" width="600px" append-to-body v-if="associationVisible">
            <el-form ref="formRef" :model="formAssociation" :rules="rules" label-width="100px"
                style="margin-top: 0px;padding-right: 20px;">
                <el-form-item label="库号">
                    <span>{{ associationRow.warehouseNumber || '--' }}</span>
                </el-form-item>

                <el-form-item label="经手人" prop="handledBy">
                    <el-tag v-for="(tag, index) in associationList" :key="index" class="mx-1" closable
                        v-if="associationList.length" style="margin-left: 5px;margin-bottom: 10px;"
                        @close="handleTagClose(tag)">
                        {{ tag.name }}
                    </el-tag>
                    <div style="display: flex;width: 100%;">
                        <el-select v-model="formAssociation.association" multiple placeholder="请选择经手人" style="width: 80%;">
                            <el-option v-for="item in optionList" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                        <el-button type="primary" @click="handleAddAssociation" style="margin-left: 10px;">添加</el-button>
                    </div>
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="() => associationVisible = false">取 消</el-button>
					<el-button type="primary" @click="submitFormAssociation">确 定</el-button>
				</div>
            </template>
        </el-dialog>
        <Review :reviewVisible="operateLogVisible" :beforeClose="beforeClose_review" :data="operateLogList"
            v-if="operateLogVisible" />
    </div>
</template>

<script setup >
import { reactive, ref, getCurrentInstance, toRefs } from 'vue'
import moment from 'moment'
import CardHeader from '@/components/CardHeader'
const { proxy } = getCurrentInstance();
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import TopTitle from '@/components/topTitle'
import Review from './review.vue'
import isDeleteAuthority from '@/utils/isDeleteAuthority'
const list = ref([]);
const open = ref(false);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const warehouseNumberStatus = ref([])
const operateLogVisible = ref(false)
const operateLogList = ref([])
const associationVisible = ref(false)
const formAssociation = ref({})
const associationRow = ref({})
const associationList = ref([])
const optionList = ref([])
const associationListId = ref([])
const optionLoading = ref(false)
const handledByOptions = ref([])
const data = reactive({
    form: {},
    queryParams: {
        current: 1,
        size: 10,
    },
    rules: {
        warehouseNumber: [{ required: true, message: "库号不能为空", trigger: "blur" }],
        status: [{ required: true, message: "隐藏状态不能为空", trigger: "change" }],
        isStop:[{ required: true, message: "停用状态不能为空", trigger: "change" }],
    },
    operateLog: {
        current: 1,
        size: 10,
    },
});

const { queryParams, form, rules, operateLog } = toRefs(data);
const remoteMethod = (query) => {
    if (query) {
        optionLoading.value = true
        getUsers(query)
    } else {
        handledByOptions.value = []
    }
}
const beforeClose_review = () => {
    operateLogVisible.value = false
}
const handleDelete = (row) => {
    proxy.$confirm('是否确认删除该条数据吗?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        warehouseNumberManagement.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                proxy.msgSuccess('删除成功')
                getList()
            } else {
                proxy.msgError('删除失败')
            }
        })

    }).catch(()=>{

    })
}
/** 查询角色列表 */
function getList() {
    loading.value = true
    warehouseNumberManagement.getList({...queryParams.value,isStop:'2'}).then(res => {
        if (res.code == 200) {
            list.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}
const handleExport = () => {
    warehouseNumberManagement.exportFile({ filename: '库号列表.xls', ...queryParams.value,isStop:'2' }).then(res => {
        proxy.download(res, "application/vnd.ms-excel", '库号列表.xls')
    })
}
const handleTagClose = (tag) => {
    associationList.value = associationList.value.filter(item => item.id !== tag.id)
    associationListId.value = associationList.value.filter(item => item !== tag.id)
}
// 添加经手人
const handleAddAssociation = () => {
    if (!formAssociation.value.association.length) {
        return proxy.msgError('经手人不能为空！')
    } else {
        optionList.value.forEach(v => {
            formAssociation.value.association.forEach(x => {
                if (v.id == x) {
                    if (associationListId.value.includes(v.id)) {
                        proxy.msgError(v.name + '已添加，已过滤！')
                    } else {
                        associationList.value.push(v)
                        associationListId.value.push(v.id)
                    }

                }

            })
        })
        formAssociation.value.association = []
    }
}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}
/** 搜索按钮操作 */
function handleQuery() {
    getList();
}
const handleAdd = (row) => {
    row ? (title.value = '修改') : (title.value = '新增')
    if (row) {
        const { warehouseNumber, status, remark, id,isStop } = row
        form.value = {
            warehouseNumber,
            remark,
            status,
            id,
            isStop
        }
    } else {
        form.value = {}
    }
    open.value = true
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
function handleLog(row) {
    operateLogVisible.value = true
    operateLogList.value = row
}
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            warehouseNumberManagement.save(form.value).then(res => {
                if (res.code == 200) {
                    proxy.msgSuccess(`${title.value}成功`)
                    open.value = false
                    getList()
                }
            }).catch(() => {

            })
        }
    })
}
function handleAssociation(row) {
    associationList.value = []
    associationListId.value = []
    formAssociation.value = {}
    associationVisible.value = true
    warehouseNumberManagement.getWarehouseNumberId({ wnid: row.id }).then(res => {
        if (res.code == 200 && res.data?.length) {
            res.data?.forEach(x => {
                associationList.value.push(x.handledBy)
                associationListId.value.push(x.handledBy.id)
            })

        }
    })
    associationRow.value = row


}
const submitFormAssociation = () => {
    let ids = []
    associationList.value.forEach(v => {
        ids.push(v.id)
    })
    if (ids?.length == 0) return proxy.msgError(`请至少选择一个经手人！`)
    const params = {
        warehouseNumber: {
            id: associationRow.value.id
        },
        handledBy: {
            id: ids.toString()
        }
    }
    warehouseNumberManagement.saveWarehouseNumber(params).then(res => {
        if (res.code == 200) {
            proxy.msgSuccess(`操作成功`)
            associationVisible.value = false
            getList()
        }
    })
}
async function dict() {
    warehouseNumberStatus.value = await proxy.getDictList('warehouseNumberManagement_status')
}
const getUsers = (name) => {
    warehouseNumberManagement.getUser({ name }).then(res => {
        if (res.code == 200 && !name) {
            optionList.value = res.data?.records
        }
        if (res.code == 200 && name) {
            handledByOptions.value = res.data?.records
            optionLoading.value = false
        }
    })
}
dict()
getList();
getUsers()
</script>
<style lang="scss" scoped>
::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}
</style>
