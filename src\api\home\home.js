import request from '@/utils/request';

export default {
    // 订单统计
    getOrderPie: function () {
        return request.get('/tms/statistics/order/orderCount');
    },
    // 订单数量
    getWaveData: function (params) {
        return request.get('/tms/statistics/order/orderCountGroupByDate', params);
    },
    // 系统公告
    getNotice: function () {
        return request.get('/sys/notice/list');
    },
    // 首页数据-顶部五个数量-承运端
    getTopFiveData: function () {
        return request.get('/tms/indexData/getIndex-orderNum');
    },
    // 首页数据-顶部五个数量-货主端
    getTopFiveDataOwner: function () {
        return request.get('/tms/owner-index/getIndex-orderNum');
    },
    // 首页数据-订单趋势-承运端
    getOrderTrend: function (params) {
        return request.get('/tms/indexData/getIndex-orderTrend', params);
    },
    // 首页数据-订单趋势-货主端
    getOrderTrendOwner: function (params) {
        return request.get('/tms/owner-index/getIndex-orderTrend', params);
    },
    // 首页数据-市区Top-承运端
    getCityTop: function (params) {
        return request.get('/tms/indexData/getIndex-city-top', params);
    },
    // 首页数据-市区Top-货主端
    getCityTopOwner: function (params) {
        return request.get('/tms/owner-index/getIndex-city-top', params);
    },
    // 首页数据-省Top-承运端
    getNationalTop: function (params) {
        return request.get('/tms/indexData/getIndex-province-top', params);
    },
    // 首页数据-省Top-货主端
    getNationalTopOwner: function (params) {
        return request.get('/tms/owner-index/getIndex-province-top', params);
    },
    // 首页数据-司机任务数量
    getDriverTaskNum: function (params) {
        return request.get('/tms/indexData/getIndex-driver-task', params);
    }
};
