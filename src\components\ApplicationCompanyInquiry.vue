<template>
    <el-dialog v-model="dialogVisible" :close-on-click-modal="true" draggable title="查询" width="80%">
        <!-- 搜索表单 -->
        <el-form :model="searchForm" class="search-form">
            <el-form-item label="申请公司">
                <el-input v-model="searchForm.companyName" clearable placeholder="请输入申请公司" @keyup.enter="handleSearch" @clear="handleSearch" />
            </el-form-item>
            <el-form-item label="发票抬头">
                <el-input v-model="searchForm.invoiceHead" clearable placeholder="请输入发票抬头" @keyup.enter="handleSearch" @clear="handleSearch" />
            </el-form-item>
            <el-form-item label="发票类型">
                <el-select v-model="searchForm.invoiceType" clearable filterable placeholder="请选择发票类型" @change="handleSearch">
                    <el-option v-for="item in invoiceTypeList" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
            </el-form-item>
            <el-alert title="双击表格，内容可回填到申请开票表单中" type="info" show-icon style="width: fit-content; padding-right: 27px" />
        </el-form>

        <!-- 表格 -->
        <el-table v-if="tableData.length" :data="tableData" :max-height="450" border style="width: 100%" @row-dblclick="handleRowDblClick">
            <el-table-column label="申请公司" min-width="180" prop="companyName" show-overflow-tooltip />
            <el-table-column label="发票抬头" min-width="180" prop="invoiceHead" show-overflow-tooltip />
            <el-table-column label="税号" min-width="170" prop="taxNo" show-overflow-tooltip />
            <el-table-column label="地址" min-width="200" prop="address" show-overflow-tooltip />
            <el-table-column label="电话" min-width="120" prop="phone" show-overflow-tooltip />
            <el-table-column label="开户银行" min-width="150" prop="openBank" show-overflow-tooltip />
            <el-table-column label="银行账号" min-width="150" prop="bankAccount" show-overflow-tooltip />
            <el-table-column label="签约公司名称" min-width="200" prop="signCompany" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ companyList.find((item) => item.value === scope.row.signCompany)?.name }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="发票类型" min-width="90" prop="invoiceType" show-overflow-tooltip>
                <template #default="scope">
                    <span>{{ invoiceTypeList.find((item) => item.value === scope.row.invoiceType)?.name }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="项目名称" min-width="120" prop="projectName" show-overflow-tooltip />
            <el-table-column align="center" label="税点(%)" min-width="100" prop="taxPoint" show-overflow-tooltip />
        </el-table>
        <!-- 空状态 -->
        <el-empty v-else description="请输入查询条件，进行查询！" />
    </el-dialog>
</template>

<script>
import customerPaymentDocument from '@/api/carrierEnd/customerPaymentDocument';
import { InfoFilled } from '@element-plus/icons-vue';
export default {
    name: 'ApplicationCompanyInquiry',
    components: {
        InfoFilled
    },
    props: {
        baseForm: {
            type: Object,
            default: () => ({})
        },
        modelValue: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            searchForm: {
                companyName: undefined,
                invoiceHead: undefined,
                invoiceType: undefined
            },
            tableData: [],
            invoiceTypeList: [],
            companyList: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    created() {
        this.getDict();
        // 添加初始化处理
        if (this.baseForm) {
            this.searchForm = {
                companyName: this.baseForm.companyName,
                invoiceHead: this.baseForm?.invoiceData?.invoiceHead,
                invoiceType: this.baseForm.invoiceType
            };
            this.handleSearch();
        }
    },
    methods: {
        async getDict() {
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
            this.companyList = await this.getDictList('signing_company');
        },
        // 处理重置
        handleReset() {
            this.searchForm = {
                companyName: undefined,
                invoiceHead: undefined,
                invoiceType: undefined
            };
            this.handleSearch();
        },
        // 处理双击行
        handleRowDblClick(row) {
            this.$emit('submit', row);
            this.dialogVisible = false;
        },
        // 处理查询
        async handleSearch() {
            try {
                customerPaymentDocument.getInvoiceCompany(this.searchForm).then((res) => {
                    if (res.code === 200) {
                        this.tableData = res.data;
                    } else {
                        this.$message.error(res.message);
                        this.tableData = [];
                    }
                });
            } catch (error) {
                console.error('查询失败:', error);
            }
        }
    },
    emits: ['update:modelValue', 'submit']
};
</script>

<style lang="scss" scoped>
.search-form {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
}

.search-form :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 10px;
}

.search-form :deep(.el-form-item__label) {
    padding: 0 8px 0 0;
}

.search-form :deep(.el-input),
.search-form :deep(.el-select) {
    width: 160px;
}
</style>
