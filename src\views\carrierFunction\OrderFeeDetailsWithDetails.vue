<template>
    <el-drawer v-model="visible" class="orderCostDialog" size="900px" title="订单费用详情" @close="hideOrderCost">
        <div v-loading="orderCostLoading" :element-loading-text="orderCostLoadingText" style="background-color: #f2f2f2; padding: 16px">
            <el-card :body-style="{ padding: '10px 20px' }" class="mb10 card__two__column" shadow="never">
                <template #header>
                    <div class="titleLayout">
                        <span class="verticalBar"></span>
                        <span class="title">订单信息</span>
                    </div>
                </template>
                <div class="d-box-content">
                    <div class="d-box-column">
                        <div v-if="detailData.orderNo" class="d-box-info">
                            <span>订单号</span>
                            <span>{{ detailData.orderNo }}</span>
                        </div>
                        <div v-if="detailData.sendAddress" class="d-box-info">
                            <span>发件详细地址</span>
                            <span>{{ detailData.sendAddress }}</span>
                        </div>
                        <div v-if="detailData.transType" class="d-box-info">
                            <span>运输类型</span>
                            <span>{{ formatDictionaryData('productTypeList', detailData.transType) }}</span>
                        </div>
                        <div v-if="detailData.productType" class="d-box-info">
                            <span>产品分类</span>
                            <span>{{ formatDictionaryData('goodsTypeList', detailData.productType) }}</span>
                        </div>
                        <div v-if="detailData.goodsWeight" class="d-box-info">
                            <span>重量</span>
                            <span>{{ detailData.goodsWeight }}</span>
                        </div>
                        <div v-if="detailData.carTypeDesc" class="d-box-info">
                            <span>车型</span>
                            <span>{{ detailData.carTypeDesc }}</span>
                        </div>
                    </div>
                    <div class="d-box-column">
                        <div v-if="detailData.customerCompanyName" class="d-box-info">
                            <span>货主</span>
                            <span>{{ detailData.customerCompanyName }}</span>
                        </div>
                        <div v-if="detailData.receivedAddress" class="d-box-info">
                            <span>收件地址</span>
                            <span>{{ detailData.receivedAddress }}</span>
                        </div>
                        <div v-if="detailData.receivedAddress" class="d-box-info">
                            <span>收件公司</span>
                            <span>{{ detailData.receivedCompanyName || '--' }}</span>
                        </div>
                        <div v-if="detailData.temperatureType" class="d-box-info">
                            <span>温层类型</span>
                            <span>{{ formatDictionaryData('temperatureTypeDicts', detailData.temperatureType) }}</span>
                        </div>
                        <div v-if="detailData.orderNumber" class="d-box-info">
                            <span>件数</span>
                            <span>{{ detailData.orderNumber }}</span>
                        </div>
                        <div v-if="detailData.goodsVolume" class="d-box-info">
                            <span>体积</span>
                            <span>{{ detailData.goodsVolume }}</span>
                        </div>
                        <div v-if="detailData.kilometre" class="d-box-info">
                            <span>公里数</span>
                            <span>{{ detailData.kilometre || '--' }}</span>
                        </div>
                    </div>
                </div>
            </el-card>
            <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <template #header>
                    <div class="titleLayout">
                        <span class="verticalBar"></span>
                        <span class="title">费用明细</span>
                    </div>
                </template>
                <column-table :columns="feeBreakdownColumns" :data="feeBreakdownData" element-loading-text="加载中...">
                    <template #costType="{ row }">
                        <span>{{ formatDictionaryData('expenseAccountList', row.costType) }}</span>
                    </template>
                    <template #costContractPrice="{ row }">
                        <div>{{ Number(row.costContractPrice).toFixed(2) }}</div>
                    </template>
                    <template #costData="{ row }">
                        <div>{{ Number(row.costData).toFixed(2) }}</div>
                    </template>
                    <template #textFormula="{ row }">
                        <div>{{ row.textFormula || '-' }}</div>
                    </template>
                </column-table>
                <div class="feeBreakdown__total">
                    <div v-if="detailData.estimateCost" class="total__item">
                        <span class="total__title">预估费用</span>
                        <span class="total__num">{{ detailData.estimateCost }}</span>
                    </div>
                    <div v-if="detailData.totalContractPrice" class="total__item">
                        <span class="total__title">合同合计价格</span>
                        <span class="total__num">{{ detailData.totalContractPrice }}</span>
                        <!--            <span v-if="detailData.textFormula" style="color: #B1B1B1">({{ detailData.textFormula }})</span>-->
                    </div>
                    <!--          <div v-if="detailData.abnormalCost" class="total__item">-->
                    <!--            <span class="total__title">费用异动</span>-->
                    <!--            <span class="total__num">{{ detailData.abnormalCost }}</span>-->
                    <!--          </div>-->
                    <!--          <div v-if="detailData.discountCost" class="total__item">-->
                    <!--            <span class="total__title">折扣合计</span>-->
                    <!--            <span class="total__num">{{ detailData.discountCost }}</span>-->
                    <!--          </div>-->
                    <div v-if="detailData.totalCost" class="total__item">
                        <span class="total__title">应收合计</span>
                        <span class="total__num">{{ detailData.totalCost }}</span>
                    </div>
                </div>
            </el-card>
            <div style="display: flex; justify-content: flex-end">
                <el-button type="primary" @click="hideOrderCost">关闭</el-button>
            </div>
        </div>
    </el-drawer>
</template>
<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';

export default {
    name: 'OrderFeeDetailsWithDetails',
    components: { ColumnTable },
	props: {
		detailData: {},
		feeBreakdownData: {},
		modelValue: {
			type: Boolean,
			required: true
		},
		orderCostLoading: {
			type: Boolean,
			default: false,
			required: false
		},
		orderCostLoadingText: {
			type: String,
			default: '加载中...',
			required: false
		}
	},
    data() {
        return {
            goodsTypeList: [],
            productTypeList: [],
            expenseAccountList: [],
            settlementManagementOrderTypeList: [],
            settlementMethodList: [],
            temperatureTypeDicts: [],
            visible: this.modelValue,
            feeBreakdownColumns: [
                { title: '费用科目', key: 'costType', align: 'center', width: '180px', columnShow: true },
                { title: '预估金额', key: 'costData', align: 'center', width: '120px', columnShow: true },
                { title: '合同价格', key: 'costContractPrice', align: 'center', width: '120px', columnShow: true },
                { title: '费用计算明细', key: 'textFormula', align: 'center', minWidth: '120px', columnShow: true }
            ],
            vehicleTypeList: []
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    watch: {
        modelValue: {
            handler(val) {
                this.visible = val;
            },
            immediate: true
        }
    },
    created() {
        this.getDict();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.productTypeList = await this.getDictList('fourpl_product_type');
            this.expenseAccountList = await this.getDictList('cost_order_type');
            this.settlementManagementOrderTypeList = await this.getDictList('cost_settlement_management_order_type');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
            this.temperatureTypeDicts = await this.getDictList('fourpl_temperature_type');
        },
        hideOrderCost() {
            this.visible = false;
            this.$emit('update:modelValue', this.visible);
        }
    }
};
</script>
<style lang="scss" scoped>
.card__two__column {
    .d-box-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        font-size: 14px;

        .d-box-column {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            .d-box-info {
                display: flex;
                justify-content: space-between;

                :nth-child(1) {
                    flex-shrink: 0;
                    flex-grow: 0;
                    text-align: left;
                    margin-right: 10px;
                    color: #999999;
                }

                :nth-child(2) {
                    color: #333333;
                }
            }
        }

        .d-box-column:first-child {
            border-right: 1px solid #e6ebf5;
            padding-right: 10px;
        }
        .d-box-column:last-child {
            padding-left: 10px;
        }
    }
}
.feeBreakdown__total {
    padding: 20px 10px 10px 10px;
    display: flex;
    gap: 15px 30px;
    flex-wrap: wrap;
    .total__item {
        display: flex;
        gap: 10px;
        align-items: baseline;
        font-size: 14px;
    }
    .total__title {
        white-space: nowrap;
    }
    .total__num {
        font-weight: bold;
        font-size: 18px;
        color: #5670fe;
    }
}
</style>
