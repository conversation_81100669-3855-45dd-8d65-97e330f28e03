import request from '@/utils/request';
export default {
    // 列表
    listTemplate: function (params) {
        return request.get('/tms/config/company/template/list', params);
    },
    // 合作货主下拉框
    cooperateSelect: function (params) {
        return request.get('/tms/cooperate/company/cooperateSelect', params);
    },
    // 模板下拉框
    xmlTemplateSelect: function (params) {
        return request.get('/printer/xmlTemplate/select', params);
    },
    // 新增模板配置
    addTemplate: function (data) {
        return request.post('/tms/config/company/template/add', data);
    },
    // 修改
    editTemplate: function (data) {
        return request.post('/tms/config/company/template/edit', data);
    },
    // 删除
    delTemplate: function (params) {
        return request.get('/tms/config/company/template/del', params);
    },
    // 预览
    viewTemplate: function (params, progressEvent) {
        let config = {
            headers: { 'X-Requested-With': 'XMLHttpRequest' },
            onDownloadProgress: progressEvent
        };
        return request.get('/tms/config/company/template/view', params, config, false, 'blob');
    },
    // 根据货主查询模板配置
    getByCompanyTemplate: function (params) {
        return request.get('/tms/config/company/template/getByCompany', params);
    },
    // 根据保温箱查询模板配置
    getByIncubatorTemplate: function (params) {
        return request.get('/tms/receipt/fourplIncubatorDetail/printColdChain/getTemplate', params);
    }
};
