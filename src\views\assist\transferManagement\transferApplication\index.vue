<template>
    <div class="app-container">
        <el-card class="box-card Botm" style="margin: 10px;">
            <el-form :model="queryParams" ref="queryRef" :inline="true" class="form_130">
                <TopTitle :handleQuery="getList" :resetQuery="resetQuery">
                    <el-form-item label="出货经手人" prop="shipmentBy">
                        <el-input v-model="queryParams.shipmentBy" placeholder="请输入出货经手人" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="入货经手人" prop="incomingBy">
                        <el-input v-model="queryParams.incomingBy" placeholder="请输入入货经手人" clearable class="form_225"
                            :disabled="incomingByDisabled" />
                    </el-form-item>
                    <el-form-item label="商品名称" prop="commodityName">
                        <el-input v-model="queryParams.commodityName" placeholder="请输入商品名称" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="商品自编码" prop="commoditySelfCode">
                        <el-input v-model="queryParams.commoditySelfCode" placeholder="请输入商品自编码" clearable
                            class="form_225" />
                    </el-form-item>
                    <el-form-item label="申请人" prop="applyByName">
                        <el-input v-model="queryParams.applyByName" placeholder="请输入申请人" clearable class="form_225" />
                    </el-form-item>

                    <el-form-item label="调拨类型" prop="transferType">
                        <el-select v-model="queryParams.transferType" placeholder="请选择调拨类型" class="form_225"
                            :disabled="transferTypeDisabled || activeName == '1'">
                            <el-option :label="item.name" :value="item.value" v-for="item in allocationType"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="申请日期" prop="applyDate">
                        <div class="box_date">
                            <el-date-picker v-model="queryParams.applyDate" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" />
                        </div>
                    </el-form-item>
                    <el-form-item label="确认状态" prop="confirmStatus">
                        <el-select v-model="queryParams.confirmStatus" placeholder="请选择确认状态" class="form_225"
                            :disabled="activeName == '1'">
                            <el-option :label="item.name" :value="item.value" v-for="item in transferConfirmationStatus"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="审核状态" prop="auditStatus">
                        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" class="form_225"
                            :disabled="activeName == '1'">
                            <el-option :label="item.name" :value="item.value" v-for="item in reviewStatus"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                </TopTitle>
            </el-form>
        </el-card>
        <el-card class="box-card" style="margin: 10px;">
            <div class="cardCnt">
                <el-tabs tab-position="left" style="height: 100%" class="demo-tabs" v-model="activeName"
                    @tab-click="handleClick">
                    <el-tab-pane label="确认列表" name="1"></el-tab-pane>
                    <el-tab-pane label="申请列表" name="2"></el-tab-pane>
                </el-tabs>
                <div style="width:92%;margin-left:20px">
                    <div style="display:flex;justify-content: space-between;">
                        <el-button type="primary" @click="() => handleAdd(undefined, 'add')"
                            style="margin-bottom:10px">创建调拨申请</el-button>
                        <RightToptipBarV2 @handleRefresh="getList" className="transferManagement_transferApplication" />
                    </div>
                    <div v-loading="loading" v-if="activeName == '2'">
                        <DragTableColumn
                            v-if="allocationType.length && reviewStatus.length && transferConfirmationStatus.length"
                            :columns="columns" :tableData="list" className="transferManagement_transferApplication"
                            v-model:queryParams="queryParams" :getList="getList" :row-style="tableRowStyle">
                            <template v-slot:operate="{ scopeData }">
                                <el-button link type="primary" @click="handleAdd(scopeData.row, 'detail')"><img
                                        src="@/assets/icons/detail.png" style="margin-right:5px" />详情</el-button>
                                <el-button link type="danger" @click="handleDelete(scopeData.row)" v-if="activeName == '2'"
                                    :disabled="isDeleteAuthority(scopeData.row.createBy.id, ['1', '4', '6'], scopeData.row.auditStatus)"><img
                                        src="@/assets/icons/delete.png" style="margin-right:5px" />删除</el-button>
                                <el-button link type="success" @click="handleReview(scopeData.row)"><img
                                        src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                            </template>
                        </DragTableColumn>
                        <div style="float: right;">
                            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                                v-model:limit="queryParams.size" @pagination="getList" />
                        </div>
                    </div>
                    <div v-loading="loading" v-if="activeName == '1'">
                        <DragTableColumn
                            v-if="allocationType.length && reviewStatus.length && transferConfirmationStatus.length"
                            :columns="columns2" :tableData="list" className="transferManagement_transferApplication2"
                            v-model:queryParams="queryParams" :getList="getList" :row-style="tableRowStyle">
                            <template v-slot:operate="{ scopeData }">
                                <el-button link type="primary" @click="handleAdd(scopeData.row, 'detail')"><img
                                        src="@/assets/icons/detail.png" style="margin-right:5px" />详情</el-button>
                                <el-button link type="danger" @click="handleDelete(scopeData.row)" v-if="activeName == '2'"
                                    :disabled="isDeleteAuthority(scopeData.row.createBy.id, ['1', '4', '6'], scopeData.row.auditStatus)"><img
                                        src="@/assets/icons/delete.png" style="margin-right:5px" />删除</el-button>
                                <el-button link type="success" @click="handleReview(scopeData.row)"><img
                                        src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                            </template>
                        </DragTableColumn>
                        <div style="float: right;">
                            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                                v-model:limit="queryParams.size" @pagination="getList" />
                        </div>
                    </div>
                </div>
            </div>



        </el-card>
        <Review :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="() => reviewVisible = false"
            :data="reviewData" />

        <ExitApplication :open="open" v-if="open" :beforClose="() => open = false" :modalType="modalType" :getList="getList"
            :title="title" :detailData="detailData" :activeName="activeName" />

    </div>
</template>
 
<script setup >
import { reactive, ref, getCurrentInstance, toRefs, onMounted } from 'vue'
import { Plus, UploadFilled, Search, ArrowDown } from '@element-plus/icons-vue'
import Review from './component/review.vue';
import ExitApplication from './component/exitApplication.vue';
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import moment from 'moment'
import transferApplication from '@/api/erp/transferApplication'
import isDeleteAuthority from '@/utils/isDeleteAuthority'
import tool from '@/utils/tool';
const { proxy } = getCurrentInstance();
const list = ref([]);
const loading = ref(false);
const total = ref(0);
const open = ref(false)
const modalType = ref(null)
const title = ref(undefined)
const reviewVisible = ref(false)
const optionList = ref([])
const reviewData = ref({})
const reviewStatus = ref([])
const detailData = ref({})
const allocationType = ref([])
const transferConfirmationStatus = ref([])
const activeName = ref('1')
const incomingByDisabled = ref(false)
const transferTypeDisabled = ref(false)
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
    },

});

const { queryParams } = toRefs(data);
const tableRowStyle = ({ row, rowIndex }) => {
    if (row.auditStatus == '6') {  // 草稿
        return {
            color: '#e6a23c'
        }
    } else if (row.auditStatus == '1') {  // 待审核
        return {
            color: '#409eff'
        }
    } else if (row.auditStatus == '2') {  //审核中
        return {
            color: '#67c23a'
        }
    } else if (row.auditStatus == '4') {  //已驳回
        return {
            color: '#ff4800'
        }
    }
}
const handleAdd = (row, type) => {
    open.value = true
    modalType.value = type
    if (row && type == 'detail') {
        title.value = "查看调拨申请"
        detailData.value = row
    }
    if (!row && type == 'add') {
        title.value = "创建调拨申请"
    }
}
const handleClickTab = value => {
    
}
const handleClick = (value) => {
    activeName.value = value.props.name
    queryParams.value = { current: 1, size: 10, }
    if (value === '2') {
        incomingByDisabled.value = false
        transferTypeDisabled.value = false
        queryParams.value.incomingBy = undefined
        getList()
    } else {
        queryParams.value.incomingBy = tool.data?.get("ROLE_LIST")?.length && tool.data?.get("ROLE_LIST")?.[0]?.enName == 'ywjl' ? tool.data?.get("USER_INFO")?.name : undefined
        incomingByDisabled.value = tool.data?.get("ROLE_LIST")?.length && tool.data?.get("ROLE_LIST")?.[0]?.enName == 'ywjl' ? true : false
        transferTypeDisabled.value = tool.data?.get("ROLE_LIST")?.length && tool.data?.get("ROLE_LIST")?.[0]?.enName == 'ywjl' ? true : false
        getList()
    }
}
/** 查询角色列表 */
function getList() {
    loading.value = true
    const params = { ...queryParams.value }
    params.beginApplyDate = params.applyDate?.length ? moment(params.applyDate[0]).format('YYYY-MM-DD 00:00:00') : undefined
    params.endApplyDate = params.applyDate?.length ? moment(params.applyDate[1]).format('YYYY-MM-DD 23:59:59') : undefined
    params['shipmentBy.name'] = params.shipmentBy
    params['incomingBy.name'] = params.incomingBy
    if (activeName.value == '1') {
        params.searchType = 'confirm'
    }
    delete params.applyDate
    delete params.shipmentBy
    delete params.incomingBy
    transferApplication.getList(params).then(res => {
        if (res.code == 200) {
            list.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}

/**
 * @description: 点击审核记录
 * @return {*}
 */
const handleReview = (row) => {
    reviewVisible.value = true
    reviewData.value = row
}




/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    activeName.value = '1'
    getList()

}
async function dict() {
    reviewStatus.value = await proxy.getDictList('erp_review_status')
    allocationType.value = await proxy.getDictList('allocation_type')
    transferConfirmationStatus.value = await proxy.getDictList('transfer_confirmation_status')


}

/**
 * @description: 删除
 * @return {*}
 */
const handleDelete = row => {
    proxy.$confirm('是否确认删除改数据项?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        transferApplication.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                proxy.msgSuccess('删除成功')
                getList()
            } else {
                proxy.msgError(res.msg)
            }
        })
    }).catch(() => { });
}
/**
 * @description: 获取经手人
 * @return {*}
 */

const getUsers = () => {
    warehouseNumberManagement.getUser().then(res => {
        if (res.code == 200) {
            optionList.value = res.data?.records
        }

    })
}
onMounted(() => {
    dict()
    getList();
    getUsers()
})
const columns = ref(
    [
        {
            label: '申请编号',
            prop: 'applyNo',
            minWidth: 180,
        }, {
            label: '申请日期',
            prop: 'applyDate',
            type: 'date'
        },

        // {
        //     label: '商品名称',
        //     prop: 'commodityName'
        // }
        // ,
        {
            label: '调拨类型',
            prop: 'transferType',
            type: "status",
            filters: allocationType
        },
        {
            label: '确认状态',
            prop: 'confirmStatus',
            type: "status",
            filters: transferConfirmationStatus
        },
        {
            label: '审核状态',
            prop: 'auditStatus',
            type: 'status',
            filters: reviewStatus
        }, {
            label: '出货经手人',
            prop: 'shipmentBy.name'
        }, {
            label: '出货库号',
            prop: 'shipmentWarehouseNo.warehouseNumber'
        }, {
            label: '入货经手人',
            prop: 'incomingBy.name'
        }, {
            label: '入货库号',
            prop: 'incomingWarehouseNo.warehouseNumber'
        }, {
            label: '合计调拨数量',
            prop: 'transferAmount'
        }, {
            label: '合计调拨差价',
            prop: 'transferQuantity'
        }, {
            label: '操作',
            type: 'operate',
            fixed: 'right',
            minWidth: 300
        }
    ]
)
const columns2 = ref(
    [
        {
            label: '申请编号',
            prop: 'applyNo',
            minWidth: 180,
        }, {
            label: '申请日期',
            prop: 'applyDate',
            type: 'date'
        },
         {
            label: '出货经手人',
            prop: 'shipmentBy.name'
        }, {
            label: '出货库号',
            prop: 'shipmentWarehouseNo.warehouseNumber'
        }, {
            label: '入货经手人',
            prop: 'incomingBy.name'
        }, {
            label: '入货库号',
            prop: 'incomingWarehouseNo.warehouseNumber'
        }, {
            label: '合计调拨数量',
            prop: 'transferAmount'
        }, {
            label: '操作',
            type: 'operate',
            fixed: 'right',
            minWidth: 300
        }
    ]
)
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

::v-deep .labelStyle {
    .el-form-item__label {
        // margin-left: 10px;
    }
}

::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

::v-deep .serchBtn {
    .el-form-item__content {
        display: flex;
        justify-content: end;
    }
}

.box_date {
    width: 220px;
}

.cardCnt {
    display: flex
}

::v-deep .el-tabs--left {
    overflow: visible
}</style>