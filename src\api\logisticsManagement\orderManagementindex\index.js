import http from "@/utils/request"

export default {
    // 订单列表查询箱码-全部
    getBoxCodeList: function (params) {
        return http.get('/tms/transBoxCode/list', params);
    },
    // 查询异常件列表数据
    abnormalGoodsList: function (params) {
        return http.get('/tms/receipt/abnormalGoods/list', params);
    },
    // 保存异常件
    abnormalGoodsSave: function (data) {
        return http.post("/tms/receipt/abnormalGoods/save", data);
    },
	// 根据Id获取异常件数据
	abnormalGoodsQueryById: function (params) {
		return http.get('/tms/receipt/abnormalGoods/queryById', params);
	},
	// 异常件处理
	getUpdateReviewStatus: function (params) {
		return http.get('/tms/receipt/abnormalGoods/updateReviewStatus', params);
	},
    // 异常件处理
	abnormalGoodsDelete: function (params) {
		return http.delete('/tms/receipt/abnormalGoods/delete', params);
	},
    // 导出
    abnormalGoodsExport: function (params,config,resDetail,responseType) {
    return http.get("/tms/receipt/abnormalGoods/export", params,config,resDetail,responseType,1);
  },

}
