<?xml version="1.0" encoding="UTF-8"?>
 <trees>
	<tree path="/src/views/logisticsConfiguration" title="物流配置"/>
	<tree path="/src/views/logisticsConfiguration/operationConfiguration" title="运营配置"/>
	<tree path="/src/views/logisticsConfiguration/operationConfiguration/CarType.vue" title="车辆类型" extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/operationConfiguration/CarManagement.vue" title="车辆管理" extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/operationConfiguration/DriverManagement.vue" title="司机管理" extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/serviceNetwork/DistributionBranch.vue" title="集散网点" extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/serviceNetwork/CollectionArea.vue" title="集货区域" extension="vue"/>
	 <tree path="/src/views/logisticsConfiguration/serviceNetwork" title="服务网点"/>
	 <tree path="/src/views/logisticsConfiguration/serviceNetwork/collectArea.vue" title="揽收区域" extension="vue"/>
	 <tree path="/src/views/logisticsConfiguration/serviceNetwork/LandAllocationArea.vue" title="地配区域"
		   extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/serviceNetwork/TripartiteLogistics.vue" title="三方物流" extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/otherConfiguration" title="其他配置"/>
	<tree path="/src/views/logisticsConfiguration/serviceNetwork/CollectArea.vue" title="揽收区域" extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/enterpriseCooperation" title="企业合作管理"/>
	<tree path="/src/views/logisticsConfiguration/enterpriseCooperation/CooperativeCargoOwners.vue" title="合作货主"
		  extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/otherConfiguration/PaymentAllocation.vue" title="支付配置"
		  extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/otherConfiguration/ThermosphereManagement.vue" title="温层配置"
		  extension="vue"/>
    <tree path="/src/views/logisticsConfiguration/customerPrepayment" title="客户预付款"/>
    <tree path="/src/views/logisticsConfiguration/customerPrepayment/CustomerPrepaymentBalance.vue" title="客户预存款"
          extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/customerPrepayment/CustomerPrepaymentBalanceDetail.vue" title="交易明细"
		  extension="vue"/>
    <tree path="/src/views/logisticsManagement" title="物流管理"/>
    <tree path="/src/views/logisticsManagement/orderManagement/PlaceAnOrder.vue" title="下单" extension="vue"/>
	<tree path="/src/views/logisticsManagement/orderManagement/OrderList.vue" title="订单列表" extension="vue"/>
    <tree path="/src/views/orderComponents/ConfirmAnOrder.vue" title="确认订单" extension="vue"/>
    <tree path="/src/api/logisticsConfiguration" title="物流配置"/>
	<tree path="/src/api/logisticsConfiguration/customerPrepayment.js" title="客户预存款" extension="js"/>
	<tree path="/src/api/logisticsConfiguration/enterpriseCooperation.js" title="企业合作" extension="js"/>
	<tree path="/src/api/logisticsConfiguration/operationConfiguration.js" title="运营配置" extension="js"/>
	<tree path="/src/api/logisticsConfiguration/otherConfiguration.js" title="其他配置" extension="js"/>
	<tree path="/src/api/logisticsConfiguration/serviceNetwork.js" title="服务网点" extension="js"/>
	<tree path="/src/api/logisticsManagement" title="物流管理"/>
	<tree path="/src/api/logisticsManagement/orderManagement.js" title="订单管理" extension="js"/>
    <tree path="/src/views/logisticsManagement/orderManagement/transferCustomer.vue" title="客户签收" extension="vue"/>
    <tree path="/src/views/orderComponents/OrderDetailTrans.vue" title="订单详情" extension="vue"/>
	<tree path="/src/views/logisticsManagement/orderManagement/TransferCustomer.vue" title="签收" extension="vue"/>
	<tree path="/src/views/orderComponents/PrintBoxTag.vue" title="打印箱签" extension="vue"/>
    <tree path="/src/views/orderComponents/OrderDetails" title="订单明细"/>
    <tree path="/src/views/logisticsManagement/orderManagement" title="订单管理"/>
	<tree path="/src/views/logisticsManagement/orderTransaction" title="订单异动"/>
	<tree path="/src/views/logisticsManagement/orderTransaction/OrderTransactionList.vue" title="订单异动列表"
		  extension="vue"/>
    <tree path="/src/views/shipperEnd/ownersOrderList" title="货主端订单管理"/>
	<tree path="/src/views/shipperEnd" title="货主端"/>
    <tree path="/src/views/logisticsManagement/waybillTracking" title="运单跟踪"/>
    <tree path="/src/views/logisticsManagement/waybillTracking/WaybillTrackingDetails.vue" title="操作记录"
          extension="vue"/>
	<tree path="/src/views/logisticsManagement/waybillTracking/TrackingListOfWaybills.vue" title="运单跟踪"
		  extension="vue"/>
	<tree path="/src/views/logisticsManagement/waybillTracking/TransportRecord.vue" title="运输记录" extension="vue"/>
    <tree path="/src/views/orderComponents" title="订单相关组件"/>
	<tree path="/src/views/orderComponents/OrderTransactionDetails.vue" title="订单异动记录" extension="vue"/>
    <tree path="/src/views/shipperEnd/ownersOrderList/ModifyOrder.vue" title="修改订单" extension="vue"/>
    <tree path="/src/views/orderComponents/TemperatureRecording.vue" title="温度记录" extension="vue"/>
	<tree path="/src/views/carrierFunction" title="承运端功能"/>
	<tree path="/src/views/carrierFunction/cabineBoxRecord.vue" title="保温箱记录" extension="vue"/>
    <tree path="/src/views/carrierFunction/packingDetails.vue" title="装箱详情" extension="vue"/>
    <tree path="/src/views/hzAddress" title="货主地址簿"/>
    <tree path="/src/views/logisticsManagement/orderManagement/SignAudit.vue" title="签收审核" extension="vue"/>
    <tree path="/src/views/orderComponents/OrderModificationDetails.vue" title="订单修改明细" extension="vue"/>
    <tree path="/src/views/logisticsManagement/outboundManagement" title="出库管理"/>
	<tree path="/src/views/logisticsManagement/outboundManagement/WarehouseOutSelfLifting.vue" title="出库登记"
		  extension="vue"/>
	<tree path="/src/views/logisticsManagement/outboundManagement/WarehouseRecords.vue" title="出库记录" extension="vue"/>
	<tree path="/src/views/logisticsManagement/outboundManagement/WarehouseDetail.vue" title="登记详情" extension="vue"/>
    <tree path="/src/views/logisticsManagement/orderManagement/collectOrder.vue" title="收藏订单" extension="vue"/>
    <tree path="/src/components/AdvertisingComponent" title="广告"/>
    <tree path="/src/views/logisticsConfiguration/prepaymentFromTheShipper" title="货主预付款"/>
    <tree path="/src/views/platformConfiguration" title=""/>
    <tree path="/src/views/carrierFunction/BillingRecord.vue" title="百望云开票记录" extension="vue"/>
	<tree path="/src/views/carrierFunction/BaiwangCloudBillingRecord.vue" title="百望云开票记录" extension="vue"/>
	<tree path="/src/views/carrierFunction/PrepaymentCollectionInvoicingStatistics.vue" title="预付款收款开票统计"
		  extension="vue"/>
	<tree path="/src/api/carrierEnd/dataQuery.js" title="数据查询" extension="js"/>
    <tree path="/src/views/carrierFunction/companyAddressBook.vue" title="承运端地址簿" extension="vue"/>
    <tree path="/src/views/carrierFunction/ValueAddedServices.vue" extension="vue"/>
    <tree path="/src/views/carrierFunction/HandoverOrderConfiguration.vue" title="交接单配置" extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/operationConfiguration/DriverConfiguration.vue" title="司机配置"
		  extension="vue"/>
	<tree path="/src/views/logisticsConfiguration/operationConfiguration/TripartiteDriverArrangement.vue" title="三方司机配置"
		  extension="vue"/>
	<tree path="/src/api/carrierEnd/handoverOrderConfiguration.js" title="冷链交接单配置" extension="js"/>
    <tree path="/src/views/auditManagement" title="审批管理"/>
	<tree path="/src/api/auditManagement" title="审批流"/>
    <tree path="/src/views/auditManagement/AuditAndFlow.vue" title="系统审批流配置" extension="vue"/>
	<tree path="/src/views/auditManagement/CarrierAuditAndFlow.vue" title="组织审批流配置" extension="vue"/>
	<tree path="/src/views/auditManagement/approvalTaskList.vue" title="审批任务列表" extension="vue"/>
    <tree path="/src/views/auditManagement/PreDepositTopUpAudit.vue" title="预存款充值审批、" extension="vue"/>
    <tree path="/src/views/auditManagement/PaymentOrderAudit.vue" title="付款单支付" extension="vue"/>
    <tree path="/src/views/carrierFunction/receiveThePayment" title="到款记录&到款认款"/>
	<tree path="/src/views/carrierFunction/receiveThePayment/ReceiptRecords.vue" title="到款记录" extension="vue"/>
	<tree path="/src/views/carrierFunction/receiveThePayment/ReceiptOfPayment.vue" title="到款认款" extension="vue"/>
	<tree path="/src/components/operationRecord" title="操作记录"/>
	<tree path="/src/components/approvalProgress" title="审批进度"/>
	<tree path="/src/views/carrierFunction/receiveThePayment/components/SubscriptionApply.vue" title="认款申请"
		  extension="vue"/>
    <tree path="/src/views/auditManagement/ReceiptApplicationAudit.vue" title="认款审批" extension="vue"/>
</trees>
