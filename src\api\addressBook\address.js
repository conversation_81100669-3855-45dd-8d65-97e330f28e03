import request from '@/utils/request';
export default {
    // 地址列表
    listBook: function (params) {
        return request.get('/tms/config/addressBook/list', params);
    },
    // 新增地址列表
    addBook: function (params) {
        return request.post('/tms/config/addressBook/save', params);
    },
    // 地址列表详情
    getBook: function (id) {
        return request.get('/tms/config/addressBook/queryById', id);
    },
    // 货主列表
    getCompanySelect: function (params) {
        return request.get('/tms/cooperate/company/cooperateSelect', params);
    },
    // 删除地址
    delBook: function (id) {
        return request.delete('/tms/config/addressBook/delete', id);
    },
    // 修改默认地址
    updateDefaultAddress: function (params) {
        return request.get('/tms/config/addressBook/updateDefaultAddress', params);
    }
};
