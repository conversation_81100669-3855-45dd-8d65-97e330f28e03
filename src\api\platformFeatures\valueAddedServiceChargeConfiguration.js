import request from '@/utils/request'
export default {
    // 获取增值服务费配置列表
    addedServiceList: function (params) {
        return request.get('/cost/addedService/config/list', params);
    },
    // 新增增值服务费配置
    addAddedService: function (params) {
        return request.post('/cost/addedService/config/save', params);
    },
    // 修改状态
    updateAddServiceStatus: function (params) {
        return request.get('/cost/addedService/config/updateStatus', params);
    }
}
