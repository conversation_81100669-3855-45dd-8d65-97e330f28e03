<template>
    <div class="app-container">
        <el-card class="box-card Botm" style="margin: 10px;">
            <el-form :model="queryParams" ref="queryRef" :inline="true" class="form_130">
                <!-- <div class="box"> -->
                <TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery">
                    <el-form-item label="供应商名称" prop="supplier">
                        <el-input v-model="queryParams.supplier" placeholder="请输入供应商名称" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="商品名称" prop="commodityName">
                        <el-input v-model="queryParams.commodityName" placeholder="请输入商品名称" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="结算方式" prop="settlementMethod">
                        <el-select v-model="queryParams.settlementMethod" placeholder="请选择结算方式" class="form_225">
                            <el-option :label="item.name" :value="item.value" v-for="item in settlementMethod"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="单据编号" prop="orderCode">
                        <el-input v-model="queryParams.orderCode" placeholder="请输入单据编号" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="订单状态" prop="status">
                        <el-select v-model="queryParams.status" placeholder="请选择订单状态" class="form_225">
                            <el-option :label="item.name" :value="item.value" v-for="item in reviewStatus"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="入库状态" prop="inventoryStatus">
                        <el-select v-model="queryParams.inventoryStatus" placeholder="请选择入库状态" class="form_225">
                            <el-option :label="item.name" :value="item.value" v-for="item in warehousingStatus"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="制单人" prop="preparedBy">
                        <el-input v-model="queryParams.preparedBy" placeholder="请输入制单人" clearable class="form_225" />
                    </el-form-item>
                    <el-form-item label="库号" prop="warehouseNumber">
                        <el-select v-model="queryParams.warehouseNumber" placeholder="请选择库号" filterable class="form_225"
                            clearable>
                            <el-option :label="item.warehouseNumber" :value="item.id" v-for="item in warehouseNumberList"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="经手人" prop="handledBy">
                        <el-select v-model="queryParams.handledBy" filterable placeholder="请选择经手人" clearable
                            class="form_225">
                            <el-option v-for="item in optionList" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="自营扣率" prop="selfRate">
                        <el-select v-model="queryParams.selfRate" placeholder="请选择自营扣率" class="form_225">
                            <el-option :label="item.name" :value="item.value" v-for="item in deductionRateList"
                                :key="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="审核人" prop="approvalManName">
                        <el-input v-model="queryParams.approvalManName" placeholder="请输入审核人" clearable class="form_225" />
                    </el-form-item>

                    <!-- 订单日期 -->
                    <el-form-item label="单据日期" prop="createDate">
                        <div class="box_date">
                            <el-date-picker v-model="queryParams.createDate" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" />
                        </div>
                    </el-form-item>
                    <el-form-item label="付款期限" prop="termsPayment">
                        <div class="box_date">
                            <el-date-picker v-model="queryParams.termsPayment" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" />
                        </div>

                    </el-form-item>
                </TopTitle>
                <!-- </div> -->
            </el-form>
        </el-card>
        <el-card class="box-card" style="margin: 10px;">
            <div style="display:flex;justify-content: space-between;align-items:center">
                <div style="display:flex;align-items:center;margin-bottom:10px">
                    <el-select v-model="operateMore" placeholder="请选择批量操作" clearable>
                        <el-option label="草稿单据批量提交" value="1" />
                        <el-option label="草稿单据批量删除" value="2" />
                    </el-select>
                    <el-button :disabled="!chooseList.length || !operateMore" style="margin-left: 10px" type="primary"
                        @click="() => handleOprateMore()">确认
                    </el-button>
                    <el-button type="primary" @click="() => handleAdd()">采购下单</el-button>
                </div>
                <RightToptipBarV2 @handleRefresh="getList" className="purchasingManagement_purchasingOrder" />
            </div>
            <div v-loading="loading" style="min-height:200px">
                <DragTableColumn v-if="reviewStatus.length && warehousingStatus.length && settlementMethod.length"
                    :columns="columns" :tableData="list" v-model:queryParams="queryParams"
                    className="purchasingManagement_purchasingOrder" :getList="getList" :row-style="tableRowStyle"
                    @selection-change="handleChangeSelection" ref="tableRef">
                    <template v-slot:operate="{ scopeData }">
                        <el-button link type="primary" @click="handleAdd(scopeData.row, 'detail')"><img
                                src="@/assets/icons/detail.png" style="margin-right:5px" />详情</el-button>
                        <el-button link type="primary" @click="handleAdd(scopeData.row, 'edit')"
                            :disabled="scopeData.row.status == '1' || scopeData.row.status == '2' || scopeData.row.status == '3'"><img
                                src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                        <el-button link type="danger" @click="handleDelete(scopeData.row)"
                            :disabled="isDeleteAuthority(scopeData.row.createBy.id, ['1', '4', '6'], scopeData.row.status)"><img
                                src="@/assets/icons/delete.png" style="margin-right:5px" />删除</el-button>
                        <el-dropdown>
                            <el-button link type="primary" style="margin-top: 3px;">更多<el-icon>
                                    <ArrowDown />
                                </el-icon></el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <!-- <el-dropdown-item @click="handleAdd(scope.row, 'draft')" v-if="false"
                                        style="color:#e6a23c"><img src="@/assets/icons/draft.png"
                                            style="margin-right:5px" />查看草稿</el-dropdown-item> -->
                                    <el-dropdown-item @click="handleReview(scopeData.row)" style="color:#67c23a"><img
                                            src="@/assets/icons/review.png"
                                            style="margin-right:5px" />操作记录</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </DragTableColumn>
            </div>
            <div style="float: right;">
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                    v-model:limit="queryParams.size" @pagination="getList" />
            </div>
        </el-card>

        <purchasingModal v-if="open" :open="open" :beforClose="beforClose" :title="title" :modalType="modalType"
            :getList="getList" ref="purchasingModalRef" :detailRow="detailRow" :closeNoTip="()=>open = false"/>
        <DetailCom v-if="detailOpen" :open="detailOpen" :beforClose="DetailBeforClose" :getList="getList"
            :detailRow="detailRow" />

        <Review :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewData" />
        <el-dialog v-model="moreVisible" title="批量操作提示" width="30%" :before-close="() => moreVisible = false"
            :show-close="chooseNum !== 2" :close-on-click-modal="false">
            <div class="moreVisibleCnt" v-if="chooseNum == 1">
                <p>当前选中单据{{ chooseList.length }}个，可批量{{ operateMore == '1' ? '提交' :'删除' }}的草稿单据{{ chooseSure.length }}个</p>
                <p>若调整单据范围，请点击“取消”</p>
                <p>若确认继续提交，请点击“确定”</p>
            </div>
            <div v-if="chooseNum == 2">
                <p style="margin: 15px 0 35px 0;text-align: center;font-size: 16px">
                    操作正在进行中，请勿关闭当前对话框
                </p>
                <el-progress :percentage="numHand" :stroke-width="26" :text-inside="true" />
            </div>
            <div v-if="chooseNum == 3">
                <p style="margin: 15px 0 15px 0;text-align: center;font-size: 16px;line-height: 25px"> 本次批量{{ operateMore == '1' ? '提交' :'删除' }}草稿单据
                    <b>{{
                        chooseSure.length
                    }}</b> 个
                </p>
                <p style="margin: 15px 0 35px 0;text-align: center;font-size: 15px;line-height: 25px">
                    {{ operateMore == '1' ? '下单' :'删除' }}成功：<b>{{ resData.suc }}</b>个 <span style="margin-left: 10px">{{ operateMore == '1' ? '下单' :'删除' }}失败：<b>{{ resData.err }}</b>个</span>
                </p>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="moreVisible = false" v-if="chooseNum !== 2 && chooseNum !== 3">取消</el-button>
                    <el-button type="primary" @click="() => handleMoreSubmit()" v-if="chooseSure.length && chooseNum !== 2">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup >
import moment from 'moment'
import { reactive, ref, getCurrentInstance, toRefs, onMounted } from 'vue'
import { Plus, UploadFilled, Search, ArrowDown } from '@element-plus/icons-vue'
import CardHeader from '@/components/CardHeader'
import purchasingModal from './component/purchasingModal.vue';
import Review from './component/review.vue';
import DetailCom from './component/detailCom.vue'
import purchasingManagement from '@/api/erp/purchasingManagement'
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import TopTitle from '@/components/topTitle'
import isDeleteAuthority from '@/utils/isDeleteAuthority'
import { ClickOutside as vClickOutside, ElLoading, ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance();
const showSearch = ref(false)
const list = ref([]);
const loading = ref(false);
const total = ref(0);
const reviewStatus = ref([])
const settlementMethod = ref([])
const open = ref(false)
const title = ref("")
const modalType = ref("")
const reviewVisible = ref(false)
const deductionRateList = ref([])
const warehousingStatus = ref([])
const warehouseNumberList = ref([])
const detailRow = ref(null)
const optionList = ref([])
const reviewData = ref({})
const chooseList = ref([])
const operateMore = ref(undefined)
const moreVisible = ref(false)
const detailOpen = ref(false)
const chooseSure = ref([])
const tableRef = ref(null)
const chooseNum = ref(1)
//百分比
const numHand = ref(0)
//提交结果成功失败数据
const resData = ref({
    suc: 0,
    err: 0
})
const data = reactive({
    form: {},
    queryParams: {
        current: 1,
        size: 10,
    },
    rules: {
    },
});

const { queryParams, form, rules } = toRefs(data);
const tableRowStyle = ({ row, rowIndex }) => {
    if (row.status == '6') {  // 草稿
        return {
            color: '#e6a23c'
        }
    } else if (row.status == '1') {  // 待审核
        return {
            color: '#409eff'
        }
    } else if (row.status == '2') {  //审核中
        return {
            color: '#67c23a'
        }
    } else if (row.status == '4') {  //已驳回
        return {
            color: '#ff4800'
        }
    }
}
const handleChangeSelection = key => {
    chooseList.value = key
}
const handleOprateMore = () => {
    chooseSure.value = []
    numHand.value = 0
    chooseList.value?.forEach(item => {
        if (item.status === '6') {
            chooseSure.value.push(item)
        }
    })
    moreVisible.value = true
}
//开始走进度条
const rightChooseFn = () => {
    chooseNum.value = 2
    let arr = []
    chooseSure.value.forEach(item => {
        arr.push(item.id)
    })
    let str = arr.join(',')
    let ranNum = Math.floor(Math.random() * (70 - 40 + 1)) + 40;
    let handNum = setInterval(() => {
        numHand.value++
        if (numHand.value == ranNum) {
            clearInterval(handNum)
            submitList(str)
        }
    }, 36)
}
const submitList = (str) => {
    if(operateMore.value == '1'){
        purchasingManagement.submitAuditMore({
        orderIds: str
    }).then((res) => {
        if (res.code == 200) {
            let handNum = setInterval(() => {
                numHand.value++
                if (numHand.value == 100) {
                    clearInterval(handNum)
                    redarkFn(res.data)
                }
            })
        } else {
            resData.value.suc = 0
            resData.value.err = chooseSure.value.length
            ElMessage.error('提交失败')
        }
    })
    }else{
        purchasingManagement.delete({ ids: str }).then(res => {
            if (res.code == 200) {
                let handNum = setInterval(() => {
                numHand.value++
                if (numHand.value == 100) {
                    clearInterval(handNum)
                    resData.value.suc = chooseSure.value.length
                    resData.value.err = 0
                    chooseNum.value = 3
                }
            })
            } else {
            resData.value.suc = 0
            resData.value.err = chooseSure.value.length
            ElMessage.error('提交失败')
            chooseNum.value = 3
            }
        })
    }

}
//提交后的数据分析
const redarkFn = (flag) => {
    resData.value.suc = 0
    resData.value.err = 0
    if (flag) {
        flag.forEach((item) => {
            if (item.status == 1) {
                resData.value.suc++
            } else {
                resData.value.err++
            }

        })
    }
    chooseNum.value = 3
}
const handleMoreSubmit = () => {
    if (chooseNum.value == 1) {
        rightChooseFn()
        return
    }
    if (chooseNum.value == 3) {
        moreVisible.value = false
        getList()
        tableRef.value.clearSelection()
        chooseSure.value = []
        chooseList.value = []
        chooseNum.value = 1
        return
    }


}
/** 查询角色列表 */
function getList() {
    loading.value = true
    const params = JSON.parse(JSON.stringify(queryParams.value))
    params['preparedBy.name'] = queryParams.value.preparedBy
    params['warehouseNumber.warehouseNumber'] = queryParams.value.warehouseNumber
    params['supplier.enterpriseName'] = queryParams.value.supplier
    params['handledBy.id'] = queryParams.value.handledBy
    params['auditorBy.name'] = queryParams.value.approvalManName
    if (params.createDate?.length) {
        params.beginCreateDate = moment(params.createDate[0]).format('YYYY-MM-DD 00:00:00')
        params.endCreateDate = moment(params.createDate[1]).format('YYYY-MM-DD 23:59:59')
    }
    if (params.termsPayment?.length) {
        params.beginTermsPayment = moment(params.termsPayment[0]).format('YYYY-MM-DD 00:00:00')
        params.endTermsPayment = moment(params.termsPayment[1]).format('YYYY-MM-DD 23:59:59')
    }
    delete params.createDate
    delete params.termsPayment
    delete params.supplier
    delete params.handledBy
    delete params.warehouseNumber
    delete params.preparedBy
    purchasingManagement.getList(params).then(res => {
        if (res.code == 200) {
            list.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })

}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}

/**
 * @description: 获取库号
 * @return {*}
 */

function getHandleBy() {
    warehouseNumberManagement.getList({ size: 9999, current: 1, status:'1',isStop:'0'}).then(res => {
        if (res.code == 200) {
            warehouseNumberList.value = res.data.records
        }
    })
}
/**
 * @description: 关闭审核记录
 * @return {*}
 */
const beforeClose_review = () => {
    reviewVisible.value = false
}
/**
 * @description: 点击审核记录
 * @return {*}
 */
const handleReview = (row) => {
    reviewVisible.value = true
    reviewData.value = row
}

/** 搜索按钮操作 */
function handleQuery() {
    getList();
}
const handleAdd = (row, type) => {
    if (row && type == 'edit') {
        title.value = "编辑"
        // purchasingModalRef.value.getDetail(row)
        open.value = true
        modalType.value = type
        detailRow.value = row
    }
    if (row && type == 'detail') {
        detailOpen.value = true
        detailRow.value = row
        // purchasingModalRef.value.getDetail(row)
    }
    if (row && type == 'draft') {
        title.value = "草稿"
        purchasingModalRef.value.getDetail(row)
        open.value = true
        modalType.value = type
    }
    if (!row || !type) {
        title.value = "下单"
        open.value = true
        modalType.value = type
    }
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
/** 删除按钮操作 */
function handleDelete(row) {
    proxy.$confirm('是否确认删除改数据项?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        purchasingManagement.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                proxy.msgSuccess('删除成功')
                getList()
            } else {
                proxy.msgError(res.msg)
            }
        })
    }).catch(() => { });
}
const beforClose = () => {
    if (modalType.value === 'detail') {
        open.value = false
    } else {
        proxy.$confirm("页面未保存确定取消编辑吗？", '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            open.value = false
        }).catch(() => {

        })
    }


}
const DetailBeforClose = () => {
    detailOpen.value = false
}
/**
 * @description: 获取经手人
 * @return {*}
 */

const getUsers = () => {
    warehouseNumberManagement.getUser().then(res => {
        if (res.code == 200) {
            optionList.value = res.data?.records
        }

    })
}
async function dict() {
    reviewStatus.value = await proxy.getDictList('erp_review_status')
    warehousingStatus.value = await proxy.getDictList('warehousing_status')
    settlementMethod.value = await proxy.getDictList('erp_settlement_method')
    deductionRateList.value = await proxy.getDictList('serp_elfoperated_deduction_rate') // 自营扣率


}
onMounted(() => {
    dict()
    getList();
    getHandleBy()
    getUsers()
})
const columns = ref([
    {
        label: '',
        prop: '',
        type: 'selection'
    },
    {
        label: '单据编号',
        prop: 'orderCode',
        minWidth: 180,
    },
    {
        label: '单据日期',
        prop: 'createDate',
        type: 'date'
    },
    {
        label: '供应商名称',
        prop: 'supplier.enterpriseName',
    },
    {
        label: '备注',
        prop: 'remark',
    },
    {
        label: '库号',
        prop: 'warehouseNumber.warehouseNumber',
    },
    {
        label: '经手人',
        prop: 'handledBy.name',
    },
    {
        label: '制单人',
        prop: 'preparedBy.name',
    },
    {
        label: '审核人',
        prop: 'auditorBy.name',
    },
    {
        label: '审核日期',
        prop: 'auditorDate',
        type: 'date'
    }, {
        label: '订单状态',
        prop: 'status',
        type: 'status',
        filters: reviewStatus
    }, {
        label: '结算方式',
        prop: 'settlementMethod',
        type: 'status',
        filters: settlementMethod
    }, {
        label: '入库状态',
        prop: 'inventoryStatus',
        type: 'status',
        filters: warehousingStatus
    }, {
        label: '单据金额',
        prop: 'totalAmount',
    }, {
        label: '单据数量',
        prop: 'totalQuantity',
    }, {
        label: '到货数量',
        prop: 'arrivalQuantity',
    }, {
        label: '未完成数量',
        prop: 'unfinishedQuantity',
    }, {
        label: '付款期限',
        prop: 'termsPayment',
        type: 'date'
    },
    {
        label: '操作',
        prop: 'operate',
        type: 'operate',
        minWidth: 300,
        fixed: 'right'
    },
])
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
}

::v-deep .labelStyle {
    .el-form-item__label {
        // margin-left: 10px;
    }
}

::v-deep .Botm {
    .el-card__body {
        padding-bottom: 0px
    }
}

::v-deep .serchBtn {
    .el-form-item__content {
        display: flex;
        justify-content: end;
    }
}

.box_date {
    width: 220px;
}

.moreVisibleCnt {
    color: red;
    line-height: 24px;

    p:nth-of-type(1) {
        line-height: 100px;
        text-align: center;
        color: #333;
        font-size: 18px
    }
}
</style>
