import http from "@/utils/request"

export default {
  // 查询列表
  assetIncubatorList: function (params) {
    return http.get('/device/assetIncubator/list', params)
  },
  // 新增修改
  assetIncubatorSave: function (data) {
    return http.post("/device/assetIncubator/save", data);
  },
  // 删除
  assetIncubatorDelete: function (params) {
    return http.delete("/device/assetIncubator/delete", params);
  },
  // 新增验证记录
  assetIncubatorCheckSave: function (data) {
    return http.post("/device/assetIncubatorCheck/save", data);
  },
  // 验证记录查询
  assetIncubatorCheckList: function (params) {
    return http.get("/device/assetIncubatorCheck/list", params);
  },
  // 下载导入保温箱数据模板
  importTemplate: function (params,config,resDetail,responseType) {
    return http.get("/device/assetIncubator/import/template", params,config,resDetail,responseType);
  },
  // 删除保温箱验证记录
  assetIncubatorCheckDelete: function (params) {
    return http.delete("/device/assetIncubatorCheck/delete", params);
  },
  // 导出
  assetIncubatorExport: function (params,config,resDetail,responseType) {
    return http.get("/device/assetIncubator/export", params,config,resDetail,responseType,1);
  },
  // 导入数据
  assetIncubatorImport: function (data) {
    return http.post("/device/assetIncubator/import", data);
  },
}
