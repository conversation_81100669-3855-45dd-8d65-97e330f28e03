<template>
    <div style="background-color: rgb(245, 247, 253); padding: 10px" v-loading="loading">
        <el-card class="mb10" shadow="never">
            <CardHeader title="保温箱信息" />
            <el-row :gutter="24" class="text-size">
                <el-col :span="12" class="mb5">
                    <div>
                        保温箱号：<span>{{ recordInfo.incubatorNo }}</span>
                    </div>
                </el-col>
                <el-col v-if="type == '2' && info.incubatorDetail && info.incubatorDetail.length > 0" :span="12" class="mb5">
                    <div>
                        装箱总件数：<span>{{ sumTotalNumberOfPackets(info.incubatorDetail) }}件</span>
                    </div>
                </el-col>
                <el-col v-if="type == '1'" :span="12" class="mb5">
                    <div>
                        传感器编号：<span class="hy-weight">{{ recordInfo.deviceNo }}</span>
                    </div>
                </el-col>
                <el-col v-if="type == '1'" :span="12" class="mb5">
                    <div>
                        温度阈值：<span class="hy-weight text-red">{{ recordInfo.temperatureLower }}</span
                        >℃-<span class="text-red">{{ recordInfo.temperatureCeiling }}</span
                        >℃
                    </div>
                </el-col>
                <el-col v-if="type == '1'" :span="12" class="mb5">
                    <div>
                        操作员：<span class="hy-weight">{{ recordInfo.createBy.name }}</span>
                    </div>
                </el-col>
                <el-col v-if="type == '2'" :span="12" class="mb5">
                    <div>
                        解绑操作人：<span class="hy-weight">{{ recordInfo.updateBy.name }}</span>
                    </div>
                </el-col>
                <el-col v-if="type == '2'" :span="12" class="mb5">
                    <div>
                        解绑时间：<span class="hy-weight">{{ recordInfo.finishIncubatorTime }}</span>
                    </div>
                </el-col>
            </el-row>
        </el-card>
        <el-card v-if="type == '2' && info.prevTask" class="mb10" shadow="never">
            <CardHeader title="解绑前任务记录" />
            <el-row :gutter="24" class="text-size">
                <el-col v-if="info.prevTask" :span="12" class="mb5">
                    <div>
                        解绑前任务所属：<span v-if="info.prevTask.taskType == '00' && info.prevTask.lanTask">{{ info.prevTask.lanTask.driver.driverName }}</span>
                        <span v-if="info.prevTask.taskType != '00' && info.prevTask.driverTask">{{ info.prevTask.driverTask.driver.driverName }}</span>
                    </div>
                </el-col>
                <el-col :span="12" class="mb5">
                    <div>
                        任务类型：<span>{{ formDict(taskTypeOptions, info.prevTask.taskType) }}</span>
                    </div>
                </el-col>
                <el-col v-if="info.prevTask" :span="12" class="mb5">
                    <div v-if="info.prevTask.taskType == '00' && info.prevTask.lanTask">
                        解绑前任务状态：
                        <span>{{ formDict(lanTaskStatusOptions, info.prevTask.lanTask.status) }}</span>
                        <span class="ml10">{{ timeFormatting(info.prevTask.lanTask.updateDate) }}</span>
                    </div>
                    <div v-if="info.prevTask.taskType != '00'">
                        解绑前任务状态：
                        <span>{{ formDict(driverTaskStatusOptions, info.prevTask.driverTask.status) }}</span>
                        <span class="ml10">{{ timeFormatting(info.prevTask.driverTask.updateDate) }}</span>
                    </div>
                </el-col>
                <!--          <el-col :span="12" class="mb5">-->
                <!--            <div >状态描述：<span>{{ taskInformation.stateDescription }}</span></div>-->
                <!--          </el-col>-->
            </el-row>
        </el-card>
        <el-card v-if="info.incubatorDetail && info.incubatorDetail.length > 0" shadow="never">
            <template #header>
                <div class="mb5" style="display: flex; justify-content: space-between; align-items: center">
                    <CardHeader :line="false" size="mini" title="货物信息" />
                    <template v-if="recordInfo.status == '1' || recordInfo.status == '2'">
                        <template v-if="coldChainHandoverType && coldChainHandoverType.length == 1">
                            <el-button plain size="small" type="primary" @click="printColdChainHandoverAll(coldChainHandoverType[0].templateId)">打印冷链交接单<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                        </template>
                        <template v-else-if="coldChainHandoverType && coldChainHandoverType.length > 0">
                            <el-dropdown @command="printColdChainHandoverAll">
                                <el-button icon="el-icon-printer" size="small" type="warning">打印冷链交接单</el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item v-for="(item, index) in coldChainHandoverType" :key="index" :command="item.templateId">
                                            {{ item.templateName }}
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                    </template>
                </div>
                <div class="border-bottom-1"></div>
            </template>
            <el-collapse>
                <el-row v-for="(item, index) in info.incubatorDetail" :gutter="24">
                    <el-col :span="24">
                        <el-collapse-item :name="index">
                            <template #title>
                                <div class="box-flex">
                                    <div>
                                        地区:<span>{{ item?.receiverArea?.province || '' }}{{ item?.receiverArea?.city || '' }}{{ item?.receiverArea?.county || '' }} {{ item?.receiverArea?.town || '' }}</span>
                                    </div>
                                    <div class="text-red">{{ item.boxTotal }}件</div>
                                </div>
                            </template>
                            <div v-if="item.orderList && item.orderList.length > 0" class="box-m-10">
                                <el-row v-for="order in item.orderList" :gutter="24">
                                    <el-col v-if="order.order.transOrderNo" :span="7">
                                        <div>
                                            运单号：<span class="hy-weight">{{ order.order.transOrderNo }}</span>
                                        </div>
                                    </el-col>
                                    <el-col v-if="order.order.sendCompany" :span="6">
                                        <div>
                                            发件公司：<span class="hy-weight">{{ order.order.sendCompany }}</span>
                                        </div>
                                    </el-col>
                                    <el-col v-if="order.boxList" :span="6">
                                        <div>
                                            货物数量：<span class="hy-weight">{{ order.boxList.length }}</span>
                                        </div>
                                    </el-col>
                                    <el-col v-if="recordInfo.status == '1' || recordInfo.status == '2'" :span="4" style="text-align: right">
                                        <template v-if="order.coldChainHandoverType && order.coldChainHandoverType.length == 1">
                                            <el-button plain size="small" type="primary" @click="printColdChainHandover(order.coldChainHandoverType[0].templateId, order.order.id)">打印冷链交接单<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                                        </template>
                                        <template v-else-if="order.coldChainHandoverType && order.coldChainHandoverType.length > 0">
                                            <el-dropdown @command="printColdChainHandover($event, order.order.id)">
                                                <el-button plain size="small" type="primary">打印冷链交接单<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                                                <template #dropdown>
                                                    <el-dropdown-menu>
                                                        <el-dropdown-item v-for="(tmp, index) in order.coldChainHandoverType" :key="index" :command="tmp.templateId">
                                                            {{ tmp.templateName }}
                                                        </el-dropdown-item>
                                                    </el-dropdown-menu>
                                                </template>
                                            </el-dropdown>
                                        </template>
                                    </el-col>
                                    <el-col v-if="order.boxList" :span="22">
                                        <el-row v-for="code in order.boxList" :gutter="24" style="width: 100%">
                                            <el-col v-if="code.code" :offset="0" :span="20">
                                                <div>箱码：{{ code.code }}</div>
                                            </el-col>
                                        </el-row>
                                    </el-col>
                                </el-row>
                            </div>
                        </el-collapse-item>
                    </el-col>
                </el-row>
            </el-collapse>
        </el-card>
    </div>
</template>

<script>
import CardHeader from '@/components/CardHeader';
import cabineBox from '@/api/cabineBox/cabineBox.js';
import orderManagement from '@/api/logisticsManagement/orderManagement';
import handoverOrderConfiguration from '@/api/carrierEnd/handoverOrderConfiguration.js';
import moment from 'moment';
import { printLabelView } from '@/utils';
//   import { getRecordDetail } from '@/api/fourpl/Incubator';
export default {
    name: 'packingDetails',
    components: {
        CardHeader
    },
    props: {
        recordInfo: {
            type: String,
            required: true
        },
        type: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            info: [],
            taskTypeOptions: [], // 任务类型
            driverTaskStatusOptions: [], // 4pl司机任务状态
            lanTaskStatusOptions: [], // 揽收任务状态
            coldChainHandoverType: [], // 冷链交接单类型
            loading: false
        };
    },
    created() {
        this.getRecordDetail();
        this.getDict();
        // 冷链交接单模板-批量
        this.getByIncubatorTemplate();
    },
    computed: {
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        },
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        sumTotalNumberOfPackets() {
            return (data) => {
                // 累计data中的boxTotal
                return data.reduce((sumData, key, index, arr) => {
                    return sumData + key.boxTotal;
                }, 0);
            };
        }
    },
    methods: {
        /**
         * 打印冷链交接单 - 批量
         */
        getByIncubatorTemplate() {
            this.coldChainHandoverType = [];
            handoverOrderConfiguration.getByIncubatorTemplate({ incubatorRecordId: this.recordInfo.id }).then((response) => {
                if (response.code == 200 && response?.data?.length > 0) {
                    this.coldChainHandoverType = response?.data;
                }
            });
        },
        /**
         * 循环获取冷链交接单模板并赋值
         */
        async getByCompanyTemplate(companyId, carrierId, index, idx) {
            await handoverOrderConfiguration.getByCompanyTemplate({ companyId, carrierId }).then((response) => {
                if (response.code == 200 && response?.data?.length > 0) {
                    this.info.incubatorDetail[index].orderList[idx].coldChainHandoverType = response?.data;
                }
            });
        },
        /**
         * 字典
         * @returns {Promise<void>}
         */
        async getDict() {
            // 任务类型
            this.taskTypeOptions = await this.getDictList('fourpl_task_type');
            // 4pl司机任务状态
            this.driverTaskStatusOptions = await this.getDictList('fourpl_driver_task_status');
            // 揽收任务状态
            this.lanTaskStatusOptions = await this.getDictList('fourpl_lan_task_status');
        },
        /**
         * 字典翻译
         * @param typeList
         * @param value
         * @returns {*}
         */
        formDict(typeList, value) {
            return this.selectDictLabel(typeList, value);
        },
        /**
         * 保温箱详情
         */
        getRecordDetail() {
            this.loading = true;
            cabineBox
                .getRecordDetail({ incubatorRecordId: this.recordInfo.id })
                .then((res) => {
                    this.info = res.data;
                    if (this.info.incubatorDetail && this.info.incubatorDetail.length > 0) {
                        this.info.incubatorDetail.forEach((item, index) => {
                            if (item.orderList && item.orderList.length > 0) {
                                item.orderList.forEach((order, idx) => {
                                    this.getByCompanyTemplate(order.order.companyId, order.order.carrierId, index, idx);
                                });
                            }
                        });
                    }
                })
                .catch((e) => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        // 打印冷链交接单
        printColdChainHandover(templateId, id) {
            orderManagement.packColdChainPassOnSheet({ orderIds: id }).then((res) => {
                if (res.code == 200) {
                    orderManagement.updatePrintDeliveryFlag({ orderId: id }).then((response) => {
                        if (response.code === 200 && response.data) {
                            printLabelView({ templateId, dataList: res.data });
                        } else {
                            this.$message.error('打印失败');
                        }
                    });
                } else {
                    this.$message.error('打印失败');
                }
            });
        },
        /**
         * 批量打印
         * @param templateId
         */
        printColdChainHandoverAll(templateId) {
            let ids = [];
            this.info.incubatorDetail.forEach((item) => {
                item.orderList.forEach((o) => {
                    ids.push(o.order.id);
                });
            });
            this.printColdChainHandover(templateId, ids.toString());
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .el-collapse-item__header {
    border-bottom: 0;
}

::v-deep .el-collapse {
    border: 0;
}

::v-deep {
    .el-collapse-item__content {
        padding-bottom: 0;
    }
}

.box-flex {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
}

.text-red {
    color: red;
}

.text-size {
    font-size: 13px;
}

.mb5 {
    margin-bottom: 5px;
}

.hy-weight {
    font-weight: bold;
}

.border-bottom-1 {
    margin-bottom: 0;
}
</style>
