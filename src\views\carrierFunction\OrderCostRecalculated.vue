<template>
    <div class="app-container customer-auto-height-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.native.prevent>
                <el-form-item label="订单号" prop="orderNo" style="width: 230px">
                    <el-input v-model="queryForm.orderNo" clearable placeholder="请输入订单号" @clear="handleChangesTheOrderType" @keyup.enter.native="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item label="订单状态" prop="status" style="width: 230px">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择订单状态" @change="handleChangesTheOrderType">
                        <el-option v-for="dict in statusDicts" :key="dict.code" :label="dict.name" :value="dict.code * 1" />
                    </el-select>
                </el-form-item>
                <el-form-item label="下单时间" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryForm.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleChangesTheOrderType"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单号" prop="transOrderNo" style="width: 250px">
                    <el-input v-model="queryForm.transOrderNo" clearable placeholder="请输入运单号" @keyup.enter.native="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单类型" prop="orderClass">
                    <el-select v-model="queryForm.orderClass" clearable disabled placeholder="请选择订单类型" style="width: 100%" @change="handleChangesTheOrderType">
                        <el-option v-for="item in settlementManagementOrderTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="queryForm.orderClass === '2'" v-show="isShowAll" label="货主公司" prop="companyId">
                    <el-select v-model="queryForm.companyId" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件公司" prop="sendCompany" style="width: 250px">
                    <el-input v-model="queryForm.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter.native="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件地址" prop="sendAddress">
                    <el-cascader v-model="queryForm.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable placeholder="请选择发件地址" @change="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件公司" prop="receiverCompany" style="width: 250px">
                    <el-input v-model="queryForm.receiverCompany" clearable placeholder="请输入收件公司" @keyup.enter.native="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件地址" prop="receiverAddress">
                    <el-cascader v-model="queryForm.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable placeholder="请选择收件地址" @change="handleChangesTheOrderType" />
                </el-form-item>
                <el-form-item v-show="isShowAll" label="产品分类" prop="productClass">
                    <el-select v-model="queryForm.productClass" clearable placeholder="请选择产品分类" @change="handleChangesTheOrderType">
                        <el-option v-for="dict in fourplProductClassOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运输类型" prop="productType">
                    <el-select v-model="queryForm.productType" clearable placeholder="请选择运输类型" @change="handleChangesTheOrderType">
                        <el-option v-for="dict in productTypeDicts" :key="dict.code" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="温层类型" prop="temperatureList">
                    <el-select v-model="queryForm.temperatureList" clearable collapse-tags filterable multiple placeholder="请选择温层类型" style="width: 250px" @change="handleChangesTheOrderType">
                        <el-option v-for="dict in temperatureType__expenses" :key="dict.id" :label="dict.describtion" :value="dict.id" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="付款方式" prop="paymentMethod">
                    <el-select v-model="queryForm.paymentMethod" clearable placeholder="请选择付款方式" @change="handleChangesTheOrderType">
                        <el-option v-for="(dict, index) in fourplPaymentMethodOptions" :key="index" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="揽收方式" prop="orderType">
                    <el-select v-model="queryForm.orderType" clearable placeholder="请选择揽收方式" @change="handleChangesTheOrderType">
                        <el-option v-for="dict in collectionMethod" :key="dict.code" :label="dict.name" :value="dict.code * 1" />
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="件数" prop="goodsPackages">
                    <div style="width: 194px">
                        <el-select v-model="queryForm.prePackageOption" clearable style="width: 45%">
                            <el-option v-for="(item, index) in prePackageOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
                        </el-select>
                        <el-input v-model="queryForm.goodsPackages" class="goodsPackages" clearable placeholder="请输入件数" style="width: 55%" @keyup.enter.native="handleChangesTheOrderType" />
                    </div>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="预估费用合计" prop="estimateCost">
                    <div style="width: 194px">
                        <el-select v-model="queryForm.preCostOption" clearable style="width: 45%">
                            <el-option v-for="(item, index) in preCostOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
                        </el-select>
                        <el-input v-if="queryForm.preCostOption !== '0'" v-model="queryForm.estimateCost" class="estimateCost" clearable placeholder="请输入预估费用合计" style="width: 55%" @keyup.enter.native="handleChangesTheOrderType" />
                    </div>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleChangesTheOrderType" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button v-if="dataList && dataList.length" type="primary" @click="handleOpenRecalculate">重新计算</el-button>
                <el-button :disabled="!dataList || dataList.length === 0" icon="el-icon-download" type="warning" @click="handleExportAll">导出全部</el-button>
                <el-button type="primary" @click="getExportRecordsList">导出记录</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="orderCostRecalculated" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="dataList" :show-check-box="true" :show-summary="true" class="customer-auto-height-table" element-loading-text="加载中..." max-height="null" @selection-change="handleSelectionChange">
                <template #sendAddress="{ row }">
                    <span>{{ row.sendProvince || '' }}{{ row.sendCity || '' }}{{ row.sendCounty || '' }}{{ row.sendTown || '' }}{{ row.sendAddress || '' }}</span>
                </template>
                <template #receiverAddress="{ row }">
                    <span>{{ row.receiverProvince || '' }}{{ row.receiverCity || '' }}{{ row.receiverCounty || '' }}{{ row.receiverTown || '' }}{{ row.receiverAddress || '' }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ formatDictionaryData('productTypeDicts', row.productType) }}</span>
                </template>
                <template #productClass="{ row }">
                    <span>{{ formatDictionaryData('fourplProductClassOptions', row.productClass) }}</span>
                </template>
                <template #paymentMethod="{ row }">
                    <span>{{ formatDictionaryData('fourplPaymentMethodOptions', row.paymentMethod) }}</span>
                </template>
                <template #orderClass="{ row }">
                    <div v-html="formatDictionaryData('settlementManagementOrderTypeList', row.orderClass)"></div>
                </template>
                <template #status="{ row }">
                    <div v-html="formatStatus(row.status)"></div>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>

        <!--  / 重新计算订单费用 dialog  -->
        <el-drawer v-if="recalculateOrderCostVisible" v-model="recalculateOrderCostVisible" size="500px" title="重新计算订单费用" @close="handleCloseRecalculateOrderCost">
            <div v-loading="recalculateOrderCostLoading" :element-loading-text="recalculateOrderCostLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card shadow="never">
                    <el-form ref="recalculateOrderCostForm" :model="recalculateOrderCostForm" :rules="recalculateOrderCostFormRules" label-width="auto">
                        <el-form-item label="揽收价格本" prop="lanId">
                            <el-select v-model="recalculateOrderCostForm.lanId" clearable filterable placeholder="请选择揽收价格本" style="width: 100%">
                                <el-option v-for="item in collectThePriceOfThisList" :key="item.id" :label="item.priceBookName + item.version" :value="item.id">
                                    <span style="float: left">{{ item.priceBookName + item.version }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ formatDictionaryData('priceBookTypeList', item.type) }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="干线价格本" prop="trackId">
                            <el-select v-model="recalculateOrderCostForm.trackId" clearable filterable placeholder="请选择干线价格本" style="width: 100%">
                                <el-option v-for="item in shippingPriceThisList" :key="item.id" :label="item.priceBookName + item.version" :value="item.id">
                                    <span style="float: left">{{ item.priceBookName + item.version }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ formatDictionaryData('priceBookTypeList', item.type) }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="配送价格本" prop="deliveryId">
                            <el-select v-model="recalculateOrderCostForm.deliveryId" clearable filterable placeholder="请选择配送价格本" style="width: 100%">
                                <el-option v-for="item in shippingPriceBookList" :key="item.id" :label="item.priceBookName + item.version" :value="item.id">
                                    <span style="float: left">{{ item.priceBookName + item.version }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ formatDictionaryData('priceBookTypeList', item.type) }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="合算公式" prop="formulaId">
                            <el-select v-model="recalculateOrderCostForm.formulaId" clearable filterable placeholder="请选择合算公式" style="width: 100%">
                                <el-option v-for="item in billingMethodList" :key="item.id" :label="item.name" :value="item.id">
                                    <span style="float: left">{{ item.name }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px" v-html="formatPublishStatus(item.publishStatus)"></span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </el-card>
                <div style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="info" @click="handleCloseRecalculateOrderCost">取消</el-button>
                    <el-button type="primary" @click="handleRecalculateOrderCost">确定</el-button>
                </div>
            </div>
        </el-drawer>

        <!-- /订单费用重新计算结果   -->
        <el-drawer v-if="recalculateOrderCostResultVisible" v-model="recalculateOrderCostResultVisible" size="1200px" title="订单费用重新计算结果" @close="handleCloseRecalculateOrderCostResult">
            <div v-loading="recalculateOrderCostResultLoading" :element-loading-text="recalculateOrderCostResultLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <div class="box__flex__drawer" style="margin-bottom: 10px">
                        <el-form ref="recalculateOrderCostResultForm" :model="recalculateOrderCostResultForm" label-width="auto">
                            <el-form-item label="订单状态" prop="billingStatus" style="margin-bottom: 0">
                                <el-select v-model="recalculateOrderCostResultForm.billingStatus" clearable placeholder="请选择订单状态" style="width: 100%" @change="handleRecalculateOrderCostRequest">
                                    <el-option v-for="item in calculationResultStatusList" :key="item.code" :label="item.name" :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <div v-if="tableData && tableData.length">
                            <el-button class="ml-auto" type="primary" @click="handleClickOneClickUpdate">一键更新</el-button>
                            <el-button type="primary" @click="handleClickExportAll">全部导出</el-button>
                        </div>
                    </div>
                    <column-table ref="recalculateOrderCostResultData" :columns="recalculateOrderCostResultColumns" :data="tableData" :showCheckBox="false">
                        <template #transType="{ row }">
                            <span>{{ formatDictionaryData('productTypeDicts', row.transType) }}</span>
                        </template>
                        <template #vehicleType="{ row }">
                            <span>{{ formatVehicleType(row.vehicleType) }}</span>
                        </template>
                        <template #productType="{ row }">
                            <span>{{ formatDictionaryData('fourplProductClassOptions', row.productType) }}</span>
                        </template>
                        <template #temperatureType="{ row }">
                            <span>{{ formatDictionaryData('thermosphereTypeList', row.temperatureType) }}</span>
                        </template>
                        <template #lanCost="{ row }">
                            <span>{{ row.lanCost }}</span>
                            <el-button v-if="row.lanCost && row.id" size="small" style="margin-left: 5px" type="primary" @click="handleASingleUpdate(row, 'lanCost')">更新</el-button>
                        </template>
                        <template #arteryCost="{ row }">
                            <span>{{ row.arteryCost }}</span>
                            <el-button v-if="row.arteryCost && row.id" size="small" style="margin-left: 5px" type="primary" @click="handleASingleUpdate(row, 'arteryCost')">更新</el-button>
                        </template>
                        <template #deliveryCost="{ row }">
                            <span>{{ row.deliveryCost }}</span>
                            <el-button v-if="row.deliveryCost && row.id" size="small" style="margin-left: 5px" type="primary" @click="handleASingleUpdate(row, 'deliveryCost')">更新</el-button>
                        </template>
                        <template #exceedCountyCost="{ row }">
                            <span>{{ row.exceedCountyCost }}</span>
                            <el-button v-if="row.exceedCountyCost && row.id" size="small" style="margin-left: 5px" type="primary" @click="handleASingleUpdate(row, 'exceedCountyCost')">更新</el-button>
                        </template>
                        <template #billingStatus="{ row }">
                            <span>{{ formatDictionaryData('calculationResultStatusList', row.billingStatus) }}</span>
                        </template>
                        <template #opt="{ row }">
                            <el-button plain size="small" type="primary" @click="handleClickFeeDetails(row)">详情</el-button>
                            <el-button size="small" type="primary" @click="handleFeeUpdates(row)">费用更新</el-button>
                        </template>
                    </column-table>
                    <div class="box__flex__drawer">
                        <div class="box__calculation_results">
                            <el-alert v-if="recalculateOrderCostResultData.length" :closable="false" :title="`计算成功${recalculateOrderCostResultData.length}个订单`" description=" " show-icon type="success"></el-alert>
                            <el-alert v-if="errorList.length" :closable="false" :title="`计算失败${errorList.length}个`" show-icon type="error">
                                <el-button v-if="errorList.length > 0" type="text" @click="handleOpenTheReasonForTheFailure">查看详情</el-button>
                            </el-alert>
                        </div>
                        <el-pagination
                            :current-page="recalculateOrderCostResultForm.current"
                            :page-size="recalculateOrderCostResultForm.size"
                            :page-sizes="[10, 20, 30, 50, 100]"
                            :total="recalculateOrderCostResultForm.total"
                            background
                            layout="total, sizes, prev, pager, next, jumper"
                            style="text-align: right"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        >
                        </el-pagination>
                    </div>
                </el-card>
            </div>
        </el-drawer>

        <!--  /失败原因drawer  -->
        <el-drawer v-if="theReasonForTheFailureVisible" v-model="theReasonForTheFailureVisible" size="600px" title="失败原因" @close="handleCloseTheReasonForTheFailure">
            <div class="p16 the-reason-for-the-failure" style="background-color: #f2f2f2; padding: 10px">
                <el-card class="mb10" shadow="never">
                    <div class="box-period">
                        <el-scrollbar>
                            <el-alert v-for="(item, index) in errorList" :key="item.id" :class="index === 0 ? '' : 'mt10'" :closable="false" :title="`订单 ${item.orderNo} ${item.error}`" show-icon type="error" />
                        </el-scrollbar>
                    </div>
                </el-card>
                <div style="display: flex; justify-content: end; margin-top: 10px">
                    <el-button type="primary" @click="handleCloseTheReasonForTheFailure">取消</el-button>
                </div>
            </div>
        </el-drawer>

        <!--  /费用更新dialog  -->
        <el-dialog v-model="costUpdateVisable" :close-on-click-modal="true" title="费用更新提示" width="45%" @close="handleCloseCostUpdate">
            <div class="box__costUpdate_results">
                <el-result v-if="errorMsgList.length" :title="`更新失败${errorMsgList.length}个`" icon="error"></el-result>
                <div v-if="errorMsgList.length" class="div2">
                    <el-scrollbar>
                        <el-alert v-for="(item, index) in errorMsgList" :key="index" :class="index === 0 ? '' : 'mt10'" :closable="false" :title="` ${item}`" show-icon type="error" />
                    </el-scrollbar>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleCloseCostUpdate">确定</el-button>
            </span>
        </el-dialog>

        <!--  /订单费用详情  -->
        <order-fee-details-with-details v-if="orderCostVisible" v-model="orderCostVisible" :detail-data="detailData" :fee-breakdown-data="feeBreakdownData" />

        <!--  /导出记录 抽屉 -->
        <el-drawer v-model="exportRecordsVisible" size="700px" title="导出记录" @close="hideExportRecords">
            <div v-loading="exportRecordsLoading" :element-loading-text="exportRecordsLoadingText" style="background-color: #f2f2f2; padding: 10px">
                <el-card :body-style="{ padding: '10px' }" shadow="never">
                    <column-table ref="exportRecords" :columns="exportRecordsColumns" :data="exportRecordsData">
                        <template #status="{ row }">
                            <span>{{ formatDictionaryData('exportRecordsStatusList', row.status) }}</span>
                        </template>
                        <template #opt="{ row }">
                            <el-button v-if="row.status === '3'" icon="el-icon-download" link size="small" type="primary" @click="downloadExportRecords(row)">下载</el-button>
                        </template>
                    </column-table>
                    <pagination v-show="exportRecordsTotal > 0" v-model:limit="exportRecords.size" v-model:page="exportRecords.current" :pageSizes="[10, 20, 30, 50, 100]" :total="exportRecordsTotal" @pagination="getExportRecordsList" />
                </el-card>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import moment from 'moment/moment';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import OrderFeeDetailsWithDetails from '@/views/carrierFunction/OrderFeeDetailsWithDetails.vue';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import orderCostRecalculated from '@/api/carrierEnd/orderCostRecalculated';
import { selectDictLabel } from '@/utils/dictLabel';
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration';
import { downloadNoData } from '@/utils';
import customerOrderExpenseManagement from '@/api/carrierEnd/customerOrderExpenseManagement';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'OrderCostRecalculated',
    components: {
        SearchButton,
        RightToolbar,
        OrderFeeDetailsWithDetails,
        ColumnTable
    },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                orderNo: undefined,
                transOrderNo: undefined,
                companyId: undefined,
                orderClass: '2',
                status: undefined,
                sendCompany: undefined,
                sendAddress: [],
                sendProvinceId: undefined,
                sendCityId: undefined,
                sendCountyId: undefined,
                sendTownId: undefined,
                receiverCompany: undefined,
                receiverAddress: [],
                receiverProvinceId: undefined,
                receiverCityId: undefined,
                receiverCountyId: undefined,
                receiverTownId: undefined,
                productClass: undefined,
                productType: undefined,
                temperatureList: [],
                paymentMethod: undefined,
                orderType: undefined,
                prePackageOption: undefined,
                preCostOption: undefined,
                estimateCost: undefined,
                queryTime: []
            },
            orderOwnerList: [],
            settlementTypeList: [],
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '120px', columnShow: true, fixed: 'left' },
                { title: '货主公司', key: 'companyName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '下单时间', key: 'createDate', align: 'center', width: '100px', columnShow: true },
                { title: '订单类型', key: 'orderClass', align: 'center', width: '100px', columnShow: true },
                { title: '产品分类', key: 'productClass', align: 'center', width: '120px', columnShow: true },
                { title: '运输类型', key: 'productType', align: 'center', width: '120px', columnShow: true },
                { title: '温层类型', key: 'temperatureTypeDesc', align: 'center', width: '200px', columnShow: true },
                { title: '件数', labelClassName: 'isShowSummary', key: 'goodsPackages', align: 'center', width: '120px', columnShow: true },
                { title: '付款方式', key: 'paymentMethod', align: 'center', width: '120px', columnShow: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '保价金额', key: 'insuranceAmount', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true },
                { title: '保费', key: 'premium', align: 'center', labelClassName: 'isShowSummary', width: '120px', columnShow: true },
                { title: '预估费用合计', labelClassName: 'isShowSummary', key: 'estimateCost', align: 'center', width: '120px', columnShow: true },
                { title: '运单状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right' }
            ],
            loading: false,
            dataList: [],
            total: 0,
            recalculateOrderCostVisible: false,
            recalculateOrderCostForm: {
                lanId: null,
                trackId: null,
                deliveryId: null,
                formulaId: null
            },
            recalculateOrderCostFormRules: {
                lanId: [{ required: true, message: '请选择揽收价格本', trigger: 'change' }],
                trackId: [{ required: true, message: '请选择运输价格本', trigger: 'change' }],
                deliveryId: [{ required: true, message: '请选择配送价格本', trigger: 'change' }],
                formulaId: [{ required: true, message: '请选择合算公式', trigger: 'change' }]
            },
            fourplPaymentMethodOptions: [],
            fourplProductClassOptions: [],
            productTypeDicts: [],
            recalculateOrderCostLoading: false,
            recalculateOrderCostLoadingText: '重新计算中...',
            billingMethodList: [],
            publishTheStatusOfList: [],
            recalculateOrderCostResultVisible: false,
            recalculateOrderCostResultLoading: false,
            recalculateOrderCostResultLoadingText: '重新计算中...',
            recalculateOrderCostResultColumns: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '180px', columnShow: true, fixed: 'left' },
                { title: '货主公司', key: 'companyName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '产品分类', key: 'productType', align: 'center', width: '120px', columnShow: true },
                { title: '温层类型', key: 'temperatureType', align: 'center', width: '120px', columnShow: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '120px', columnShow: true },
                { title: '运输类型', key: 'transType', align: 'center', width: '120px', columnShow: true },
                { title: '车辆类型', key: 'carTypeDesc', align: 'center', width: '120px', columnShow: true },
                { title: '公里数', key: 'kilometre', align: 'center', width: '120px', columnShow: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                {
                    title: '揽收费用',
                    align: 'center',
                    minWidth: '200px',
                    columnShow: true,
                    children: [
                        { title: '原费用', key: 'oldCollectCost', align: 'center', width: '100px', columnShow: true },
                        { title: '新费用', key: 'collectCost', align: 'center', width: '130px', columnShow: true }
                    ]
                },
                {
                    title: '干线费用',
                    align: 'center',
                    minWidth: '200px',
                    columnShow: true,
                    children: [
                        { title: '原费用', key: 'oldTransportCost', align: 'center', width: '100px', columnShow: true },
                        { title: '新费用', key: 'transportCost', align: 'center', width: '130px', columnShow: true }
                    ]
                },
                {
                    title: '配送费用',
                    align: 'center',
                    minWidth: '200px',
                    columnShow: true,
                    children: [
                        { title: '原费用', key: 'oldDeliveryCost', align: 'center', width: '100px', columnShow: true },
                        { title: '新费用', key: 'deliveryCost', align: 'center', width: '130px', columnShow: true }
                    ]
                },
                {
                    title: '超区费用',
                    align: 'center',
                    minWidth: '200px',
                    columnShow: true,
                    children: [
                        { title: '原费用', key: 'oldExceedCountyCost', align: 'center', width: '100px', columnShow: true },
                        { title: '新费用', key: 'exceedCountyCost', align: 'center', width: '130px', columnShow: true }
                    ]
                },
                { title: '预估费用合计', key: 'estimateCost', align: 'center', width: '120px', columnShow: true },
                { title: '订单状态', key: 'billingStatus', align: 'center', width: '120px', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '180px', columnShow: true, fixed: 'right' }
            ],
            recalculateOrderCostResultData: [],
            collectThePriceOfThisList: [],
            shippingPriceThisList: [],
            shippingPriceBookList: [],
            priceBookTypeList: [],
            statusDicts: [],
            sysAreas: [],
            collectionMethod: [],
            prePackageOptions: [
                {
                    label: '等于',
                    value: '= '
                },
                {
                    label: '大于',
                    value: '> '
                },
                {
                    label: '大于等于',
                    value: '>= '
                },
                {
                    label: '小于',
                    value: '< '
                },
                {
                    label: '小于等于',
                    value: '<= '
                }
            ],
            preCostOptions: [
                {
                    label: '等于',
                    value: '= '
                },
                {
                    label: '大于',
                    value: '> '
                },
                {
                    label: '大于等于',
                    value: '>= '
                },
                {
                    label: '小于',
                    value: '< '
                },
                {
                    label: '小于等于',
                    value: '<= '
                },
                {
                    label: '为空',
                    value: '0'
                }
            ],
            theReasonForTheFailureVisible: false,
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            selection: [],
            errorList: [],
            orderCostVisible: false,
            detailData: {},
            feeBreakdownData: [],
            settlementManagementOrderTypeList: [],
            typeTitle: '货主公司',
            customerList: [],
            temperatureType__expenses: [],
            recalculateOrderCostResultForm: {
                current: 1,
                size: 10,
                total: 0,
                billingStatus: ''
            },
            calculationResultStatusList: [],
            vehicleTypeList: [],
            costUpdateVisable: false,
            errorMsgList: [],
            isShowAll: false,
            thermosphereTypeList: [],
            shortcuts: setDatePickerShortcuts(),
            exportRecordsVisible: false,
            exportRecordsLoading: false,
            exportRecordsLoadingText: '加载中...',
            exportRecordsColumns: [
                { title: '操作人', key: 'operateName', align: 'center', minWidth: '180px', columnShow: true },
                { title: '操作时间', key: 'operateTime', align: 'center', width: '180px', columnShow: true },
                { title: '状态', key: 'status', align: 'center', width: '80px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            exportRecordsData: [],
            exportRecords: {
                current: 1,
                size: 10
            },
            exportRecordsTotal: 0,
            exportRecordsStatusList: []
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        formatPublishStatus() {
            return (value) => {
                const publishStatusText = selectDictLabel(this.publishTheStatusOfList, value);
                if (value === '0') {
                    return `<span style="color: #F4AC00">${publishStatusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #1ACD7E">${publishStatusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #B1B1B1">${publishStatusText}</span>`;
                } else {
                    return `<span style="color: #B1B1B1">${publishStatusText}</span>`;
                }
            };
        },
        /**
         * 格式化订单状态
         * @returns {(function(*): (string|string))|*}
         */
        formatStatus() {
            return (value) => {
                const statusText = selectDictLabel(this.statusDicts, value);
                if (value === '0') {
                    return `<span style="color: #B1B1B1">${statusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #F4AC00">${statusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #5670FE">${statusText}</span>`;
                } else if (value === '3') {
                    return `<span style="color: #1ACD7E">${statusText}</span>`;
                } else {
                    return statusText;
                }
            };
        },
        /**
         * 格式化车辆类型
         * @returns {function(*): string}
         */
        formatVehicleType() {
            return (value) => {
                return this.vehicleTypeList.find((item) => item.typeCode == value)?.typeName || value || '--';
            };
        },
        tableData() {
            return this.recalculateOrderCostResultData.slice((this.recalculateOrderCostResultForm.current - 1) * this.recalculateOrderCostResultForm.size, this.recalculateOrderCostResultForm.current * this.recalculateOrderCostResultForm.size);
        }
    },
    created() {
        this.getDict();
        // 将默认设置调整为当天
        this.queryForm.startTime = moment().format('YYYY-MM-DD');
        this.queryForm.endTime = moment().format('YYYY-MM-DD');
        this.queryForm.queryTime = [this.queryForm.startTime, this.queryForm.endTime];
        // 获取地址级联数据
        this.sysAreas = this.getSysAreas;
        // 货主公司列表
        this.handleChangesTheOrderType();
        // 获取温层
        this.getTemperatureType();
    },
    methods: {
        /**
         * 下载导出记录
         * @param row
         */
        downloadExportRecords(row) {
            if ('fileId' in row) {
                customerOrderExpenseManagement.downloadExportRecords({ filename: '订单费用重新计算.xls', recordId: row.fileId }, '', '', 'blob').then((res) => {
                    downloadNoData(res, 'application/vnd.ms-excel', '订单费用重新计算.xlsx');
                });
            }
        },
        // 获取合算公式列表
        getAListOfCostEffectiveFormulas() {
            orderCostRecalculated
                .getFormulaList()
                .then((response) => {
                    if (response.code === 200 && response.data) {
                        this.billingMethodList = response.data;
                    }
                })
                .catch(() => {
                    this.billingMethodList = [];
                });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.settlementManagementOrderTypeList = await this.getDictList('cost_settlement_management_order_type');
            this.fourplProductClassOptions = await this.getDictList('fourpl_product_class');
            this.productTypeDicts = await this.getDictList('fourpl_product_type');
            this.fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
            this.publishTheStatusOfList = await this.getDictList('cost_price_this_release_status');
            this.priceBookTypeList = await this.getDictList('cost_price_book_type');
            this.statusDicts = await this.getDictList('fourpl_order_status');
            this.collectionMethod = await this.getDictList('fourpl_mail_service');
            this.calculationResultStatusList = await this.getDictList('order_update_status');
            this.thermosphereTypeList = await this.getDictList('fourpl_temperature_type');
            this.exportRecordsStatusList = await this.getDictList('export_record_status');
        },
        /**
         * 获取导出记录列表
         */
        getExportRecordsList() {
            this.exportRecordsLoading = true;
            this.exportRecordsLoadingText = '加载中...';
            customerOrderExpenseManagement
                .exportRecords({ type: '2', ...this.exportRecords })
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.exportRecordsVisible = true;
                        this.exportRecordsData = res.data.records || [];
                        this.exportRecordsTotal = res.data.total || 0;
                    } else {
                        this.$message({
                            type: 'warning',
                            message: '暂无导出记录'
                        });
                    }
                })
                .finally(() => {
                    this.exportRecordsLoading = false;
                });
        },
        async getList() {
            this.loading = true;

            try {
                // 去除this.queryForm中的queryTime
                const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;

                // 处理 temperatureList 转为字符串
                if (params.temperatureList && params.temperatureList.length) {
                    params.temperatureList = params.temperatureList.join(',');
                }

                const res = await orderCostRecalculated.getTheOrderFeeRecalculationList(params);

                if (res.code === 200 && res.data) {
                    this.dataList = res.data.records || [];
                    this.total = res.data.total || 0;
                } else {
                    this.dataList = [];
                    this.total = 0;
                }
            } catch (error) {
                this.dataList = [];
                this.total = 0;
            } finally {
                this.loading = false;
            }
        },
        // 获取价格本下拉列表数据
        getPriceBookSelect() {
            this.recalculateOrderCostLoading = true;
            this.recalculateOrderCostLoadingText = '加载中...';
            const releaseStatusList = [2, 3];
            orderCostRecalculated
                .getPricesForThisList(releaseStatusList)
                .then((response) => {
                    if (response.code === 200 && response.data) {
                        // response.data 筛选 releaseStatus 为 2 3 4 的数据 未发布 已发布 已注销
                        const priceBookNewList = response.data.filter((item) => item.releaseStatus === '2' || item.releaseStatus === '3' || item.releaseStatus === '4');
                        this.collectThePriceOfThisList = [];
                        this.shippingPriceThisList = [];
                        this.shippingPriceBookList = [];
                        // priceBookNewList 中 costType 为 1 添加到 this.collectThePriceOfThisList
                        this.collectThePriceOfThisList = response.data.filter((item) => item.costType === '1');
                        // priceBookNewList 中 costType 为 2 添加到 this.shippingPriceThisList
                        this.shippingPriceThisList = response.data.filter((item) => item.costType === '2');
                        // priceBookNewList 中 costType 为 3 添加到 this.shippingPriceBookList
                        this.shippingPriceBookList = response.data.filter((item) => item.costType === '3');
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.recalculateOrderCostLoading = false;
                });
        },
        /**
         * 获取温层数据
         */
        getTemperatureType() {
            otherConfiguration
                .listTemperatureType()
                .then((response) => {
                    if (response.code === 200 && response.data.records) {
                        this.temperatureType__expenses = response.data.records;
                    }
                })
                .catch(() => {
                    this.temperatureType__expenses = [];
                });
        },
        /**
         * 根据费用类型单个更新
         * @param row 行数据
         * @param costType
         */
        handleASingleUpdate(row, costType) {
            console.log(row);
            const { id, orderNo, transOrderNo, textFormula, lanCost, arteryCost, deliveryCost, exceedCountyCost } = row;
            const typeMap = {
                lanCost: '揽收费用',
                arteryCost: '干线费用',
                deliveryCost: '配送费用',
                exceedCountyCost: '超区费用'
            };
            this.$confirm(`您确定对该订单的${typeMap[costType]}进行单个更新吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const loading = this.$loading({
                    lock: true,
                    text: '正在更新中',
                    spinner: 'el-icon-loading'
                });
                const params = {
                    orderNo,
                    transOrderNo,
                    costOrderId: id,
                    costType: costType === 'lanCost' ? '1' : costType === 'arteryCost' ? '2' : costType === 'deliveryCost' ? '3' : '4',
                    costData: costType === 'lanCost' ? lanCost : costType === 'arteryCost' ? arteryCost : costType === 'deliveryCost' ? deliveryCost : exceedCountyCost,
                    textFormula
                };
                orderCostRecalculated
                    .updateOrderCostByType(params)
                    .then((response) => {
                        if (response.code === 200) {
                            this.$message.success('更新成功');
                            // 请求列表
                            this.handleRecalculateOrderCostRequest();
                        }
                    })
                    .catch(() => {})
                    .finally(() => {
                        loading.close();
                    });
            });
        },
        /**
         * 改变订单类型
         */
        handleChangesTheOrderType() {
            this.handleQuery();
            const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;
            orderCostRecalculated
                .getCustomerList({ ...params })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.customerList = res.data || [];
                    } else {
                        this.customerList = [];
                        this.queryForm.companyId = null;
                    }
                })
                .catch(() => {
                    this.customerList = [];
                    this.queryForm.companyId = null;
                });
        },
        /**
         * 全部导出
         */
        handleClickExportAll() {
            const params = {
                ...this.recalculateOrderCostForm,
                ...this.queryForm,
                ...this.recalculateOrderCostResultForm
            };
            // 删除params中的pageNum和pageSize
            delete params.current;
            delete params.size;
            delete params.total;
            orderCostRecalculated
                .exportCalculateCost({ filename: '计算结果.xls' }, '', '', 'blob')
                .then((res) => {
                    downloadNoData(res, 'application/vnd.ms-excel', '计算结果.xlsx');
                })
                .catch(() => {});
        },
        /**
         * 查看费用详情
         * @param data
         */
        handleClickFeeDetails(data) {
            this.orderCostVisible = true;
            // this.detailData 初始为空
            this.detailData = {};
            this.detailData = { ...data };
            const { addedServices, costList } = data;
            let addedServicesFilter = [];
            if (addedServices) {
                // addedServices addedServicesContractPrice 变为 costContractPrice, addedServicesCost 变为 costData,addedServicesName 变为 costType 赋值给 addedServices
                // 使用 for of 循环遍历 addedServices
                for (const item of addedServices) {
                    item.costContractPrice = item.addedServicesContractPrice;
                    item.costData = item.addedServicesCost;
                    item.costType = item.addedServicesName;
                }
                // 去除 addedServices 中 costData 和 costContractPrice 都为 '0' 的数据 其中一个为 '0' 的数据不去除
                // 赋值给 addedServicesFilter
                addedServicesFilter = addedServices.filter((item) => item.costData != 0 || item.costContractPrice != 0);
            }
            // 筛选 costList 中 costData 和 costContractPrice 都不等于0的数据
            const costListFilter = costList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
            costListFilter.sort((a, b) => {
                return a.costType - b.costType;
            });

            // addedServices 为 null 解构赋值给 this.feeBreakdownData []
            this.feeBreakdownData = [...(addedServicesFilter || []), ...(costListFilter || [])];
        },
        /**
         * 一键更新
         */
        handleClickOneClickUpdate() {
            this.recalculateOrderCostResultLoading = true;
            this.recalculateOrderCostResultLoadingText = '正在更新中...';
            orderCostRecalculated
                .updateOrderCostBatch(this.recalculateOrderCostResultData)
                .then((response) => {
                    if (response.code === 200) {
                        if (response.data.errorMsgList?.length) {
                            this.costUpdateVisable = true;
                            this.errorMsgList = response.data.errorMsgList || [];
                        }
                        // 1s后
                        const timer = setTimeout(() => {
                            if (response.data.successNum) {
                                this.$message({
                                    message: `更新成功${response.data.successNum}条`,
                                    type: 'success'
                                });
                            }
                            clearTimeout(timer);
                        }, 1000);
                        // 请求列表
                        this.handleRecalculateOrderCostRequest();
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.recalculateOrderCostResultLoading = false;
                });
        },
        /**
         * 隐藏费用更新弹窗
         */
        handleCloseCostUpdate() {
            this.costUpdateVisable = false;
        },
        //关闭重新计算订单费用弹窗
        handleCloseRecalculateOrderCost() {
            this.recalculateOrderCostVisible = false;
            this.$refs.recalculateOrderCostForm.resetFields();
        },
        handleCloseRecalculateOrderCostResult() {
            this.recalculateOrderCostResultVisible = false;
            this.handleCloseRecalculateOrderCost();
            this.$refs.recalculateOrderCostResultForm.resetFields();
        },
        /**
         * 关闭失败原因弹窗
         */
        handleCloseTheReasonForTheFailure() {
            this.theReasonForTheFailureVisible = false;
        },
        //当前页改变时触发 跳转其他页
        handleCurrentChange(val) {
            this.recalculateOrderCostResultForm.current = val;
        },
        /**
         * 导出全部
         */
        handleExportAll() {
            // 提示框 询问是否导出全部
            this.$confirm('是否导出全部数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    this.loading = true;
                    const { queryTime, sendAddress, receiverAddress, current, size, ...params } = this.queryForm;
                    orderCostRecalculated
                        .exportRecalculate(params)
                        .then((res) => {
                            if (res.code === 200) {
                                // 提示正在导出
                                this.$message({ type: 'warning', message: '正在导出，请稍后在导出记录中下载...' });
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            this.loading = false;
                        });
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 费用更新
         * @param row 行数据
         */
        handleFeeUpdates(row) {
            this.$confirm('您确定对该订单进行费用更新吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const loading = this.$loading({
                        lock: true,
                        text: '正在更新中',
                        spinner: 'el-icon-loading'
                    });
                    orderCostRecalculated
                        .updateOrderCost(row)
                        .then((response) => {
                            if (response.code === 200) {
                                if (response.errorMsgList?.length) {
                                    this.costUpdateVisable = true;
                                    this.errorMsgList = response.errorMsgList || [];
                                }
                                // 1s后
                                const timer = setTimeout(() => {
                                    if (response.successNum) {
                                        this.$message({
                                            message: `更新成功${response.successNum}条`,
                                            type: 'success'
                                        });
                                    }
                                    clearTimeout(timer);
                                }, 1000);
                                // 请求列表
                                this.handleRecalculateOrderCostRequest();
                            }
                        })
                        .catch(() => {})
                        .finally(() => {
                            loading.close();
                        });
                })
                .catch(() => {});
        },
        // 打开重新计算订单费用弹窗
        handleOpenRecalculate() {
            this.recalculateOrderCostVisible = true;
            // 获取合算公式列表数据
            this.getAListOfCostEffectiveFormulas();
            // 获取价格本下拉列表数据
            this.getPriceBookSelect();
        },
        /**
         * 打开失败原因弹窗
         */
        handleOpenTheReasonForTheFailure() {
            this.theReasonForTheFailureVisible = true;
        },
        handleQuery() {
            this.queryForm.current = 1;
            const { queryTime } = this.queryForm;

            // 验证queryTime是否包含两个合法的日期字符串
            const isValidDateRange = queryTime && queryTime.length === 2 && !queryTime.some((date) => date === 'Invalid Date');

            if (isValidDateRange) {
                this.queryForm.startTime = queryTime[0] + ' 00:00:00';
                this.queryForm.endTime = queryTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startTime = null;
                this.queryForm.endTime = null;
            }
            if (this.queryForm.sendAddress) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = this.queryForm.sendAddress;
                this.queryForm.sendProvinceId = sendProvinceId || undefined;
                this.queryForm.sendCityId = sendCityId || undefined;
                this.queryForm.sendCountyId = sendCountyId || undefined;
                this.queryForm.sendTownId = sendTownId || undefined;
            } else {
                this.queryForm.sendProvinceId = undefined;
                this.queryForm.sendCityId = undefined;
                this.queryForm.sendCountyId = undefined;
                this.queryForm.sendTownId = undefined;
            }
            if (this.queryForm.receiverAddress) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = this.queryForm.receiverAddress;
                this.queryForm.receiverProvinceId = receiverProvinceId || undefined;
                this.queryForm.receiverCityId = receiverCityId || undefined;
                this.queryForm.receiverCountyId = receiverCountyId || undefined;
                this.queryForm.receiverTownId = receiverTownId || undefined;
            } else {
                this.queryForm.receiverProvinceId = undefined;
                this.queryForm.receiverCityId = undefined;
                this.queryForm.receiverCountyId = undefined;
                this.queryForm.receiverTownId = undefined;
            }

            this.getList();
        },
        /**
         * 重新计算订单费用 确定
         */
        handleRecalculateOrderCost() {
            this.$refs.recalculateOrderCostForm.validate((valid) => {
                if (valid) {
                    // 请求列表
                    this.handleRecalculateOrderCostRequest();
                }
            });
        },
        /**
         * 重新计算订单费用 请求
         */
        handleRecalculateOrderCostRequest() {
            this.recalculateOrderCostLoading = true;
            this.recalculateOrderCostLoadingText = '加载中...';
            this.recalculateOrderCostResultLoading = true;
            this.recalculateOrderCostResultLoadingText = '加载中...';
            const params = {
                orderIdList: this.selection.map((o) => o.id),
                ...this.recalculateOrderCostForm,
                ...this.queryForm,
                ...this.recalculateOrderCostResultForm
            };
            // 删除params中的pageNum和pageSize
            delete params.current;
            delete params.size;
            delete params.total;
            orderCostRecalculated
                .orderCostRecalculate(params)
                .then((response) => {
                    if (response.code === 200) {
                        this.recalculateOrderCostResultData = response.data.success || [];
                        this.recalculateOrderCostResultForm.total = response.data.success?.length ?? 0;
                        this.errorList = response.data.error || [];
                        this.recalculateOrderCostResultVisible = true;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.recalculateOrderCostLoading = false;
                    this.recalculateOrderCostResultLoading = false;
                });
        },
        /**
         * 多选框选中数据
         * @param selection 选中的数据
         */
        handleSelectionChange(selection) {
            this.selection = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        //每页条数改变时触发 选择一页显示多少行
        handleSizeChange(val) {
            this.recalculateOrderCostResultForm.current = 1;
            this.recalculateOrderCostResultForm.size = val;
        },
        /**
         * 隐藏 导出记录 弹窗
         */
        hideExportRecords() {
            this.exportRecordsVisible = false;
        },

        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.queryForm.sendAddress = [];
            this.queryForm.sendProvinceId = undefined;
            this.queryForm.sendCityId = undefined;
            this.queryForm.sendCountyId = undefined;
            this.queryForm.sendTownId = undefined;
            this.queryForm.receiverAddress = [];
            this.queryForm.receiverProvinceId = undefined;
            this.queryForm.receiverCityId = undefined;
            this.queryForm.receiverCountyId = undefined;
            this.queryForm.receiverTownId = undefined;
            this.handleChangesTheOrderType();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
    .el-dialog__header {
        padding-bottom: 20px;
    }
    .el-dialog__footer {
        padding: 10px 20px;
    }
}
.box-period {
    height: calc(100vh - 200px);

    ::v-deep {
        .el-scrollbar {
            height: 100%;
        }

        .el-scrollbar__wrap {
            //overflow: scroll;
            width: 102%;
        }
    }
}
.box__calculation_results {
    display: flex;
    width: fit-content;
    gap: 15px;
    ::v-deep {
        .el-alert {
            width: fit-content;
        }
        .el-alert__content {
            display: flex;
            margin-top: 10px;
            margin-bottom: 10px;
            justify-content: space-between;
            align-items: center;
            flex: 1;
            .el-alert__title {
                line-height: inherit;
            }
        }
        .el-alert__icon {
            line-height: inherit;
            font-size: 20px;
            width: auto;
        }
        .el-alert__description {
            font-size: 14px;
            margin: 0;
            button {
                padding: 0;
                color: #ff2a2a;
            }
        }
    }
}
.the-reason-for-the-failure {
    ::v-deep {
        .el-alert__content {
            display: flex;
            margin-top: 5px;
            margin-bottom: 5px;
            justify-content: space-between;
            align-items: center;
            flex: 1;
            .el-alert__title {
                line-height: inherit;
            }
        }
        .el-alert__icon {
            line-height: inherit;
            font-size: 20px;
        }
        .el-alert__description {
            font-size: 14px;
            margin: 0;
            button {
                padding: 0;
                color: #ff2a2a;
            }
        }
    }
}
::v-deep .goodsPackages {
    .el-input__inner {
        padding-right: 15px !important;
    }
}
.box__flex__drawer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.box__costUpdate_results {
    display: grid;
    grid-template-columns: 180px 1fr;
    grid-template-rows: 1fr;
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    ::v-deep .el-result {
        padding: 0;
    }
    .div2 {
        height: 200px;

        ::v-deep {
            .el-scrollbar {
                height: 100%;
            }

            .el-scrollbar__wrap {
                //overflow: scroll;
                width: 102.2%;
            }
        }
    }
}
</style>
