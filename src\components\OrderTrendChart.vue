<template>
    <div class="order-trend-chart">
        <div class="chart-wrapper">
            <!-- 添加 filter-overlay -->
            <div class="filter-overlay">
                <el-radio-group v-model="queryType" size="small">
                    <el-radio-button label="1">按日</el-radio-button>
                    <el-radio-button label="3">按月</el-radio-button>
                    <el-radio-button label="4">按年</el-radio-button>
                </el-radio-group>

                <!-- 按日期范围 -->
                <el-date-picker v-if="queryType === '1'" v-model="dateRange" :default-value="[thirtyDaysAgo, today]" end-placeholder="结束日期" size="small" start-placeholder="开始日期" style="width: 300px" type="daterange" value-format="YYYY-MM-DD" />

                <!-- 按月范围 -->
                <el-date-picker v-if="queryType === '3'" v-model="dateRange" :default-value="[twelveMonthsAgo, today]" end-placeholder="结束月份" size="small" start-placeholder="开始月份" type="monthrange" value-format="YYYY-MM" />

                <!-- 按年范围 -->
                <div v-if="queryType === '4'" class="year-range-wrapper">
                    <el-date-picker v-model="yearRange[0]" placeholder="开始年份" size="small" style="width: 120px" type="year" value-format="YYYY" @change="handleYearChange" />
                    <span class="separator">至</span>
                    <el-date-picker v-model="yearRange[1]" placeholder="结束年份" size="small" style="width: 120px" type="year" value-format="YYYY" @change="handleYearChange" />
                </div>
            </div>

            <div ref="chartRef" class="chart-container"></div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { onMounted, onUnmounted, ref, watch, computed } from 'vue';
import home from '@/api/home/<USER>';

export default {
    name: 'OrderTrendChart',
    props: {
        isCarrier: {
            type: Boolean,
            default: false
        }
    },
    setup(props) {
        const chartRef = ref(null);
        let chart = null;
        const queryType = ref('1');
        const dateRange = ref([]);
        const yearRange = ref([null, null]);
        const isInitializing = ref(true);

        // 计算默认日期
        const today = computed(() => new Date());

        const thirtyDaysAgo = computed(() => {
            const date = new Date();
            date.setDate(date.getDate() - 29);
            return date;
        });

        const twelveMonthsAgo = computed(() => {
            const date = new Date();
            date.setMonth(date.getMonth() - 11);
            return date;
        });

        // 初始化图表配置
        const initChartOption = () => {
            return {
                title: {
                    text: '订单趋势',
                    left: '0%',
                    top: '0%'
                },
                grid: {
                    top: '60px',
                    bottom: '1%',
                    left: '1%',
                    right: '0%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    top: '0',
                    left: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: [], // 待填充
                    axisLabel: {
                        formatter: function(value) {
                            if (queryType.value === '1') {
                                // 按日查询时，将 YYYY-MM-DD 格式转换为 MM-DD
                                return value.substring(5); // 截取 MM-DD 部分
                            }
                            return value;
                        }
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '数量',
                        nameTextStyle: {
                            align: 'right',
                            padding: [0, 5, 0, 0]
                        }
                    },
                    {
                        type: 'value',
                        name: '金额',
                        nameTextStyle: {
                            align: 'left',
                            padding: [0, 0, 0, 5]
                        }
                    }
                ],
                series: [
                    {
                        name: '数量',
                        type: 'bar',
                        data: [],
                        yAxisIndex: 0,
                        z: 1,
                        itemStyle: {
                            color: '#5985F8',
                            borderRadius: [4, 4, 0, 0]
                        },
                        barWidth: '30%',
                        label: {
                            show: true,
                            position: 'top',
                            distance: 5,
                            textStyle: {
                                color: '#5985F8'
                            }
                        }
                    },
                    {
                        name: '金额',
                        type: 'line',
                        data: [],
                        yAxisIndex: 1,
                        z: 2,
                        itemStyle: {
                            color: '#FFBB51'
                        },
                        label: {
                            show: true,
                            position: 'top',
                            distance: 15,
                            textStyle: {
                                color: '#FFBB51'
                            }
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(255, 187, 81, 0.3)'
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(255, 187, 81, 0.1)'
                                    }
                                ]
                            }
                        }
                    }
                ]
            };
        };

        // 初始化图表
        const initChart = () => {
            if (chart) {
                chart.dispose();
            }
            chart = echarts.init(chartRef.value);
            chart.setOption(initChartOption());
        };

        // 设置默认日期范围
        const setDefaultDateRange = (type, oldType) => {
            // 只在按年的情况下直接触发请求
            const shouldTriggerRequest = oldType === '4' || type === '4';

            switch (type) {
                case '1': // 按日
                    dateRange.value = [thirtyDaysAgo.value.toISOString().split('T')[0], today.value.toISOString().split('T')[0]];
                    break;
                case '3': // 按月
                    dateRange.value = [twelveMonthsAgo.value.toISOString().slice(0, 7), today.value.toISOString().slice(0, 7)];
                    break;
                case '4': // 按年
                    yearRange.value = [null, null];
                    break;
            }

            // 只在涉及按年切换时触发请求
            if (shouldTriggerRequest) {
                updateChartData();
            }
        };

        // 监听查询类型变化
        watch(
            queryType,
            (newType, oldType) => {
                if (isInitializing.value) {
                    isInitializing.value = false;
                    return;
                }
                setDefaultDateRange(newType, oldType);
            },
            { flush: 'post' }
        );

        // 监听日期范围变化
        watch(
            dateRange,
            (newRange, oldRange) => {
                if (!isInitializing.value && queryType.value !== '4' && newRange && newRange.length === 2 && JSON.stringify(newRange) !== JSON.stringify(oldRange)) {
                    updateChartData();
                }
            },
            { flush: 'post' }
        );

        // 处理年份变化
        const handleYearChange = () => {
            if (yearRange.value[0] && yearRange.value[1]) {
                if (yearRange.value[0] > yearRange.value[1]) {
                    [yearRange.value[0], yearRange.value[1]] = [yearRange.value[1], yearRange.value[0]];
                }
                updateChartData();
            }
        };

        // 更新图表数据
        const updateChartData = async () => {
            try {
                const params = {
                    queryType: queryType.value
                };

                // 按年且有选择年份范围时才添加日期参数
                if (queryType.value === '4' && yearRange.value[0] && yearRange.value[1]) {
                    params.startQueryDate = yearRange.value[0];
                    params.endQueryDate = yearRange.value[1];
                } else if (queryType.value !== '4' && dateRange.value && dateRange.value.length === 2) {
                    // 非按年模式下的日期参数
                    params.startQueryDate = dateRange.value[0];
                    params.endQueryDate = dateRange.value[1];
                }

                const response = await (props.isCarrier ? home.getOrderTrend : home.getOrderTrendOwner)(params);
                const list = response.data;

                // 完全重置图表选项
                const baseOption = initChartOption();

                if (!list || list.length === 0) {
                    // 无数据时的配置
                    baseOption.xAxis.show = false;
                    baseOption.series[0].data = [];
                    baseOption.series[1].data = [];
                    baseOption.graphic = [
                        {
                            type: 'text',
                            left: 'center',
                            top: 'middle',
                            style: {
                                text: '暂无数据',
                                fontSize: 14,
                                fill: '#999'
                            }
                        }
                    ];
                } else {
                    // 有数据时的配置
                    const dates = list.map((item) => item.orderDate);
                    const orders = list.map((item) => Number(item.orderNum));
                    const amounts = list.map((item) => Number(item.orderAmount));

                    baseOption.xAxis.show = true;
                    baseOption.xAxis.data = dates;
                    baseOption.series[0].data = orders;
                    baseOption.series[1].data = amounts;
                    baseOption.graphic = [];
                }

                chart.clear();
                chart.setOption(baseOption, true);
            } catch (error) {
                console.error('获取订单趋势数据失败:', error);
            }
        };

        // 优化 resize 监听
        const handleResize = () => {
            chart?.resize();
        };

        onMounted(() => {
            initChart();
            setDefaultDateRange('1');
            isInitializing.value = false;
            updateChartData();

            // 添加 resize 监听
            window.addEventListener('resize', handleResize);
        });

        onUnmounted(() => {
            // 移除 resize 监听
            window.removeEventListener('resize', handleResize);
            chart?.dispose();
        });

        return {
            chartRef,
            queryType,
            dateRange,
            yearRange,
            today,
            thirtyDaysAgo,
            twelveMonthsAgo,
            handleYearChange
        };
    }
};
</script>

<style lang="scss" scoped>
.order-trend-chart {
    display: flex;
    flex-direction: column;
    height: 100%;

    .chart-wrapper {
        flex: 1;
        min-height: 300px;
        position: relative;

        .filter-overlay {
            position: absolute;
            top: -10px;
            right: 0;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 12px; // 设置间距
            background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
            padding: 8px;
            border-radius: 4px;
        }

        .chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
        }
    }
}

.year-range-wrapper {
    display: inline-flex;
    align-items: center;

    .separator {
        margin: 0 8px;
        color: #606266;
    }
}
</style>
