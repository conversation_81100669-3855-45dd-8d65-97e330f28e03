<template>
    <el-row :gutter="40">
        <el-col v-if="!form.id">
            <el-empty description="请选择左侧菜单后操作" :image-size="100"></el-empty>
        </el-col>
        <template v-else>
            <el-col :lg="12">
                <h2>{{ form.name || '新增菜单' }}</h2>
                <el-form :model="form" :rules="rules" ref="dialogForm" label-width="80px" label-position="left">
                    <el-form-item label="显示名称" prop="name">
                        <el-input v-model="form.name" clearable placeholder="菜单显示名字"></el-input>
                    </el-form-item>
                    <el-form-item label="上级菜单" prop="parent.id">
                        <el-cascader v-model="form.parent.id" :options="menuOptions" :props="menuProps" :show-all-levels="false" placeholder="顶级菜单" clearable></el-cascader>
                    </el-form-item>
                    <el-form-item label="类型" prop="type">
                        <el-radio-group v-model="form.type">
                            <el-radio-button label="menu">菜单</el-radio-button>
                            <el-radio-button label="iframe">Iframe</el-radio-button>
                            <el-radio-button label="link">外链</el-radio-button>
                            <el-radio-button label="button">按钮</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="别名" prop="alias">
                        <el-input v-model="form.alias" clearable placeholder="菜单别名"></el-input>
                        <div class="el-form-item-msg">系统唯一且与内置组件名一致，否则导致缓存失效。如类型为Iframe的菜单，别名将代替源地址显示在地址栏</div>
                    </el-form-item>
                    <el-form-item label="菜单图标" prop="logo">
                        <sc-icon-select v-model="form.logo" clearable></sc-icon-select>
                    </el-form-item>
                    <el-form-item label="路由地址" prop="path">
                        <el-input v-model="form.path" clearable placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="重定向" prop="redirect">
                        <el-input v-model="form.redirect" clearable placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="菜单高亮" prop="menuHighlight">
                        <el-input v-model="form.menuHighlight" clearable placeholder=""></el-input>
                        <div class="el-form-item-msg">子节点或详情页需要高亮的上级菜单路由地址</div>
                    </el-form-item>
                    <el-form-item label="视图" prop="view">
                        <el-input v-model="form.view" clearable placeholder="">
                            <template #prepend>views/</template>
                        </el-input>
                        <div class="el-form-item-msg">如父节点、链接或Iframe等没有视图的菜单不需要填写</div>
                    </el-form-item>
                    <el-form-item label="权限标识" prop="permission">
                        <el-input v-model="form.permission" clearable placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="颜色" prop="affix">
                        <el-color-picker v-model="form.affix" :predefine="predefineColors"></el-color-picker>
                    </el-form-item>
                    <el-form-item label="是否隐藏" prop="hide">
                        <el-checkbox v-model="form.hide">隐藏菜单</el-checkbox>
                        <!-- <el-checkbox v-model="form.meta.hiddenBreadcrumb">隐藏面包屑</el-checkbox>  -->
                        <div class="el-form-item-msg">菜单不显示在导航中，但用户依然可以访问，例如详情页</div>
                    </el-form-item>
                    <el-form-item label="整页路由" prop="wholePageRoute">
                        <el-switch v-model="form.wholePageRoute" />
                    </el-form-item>
                    <el-form-item label="排序" prop="sort">
                        <el-input v-model="form.sort" clearable placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="标签" prop="label">
                        <el-input v-model="form.label" clearable placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="save" :loading="loading">保 存</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :lg="12">
                <el-container v-loading="dataRuleLoading">
                    <h2>数据权限</h2>
                    <el-header>
                        <div class="left-panel">
                            <el-button type="primary" icon="el-icon-plus" @click="addDataRule"></el-button>
                            <el-button type="danger" plain icon="el-icon-delete" @click="delSelectDataRule()"></el-button>
                        </div>
                    </el-header>
                    <el-main class="nopadding">
                        <scTable ref="table" :data="dataRuleList" row-key="id" stripe @selection-change="dataRuleHandleSelectionChange">
                            <el-table-column type="selection" width="50"></el-table-column>
                            <el-table-column label="规则名称" prop="ruleName" width="150"></el-table-column>
                            <el-table-column label="规则字段" prop="ruleField" width="250"></el-table-column>
                            <el-table-column #default="scope">
                                <el-button type="text" @click="viewDataRule(scope.row)">查看</el-button>
                                <el-button type="text" @click="editDataRule(scope.row)">修改</el-button>
                                <el-button type="text" @click="delDataRule(scope.row)">删除</el-button>
                            </el-table-column>
                        </scTable>
                    </el-main>
                </el-container>
            </el-col>
        </template>
    </el-row>
    <data-rule-form-dialog ref="dataRuleFormDialog" :mode="dataRuleFormDialog" :refreshList="getDataRuleList" v-if="asynDataRuleForm" draggable @closed="asynDataRuleForm = false"></data-rule-form-dialog>
</template>

<script>
import scIconSelect from '@/components/scIconSelect';
import dataRuleFormDialog from './dataRuleForm';

export default {
    components: {
        scIconSelect,
        dataRuleFormDialog
    },
    props: {
        menu: { type: Object, default: () => {} }
    },
    data() {
        return {
            //系统菜单表单
            form: {
                id: '',
                parent: {
                    id: ''
                },
                name: '',
                alias: '',
                type: '',
                logo: '',
                path: '',
                redirect: '',
                permission: '',
                menuHighlight: '',
                view: '',
                affix: '',
                hide: false,
                sort: '',
                wholePageRoute: false,
                label: '',
                fixedLabelBar: '',
                sysPlatform: {
                    id: ''
                }
            },
            //数据权限列表
            dataRuleList: [],
            //数据权限列表选中
            dataRuleSelection: [],
            asynDataRuleForm: false,
            menuOptions: [],
            menuProps: {
                value: 'id',
                label: 'title',
                checkStrictly: true,
                multiple: false
            },
            predefineColors: ['#ff4500', '#ff8c00', '#ffd700', '#67C23A', '#00ced1', '#409EFF', '#c71585'],
            rules: {},
            loading: false,
            dataRuleLoading: false
        };
    },
    watch: {
        menu: {
            handler() {
                this.menuOptions = this.treeToMap(this.menu);
            },
            deep: true
        }
    },
    mounted() {},
    methods: {
        //简单化菜单
        treeToMap(tree) {
            const map = [];
            tree.forEach((item) => {
                var obj = {
                    id: item.id,
                    parentId: item.parent.id,
                    title: item.name,
                    children: item.children && item.children.length > 0 ? this.treeToMap(item.children) : null
                };
                map.push(obj);
            });
            return map;
        },
        /*
         * 保存系统菜单
         * @author: 路正宁
         * @date: 2023-03-23 17:57:46
         */
        async save() {
            this.loading = true;
            // 判断this.form.parent.id 的类型
            if (typeof this.form.parent.id === 'undefined') this.form.parent.id = '0';
            if (typeof this.form.parent.id === 'object') {
                // 取最后一个元素
                const id = this.form.parent.id.pop();
                this.form.parent.id = id;
                this.form.parentId = id;
                this.form.parent.parentIds = this.form.parent.id.toString();
            }
            var res = await this.$API.sysMenuService.save(this.form);
            if (res.code == 200) {
                this.$message.success('保存成功');
            } else {
                this.$message.warning('保存失败');
            }
            this.loading = false;
        },
        /*
         * 设置系统菜单表单数据
         * @author: 路正宁
         * @date: 2023-03-23 17:58:09
         */
        setData(data, pid) {
            if (this.$ObjectUtils.isEmpty(data)) {
                this.form.id = null;
                this.dataRuleList = null;
            } else {
                this.form = data;
                this.form.parent.id = pid;
                this.getDataRuleList();
            }
        },
        /*
         * 获取数据权限列表
         * @author: 路正宁
         * @date: 2023-03-23 16:45:07
         */
        async getDataRuleList() {
            this.dataRuleLoading = true;
            var res = await this.$API.sysDataRulesService.list({
                'sysMenu.id': this.form.id,
                //当前页码
                current: 1,
                //每页条数
                size: 1000
            });
            if (res.code == 200) {
                this.dataRuleList = res.data.records;
            } else {
                this.$Response.errorNotice(res, '数据权限查询失败');
                this.dataRuleList = null;
            }
            this.dataRuleLoading = false;
        },
        /*
         * 添加数据权限
         * @author: 路正宁
         * @date: 2023-03-23 17:10:35
         */
        addDataRule() {
            this.asynDataRuleForm = true;
            this.$nextTick(() => {
                this.$refs.dataRuleFormDialog.addView(this.form);
            });
        },
        /*
         * 编辑数据权限
         * @author: 路正宁
         * @date: 2023-03-23 17:58:29
         */
        editDataRule(dataRule) {
            this.asynDataRuleForm = true;
            this.$nextTick(() => {
                this.$refs.dataRuleFormDialog.editView(dataRule);
            });
        },
        /*
         * 查看数据权限
         * @author: 路正宁
         * @date: 2023-03-23 17:58:41
         */
        viewDataRule(dataRule) {
            this.asynDataRuleForm = true;
            this.$nextTick(() => {
                this.$refs.dataRuleFormDialog.view(dataRule);
            });
        },
        /*
         * 删除数据权限
         * @author: 路正宁
         * @date: 2023-03-23 17:58:55
         */
        async delDataRule(dataRule) {
            this.dataRuleLoading = true;
            var res = await this.$API.sysDataRulesService.delete(dataRule.id);
            if (res.code == 200) {
                this.$message.success('删除成功');
                this.getDataRuleList();
            } else {
                this.$Response.errorNotice(res, '删除失败');
            }
            this.dataRuleLoading = false;
        },
        /*
         * 数据权限列表选中事件
         * @author: 路正宁
         * @date: 2023-03-24 10:42:45
         */
        dataRuleHandleSelectionChange(val) {
            this.dataRuleSelection = val;
        },
        /*
         * 删除选中的数据权限数据
         * @author: 路正宁
         * @date: 2023-03-24 10:43:53
         */
        async delSelectDataRule() {
            //非空校验
            if (this.$ObjectUtils.isEmpty(this.dataRuleSelection)) {
                this.$message.warning('请选择要删除的数据');
                return;
            }
            //删除操作确认
            var confirm = await this.$confirm('确认删除已选择的数据吗？', '提示', {
                type: 'warning',
                confirmButtonText: '删除',
                confirmButtonClass: 'el-button--danger'
            }).catch(() => {});
            if (confirm != 'confirm') {
                return false;
            }
            //拼接数据id
            var reqData = this.dataRuleSelection.map((item) => item.id).join(',');
            this.dataRuleLoading = true;
            //调用删除接口
            var res = await this.$API.sysDataRulesService.delete(reqData);
            if (res.code == 200) {
                this.$message.success('删除成功');
                //刷新数据权限列表
                this.getDataRuleList();
            } else {
                this.$Response.errorNotice(res, '删除失败');
            }
            this.dataRuleLoading = false;
        }
    }
};
</script>

<style scoped>
h2 {
    font-size: 17px;
    color: #3c4a54;
    padding: 0 0 30px 0;
}

[data-theme='dark'] h2 {
    color: #fff;
}
[data-theme='dark'] {
    border-color: #434343;
}
</style>
