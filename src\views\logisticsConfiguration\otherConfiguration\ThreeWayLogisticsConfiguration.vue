<template>
    <div class="app-container">
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="table-header">
                <div></div>
                <el-button
                    type="primary"
                    :icon="Refresh"
                    circle
                    @click="getList"
                />
            </div>
            <el-table :data="tableData" border style="width: 100%" v-loading="loading">
                <el-table-column align="center" label="序号" type="index" width="80" />
                <el-table-column align="center" label="参数名称" prop="configName" />
                <el-table-column align="center" label="参数键名" prop="configKey" />
                <el-table-column align="center" label="参数值">
                    <template #default="{ row }">
                        <!-- 开关类型 -->
                        <template v-if="row.configType === '1'">
                            <el-switch 
                                v-model="row.tempValue" 
                                active-color="#13ce66" 
                                active-text="启用" 
                                inactive-color="#dcdfe6" 
                                inactive-text="关闭" 
                                inline-prompt 
                                @change="handleValueChange(row)" 
                            />
                        </template>
                        <!-- 时间选择器类型 -->
                        <template v-else-if="row.configType === '2'">
                            <el-time-picker 
                                v-model="row.tempValue" 
                                format="HH:mm:ss" 
                                placeholder="选择时间" 
                                style="width: 100%" 
                                value-format="HH:mm:ss" 
                                @change="handleValueChange(row)" 
                            />
                        </template>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="参数类型" prop="configType">
                    <template #default="{ row }">
                        <span v-if="row.configType === '1'">开关</span>
                        <span v-else-if="row.configType === '2'">时间选择器</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="备注" prop="remark">
                    <template #header>
                        <span>备注</span>
                        <el-tooltip
                            content="双击编辑备注信息"
                            placement="top"
                        >
                            <el-icon class="el-icon--right"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </template>
                    <template #default="{ row }">
                        <span 
                            v-if="!row.isEditingRemark" 
                            @dblclick="handleStartEditRemark(row)"
                        >{{ row.remark || '-' }}</span>
                        <el-input
                            v-else
                            v-model="row.tempRemark"
                            @blur="handleSaveRemark(row)"
                            ref="remarkInput"
                        />
                    </template>
                </el-table-column>
            </el-table>
            
            <div class="pagination-container">
                <el-pagination
                    v-model:current-page="queryParams.current"
                    v-model:page-size="queryParams.size"
                    :page-sizes="[10, 20, 30, 50]"
                    :total="total"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </el-card>
    </div>
</template>

<script>
import { ElMessageBox } from 'element-plus';
import { QuestionFilled, Refresh } from '@element-plus/icons-vue';
import threeWayLogisticsConfiguration from '@/api/logisticsConfiguration/threeWayLogisticsConfiguration';

export default {
    name: 'ThreeWayLogisticsConfiguration',
    components: {
        QuestionFilled,
        Refresh
    },
    data() {
        return {
            loading: false,
            total: 0,
            queryParams: {
                current: 1,
                size: 10
            },
            tableData: [],
            isInitializing: true,
            Refresh
        };
    },
    methods: {
        getList() {
            this.loading = true;
            this.isInitializing = true;
            threeWayLogisticsConfiguration.getThreeWayLogisticsConfiguration(this.queryParams)
                .then(res => {
                    if (res.code === 200) {
                        this.tableData = (res.data.records || []).map(item => ({
                            ...item,
                            tempValue: item.configType === '1' ? item.configValue === '1' : item.configValue,
                            tempRemark: item.remark,
                            isEditingRemark: false
                        }));
                        this.total = res.data.total;
                    }
                })
                .finally(() => {
                    this.loading = false;
                    this.$nextTick(() => {
                        this.isInitializing = false;
                    });
                });
        },

        // 开始编辑备注
        handleStartEditRemark(row) {
            row.isEditingRemark = true;
            row.tempRemark = row.remark;
            this.$nextTick(() => {
                this.$refs.remarkInput?.[0]?.focus();
            });
        },

        // 保存备注
        async handleSaveRemark(row) {
            if (row.tempRemark === row.remark) {
                row.isEditingRemark = false;
                return;
            }

            try {
                await ElMessageBox.confirm('确认修改备注？', '', {
                    confirmButtonText: '确认',
                    cancelButtonText: '取消',
                    type: 'warning',
                    icon: QuestionFilled,
                    center: true,
                    customClass: 'custom-message-box'
                });

                const params = {
                    id: row.id,
                    configValue: row.configValue,
                    remark: row.tempRemark
                };

                const res = await threeWayLogisticsConfiguration.updateThreeWayLogisticsConfiguration(params);
                if (res.code === 200) {
                    row.remark = row.tempRemark;
                    row.isEditingRemark = false;
                    this.$message.success('修改成功');
                } else {
                    throw new Error(res.message);
                }
            } catch (error) {
                row.tempRemark = row.remark;
                if (error.message) {
                    this.$message.error(error.message);
                }
            } finally {
                row.isEditingRemark = false;
            }
        },

        // 处理值变更
        async handleValueChange(row) {
            if (this.isInitializing) return;

            const oldValue = row.configType === '1' ? row.configValue : row.configValue;
            const newValue = row.configType === '1' ? (row.tempValue ? '1' : '0') : row.tempValue;

            if (oldValue === newValue) return;

            try {
                await ElMessageBox.confirm('确认修改？', '', {
                    confirmButtonText: '确认',
                    cancelButtonText: '取消',
                    type: 'warning',
                    icon: QuestionFilled,
                    center: true,
                    customClass: 'custom-message-box'
                });

                const params = {
                    id: row.id,
                    configValue: newValue,
                    remark: row.remark
                };
                
                const res = await threeWayLogisticsConfiguration.updateThreeWayLogisticsConfiguration(params);
                if (res.code === 200) {
                    row.configValue = newValue;
                    this.$message.success('修改成功');
                } else {
                    throw new Error(res.message);
                }
            } catch (error) {
                row.tempValue = row.configType === '1' ? row.configValue === '1' : row.configValue;
                if(error.message) {
                    this.$message.error(error.message);
                }
            }
        },

        // 处理每页条数变化
        handleSizeChange(val) {
            this.queryParams.size = val;
            this.getList();
        },

        // 处理页码变化
        handleCurrentChange(val) {
            this.queryParams.current = val;
            this.getList();
        }
    },
    created() {
        this.getList();
    }
};
</script>

<style lang="scss">
// 使用全局样式以确保能覆盖 ElMessageBox 的默认样式
.custom-message-box {
    padding: 20px 0;

    .el-message-box__header {
        padding-top: 0;
    }

    .el-message-box__headerbtn {
        display: none;
    }

    .el-message-box__status {
        font-size: 48px !important;
        color: #ff9900 !important;
        top: 35px !important;
    }

    .el-message-box__message {
        margin-top: 35px;
        font-size: 16px;
    }

    .el-message-box__btns {
        text-align: center;
        margin-top: 20px;
    }
}
</style>

<style lang="scss" scoped>
.el-icon--right {
    margin-left: 4px;
    font-size: 16px;
    vertical-align: middle;
    cursor: help;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
</style>
