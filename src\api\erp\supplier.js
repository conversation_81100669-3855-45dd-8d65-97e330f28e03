/*
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-24 18:25:47
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-15 19:30:32
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\api\erp\supplier.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import http from "@/utils/request"

export default {
  list: function (inputForm) {
    return http.get('/erp/supplier/erpSupplierProduction/list',inputForm)
  },

  delete: function (ids) {
    return http.delete('/erp/supplier/erpSupplierProduction/delete',ids)
  },

  detail: function (id) {
    return http.get('/erp/supplier/erpSupplierProduction/queryById',id)
  },

  save: function (data) {
    return http.post('/erp/supplier/erpSupplierProduction/save',data)
  },
  delFlag: function (params) {
    return http.get('/erp/supplier/erpSupplierProduction/update', params)
  },
  erpSupplierProductionApproval: function (id) {
    return http.get('/erp/supplier/erpSupplierProductionApproval/list',id)
  },
  checkLicenseCode: function (params) {
    return http.get(`/erp/supplier/erpSupplierProduction/checkLicenseCode`, params)
  },
  
   // 删除许可证信息
   deleteLicence: function (params) {
    return http.delete(`/erp/supplier/erpSupplierProductionLicence/delete`, params)
  },
  // 删除委托书
  erpCustomersDelegate: function (ids) {
    return http.delete(' /erp/supplier/erpSupplierProductionDelegate/delete', ids)
  },
  // 删除委托商品
  erpCustomersDelegateCommodity: function (ids) {
    return http.delete('/erp/supplier/erpSupplierProductionDelegateCommodity/delete', ids)
  },
  // 删除质保协议
  erpCustomersQuality: function (ids) {
    return http.delete('/erp/supplier/erpSupplierProductionQuality/delete', ids)
  },
  // 删除附件
  erpCommonFile: function (ids) {
    return http.delete('/erp/common/erpCommonFile/delete', ids)
  },
  // 删除gmp
  deleteGmp: function (params) {
    return http.delete(`/erp/supplier/erpSupplierGmpCertificate/delete`, params)
  },
  // 获取自编码
  getSelfCodeByType: function (params) {
    return http.get(`/erp/supplier/erpSupplierProduction/getSelfCodeByType`, params)
  },
  
  // /erp/supplier/erpSupplierProductionLicence/delete
//   delFlag: function (params) {
//     return http.get('/erp/customer/erpCustomers/update',params)
//   },

//   exportExcel: function (params) {
//     return http.get('/sys/notice/export',params,'blob')
//   },

//   importExcel: function (data) {
//     return http.post( '/sys/notice/import', data)
//   },
//   release:function ({id,status}) {
//     return http.get( `/sys/notice/updateStatusById?id=${id}&status=${status}`)
//   },
//   shopList:function (params) {
//     return http.get( `/erp/product/erpCommodity/list`,params)
//   },
//   getTerat:function (params) {
//     return http.get( `/erp/assist/erpTreatmentScopeSet/treeData`,params)
//   },
//   getRange:function (params) {
//     return http.get( `/erp/assist/erpMassRangeSet/treeData`,params)
//   },
//   getRangeId:function (params) {
//     return http.get( `/erp/assist/diagnosisTreatmentScopeConfig/listByCus`,params)
//   },
}
