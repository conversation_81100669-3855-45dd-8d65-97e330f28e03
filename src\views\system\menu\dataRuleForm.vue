<template>
  <sc-dialog v-model="visible" :title="title" @closed="$emit('closed')">
    <el-form
      :model="inputForm"
      ref="inputForm"
      v-loading="loading"
      :class="method === 'view' ? 'readonly' : ''"
      :disabled="method === 'view'"
      label-width="120px"
    >
      <el-row :gutter="15">
        <el-col :span="24">
          <el-form-item
            label="数据规则名称"
            prop="ruleName"
            clearable
            :rules="[
              { required: true, message: '数据规则名称不能为空', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="inputForm.ruleName"
              placeholder="请填写数据规则名称"
            ></el-input>
          </el-form-item>
        </el-col>
		  <el-col :span="24">
			  <el-form-item label="规则mapper方法" prop="mapperClass" :rules="[]">
				  <el-input
					  v-model="inputForm.mapperClass"
					  placeholder="请填写规则mapper方法"
				  ></el-input>
			  </el-form-item>
		  </el-col>
		  <el-col :span="24">
			  <el-form-item label="规则类型" prop="ruleType" :rules="[]">
			  <el-select v-model="inputForm.ruleType" placeholder="请选择规则类型">
				  <el-option label="自己可见" value="self"/>

				  <el-option label="自己机构可见" value="self_org"/>
				  <el-option label="同级机构可见" value="same_org"/>
				  <el-option label="自己机构和机构下级可见" value="lower_org"/>

				  <el-option label="自己部门可见" value="self_office"/>
				  <el-option label="同级部门可见" value="same_office"/>
				  <el-option label="自己部门和部门下级可见" value="lower_office"/>

				  <el-option label="自定义" value="custom"/>
			  </el-select>
			  </el-form-item>
		  </el-col>

        <el-col :span="24">
          <el-form-item label="规则字段" prop="ruleField" :rules="[]">
            <el-input
              v-model="inputForm.ruleField"
              placeholder="请填写规则字段"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="规则条件" prop="ruleCondition" :rules="[]">
			  <el-select v-model="inputForm.ruleCondition" placeholder="请选择条件">
				  <el-option label="等于" value="eq"/>
				  <el-option label="不等于" value="ne"/>
				  <el-option label="like" value="like"/>
				  <el-option label="not like" value="notlike"/>
				  <el-option label="大于" value="gt"/>
				  <el-option label="大于等于" value="ge"/>
				  <el-option label="小于" value="lt"/>
				  <el-option label="小于等于" value="le"/>
				  <el-option label="between" value="between"/>
				  <el-option label="not between" value="notbetween"/>
			  </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="规则值" prop="ruleValue" :rules="[]">
            <el-input v-model="inputForm.ruleValue" placeholder="请填写规则值"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="自定义sql" prop="customSql" :rules="[]">
            <el-input
              v-model="inputForm.customSql"
              placeholder="请填写自定义sql"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="doSubmit" :loading="loading" v-if="submitButtonHidden">确 定</el-button>
    </template>
  </sc-dialog>
</template>

<script>
export default {
  props: {
    //刷新列表回调函数
    refreshList: { type: Function },
  },
  data() {
    return {
      //表单标题
      title: "",
      //页面加载中
      loading: false,
      //输入表单
      inputForm: {
        id: "",
        sysMenu: {
          id: "",
        },
        ruleName: "",
        mapperClass: "",
        ruleField: "",
        ruleCondition: "",
        ruleValue: "",
        customSql: "",
        sysPlatform: {
          id: "",
        },
      },
      //页面展示
      visible: true,
      //提交按钮显示
      submitButtonHidden: true,
    };
  },
  mounted() {},
  methods: {
    /*
     * 添加视图
     * @author: 路正宁
     * @date: 2023-03-23 16:28:53
     */
    addView(sysMenu) {
      //设置标题
      this.title = `新建数据权限`;
      //字段重置
      this.$refs.inputForm.resetFields();
      //初始化所属菜单
      this.inputForm.sysMenu = sysMenu;
      //显示页面
      this.visible = true;
      //禁用加载
      this.loading = false;
      //显示提交按钮
      this.submitButtonHidden = true;
    },
    /*
     * 编辑视图
     * @author: 路正宁
     * @date: 2023-03-23 16:29:05
     */
    async editView(sysDataRule) {
      //设置标题
      this.title = `修改数据权限`;
      //显示页面
      this.visible = true;
      //启用加载
      this.loading = true;
      //重新查询数据权限
      var res = await this.$API.sysDataRulesService.queryById(sysDataRule.id);
      if (res.code == 200) {
        this.inputForm = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      //禁用加载
      this.loading = false;
      //显示提交按钮
      this.submitButtonHidden = true;
    },
    /*
     * 查看视图
     * @author: 路正宁
     * @date: 2023-03-23 16:33:17
     */
    async view(sysDataRule) {
      //设置标题
      this.title = `查看数据权限`;
      //显示页面
      this.visible = true;
      //启用加载
      this.loading = true;
      //重新查询数据权限
      var res = await this.$API.sysDataRulesService.queryById(sysDataRule.id);
      if (res.code == 200) {
        this.inputForm = res.data;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      //禁用加载
      this.loading = false;
      //隐藏提交按钮
      this.submitButtonHidden = false;
    },
    /*
     * 提交表单
     * @author: 路正宁
     * @date: 2023-03-23 16:35:17
     */
    async doSubmit() {
      //表单校验
      if ((await this.$refs.inputForm.validate()) == false) {
        return;
      }
      this.loading = true;
      //保存数据权限接口
      var res = await this.$API.sysDataRulesService.save(this.inputForm);
      if (res.code == 200) {
        this.$message.success("保存成功");
        this.refreshList();
        this.visible = false;
        this.inputForm = res.data;
      } else {
        this.visible = true;
        this.$Response.errorNotice(res, "保存失败");
      }
      this.loading = false;
    },
  },
};
</script>

<style></style>
