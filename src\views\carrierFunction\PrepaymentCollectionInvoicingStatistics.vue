<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form">
                <el-form-item label="客户名称" prop="companyId" style="width: 240px">
                    <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择客户名称" @change="handleQuery">
                        <el-option v-for="(dict,index) in ownerList" :key="index" :label="dict.companyName" :value="dict.companyId" />
                    </el-select>
                </el-form-item>
                <el-form-item label="收款公司" prop="receiveCompany" style="width: 240px">
                    <el-select v-model="queryParams.receiveCompany" clearable filterable placeholder="请选择收款公司" @change="handleQuery">
                        <el-option v-for="(item, index) in receiveCompanyList" :key="index" :label="item.name" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item class="date-screening" label="汇款时间" prop="remittanceTime" style="width: 305px">
                    <el-date-picker v-model="queryParams.remittanceTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item v-show="isShowAll" class="date-screening" label="开票日期" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <div class="d__flex__statisticsRows">
                <el-statistic :precision="2" :value="summaryStatistics.remitMoney" :value-style="{ color: '#5670FE' }" group-separator="," title="预付总金额"></el-statistic>
                <el-statistic :precision="2" :value="summaryStatistics.rebateMoney" :value-style="{ color: '#11caca' }" group-separator="," title="返利总金额"></el-statistic>
                <el-statistic :precision="2" :value="summaryStatistics.accountMoney" :value-style="{ color: '#fea700' }" group-separator="," title="记账总金额"></el-statistic>
                <el-statistic :precision="2" :value="summaryStatistics.rebateRate" :value-style="{ color: '#3dc726' }" group-separator="," suffix="%" title="返利占比"></el-statistic>
            </div>
        </el-card>
        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 16px">
                <el-button :disabled="dataList.length == 0" icon="el-icon-download" size="mini" type="warning" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" :tableID="'PrepaymentCollectionInvoicingStatistics'" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="PrepaymentCollectionInvoicingStatistics" ref="ColumnTable" v-loading="loading" :columns="columns" :data="dataList" :show-summary="true">
                <template #invoiceType="{ row }">
                    <span>{{ row.invoiceType ? selectDictLabel(invoiceTypeDictionary, row.invoiceType) : '' }}</span>
                </template>
                <template #receiveCompany="{ row }">
                    <span>{{ row.receiveCompany ? selectDictLabel(receiveCompanyList, row.receiveCompany) : '' }}</span>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import moment from 'moment';
import dataQuery from '@/api/carrierEnd/dataQuery.js';
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'PrepaymentCollectionInvoicingStatistics',
    components: {
        SearchButton,
        ColumnTable,
        RightToolbar
    },
    data() {
        return {
            showSearch: true,
            isShowAll: false,
            loading: false,
            queryParams: {
                current: 1,
                size: 10,
                businessType: '1',
                receiveCompany: null, // 收款公司
                companyId: null, // 货主id
                remittanceTime: [], // 汇款时间
                queryTime: [] // 开票日期
            },
            dataList: [],
            columns: [
                { title: '客户名称', minWidth: '220px', align: 'left', key: 'companyName', columnShow: true, showOverflowTooltip: true },
                { title: '汇款时间', width: '180px', align: 'center', key: 'remitTime', columnShow: true },
                { title: '预付金额', width: '100px', align: 'center', key: 'remitAmount', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '返利金额', width: '100px', align: 'center', key: 'rebateAmount', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '记账金额', width: '100px', align: 'center', key: 'accountAmount', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款公司', minWidth: '220px', align: 'center', key: 'receiveCompany', columnShow: true, showOverflowTooltip: true },
                { title: '发票类型', width: '90px', align: 'center', key: 'invoiceType', columnShow: true },
                { title: '发票号码', minWidth: '220px', align: 'center', key: 'invoiceNo', columnShow: true },
                { title: '开票日期', width: '160px', align: 'center', key: 'invoiceDate', columnShow: true }
            ],
            shortcuts: setDatePickerShortcuts(),
            summaryStatistics: {
                remitMoney: 0, // 预付总金额
                rebateMoney: 0, // 返利总金额
                accountMoney: 0, // 记账总金额
                rebateRate: 0 // 返利占比
            }, // 汇总统计
            ownerList: [], // 货主
            receiveCompanyList: [], // 收款公司
            invoiceTypeDictionary: [] // 发票类型
        };
    },
    created() {
        let now = moment(new Date()).format('YYYY-MM-DD');
        this.queryParams.remittanceTime = [now, now];
        this.getDict();
        this.handleQuery();
        this.getCompanySelect();
    },
    methods: {
        // 获取货主公司下拉
        getCompanySelect() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                if (response.code === 200 && response.data) {
                    this.ownerList = response.data;
                }
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.invoiceTypeDictionary = await this.getDictList('collaborating_shipper_invoice_type'); // 发票类型
            this.receiveCompanyList = await this.getDictList('signing_company'); // 收款公司
        },
        /**
         * 切换查询显示
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 重置查询
         */
        resetQuery() {
            this.resetForm('queryForm');
            let now = moment(new Date()).format('YYYY-MM-DD');
            this.queryParams = {
                current: 1,
                size: 10,
                businessType: '1',
                receiveCompany: null, // 收款公司
                companyId: null, // 货主id
                remittanceTime: [now, now], // 汇款时间
                queryTime: []
            };
            this.handleQuery();
        },
        /**
         * 查询
         * @param e
         */
        handleQuery() {
            const { queryTime, remittanceTime } = this.queryParams;
            if (queryTime != undefined && queryTime.length != 0 && queryTime[0] != 'Invalid Date') {
                this.queryParams.startInvoiceDate = queryTime[0] + ' 00:00:00';
                this.queryParams.endInvoiceDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.startInvoiceDate = null;
                this.queryParams.endInvoiceDate = null;
            }
            if (remittanceTime != undefined && remittanceTime.length != 0 && remittanceTime[0] != 'Invalid Date') {
                this.queryParams.startPayDate = remittanceTime[0] + ' 00:00:00';
                this.queryParams.endPayDate = remittanceTime[1] + ' 23:59:59';
            } else {
                this.queryParams.startPayDate = null;
                this.queryParams.endPayDate = null;
            }
            this.queryParams.current = 1;
            this.getList();
            this.getInvoicMoney();
        },
        /**
         * 请求列表API
         */
        getList() {
            this.loading = true;
            const { queryTime, remittanceTime, ...params } = this.queryParams;
            dataQuery
                .getAdvanceInvoiceRecordPage(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.loading = false;
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    } else {
                        this.loading = false;
                    }
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        /**
         * 预付款收款开票记录-合计金额计算
         */
        getInvoicMoney() {
            this.summaryStatistics = {
                remitMoney: 0, // 预付总金额
                rebateMoney: 0, // 返利总金额
                accountMoney: 0, // 记账总金额
                rebateRate: 0 // 返利占比
            }; // 汇总统计
            const { current, size, queryTime, remittanceTime, ...params } = this.queryParams;
            dataQuery.getInvoicMoney(params).then((res) => {
                if (res.code === 200) {
                    this.summaryStatistics = res.data;
                }
            });
        },
        /**
         * 导出
         */
        handleExport() {
            const { current, size, queryTime, remittanceTime, ...params } = this.queryParams;
            dataQuery.exporInvoiceRecord({ filename: '预付款收款开票统计列表.xls', ...params }, '', '', 'blob').then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '预付款收款开票统计列表.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                    this.msgSuccess('预付款收款开票统计列表导出任务已生成！');
                } else {
                    this.msgError('导出异常请联系管理员');
                }
            });
        }
    }
};
</script>

<style scoped>
.d__flex__statisticsRows {
    display: flex;
    justify-content: space-around;
}
.el-statistic {
    display: flex;
    align-items: baseline;
    margin-right: 20px;
    gap: 10px;
}
</style>
