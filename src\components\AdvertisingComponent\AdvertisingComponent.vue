<template>
<div class="ad-box" :class="open?'':'ad-box-close'">
  <el-carousel height="600px" :interval="5000" arrow="never">
	  <el-carousel-item style="background: #E8F0FD;">
		  <div class="advertising-item">
			  <h2>Join Us 天囤诚邀您成为我司战略级合作伙伴</h2>
			  <div class="advertising-content">
				  <p>甘肃天囤医药有限公司专注医药第三方一体化建设，致力于为客户提供“仓库+仓内管理+物流配送”的全链条、一站式服务。</p>
				  <p><span class="font-bold">委托仓库价格优惠，</span>对首次委托仓库及长期合作客户，提供各项费用减免措施，并承诺除必要的成本调控外，合同期内坚决履行约定价格，不涨价，不设消费陷阱，给您始终如一的服务体验。</p>
				  <p>期待与您长期合作！</p>
				  <br>
				  <p>VIP-LINE:</p>
				  <p><span class="font-bold">吕经理 13893344305</span></p>
				  <p><span class="font-bold">王经理 17794276183</span></p>
			  </div>
		  </div>
	  </el-carousel-item>
    <el-carousel-item style="background: #E5E7FF;">
      <div class="advertising-item">
        <img src="./images/ad_1.png" class="ad-img">
      </div>
    </el-carousel-item>
  </el-carousel>
  <div class="close-box" @click="onClose">
    <el-icon size="32">
      <CircleCloseFilled color="#cdcdcd"/>
    </el-icon>
  </div>
</div>
</template>

<script>
import {CircleCloseFilled} from '@element-plus/icons-vue'

export default {
  name: "AdvertisingComponent",
  components: {
    CircleCloseFilled
  },
  props: {
    adOpen: {
      required: true,
      type: Boolean
    },
    openDelay: {
      required: true,
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      open: false,
    }
  },
  created() {
    this.open = this.adOpen;
  },
  methods: {
    onClose() {
      this.open = false;
      this.$emit('close');
    },
  }
}
</script>

<style scoped lang="scss">
.ad-box{
  width: 400px;
  height: 600px;
  position: fixed;
  right: 0;
  bottom: 0;
  animation-name: slideUp; /* 定义动画名称 */
  animation-duration: 2s; /* 定义动画持续时间 */
}
@keyframes slideUp {
  0% { bottom: -600px; } /* 初始状态位于原点 */
  100% { bottom: 0; } /* 结束状态移至上方 */
}
.ad-box-close{
  bottom: -600px;
  animation-name: slideDown; /* 定义动画名称 */
  animation-duration: 2s; /* 定义动画持续时间 */
}
@keyframes slideDown {
  0% { bottom: 0; } /* 初始状态位于原点 */
  100% { bottom: -600px; } /* 结束状态移至上方 */
}
::v-deep {
  .el-carousel__item:nth-child(1){
    height: 600px;
    overflow-y: scroll;
    .advertising-item{
      height: auto;
    }
  }
  // 滚动窗口底部的指示器
  .el-carousel__indicators li button{
    height: 20px;
    background: #003;
    color: #ffffff;
    width: 60px;
  }
  .el-carousel__indicators li:nth-child(1) button::after {
    content: "1";
  }

  .el-carousel__indicators li:nth-child(2) button::after {
    content: "2";
  }
}
.close-box {
  position: absolute;
  right: 3px;
  top: 3px;
}

.advertising-item {
  padding: 20px;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;

  .ad-img {
    width: 100%;
  }
  h2{
    font-weight: 700;
    font-style: normal;
    color: #02A7F0;
    text-align: center;
    font-size: 24px;
  }
  .advertising-content{
    margin-top: 20px;
    p{
      font-size: 14px;
      line-height: 1.5;
      text-indent: 2em;
      margin-bottom: 5px;
      font-weight:400;
      color:#555555;
      .text-red{
        color: #A30014;
        font-weight:400;
      }
		.font-bold{
			font-weight: 700;
		}
    }
    img{
      width: 100%;
      margin-bottom: 10px;
    }
  }

}

</style>
