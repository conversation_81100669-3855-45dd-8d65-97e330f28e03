import request from '@/utils/request';
export default {
    // 按集货区显示
    getCustomerByArea(params) {
        return request.get('/tms/receipt/areaGoodsDetail/keyAccount/area/list', params);
    },
	// 按运单显示
	getCustomerByWaybill(params) {
        return request.get('/tms/receipt/areaGoodsDetail/keyAccount/trans/list', params);
    },
	// 运单按省市区分组未分拣订单列表
	getCustomerByWaybillNoSort(params) {
        return request.get('/tms/orderDrug/keyAccount/groupAreaList', params);
    },
	// 查询承运商合作的大客户列表
	getCustomerByCarrier(params) {
        return request.get('/tms/cooperate/company/keyAccountList', params);
    },
	// 大客户订单上架
	putCustomerByWaybill(params) {
		return request.post('/tms/receipt/areaGoodsDetail/keyAccount/sorting', params);
	},
	// 集货区列表
	getBranchCollectAreaList(params) {
		return request.get('/tms/area/getBranchCollectAreaList', params);
	},
	// 大客户订单下架
	putCustomerByWaybillDown(params) {
		return request.get('/tms/receipt/areaGoodsDetail/keyAccount/unsorting', params);
	}
};
