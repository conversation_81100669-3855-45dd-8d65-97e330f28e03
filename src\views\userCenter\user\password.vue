<template>
    <el-card shadow="never" header="修改密码">
        <el-alert title="密码更新成功后，您将被重定向到登录页面，您可以使用新密码重新登录。" type="info" show-icon style="margin-bottom: 15px" />
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" style="margin-top: 20px">
            <el-form-item label="当前密码" prop="userPassword">
                <el-input v-model="form.userPassword" type="password" show-password placeholder="请输入当前密码"></el-input>
                <div class="el-form-item-msg">必须提供当前登录用户密码才能进行更改</div>
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword">
                <el-input v-model="form.newPassword" type="password" show-password placeholder="请输入新密码"></el-input>
                <sc-password-strength v-model="form.newPassword"></sc-password-strength>
            </el-form-item>
            <el-form-item label="确认新密码" prop="confirmNewPassword">
                <el-input v-model="form.confirmNewPassword" type="password" show-password placeholder="请再次输入新密码"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="save(formRef)">保存密码</el-button>
            </el-form-item>
        </el-form>
    </el-card>
</template>

<script setup>
import scPasswordStrength from '@/components/scPasswordStrength';
import erpUserCenter from '@/api/erp/user/erpUserCenter';
import { getCurrentInstance, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import md5 from 'js-md5';
import router from '@/router';

const { proxy } = getCurrentInstance();
proxy.$md5 = md5;
const formRef = ref();
const form = reactive({
    userPassword: '',
    newPassword: '',
    confirmNewPassword: ''
});
const rules = reactive({
    userPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
    newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        // 要求必须包含字母、数字、特殊字符，且长度不能小于8位
        { pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/, message: '密码必须包含字母、数字、特殊字符，且长度不能小于8位', trigger: 'blur' }
    ],
    confirmNewPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                if (value !== form.newPassword) {
                    callback(new Error('两次输入密码不一致'));
                } else {
                    callback();
                }
            }
        }
    ]
});

const save = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            var params = {
                oldPwd: proxy.$md5(form.userPassword),
                newPwd: proxy.$md5(form.newPassword)
            };
            console.log(params);
            erpUserCenter.updatePassword(params).then(async (res) => {
                if (res.code == 200) {
                    ElMessage({
                        message: '修改密码成功',
                        type: 'success'
                    });
                    router.push('/login');
                    // await erpUserCenter.logOut.then(res => {
                    // 	if(res.code == 200) {
                    //         router.push('/login')
                    // 	}
                    // })
                } else {
                    proxy.msgError(res.msg);
                }
            });
        }
    });
};
</script>

<style></style>
