import request from "@/utils/request";

export default {
	labelView(params,progressEvent) {
		let config = {
			headers:{ "X-Requested-With" :  "XMLHttpRequest"},
			onDownloadProgress: progressEvent
		}
		return request.get(
			'/printer/printLabel/view',
			params,
			config,
			false,
			'blob'
		);
	},
	printPdfWithData(data,progressEvent){
		let config = {
			headers:{ "X-Requested-With" : "XMLHttpRequest" },
			responseType: 'blob',
			onDownloadProgress: progressEvent
		}
		return request.post('/printer/printLabel/printPdfWithData',data,config,false)
	},
	printLabelViewNew(data,progressEvent){
		let config = {
			headers:{ "X-Requested-With" : "XMLHttpRequest" },
			responseType: 'blob',
			onDownloadProgress: progressEvent
		}
  		return request.post('/printer/printLabel/viewNew',data,config,false)
	},
	getDeviceData(data,progressEvent){
		let config = {
			headers:{ "X-Requested-With" : "XMLHttpRequest" },
			responseType: 'blob',
			onDownloadProgress: progressEvent
		}
  		return request.get('/fourPL/app/driver/device/deviceData',data,config,false)
	},
	batchPrintPdfWithDataUrl(data,progressEvent){
		let config = {
			headers:{ "X-Requested-With" :  "XMLHttpRequest"},
			onDownloadProgress: progressEvent
		}
  		return request.get('/printer/printLabel/printPdfWithDataUrl',data,config,false,'blob')
	},
	// 生成多个保温箱码
	printLabel: function (data,progressEvent) {
		let config = {
			headers:{ "X-Requested-With" :  "XMLHttpRequest"},
			responseType: 'blob',
			onDownloadProgress: progressEvent
		}
		return request.post('/printer/printLabel/views', data,config,false);
	},
}


// export function labelView(params, progressEvent) {
//   return request({
//     headers: { 'X-Requested-With': 'XMLHttpRequest' },
//     url: '/printer/printLabel/view',
//     method: 'get',
//     params: params,
//     responseType: 'blob',
//     onDownloadProgress: progressEvent
//   })
// }
//
// export function printLabelViewNew(data,progressEvent){
//   return request({
//     headers: { 'X-Requested-With': 'XMLHttpRequest' },
//     responseType: 'blob',
//     onDownloadProgress: progressEvent,
//     url: '/printer/printLabel/viewNew',
//     method: 'post',
//     data: data
//   })
// }
// export function printPdfWithData(data) {
//   return request({
//     url: '/printer/printLabel/printPdfWithData',
//     method: 'post',
//     data: data,
//     responseType: 'blob'
//   })
// }
// // 打印预览
// export function printPreview(params, progressEvent) {
//   return request({
//     url: '/printer/xmlTemplate/printer',
//     method: 'get',
//     params: params,
//     responseType: 'blob',
//     onDownloadProgress: progressEvent
//   })
// }
