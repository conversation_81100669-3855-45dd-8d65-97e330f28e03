<template>
    <div>
        <!-- 搜索 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item label="商品名称" prop="commodityCommonName">
                    <el-input v-model="queryParams.commodityCommonName" placeholder="请输入商品名称" clearable class="form_225" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchQuery">搜索</el-button>
                    <el-button @click="resetQuery(queryForm)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(form)" class="creatSpan">上传</el-button>
            <el-button @click="downLoad" class="creatSpan">下载模板</el-button>
            <RightToptipBarV2 @handleRefresh="getList" className="spillDeclaration" style="float:right;margin-top:10px" />
            <DragTableColumn :columns="columns" :tableData="spillList" className="spillDeclaration"
                v-model:queryParams="queryParams" :getList="getList">

            </DragTableColumn>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 上传的弹框 -->
        <el-dialog v-model="dialogFormVisible" width="30%" title="报溢单导入">
            <el-upload v-model:file-list="fileList" class="upload-demo" :limit="1" :action="uploadUrl" :on-preview="handlePreview"
                :before-remove="beforeRemove" :on-exceed="(files) => handleExceed(files, upload)"  ref="upload" :headers='headers'
                :on-success="(res, file, filList) => handleUploadSuccess(res, file, filList, index)"
                :on-progress="uploadVideoProcess">
                <el-button type="primary" class="upload-button">
                    <el-icon>
                        <UploadFilled />
                    </el-icon>
                    上传
                </el-button>
            </el-upload>
            <div style="margin-left:30px;" v-if="uploadDiv">
                <p class="upload-p">导入完成，成功导入{{ uploadSucceed }}条，导入失败{{ uploadFail }}条!</p>
                <div v-if="uploadDiv2">
                    <p>错误内容：</p>
                    <p style="margin:20px 0px 20px 40px;">{{ infoValue }}</p>
                </div>
            </div>
            <div class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary" @click="creat">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import tool from '@/utils/tool';
import { UploadFilled } from '@element-plus/icons-vue';
import spillDeclaration from '@/api/erp/takeStock/spillDeclaration'
import { genFileId } from 'element-plus'
import { ElMessage } from "element-plus";
const { proxy } = getCurrentInstance();
const videoUploadPercent = ref()
const queryParams = ref({
    current: 1,
    size: 10,
});
const queryForm = ref()
const total = ref(0)
const uploadDiv = ref(false)
const uploadDiv2 = ref(false)
const loading = ref(false)
const fileList = ref([])
const resultValue = ref('')
const itemcount = ref(0)
const failInfo = ref([])
const upload = ref()
const infoValue = ref('')
const spillList= ref([])
const uploadUrl = '/erp/stock/inventory/erpOverflowReport/import'
const uploadSucceed = ref(0)
const uploadFail = ref(0)
const dialogFormVisible = ref(false)
const columns = ref(
    [
        {
            label: '商品自编码',
            prop: 'commoditySelfCode'
        }, {
            label: '商品编码',
            prop: 'commodityCode'
        }, {
            label: '货主名称',
            prop: 'shipownerName'
        }, {
            label: '商品名称',
            prop: 'commodityCommonName'
        }
        , {
            label: '规格',
            prop: 'commodityPackageSpecification'
        }, {
            label: '生产厂家',
            prop: 'commodityManufactureName'
        }, {
            label: '产地',
            prop: 'commodityProducingArea'
        }, {
            label: '单位',
            prop: 'commodityBasicUnit'
        }, {
            label: '批号',
            prop: 'batchNumber'
        },

        {
            label: '生产日期',
            prop: 'commodityManufactureDate',
            type: 'date'
        },
        {
            label: '有效期',
            prop: 'commodityValidityTime',
            type: 'date'
        }, {
            label: '批准文号',
            prop: 'approvalNumber'
        }, {
            label: '剂型',
            prop: 'commodityDosageForm'
        }, {
            label: '仓库',
            prop: 'warehouse',
        },
        {
            label: '库号',
            prop: 'warehouseNumber'
        },
        {
            label: '货位',
            prop: 'goodsShelves',
        }, {
            label: '报溢数量',
            prop: 'overflowQauntity'
        }, {
            label: '报溢日期',
            prop: 'overflowDate',
            type: 'date'
        }, {
            label: '报溢原因',
            prop: 'overflowReason'
        }, {
            label: '成本单价',
            prop: 'costUnitPrice'
        }, {
            label: '报溢金额',
            prop: 'overflowAmount'
        }, {
            label: '备注',
            prop: 'remark'
        },
    ]
)
function handleAdd() {
    dialogFormVisible.value = true
    uploadDiv.value = false
    fileList.value = []
    uploadSucceed.value = 0
    uploadFail.value = 0
    itemcount.value = 0
};
function uploadVideoProcess(event, file, fileList) {
    videoUploadPercent.value = Math.floor(event.percent);
}
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
// 超出限制时触发
const handleExceed = (files, ref) => {
    ref.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    ref.handleStart(file)
    ref.submit()
}
//文件上传成功
const handleUploadSuccess = (res, file, fileList, index, type) => {
    if (res.code == 200) {
        var result = JSON.parse(res.data)
        fileList.value = [{ ...res.data }]
        uploadDiv.value = true
        uploadDiv2.value = false
        var info = [result.info.split(",").join(',')]
        failInfo.value.push(info)
        infoValue.value = failInfo.value[failInfo.value.length - 1].join(',')
        uploadSucceed.value = 0
        uploadFail.value = 0
        uploadSucceed.value += Number(result.successNum)
        uploadFail.value += Number(result.failNum)
        if (result.result == '失败') {
            resultValue.value = result.result
            uploadDiv2.value = true
            itemcount.value += 1
        }
    } else {
        uploadDiv2.value = false
        proxy.msgError(res.msg)
    }
}
// 点击已经上传的文件触发
const handlePreview = (uploadFile) => {
    console.log(uploadFile)
}
// 重置
function resetQuery(formEl) {
    formEl.resetFields()
    getList()
}
//搜索
const searchQuery = (row) => {
    getList()
}
// 确定按钮
const creat = () => {
    if (resultValue.value) {
        ElMessage({
            message: "保存失败",
            type: "error",
        });
    } else {
        dialogFormVisible.value = false
        getList()
        ElMessage({
            message: "保存成功",
            type: "success",
        });
    }

}
// 删除之前触发
const beforeRemove = (uploadFile, uploadFiles) => {
    return proxy.$confirm(`是否确认删除${uploadFile.name}?`, '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        if (uploadSucceed.value >= 1) {
            uploadSucceed.value -= 1
        }
        if (uploadFail.value >= 1) {
            uploadFail.value -= 1
        }
        uploadDiv2.value = false
    }).catch(() => { });
}
//报损单列表
function getList() {
    loading.value = true
    spillDeclaration.list({ ...queryParams.value }).then(res => {
        if (res.code == 200) {
            spillList.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}
// 下载按钮
const downLoad = () => {
    spillDeclaration.downLoad().then(res => {
        proxy.download(res, "application/vnd.ms-excel", '报溢单导入模板.xls')
    })
}
getList()
</script>

<style scoped lang='scss'>
::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.el-table {
    margin-top: 20px;
}

::v-deep .el-upload-list__item {
    width: 350px;
    position: relative;
    left: 120px;
    top: -40px;
    margin: 10px 0px;

}

::v-deep .el-progress-bar {
    position: relative;
    top: -90px
}

::v-deep .el-progress__text {
    position: relative;
    top: -90px;
    left: 15px
}


.upload-button {
    margin-left: 30px;

}

.upload-p {
    display: flex;
    // justify-content: end;
    justify-content: center;
    margin: 20px 0px;
}

.dialog-footer {
    display: flex;
    justify-content: end;
}
</style>
