<template>
    <div class="app-container customer-auto-height-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10 customer-auto-height-card" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.native.prevent>
                <el-form-item label="单号" prop="orderNo">
                    <el-input v-model="queryForm.orderNo" placeholder="请输入单号" clearable @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="日期时段" prop="datePeriod" style="width: 320px">
                    <el-date-picker v-model="queryForm.datePeriod" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <el-form-item label="充值方式" prop="paymentType">
                    <el-select v-model="queryForm.paymentType" clearable filterable placeholder="请选择充值方式" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in rechargeMethodList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="交易类型" prop="dealType" v-show="isShowAll">
                    <el-select v-model="queryForm.dealType" clearable filterable placeholder="请选择充值方式" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in transactionTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px', display: 'flex', flexDirection: 'column', height: '100%' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button :disabled="total === 0" type="primary" @click="handleExport">导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="CustomerPrepaymentBalanceDetailShipperEnd" @queryTable="getList" />
            </div>
            <column-table ref="CustomerPrepaymentBalanceDetailShipperEnd" :columns="columns" :data="dataList" class="customer-auto-height-table" max-height="null" :show-index="true">
                <template #dealType="{ row }">
                    <span>{{ formatDictionaryData('transactionTypeList', row.dealType) }}</span>
                </template>
                <template #paymentType="{ row }">
                    <span>{{ formatDictionaryData('rechargeMethodList', row.paymentType) }}</span>
                </template>
            </column-table>

            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" class="mb16" @pagination="getList" />
            </div>
        </el-card>
    </div>
</template>

<script>
import moment from 'moment/moment';
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar';
import customerPrepayment from '@/api/logisticsConfiguration/customerPrepayment.js';
import { setDatePickerShortcuts } from '@/utils/config-store';
import SearchButton from '@/components/searchModule/SearchButton.vue';

export default {
    name: 'CustomerPrepaymentBalanceDetailShipperEnd',
    components: { ColumnTable, RightToolbar, SearchButton },
    data() {
        return {
            showSearch: true,
            total: 0,
            loading: false,
            queryForm: {
                current: 1,
                size: 10,
                // 预付款账户id
                advancePaymentId: null,
                datePeriod: [],
                startDate: null,
                endDate: null,
                orderNo: null,
                paymentType: null,
                dealType: null
            },
            columns: [
                { title: '单号', key: 'orderNo', align: 'center', minWidth: '180px', columnShow: true, fixed: 'left' },
                { title: '承运商名称', key: 'carrierName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '日期', key: 'createDate', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易类型', key: 'dealType', align: 'center', minWidth: '140px', columnShow: true },
                { title: '付款方式', key: 'paymentType', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易前金额', key: 'dealBeforeAmount', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易金额', key: 'dealAmount', align: 'center', minWidth: '140px', columnShow: true },
                { title: '交易后余额', key: 'dealAfterAmount', align: 'center', minWidth: '140px', columnShow: true },
                { title: '操作员', key: 'createBy', align: 'center', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true }
            ],
            dataList: [],
            rechargeMethodList: [],
            transactionTypeList: [],
            shortcuts: setDatePickerShortcuts(),
            isShowAll: false
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                return this.selectDictLabel(this[dictionaryName], value) || value || '--';
            };
        },
        /**
         * 格式化时间
         */
        formatDate() {
            return (value) => {
                if (!value) {
                    return '-';
                }
                return moment(value).format('YYYY-MM-DD');
            };
        }
    },
    activated() {
        if (this.$route.query && !this.$route.query.advancePaymentId) {
            this.queryForm.advancePaymentId = this.query.advanceId;
            this.getList();
        }
    },
    async created() {
        // 交易类型 字典
        this.rechargeMethodList = await this.getDictList('fourpl_payment_type');
        // 交易类型 字典
        this.transactionTypeList = await this.getDictList('fourpl_deal_type');
        if (this.$route.query) {
            this.query = this.$route.query;
            if (this.$route.query.advanceId) {
                this.queryForm.advancePaymentId = this.$route.query.advanceId;
                this.getList();
            }
        }
    },
    methods: {
        getList() {
            this.loading = true;
            let params = { ...this.queryForm };
            delete params.datePeriod;
            customerPrepayment
                .listAdvancePaymentDetail(params)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    } else {
                        this.dataList = [];
                        this.total = 0;
                    }
                    this.loading = false;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleQuery() {
            this.queryForm.current = 1;
            const { datePeriod } = this.queryForm;
            if (datePeriod && datePeriod.length) {
                this.queryForm.startDate = datePeriod[0];
                this.queryForm.endDate = datePeriod[1];
            } else {
                this.queryForm.startDate = null;
                this.queryForm.endDate = null;
            }
            this.getList();
        },
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.handleQuery();
        },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 导出交易明细
         */
        handleExport() {
            const params = {
                advancePaymentId: this.queryForm.advancePaymentId,
                startDate: this.queryForm.startDate,
                endDate: this.queryForm.endDate,
                dealType: this.queryForm.dealType,
                paymentType: this.queryForm.paymentType
            };
            customerPrepayment.advancePaymentDetailExport({ filename: '预付款交易明细.xls', ...params }, '', '', 'blob').then((res) => {
                var debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '预付款列表.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    this.$message.error('导出异常请联系管理员');
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
</style>
