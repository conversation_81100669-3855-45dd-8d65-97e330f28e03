import request from '@/utils/request';
export default {
    // 获取计费参数列表
    billingParameterListCarrier: function (params) {
        return request.get('/cost/carrier/billingParameters/list', params);
    },
    // 查询未新增的参数列表
    getBillingParamList: function (params) {
        return request.get('/cost/carrier/billingParameters/selectAddData', params);
    },
    // 批量新增
    addBillingParameterCarrier: function (params) {
        return request.post('/cost/carrier/billingParameters/branchAdd', params);
    },
	// 状态切换
	changeStatus: function (params) {
        return request.get('/cost/carrier/billingParameters/updateStatus', params);
    },
	// 客户列表
	customerList: function (params) {
        return request.get('/cost/carrier/billingParameters/selectCustomer', params);
    },
	// 批量修改
	branchEdit: function (params) {
        return request.post('/cost/carrier/billingParameters/branchEdit', params);
    },
};
