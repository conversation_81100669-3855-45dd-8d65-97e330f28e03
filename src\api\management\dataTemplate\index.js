import http from "@/utils/request"

export default {
  // 查询列表
  tempTempletList: function (params) {
    return http.get('/device/tempTemplet/list', params)
  },
  // 新增
  tempTempletSave: function (data) {
    return http.post("/device/tempTemplet/save", data);
  },
  // 删除
  tempTempletDelete: function (params) {
    return http.delete("/device/tempTemplet/delete", params);
  },
  // 下载上传
  importTemplate: function (params,config,resDetail,responseType) {
    return http.get("/device/tempTempletData/import/template", params,config,resDetail,responseType);
  },
  // 导出子表数据
  tempTempletDataExport: function (params,config,resDetail,responseType) {
    return http.get("/device/tempTempletData/export", params,config,resDetail,responseType);
  },

  // 模版详情列表
  tempTempletDataList: function (params) {
    return http.get('/device/tempTempletData/list', params)
  },
}
