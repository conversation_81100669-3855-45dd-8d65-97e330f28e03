<template>
  <div>
    <el-card class="box-card" style="margin-bottom: 10px">
      <h4 class="el-dialog__title">选择销退入库单据</h4>
      <el-form ref="creatform" :inline="true" :model="searchForm" :rules="creatRules" label-width="90px"
               style="margin-top: 20px">
        <el-form-item label="客户" prop="n1">
          <el-select v-model="searchForm.n1" clearable filterable placeholder="请选择客户"
                     style="width: 150px;">
            <template #empty>
              <p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
                无数据 
              </p>
              <p style="text-align: center">
                <el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
									data.clientType.value =
										'';
									clientList();
								}
									">
                  返回
                </el-button>
              </p>
            </template>
            <el-input v-model="data.clientType.value" placeholder="请输入客户名称Enter键搜索"
                      @keydown.enter="clientList"/>
            <el-option v-for="(item, index) in data.clientType
							.type" :key="index" :label="item.enterpriseName" :value="item.id"/>

          </el-select>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.n2" clearable placeholder="请输入商品名称" style="width: 150px"/>
        </el-form-item>
        <el-form-item label="经手人" prop="n3">
          <el-select v-model="searchForm.n3" clearable filterable placeholder="请输入经手人"
                     style="width: 150px">


            <template #empty>
              <p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
                无数据
              </p>
              <p style="text-align: center">
                <el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
									data.handle.value =
										'';
									handleList();
								}
									">
                  返回
                </el-button>
              </p>
            </template>
            <el-input v-model="data.handle.value" placeholder="请输入客户名称Enter键搜索"
                      @keydown.enter="handleList"/>
            <el-option v-for="(item, index) in data.handle.type" :key="index" :label="item.name"
                       :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="单据编号">
          <el-input v-model="searchForm.n4" clearable placeholder="请输入单据编号" style="width: 150px"/>
        </el-form-item>
        <el-form-item>
          <label id="el-id-2128-46" class="el-form-item__label" for="el-id-2128-55" style="width: 90px;">
            <el-button type="primary" @click="handleQuery(creatform)">查询</el-button>
          </label>
        </el-form-item>
      </el-form>
      <el-button :disabled="!multipleSelection.length" style="margin-bottom: 15px" type="primary" @click="addFn">
        添加
      </el-button>
      <el-table ref="multipleTableRef" v-loading="receiptsFlag" :border="true"
                :data="receiptsList"
                :header-cell-style="{ 'text-align': 'center' }" :row-style="({row})=>{
                  return row.redFlag?{
                    color:'red'
                  }:null
                }"
                style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column :selectabl="selectable" align="center" fixed="left" type="selection" width="55"/>
        <el-table-column align="center" label="单据编号" property="docNum" width="190px"/>
        <el-table-column :sort-method="dateSortMethod" align="center" label="单据创建日期" property="" sortable
                         width="130">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="入库日期" property="inTime" width="120px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.inTime) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="客户" property="customer" width="130px"/>
        <el-table-column align="center" label="商品名称" property="commodity.commonName" width="150px"/>
        <el-table-column align="center" label="自编码" property="commodity.commoditySelfCode" width="120px"/>
        <el-table-column align="center" label="规格" property="commodity.packageSpecification"/>
        <el-table-column align="center" label="生产厂家" property="commodity.manufactureName" width="170px"/>
        <el-table-column align="center" label="产地" property="commodity.producingArea" width="120"/>
        <el-table-column align="center" label="生产日期" property="produceDate" width="120px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="批号" property="batchNumber"/>
        <el-table-column align="center" label="有效期" property="endValiDate" width="120px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.endValiDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" property="commodity.basicUnit"/>
        <el-table-column align="center" label="单价" property="unitPrice"/>
        <el-table-column align="center" label="入库数量" property="inQuantity"/>
        <el-table-column align="center" fixed="right" label="可开票数量" property="invoicableQuantity"
                         width="100"/>
        <el-table-column align="center" fixed="right" label="开票数量" property="" width="150">
          <template #default="scope">
            <el-input-number v-model="scope.row.num"
                             :max="scope.row.invoicableQuantity" :min="0"
                             :precision="0" :step="1"
                             size="small"
                             @change="numChange(scope.$index)"/>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="开票金额" property="allPrice" width="100">
          <template #default="scope">
            {{ scope.row.allPrice.toFixed(2) }}
          </template>
        </el-table-column>

      </el-table>
      <el-pagination v-model:current-page="data.pageNum" v-model:page-size="data.pageSize" :background="true"
                     :disabled="false" :page-sizes="[5, 10, 50, 100]" :small="false" :total="data.total"
                     layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                     @size-change="handleQuery(creatform)" @current-change="handleQuery(creatform)"/>
    </el-card>

    <el-card class="box-card" style="margin-bottom: 10px">
      <h4 class="el-dialog__title">待开票记录</h4>
      <el-button :disabled="multipleSelection2.length==0" style="margin: 15px 0" type="primary" @click="delFn()">
        删除
      </el-button>
      <el-table ref="multipleTableRef2" :border="true"
                :data="chooseData" :header-cell-style="{ 'text-align': 'center' }"
                style="width: 100%"
                @selection-change="handleSelectionChange2">
        <el-table-column align="center" fixed="left" type="selection" width="55"/>
        <el-table-column align="center" label="单据编号" property="docNum" width="190px"/>
        <el-table-column :sort-method="dateSortMethod" align="center" label="单据创建日期" property="createDate"
                         sortable
                         width="130">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="入库日期" property="" width="120px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.inTime) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="客户" property="customer" width="130px"/>
        <el-table-column align="center" label="商品名称" property="commodity.commonName" width="150px"/>
        <el-table-column align="center" label="自编码" property="commodity.commoditySelfCode" width="120px"/>
        <el-table-column align="center" label="规格" property="commodity.packageSpecification"/>
        <el-table-column align="center" label="生产厂家" property="commodity.manufactureName" width="170px"/>
        <el-table-column align="center" label="产地" property="commodity.producingArea" width="120"/>
        <el-table-column align="center" label="生产日期" property="produceDate" width="120px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.produceDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="批号" property="batchNumber"/>
        <el-table-column align="center" label="有效期" property="endValiDate" width="120px">
          <template #default="scope">
            {{ functionIndex.transformTimestamp(scope.row.endValiDate) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" property="commodity.basicUnit"/>
        <el-table-column align="center" label="单价" property="unitPrice"/>
        <el-table-column align="center" label="入库数量" property="inQuantity"/>
        <el-table-column align="center" fixed="right" label="开票数量" property="num"
                         width="100"/>
        <el-table-column align="center" fixed="right" label="开票金额" property="allPrice" width="100">
          <template #default="scope">
            {{ scope.row.allPrice.toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>
      <p class="textStyle" style="margin: 15px 0 0px 0">合计开票金额: <span style="color:red">{{
          formData.n4.toFixed(2)
        }}元</span>
      </p>
    </el-card>
    <el-card class="box-card" style="margin-bottom: 10px">
      <h4 class="el-dialog__title" style="margin-bottom: 10px">发票信息</h4>
      <table border="0" cellpadding="0" cellspacing="1" class="messTable">
        <tr>
          <td>发票客户</td>
          <td>{{ formData.n7 }}</td>
        </tr>
        <tr>
          <td>纳税人识别号</td>
          <td>{{ formData.n6 }}</td>
        </tr>
        <tr>
          <td>开票金额</td>
          <td>{{ formData.n4.toFixed(2) }}元</td>
        </tr>
      </table>
      <el-form ref="creatform2" :model="formData" :rules="creatRules2" label-width="80px"
               style="margin-top: 20px">
        <el-form-item label="发票类型" prop="n5">
          <el-select v-model="formData.n5" class="m-2" placeholder="请选择发票类型">
            <el-option v-for="item in invoiceType" :key="item.value" :label="item.name"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="formData.n1" :rows="3" clearable placeholder="请输入备注" type="textarea"/>
        </el-form-item>
      </el-form>
    </el-card>

    <p style="color:red;margin: 10px 0 0 10px">点击'提交申请'将发起开票申请流程
    </p>
  </div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect} from 'vue';
import {backApi, manageApi} from "@/api/model/salesManagement";
import {ElMessage, ElMessageBox} from "element-plus";
import {applocation} from "@/api/model/invoice";
import {functionIndex} from "../../commodity/functionIndex";

const {proxy} = getCurrentInstance();
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const creatform = ref(); //验证表单/流程新增
const creatRules = reactive({
  n1: [{required: true, message: "请选择客户", trigger: "blur"}],
  n3: [{required: true, message: "请选择经手人", trigger: "blur"}],
});

const creatform2 = ref(); //验证表单/流程新增
const creatRules2 = reactive({
  n5: [{required: true, message: "请选择发票类型", trigger: "blur"}],
});
const dialogVisible = ref(false)
const chooseData = ref([])
const searchForm = ref({
  n1: "",
  n2: "",
  n3: "",
  n4: "",
})
const invoiceType = ref(null)
const formData = ref({
  n1: "", //表单备注
  n2: "", // 客户id
  n3: "",	// 经手人id
  n4: 0, // 合计金额
  n5: '',// 发票类型
  n6: "", //纳税人识别号
  n7: "", //客户名字
})
const data = reactive({
  clientType: {
    value: '',
    type: []
  },
  handle: {
    value: '',
    type: []
  },
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const multipleSelection = ref([])
const multipleTableRef = ref()
const multipleSelection2 = ref([])
const formFlag = ref(false)
const multipleTableRef2 = ref()
const emit = defineEmits([])
const props = defineProps({})
const tableData = ref([])
const receiptsFlag = ref(false)
const receiptsList = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}
const handleSelectionChange2 = (val) => {
  multipleSelection2.value = val
}
const numChange = (ind) => {
  receiptsList.value[ind].allPrice = receiptsList.value[ind].num * receiptsList.value[ind].unitPrice
}
const dateSortMethod = (a, b) => {
  const dateA = new Date(a.date);
  const dateB = new Date(b.date);
  if (dateA < dateB) {
    return -1;
  } else if (dateA > dateB) {
    return 1;
  } else {
    return 0;
  }
}
const savePrice = () => {
  receiptsList.value.forEach((item) => {
    if (item.id == data.getEditInList[0].fatherId) {
      data.getEditInList.forEach(item => {
        item.num = item.beiNum
        item.allPrices = item.num * item.adjustUnitPrice
      })
      let allPrice = 0
      item.invoicingInfo = item.adjustPriceList?.map(items => {
        let price = items.num * items.adjustUnitPrice?.toFixed(2)
        allPrice += price
        return items.adjustUnitPrice?.toFixed(2) + ' * ' + items.num + ' = ' + price.toFixed(2)
      })
      item.allPrice = allPrice
    }
  })
  dialogVisible.value = false
}
const editInList = (row) => {
  data.getEditInList = row.adjustPriceList
  console.log(row)
  data.editInGoods = {
    tradeName: row.commodity.tradeName,
    intoNo: row.intoNo
  }
  data.getEditInList.forEach(item => {
    item.fatherId = row.id
    item.beiNum = item.num
    item.allPrice = item.num * item.adjustUnitPrice
  })
  dialogVisible.value = true
}
const addFn = () => {
  if (multipleSelection.value.length > 0) {
    let flag = false
    let flag2 = false
    let flag3 = false
    formData.value.n4 = 0
    multipleSelection.value.forEach(item => {
      if (!chooseData.value.find(items => items.id == item.id)) {
        if (!chooseData.value.find(items => items.customer !== item.customer || items.handledBy !== item.handledBy)) {
          if (item.allPrice > 0) {
            item.redFlag = false
            item.newList = true
            chooseData.value.push({...item})
          } else {
            item.redFlag = true
            flag3 = true
          }
        } else {
          item.redFlag = true
          flag2 = true
        }
      } else {
        item.redFlag = true
        flag = true
      }
    })
    backApi
        .handleLists({
          size: 1000,
          id: searchForm.value.n1
        })
        .then((res) => {
          if (res.code == 200) {
            formData.value.n6 = res.data.customersDTO.socialCreditCode
            formData.value.n7 = res.data.customersDTO.enterpriseName
          }
        });
    chooseData.value.forEach(item => {
      item.checkFlag = false
      formData.value.n4 += item.allPrice
    })
    if (flag) {
      ElMessage.warning('添加成功，重复的入库记录已过滤')
    }
    if (flag2) {
      ElMessage.warning('添加成功，客户或经手人不一致的入库记录已过滤')
    }
    if (flag3) {
      ElMessage.warning('添加成功，开票金额为 0 的已过滤')
    }
    if (!flag && !flag2 && !flag3) {
      ElMessage.success('添加成功')
    }
  } else {
    ElMessage.error('请先选择')
  }
}
const delFn = () => {
  ElMessageBox.confirm("确认删除此项吗?(删除后不可撤销)", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        let newarr = chooseData.value.filter(
            (item) =>
                !multipleSelection2.value.some(
                    (subItem) => subItem.id === item.id
                )
        );
        chooseData.value = newarr;
        if (chooseData.value.length == 0) {
          for (let key in formData.value) {
            if (key == 'n4') {
              formData.value[key] = 0
            } else {
              formData.value[key] = ''
            }
          }
        }
        formData.value.n4 = 0
        if (chooseData.value.length) {
          chooseData.value.forEach(item => {
            formData.value.n4 += item.allPrice
          })
        }
        ElMessage.success('删除成功')
      })
      .catch(() => {
      });
}
const clientList = () => {
  manageApi
      .clientType({
        current: 1,
        size: 1000,
        enterpriseName: data.clientType.value,
      })
      .then((res) => {
        data.clientType.type = res.data.records;
      });
};
const handleList = () => {
  backApi
      .handleList({
        size: 1000,
        name: data.handle.value
        // sysOrg.id
      })
      .then((res) => {
        if (res.code == 200) {
          data.handle.type = res.data.records;
        }
      });
};
const selectable = (row) => {
  return row.invoicableQuantity > 0
}
const checkFn = (ind) => {
  receiptsList.value[ind].checkFlag = !receiptsList.value[ind].checkFlag
}
const checkFns = (ind) => {
  chooseData.value[ind].checkFlag = !chooseData.value[ind].checkFlag
}
const handleQuery = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      receiptsFlag.value = true
      applocation.getGoodsListOut({
        'erpCustomer.id': searchForm.value.n1,
        tradeName: searchForm.value.n2,
        handledById: searchForm.value.n3,
        docNum: searchForm.value.n4,
        size: data.pageSize,
        busType: 3,
        current: data.pageNum,
        ascoutTime: 1
      }).then(res => {
        if (res.code == 200) {
          receiptsList.value = res.data.records
          receiptsList.value.forEach(item => {
            item.num = item.invoicableQuantity
            item.allPrice = item.num * item.unitPrice
          })
          if (chooseData.value.length > 0) {
            if (!(chooseData.value[0].erpCustomer.id == searchForm.value.n1 && chooseData.value[0].handledById == searchForm.value.n3)) {
              chooseData.value = []
            }
          }
          data.total = res.data.total
          formData.value.n2 = searchForm.value.n1
          formData.value.n3 = searchForm.value.n3
        }
        receiptsFlag.value = false
      })
    }
  });
}
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  clientList()
  handleList()
  invoiceType.value = await proxy.getDictList("invoice_type");
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  creatform2,
  chooseData,
  searchForm,
  receiptsList,
  formData
})
</script>
<style lang='scss' scoped>
.el-select-dropdown__list {
  .el-input {
    width: 90%;
    margin-left: 5%;
    margin-top: 5px;
    margin-bottom: 15px;
  }

  .el-pagination {
    margin-right: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.textStyle {
  font-size: 15px;
  color: #000;
  font-weight: bolder;
}

.messTable {
  width: 100%;
  background-color: #eaedf3;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 1px 1px 0 1px;

  tr {
    margin-bottom: 1px;
    display: flex;

    td {
      background-color: white;
      line-height: 40px;
    }

    td:nth-child(1) {
      flex: 1;
      padding: 0 10px;
      font-weight: bold;
      color: #505050;
      background: #f7f7f7;
    }

    td:nth-child(2) {
      color: #606266;
      padding: 0 10px;
      flex: 2
    }
  }
}
</style>
