<template>
    <div v-loading="loading" class="app-container" element-loading-text="加载中...">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="时间" prop="queryDate">
                    <el-date-picker v-model="queryForm.queryDate" clearable end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"> </el-date-picker>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb-40">
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="InvoiceApplication" @queryTable="getList"></right-toolbar>
            </div>
            <column-table ref="InvoiceApplication" :columns="columns" :data="dataList" :max-height="600" show-summary>
                <template #invoiceType="{ row }">
                    <div class="flex items-center justify-center">
                        <span class="mr-5">{{ formatDictionaryData('invoiceTypeList', row.invoiceType) }}</span>
                        <el-button v-if="row?.invoiceResult == '1'" link size="small" type="primary" @click="onDownloadInvoice(row)">下载</el-button>
                        <span v-else-if="row?.invoiceResult == '0'" class="text-red-500">开票失败</span>
                    </div>
                </template>
                <template #redFlag="{ row }">
                    <span :style="setRedFlagColor(row.redFlag)">{{ formatDictionaryData('redFlagList', row.redFlag) }}</span>
                </template>
                <template #businessType="{ row }">
                    <span :style="setBusinessTypeColor(row.businessType)">{{ formatDictionaryData('businessTypeList', row.businessType) }}</span>
                </template>
                <template #status="{ row }">
                    <span :style="setStatusColor(row.status)">{{ formatDictionaryData('statusList', row.status) }}</span>
                </template>
                <template #opt="{ row }">
                    <div>
                        <!-- 1-预存款充值 2-付款单支付 -->
                        <!-- 审批状态 status 0-待审批 1-审批通过 2-审批驳回 3-作废 -->
                        <!-- 发票状态 invoiceResult 0-开票失败 1-开票成功 -->
                        <el-button v-if="row.status === '2'" icon="el-icon-edit" link size="small" type="warning" @click="onOpenInvoice(row)">修改</el-button>
                        <el-button v-if="row.status === '2'" icon="el-icon-delete" link size="small" type="danger" @click="onDelete(row)">删除</el-button>
                        <el-button v-if="row.status === '1' && row?.invoiceResult == '1' && row?.redFlag === '1'" icon="el-icon-plus" link size="small" type="danger" @click="onOpenRedInvoice(row)">红冲</el-button>
                        <el-button v-if="row.businessType === '2'" icon="el-icon-info-filled" link size="small" type="primary" @click="onOpenPaymentApproval('detail', row)">查看详情</el-button>
                        <el-button v-if="row.businessType === '1'" icon="el-icon-info-filled" link size="small" type="primary" @click="onOpenPrepaymentRechargeModification('detail', row)">查看详情</el-button>
                    </div>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!-- / 付款单审批、修改	-->
        <payment-order-approval-invoicing-detail
            v-if="paymentOrderApprovalVisible"
            v-model:payment-order-approval-visible="paymentOrderApprovalVisible"
            :payment-order-approval-invoicingId="paymentOrderApprovalInvoicingId"
            :payment-order-approval-status="paymentOrderApprovalStatus"
            :payment-order-approval-title="paymentOrderApprovalTitle"
            @getList="getList"
        />

        <!--  / 预付款充值审批、修改  -->
        <prepayment-recharge-modification-invoicing-detail
            v-if="prepaymentRechargeModificationVisible"
            v-model:prepayment-recharge-modification-visible="prepaymentRechargeModificationVisible"
            :prepayment-recharge-modification-invoicingId="prepaymentRechargeModificationInvoicingId"
            :prepayment-recharge-modification-status="prepaymentRechargeModificationStatus"
            :prepayment-recharge-modification-title="prepaymentRechargeModificationTitle"
            @getList="getList"
        />

        <!--  / 开票  -->
        <invoice-drawer v-if="invoiceVisible" v-model="invoiceVisible" :initial-form-data="invoiceForm" :invoice-list="invoiceList" :is-collect-payment="isCollectPayment" @close="closeInvoice" @submit="submitInvoice" />

        <!--  / 红冲  -->
        <invoice-red-invoicing-detail
            v-if="invoiceRedInvoicingVisible"
            v-model:invoice-red-invoicing-visible="invoiceRedInvoicingVisible"
            :invoice-apply-id="invoiceRedInvoicingId"
            :invoice-red-invoicing-source="invoiceRedInvoicingSource"
            :invoice-red-invoicing-title="invoiceRedInvoicingTitle"
            :invoice-red-invoicing-type="invoiceRedInvoicingType"
            @close="closeRedInvoice"
            @submit="submitRedInvoice"
        />
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable/index.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import PaymentOrderApprovalInvoicingDetail from '@/views/carrierFunction/PaymentOrderApprovalInvoicingDetail.vue';
import PrepaymentRechargeModificationInvoicingDetail from '@/views/carrierFunction/PrepaymentRechargeModificationInvoicingDetail.vue';
import InvoiceApplication from '@/api/shipperEnd/InvoiceApplication';
import invoiceInformationMaintenance from '@/api/shipperEnd/invoiceInformationMaintenance';
import InvoiceDrawer from '@/components/InvoiceDrawer.vue';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import paymentDoc from '@/api/shipperEnd/paymentDoc';
import InvoiceRedInvoicingDetail from '@/views/carrierFunction/InvoiceRedInvoicingDetail.vue';
import InvoiceApproval from '@/api/carrierEnd/InvoiceApproval';
import customerPrepaymentBalance from '@/api/shipperEnd/customerPrepaymentBalance';

export default {
    name: 'InvoiceApplication',
    components: {
        PrepaymentRechargeModificationInvoicingDetail,
        PaymentOrderApprovalInvoicingDetail,
        ColumnTable,
        RightToolbar,
        SearchButton,
        InvoiceDrawer,
        InvoiceRedInvoicingDetail
    },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                queryDate: [],
                queryType: '1' // 1-货主 2-承运商
            },
            columns: [
                { title: '申请时间', key: 'applyTime', align: 'center', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '发票抬头', key: 'invoiceHead', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'businessType', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '开票金额（元）', key: 'invoiceAmount', align: 'center', minWidth: '120px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '审批状态', key: 'status', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '是否发起红冲', key: 'redFlag', align: 'center', minWidth: '90px', columnShow: true, showOverflowTooltip: true },
                { title: '发票类型', key: 'invoiceType', align: 'center', minWidth: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '220px', fixed: 'right', columnShow: true, showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            businessTypeList: [],
            redFlagList: [],
            statusList: [],
            paymentOrderApprovalVisible: false,
            paymentOrderApprovalInvoicingId: undefined,
            paymentOrderApprovalTitle: '付款单审批',
            paymentOrderApprovalStatus: undefined,
            prepaymentRechargeModificationVisible: false,
            prepaymentRechargeModificationInvoicingId: undefined,
            prepaymentRechargeModificationTitle: undefined,
            prepaymentRechargeModificationStatus: undefined,
            invoiceTypeList: [],
            invoiceList: [],
            invoiceVisible: false,
            invoiceLoading: false,
            invoiceForm: {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined,
                payIdList: []
            },
            isCollectPayment: false,
            invoiceRedInvoicingVisible: false,
            invoiceRedInvoicingId: undefined,
            invoiceRedInvoicingTitle: '红字发票申请',
            invoiceRedInvoicingType: undefined,
            invoiceRedInvoicingSource: undefined
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置业务类型颜色
         */
        setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': {}
                    }[businessType] || { color: '#999' }
                );
            };
        },
        /**
         * 设置红冲状态颜色
         */
        setRedFlagColor() {
            return (redFlag) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': { color: '#5cb85c' }
                    }[redFlag] || { color: '#999' }
                );
            };
        },
        /**
         * 设置状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' },
                        '3': { color: '#999' }
                    }[status] || { color: '#999' }
                );
            };
        }
    },
    created() {
        this.getDict();
        const Organization = this.$TOOL.data.get('Organization');
        this.queryForm['company.id'] = Organization[0].id;
        this.handleQuery();
    },
    methods: {
        /**
         * 关闭开票
         */
        closeInvoice() {
            this.invoiceVisible = false;
            this.invoiceForm = {
                invoiceAmount: undefined,
                invoiceData: null,
                remark: undefined
            };
        },
        /**
         * 关闭红冲
         */
        closeRedInvoice() {
            this.invoiceRedInvoicingVisible = false;
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.businessTypeList = await this.getDictList('payment_type');
            this.redFlagList = await this.getDictList('invoice_red_flag');
            this.statusList = await this.getDictList('payment_approval_type');
            this.invoiceTypeList = await this.getDictList('collaborating_shipper_invoice_type');
        },
        /**
         * 获取付款单审批列表
         */
        async getInvoiceHeaderList() {
            const ownerId = this.$TOOL.data.get('Organization')[0].id;
            const res = (await invoiceInformationMaintenance.getInvoiceHeadList({ ownerId })) || {};
            if (res.code === 200) {
                this.invoiceList = res.data || [];
                if (this.invoiceList.length === 0) {
                    this.$message.warning('未查询到发票抬头信息,请先前往【发票信息维护】新增发票抬头');
                }
            } else {
                this.invoiceList = [];
                // 提示维护发票抬头信息
                this.$message.error('请维护发票抬头信息');
            }
        },
        /**
         * 获取列表
         */
        async getList() {
            this.loading = true;
            const { ...params } = this.queryForm;
            const res = await InvoiceApplication.getList(params);
            if (res.code === 200) {
                this.dataList = res.data.records;
                this.total = res.data.total;
            } else {
                this.dataList = [];
                this.total = 0;
            }
            this.loading = false;
        },
        /**
         * 查询
         */
        handleQuery() {
            this.queryForm.current = 1;
            const { queryDate } = this.queryForm;
            if (queryDate && queryDate.length) {
                this.queryForm.queryStartDate = queryDate[0] ? queryDate[0] + ' 00:00:00' : '';
                this.queryForm.queryEndDate = queryDate[1] ? queryDate[1] + ' 23:59:59' : '';
            } else {
                this.queryForm.queryStartDate = undefined;
                this.queryForm.queryEndDate = undefined;
            }
            this.getList();
        },
        /**
         * 删除
         * @param row
         */
        onDelete(row) {
            this.$confirm('是否删除该条数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(async () => {
                    const res = await InvoiceApplication.delete({
                        businessType: row.businessType,
                        applyIds: row.id
                    });
                    if (res.code === 200) {
                        this.$message.success('删除开票申请成功');
                        await this.getList();
                    }
                })
                .catch(() => {});
        },
        /**
         * 下载发票
         * @param row
         */
        onDownloadInvoice(row) {
            if ('invoiceUrl' in row) {
                window.open(row.invoiceUrl);
            } else {
                this.$message.error('未获取到发票地址');
            }
        },
        /**
         * 打开开票
         * @param row
         */
        async onOpenInvoice(row) {
            await this.getInvoiceHeaderList();

            // 设置 isCollectPayment 的值
            if (row.businessType === '2') {
                // 付款单支付：只有当支付方式是到付(4)时才为true
                this.isCollectPayment = row.payMethod === '4';
            } else {
                // 预存款充值：直接为false
                this.isCollectPayment = false;
            }

            this.invoiceForm = {
                id: row?.id,
                businessType: row?.businessType,
                carrierId: row?.carrier?.id,
                applyNo: row?.applyNo,
                invoiceAmount: row?.invoiceAmount,
                remark: row?.remark,
                companyId: row?.companyId,
                companyName: row?.companyName
            };

            // 获取付款单审批详情
            if (row?.id) {
                const res = await paymentOrderApproval.getInvoiceApprovalDetail({ id: row.id });

                if (res?.code === 200) {
                    // Store response data in a variable to avoid scope issues
                    const responseData = res.data;

                    try {
                        if (!responseData) {
                            this.invoiceForm.payIdList = [];
                        } else {
                            this.invoiceForm.payIdList = (responseData.docList || []).map((item) => item?.id || null).filter((id) => id !== null);
                        }
                    } catch (error) {
                        this.invoiceForm.payIdList = [];
                    }

                    // 获取开票业务类型
                    this.invoiceForm.openInvoiceType = responseData?.invoiceApply?.openInvoiceType || undefined;
                    this.invoiceForm.signCompany = responseData?.invoiceApply?.signCompany || undefined;
                    this.invoiceForm.invoiceType = responseData?.invoiceApply?.invoiceType || undefined;
                    this.invoiceForm.projectType =
                        responseData?.invoiceApply?.projectName === '运输服务费' || responseData?.invoiceApply?.projectName === '国内运输费'
                            ? '运输服务'
                            : responseData?.invoiceApply?.projectName === '收派服务费' || responseData?.invoiceApply?.projectName === '仓储服务费'
                            ? '物流辅助服务'
                            : undefined;
                    this.invoiceForm.projectFeeType = responseData?.invoiceApply?.projectName || undefined;
                    this.invoiceForm.taxPoint = responseData?.invoiceApply?.taxPoint || undefined;

                    // 默收款公司
                    const Organization = this.$TOOL.data.get('Organization');
                    const companyRes = await customerPrepaymentBalance.getCompanyInfo(Organization[0].id, responseData.invoiceApply.carrierId);
                    if (companyRes.code === 200) {
                        this.invoiceForm.invoiceList = companyRes.data.invoiceList;
                    } else {
                        this.invoiceForm.invoiceList = [];
                    }

                    // 如果是付款单支付(2)才判断支付方式
                    if (row.businessType === '2') {
                        // 获取所有付款单地付款类型
                        const paymentDocTypes = responseData.docList.map((item) => item.paymentDocType);
                        // 判断是否都是到付(4)
                        const isAllCollectPay = paymentDocTypes.every((type) => type === '4');
                        // 设置付款类型
                        this.invoiceForm.paymentDocType = isAllCollectPay ? '4' : '3';
                        // 设置支付方式
                        this.invoiceForm.payMethod = isAllCollectPay ? '4' : '3';
                    }
                    // 如果是预付款充值(1)，使用 responseData.invoiceApply 中的信息
                    if (row.businessType === '1') {
                        this.invoiceForm.advanceAccount = responseData?.paymentApplyDTO?.advanceAccount || undefined;
                        this.invoiceForm.paymentApplyId = responseData?.paymentApplyDTO?.id || undefined;
                    }
                    // 如果是到付，使用 responseData.invoiceApply 中的信息
                    if (row.payMethod === '4') {
                        this.invoiceForm.invoiceData = {
                            invoiceHead: responseData?.invoiceApply?.invoiceHead,
                            taxNo: responseData?.invoiceApply?.taxNo,
                            address: responseData?.invoiceApply?.address,
                            phone: responseData?.invoiceApply?.phone,
                            openBank: responseData?.invoiceApply?.openBank,
                            bankAccount: responseData?.invoiceApply?.bankAccount
                        };
                    } else {
                        // 其他类型使用 invoiceList 中的数据
                        this.invoiceForm.invoiceData = this.invoiceList.find((item) => item.id === row.invoiceId);
                    }
                }
            }
            this.invoiceVisible = true;
        },
        /**
         * 打开付款单审批
         * @param status approval 审批 modify 修改 detail 查看
         * @param row
         * @return {Promise<void>}
         */
        onOpenPaymentApproval(status, row) {
            this.paymentOrderApprovalVisible = true;
            if (status === 'detail') {
                this.paymentOrderApprovalTitle = '付款单查看';
            }
            this.paymentOrderApprovalStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.paymentOrderApprovalInvoicingId = row.id;
            }
        },
        /**
         * 打开预付款充值修改
         * @param status approval 审批 modify 修改 detail 查看
         * @param row
         */
        onOpenPrepaymentRechargeModification(status, row) {
            this.prepaymentRechargeModificationVisible = true;
            if (status === 'detail') {
                this.prepaymentRechargeModificationTitle = '查看';
            }
            this.prepaymentRechargeModificationStatus = status;
            // 传递id用于查询详情
            if ('id' in row) {
                this.prepaymentRechargeModificationInvoicingId = row.id;
            }
        },
        /**
         * 红冲
         * @param row
         */
        onOpenRedInvoice(row) {
            this.invoiceRedInvoicingVisible = true;
            this.invoiceRedInvoicingId = row.id;
            this.invoiceRedInvoicingTitle = '红字发票申请';
            this.invoiceRedInvoicingType = row.businessType;
            this.invoiceRedInvoicingSource = 'owner';
        },
        /**
         * 重置表单
         * @param formName
         */
        resetForm(formName) {
            if (this.$refs[formName] !== undefined) {
                this.$refs[formName].resetFields();
            }
        },
        /**
         * 重置查询条件
         */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /**
         * 提交开票
         */
        submitInvoice(formData) {
            // 设置开票加载状态为true
            this.invoiceLoading = true;
            // 构建开票参数
            const params = {
                applyWay: '1', // 1-货主 2-承运商 (预付款默认货主发起)， 不传默认货主发起
                applyNo: formData.applyNo,
                id: formData.id,
                businessType: formData.businessType,
                carrierId: formData.carrierId,
                companyId: formData.payMethod === '4' ? undefined : formData.companyId, // 到付时不需要companyId,其他使用公司ID
                companyName: formData.companyName,
                invoiceId: formData.payMethod === '4' ? undefined : formData.invoiceData.id,
                invoiceHead: formData.invoiceData.invoiceHead,
                taxNo: formData.invoiceData.taxNo,
                address: formData.invoiceData.address,
                phone: formData.invoiceData.phone,
                openBank: formData.invoiceData.openBank,
                bankAccount: formData.invoiceData.bankAccount,
                invoiceAmount: formData.invoiceAmount,
                payIdList: formData.payIdList,
                remark: formData.remark,
                payMethod: formData.payMethod,
                openInvoiceType: formData.openInvoiceType,
                invoiceType: formData.invoiceType,
                projectName: formData.projectFeeType,
                signCompany: formData.signCompany,
                taxPoint: formData.taxPoint,
                paymentApplyId: formData.businessType === '1' ? formData.paymentApplyId : undefined
            };
            // 调用后端接口创建发票
            paymentDoc
                .applyInvoice(params)
                .then((res) => {
                    if (res.code === 200) {
                        this.$message.success('修改成功');
                        this.closeInvoice();
                        this.getList();
                    }
                })
                .finally(() => {
                    this.invoiceLoading = false;
                });
        },
        /**
         * 提交红冲
         */
        submitRedInvoice(formData) {
            const params = {
                source: '1', // 1-货主发起 2-承运商发起
                carrierId: formData.carrierId,
                invoiceApplyId: formData.invoiceApplyId,
                reason: formData.reason,
                detailDesc: formData.detailDesc,
                invoiceMoney: formData.invoiceMoney,
                redInvoiceAmount: formData.redInvoiceAmount
            };
            InvoiceApproval.submitInvoiceRedInvoicing(params).then((res) => {
                if (res.code === 200) {
                    this.$message.success('红冲申请成功');
                    this.closeRedInvoice();
                    this.getList();
                } else {
                    this.$message.error(res.msg || '红冲申请失败');
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
.number__unit__element {
    position: relative;
    :deep(.el-input__inner) {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 10px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
