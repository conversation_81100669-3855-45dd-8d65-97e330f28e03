<template>
  <el-card body-style="padding-bottom:2px" class="box-card">
    <el-form ref="queryRef" :inline="true" :model="searchForm" class="form_130" label-width="130px">
      <TopTitle :handleQuery="handleQuery" :resetQuery="resetQuery" :selFlag="false">
        <el-form-item label="名称">
          <el-input v-model="searchForm.n1" class="form_225" clearable placeholder="请输入名称" style="width: 220px"/>
        </el-form-item>
        <el-form-item label="自编码">
          <el-input v-model="searchForm.n2" class="form_225" clearable placeholder="请输入自编码" style="width: 220px"/>
        </el-form-item>
        <el-form-item label="大类" prop="noticeContent">
          <el-select v-model="searchForm.n3" class="form_225" placeholder="请选择大类" style="width: 220px">
            <el-option label="全部" value=""/>
            <el-option v-for="(item, index) in bigType" :key="index" :label="item.name" :value="item.value"/>
          </el-select>
        </el-form-item>
      </TopTitle>
    </el-form>

  </el-card>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watchEffect,} from "vue";
import TopTitle from "@/components/topTitle/index.vue";

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const searchForm = ref({
  n1: "",
  n2: "",
  n3: "",
});
const showSearch = ref(false);
const data = reactive({});
const emit = defineEmits(["handleQuery"]);
const {proxy} = getCurrentInstance();
const bigType = ref()
const handleQuery = () => {
  emit("handleQuery");
};
const resetQuery = () => {
  for (let i in searchForm.value) {
    searchForm.value[i] = "";
  }
  emit("handleQuery");
};
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
});
onMounted(async () => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  let bigKey = JSON.parse(localStorage.getItem('directory_category'))
  if (bigKey) {
    bigType.value = bigKey
  } else {
    bigType.value = await proxy.getDictList("directory_category")
    localStorage.setItem('directory_category', JSON.stringify(bigType.value))
  }
});
watchEffect(() => {
});
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  searchForm
});
</script>
<style lang="scss" scoped>
.butns {
  text-align: center;
}

.xBox {
  width: 220px;
}
</style>
