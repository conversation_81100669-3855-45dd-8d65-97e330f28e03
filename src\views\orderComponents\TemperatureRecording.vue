<template>
<div>
  <el-card class="mb10" shadow="never">
    <el-table :data="printRecordList">
      <el-table-column align="center" label="设备编号" prop="deviceNo" />
      <el-table-column align="center" label="查询开始时间" prop="printStartTime" />
      <el-table-column align="center" label="查询结束时间" prop="printEndTime" />
      <el-table-column align="center" label="打印数据">
        <template #default="scope">
          <el-link type="primary" @click="viewDevicePrintData(scope.row)">点击查看</el-link>
        </template>
      </el-table-column>
      <el-table-column align="center" label="打印人" prop="operateBy" >
        <template #default="scope">
          {{ scope.row?.operateBy }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="打印时间" prop="requestTime">
        <template #default="scope">
          {{scope.row?.requestTime }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button icon="el-icon-printer" type="primary" @click="printDeviceRecord(scope.row)">打印数据</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination class="mt10" v-show="printRecordTotal > 0" v-model:limit="printRecordQueryParams.size" v-model:page="printRecordQueryParams.current" :total="printRecordTotal" @pagination="getPrintRecord" />
  </el-card>
  <el-drawer :modal="false" v-model="printRecordReturnDataViewShow" title="打印数据" width="55vh">
    <div style="margin: 20px">
      <el-divider>打印参数</el-divider>
      <el-descriptions column="1">
        <el-descriptions-item label="运输单位">
          {{ printRecordParams.transportUnit }}
        </el-descriptions-item>
        <el-descriptions-item label="运单编号">
          {{ printRecordParams.transOrderNo }}
        </el-descriptions-item>
        <el-descriptions-item label="单据编号">
          {{ printRecordParams.orderNo }}
        </el-descriptions-item>
        <el-descriptions-item label="发件公司">
          {{ printRecordParams.sendingCompany }}
        </el-descriptions-item>
        <el-descriptions-item label="收件公司">
          {{ printRecordParams.receivingCompany }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider>温度数据</el-divider>
      <el-table :data="printRecord.data" height="500px" v-if="printRecordParams.deviceNoList.length > 1">
        <el-table-column align="center" label="时间" prop="dateTime" />
        <el-table-column align="center" :label="printRecordParams.deviceNoList[0]">
          <template #default="scope"> {{ scope.row.temperature }}℃ </template>
        </el-table-column>
		  <el-table-column align="center" :label="printRecordParams.deviceNoList[1]">
			  <template #default="scope"> {{ scope.row.temperature2 }}℃ </template>
		  </el-table-column>
      </el-table>
		<el-table :data="printRecord.data" height="500px" v-else>
			<el-table-column align="center" label="时间" prop="dateTime" />
			<el-table-column align="center" label="温度">
				<template #default="scope"> {{ scope.row.temperature }}℃ </template>
			</el-table-column>
		</el-table>
    </div>
  </el-drawer>
</div>
</template>

<script>
import orderManagement from "@/api/logisticsManagement/orderManagement";
import printRecord from "@/api/management/printRecord/index.js";
import moment from 'moment';

import printLabel from "@/api/print/printLabelView";

export default {
  name: "TemperatureRecording",
  props: {
    orderInfo: {
      required: true,
      type: Object
    },
  },
  computed: {
    /**
     * 时间格式化
     * @returns {function(*=): *}
     */
    timeFormatting() {
      return (val) => {
        return moment(val).format('YYYY-MM-DD HH:mm:ss');
      };

    },
  },
  data() {
    return {
      printRecordList: [],
      printRecord: {
        params: {},
        data: []
      },
      printRecordParams: {
        page: 1,
        size: 10,
        total:0,
      },
      printRecordTotal: 0,
      printRecordReturnDataViewShow: false,
      printRecordQueryParams: {
        current: 1,
        size: 10
      },
      trafficUnitOptions:[], // 运输单位
    }
  },
  async created() {
    /** 运输单位 */
    this.trafficUnitOptions = await this.getDictList('tms_traffic_unit');
    this.getPrintRecord();
  },
  methods: {

    // 温度打印记录
    getPrintRecord() {
        if (!this.orderInfo.transOrderNo) {
          this.msgError('当前订单尚未确认，没有温度记录');
          return;
        }
      this.printRecordQueryParams.transOrderNo = this.orderInfo.transOrderNo;
      printRecord.printLogList(this.printRecordQueryParams).then((res) => {
        if(res.code == 200){
          this.printRecordList = res.data.records;
          this.printRecordTotal = res.data.total;
        }
      });
    },
    // 打印温度记录数据
    printDeviceRecord(row) {
		let deviceNoList = [];
		if(row.deviceNo.indexOf(',') ==-1){
			deviceNoList = [row.deviceNo];
		}else{
			deviceNoList = row.deviceNo.split(',');
		}
		let {minTemp1,maxTemp1,minTemp2,maxTemp2} = this.getTemp(row.tempDataList);
		let params = {
			transportCompany: row.transportUnit, // 承运单位
			deviceNoList: deviceNoList, // 设备编号
			carCode: row.carCode, // 车牌号
			transOrderNo: row.transOrderNo, // 运单号
			externalOrderNo: row.externalOrderNo, // 外部订单号
			sendCompany: row.sendingCompany, // 发货单位
			receiverCompany: row.receivingCompany, // 收货单位
			startTime: row.firstTimeData,
			endTime: row.lastTimeData,
			minTemp1,maxTemp1,minTemp2,maxTemp2,
			source:'0',
			tempDataList:row.tempDataList
		};
		orderManagement.devicePrint(params).then(response=>{
			this.loading = false;
			const binaryData = [];
			binaryData.push(response);
			//获取blob链接
			let pdfUrl = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));
			window.open(pdfUrl)

		}).catch(e=>{
		})
    },
	  /**
	   *  获取温度数据中的最大值和最小值
	   * @param data
	   * @returns {{maxTemp1: (*|{style: string, type: string}), minTemp1: (*|{style: string, type: string}), maxTemp2, minTemp2}}
	   */
	  getTemp(data){
		  let minTemp1 = data[0].temperature;
		  let maxTemp1 = data[0].temperature;
		  let minTemp2,maxTemp2;
		  if(data[0].temperature2){
			  minTemp2 = data[0].temperature2;
			  maxTemp2 = data[0].temperature2;
		  }
		  // 遍历数组以找到最小和最大温度
		  for (let i = 1; i < data.length; i++) {
			  const temp1 = data[i].temperature;

			  // 如果当前值更小，则更新minTemp1
			  if (temp1 < minTemp1) {
				  minTemp1 = temp1;
			  }

			  // 如果当前值更大，则更新maxTemp1
			  if (temp1 > maxTemp1) {
				  maxTemp1 = temp1;
			  }
			  if(data[i].temperature2){
				  const temp2 = data[i].temperature2;
				  // 如果当前值更小，则更新minTemp1
				  if (temp2 < minTemp2) {
					  minTemp2 = temp2;
				  }

				  // 如果当前值更大，则更新maxTemp1
				  if (temp2 > maxTemp2) {
					  maxTemp2 = temp2;
				  }
			  }

		  }
		  return  {minTemp1,maxTemp1,minTemp2,maxTemp2};
	  },
    // 查看温度记录打印数据
    viewDevicePrintData(row) {
		let deviceNoList = [];
		if(row.deviceNo.indexOf(',') ==-1){
			deviceNoList = [row.deviceNo];
		}else{
			deviceNoList = row.deviceNo.split(',');
		}
      this.printRecordParams =  {
        deviceNo:row.deviceNo,
		  deviceNoList:deviceNoList,
        queryStartTime:row.printStartTime,
        queryEndTime:row.printEndTime,
        transOrderNo:row.transOrderNo,	// 运单号
        orderNo:row.orderNo, // 订单编号
        transportUnit:row.transportUnit, // 运输单位
        sendingCompany:row.sendingCompany, // 发件公司
        receivingCompany:row.receivingCompany,// 发件公司
        logPrint:false,
        page:1,
      };
      	this.printRecordParams.transOrderNo = this.orderInfo.transOrderNo;
		this.printRecord.data = row.tempDataList;
		this.printRecordReturnDataViewShow = true;
    }
  }
}
</script>

<style scoped>

</style>
