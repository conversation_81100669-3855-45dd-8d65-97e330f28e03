<template>
    <div>
        <div class="app-container">
            <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                <el-form ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form" @submit.native.prevent>
                    <el-form-item label="订单号" prop="orderNo" style="width: 195px">
                        <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号" @clear="handleQuery" @keyup.enter.native="handleQuery" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="运单号" prop="transOrderNo" style="width: 280px">
                        <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单号" @clear="handleQuery" @keyup.enter.native="handleQuery" />
                    </el-form-item>
                    <el-form-item label="货主公司" prop="companyId" style="width: 280px">
                        <el-select v-model="queryParams.companyId" clearable filterable placeholder="请选择货主公司" @change="handleQuery">
                            <el-option v-for="dict in ownerList" :key="dict.companyId" :label="dict.companyName" :value="dict.companyId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="订单状态" prop="status">
                        <el-select v-model="queryParams.status" clearable placeholder="请选择订单状态" @change="handleQuery">
                            <el-option v-for="(dict, idx) in fourplOrderStatusOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="发件公司" prop="sendCompany" style="width: 280px">
                        <el-input v-model="queryParams.sendCompany" clearable placeholder="请输入发件公司" @clear="handleQuery" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="发件地址" prop="sendAddress">
                        <el-cascader v-model="queryParams.sendAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择发件地址" @change="(v) => handleSendOrReceiverAddress(v, 'send')" @visible-change="visibleChange" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="收件人" prop="receiverUser" style="width: 280px">
                        <el-input v-model="queryParams.receiverUser" clearable placeholder="请输入收件人" @clear="handleQuery" @keyup.enter.native="handleQuery" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="收件公司" prop="receiverCompany" style="width: 280px">
                        <el-input v-model="queryParams.receiverCompany" clearable placeholder="请输入收件公司" @clear="handleQuery" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="收件地址" prop="receiverAddress">
                        <el-cascader v-model="queryParams.receiverAddress" :options="sysAreas" :props="{ checkStrictly: true }" clearable filterable placeholder="请选择收件地址" @change="handleSendOrReceiverAddress" @visible-change="visibleChange" />
                    </el-form-item>
                    <el-form-item v-show="isShow" label="产品分类" prop="productClass">
                        <el-select v-model="queryParams.productClass" clearable filterable placeholder="请选择产品分类" @change="handleQuery">
                            <el-option v-for="dict in fourplProductClassOptions" :key="dict.value" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="温层类型" prop="temperatureTypeTemp" style="width: 280px">
                        <el-select v-model="queryParams.temperatureTypeTemp" clearable collapse-tags collapse-tags-tooltip filterable multiple placeholder="请选择温层类型" @change="handleQuery">
                            <el-option v-for="(dict, index) in temperatureTypeDicts" :key="index" :label="dict.describtion" :value="dict.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="付款方式" prop="paymentMethod">
                        <el-select v-model="queryParams.paymentMethod" clearable filterable placeholder="请选择付款方式" @change="handleQuery">
                            <el-option v-for="(dict, index) in fourplPaymentMethodOptions" :key="index" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="揽收方式" prop="orderType">
                        <el-select v-model="queryParams.orderType" clearable placeholder="请选择揽收方式" @change="handleQuery">
                            <el-option v-for="dict in collectionMethod" :key="dict.value" :label="dict.name" :value="dict.value * 1" />
                        </el-select>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="件数" prop="goodsPackages">
                        <div style="width: 220px">
                            <el-select v-model="queryParams.prePackageOption" style="width: 45%">
                                <el-option v-for="(item, index) in prePackageOptions" :key="index" :label="item.name" :value="item.value"> </el-option>
                            </el-select>
                            <el-input v-model="queryParams.goodsPackages" clearable placeholder="请输入件数" style="width: 55%" @keyup.enter.native="handleQuery" />
                        </div>
                    </el-form-item>
                    <el-form-item v-show="isShow" label="是否有异动" prop="isChange">
                        <el-select v-model="queryParams.isChange" clearable placeholder="请选择是否异动订单" @change="handleQuery">
                            <el-option v-for="(dict, index) in fourplIsChangeOptions" :key="index" :label="dict.name" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="date-screening" label="下单时间" prop="queryTime" style="width: 320px">
                        <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                    </el-form-item>
                    <search-button :is-show-all="isShow" @handleQuery="handleQuery" @resetQuery="resetQuery" @showAllClick="showAllClick" />
                </el-form>
            </el-card>
            <el-card :body-style="{ padding: '10px' }" shadow="never">
                <div class="mb10" style="display: flex">
                    <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" :tableID="'fourPlChangeOfOrderList'" style="margin-left: auto" @queryTable="getList"></right-toolbar>
                </div>
                <column-table key="fourPlChangeOfOrderList" v-loading="loading" :columns="columns" :data="orderList" :defaultSort="{ prop: 'createtime', order: 'descending' }">
                    <template #status="{ row }">
                        <span>{{ fourplOrderStatusFormat(row) }}</span>
                    </template>
                    <template #createDate="{ row }">
                        <span>{{ timeFormatting(row.createDate) }}</span>
                    </template>
                    <template #receiverAddress="{ row }">
                        <span>{{ row?.receiverTown?.province }}{{ row?.receiverTown?.city }}{{ row?.receiverTown?.county }}{{ row?.receiverTown?.town }}{{ row?.receiverAddress || '' }}</span>
                    </template>
                    <template #isChange="{ row }">
                        <span>{{ fourplIsChangeFormat(row) }}</span>
                    </template>
                    <template #opt="{ row }">
                        <el-button v-if="row.status != 5 && row.status != 6" v-hasPermi="['fourpl:corderChange:add']" icon="el-icon-check" link size="small" type="warning" @click="changeOfOrderFormBtnClick(row)">订单异动</el-button>
                        <el-button v-if="row.isChange == '1'" icon="el-icon-info-filled" link size="small" type="primary" @click="changeOfOrderRecordBtnClick(row)">异动记录</el-button>
                    </template>
                </column-table>
                <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
            </el-card>
        </div>
        <!-- 订单异动表单 -->
        <el-drawer v-model="changeOfOrderFormOpen" append-to-body size="1100px" title="订单异动" @close="changeOfOrderFormOpen = false">
            <div style="position: absolute; right: 78px; top: 23px">
                <el-button icon="el-icon-document-copy" round size="mini" type="danger" @click="changeOfOrderRecordBtnClick(orderInfo)">异动记录 </el-button>
            </div>
            <OrderTransactionForm v-if="changeOfOrderFormOpen" :orderInfo="orderInfo" @callbackMethod="changeOfOrderFormBtnHandle"> </OrderTransactionForm>
        </el-drawer>
        <!-- 订单异动记录 -->
        <el-drawer v-model="changeOfOrderRecordOpen" :close-on-press-escape="false" :wrapperClosable="true" append-to-body size="80%" title="订单异动记录" @close="changeOfOrderRecordOpen = false">
            <OrderTransactionDetails v-if="changeOfOrderRecordOpen" :orderId="orderId" @callbackMethod="changeOfOrderRecordBtnHandle"></OrderTransactionDetails>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import OrderTransactionForm from '@/views/logisticsManagement/orderTransaction/OrderTransactionForm.vue';
import OrderTransactionDetails from '@/views/orderComponents/OrderTransactionDetails.vue';
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
import enterpriseCooperation from '@/api/logisticsConfiguration/enterpriseCooperation.js'; // 合作配置
import otherConfiguration from '@/api/logisticsConfiguration/otherConfiguration.js'; // 其他配置
import moment from 'moment';

export default {
    name: 'OrderTransactionList',
    components: {
        ColumnTable,
        RightToolbar,
        SearchButton,
        OrderTransactionForm,
        OrderTransactionDetails
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 运单信息表格数据
            orderList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                current: 1, // 页码
                size: 10, // 每页大小
                beginCreateDate: null, //下单时间-开始
                endCreateDate: null, // 下单时间-结束
                receiverUser: null, // 收件人
                receiverCompany: null, // 收件公司
                sendCompany: null, // 发件公司
                productType: null, // 货品类型
                orderNo: null, // 订单号
                transOrderNo: null, // 运单号
                status: null, // 订单状态
                companyId: null, //货主公司
                sendAddress: null, //发件地址
                receiverAddress: null, // 收件地址
                productClass: null, // 货品类型
                'temperatureType.id': '', //温层类型
                paymentMethod: null, // 付款方式
                orderType: null, // 货物收揽方式
                prePackageOption: '', // 件数
                packages: null, // 件数数量
                sendProvinceId: null,
                sendCityId: null,
                sendCountyId: null,
                sendTownId: null,
                receiverProvinceId: null,
                receiverCityId: null,
                receiverCountyId: null,
                receiverTownId: null,
                temperatureTypeTemp: ''
            },
            shortcuts: [
                {
                    text: '无',
                    value: (e) => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: (e) => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ],
            // 表单参数
            form: {
                orderInfo: {}
            },
            // 表单校验
            rules: {},
            // 运输类型
            fourplProductTypeOptions: [],
            // 货品类型
            fourplProductClassOptions: [],
            sysAreas: [], //省市区数据
            temperatureTypeDicts: [], //温层类型字典
            fourplPaymentMethodOptions: [], // 付款方式
            collectionMethod: [], // 揽收方式
            fourplIsChangeOptions: [
                {
                    name: '是',
                    value: '1'
                },
                {
                    name: '否',
                    value: '0'
                }
            ],
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', width: '120px', fixed: 'left', columnShow: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', width: '120px', columnShow: true },
                { title: '订单状态', key: 'status', align: 'center', width: '120px', columnShow: true },
                { title: '是否有异动', key: 'isChange', align: 'center', width: '100px', columnShow: true },
                { title: '下单时间', key: 'createDate', align: 'center', width: '160px', columnShow: true },
                { title: '收件人', key: 'receiverUser', align: 'center', width: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '收件人电话', key: 'receiverUserPhone', align: 'center', width: '180px', columnShow: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', width: '210px', columnShow: true, showOverflowTooltip: true },
                { title: '收件地址', key: 'receiverAddress', align: 'center', width: '210px', columnShow: true, showOverflowTooltip: true },
                { title: '发件公司', key: 'sendCompany', align: 'center', width: '210px', columnShow: true, showOverflowTooltip: true },
                { title: '运费(元)', key: 'orderCost', align: 'center', width: '120px', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '180px', fixed: 'right', hideFilter: true, columnShow: true }
            ],
            prePackageOptions: [],
            orderInfo: {},
            infoOpen: false,
            // orderStatus: '0',
            fourplOrderStatusOptions: [], // 运单状态
            examineOpen: false,
            companyList: [], // 收件公司
            listOfShippingCompanies: [], // 发货公司
            terminateOrderFormOpen: false, // 显示隐藏中止订单表单
            changeOfOrderFormOpen: false, // 显示隐藏订单异动表单
            fourplOrderChangeTypeOptions: [], // 异动类型
            fourplOrderTerminationTypeOptions: [], // 中止类型
            orderId: null, // 订单id
            changeOfOrderRecordOpen: false, // 显示隐藏订单异动记录
            detailOrder: false, // 显示或隐藏订单详情
            addOrderFormOpen: false, // 显示隐藏新建订单表单
            terminateOrderReasonOpen: false, // 显示隐藏中止订单详情
            terminateOrderId: null, // 中止订单id；
            fourplTransorderStatusOptions: [],
            ownerList: [], // 货主公司下拉选
            isShow: false
        };
    },
    computed: {
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },
    async created() {
        // 订单状态字典
        this.fourplOrderStatusOptions = await this.getDictList('fourpl_order_status');

        /** 产品分类 */
        this.fourplProductClassOptions = await this.getDictList('fourpl_product_class');

        /** 付款方式 */
        let fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
        this.fourplPaymentMethodOptions = fourplPaymentMethodOptions.filter((item) => item.value != '4' && item.value != '5' && item.value != '6');
        /** 揽收方式 */
        this.collectionMethod = await this.getDictList('fourpl_mail_service');
        /** 付款状态订单件数搜索条件 */
        this.prePackageOptions = await this.getDictList('fourpl_order_equals');
        // 默认设置当天
        let now = moment(new Date()).format('YYYY-MM-DD');
        this.queryParams.queryTime = [now, now];
        this.handleQuery();
        // 获取货主公司
        this.getCompanySelect();
        // 获取温层类型
        this.getTemperatureType();
    },
    methods: {
        // 订单异动开启
        changeOfOrderFormBtnClick(row) {
            this.orderInfo = row;
            this.changeOfOrderFormOpen = true;
        },

        // 订单异动关闭
        changeOfOrderFormBtnHandle(data) {
            this.changeOfOrderFormOpen = false;
            this.getList();
        },
        // 订单异动记录开启
        changeOfOrderRecordBtnClick(row) {
            this.orderId = row.id;
            this.changeOfOrderRecordOpen = true;
            this.changeOfOrderFormOpen = false;
        },
        // 订单异动记录关闭
        changeOfOrderRecordBtnHandle(data) {
            this.changeOfOrderRecordOpen = false;
            this.getList();
        },
        // 标准时间格式化
        formatDate(cellValue) {
            return formatDate(cellValue);
        },
        // 格式化异动订单状态
        formatTheStatusOfTheChangeOrder(data) {
            return this.selectDictLabel(this.fourplTransorderStatusOptions, data);
        },
        /** 4PL字典转换 */
        fourplFormat(data, val) {
            return this.selectDictLabel(data, val);
        },
        /** 是否异动转换 */
        fourplIsChangeFormat(val) {
            return this.selectDictLabel(this.fourplIsChangeOptions, val.isChange);
        },
        /** 订单状态4PL字典转换 */
        fourplOrderStatusFormat(val) {
            return this.selectDictLabel(this.fourplOrderStatusOptions, val.status);
        },
        /** 运输类型4PL字典转换 */
        fourplProductTypeFormat(val) {
            return this.selectDictLabel(this.fourplProductTypeOptions, val.productType);
        },
        // 获取货主公司下拉
        getCompanySelect() {
            enterpriseCooperation.cooperateSelect({ status: '1' }).then((response) => {
                this.ownerList = response.data;
            });
        },
        // 新订单详情开启
        getDetail(row) {
            this.detailOrder = true;
            this.orderInfo = { id: row.newOrderId };
        },
        /** 查询运单信息列表 */
        getList() {
            this.orderList = [];
            this.loading = true;
            let params = { ...this.queryParams };
            params.queryType = '1'; // 0-货主 1-承天商
            params.tempId = params.temperatureTypeTemp.toString();
            delete params.queryTime;
            delete params.sendAddress;
            delete params.receiverAddress;
            delete params.temperatureTypeTemp
            orderManagement
                .listOrderDrug(params)
                .then((response) => {
                    if (response.code === 200) {
                        this.loading = false;
                        this.orderList = response.data.records || [];
                        this.total = response.data.total || 0;
                    } else {
                        this.loading = false;
                        this.$message.error(response.msg);
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 获取温层类型
        getTemperatureType() {
            otherConfiguration.getTemperatureTypeList({ status: '0' }).then((res) => {
                if (res.code === 200 && res?.data?.records.length > 0) {
                    this.temperatureTypeDicts = res.data.records;
                }
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = null;
                this.queryParams.endCreateDate = null;
            }
            if (this.queryParams.sendAddress) {
                const [sendProvinceId, sendCityId, sendCountyId, sendTownId] = this.queryParams.sendAddress;
                this.queryParams.sendProvinceId = sendProvinceId || null;
                this.queryParams.sendCityId = sendCityId || null;
                this.queryParams.sendCountyId = sendCountyId || null;
                this.queryParams['sendTown.id'] = sendTownId || null;
            } else {
                this.queryParams.sendProvinceId = null;
                this.queryParams.sendCityId = null;
                this.queryParams.sendCountyId = null;
                this.queryParams['sendTown.id'] = null;
            }
            if (this.queryParams.receiverAddress) {
                const [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = this.queryParams.receiverAddress;

                this.queryParams.receiverProvinceId = receiverProvinceId || null;
                this.queryParams.receiverCityId = receiverCityId || null;
                this.queryParams.receiverCountyId = receiverCountyId || null;
                this.queryParams['receiverTown.id'] = receiverTownId || null;
            } else {
                this.queryParams.receiverProvinceId = null;
                this.queryParams.receiverCityId = null;
                this.queryParams.receiverCountyId = null;
                this.queryParams['receiverTown.id'] = null;
            }
            this.queryParams.current = 1;
            this.getList();
        },
        handleReceivingCompanyChange(e) {
            const { company } = e;
            if (company) this.queryParams.receiverCompany = company;
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /**
         * @description: 监听收发件地址
         * @return {*}
         */
        handleSendOrReceiverAddress(v, type) {
            if (v && type === 'send') {
                let [sendProvinceId, sendCityId, sendCountyId, sendTownId] = v;
                this.queryParams = { ...this.queryParams, sendProvinceId, sendCityId, sendCountyId, sendTownId };
            } else {
                let [receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId] = v;
                this.queryParams = { ...this.queryParams, receiverProvinceId, receiverCityId, receiverCountyId, receiverTownId };
            }
            this.handleQuery();
        },
        handleSendingCompanyChange(e) {
            const { company } = e;
            if (company) this.queryParams.sendCompany = company;
            this.handleQuery();
        },
        // // 点击tabs
        // handleTabsClick(tab, event) {
        //   this.orderStatus = tab.name;
        //   this.queryParams.current = 1;
        //   this.resetForm('queryForm');
        //   this.getList();
        // },
        /** 重置按钮操作 */
        resetQuery() {
            const resetParams = ['sendProvinceId', 'sendCityId', 'sendCountyId', 'sendTownId', 'receiverProvinceId', 'receiverCityId', 'receiverCountyId', 'receiverTownId', 'receiverAddress', 'sendAddress'];
            for (let key in resetParams) {
                this.queryParams[resetParams[key]] = null;
            }
            let now = moment(new Date()).format('YYYY-MM-DD');
            this.queryParams.queryTime = [now, now];
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 展开或者合上
        showAllClick() {
            this.isShow = !this.isShow;
        },
        // 中止订单开启
        terminateOrderFormBtnClick(row) {
            this.orderInfo = row;
            this.terminateOrderFormOpen = true;
        },
        // 中止订单关闭
        terminateOrderFormBtnHandle(data) {
            this.terminateOrderFormOpen = false;
            this.getList();
        },
        // 中止订单详情开启
        terminateOrderReasonBtnClick(row) {
            this.orderInfo = row;
            this.terminateOrderReasonOpen = true;
            // this.changeOfOrderFormOpen = false;
        },
        // 中止订单详情关闭
        terminateOrderReasonBtnHandle(data) {
            this.terminateOrderReasonOpen = false;
        },
        /**
         * 获取省市区
         */
        visibleChange() {
            this.sysAreas = this.getSysAreas;
            this.$nextTick(() => {
                const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
                Array.from($el).map((item) => item.removeAttribute('aria-owns'));
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
