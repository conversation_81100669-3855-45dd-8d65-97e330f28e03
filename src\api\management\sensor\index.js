import http from "@/utils/request"

export default {
    // 查询列表
    terminalList: function (params) {
        return http.get('/device/terminal/list', params)
    },
    // 新增修改
    terminalSave: function (data) {
        return http.post("/device/terminal/save", data);
    },
    // 删除
    terminalDelete: function (params) {
        return http.delete("/device/terminal/delete", params);
    },
    // 导出
    terminalExport: function (params,config,resDetail,responseType) {
        return http.get('/device/terminal/export', params,config,resDetail,responseType,1)
    },
    // 根据Id获取设备数据
    terminalQueryById: function (params) {
        return http.get('/device/terminal/queryById', params)
    },
    // 根据Id获取设备数据
    printTmpList: function (params) {
        return http.get('/device/tmp/printTmpList', params)
    },
    // 根据Id校准数据
    queryByIdList: function (params) {
        return http.get('/device/terminalCalibration/queryById', params)
    },
    // 保存设备校准
    saveCheckList: function (data) {
        return http.post("/device/terminalCalibration/save", data);
    },
    // 设备校准列表查询
    terminalCalibrationList: function (params) {
        return http.get('/device/terminalCalibration/list', params)
    },
    // 删除
    terminalCalibrationDelete: function (params) {
        return http.delete('/device/terminalCalibration/delete', params)
    },
    // 查询列表
    getTmpList: function (params) {
        return http.get('/device/tmp/list', params)
    },
	// 导出设备查询记录
	tmpExport: function (params,config,resDetail,responseType) {
		return http.get('/device/tmp/export', params,config,resDetail,responseType,1)
	},
	// 传感器导入模板下载
	downloadTemplate: function (params,config,resDetail,responseType) {
		return http.get('/device/terminal/downloadTemplate', params,config,resDetail,responseType,1)
	},
	// 确认写入数据库
	batchImportDevice: function (data) {
		return http.post("/device/terminal/batchImportDevice", data);
	},
}

