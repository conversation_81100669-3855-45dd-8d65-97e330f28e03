<template>
    <div ref="tableBox" class="ColumnTable">
        <el-table
            ref="ColumnTable"
            v-loading="loading"
            :border="border"
            :data="data"
            :default-sort="defaultSort"
            :highlight-current-row="highlightCurrentRow"
            :lazy="lazy"
            :load="load"
            :max-height="maxHeight"
            :row-class-name="rowClassName"
            :row-key="rowKey"
            :row-style="rowStyle"
            :show-summary="showSummary"
            :span-method="spanMethod"
            :stripe="stripe"
            :summary-method="summaryMethod"
            :tableId="tableId"
            :tree-props="treeProps"
            @select="(selection, row) => $emit('select', selection, row)"
            @select-all="(selection) => $emit('select-all', selection)"
            @selection-change="(selection) => $emit('selection-change', selection)"
            @current-change="(currentRow, oldCurrentRow) => $emit('current-change', currentRow, oldCurrentRow)"
            @row-dblclick="(row, column, event) => $emit('row-dblclick', row, column, event)"
            @expand-change="(row, column, event) => $emit('expand-change', row, column, event)"
            @row-click="(row, column, event) => $emit('row-click', row, column, event)"
        >
            <el-table-column v-if="showCheckBox" align="center" fixed type="selection" width="53" />
            <el-table-column v-if="showExpand" type="expand" width="60">
                <template #default="props">
                    <slot :props="props.row" name="expand"></slot>
                </template>
            </el-table-column>
            <el-table-column v-if="showIndex" align="center" class-name="show__index" fixed label="序号" type="index" width="80"></el-table-column>
            <template v-for="(item, index) in columns">
                <el-table-column
                    v-if="item.columnShow && item.children && Array.isArray(item.children)"
                    :key="index"
                    :align="item.align"
                    :fixed="item.fixed"
                    :label="item.title"
                    :label-class-name="item.labelClassName"
                    :min-width="item.minWidth"
                    :prop="item.key"
                    :show-overflow-tooltip="item.showOverflowTooltip || false"
                    :sortable="item.sortable || false"
                    :width="item.width"
                >
                    <el-table-column
                        v-for="(child, index) in item.children"
                        :key="index"
                        :align="child.align"
                        :fixed="child.fixed"
                        :label="child.title"
                        :label-class-name="item.labelClassName"
                        :min-width="item.minWidth"
                        :prop="child.key"
                        :show-overflow-tooltip="child.showOverflowTooltip || false"
                        :sortable="child.sortable || false"
                        :width="child.width"
                    >
                        <template #default="{ row, column, rowIndex }">
                            <slot :column="column" :name="child ? child.key : ''" :row="row" :rowIndex="rowIndex">
                                <span v-if="row[child.key] === null || typeof row[child.key] === undefined">--</span>
                                <span v-else>{{ row[child.key] }}</span>
                            </slot>
                        </template>
                    </el-table-column>
                    <template #header="scope">
                        <span v-if="item.isFilter">
                            <el-dropdown @command="(command) => handleCommand(command, item.key, index)">
                                <span>
                                    {{ item.title }}
                                    <el-tooltip class="item" content="筛选" effect="dark" placement="top-start">
                                        <img v-if="item.isActive" height="12" src="./filtersH.png" width="12" />
                                        <img v-else height="12" src="./filters.png" width="12" />
                                    </el-tooltip>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu class="table-dropdown-menu">
                                        <el-dropdown-item :tabindex="-1" :command="null">全部</el-dropdown-item>
                                        <el-dropdown-item v-for="(val, i) in item.filters" :key="val.value" :class="filterClass(val.value, item.filters)" :command="val.value" :tabindex="i">{{ val.name }}</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </span>
                        <span v-else-if="item.isShowHelpIcon" style="display: inline-flex; align-items: center">
                            {{ item.title }}
                            <el-tooltip class="item" placement="top">
                                <template #content>
                                    <div v-html="item.helpText"></div>
                                </template>
                                <QuestionFilled style="width: 14px; height: 14px; color: #909399; margin-left: 2px" />
                            </el-tooltip>
                        </span>
                        <span v-else>{{ item.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="item.columnShow && !item.children"
                    :key="index"
                    :align="item.align"
                    :fixed="item.fixed"
                    :label="item.title"
                    :label-class-name="item.labelClassName"
                    :min-width="item.minWidth"
                    :prop="item.key"
                    :show-overflow-tooltip="item.showOverflowTooltip || false"
                    :sortable="item.sortable || false"
                    :width="item.width"
                >
                    <template #default="{ row, column, rowIndex }">
                        <slot :column="column" :name="item.key" :row="row" :rowIndex="rowIndex">
                            <span v-if="row[item.key] === null || typeof row[item.key] === undefined">--</span>
                            <span v-else>{{ row[item.key] }}</span>
                        </slot>
                    </template>
                    <template #header="scope">
                        <span v-if="item.isFilter">
                            <el-dropdown @command="(command) => handleCommand(command, item.key, index)">
                                <span>
                                    {{ item.title }}
                                    <el-tooltip class="item" content="筛选" effect="dark" placement="top-start">
                                        <img v-if="item.isActive" height="12" src="./filtersH.png" width="12" />
                                        <img v-else height="12" src="./filters.png" width="12" />
                                    </el-tooltip>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu class="table-dropdown-menu">
                                        <el-dropdown-item :tabindex="-1" :command="null">全部</el-dropdown-item>
                                        <el-dropdown-item v-for="(val, i) in item.filters" :key="val.value" :class="filterClass(val.value, item.filters)" :command="val.value" :tabindex="i">{{ val.name }}</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </span>
                        <span v-else-if="item.isShowHelpIcon" style="display: inline-flex; align-items: center">
                            {{ item.title }}
                            <el-tooltip class="item" placement="top">
                                <template #content>
                                    <div v-html="item.helpText"></div>
                                </template>
                                <QuestionFilled style="width: 14px; height: 14px; color: #909399; margin-left: 2px" />
                            </el-tooltip>
                        </span>
                        <span v-else>{{ item.title }}</span>
                    </template>
                </el-table-column>
            </template>
        </el-table>
    </div>
</template>

<script>
import { QuestionFilled } from '@element-plus/icons-vue';
export default {
    name: 'ColumnTable',
    components: { QuestionFilled },
    props: {
        columns: {
            type: Array,
            default: () => {
                return [];
            }
        },
        data: {
            type: Array,
            default: () => {
                return [];
            }
        },
        tableId: {
            type: String,
            default: () => {
                return '';
            }
        },
        loading: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        showExpand: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        showIndex: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        showCheckBox: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        rowKey: {
            type: String,
            default: () => {
                return '';
            }
        },
        treeProps: {
            type: Object,
            default: () => {
                return {};
            }
        },
        spanMethod: {
            type: Function,
            default: () => {
                return () => {};
            }
        },
        lazy: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        border: {
            type: Boolean,
            default: () => {
                return true;
            }
        },
        load: {
            type: Function,
            default: () => {
                return () => {};
            }
        },
        stripe: {
            type: Boolean,
            default: () => {
                return true;
            }
        },
        maxHeight: {
            type: [Number, String],
            default: () => {
                return 400;
            }
        },
        showSummary: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        highlightCurrentRow: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        defaultSort: {
            type: Object,
            default: () => {
                return {};
            }
        },
        rowClassName: {
            type: Function,
            default: () => {
                return () => {};
            }
        },
        rowStyle: {
            type: Function,
            default: () => {
                return () => {};
            }
        },
        handleSummaryMethod: {
            type: Function,
            default: undefined
        }
    },
    computed: {
        filterClass() {
            return (command, filters) => {
                return filters.some((item) => item.isActive === true && item.value === command) ? 'is-active' : '';
            };
        }
    },
    methods: {
        // 多选选中或取消
        toggleRowSelection(row, flag) {
            this.$refs.ColumnTable.toggleRowSelection(row, flag);
        },
        // 取消全选
        clearSelection(row, flag) {
            this.$refs.ColumnTable.clearSelection();
        },
        //
        summaryMethod(param) {
            // 如果父组件有重构此方法，优先调用handleSummaryMethod，没有重构的情况继续执行
            if (typeof this.handleSummaryMethod === 'function') {
                return this.handleSummaryMethod(param);
            }
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                // 列名声明为isShowSummary时才显示合计
                if (column.labelClassName === 'isShowSummary') {
                    const values = data.map((item) => {
                        return Number(item[column.property]) || 0;
                    });
                    if (!values.every((value) => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            let value = Number(curr);
                            prev = !isNaN(Number(prev)) ? Number(prev) : 0;
                            if (!isNaN(value)) {
                                return Number((prev + curr).toFixed(2));
                            } else {
                                if (!isNaN(Number(prev))) {
                                    return Number(prev);
                                }
                                return 0;
                            }
                        }, 0);
                    } else {
                        sums[index] = '-';
                    }
                } else {
                    sums[index] = '-';
                }
            });
            return sums;
        },
        /**
         *  筛选的行
         * @param command
         * @param key
         * @param index
         */
        handleCommand(command, key, index) {
            let value = command;
            // 如果command不为空
            if (command !== '' && command != null && command.length !== 0) {
                // 如果不允许多选，重置所有isActive并且关闭isActive状态
                if (!this.columns[index].multiple) {
                    this.columns[index].isActive = false;
                    this.columns[index].filters.forEach((item) => {
                        item.isActive = false;
                    });
                }
                // 多选时切换isActive状态
                let i = this.columns[index].filters.findIndex((item) => item.value === command);
                if (i > -1) {
                    this.columns[index].filters[i].isActive = !this.columns[index].filters[i].isActive;
                }
                this.columns[index].isActive = this.columns[index].filters.some((item) => item.isActive);
            } else {
                // 重置isActive并关闭isActive状态
                this.columns[index].filters.forEach((item) => {
                    item.isActive = false;
                });
                this.columns[index].isActive = false;
            }

            // 如果是多选，获取所有isActive为true的value组成的数组
            if (this.columns[index].multiple) {
                value = this.columns[index].filters.filter((item) => item.isActive).map((item) => item.value);
            } else {
                // 如果不允许多选，直接返回当前选中的value
                value = command;
            }
            this.$emit('filter-click', value, key);
        },
        /**
         *   设置筛选的行
         * @param command
         * @param key
         */
        setFilterActive(command, key) {
            let index = this.columns.findIndex((item) => item.key === key);
            if (index > -1) {
                if (command === '' || command == null || command.length == 0) {
                    this.columns[index].filters = this.columns[index].filters.map((item) => {
                        return { name: item.name, value: item.value };
                    });
                    this.columns[index].isActive = false;
                    return false;
                }
                // 改变this.columns[index].filters中所有isActive
                if (!this.columns[index].multiple) {
                    this.columns[index].filters.forEach((item) => {
                        item.isActive = false;
                    });
                    let i = this.columns[index].filters.findIndex((item) => item.value === command);
                    if (i > -1) {
                        this.columns[index].filters[i].isActive = true;
                    }
                } else {
                    let i = this.columns[index].filters.findIndex((item) => command.indexOf(item[key]) > -1);
                    if (i > -1) {
                        this.columns[index].filters[i].isActive = true;
                    }
                }
                this.columns[index].isActive = true;
            }
        }
    }
};
</script>
<style scoped>
.ColumnTable {
    padding-bottom: 12px;
}
.department-table-style.ColumnTable {
    padding: 0;
}
.el-dropdown {
    line-height: 23px;
}
.el-dropdown img {
    margin: -2px 0;
}
.el-table .el-dropdown span {
    font-size: 12px;
    color: #333;
}
</style>
