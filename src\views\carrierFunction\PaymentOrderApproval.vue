<template>
    <div>
        <el-dialog v-model="visible" :close-on-click-modal="paymentOrderApprovalStatus === 'detail'" :show-close="paymentOrderApprovalStatus === 'detail'" :title="paymentOrderApprovalTitle" top="5vh" width="90%" @close="closeVisible">
            <el-form ref="paymentOrderApprovalForm" :model="paymentOrderApprovalForm" label-width="auto" style="max-height: 70vh">
                <el-descriptions :column="3" border size="small" title="申请信息">
                    <el-descriptions-item label="收款公司">
                        <span class="whitespace-nowrap">{{ formatDictionaryData('companyList', paymentOrderApprovalForm.paymentApplyDto.receiveCompany) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="汇款金额">
                        <template v-if="paymentOrderApprovalStatus === 'modify'">
                            <el-input-number v-model="paymentOrderApprovalForm.paymentApplyDto.remitAmount" :min="0" :precision="2" controls-position="right" placeholder="请输入汇款金额" style="width: 200px"></el-input-number>
                        </template>
                        <template v-else>
                            <span>{{ paymentOrderApprovalForm.paymentApplyDto.remitAmount }}</span>
                        </template>
                    </el-descriptions-item>
                    <el-descriptions-item label="汇款时间">
                        <span class="whitespace-nowrap">{{ paymentOrderApprovalForm.paymentApplyDto.remitTime }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item :span="5" label="付款凭证">
                        <template v-if="paymentOrderApprovalStatus === 'modify'">
                            <el-upload
                                :action="uploadFileUrl"
                                :file-list="paymentOrderApprovalForm.paymentApplyDto.remitFile"
                                :headers="headers"
                                :limit="9"
                                :on-preview="handlePictureCardPreview"
                                :on-remove="handleRemovePaymentOrderApproval"
                                :on-success="fileUploadSuccess"
                                accept="image/*"
                                list-type="picture-card"
                            >
                                <el-icon><Plus /></el-icon>
                            </el-upload>
                        </template>
                        <template v-else>
                            <el-image v-for="(item, index) in paymentOrderApprovalForm.paymentApplyDto.remitFile" :key="index" :preview-src-list="[item.url]" :src="item.url" class="mr-5" fit="cover" style="width: 100px; height: 100px" />
                        </template>
                    </el-descriptions-item>
                </el-descriptions>
                <div class="flex items-center my-5">
                    <el-descriptions class="mt-10" size="small" title="付款单明细"></el-descriptions>
                    <el-button v-if="paymentOrderApprovalStatus === 'modify'" class="ml-10" type="danger" @click="handleEditPaymentOrder">编辑付款单</el-button>
                </div>
                <column-table :columns="paymentOrderApprovalStatus === 'modify' ? modifyColumns : paymentOrderApprovalColumns" :data="paymentOrderApprovalForm.docList" :show-summary="true">
                    <template #companyName="{ row }">
                        {{ setCompanyName(row) }}
                    </template>
                    <template #paymentDocType="{ row }">
                        {{ formatDictionaryData('paymentDocTypeList', row.paymentDocType) }}
                    </template>
                    <template #startDate="{ row }"> {{ formatDate(row.startDate) }} ~ {{ formatDate(row.endDate) }} </template>
                    <template #discountType="{ row }">
                        {{ formatDictionaryData('discountTypeList', row.discountType) }}
                    </template>
                    <template #opt="{ row }">
                        <el-button link plain type="danger" @click="handleDeletePaymentOrder(row)">删除</el-button>
                    </template>
                </column-table>
                <div class="grid items-start" style="grid-template-columns: minmax(0, 1fr) minmax(0, 1fr); padding-bottom: 10px">
                    <div v-if="paymentOrderApprovalStatus === 'approval'" class="flex flex-col">
                        <el-descriptions size="small" title="审批">
                            <el-descriptions-item label="申请人">{{ paymentOrderApprovalForm.paymentApplyDto.createBy.name }}</el-descriptions-item>
                            <el-descriptions-item label="申请时间">{{ formatDate(paymentOrderApprovalForm.paymentApplyDto.createDate) }}</el-descriptions-item>
                        </el-descriptions>
                        <el-form-item :rules="[{ required: true, message: '请选择审批意见', trigger: 'change' }]" label="审批意见" prop="approveStatus">
                            <el-radio-group v-model="paymentOrderApprovalForm.approveStatus">
                                <el-radio label="1">通过</el-radio>
                                <el-radio label="2">驳回</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="paymentOrderApprovalForm.approveStatus === '2'" :rules="[{ required: true, message: '请输入驳回原因', trigger: 'blur' }]" label="驳回原因" prop="approveIdea">
                            <el-input v-model="paymentOrderApprovalForm.approveIdea" :rows="4" maxlength="500" placeholder="请输入驳回原因" show-word-limit type="textarea"></el-input>
                        </el-form-item>
                    </div>
                    <div v-else-if="paymentOrderApprovalStatus === 'detail' && paymentOrderApprovalForm.approveList && paymentOrderApprovalForm.approveList.length">
                        <div v-if="paymentOrderApprovalForm.approveList && paymentOrderApprovalForm.approveList.length">
                            <el-descriptions size="small" title="审批"></el-descriptions>
                            <audit-and-flow-records ref="AuditAndFlowRecords"></audit-and-flow-records>
                        </div>
                    </div>
                </div>
            </el-form>
            <template v-if="paymentOrderApprovalStatus === 'modify' || paymentOrderApprovalStatus === 'approval'" #footer>
                <div v-loading="paymentOrderApprovalLoading" style="text-align: center">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm()">提 交</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 付款单编辑弹窗 -->
        <el-dialog v-model="paymentOrderApprovalEditVisible" :close-on-click-modal="false" title="编辑付款单" top="5vh" width="90%" @close="closePaymentOrderApprovalEditVisible">
            <el-form ref="paymentOrderApprovalEditForm" :model="paymentOrderApprovalEditForm" label-width="auto" style="max-height: 70vh">
                <div class="flex" style="flex-wrap: wrap; gap: 10px">
                    <el-form-item label="收款单号" prop="paymentOrderNo">
                        <el-input v-model="paymentOrderApprovalEditForm.paymentOrderNo" clearable placeholder="请输入收款单号" style="width: 200px" @keyup.enter="handleSearchPaymentOrder" />
                    </el-form-item>
                    <el-form-item label="结算公司" prop="settlementCompanyName">
                        <el-input v-model="paymentOrderApprovalEditForm.settlementCompanyName" clearable placeholder="请输入结算公司" style="width: 200px" @keyup.enter="handleSearchPaymentOrder" />
                    </el-form-item>
                    <el-form-item label="账单时间" prop="billDate">
                        <el-date-picker v-model="paymentOrderApprovalEditForm.billDate" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleSearchPaymentOrder" />
                    </el-form-item>
                    <el-button class="ml-10" @click="handleResetPaymentOrder">重 置</el-button>
                    <el-button type="primary" @click="handleSearchPaymentOrder">搜 索</el-button>
                </div>

                <el-form-item label="排序方式" prop="sortType">
                    <el-radio-group v-model="paymentOrderApprovalEditForm.sortType" @change="handleSortTypeChange">
                        <el-radio label="default">默认排序</el-radio>
                        <el-radio label="selected">已选置顶</el-radio>
                    </el-radio-group>
                </el-form-item>

                <column-table ref="paymentOrderEditTable" v-loading="searchLoading" :columns="paymentOrderEditColumns" :data="paymentOrderApprovalEditForm.docList" :show-checkBox="true">
                    <template #discountType="{ row }">
                        {{ formatDictionaryData('discountTypeList', row.discountType) }}
                    </template>
                </column-table>
            </el-form>
            <template #footer>
                <div class="flex justify-center gap-2">
                    <el-button @click="closePaymentOrderApprovalEditVisible">取 消</el-button>
                    <el-button type="primary" @click="submitEditForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 汇款金额与收款单金额不匹配 弹窗组件 -->
        <mismatch-dialog v-if="mismatchDialogVisible" v-model="mismatchDialogVisible" :initial-form-data="mismatchFormData" @close="closeMismatchDialog" @submit="submitMismatchDialog" />
    </div>
</template>
<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import AuditAndFlowRecords from '@/views/auditManagement/AuditAndFlowRecords';
import tool from '@/utils/tool';
import paymentOrderApproval from '@/api/carrierEnd/paymentOrderApproval';
import { selectDictLabel } from '@/utils/dictLabel';
import { Close, Delete, Plus } from '@element-plus/icons-vue';
import paymentDoc from '@/api/shipperEnd/paymentDoc';
import moment from 'moment';
import MismatchDialog from '@/components/MismatchDialog.vue';

export default {
    name: 'PaymentOrderApproval',
    components: { MismatchDialog, AuditAndFlowRecords, ColumnTable, Close, Delete, Plus },
    model: {
        prop: 'paymentOrderApprovalVisible',
        event: 'update:paymentOrderApprovalVisible'
    },
    props: {
        paymentOrderApprovalId: {
            type: String,
            default: undefined
        },
        paymentOrderApprovalStatus: {
            type: String,
            default: undefined
        },
        paymentOrderApprovalTitle: {},
        paymentOrderApprovalVisible: {
            type: Boolean,
            default: false
        },
        queryType: {
            type: String,
            default: '2',
            validator: (value) => ['1', '2'].includes(value)
        }
    },
    data() {
        return {
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            paymentOrderApprovalForm: {
                approveStatus: undefined,
                approveIdea: undefined,
                approveList: [],
                docList: [],
                paymentApplyDto: {
                    receiveCompany: undefined,
                    remitAmount: undefined,
                    remitTime: undefined,
                    remitFile: [],
                    createBy: {
                        id: undefined,
                        name: undefined
                    },
                    createDate: undefined
                }
            },
            paymentOrderApprovalLoading: false,
            visible: this.paymentOrderApprovalVisible,
            viewerList: [],
            paymentOrderApprovalColumns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '付款类型', key: 'paymentDocType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'startDate', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '其他服务费', key: 'warehouseFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '纸箱费用', key: 'cartonFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '垫付费用', key: 'advanceFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '租箱费用', key: 'rentalBoxFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '其他费用', key: 'otherFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '费用说明', key: 'feeDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '坏账总金额', key: 'badDebtCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true }
            ],
            // 修改 modify 时显示的 Columns
            modifyColumns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'startDate', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '纸箱费用', key: 'cartonFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '垫付费用', key: 'advanceFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '租箱费用', key: 'rentalBoxFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '其他费用', key: 'otherFee', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '费用说明', key: 'feeDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
				{ title: '收款单未收合计', key: 'unpaidCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '坏账总金额', key: 'badDebtCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '150px', columnShow: true, labelClassName: 'isShowSummary' },
                { title: '操作员', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '调整日期', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', minWidth: '90px', columnShow: true, showOverflowTooltip: true, fixed: 'right' }
            ],
            companyList: [],
            discountTypeList: [],
            paymentOrderApprovalEditVisible: false,
            paymentOrderApprovalEditForm: {
                paymentOrderNo: undefined,
                settlementCompanyName: undefined,
                billDate: [
                    moment().startOf('month').format('YYYY-MM-DD'), // 当月第一天
                    moment().endOf('month').format('YYYY-MM-DD') // 当月最后一天
                ],
                sortType: 'default',
                docList: []
            },
            paymentOrderEditColumns: [
                { title: '收款单号', key: 'paymentOrderNo', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '货主公司', key: 'companyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settlementCompanyName', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '账单时间', key: 'billDate', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '合同费用合计', key: 'contractCost', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '折扣合计', key: 'discountCost', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '折扣方式', key: 'discountType', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '纸箱费用', key: 'cartonFee', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '垫付费用', key: 'advanceFee', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '租箱费用', key: 'rentalBoxFee', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '其他费用', key: 'otherFee', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '费用说明', key: 'feeDesc', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单应收合计', key: 'receivableCost', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单实收合计', key: 'paidCost', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '收款单未收合计', key: 'unpaidCost', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '坏账总金额', key: 'badDebtCost', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '调整费用合计', key: 'adjustCost', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销人', key: 'reversedName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '核销时间', key: 'reversedTime', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建人', key: 'createName', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true },
                { title: '创建时间', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true, showOverflowTooltip: true }
            ],
            searchLoading: false,
            paymentDocTypeList: [],
            mismatchDialogVisible: false,
            mismatchFormData: {
                selectedData: [],
                receivableCost: 0,
                unpaidCost: 0,
                remitAmount: 0
            }
        };
    },
    computed: {
        /**
         * 计算汇款总金额（合同费用合计的总和）
         */
        calculateTotalAmount() {
            return this.paymentOrderApprovalForm.docList
                .reduce((sum, item) => {
                    return sum + (Number(item.unpaidCost) || 0);
                }, 0)
                .toFixed(2);
        },
        /**
         * 格式化日期
         * @returns {function(*): *}
         */
        formatDate() {
            return (value) => {
                return moment(value).format('YYYY-MM-DD');
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 设置公司名称
         */
        setCompanyName() {
            return (row) => {
                const orgKey = this.$TOOL.data.get('orgKey');
                const Organization = this.$TOOL.data.get('Organization');
                const { carrierId, companyId } = row;
                if (carrierId == Organization[orgKey].id) {
                    return row.companyName;
                } else if (companyId == Organization[orgKey].id) {
                    return row.carrierName;
                }
            };
        }
    },
    watch: {
        /**
         * 监听付款单明细变化，自动更新汇款金额
         */
        'paymentOrderApprovalForm.docList': {
            handler() {
                if (this.paymentOrderApprovalStatus === 'modify') {
                    this.paymentOrderApprovalForm.paymentApplyDto.remitAmount = this.calculateTotalAmount;
                }
            },
            deep: true
        },
        paymentOrderApprovalVisible: {
            handler(val) {
                this.visible = val;
            },
            immediate: true
        }
    },
    created() {
        this.getDict();
        // 获取详情信息
        if (this.paymentOrderApprovalId) {
            this.getPaymentOrderApprovalDetail(this.paymentOrderApprovalId);
        }
    },
    methods: {
        /**
         * 关闭汇款金额与收款单金额不匹配 弹窗
         */
        closeMismatchDialog() {
            this.mismatchDialogVisible = false;
            this.mismatchFormData = {
                selectedData: [],
                receivableCost: 0,
                remitAmount: 0,
                unpaidCost: 0
            };
        },
        /**
         * 关闭编辑弹窗
         */
        closePaymentOrderApprovalEditVisible() {
            this.$refs.paymentOrderApprovalEditForm.resetFields();
            this.paymentOrderApprovalEditVisible = false;
            this.paymentOrderApprovalEditForm.docList = [];
        },
        /**
         * 关闭弹窗
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:paymentOrderApprovalVisible', false);
        },
        /**
         * 文件上传成功
         * @param res
         */
        fileUploadSuccess(res) {
            this.paymentOrderApprovalForm.paymentApplyDto.remitFile.push({
                name: res.data.fileName,
                url: res.data.fileUrl
            });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
            this.discountTypeList = await this.getDictList('discount_type');
            this.paymentDocTypeList = await this.getDictList('cost_payment_doc_type');
        },
        /**
         * 获取付款单审批详情
         */
        async getPaymentOrderApprovalDetail(id) {
            this.paymentOrderApprovalLoading = true;
            const res = await paymentOrderApproval.getPaymentOrderApprovalDetail({ id });
            if (res.code === 200) {
                // 统一处理 remitFile 数据格式
                if (res.data.paymentApplyDto.remitFile) {
                    res.data.paymentApplyDto.remitFile = res.data.paymentApplyDto.remitFile.map((item) => ({
                        name: item.fileName || item.name,
                        url: item.fileUrl || item.url
                    }));
                }
                this.paymentOrderApprovalForm = res.data;
                this.$nextTick(() => {
                    if (this.paymentOrderApprovalForm.approveList.length > 0) {
                        this.$refs.AuditAndFlowRecords.timeFns(this.paymentOrderApprovalForm.approveList);
                    }
                });
            }
            this.paymentOrderApprovalLoading = false;
        },
        /**
         * 获取未申请付款单数据
         */
        async getUnApplyPaymentOrder() {
            this.searchLoading = true;
            try {
                const params = {
                    queryType: this.queryType,
                    companyId: this.paymentOrderApprovalForm.paymentApplyDto.companyId,
                    paymentOrderNo: this.paymentOrderApprovalEditForm.paymentOrderNo,
                    settlementCompanyName: this.paymentOrderApprovalEditForm.settlementCompanyName,
                    startBillDate: this.paymentOrderApprovalEditForm.billDate?.[0] ? this.paymentOrderApprovalEditForm.billDate[0] + ' 00:00:00' : undefined,
                    endBillDate: this.paymentOrderApprovalEditForm.billDate?.[1] ? this.paymentOrderApprovalEditForm.billDate[1] + ' 23:59:59' : undefined
                };

                const res = await paymentOrderApproval.getUnApplyPaymentOrder(params);
                if (res.code === 200) {
                    this.paymentOrderApprovalEditForm.docList = res.data;
                }
            } finally {
                this.searchLoading = false;
            }
        },
        /**
         * 删除付款单
         */
        async handleDeletePaymentOrder(row) {
            // 将选中的数据从 paymentOrderApprovalForm.docList 中移除
            this.paymentOrderApprovalForm.docList = this.paymentOrderApprovalForm.docList.filter((item) => item.id !== row.id);
            // 将选中的数据从 paymentOrderApprovalEditForm.docList 中移除
            this.paymentOrderApprovalEditForm.docList = this.paymentOrderApprovalEditForm.docList.filter((item) => item.id !== row.id);
        },
        /**
         * 编辑付款单
         */
        async handleEditPaymentOrder() {
            this.paymentOrderApprovalEditVisible = true;
            // 获取未申请付款单数据
            await this.getUnApplyPaymentOrder();
        },
        /**
         * 图片预览
         * @param file
         */
        handlePictureCardPreview(file) {
            this.viewerList = [file.url || file];
        },
        /**
         * 文件上传移除
         * @param uploadFile
         */
        handleRemovePaymentOrderApproval(uploadFile) {
            this.paymentOrderApprovalForm.paymentApplyDto.remitFile = this.paymentOrderApprovalForm.paymentApplyDto.remitFile.filter((item) => item.url !== uploadFile.url);
        },
        /**
         * 重置付款单
         */
        handleResetPaymentOrder() {
            this.paymentOrderApprovalEditForm = {
                paymentOrderNo: undefined,
                settlementCompanyName: undefined,
                billDate: [
                    moment().startOf('month').format('YYYY-MM-DD'), // 重置为当月第一天
                    moment().endOf('month').format('YYYY-MM-DD') // 重置为当月最后一天
                ],
                sortType: 'default',
                docList: []
            };
            this.getUnApplyPaymentOrder();
        },
        /**
         * 搜索付款单
         */
        async handleSearchPaymentOrder() {
            // 验证账单时间是否已选择
            if (!this.paymentOrderApprovalEditForm.billDate || !this.paymentOrderApprovalEditForm.billDate.length) {
                this.$message.warning('数据量较大，请选择账单时间再进行搜索');
            } else {
                await this.getUnApplyPaymentOrder();
            }
        },
        handleSortTypeChange(val) {
            if (val === 'selected') {
                // 获取表格组件的选中数据
                const selectedRows = this.$refs.paymentOrderEditTable.$refs.ColumnTable.getSelectionRows();
                if (selectedRows && selectedRows.length > 0) {
                    // 获取未选中的数据
                    const unselectedRows = this.paymentOrderApprovalEditForm.docList.filter((item) => !selectedRows.some((selected) => selected.id === item.id));
                    // 将选中的数据放在前面，未选中的数据放在后面
                    this.paymentOrderApprovalEditForm.docList = [...selectedRows, ...unselectedRows];

                    // 在nextTick中重新设置选中状态
                    this.$nextTick(() => {
                        selectedRows.forEach((row) => {
                            this.$refs.paymentOrderEditTable.$refs.ColumnTable.toggleRowSelection(row, true);
                        });
                    });
                }
            } else {
                // 恢复默认排序
                const selectedRows = this.$refs.paymentOrderEditTable.$refs.ColumnTable.getSelectionRows();
                // 重新获取原始数据
                this.getUnApplyPaymentOrder().then(() => {
                    // 在数据加载完成后恢复选中状态
                    this.$nextTick(() => {
                        selectedRows.forEach((selectedRow) => {
                            const row = this.paymentOrderApprovalEditForm.docList.find((item) => item.id === selectedRow.id);
                            if (row) {
                                this.$refs.paymentOrderEditTable.$refs.ColumnTable.toggleRowSelection(row, true);
                            }
                        });
                    });
                });
            }
        },
        /**
         * 设置发票信息
         */
        setInvoiceData(val) {
            const { invoiceHead, taxNo, address, phone, openBank, bankAccount } = val;
            this.paymentOrderApprovalForm.paymentApplyDto.invoice = {
                invoiceHead,
                taxNo,
                address,
                phone,
                openBank,
                bankAccount
            };
        },
        /**
         * 提交编辑表单
         */
        submitEditForm() {
            // 获取选中的数据
            const selectedRows = this.$refs.paymentOrderEditTable.$refs.ColumnTable.getSelectionRows();

            // 过滤掉已存在的数据,只添加新选中的数据
            const newDocList = selectedRows.filter((selected) => !this.paymentOrderApprovalForm.docList.some((existing) => existing.id === selected.id));

            // 将新数据添加到现有列表中
            this.paymentOrderApprovalForm.docList = [...this.paymentOrderApprovalForm.docList, ...newDocList];

            this.closePaymentOrderApprovalEditVisible();
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.paymentOrderApprovalForm.validate(async (valid) => {
                if (valid) {
                    this.paymentOrderApprovalLoading = true;
                    // approval 审批
                    if (this.paymentOrderApprovalStatus === 'approval') {
                        let params = {
                            id: this.paymentOrderApprovalForm.paymentApplyDto.id,
                            payIdList: this.paymentOrderApprovalForm.docList.map((item) => item.id),
                            approveStatus: this.paymentOrderApprovalForm.approveStatus,
                            remark: this.paymentOrderApprovalForm.paymentApply.remark
                        };
                        if (this.paymentOrderApprovalForm.approveStatus === '2') {
                            params.approveIdea = this.paymentOrderApprovalForm.approveIdea;
                        }
                        const res = await paymentOrderApproval.approvalPaymentOrder(params);
                        if (res.code === 200) {
                            this.$message.success('操作成功');
                            this.$emit('getList');
                            this.closeVisible();
                        }
                    }
                    // modify 修改
                    else if (this.paymentOrderApprovalStatus === 'modify') {
                        // 检查汇款金额不能等于0
                        if (this.paymentOrderApprovalForm.paymentApplyDto.remitAmount === 0) {
                            this.$message.warning('汇款金额不能等于0');
                            this.paymentOrderApprovalLoading = false;
                            return;
                        }
                        // 检查付款单明细是否为空
                        if (!this.paymentOrderApprovalForm.docList || this.paymentOrderApprovalForm.docList.length === 0) {
                            this.$message.warning('请选择付款单明细');
                            this.paymentOrderApprovalLoading = false;
                            return;
                        }

                        // 收款单应收合计金额
                        this.mismatchFormData.receivableCost = this.paymentOrderApprovalForm.docList.reduce((total, item) => {
                            const receivableCost = Number(item.unpaidCost) || 0;
                            return Number((total + receivableCost).toFixed(2));
                        }, 0);
                        // 收款单未收合计金额
                        this.mismatchFormData.unpaidCost = this.paymentOrderApprovalForm.docList.reduce((total, item) => {
                            const unpaidCost = Number(item.unpaidCost) || 0;
                            return Number((total + unpaidCost).toFixed(2));
                        }, 0);
                        // 汇款合计金额
                        this.mismatchFormData.remitAmount = this.paymentOrderApprovalForm?.paymentApplyDto?.remitAmount;
                        this.mismatchFormData.selectedData = this.paymentOrderApprovalForm.docList;
                        this.mismatchDialogVisible = true;
                    }
                    this.paymentOrderApprovalLoading = false;
                }
            });
        },
        /**
         * 确认汇款金额与收款单金额不匹配
         */
        submitMismatchDialog(mismatchFormData) {
            // 创建一个新的remitFile数组,而不是直接修改原对象
            const remitFile = this.paymentOrderApprovalForm.paymentApplyDto.remitFile.map((item) => ({
                fileUrl: item.url,
                fileName: item.name
            }));

            let params = {
                applyWay: '2', // 申请方式 1-货主发起 2-承运商发起
                businessType: this.paymentOrderApprovalForm.paymentApplyDto?.businessType || '', // 业务类型 1-预存款充值 2-付款单支付 3-收款单收款
                id: this.paymentOrderApprovalForm.paymentApplyDto?.id || '',
                applyNo: this.paymentOrderApprovalForm.paymentApplyDto?.applyNo || '',
                companyId: this.paymentOrderApprovalForm.paymentApplyDto?.companyId || '',
                companyName: this.paymentOrderApprovalForm.paymentApplyDto?.companyName || '',
                carrierId: this.paymentOrderApprovalForm.paymentApplyDto?.carrier?.id || '',
                receiveCompany: this.paymentOrderApprovalForm.paymentApplyDto?.receiveCompany || '',
                remitAmount: this.paymentOrderApprovalForm.paymentApplyDto?.remitAmount || 0,
                remitTime: this.paymentOrderApprovalForm.paymentApplyDto?.remitTime || '',
                remitFile: JSON.stringify(remitFile),
                payDocList: mismatchFormData.selectedData.map((item) => ({
                    payId: item?.id || '',
                    paidCost: item?.paidCost || 0
                })),
                payMethod: this.paymentOrderApprovalForm.paymentApplyDto?.payMethod || '',
                remark: this.paymentOrderApprovalForm.paymentApplyDto?.remark || ''
            };
            paymentDoc.applyPayment(params).then((res) => {
                if (res.code === 200) {
                    this.$message.success('修改成功');
                    this.$emit('getList');
                    this.closeMismatchDialog();
                    this.closeVisible();
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__header) {
    margin-bottom: 10px;
}
:deep(.el-descriptions__cell.el-descriptions__label) {
    white-space: nowrap;
}
.number__unit__element {
    position: relative;
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
</style>
