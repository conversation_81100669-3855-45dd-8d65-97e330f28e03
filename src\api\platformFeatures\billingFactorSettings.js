import request from '@/utils/request'
export default {
	// 获取计费因子设置列表
	getBillingFactorList: function (params) {
		return request.get('/cost/billing/factor/list', params);
	},
	// 新增计费因子设置
    addBillingFactor: function (params) {
		return request.post('/cost/billing/factor/save', params);
    },
	// 修改状态
    updateBillingFactorStatus: function (params) {
		return request.get('/cost/billing/factor/updateStatus', params);
    },
	// 查询可用的计费因子列表
	billingFactorListByFormula: function (params) {
		return request.get('/cost/billing/factor/usable/list', params);
	}
}
