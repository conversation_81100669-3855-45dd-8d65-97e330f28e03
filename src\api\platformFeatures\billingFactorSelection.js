import request from '@/utils/request'
export default {
	// 所有承运商下拉框
    getCarrierList: function (params) {
        return request.get('/sys/org/orgSelect', params);
    },
    // 查询承运商已选择的计费因子和计费参数id
    billingFormulaDetail: function (params) {
        return request.get('/cost/billing/configParameters/selected', params);
    },
    // 保存承运商计费因子和计费参数
    addBillingFormula: function (params) {
        return request.post('/cost/billing/configParameters/save', params);
    }
}
