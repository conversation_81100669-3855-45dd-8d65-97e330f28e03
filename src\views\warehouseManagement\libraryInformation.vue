<template>
    <div>
        <!-- 搜索 -->
        <el-card class="box-card Botm">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                <el-form-item prop="wareHouse.id">
                    <el-select v-model="queryParams.wareHouseId" placeholder="请选择所属仓" clearable @change="handleSearch"
                        @clear="getList">
                        <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入库名称或简称快速查询" clearable class="form_225"
                        @clear="getList" @keydown.enter="handleSearch">
                        <template v-slot:suffix>
                            <el-icon @click="handleSearch">
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card style="margin:10px;">
            <el-button type="primary" @click="handleAdd(creatForm)" class="creatSpan">新增</el-button>
            <RightToptipBarV2 @handleRefresh="getList" className="libraryInformation" style="float:right;margin-top:10px" />
            <DragTableColumn :columns="columns" :tableData="warehouseList" className="libraryInformation"
                v-if="categoryOptions.length" v-model:queryParams="queryParams" :getList="getList">
                <template v-slot:operate="{ scopeData }">
                    <el-button link type="primary" @click="handleEdit(scopeData.row)">
                        <img src="@/assets/icons/update.png" style="margin-right:5px" />编辑</el-button>
                    <el-button link type="danger" @click="handleDelete(scopeData.row)"><img src="@/assets/icons/delete.png"
                            style="margin-right:5px" />删除</el-button>
                    <el-button link @click="handlerLog(scopeData.row)" style="color:#67c23a"><img
                            src="@/assets/icons/review.png" style="margin-right:5px" />操作记录</el-button>
                </template>
            </DragTableColumn>
            <div style="float: right;">
                <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                    @pagination="getList" />
            </div>
        </el-card>
        <!-- 新增的弹框 -->
        <el-dialog v-model="dialogFormVisible" width="40%" title="新增库信息" :before-close="() => handlerClose()">
            <el-form :model="dialogform" label-width="100px" :rules="rules" ref="creatForm">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="所属仓" prop="wareHouseId">
                            <el-select v-model="dialogform.wareHouseId" placeholder="请选择所属仓" style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库类别" prop="type">
                            <el-select v-model="dialogform.type" placeholder="请选择库类别" clearable style="width:100%;">
                                <el-option v-for="item in categoryOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库编码" prop="code">
                            <el-input v-model="dialogform.code" autocomplete="off" placeholder="请输入库编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库名称" prop="name">
                            <el-input v-model="dialogform.name" autocomplete="off" placeholder="请输入库名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库简称" prop="nameSimplified">
                            <el-input v-model="dialogform.nameSimplified" autocomplete="off" placeholder="请输入库简称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="dialogform.nameSimplifiedPinyin" autocomplete="off" placeholder="请输入库简拼" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="dialogform.remark" :rows="3" type="textarea" placeholder="请输入备注" />
                </el-form-item>
                <el-form-item label="库平面图" prop="plane">
                    <el-upload v-model:file-list="dialogform.plane" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item label="库CRT图" prop="crtPicture">
                    <el-upload v-model:file-list="dialogform.crtPicture" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="creat(creatForm)">确定</el-button>
            </div>
        </el-dialog>
        <!-- 编辑的弹框 -->
        <el-dialog v-model="editdialogFormVisible" width="40%" title="修改库信息" :before-close="() => handlerClose()">
            <el-form :model="editdialogform" label-width="100px" :rules="rules" ref="editcreatForm">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="所属仓" prop="wareHouseId">
                            <el-select v-model="editdialogform.wareHouseId" placeholder="请选择所属仓" style="width:100%;">
                                <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库类别" prop="type">
                            <el-select v-model="editdialogform.type" placeholder="请选择库类别" clearable style="width:100%;">
                                <el-option v-for="item in categoryOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库编码" prop="code">
                            <el-input v-model="editdialogform.code" autocomplete="off" placeholder="请输入库编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库名称" prop="name">
                            <el-input v-model="editdialogform.name" autocomplete="off" placeholder="请输入库名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="库简称" prop="nameSimplified">
                            <el-input v-model="editdialogform.nameSimplified" autocomplete="off" placeholder="请输入库简称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="库简拼" prop="nameSimplifiedPinyin">
                            <el-input v-model="editdialogform.nameSimplifiedPinyin" autocomplete="off"
                                placeholder="请输入库简拼" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="editdialogform.remark" :rows="3" type="textarea" placeholder="请输入备注" />
                </el-form-item>
                <el-form-item label="库平面图" prop="plane">
                    <el-upload v-model:file-list="editdialogform.plane" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove"
                        :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
                <el-form-item label="库CRT图" prop="crtPicture">
                    <el-upload v-model:file-list="editdialogform.crtPicture" :action="uploadUrl" :headers='headers'
                        list-type="picture-card" :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :before-upload="beforeUpload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="() => handlerClose()">取消</el-button>
                <el-button type="primary" @click="edit(editcreatForm)">确定</el-button>
            </div>
        </el-dialog>
        <logList :reviewVisible="reviewVisible" v-if="reviewVisible" :beforeClose="beforeClose_review" :data="reviewRow" />
        <viewImg v-if="uploadVisible" :visible="uploadVisible" :src="uploadViewImgUrl"
            :beforeClose="() => uploadVisible = false" />
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { Plus, Search } from '@element-plus/icons-vue'
import informationMaintenance from '@/api/erp/warehouseManagement/informationMaintenance'
import libraryInformation from '@/api/erp/warehouseManagement/libraryInformation'
import { ElMessage, ElMessageBox } from 'element-plus'
import logList from './logList.vue'
import tool from '@/utils/tool';
const { proxy } = getCurrentInstance();
const queryParams = reactive({
    current: 1,
    size: 10,
})
const selected = ref([])
const dialogFormVisible = ref(false)
const editdialogFormVisible = ref(false)
const reviewVisible = ref(false)
const uploadVisible = ref(false)
const editcreatForm = ref()
const uploadViewImgUrl = ref('')
const ids = ref('')
const dialogVisible = ref(false)
const dialogform = ref([])
const reviewRow = ref({})
const plane = ref([])
const crtPicture = ref([])
const editdialogform = ref([])
const warehouseList = ref([])
const warehouseOptions = ref([])
const categoryOptions = ref([])
const creatForm = ref()
const total = ref(0)
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const columns = ref(
    [
        {
            label: '所属仓简称',
            prop: 'wareHouse.nameSimplified',
        },
        {
            label: '库编码',
            prop: 'code'
        }, {
            label: '库名称',
            prop: 'name'
        }, {
            label: '库简称',
            prop: 'nameSimplified'
        }, {
            label: '库简拼',
            prop: 'nameSimplifiedPinyin'
        }
        , {
            label: '库类别',
            prop: 'type',
            type: 'status',
            filters: categoryOptions
        }, {
            label: '操作',
            prop: 'operate',
            type: 'operate',
            minWidth: 200,
        },
    ]
)
const rules = reactive({
    wareHouseId: [{ required: true, message: '请选择所属仓', trigger: 'blur' },],
    type: [{ required: true, message: '请选择库类别', trigger: 'blur' },],
    code: [{ required: true, message: '请输入库编码', trigger: 'blur' },],
    name: [{ required: true, message: '请输入库名称', trigger: 'blur' },],
    nameSimplified: [{ required: true, message: '请输入库简称', trigger: 'blur' },],
    nameSimplifiedPinyin: [{ required: true, message: '请输入库简拼', trigger: 'blur' },],
    plane: [{ required: true, message: '请上传仓库平面图', trigger: 'blur' },],
    crtPicture: [{ required: true, message: '请上传仓库CRT图', trigger: 'blur' },],
})
// 新增按钮
const handleAdd = (formEl) => {
    dialogFormVisible.value = true
    formEl.resetFields()
}
//搜索
const handleSearch = () => {
    getList()
}
//获取仓列表
function getwarehouseList() {
    informationMaintenance.list().then(res => {
        if (res.code == 200) {
            warehouseOptions.value = res.data.records

        }
    })
}
getwarehouseList()
// 获取库列表
function getList() {
    const { wareHouseId, ...rest } = queryParams;
    const queryParamsUpdated = {
        'wareHouse.id': wareHouseId,
        ...rest
    };
    libraryInformation.list({
        ...queryParamsUpdated
    }).then(res => {
        if (res.code == 200) {
            warehouseList.value = res.data.records
            total.value = res.data.total
        }
    })
}
getList()
// 新增库信息
const creat = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            const params = {
                ...dialogform.value,
            }
            params.commonFileDTOList = []
            if (params.plane) {
                params.plane.forEach((item) => {
                    params.commonFileDTOList.push({
                        fileType: '1',
                        fileName: item.response.data.name,
                        fileUrl: item.response.data.url
                    })
                })
                delete params.plane
            }
            if (params.crtPicture) {
                params.crtPicture.forEach((item) => {
                    params.commonFileDTOList.push({
                        fileType: '2',
                        fileName: item.response.data.name,
                        fileUrl: item.response.data.url
                    })
                })
                delete params.crtPicture
            }
            libraryInformation.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "保存成功",
                            type: "success",
                        });
                        dialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "添加失败，请稍后重试",
                        });
                    }
                })
        }
    });
};
// 关闭弹框
const handlerClose = () => {
    ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        dialogFormVisible.value = false
        editdialogFormVisible.value = false
    }).catch(() => {

    });
}
// 编辑按钮
const handleEdit = (row) => {
    editdialogFormVisible.value = true
    libraryInformation.detail({ id: row.id }).then(res => {
        if (res.code == 200) {
            editdialogform.value = res.data
            ids.valus = res.data.id
            editdialogform.value.wareHouseId = res.data.wareHouse.id
            console.log(res);
            editdialogform.value.plane = []
            editdialogform.value.crtPicture = []
            res.data.commonFileDTOList.forEach((item) => {
                if (item.fileType == '1') {
                    editdialogform.value.plane.push({
                        url: item.fileUrl,
                        name: item.fileName,
                        id: item.id
                    })
                } else {
                    editdialogform.value.crtPicture.push({
                        url: item.fileUrl,
                        name: item.fileName,
                        id: item.id
                    })
                }
            })
        }
    })
}
// 编辑请求
const edit = async (formEl) => {
    console.log();
    if (!formEl) return;
    await formEl.validate((valid) => {
        if (valid) {
            const params = {
                ...editdialogform.value,
            }
            params.commonFileDTOList = []
            editdialogform.value.plane.forEach((item) => {
                params.commonFileDTOList.push({
                    fileType: '1',
                    id: item.id,
                    fileName: item.name,
                    fileUrl: item.url
                })
            })
            editdialogform.value.crtPicture.forEach((item) => {
                params.commonFileDTOList.push({
                    fileType: '2',
                    id: item.id,
                    fileName: item.name,
                    fileUrl: item.url
                })
            })
            params.id = ids.valus
            console.log(params);
            libraryInformation.save(params)
                .then(res => {
                    if (res.code == 200) {
                        ElMessage({
                            message: "修改成功",
                            type: "success",
                        });
                        editdialogFormVisible.value = false
                        getList()
                    } else {
                        ElMessage({
                            type: "error",
                            message: "修改失败，请稍后重试",
                        });
                    }
                })
        }
    });
};
// 删除库
const handleDelete = (row) => {
    proxy.$confirm('是否确认删除此库信息?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
    }).then(() => {
        libraryInformation.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })
    }).catch(() => { });
}
// 操作日志请求
const handlerLog = (row) => {
    reviewVisible.value = true
    reviewRow.value = row
}
const beforeClose_review = () => {
    reviewVisible.value = false
}
const handlePictureCardPreview = (uploadFile) => {
    uploadViewImgUrl.value = uploadFile?.url
    uploadVisible.value = true
}
const handleRemove = (uploadFile, uploadFiles) => {
    console.log(uploadFile, uploadFiles)
}
const beforeUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
        ElMessage.error('只允许上传图片文件!')
    }
    return isImage;
}
// 字典
async function dict() {
    categoryOptions.value = await proxy.getDictList('library_type')
}
dict()
</script>

<style lang="scss" scoped>
.dialog {
    padding: 30px
}

::v-deep .Botm {
    margin: 10px;

    .el-card__body {
        padding-bottom: 0px
    }
}

.dialogP {
    position: relative;
    top: -5px;
    left: 100px
}
::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
    display: contents !important;
}
.creatSpan {
    margin-bottom: 10px;
}

.dialog-footer {
    display: flex;
    justify-content: end;
}
</style>
