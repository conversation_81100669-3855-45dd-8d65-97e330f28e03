{"name": "scui", "version": "1.6.9", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "vue-cli-service build --report", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@babel/helper-plugin-utils": "^7.22.5", "@element-plus/icons-vue": "2.3.1", "@pureadmin/utils": "^1.9.7", "@riophae/vue-treeselect": "0.4.0", "@tinymce/tinymce-vue": "5.0.0", "@vue-office/pdf": "^2.0.10", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "amfe-flexible": "^2.2.1", "axios": "1.3.4", "codemirror": "5.65.5", "compressorjs": "^1.2.1", "coordtransform": "^2.1.2", "core-js": "^3.32.0", "cropperjs": "1.5.13", "crypto-js": "4.1.1", "echarts": "^5.4.2", "element-china-area-data": "^6.0.2", "element-plus": "2.2.32", "element-ui": "^2.15.14", "html2canvas": "^1.4.1", "jquery": "^3.7.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.4", "nprogress": "0.2.0", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "sortablejs": "^1.15.0", "tinymce": "6.3.2", "tui-image-editor": "^3.15.3", "vue": "3.2.47", "vue-i18n": "9.2.2", "vue-router": "4.1.6", "vue-uuid": "^3.0.0", "vue3-seamless-scroll": "^2.0.1", "vuedraggable": "4.0.3", "vuex": "4.1.0", "xe-clipboard": "^1.10.2", "xgplayer": "2.32.2", "xgplayer-hls": "2.5.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "7.21.00", "@babel/eslint-parser": "7.19.1", "@vue/cli-plugin-babel": "5.0.8", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-service": "5.0.8", "eslint": "8.35.0", "eslint-plugin-vue": "9.9.0", "js-md5": "^0.7.3", "sass": "1.58.3", "sass-loader": "10.1.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "globals": {"APP_CONFIG": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"indent": 0, "no-tabs": 0, "no-mixed-spaces-and-tabs": 0, "vue/no-unused-components": 0, "vue/multi-word-component-names": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}