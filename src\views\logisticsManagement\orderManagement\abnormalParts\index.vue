<template>
    <div class="app-container">
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :label-width="isShowAll ? 'auto' : ''" :model="queryParams" class="seache-form">
                <el-form-item label="异常类型" style="width: 250px">
                    <el-select v-model="queryParams.type" clearable placeholder="请输入异常类型">
                        <el-option v-for="dict in abnormalPartList" :key="dict.dictValue" :label="dict.name" :value="dict.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="运单编号" style="width: 250px">
                    <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单编号" @keyup.enter="getList" />
                </el-form-item>
                <el-form-item label="登记时间" prop="queryTime" style="width: 305px">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button v-hasPermi="['fourpl:carrier:transOrder:abnormalGoods:insert']" icon="el-icon-plus" size="mini" type="primary" @click="openRegistrationDrawerFn">异常件登记 </el-button>
                <el-button v-hasPermi="['transport:driverTask:saveTaskHand']" icon="el-icon-plus" size="mini" type="warning" @click="handoverBatch()">导出</el-button>
            </div>
            <el-table v-loading="loading" :data="dataList" border>
                <el-table-column align="center" fixed label="序号" type="index" width="60"></el-table-column>
                <el-table-column :formatter="(row) => formDict(abnormalPartList, row.type)" align="center" label="异常类型" prop="type" width="110px"> </el-table-column>
                <el-table-column align="center" label="异常情况" prop="subType" width="220px">
                    <template v-slot="scope">
                        <div v-if="scope.row.type == 3">{{ abnormalPartChangeSituation(scope.row.subType) }}</div>
                        <div v-if="scope.row.type == 1 || scope.row.type == 2">{{ abnormalPartChange(scope.row.subType) }}</div>
                        <div v-if="scope.row.type == 4">{{ abnormalComponentCostItuation(scope.row.subType) }}</div>
                    </template>
                </el-table-column>
                <!-- <el-table-column align="center" label="订单编号" prop="org.id" width="220px"></el-table-column> -->
                <el-table-column align="center" label="运单号" prop="transOrderNo" width="160"></el-table-column>
                <el-table-column align="center" label="箱签码" prop="assetCode" width="160">
                    <template v-slot="scope">
                        <div v-if="scope.row.type == 1 || scope.row.type == 4">{{ scope.row.assetCode }}</div>
                        <div v-if="scope.row.type != 1 && scope.row.type != 4">--</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="货物描述" min-width="160" prop="goodsDesc"></el-table-column>
                <el-table-column align="center" label="保温箱号" prop="assetCode" width="160">
                    <template v-slot="scope">
                        <div v-if="scope.row.type == 2">{{ scope.row.assetCode }}</div>
                        <div v-if="scope.row.type != 2">--</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="传感器号" prop="assetCode" width="160">
                    <template v-slot="scope">
                        <div v-if="scope.row.type == 3">{{ scope.row.assetCode }}</div>
                        <div v-if="scope.row.type != 3">--</div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="任务所属人" prop="taskOwner" show-overflow-tooltip width="95px"></el-table-column>
                <el-table-column align="center" label="问题描述" prop="problemDesc" width="220px">
                    <template v-slot="scope">
                        <div class="m-flex">
                            <span class="m-row-text-clip">{{ scope.row.problemDesc }}</span>
                            <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="showDescriptionFn(scope.row.problemDesc)">详情</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="问题照片" prop="goodsPics" width="75px">
                    <template v-slot="scope">
                        <el-button v-if="scope.row.goodsPics" icon="el-icon-info-filled" link size="small" type="primary" @click="showPicsFn(scope.row.goodsPics)">查看</el-button>
                    </template>
                </el-table-column>
                <!-- <el-table-column align="center" label="审核状态" prop="reviewStatus">
                    <template v-slot="scope">
                        {{ examineType(scope.row.reviewStatus) }}
                    </template>
                </el-table-column> -->
                <el-table-column align="center" label="处理状态" prop="reviewStatus">
                    <template v-slot="scope">
                        {{ scope.row.reviewStatus == 0 ? '待处理' : scope.row.reviewStatus == 1 ? '处理中' : '已处理' }}
                    </template>
                </el-table-column>
                <el-table-column align="center" label="登记人" prop="createBy.name" width="160"></el-table-column>
                <el-table-column align="center" label="登记时间" prop="createDate" width="170px">
                    <template v-slot="scope">
                        {{ formatDate(scope.row.createDate) }}
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed="right" label="操作" width="200px">
                    <template v-slot="scope">
                        <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="detailsFn(scope.row)">详情</el-button>
                        <el-button v-if="scope.row.reviewStatus == 2" icon="el-icon-check" link size="small" type="success" @click="detailsFn(scope.row)">已处理</el-button>
                        <el-button v-if="scope.row.reviewStatus == 1" icon="el-icon-close" link size="small" type="danger" @click="handlingExceptions(scope.row)">处理</el-button>
                        <el-button v-if="scope.row.reviewStatus == 1" icon="el-icon-refresh" link size="small" type="warning" @click="recallRegistrFn(scope.row)">撤回</el-button>
                        <el-button v-if="scope.row.reviewStatus == 5" disabled icon="el-icon-close" link size="small">已撤回</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mt10" @pagination="getList" />
        </el-card>

        <!--  仅使用图片的查看大图功能  -->
        <el-image-viewer v-if="showImageViewer" :url-list="picsUrlList" @close="closeViewer" />

        <!--  描述详情对话框  -->
        <el-dialog v-model="showDesc" title="问题描述" width="40%">
            <template v-slot:title>
                <span style="color: #5670fe; background: #ffffff; font-weight: bold; font-size: 14px">问题描述</span>
            </template>
            <div style="padding: 0 25px; height: 350px; color: #807e7e; text-indent: 2em">
                <p>{{ description }}</p>
            </div>
        </el-dialog>

        <!-- 详情抽屉 -->
        <el-drawer v-model="openDrawer" :show-close="true" size="40vw" title="异常件详情" @close="closeDrawerFn">
            <div class="drawer-container">
                <el-descriptions :column="1" border>
                    <template #title>
                        <div class="drawer-title">
                            <el-icon><Document /></el-icon>
                            <span>订单情况</span>
                        </div>
                    </template>
                    <el-descriptions-item label="异常类型">
                        <el-tag :type="getTagType(form.type)">{{ formDict(abnormalPartList, form.type) }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.type == 1 || form.type == 2" label="异常情况">
                        <el-tag v-for="(item, index) in form.subType.split(',')" :key="index" class="mx-1" type="warning">
                            {{ abnormalPartChange(item) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.type == 3" label="异常情况">
                        <el-tag v-for="(item, index) in form.subType.split(',')" :key="index" class="mx-1" type="warning">
                            {{ abnormalPartChangeSituation(item) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.type == 4" label="异常情况">
                        <el-tag v-for="(item, index) in form.subType.split(',')" :key="index" class="mx-1" type="warning">
                            {{ abnormalComponentCostItuation(item) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.type == 1 || form.type == 4" label="运单编号">
                        <el-text class="mx-1" type="primary">{{ form.transOrderNo }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.type == 2" label="保温箱编号">
                        <el-text class="mx-1" type="primary">{{ form.assetCode }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.type == 3" label="传感器编号">
                        <el-text class="mx-1" type="primary">{{ form.assetCode }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.type == 1 || form.type == 4" label="货物描述">
                        <el-text class="mx-1">{{ form.goodsDesc }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="问题描述">
                        <div class="problem-desc">
                            {{ form.problemDesc }}
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="货物图片">
                        <div class="image-container">
                            <el-image 
                                v-for="url in detailsList" 
                                :key="url" 
                                :preview-src-list="detailsList" 
                                :src="url" 
                                class="preview-image"
                                fit="cover"
                            >
                                <template #error>
                                    <div class="image-error">
                                        <el-icon><Picture /></el-icon>
                                    </div>
                                </template>
                            </el-image>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="任务所属人">
                        <el-text class="mx-1">{{ form.taskOwner }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="登记人">
                        <el-text class="mx-1">{{ form.createBy.name }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="登记时间">
                        <el-text class="mx-1">{{ formatDate(form.createDate) }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item v-if="form.reviewStatus == 2" label="处理结果">
                        <div class="review-comments">
                            {{ form.reviewComments }}
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-drawer>

        <!--  新增抽屉  -->
        <el-drawer v-model="openInsertDrawer" size="600px" title="异常件登记" @close="closeDrawerFn">
            <div class="p-10" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <div class="form-section">
                        <div class="section-header">
                            <div style="color: #f56c6c">*</div>
                            <span>异常类型</span>
                        </div>
                        <div class="section-content">
                            <el-radio-group v-model="radio">
                                <el-radio-button v-for="(item, index) in abnormalPartList" :key="index" :label="item.name">{{ item.name }}</el-radio-button>
                            </el-radio-group>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="section-header">
                            <div style="color: #f56c6c">*</div>
                            <span>异常情况</span>
                        </div>
                        <div class="section-content">
                            <el-checkbox-group v-model="checkboxGroup2" style="margin-bottom: 15px">
                                <div v-if="radio == '货物异常' || radio == '保温箱异常'" class="checkbox-container">
                                    <el-checkbox-button v-for="(item, index) in abnormalConditionAbnormalPartsList" :key="index" :label="item.code" class="custom-checkbox">
                                        {{ item.name }}
                                    </el-checkbox-button>
                                </div>
                                <div v-if="radio == '传感器异常'" class="checkbox-container">
                                    <el-checkbox-button v-for="(item, index) in abnormalComponentSensorAbnormalSituationList" :key="index" :label="item.code" class="custom-checkbox">
                                        {{ item.name }}
                                    </el-checkbox-button>
                                </div>
                                <div v-if="radio == '件数费用异常'" class="checkbox-container">
                                    <el-checkbox-button v-for="(item, index) in abnormalComponentCostItuationList" :key="index" :label="item.code" class="custom-checkbox">
                                        {{ item.name }}
                                    </el-checkbox-button>
                                </div>
                            </el-checkbox-group>
                            <el-form ref="form" :model="form" :rules="rules" label-width="auto">
                                <el-form-item v-if="radio == '货物异常' || radio == '件数费用异常'" label="箱签码" prop="assetCode">
                                    <el-autocomplete
                                        v-model.trim="form.assetCode"
                                        :fetch-suggestions="queryBoxFn"
                                        :style="{ width: '100%' }"
                                        :trigger-on-focus="false"
                                        clearable
                                        highlight-first-item
                                        placeholder="请输入箱签码"
                                        value-key="code"
                                        @change="codeChangeFn"
                                        @select="handleSelectFn"
                                    >
                                        <template #default="{ item }">
                                            <div class="m-flex-between">
                                                <span class="addr">{{ item.code }}</span>
                                                <span style="color: #aaa; margin-left: 20px"> {{ item.codeDesc }}</span>
                                            </div>
                                        </template>
                                    </el-autocomplete>
                                </el-form-item>
                                <el-form-item v-if="radio == '货物异常' || radio == '件数费用异常'" label="货物描述" prop="goodsDesc">
                                    <el-input v-model.trim="form.goodsDesc" clearable disabled placeholder="填写箱码后自动填写" />
                                </el-form-item>
                                <el-form-item v-if="radio == '保温箱异常'" label="保温箱号" prop="assetCode">
                                    <el-input v-model.trim="form.assetCode" clearable placeholder="请输入保温箱号" />
                                </el-form-item>
                                <el-form-item v-if="radio == '传感器异常'" label="传感器号" prop="assetCode">
                                    <el-input v-model.trim="form.assetCode" clearable placeholder="请输入传感器号" />
                                </el-form-item>
                                <el-form-item v-if="radio == '货物异常' || radio == '件数费用异常'" label="运单编号" prop="transOrderNo">
                                    <el-input v-model.trim="form.transOrderNo" clearable disabled placeholder="填写箱码后自动填写" />
                                </el-form-item>
                                <el-form-item label="问题描述" prop="problemDesc">
                                    <el-input v-model="form.problemDesc" clearable maxlength="200" placeholder="请输入问题描述" type="textarea" />
                                </el-form-item>
                                <el-form-item label="任务所属人" prop="taskOwner">
                                    <el-input v-model="form.taskOwner" clearable maxlength="20" placeholder="请输入任务所属人" />
                                </el-form-item>
                                <el-form-item label="问题件照片" prop="goodsPics">
                                    <el-upload
                                        :action="uploadFileUrl"
                                        :file-list="fileList"
                                        :headers="headers"
                                        :limit="9"
                                        :on-exceed="exceed"
                                        :on-preview="handlePictureCardPreview"
                                        :on-remove="removeFileFn"
                                        :on-success="uploadSuccessFn"
                                        accept="image/png, image/jpeg"
                                        list-type="picture-card"
                                        multiple
                                    >
                                        <el-icon><Plus /></el-icon>
                                    </el-upload>
                                    <el-dialog v-model="dialogVisible" :modal="false">
                                        <img :src="dialogImageUrl" alt="Preview Image" style="max-width: 100%" w-full />
                                    </el-dialog>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                </el-card>
                <div class="flex justify-end mt-10">
                    <el-button @click="submitCancelFn">取 消</el-button>
                    <el-button type="primary" @click="submitFormFn">提 交</el-button>
                </div>
            </div>
        </el-drawer>
        <el-dialog v-model="handlingOpen" :before-close="handleClose" title="处理意见" width="30%">
            <el-input v-model="handlingValue" :rows="2" maxlength="200" placeholder="请输入处理意见" type="textarea" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button
                        @click="
                            handlingOpen = false;
                            handlingValue = [];
                        "
                        >取消</el-button
                    >
                    <el-button type="primary" @click="determineProcessing"> 确定 </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import orderManagementindexApi from '@/api/logisticsManagement/orderManagementindex';
import tool from '@/utils/tool';
import moment from 'moment/moment';
import { selectDictLabel } from '@/utils/dictLabel';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import { Plus, Document, Picture } from '@element-plus/icons-vue';

export default {
    name: 'AbnormalGoods',
    components: { SearchButton, Plus, Document, Picture },
    filters: {
        timeFilter(val) {
            return val ? moment(val).format('YYYY-MM-DD') : '--';
        }
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 表单信息
            form: {},
            rules: {
                description: [{ required: true, message: '请填写异常描述', trigger: 'blur' }],
                goodsDesc: [{ required: true, message: '请填写货物箱码', trigger: 'blur' }],
                problemDesc: [{ required: true, message: '货物描述不能为空', trigger: 'blur' }],
                transOrderNo: [{ required: true, message: '运单编号不能为空', trigger: 'blur' }],
                // taskOwner: [{ required: true, message: '任务所属人不能为空', trigger: 'blur' }],
                goodsPics: [{ required: true, message: '请上传异常图片', trigger: 'blur' }],
                assetCode: [{ required: true, message: '请按要求填写', trigger: 'blur' }]
            },
            // 展示表格信息
            dataList: [],
            // 页面所需字典信息
            dictMap: {},
            // 查询条件
            queryParams: {
                queryTime: [],
                current: 1,
                size: 10
            },
            // 记录条数
            total: 0,
            // 显示搜索条件
            showSearch: true,
            // 打开登记详情抽屉
            openDrawer: false,
            // 打开新增登记信息抽屉
            openInsertDrawer: false,
            // 抽屉标题
            drawerTitle: '',
            // 图片地址数组
            picsUrlList: [],
            picsUrl: '',
            previewsImgUrl: '',
            showImageViewer: false,
            // 展示问题描述弹窗
            showDesc: false,
            // 设置当前问题描述内容
            description: '',
            // 上传图片token
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            fileList: [],
            fileType: ['png', 'jpg', 'jpeg'],
            goodsPicsList: [],
            timeLineData: [],
            radio: '',
            checkboxGroup2: [],
            detailsList: [],
            abnormalList: [
                {
                    'id': 1,
                    'code': '货物异常'
                },
                {
                    'id': 1,
                    'code': '货物异常'
                },
                {
                    'id': 1,
                    'code': '货物异常'
                }
            ],
            handlingOpen: false,
            handlingValue: '', //处理意见
            abnormalId: '',
            abnormalPartList: [],
            abnormalConditionAbnormalPartsList: [],
            abnormalComponentSensorAbnormalSituationList: [],
            abnormalComponentCostItuationList: [],
            shortcuts: [
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ],
            dialogImageUrl: '',
            dialogVisible: false
        };
    },
    created() {
        this.queryParams.queryTime = [moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')];
        this.getDict();
        this.getList();
        // 默认设置七天
    },
    methods: {
        //传感器异常情况字典值处理
        abnormalComponentCostItuation(val) {
            var arr = [];
            var array = val.split(',');
			array.map((item) => {
				let item2 = selectDictLabel(this.abnormalComponentCostItuationList, item);
				return arr.push(item2);
			});
			return arr.join(',');
        },
        //异常情况字典值处理
        abnormalPartChange(val) {
            var arr = [];
            var array = val.split(',');
			array.map((item) => {
				let item2 = selectDictLabel(this.abnormalConditionAbnormalPartsList, item);
				return arr.push(item2);
			});
			return arr.join(',');
        },
        //传感器异常情况字典值处理
        abnormalPartChangeSituation(val) {
            var arr = [];
            var array = val.split(',');
			array.map((item) => {
				let item2 = selectDictLabel(this.abnormalComponentSensorAbnormalSituationList, item);
				return arr.push(item2);
			});
			return arr.join(',');
        },
        /** 关闭抽屉方法 */
        closeDrawerFn() {
            this.openDrawer = false;
            this.drawerTitle = '';
            this.form = {};
        },
        // 关闭查看器
        closeViewer() {
            this.showImageViewer = false;
        },
        codeChangeFn() {
            this.form.goodsDesc = '';
            this.form.transOrderNo = '';
        },
        /** 查看详情 */
        detailsFn(data) {
            this.openDrawer = true;
            orderManagementindexApi
                .abnormalGoodsQueryById({
                    id: data.id
                })
                .then((res) => {
                    if (res.code == 200) {
                        this.form = res.data;
                        var array = this.form.goodsPics.split(',');
                        // 图片路径已经是完整的,不需要拼接
                        this.detailsList = array;
                    }
                });
        },
        // 确定处理意见
        determineProcessing() {
            if (this.handlingValue) {
                this.handlingOpen = false;
                orderManagementindexApi
                    .getUpdateReviewStatus({
                        id: this.abnormalId,
                        reviewStatus: '2',
                        processStatus: '1',
                        reviewComments: this.handlingValue
                    })
                    .then((res) => {
                        if (res.code === 200) {
                            this.handlingValue = [];
                            this.$message.success('操作成功');
                            this.getList();
                            this.openInsertDrawer = false;
                        }
                    });
            } else {
                this.$message.error('请填写处理意见');
            }
        },
        examineType(val) {
            // 1：待审核， 2：审核中 3：已拒绝 4：已通过 5：撤回",
            if (val == 1) {
                return '待审核';
            } else if (val == 2) {
                return '审核中';
            } else if (val == 3) {
                return '已拒绝';
            } else if (val == 4) {
                return '已通过';
            } else if (val == 5) {
                return '撤回';
            }
        },
        exceed() {
            this.msgError('最大上传个数为9个');
        },
        //字典回显
        formDict(data, val) {
            return selectDictLabel(data, val);
        },
        formatDate(val) {
            return val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--';
        },
        /**
         * 获取字典值
         */
        async getDict() {
            this.abnormalPartList = await this.getDictList('abnormal_part_type');
            this.abnormalConditionAbnormalPartsList = await this.getDictList('abnormal_condition_abnormal_parts');
            this.abnormalComponentSensorAbnormalSituationList = await this.getDictList('abnormal_component_sensor_abnormal_situation');
            this.abnormalComponentCostItuationList = await this.getDictList('abnormal_Component_Cost_ituation');
        },
        /** 查询表格内容 */
        getList() {
            this.loading = true;
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0) {
                this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = '';
                this.queryParams.endCreateDate = '';
            }
            // eslint-disable-next-line no-unused-vars
            const { queryTime, ...params } = this.queryParams;
            orderManagementindexApi
                .abnormalGoodsList(params)
                .then((res) => {
                    if (res.code == 200) {
                        this.dataList = res.data.records;
                        this.total = res.data.total;
                        this.loading = false;
                        // }
                    }
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url || file.response.data.fileUrl;
            this.dialogVisible = true;
        },
        /** 搜索按钮操作 */
        handleQuery() {
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0) {
                this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = '';
                this.queryParams.endCreateDate = '';
            }
            this.queryParams.pageNum = 1;
            this.getList();
        },
        handleSelectFn(item) {
            this.form.goodsDesc = item.codeDesc;
            this.form.transOrderNo = item.transOrderNo;
        },

        /** 处理异常 */
        handlingExceptions(val) {
            this.abnormalId = val.id;
            this.handlingOpen = true;
        },
        // 导出
        handoverBatch() {
            orderManagementindexApi
                .abnormalGoodsExport('', '', '', 'blob')
                .then((res) => {
                    var debug = res;
                    if (debug) {
                        var elink = document.createElement('a');
                        elink.download = '异常记录.xlsx';
                        elink.style.display = 'none';
                        var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                        elink.href = URL.createObjectURL(blob);
                        document.body.appendChild(elink);
                        elink.click();
                        document.body.removeChild(elink);
                    } else {
                        this.$message.error('导出异常请联系管理员');
                    }
                })
                .catch((err) => {
                    this.$message.error(err.msg);
                });
        },
        /** 打开异常件登记抽屉 */
        openRegistrationDrawerFn() {
            this.radio = '';
            this.checkboxGroup2 = [];
            this.fileList = [];
            // this.reset();
            this.openInsertDrawer = true;
        },
        queryBoxFn(code, cb) {
            this.form.goodsDesc = '';
            this.form.transOrderNo = '';
            orderManagementindexApi
                .getBoxCodeList({
                    code: code
                })
                .then((res) => {
                    if (res.code == 200) {
                        if (!res.data.records) {
                            cb([]);
                            return this.$message.error('不存在此箱码货物信息，请检查箱码是否正确');
                        }
                        cb(res.data.records);
                    }
                });
        },
        /** 撤回登记信息 */
        recallRegistrFn(data) {
            if (data.reviewStatus == 1) {
                this.$confirm('确定撤回登记信息？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        orderManagementindexApi
                            .getUpdateReviewStatus({
                                id: data.id,
                                reviewStatus: '5'
                            })
                            .then((res) => {
                                if (res.code === 200) {
                                    this.$message.success('撤回成功');
                                    this.getList();
                                }
                            });
                    })
                    .catch(() => {});
            }
        },
        /** 删除列表文件 */
        removeFileFn(file, fileList) {
            this.fileList = fileList;
            this.form.goodsPics = fileList.map((o) => o.response.data.fileUrl);
        },
        reset() {
            this.form = {
                goodsDesc: '',
                description: '',
                code: '',
                transOrderNo: '',
                goodsPics: []
            };
            this.fileList = [];
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.queryParams = {
                pageNum: 1,
                size: 10
            };
            this.handleQuery();
        },
        /** 查看问题描述 */
        showDescriptionFn(desc) {
            this.showDesc = true;
            this.description = desc;
        },
        /** 查看大图 */
        showPicsFn(pics) {
            var array = pics.split(',');
            for (var i = 0; i < array.length; i++) {
                array[i] = process.env.VUE_APP_API_fileUrl + '/' + array[i];
            }
            this.picsUrl = array[0];
            this.picsUrlList = array;
            this.showImageViewer = true;
            // this.$refs.previewImg.clickHandler();
        },
        /** 取消提交数据 */
        submitCancelFn() {
            this.openInsertDrawer = false;
            this.reset();
        },
        /** 提交数据 */
        submitFormFn() {
            // 1: 货物异常 2:保温箱异常 3:传感器异常
            let type = '';
            if (this.radio) {
                if (this.radio == '货物异常') {
                    type = 1;
                } else if (this.radio == '保温箱异常') {
                    type = 2;
                } else if (this.radio == '传感器异常') {
                    type = 3;
                } else if (this.radio == '件数费用异常') {
                    type = 4;
                }
                this.form.type = type;
            } else {
                this.msgError('请选择异常类型');
                return false;
            }
            if (this.checkboxGroup2.length === 0) {
                this.msgError('请选择异情况');
                return false;
            } else {
                var idStr = this.checkboxGroup2.map((v) => v);
                //拼接的数组字符串，接口传参
                this.form.subType = idStr.join(',');
            }
            this.form.org = this.$TOOL.data.get('ROLE_LIST')[0].sysOrg.id;
            this.form.goodsOwnerVisible = '1';
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    orderManagementindexApi.abnormalGoodsSave(this.form).then((res) => {
                        if (res.code === 200) {
                            this.$message.success('操作成功');
                            this.getList();
                            this.openInsertDrawer = false;
                        }
                    });
                }
            });
        },
        /** 上传异常件成功回调 */
        uploadSuccessFn(response, file, fileList) {
            this.fileList = fileList;
            var idStr = this.fileList.map((v) => v.response.data.fileUrl);
            //拼接的数组字符串，接口传参
            this.form.goodsPics = idStr.join(',');
        },
        /**
         * 根据异常类型获取标签类型
         * @param {number} type 异常类型
         * @returns {string} 标签类型
         */
        getTagType(type) {
            const typeMap = {
                1: 'danger',
                2: 'warning',
                3: 'info',
                4: 'success'
            }
            return typeMap[type] || 'info'
        }
    }
};
</script>

<style scoped>
.m-row-text-clip {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 180px;
    display: inline-block;
}

.m-flex {
    display: flex;
    align-items: center;
}

.m-flex-between {
    display: flex;
    justify-content: space-between;
}

.m-title {
    color: #027afe;
}

.m-drawer-inner-container {
    margin: 0 50px;
}

::v-deep .m-label {
    text-align: right;
    color: #aaaaaa;
    width: 90px;
    display: flex;
    justify-content: flex-end;
    margin: 0 20px;
}

::v-deep .m-content {
    color: #4c4c4c;
}

::v-deep .el-dialog__header {
    background: #ffffff;
}

::v-deep .m-finished .el-timeline-item__timestamp {
    color: green;
}

::v-deep .m-active .el-timeline-item__timestamp {
    color: #0f87ff;
}

::v-deep .m-finished .el-timeline-item__tail {
    border-color: green;
}

.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.checkbox-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

::v-deep .custom-checkbox {
    width: 100%;
    margin: 0;
    border-radius: 0;
    border: 1px solid #dcdfe6;
    position: relative;
    overflow: visible;
}

::v-deep .custom-checkbox.is-checked {
    border-color: #2a76f8;
}

::v-deep .custom-checkbox.is-checked::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 20px 23px;
    border-color: transparent transparent #2a76f8 transparent;
    transform: translate(1px, 1px);
}

::v-deep .custom-checkbox.is-checked::before {
    content: '✓';
    position: absolute;
    right: 1%;
    bottom: 5px;
    font-size: 12px;
    color: #ffffff;
    z-index: 1;
}

::v-deep .el-checkbox-button__inner {
    width: 100%;
    border: none;
    padding: 8px 20px 8px 15px;
    border-radius: 0;
    white-space: normal;
    height: auto;
    line-height: 1.4;
    text-align: left;
}

::v-deep .el-checkbox-button.is-checked .el-checkbox-button__inner {
    background-color: #ecf5ff;
    color: #2a76f8;
    box-shadow: none;
}

::v-deep .el-checkbox-button:first-child .el-checkbox-button__inner {
    border-radius: 0;
    border-left: none;
}

.p-10 {
    padding: 10px;
}

.mt-10 {
    margin-top: 10px;
}

.flex {
    display: flex;
}

.justify-end {
    justify-content: flex-end;
}

::v-deep .el-drawer__header {
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #dcdfe6;
}

::v-deep .el-drawer__body {
    padding: 0;
}

::v-deep .el-card {
    border: none;
}

::v-deep .el-card__header {
    padding: 10px 20px;
    border-bottom: none;
}

::v-deep .section-content .el-form-item {
    margin-bottom: 18px;
}

.box-card + .box-card {
    margin-top: 10px;
}

.form-section {
    padding: 0 20px;
}

.form-section:first-child {
    padding-top: 0;
}

.form-section + .form-section {
    margin-top: 10px;
}

.section-header {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 15px;
}

.section-header span {
    font-size: 14px;
}

::v-deep .el-card__body {
    padding: 10px 0;
}

::v-deep .el-radio-button__inner {
    padding: 8px 15px;
}

.drawer-container {
    padding: 20px;
}

.drawer-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

.drawer-title .el-icon {
    font-size: 20px;
    color: #409EFF;
}

.problem-desc {
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    line-height: 1.6;
    color: #606266;
}

.image-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    padding: 10px 0;
}

.preview-image {
    width: 100px;
    height: 100px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.3s;
}

.preview-image:hover {
    transform: scale(1.05);
}

.image-error {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #f5f7fa;
    color: #909399;
}

.review-comments {
    padding: 12px;
    background-color: #f0f9eb;
    border-radius: 4px;
    line-height: 1.6;
    color: #67c23a;
}

.mx-1 {
    margin: 0 4px;
}

:deep(.el-descriptions__label) {
    width: 120px;
    color: #606266;
    font-weight: 500;
}

:deep(.el-descriptions__content) {
    color: #303133;
}

:deep(.el-tag) {
    margin-right: 8px;
    margin-bottom: 4px;
}
</style>
