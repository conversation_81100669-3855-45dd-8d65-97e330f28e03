<template>
  <div>
    <el-table :data="formulaListData" :stripe="false" border style="margin: 0">
      <el-table-column align="center" label="条件公式编号" prop="formulaCode" width="208">
        <template slot-scope="scope">
          <span>{{ scope.row.formulaCode }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="条件" prop="conditions">
        <template slot-scope="scope">
          <span>{{ TheFormulaShowsFormatting(scope.row.conditions) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="公式" prop="formula">
        <template slot-scope="scope">
          <span>{{ TheFormulaShowsFormatting(scope.row.formula) }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>

export default {
  name: 'CustomFormulaSelection',
  props: {
    formulaSource: {
      type: String,
      default: ''
    },
    operatorList: {
      type: Array,
      default: () => []
    },
    parameterList: {
      type: Array,
      default: () => []
    },
    props: {}
  },
  data() {
    return {
      formulaListData: [],
      otherExpenseList: [],
    };
  },
  created() {
    // 其他费用 字典
    // this.getDicts('cost_formula_other_param').then((response) => {
    //   this.otherExpenseList = response.data;
    // });
  },
  methods: {
    // 公式显示 格式化
    TheFormulaShowsFormatting(val) {
      const dataArr = val.split(/(\+|-|\*|\/|\(|\)|&|\||=|!|>|<)/);
      dataArr.forEach((item, index) => {
        if (item.indexOf('@') > -1) {
          const itemArr = item.split('@');
          const id = itemArr[0];
          const type = itemArr[1];
          if (type === 'Var') {
            const variableName = this.operatorList.find((item) => item.id === id)?.variableName || '';
            dataArr[index] = { id, type, value: variableName };
          } else if (type === 'Param') {
            const parameterName = this.parameterList.find((item) => item.id === id)?.variableName || '';
            dataArr[index] = { id, type, value: parameterName };
          }
        } else if (item.indexOf('#') > -1) {
          const otherExpense = this.otherExpenseList.find((item1) => item1.code === item);
          dataArr[index] = { id: otherExpense?.code || '', type: 'Other', value: otherExpense?.name || '' };
        } else {
          dataArr[index] = { type: '', value: item };
        }
      });
      // dataArr value 拼接
      return dataArr.map((item) => item.value).join('');
    },
  }
};
</script>
<style lang="scss" scoped>
::v-deep {
  .el-dialog__header {
    padding-bottom: 20px;
  }
  .el-result {
    padding: 0;
  }
  .el-drawer__header {
    margin-bottom: 20px;
  }
  .el-input.is-disabled .el-input__inner {
    color: #666666;
    background-color: #f5f7fa;
  }
  .el-table {
    border: none;
  }
}

.billingFormulaForm {
  .billingFormulaForm__radio {
    .el-radio--small.is-bordered {
      height: auto;
      width: 100%;
      display: flex;
      align-items: center;
      padding: 16px;

      > :nth-child(1) {
        display: none;
      }
    }
  }
  .billingFormulaForm__radio__condition {
    .el-radio--small.is-bordered {
      height: auto;
      width: 100%;
      display: flex;
      align-items: center;
      padding: 8px;

      > :nth-child(1) {
        display: none;
      }
    }
  }
}

.billingFormulaForm__radio,
.billingFormulaForm__radio__condition {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .el-radio.is-bordered + .el-radio.is-bordered {
    margin-left: 0;
  }

  ::v-deep .el-radio__label {
    display: flex;
    padding-left: 0;
    align-items: center;
    gap: 20px;
    flex: 1;

    > :nth-child(1) {
      flex: 1;
    }
  }
}

.tag__box {
  display: flex;
  gap: 10px;
}
.tag__m {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
  padding: 0 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 13px;
}
.box__grid__parameter {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  button {
    margin-left: 0;
  }
}
.btn__revoke {
  margin-left: auto;
  color: #5670fe;
  cursor: pointer;
}
</style>
