<template>
    <div>
        <div>
            <!-- 搜索 -->
            <el-card class="box-card Botm">
                <el-form :model="queryParams" ref="queryForm" :inline="true" class="form_130">
                    <el-form-item prop="warehouse">
                        <el-input v-model="queryParams.content" placeholder="请输入消息内容关键字快速查询" clearable class="form_225">
                            <template v-slot:suffix>
                                <el-icon @click="handleSearch" @keyup.enter.native="handleSearch">
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-form>
                <el-table :data="messageList" border>
                    <el-table-column label="序号" prop="sort" width="80" align="center">
                        <template #default="scope">
                            {{ scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="消息类型" prop="type" align="center">
                        <template #default="scope">
                            {{ formDict(msgList, scope.row.type) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="消息内容" prop="content" align="center" :show-overflow-tooltip="true" />
                    <el-table-column label="发送时间" prop="sendTime" align="center" />
                    <el-table-column label="状态" prop="status" align="center">
                        <template #default="scope">
                            {{ scope.status == '1' ? '已读' : '未读' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="读取人" prop="receiver" align="center" />
                    <el-table-column label="读取时间" prop="receiveTime" align="center" />
                </el-table>
                <div style="float: right;">
                    <pagination :total="total" v-model:page="queryParams.current" v-model:limit="queryParams.size"
                        @pagination="getList" />
                </div>
            </el-card>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import system from '@/api/model/system';
import { Search } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance();
const queryParams = reactive({
    current: 1,
    size: 10,
})
const total = ref(0)
const messageList = ref([])
const msgList = ref([])
const getList = () => {
    var Organization = JSON.parse(localStorage.getItem("Organization"))
    var orgKey = JSON.parse(localStorage.getItem("orgKey"))
    var num = orgKey.content
    var userList = Organization.content
    var userId = userList[num].id
    console.log(userId);
    system.getMessageList({
        orgId: userId,
        ...queryParams
    }).then(res => {
        if (res.code == 200) {
            messageList.value = res.data.records
            total.value = res.data.total
        }
    })
}
// 字典
async function dict() {
    msgList.value = await proxy.getDictList('alarm_message_type')
}
const formDict = (data, val) => {
    return (data && val) ? proxy.selectDictLabel(data, val) : '--'
}
//搜索
const handleSearch = () => {
    getList()
}
getList()
dict()
</script>

<style lang="scss" scoped></style>