<template>
	<div ref="qrcode" class="qr-code"></div>
</template>

<script>
import QRCode from 'qrcodejs2'

export default {
	name: 'QrCodeWithLogo',
	props: {
		value: {
			type: String,
			required: true
		},
		size: {
			type: Number,
			default: 200
		},
		logo: {
			type: String,
			default: ''
		}
	},
	mounted() {
		this.generateQRCode();
	},
	methods: {
		generateQRCode() {
			const options = {
				text: this.value,
				width: this.size,
				height: this.size,
				colorDark: "#000000",
				colorLight: "#ffffff",
				correctLevel: QRCode.CorrectLevel.H
			};

			// Create QRCode object
			new QRCode(this.$refs.qrcode, options);

			// Create logo element
			const img = document.createElement('img');
			img.src = require('@/assets/' + this.logo);
			img.style.width = '30%';
			img.style.height = '30%';
			img.style.position = 'absolute';
			img.style.top = '35%';
			img.style.left = '35%';

			// Append logo to QRCode element
			this.$refs.qrcode.appendChild(img);
		}
	}
}
</script>

<style scoped>
.qr-code {
	position: relative;
	display: inline-block;
}
</style>
