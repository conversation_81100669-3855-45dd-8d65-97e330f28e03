<template>
  <el-container>
    <el-container>
      <el-main class="nopadding">
        <el-container>
          <el-header>
            <div class="left-panel">
              <el-date-picker
                v-model="date"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
            <div class="right-panel"></div>
          </el-header>
          <el-main class="nopadding">
            <ytzhTable
              highlightCurrentRow
              @row-click="rowClick"
              ref="dataTable"
              :data="dataList"
              row-key="id"
              @selection-change="selectionChange"
              stripe
              :tablePage="tablePage"
              :pageChangeHandle="getDataList"
              :refreshDataListHandle="getDataList"
            >
				<el-table-column label="日志类型" prop="logType" width="100">
					<template #default="scope">
						<div v-if="scope.row.logType == '1'" >登陆日志</div>
						<div v-if="scope.row.logType == '2'" >操作日志</div>
						<div v-if="scope.row.logType == '3'" >异常日志</div>
					</template>
				</el-table-column>
              <el-table-column label="级别" prop="logGrade" width="60">
                <template #default="scope">
                  <el-icon v-if="scope.row.logGrade == 'error'" style="color: #f56c6c"
                    ><el-icon-circle-close-filled
                  /></el-icon>
                  <el-icon v-if="scope.row.logGrade == 'warn'" style="color: #e6a23c"
                    ><el-icon-warning-filled
                  /></el-icon>
                  <el-icon v-if="scope.row.logGrade == 'info'" style="color: #409eff"
                    ><el-icon-info-filled
                  /></el-icon>
                </template>
              </el-table-column>
				<el-table-column label="日志名称" prop="appName" width="200"></el-table-column>
              <el-table-column
                label="操作人"
                prop="operator"
                width="150"
              ></el-table-column>
              <el-table-column label="请求接口" prop="url" width="180"></el-table-column>
              <el-table-column
                label="请求方法"
                prop="method"
                width="150"
              ></el-table-column>
              <el-table-column
                label="状态码"
                prop="httpCode"
                width="150"
              ></el-table-column>
              <el-table-column
                label="客户端IP"
                prop="clientIp"
                width="150"
              ></el-table-column>
              <el-table-column
                label="信息"
                prop="pointMessage"
                width="150"
              ></el-table-column>
              <el-table-column
                label="日志时间"
                prop="createDate"
                width="250"
              ></el-table-column>
            </ytzhTable>
          </el-main>
        </el-container>
      </el-main>
    </el-container>
  </el-container>

  <el-drawer v-model="infoDrawer" title="日志详情" :size="600" destroy-on-close>
    <info ref="info"></info>
  </el-drawer>
</template>

<script>
import info from "./info";
import scEcharts from "@/components/scEcharts";

export default {
  name: "log",
  components: {
    info,
    scEcharts,
  },
  data() {
    return {
      infoDrawer: false,
      date: [],
      //数据列表
      dataList: {},
      //分页参数
      tablePage: {
        //数据总数
        total: 0,
        //当前页码
        currentPage: 1,
        //每页条数
        pageSize: 10,
        //排序
        //orders: [{ column: "createDate", asc: false }],
      },
      //查询表单
      searchForm: {
        name: "",
        url: "",
      },
      //数据列选中行
      selection: [],
      //列表加载
      listLoading: false,
    };
  },
  mounted() {
    //刷新数据列表
    this.getDataList();
  },
  methods: {
    upsearch() {},
    rowClick(row) {
      this.infoDrawer = true;
      this.$nextTick(() => {
        this.$refs.info.setData(row);
      });
    },
    /*
     * 刷新数据列表
     * @author: 路正宁
     * @date: 2023-03-24 13:13:35
     */
    async getDataList() {
      //页面加载
      this.listLoading = true;
      this.dataList = null;
      var res = await this.$API.sysOperateLogService.list({
        //当前页码
        current: this.tablePage.currentPage,
        //每页条数
        size: this.tablePage.pageSize,
        //排序查询
        orders: this.tablePage.orders,
        ...this.searchForm,
      });
      if (res.code == 200) {
        //总数据条数
        this.tablePage.total = res.data.total;
        //数据列表
        this.dataList = res.data.records;
      } else {
        this.$Response.errorNotice(res, "查询失败");
      }
      this.listLoading = false;
    },
    /*
     * 删除数据，行内删除
     * @author: 路正宁
     * @date: 2023-03-24 14:35:00
     */
    async deleteData(row, index) {
      this.listLoading = true;
      var res = await this.$API.sysOperateLogService.delete(row.id);
      if (res.code == 200) {
        this.$refs.dataTable.removeIndex(index);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      this.listLoading = false;
    },
    /*
     * 批量删除
     * @author: 路正宁
     * @date: 2023-03-24 14:36:11
     */
    async deleteDatas() {
      //确认删除弹框
      var confirmRes = await this.$confirm(
        `确定删除选中的 ${this.selection.length} 项吗？`,
        "提示",
        {
          type: "warning",
          confirmButtonText: "删除",
          confirmButtonClass: "el-button--danger",
        }
      ).catch(() => {});
      //确认结果处理
      if (!confirmRes) {
        return false;
      }
      //要删除的id数组
      var ids = this.selection.map((v) => v.id);
      //拼接的数组字符串，接口传参
      var idStr = this.selection.map((v) => v.id).join(",");
      //页面加载中
      this.listLoading = true;
      var res = await this.$API.sysOperateLogService.delete(idStr);
      if (res.code == 200) {
        //从列表中移除已删除的数据
        this.$refs.dataTable.removeKeys(ids);
        this.$message.success("删除成功");
      } else {
        this.$Response.errorNotice(res, "删除失败");
      }
      //释放页面加载中
      this.listLoading = false;
    },
    /*
     * 表格选择后回调事件
     * @author: 路正宁
     * @date: 2023-03-24 14:51:09
     */
    selectionChange(selection) {
      this.selection = selection;
    },
  },
};
</script>

<style></style>
