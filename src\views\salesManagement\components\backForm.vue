<template xmlns="http://www.w3.org/1999/html">
	<div class="formsDiv">
		<div v-show="props.active == 1" class="NO1">
			<el-form ref="queryRef" :inline="true" :model="searchForm" label-width="90px">
				<el-form-item label="客户">
          <el-select v-model="searchForm.n1" clearable filterable placeholder="请选择客户" style="width: 220px;">
						<template #empty>
							<p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
								无数据
							</p>
							<p style="text-align: center">
								<el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
									data.clientType.value =
										'';
									clientList();
								}
									">
									返回
								</el-button>
							</p>
						</template>
						<el-input v-model="data.clientType.value" placeholder="请输入客户名称Enter键搜索"
                      @keydown.enter="clientList"/>
						<el-option v-for="(item, index) in data.clientType
							.type" :key="index" :label="item.enterpriseName" :value="item.id"/>

					</el-select>
				</el-form-item>
				<el-form-item label="商品名称">
          <el-input v-model="searchForm.n2" clearable placeholder="请输入商品名称" style="width: 220px"/>
				</el-form-item>
				<el-form-item label="经手人">
          <el-select v-model="searchForm.n3" clearable filterable placeholder="请输入经手人" style="width: 220px">
						<template #empty>
							<p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
								无数据
							</p>
							<p style="text-align: center">
								<el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
									data.handle.value =
										'';
									handleList();
								}
									">
									返回
								</el-button>
							</p>
						</template>
            <el-input v-model="data.handle.value" placeholder="请输入经手人名称Enter键搜索"
                      @keydown.enter="handleList"/>
						<el-option v-for="(item, index) in data.handle.type" :key="index" :label="item.name"
                       :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="出库日期">
					<div class="xBox">
            <el-date-picker v-model="searchForm.n4" end-placeholder="结束日期" format="YYYY/MM/DD HH:mm:ss"
                            range-separator="至" size="default" start-placeholder="开始日期" style="width: 220px"
                            type="daterange" value-format="YYYY-MM-DD HH:mm:ss"/>
					</div>
				</el-form-item>
				<el-form-item label="单据编号">
          <el-input v-model="searchForm.n5" clearable placeholder="请输入单据编号" style="width: 220px"/>
				</el-form-item>
				<el-form-item>
					<label id="el-id-2128-46" class="el-form-item__label" for="el-id-2128-55" style="width: 90px;">
						<el-button type="primary" @click="handleQuery">查询</el-button>
					</label>
				</el-form-item>
			</el-form>
			<el-table ref="multipleTableRef" v-loading="LoadingFlag" :cell-style="{ textAlign: 'center' }"
                :data="goodsTable" :header-cell-style="{ 'text-align': 'center' }" :row-key="renderKey"
                style="width: 100%"
                @selection-change="handleSelectionChange">
        <el-table-column :reserve-selection="true" fixed type="selection" width="55"/>
        <el-table-column :reserve-selection="true" :show-overflow-tooltip="true" fixed label="单据编号" prop="docNum"
                         width="180"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="客户" prop="customer"
                         width="120"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="商品名称"
                         prop="commodity.tradeName" width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="自编码"
                         prop="commodity.commoditySelfCode" width="100"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="规格"
                         prop="commodity.packageSpecification" width="90"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="生产厂家"
                         prop="commodity.manufactureName" width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="产地"
                         prop="commodity.originPlace" width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="剂型"
                         prop="commodity.dosageForm" width="100"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="基本单位"
                         prop="commodity.basicUnit"/>
        <el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="库存余量" prop="inventory"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" fixed="right" label="销售单价"
                         prop="unitPrice"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" fixed="right" label="可退数量"
                         prop="refundable"/>

				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="批号" prop="batchNumber"
                         width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="生产日期"
                         prop="commodityProduceDate" width="100">
					<template #default="scope">
						{{ functionIndex.transformTimestamp(scope.row.produceDate) }}
					</template>
				</el-table-column>
				<el-table-column :reserve-selection="true" label="有效期" prop="commodity.validityTime" width="100">
					<template #default="scope">
						{{ echoTime(scope.row.produceDate, scope.row.commodity.validityTime) }}
					</template>
				</el-table-column>
			</el-table>
      <el-pagination v-model:current-page="searchForm.current" v-model:page-size="searchForm.size" :background="true"
                     :disabled="false" :page-sizes="[5, 10, 20, 50]" :small="false" :total="searchForm.total"
                     layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
                     @size-change="handleQuery"
                     @current-change="handleQuery"/>
		</div>
		<div v-if="props.active == 2" class="NO2">
			<el-table :cell-style="{ textAlign: 'center' }" :data="chooseGoods"
                :header-cell-style="{ 'text-align': 'center' }" style="width: 100%">
        <el-table-column label="商品名称" prop="commodity.tradeName"/>
        <el-table-column label="规格" prop="commodity.packageSpecification"/>
        <el-table-column label="生产厂家" prop="commodity.manufactureName"/>
        <el-table-column label="批号" prop="batchNumber"/>
        <el-table-column label="可退数量" prop="refundable"/>
				<el-table-column label="申退数量">
					<template #default="scope">
						<p v-show="!scope.row.amount.flag" style="width: 100%;color: red;font-size: 16px;" @click="
							chooseGoods[scope.$index].amount.flag = true
							">
							<span v-if="scope.row.amount.num == 0
								" style="color: red">点击输入</span>
							<span v-else>
								{{ scope.row.amount.num }}
							</span>
						</p>
						<el-input-number v-show="scope.row.amount.flag" v-model="chooseGoods[scope.$index].amount.num"
                             v-clickOutside="() => {
								chooseGoods[scope.$index].amount.flag = false
							}
								" :max="scope.row.refundable" :min="0" :precision="0" :step="1" :value-on-clear="0"
                             @change="allPriceFn(scope.$index)"/>
					</template>
				</el-table-column>
				<el-table-column label="申退金额" prop="price">
					<template #default="scope">
						{{ scope.row.price.toFixed(2) }}
					</template>
				</el-table-column>
				<el-table-column label="操作">
					<template #default="scope">
						<el-button text type="primary" @click="detailGoods(scope.row)">
							查看详情
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="display: flex">
				<p class="textStyle" style="margin: 15px 0 0px 0">合计申退数量: <span style="color:red">{{
            formPrice.allNum
          }}</span></p>
				<p class="textStyle" style="margin: 15px 0 0px 20px">合计申退金额: <span style="color:red">{{
            formPrice.allPrice.toFixed(2)
          }}元</span></p>
			</div>
			<el-drawer v-model="detailFlag" direction="rtl" size="30%" title="商品详情">
				<div style="padding: 0 20px">
					<table border="0" cellpadding="0" cellspacing="1" class="detailTable">
						<tr>
							<td>单据编号</td>
							<td>{{ detailsStr.docNum }}</td>
						</tr>
						<tr>
							<td>客户</td>
							<td>{{ detailsStr.customer }}</td>
						</tr>
						<tr>
							<td>商品名称</td>
							<td>{{ detailsStr.commodity.tradeName }}</td>
						</tr>
						<tr>
							<td>自编码</td>
							<td>{{ detailsStr.commodity.commoditySelfCode }}</td>
						</tr>
						<tr>
							<td>规格</td>
							<td>{{ detailsStr.commodity.packageSpecification }}</td>
						</tr>
						<tr>
							<td>生产厂家</td>
							<td>{{ detailsStr.commodity.manufactureName }}</td>
						</tr>
						<tr>
							<td>产地</td>
							<td>{{ detailsStr.commodity.originPlace }}</td>
						</tr>
						<tr>
							<td>剂型</td>
							<td>{{ detailsStr.commodity.dosageForm }}</td>
						</tr>
						<tr>
							<td>基本单位</td>
							<td>{{ detailsStr.commodity.basicUnit }}</td>
						</tr>
						<tr>
							<td>库存余量</td>
							<td>{{ detailsStr.inventory }}</td>
						</tr>
						<tr>
							<td>可退数量</td>
							<td>{{ detailsStr.refundable }}</td>
						</tr>
						<tr>
							<td>销售单价</td>
							<td>{{ detailsStr.unitPrice.toFixed(2) }}</td>
						</tr>
						<tr>
							<td>批号</td>
							<td>{{ detailsStr.batchNumber }}</td>
						</tr>
						<tr>
							<td>生产日期</td>
							<td>{{ functionIndex.transformTimestamp(detailsStr.produceDate) }}</td>
						</tr>
						<tr>
							<td>有效期</td>
							<td> {{ echoTime(detailsStr.produceDate, detailsStr.commodity.validityTime) }}</td>
						</tr>
					</table>
				</div>
			</el-drawer>

			<el-form ref="creatform" :inline="true" :model="backForm" :rules="creatRules" label-width="90px"
               style="margin-top: 20px">
				<el-form-item label="退货原因" prop="n1">
					<el-select v-model="backForm.n1" placeholder="请选择退货原因" style="width: 220px">
            <el-option v-for="(item, index) in data.reasonType" :key="'reasonType' + index" :label="item.name"
                       :value="item.value"/>
					</el-select>
				</el-form-item>
				<el-form-item label="扣款金额" prop="n4">
					<p>{{ backForm.n4.toFixed(2) }}</p>
				</el-form-item>
				<el-form-item label="有无实货" prop="n2">
					<el-select v-model="backForm.n2" placeholder="请选择有无实货" style="width: 220px">
            <el-option label="有" value="1"/>
            <el-option label="无" value="0"/>
					</el-select>
				</el-form-item>
				<el-form-item label="责任人" prop="n3">
          <el-input v-model="backForm.n3" clearable placeholder="请输入责任人" style="width: 220px"/>
				</el-form-item>
        <br/>
				<el-form-item label="备注" style="width: 50%">
          <el-input v-model="backForm.n5" :rows="3" class="BINP" clearable maxlength="100" placeholder="请输入备注"
                    show-word-limit type="textarea"/>
				</el-form-item>
        <br/>
				<el-form-item label="上传附件" prop="n6">
          <el-upload v-model:file-list="backForm.n6" :before-upload="(file) => beforeFile(file)" :data="uploadData"
                     :headers="headers" :limit="0" :on-error="() => errorFile()" :on-preview="handlePreview"
                     :on-success="(response) => successFile(response)" action="/file/uploadd" class="upload-demo"
                     multiple>
						<el-button type="primary">点击上传</el-button>
						<template #tip>
							<div class="el-upload__tip">
								文件大小不大于5MB
							</div>
						</template>
					</el-upload>
				</el-form-item>
			</el-form>
		</div>
		<div v-if="props.active == 3" class="NO3">
			<h3 class="el-dialog__title">退货明细</h3>
			<el-table :data="chooseGoods" style="width: 100%">
        <el-table-column :show-overflow-tooltip="true" fixed label="单据编号" prop="docNum" width="180"/>
        <el-table-column :show-overflow-tooltip="true" label="商品名称" prop="commodity.tradeName" width="150"/>
        <el-table-column :show-overflow-tooltip="true" label="自编码" prop="commodity.commoditySelfCode" width="100"/>
				<el-table-column :show-overflow-tooltip="true" label="规格" prop="commodity.packageSpecification"
                         width="90"/>
        <el-table-column :show-overflow-tooltip="true" label="生产厂家" prop="commodity.manufactureName" width="150"/>
        <el-table-column :show-overflow-tooltip="true" label="产地" prop="commodity.originPlace" width="150"/>
        <el-table-column :show-overflow-tooltip="true" label="剂型" prop="commodity.dosageForm" width="100"/>
        <el-table-column :show-overflow-tooltip="true" label="基本单位" prop="commodity.basicUnit"/>
        <el-table-column :show-overflow-tooltip="true" label="库存余量" prop="inventory"/>
        <el-table-column :show-overflow-tooltip="true" label="可退数量" prop="refundable"/>
        <el-table-column :show-overflow-tooltip="true" label="销售单价" prop="unitPrice"/>
        <el-table-column :show-overflow-tooltip="true" label="批号" prop="batchNumber" width="150"/>
				<el-table-column :show-overflow-tooltip="true" label="生产日期" prop="commodityProduceDate" width="100">
					<template #default="scope">
						{{ functionIndex.transformTimestamp(scope.row.produceDate) }}
					</template>
				</el-table-column>
				<el-table-column label="有效期" prop="commodity.validityTime" width="100">
					<template #default="scope">
						{{ echoTime(scope.row.produceDate, scope.row.commodity.validityTime) }}
					</template>
				</el-table-column>
				<el-table-column align="center" fixed="right" label="申退数量" prop="amount.num">
					<template #default="scope">
						<span style="color: red">{{ scope.row.amount.num }}</span>
					</template>
				</el-table-column>
				<el-table-column align="center" fixed="right" label="申退金额" prop="">
					<template #default="scope">
						<span style="color: red">{{ scope.row.price.toFixed(2) }}</span>
					</template>
				</el-table-column>

			</el-table>
			<h3 class="el-dialog__title" style="margin: 10px 0">退货信息</h3>
			<table border="0" cellpadding="0" cellspacing="1" class="messTable">
				<tr>
					<td>退货原因</td>
					<td>{{ echoReason(backForm.n1) }}</td>
				</tr>
				<tr>
					<td>有无实货</td>
					<td>{{ backForm.n2 == 1 ? '有' : '无' }}</td>
				</tr>
				<tr>
					<td>责任人</td>
					<td>{{ backForm.n3 }}</td>
				</tr>
				<tr>
					<td>扣款金额</td>
					<td>{{ backForm.n4.toFixed(2) }}</td>
				</tr>
				<tr>
					<td>附件</td>
          <td>
            <p v-for="(item, index) in  backForm.n6" :key="index"
               :style="backForm.n6.length == 1 ? 'color:#2a75f6;cursor: pointer;' : 'color:#2a75f6;cursor: pointer;line-height: 25px'"
               @click="handlePreview(item)">
              {{ item.name }}</p>
					</td>
				</tr>
				<tr>
					<td>备注</td>
					<td>{{ backForm.n5 }}</td>
				</tr>
				<tr>
					<td>合计申退数量</td>
					<td><span style="color: red">{{ formPrice.allNum }}</span></td>
				</tr>
				<tr>
					<td>合计申退金额</td>
					<td><span style="color: red">{{ formPrice.allPrice.toFixed(2) }}</span></td>
				</tr>
			</table>
			<p style="color: red;margin: 40px 2% 0 4%;text-align: left">
				请检查以上信息是否填写准确，
				若信息准确，请点击“确定”发起“销售退回申请，
				若需修改，请点击“上一步”进行修改。</p>
		</div>
    <el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
	</div>
</template>

<script setup>
import {getCurrentInstance, onBeforeMount, onMounted, reactive, ref, watch, watchEffect} from 'vue';
import {ClickOutside as vClickOutside, ElMessage} from 'element-plus'
import {backApi, manageApi} from "@/api/model/salesManagement";
import {functionIndex} from '../functionIndex'
import tool from "@/utils/tool";
//console.log('1-开始创建组件-setup')
const LIU = vClickOutside
const uploadData = ref()
const {proxy} = getCurrentInstance();
/**
 * 数据部分
 */
const detailFlag = ref(false)
const data = reactive({
	test: 999,
	reasonType: [],
	clientType: {
		type: [],
		value: ""
	},
	handle: {
		value: "",
		type: []
	},
	checkFlag: false,
	imgUrl: [],

})
const goodsTable = ref([])
const searchForm = ref({
	n1: "",
	n2: "",
	n3: "",
	n4: [],
	n5: "",
	size: 10,
	current: 1,
	total: 0
});
const formPrice = ref({
	allNum: 0,
	allPrice: 0
})
const backForm = ref({
	n1: "",
	n2: "",
	n3: "",
	n4: null,
	n5: "",
	n6: [],
	n7: "",
	n8: "",
	n9: "",
	n10: "",
});
const creatform = ref(); //验证表单/流程新增
const creatRules = reactive({
  n1: [{required: true, message: "请选择退货原因", trigger: "blur"}],
  n2: [{required: true, message: "请选择有无实货", trigger: "blur"}],
  n3: [{required: true, message: "请输入责任人", trigger: "blur"}],
  n4: [{required: true, message: "请输入扣款金额", trigger: "blur"}],
  n6: [{required: true, message: "请上传附件", trigger: "blur"}],
});
const emit = defineEmits([])
const props = defineProps({
	active: {
		default: 1
	},
	uuid: {
		default: null
	},
})
const detailsStr = ref({})
const multipleTableRef = ref()
watch(() => backForm.value.n1, () => {
	if (backForm.value.n1 == 0) {
		backForm.value.n4 = 30
	} else {
		backForm.value.n4 = formPrice.value.allPrice * 0.02
	}
})
const handleList = () => {
	backApi
		.handleList({
			size: 1000,
			name: data.handle.value
		})
		.then((res) => {
			if (res.code == 200) {
				data.handle.type = res.data.records;
			}
		});
};
const allPriceFn = (ind) => {
	chooseGoods.value[ind].price = chooseGoods.value[ind].amount.num * chooseGoods.value[ind].unitPrice
	formPrice.value.allPrice = 0
	formPrice.value.allNum = 0
	chooseGoods.value.forEach(item => {
		formPrice.value.allNum += item.amount.num
		formPrice.value.allPrice += item.price
	})
	if (backForm.value.n1 && backForm.value.n1 != 0) {
		backForm.value.n4 = formPrice.value.allPrice * 0.02
	}
}
const echoReason = (str) => {
	const newStr = data.reasonType.find(item => item.value == str)
	return newStr.name
}
const handlePreview = (uploadFile) => {
	const fileName = uploadFile.name.split(".")
	data.imgUrl = []
	if (
		fileName[fileName.length - 1] == "gif" ||
		fileName[fileName.length - 1] == "jpeg" ||
		fileName[fileName.length - 1] == "apng" ||
		fileName[fileName.length - 1] == "jpg" ||
		fileName[fileName.length - 1] == "avif" ||
		fileName[fileName.length - 1] == "png" ||
		fileName[fileName.length - 1] == "svg" ||
		fileName[fileName.length - 1] == "webp"
	) {
		data.checkFlag = true;
		data.imgUrl.push(uploadFile.resFileUrl)
	}
}
const renderKey = (row) => {
	return row.id
}
const close = () => {
	data.checkFlag = false;
	data.imgUrl = []
}
const beforeFile = (file) => {
	console.log(file)
	if (file.size > 5242880) {
		ElMessage.error("文件不能大于5M");
		return false;
	} else {
		uploadData.value = {
			uuid: props.uuid,
			fileGroup: "salesManagement",
			businessType: "salesBack",
			fileType: 50
		}
	}
}
const successFile = (res) => {
	ElMessage.success("上传成功");
	backForm.value.n6[backForm.value.n6.length - 1].resFileUrl = res.data.fileUrl
};
const errorFile = () => {
	ElMessage.error("上传失败");
};
const headers = {
	Authorization: "Bearer " + tool.cookie.get("TOKEN"),
	ContentType: "multipart/form-data",
  clientType:'pc',
};
const chooseGoods = ref([])

const detailGoods = (row) => {
	detailsStr.value = row
	detailFlag.value = true
	console.log(detailsStr)
}
const LoadingFlag = ref(false)
const changeTime = (time) => {
	if (time) {
		let newTime = new Date(time)
		newTime = newTime.setDate(newTime.getDate() + 1);
		newTime = functionIndex.transformTimestampSearch(newTime)
		return newTime
	} else {
		return null
	}
}
const handleQuery = () => {
	if (searchForm.value.n1 == '' || searchForm.value.n1 == null) {
		ElMessage.error('请选择查询客户')
	} else {
		LoadingFlag.value = true
		backApi.getList({
			'erpCustomer.id': searchForm.value.n1,
			tradeName: searchForm.value.n2,
			handledById: searchForm.value.n3,
			beginOutTime: searchForm.value.n4 ? searchForm.value.n4[0] : null,
			endOutTime: searchForm.value.n4 ? changeTime(searchForm.value.n4[1]) : null,
			docNum: searchForm.value.n5,
			size: searchForm.value.size,
			current: searchForm.value.current,
			busType: '3'
		}).then(res => {
			if (res.code == 200) {
				searchForm.value.total = res.data.total
				goodsTable.value = res.data.records
				backForm.value.n9 = searchForm.value.n1
				if (goodsTable.value.length > 0) {
					if (goodsTable.value[0].erpCustomer.id != searchForm.value.n1) {
						multipleTableRef.value?.clearSelection()
						chooseGoods.value = []
					}
				} else {
					multipleTableRef.value?.clearSelection()
				}
			}
			LoadingFlag.value = false
		})
	}
}
const handleSelectionChange = (val) => {
	chooseGoods.value = val
	if (val.length > 1) {
		if (val[val.length - 2].erpCustomer.id != val[val.length - 1].erpCustomer.id) {
			multipleTableRef.value?.toggleRowSelection(chooseGoods.value[chooseGoods.value.length - 1], undefined)
			ElMessage.error('请选择相同客户')
		}
	}
}
const clientList = () => {
	manageApi
		.clientType({
			current: 1,
			size: 1000,
			isEnable: 0,
			status: 3,
			enterpriseName: data.clientType.value,
		})
		.then((res) => {
			if (res.code == 200) {
				data.clientType.type = res.data.records;
			}
		});
};
const echoTime = (startTime, oldTime) => {
	let time1 = new Date(startTime)
	time1.setMonth(time1.getMonth() + oldTime)
	return functionIndex.transformTimestamp(time1)
}
onBeforeMount(async () => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	clientList()
	handleList()
	data.reasonType = await proxy.getDictList("reason_for_order");

})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	creatform,
	backForm,
	goodsTable,
	chooseGoods,
	multipleTableRef,
	searchForm,
	formPrice
})

</script>
<style lang='scss' scoped>
@import "../style/backForm";
</style>
