<template>
    <div>
        <el-drawer v-model="open" :title="title" size="50%" @close="onClose">
            <div class="p10" style="background-color: #f5f7fd">
                <el-radio-group v-model="type" style="margin: 10px" @change="tabClick">
                    <el-radio-button label="1">详情</el-radio-button>
                    <el-radio-button label="2">轨迹</el-radio-button>
                    <!--        <el-radio-button label="3">支付</el-radio-button>-->
                    <el-radio-button label="4">温度记录</el-radio-button>
                    <el-radio-button label="5">异动记录</el-radio-button>
                    <el-radio-button label="6">操作记录</el-radio-button>
                </el-radio-group>
                <order-details v-if="type == 1" :order-info="orderInfo"></order-details>
                <div v-if="type == 2">
                    <el-card class="mb16 box__locus" shadow="never">
                        <el-timeline :reverse="true">
                            <el-timeline-item v-for="(activity, i) in activities" :key="i" :timestamp="timeFormatting(activity.createDate)" :type="i == 0 ? 'success' : ''">
                                <div>
                                    {{ activity.content }}
                                    <el-tag v-if="activity.remark" effect="plain" round size="small" @click="toggleVisibility(i)">
                                        查看三方物流轨迹
                                        <el-icon> <ArrowUp v-if="activity.isVisible" /> <ArrowDown v-else /> </el-icon>
                                    </el-tag>
                                    <transition name="fade">
                                        <div v-if="activity.isVisible" class="three_party">
                                            <el-timeline :reverse="true">
                                                <el-timeline-item v-for="(record, recordIndex) in parseRemark(activity.remark)" :key="recordIndex" :timestamp="timeFormatting(record.time)" :type="recordIndex == 0 ? 'success' : ''">
                                                    <div>
                                                        {{ record.remark }}
                                                    </div>
                                                </el-timeline-item>
                                            </el-timeline>
                                        </div>
                                    </transition>
                                </div>
                            </el-timeline-item>
                        </el-timeline>
                        <el-empty v-if="activities && activities.length == 0" description="不存在运输轨迹"></el-empty>
                    </el-card>
                </div>
                <div v-if="type == 3">
                    <card-header title="支付费用" />
                </div>
                <temperature-recording v-if="type == 4" :orderInfo="orderInfo"></temperature-recording>
                <OrderTransactionDetails v-if="type == 5" :orderId="orderInfo.id"></OrderTransactionDetails>
                <waybill-tracking-details v-if="type == 6" :orderInfo="orderInfo"></waybill-tracking-details>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import CardHeader from '@/components/CardHeader';
import WaybillTrackingDetails from '@/views/logisticsManagement/waybillTracking/WaybillTrackingDetails.vue';
import OrderTransactionDetails from '@/views/orderComponents/OrderTransactionDetails.vue';
import OrderDetails from '@/views/orderComponents/OrderDetails/index.vue';
import TemperatureRecording from '@/views/orderComponents/TemperatureRecording.vue';
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
import moment from 'moment';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

export default {
    name: 'TransportRecord',
    components: { WaybillTrackingDetails, OrderTransactionDetails, OrderDetails, CardHeader, TemperatureRecording, ArrowDown, ArrowUp },
    props: {
        infoOpen: {
            required: true,
            type: Boolean
        },
        orderInfo: {
            required: true,
            type: Object
        },
        title: {
            required: true,
            type: String
        }
    },
    data() {
        return {
            type: 1,
            activities: [], // 订单轨迹
            info: {} // 订单详情
            // mainInfo:null, // 主表ID
        };
    },
    computed: {
        /**
         * 时间格式化
         * @returns {function(*=): *}
         */
        timeFormatting() {
            return (val) => {
                return moment(val).format('YYYY-MM-DD HH:mm:ss');
            };
        }
    },
    created() {
        this.type = 1;
        this.open = this.infoOpen;
        this.getTransDetail();
    },
    methods: {
        // 温层类型翻译
        getTemperatureTypeVal(val) {
            let index = this.temperatureTypeDicts.findIndex((t) => t.id == parseInt(val));
            if (index > -1) {
                return this.temperatureTypeDicts[index].describtion;
            }
            return '';
        },
        // 获取订单轨迹
        getTrackTrajectory() {
            const { transOrderNo } = this.orderInfo;
            if (!transOrderNo) {
                this.msgError('此订单没有运输轨迹');
                return false;
            }
            orderManagement.getTransRecord({ transOrderNo }).then((response) => {
                if (response.code == 200 && response.data.records) {
                    this.activities = response.data.records;
                }
            });
        },
        onClose() {
            this.$emit('closeSlider');
        },
        submitForm() {},
        // 点击tabs
        tabClick(label) {
            switch (label) {
                case '1':
                    break;
                case '2':
                    this.getTrackTrajectory();
                    break;
                case '4':
                    break;
                case '5':
                    break;
                case '6':
                    break;
                default:
                    break;
            }
        },
        toggleVisibility(index) {
            // 切换指定 activity 的 isVisible 状态
            this.activities[index].isVisible = !this.activities[index].isVisible;
        },
        parseRemark(remark) {
            // 解析 JSON 字符串为对象
            try {
                return JSON.parse(remark);
            } catch (e) {
                console.error('Invalid JSON format in remark:', remark);
                return [];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .three_party .el-timeline-item__node--normal {
        left: 0;
    }
    .three_party .el-timeline-item {
        padding-bottom: 10px;
    }
    // 最后一个 .el-timeline-item padding-bottom: 0;
    .three_party .el-timeline-item:last-child {
        padding-bottom: 0;
    }
}

.three_party {
    border: 1px solid #2a76f8;
    padding: 10px 10px 5px 10px;
    margin-top: 5px;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease;
}
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
    opacity: 0;
}
</style>
