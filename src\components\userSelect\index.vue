<template>
<el-dialog :title="values.title" v-model="open" width="850px" append-to-body @close="close">
  <div class="box-flex">
    <div class="box-flex-item">
      <el-row class="mb8">
        <el-col :span="24">
          <el-input
              v-model="searchName"
              placeholder="请输入员工名称"
              clearable
              size="small"
              @keyup.enter.native="loadNode"
          />
        </el-col>
      </el-row>
      <div class="overflow-div">
        <el-tree
            :ref="config.type"
            :props="{
               children: config.children,
               label: config.label,
               isLeaf: config.isLeaf,
               disabled: config.disabled
            }"
            :data="treeList"
            node-key="id"
            :check-strictly="true"
            @check-change="(data, checked) => onCheckChange(data, checked)"
            :default-checked-keys="config.checkedHandle(selectList)"
            show-checkbox
            default-expand-all
        >
        </el-tree>
      </div>
    </div>
    <div class="box-flex-item">
      <div style="display: flex;justify-content:space-between;">
        <div style="text-align: left;padding: 7px 0; "><img src="@/assets/images/userSelect/people.png"
                                                            style="width: 12px;height: 12px"> 已选：<span
            style="color:#5670FE; ">{{ selectList.length }}人</span></div>
        <el-button size="mini" type="text" style="text-align: right; " @click="clear"><img
            src="@/assets/images/userSelect/delete.png" class="icon-size"></el-button>
      </div>
      <div style="margin: 5px 0">用户列表</div>
      <div class="overflow-div">
        <div style="display: flex;justify-content:space-between;margin: 5px 0" v-for="(item,idx) in selectList"
             @click="changeStatus(idx)">
          <div>
            <img src="@/assets/images/userSelect/meter.png"><span
              style="margin-left: 10px;letter-spacing: 4px;">{{ item.name }}</span>
          </div>
          <div style="text-align: right;padding-right: 5px">
            <div v-if="item.status" style="cursor: pointer;" alt="负责人"><el-icon color="#5670FE" :size="20"><UserFilled size="30" /></el-icon></div>
            <div @click.stop="delData(idx)" style="cursor: pointer;" class="ml10"><el-icon color="#5670FE"><Close class="icon-size"/></el-icon></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <template #footer>
	  <el-button @click="close">取 消</el-button>
	  <el-button type="primary" @click="submitForm">确 定</el-button>
  </template>
</el-dialog>
</template>

<script>
import {CONFIG_LIST} from './user-config.js'
import { UserFilled,Close} from '@element-plus/icons-vue'

export default {
  name: 'userSelect',
  components: {UserFilled,Close},
  props: {
    show:{
      required: true,
      type: Boolean,
      default:false
    },
    type: {
      type: String,
      default:'dep'
    },
    values: {
      required: true,
      type: Object
    },
    userData: {
      required: true,
      type: Array
    },
  },
  data() {
    return {
      open: false,
      searchName: null,
      searchType: null,
      treeList: [],
      selectList: []
    }
  },
  created() {
    this.selectList = [...this.userData];
    this.dataInit()
    this.loadNode();
  },
  watch: {
    show:{
      handler(val) {
        this.open = val;
      }
    },
  },
  methods: {
    // 清空
    clear() {
      this.$refs[this.type].setCheckedKeys([]);
      this.selectList = [];
    },
    // 改变状态
    changeStatus(idx) {
      if (this.values.statusFlag) {
        this.selectList.forEach((item, index) => {
          this.$set(this.selectList[index], 'status', false);
        })
        this.$set(this.selectList[idx], 'status', true);
      }
    },
    dataInit() {
      var that = this
      this.open = this.show;
      this.config = {}
      CONFIG_LIST.find(function (cfg) {
        if (cfg.type == that.type) {

          that.config = cfg
        }
      })
    },
    submitForm() {
      if (this.values.minNum && this.selectList.length < this.values.minNum) {
        this.msgError('最少选择' + this.values.minNum + '人');
        return false;
      }
      if (this.values.maxNum && this.selectList.length > this.values.maxNum) {
        this.msgError('最多选择' + this.values.minNum + '人');
        return false;
      }
      if (this.values.statusFlag) {
        let statusData = this.selectList.filter(item => item.status == true);
        if (statusData.length == 0) {
          this.msgError('请设置负责任人');
          return false;
        }
      }
      this.$emit('onConfirm', {data: this.values.data, userList: this.selectList})
    },

    delData(idx) {
      this.onCheckChange(this.selectList[idx],false)
    },
    close() {
      this.$emit('changeShow', false)
    },
    onCheckChange(data, checked) {
      if (checked) {
        if (this.values.statusFlag && this.selectList.length == 0) {
          this.selectList.push({id: data.id, name: data.name, phone: data.phone, status: true});
        } else {
          this.selectList.push({id: data.id, name: data.name, phone: data.phone, status: false});
        }
      } else {
        let index = this.selectList.findIndex(t => t.id === data.id)
        if(index > -1){
          this.selectList.splice(index,1)
        }
        this.$refs[this.type].setChecked(data.id, false);
      }
    },
    loadNode() {
      var that = this
      this.config.onload(this.searchName,this.values.roleType).then(res => {
        that.treeList = res
      })
    }
  }
}
</script>

<style scoped>
.icon-size{
  width: 16px;
  height: 16px;
}
.box-flex {
  display: flex;
  border: 1px solid rgba(229, 236, 252, 1);
}

.box-flex-item {
  flex: 1;
  padding: 10px;
}

.box-flex-item:first-child {
  border-right: 1px solid rgba(229, 236, 252, 1);
}

/*设置滚动条 start*/
.overflow-div {
  height: 500px;
  overflow-y: scroll;
}

.overflow-div::-webkit-scrollbar {
  width: 5px;
}

.overflow-div::-webkit-scrollbar {
  height: 5px;
}

.overflow-div::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 2px;
}

.overflow-div::-webkit-scrollbar-thumb {
  background: #bfbdbd;
  border-radius: 10px;
}

.overflow-div::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.overflow-div::-webkit-scrollbar-corner {
  background: #a7aaaa;
}

/*设置滚动条 end*/
</style>
