<template>
	<div style="margin-top: 20px">
		<div v-show="active==1">
			<el-form ref="queryRef" :inline="true" :model="searchForm" :rules="creatRules2" label-width="90px">
				<el-form-item label="客户" prop="n1">
					<el-select v-model="searchForm.n1" clearable filterable placeholder="请选择客户"
							   style="width: 220px;" @selection-change="handleSelectionChange">
						<template #empty>
							<p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
								无数据
							</p>
							<p style="text-align: center">
								<el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
									data.clientType.value =
										'';
									clientList();
								}
									">
									返回
								</el-button>
							</p>
						</template>
						<el-input v-model="data.clientType.value" placeholder="请输入客户名称Enter键搜索"
								  @keydown.enter="clientList"/>
						<el-option v-for="(item, index) in data.clientType
							.type" :key="index" :label="item.enterpriseName" :value="item.id"/>

					</el-select>
        </el-form-item>
        <el-form-item label="商品名称">
					<el-input v-model="searchForm.n2" clearable placeholder="请输入商品名称" style="width: 220px"/>
				</el-form-item>
				<el-form-item label="经手人" prop="n3">
					<el-select v-model="searchForm.n3" clearable filterable placeholder="请输入经手人"
							   style="width: 220px">
						<template #empty>
							<p style="
									text-align: center;
									color: #635f5e;
									margin: 15px 0;
								">
								无数据
							</p>
							<p style="text-align: center">
								<el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
									data.handle.value =
										'';
									handleList();
								}
									">
									返回
								</el-button>
							</p>
						</template>
						<el-input v-model="data.handle.value" placeholder="请输入经手人名称Enter键搜索"
								  @keydown.enter="handleList"/>
						<el-option v-for="(item, index) in data.handle.type" :key="index" :label="item.name"
								   :value="item.id"/>
					</el-select>
				</el-form-item>
				<el-form-item label="出库日期">
					<div class="xBox">
                        <el-date-picker v-model="searchForm.n4" end-placeholder="结束日期" format="YYYY/MM/DD HH:mm:ss"
                                        range-separator="至" size="default" start-placeholder="开始日期"
                                        style="width: 220px"
										type="daterange" value-format="YYYY-MM-DD HH:mm:ss"/>
					</div>
				</el-form-item>
				<el-form-item label="单据编号">
					<el-input v-model="searchForm.n5" clearable placeholder="请输入单据编号" style="width: 220px"/>
				</el-form-item>
				<el-form-item>
					<label id="el-id-2128-46" class="el-form-item__label" for="el-id-2128-55" style="width: 90px;">
						<el-button type="primary" @click="handleQuery">查询</el-button>
					</label>
				</el-form-item>
			</el-form>
			<el-table ref="multipleTableRef" v-loading="LoadingFlag" :cell-style="{ textAlign: 'center' }"
					  :data="goodsTable"
					  :header-cell-style="{ 'text-align': 'center' }"
					  :row-key="renderKey" style="width: 100%;margin-top: 10px"
					  @selection-change="handleSelectionChange">
				<el-table-column :reserve-selection="true" fixed="left" type="selection" width="55"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="客户" prop="customer"
								 width="120"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="单据编号"
								 prop="docNum" width="180"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="单据创建日期"
								 prop="" width="120">
					<template #default="scope">
						{{ functionIndex.transformTimestamp(scope.row.createDate) }}
					</template>
				</el-table-column>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="出库日期"
								 min-width="120"
								 prop="">
					<template #default="scope">
						{{ functionIndex.transformTimestamp(scope.row.outTime) }}
					</template>
				</el-table-column>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="商品名称"
								 prop="commodity.tradeName" width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="自编码"
								 prop="commodity.commoditySelfCode"
								 width="100"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="批号" prop="batchNumber"
								 width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="仓库" prop="storages"
								 width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" label="货位"
								 prop="goodsPosition.goodsShelvesId"
								 width="150"/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" fixed="right" label="出库数量"
								 prop='outQuantity'/>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" fixed="right" label="单价"
								 prop="unitPrice">
					<template #default="scope">
						{{ Number(scope.row.unitPrice).toFixed(2) }}
					</template>
				</el-table-column>
				<el-table-column :reserve-selection="true" :show-overflow-tooltip="true" fixed="right" label="可调数量"
								 prop="allowAdjustQuantity"/>
			</el-table>
			<el-pagination v-model:current-page="searchForm.current" v-model:page-size="searchForm.size"
						   :background="true"
						   :disabled="false" :page-sizes="[5, 10, 20, 50]" :small="false" :total="searchForm.total"
						   layout="->,total, sizes, prev, pager, next, jumper" style="margin-top: 19px"
						   @size-change="handleQuery"
						   @current-change="handleQuery"/>
		</div>
		<div v-show="active==2">
			<el-table :data="chooseGoods" :expand-row-keys="comeList" :row-key="renderKey" style="width: 100%"
					  @expand-change="onchangeTable">
				<el-table-column type="expand">
					<template #default="props">
						<div style="display: flex;justify-content: end">
							<div m="4"
								 style="display: flex;width: 87%; justify-content:space-between;align-items: center">
								<div style="color: red;font-size: 13px;line-height: 25px">
									<p>注意事项：</p>
									<p>1、调整数量总和不能大于所选出库（入库）记录当前的可调数量；</p>
									<p>2、调整数量不能录入小数；</p>
									<p>3、调整后单价不能大于所选出库（入库）记录当前的单价。</p>
								</div>
								<el-table :border="true" :data="props.row.adjustList" size="small" style="width: 55%">
									<el-table-column :value-on-clear="0" align="center" label="调整数量" prop="">
										<template #default="scope">
											<el-input-number
												v-model="chooseGoods[props.$index].adjustList[scope.$index].num"
												:min="0" :precision="0" :size="'small'" :step="1"
												style="width: 70%"
												@change="maxFn(props.$index,scope.$index)"/>
										</template>
									</el-table-column>
									<el-table-column :value-on-clear="0" align="center" label="调整后单价" prop="">
										<template #default="scope">
											<el-input-number
												v-model="chooseGoods[props.$index].adjustList[scope.$index].price"
												:min="0" :precision="2" :size="'small'"
												:step="1"
                                                @change="()=>{
													if(chooseGoods[props.$index].adjustList[scope.$index].price==props.row.unitPrice){
														chooseGoods[props.$index].adjustList[scope.$index].price=0
														ElMessage.error('调整够的单价不能等于当前单价')
													}
												}"
												style="width: 70%"/>
										</template>
									</el-table-column>
									<el-table-column align="center" label="操作" prop="" width="120px">
										<template #default="scope">
											<el-button :size="'small'" text type="primary"
													   @click="delPrice(props.$index,scope.$index)">删除
											</el-button>
										</template>
									</el-table-column>
								</el-table>
							</div>
						</div>
					</template>
				</el-table-column>
				<el-table-column align="center" label="商品名称" prop="commodity.tradeName"/>
				<el-table-column align="center" label="批号" prop="batchNumber"/>
				<el-table-column align="center" label="出库数量" prop="outQuantity"/>
				<el-table-column align="center" label="可调数量" prop="allowAdjustQuantity"/>
				<el-table-column align="center" label="单价" prop="unitPrice"/>
				<el-table-column align="center" label="操作" prop="name">
					<template #default="scope">
						<el-button size="small" type="primary" @click="detailFn(scope.row)">查看详情</el-button>
						<el-button size="small" type="primary" @click="newAdd(scope.row)">新增调价</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-form ref="creatform" :model="backForm" :rules="creatRules" label-width="90px"
					 style="margin-top: 20px">
				<el-form-item label="调价说明" prop="n1" style="width: 50%">
					<el-input v-model="backForm.n1" :rows="3" clearable maxlength="100" placeholder="请输入调价说明"
							  show-word-limit
							  type="textarea"/>
				</el-form-item>
				<el-form-item label="上传附件" prop="n2">
					<el-upload v-model:file-list="backForm.n2"
							   :before-upload="(file) =>beforeFile(file)"
							   :data="uploadData" :headers="headers" :limit="0" :on-error="() => errorFile()"
							   :on-preview="handlePreview" :on-success="(response) =>successFile(response)"
							   action="/file/uploadd" class="upload-demo" multiple>
						<el-button type="primary">点击上传</el-button>
						<template #tip>
							<div class="el-upload__tip">
								文件大小不大于5MB
							</div>
						</template>
					</el-upload>
				</el-form-item>
			</el-form>
		</div>
		<el-drawer
			v-model="detailFlag"
			direction="rtl"
			size="30%"
			title="查看详情"
		>
			<div style="padding: 0 20px">
				<table border="0" cellpadding="0" cellspacing="1" class="messTable">
					<tr>
						<td>客户</td>
						<td>{{ data.detailObj?.customer }}</td>
					</tr>
					<tr>
						<td>单据编号</td>
						<td>{{ data.detailObj?.docNum }}</td>
					</tr>
					<tr>
						<td>单据创建日期</td>
						<td>{{ functionIndex.transformTimestamp(data.detailObj?.createDate) }}</td>
					</tr>
					<tr>
						<td>出库日期</td>
						<td>{{ functionIndex.transformTimestamp(data.detailObj?.outTime) }}</td>
					</tr>
					<tr>
						<td>商品名称</td>
						<td>{{ data.detailObj?.commodity.tradeName }}</td>
					</tr>
					<tr>
						<td>自编码</td>
						<td>{{ data.detailObj?.commodity.commoditySelfCode }}</td>
					</tr>
					<tr>
						<td>批号</td>
						<td>{{ data.detailObj?.batchNumber }}</td>
					</tr>
					<tr>
						<td>仓库</td>
						<td>{{ data.detailObj?.storages }}</td>
					</tr>
					<tr>
						<td>货位</td>
						<td>{{ data.detailObj?.goodsPosition.goodsShelvesId }}</td>
					</tr>
					<tr>
						<td>出库数量</td>
						<td>{{ data.detailObj?.outQuantity }}</td>
					</tr>
					<tr>
						<td>单价</td>
						<td>{{ data.detailObj?.unitPrice }}</td>
					</tr>
					<tr>
						<td>可调数量</td>
						<td>{{ data.detailObj?.allowAdjustQuantity }}</td>
					</tr>
				</table>
			</div>
		</el-drawer>
		<el-image-viewer v-if="data.checkFlag" :url-list="data.imgUrl" @close="close"/>
	</div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import {backApi, manageApi} from "@/api/model/salesManagement";
import {functionIndex} from "@/views/commodity/functionIndex";
import {ElMessage} from "element-plus";
import tool from "@/utils/tool";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const childRef = ref(null)
const detailFlag = ref(false)
const multipleTableRef = ref(null)
const data = reactive({
	clientType: {
		value: "",
		type: []
	},
	handle: {
		value: "",
		type: []
	},
	checkFlag: false,
	imgUrl: []
})
const backForm = ref({
	n1: "",
	n2: [],
	n3: "",
	n4: ""
})
const creatform = ref(null)
const goodsTable = ref([])
const comeList = ref([])
const chooseGoods = ref([])
const emit = defineEmits([])
const uploadData = ref(null)
const headers = {
	Authorization: "Bearer " + tool.cookie.get("TOKEN"),
	ContentType: "multipart/form-data",
  clientType:'pc',
};
const LoadingFlag = ref(false)
const queryRef = ref()
const searchForm = ref({
	n1: '',
	n2: "",
	n3: "",
	n4: [],
	n5: "",
	size: 10,
	current: 1,
	total: 0
})
const creatRules = reactive({
	n1: [{required: true, message: "请输入调价说明", trigger: "blur"}],
	n2: [{required: true, message: "请上传附件", trigger: "blur"}],
});
const creatRules2 = reactive({
	n1: [{required: true, message: "请选择客户", trigger: "blur"}],
	n3: [{required: true, message: "请选择经手人", trigger: "blur"}],
});
const delPrice = (num1, num2) => {
	chooseGoods.value[num1].adjustList.splice(num2, 1)
	ElMessage.success('删除成功')
}
const newAdd = (row) => {
	if (!comeList.value.includes(row.id)) {
		comeList.value.push(row.id)
	}
	let allNum = 0
	row.adjustList?.forEach(item => {
		allNum += item.num
	})
	if (allNum < row.allowAdjustQuantity) {
		if (row.adjustList.length > 0) {
			if (row.adjustList[row.adjustList.length - 1].num != 0 && row.adjustList[row.adjustList.length - 1].price != 0) {
				row.adjustList.push({
					num: 0,
					price: 0
				})
			} else {
				ElMessage.error('请先录入全当前行调价信息')
			}
		} else {
			row.adjustList.push({
				num: 0,
				price: 0
			})
		}
	} else {
		ElMessage.error('可调数量已达上限')
	}
}
const maxFn = (ind1, ind2) => {
	let allNum = 0
	let chooseNum = 0
	chooseGoods.value[ind1].adjustList.forEach((item, index) => {
		allNum += item.num
		if (index != ind2) {
			chooseNum += item.num
		}
	})
	if (chooseGoods.value[ind1].allowAdjustQuantity <= allNum) {
		ElMessage.error('可调数量已达上限')
		chooseGoods.value[ind1].adjustList[ind2].num = chooseGoods.value[ind1].allowAdjustQuantity - chooseNum
	}
}
const detailFn = (row) => {
	detailFlag.value = true
	data.detailObj = row
}
const props = defineProps({
	active: {
		default: 1
	},
	UUID: {
		default: null
	}
})
const onchangeTable = (row) => {
	let num = comeList.value.indexOf(row.id)
	if (num === -1) {
		comeList.value.push(row.id)
	} else {
		comeList.value.splice(num, 1)
	}
}
const handleSelectionChange = (val) => {
	chooseGoods.value = val
	if (val.length > 1) {
		if (val[val.length - 2].customer != val[val.length - 1].customer) {
			multipleTableRef.value?.toggleRowSelection(chooseGoods.value[chooseGoods.value.length - 1], undefined)
			ElMessage.error('请选择相同客户')
		}
	}
}
const renderKey = (row) => {
	return row.id
}
const {active} = toRefs(props)

const handleQuery = async () => {
	if (!queryRef.value) return;
	await queryRef.value.validate((valid) => {
		if (valid) {
			if (searchForm.value.n1 == '' || searchForm.value.n1 == null) {
				ElMessage.error('请选择查询客户')
			} else {
				LoadingFlag.value = true
				backApi.getList({
					'erpCustomer.id': searchForm.value.n1,
					tradeName: searchForm.value.n2,
					handledById: searchForm.value.n3,
					beginOutTime: searchForm.value.n4 ? searchForm.value.n4[0] : null,
					endOutTime: searchForm.value.n4 ? changeTime(searchForm.value.n4[1]) : null,
					docNum: searchForm.value.n5,
					size: searchForm.value.size,
					current: searchForm.value.current,
					busType: '3'
				}).then(res => {
					if (res.code == 200) {
						searchForm.value.total = res.data.total
						goodsTable.value = res.data.records
						backForm.value.n3 = searchForm.value.n1
						backForm.value.n4 = searchForm.value.n3
						if (goodsTable.value.length > 0) {
							if (goodsTable.value[0].erpCustomer.id != searchForm.value.n1 || goodsTable.value[0].handledById != searchForm.value.n3) {
								multipleTableRef.value?.clearSelection()
								chooseGoods.value = []
							}
						} else {
							multipleTableRef.value?.clearSelection()
						}
					}
					LoadingFlag.value = false
				})
			}
		}
	});
}
const changeTime = (time) => {
	if (time) {
		let newTime = new Date(time)
		newTime = newTime.setDate(newTime.getDate() + 1);
		newTime = functionIndex.transformTimestampSearch(newTime)
		return newTime
	} else {
		return null
	}
}
const clientList = () => {
	manageApi
		.clientType({
			current: 1,
			size: 1000,
			isEnable: 0,
			status: 3,
			enterpriseName: data.clientType.value,
		})
		.then((res) => {
			if (res.code == 200) {
				data.clientType.type = res.data.records;
			}
		});
};
const handleList = () => {
	backApi
		.handleList({
			size: 1000,
			name: data.handle.value
		})
		.then((res) => {
			if (res.code == 200) {
				data.handle.type = res.data.records;
			}
		});
};

const beforeFile = (file) => {
	if (file.size > 5242880) {
		ElMessage.error("文件不能大于5M");
		return false;
	} else {
		uploadData.value = {
			uuid: props.UUID,
			fileGroup: "salesManagement",
			businessType: "SalesPriceAdjustment",
			fileType: 50
		}
	}
}

const successFile = (res) => {
	ElMessage.success("上传成功");
	backForm.value.n2[backForm.value.n2.length - 1].resFileUrl = res.data.fileUrl
};

const errorFile = () => {
	ElMessage.error("上传失败");
};

const handlePreview = (uploadFile) => {
	const fileName = uploadFile.name.split(".")
	data.imgUrl = []
	if (
		fileName[fileName.length - 1] == "gif" ||
		fileName[fileName.length - 1] == "jpeg" ||
		fileName[fileName.length - 1] == "apng" ||
		fileName[fileName.length - 1] == "jpg" ||
		fileName[fileName.length - 1] == "avif" ||
		fileName[fileName.length - 1] == "png" ||
		fileName[fileName.length - 1] == "svg" ||
		fileName[fileName.length - 1] == "webp"
	) {
		data.checkFlag = true;
		data.imgUrl.push(uploadFile.resFileUrl)
	}
}

const close = () => {
	data.checkFlag = false;
	data.imgUrl = []
}

onBeforeMount(() => {
	//console.log('2.组件挂载页面之前执行----onBeforeMount')
	clientList()
	handleList()
})
onMounted(() => {
	//console.log('3.-组件挂载到页面之后执行-------onMounted')
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
	chooseGoods,
	backForm,
	creatform,
	goodsTable,
	multipleTableRef,
	searchForm,
	comeList
})
const tableData = ref([])
</script>
<style lang='scss' scoped>

.el-select-dropdown__list {
	.el-input {
		width: 90%;
		margin-left: 5%;
		margin-top: 5px;
		margin-bottom: 15px;
	}

	.el-pagination {
		margin-right: 20px;
		margin-top: 10px;
		margin-bottom: 10px;
	}
}

.messTable {
	width: 100%;
	background-color: #eaedf3;
	display: grid;
	grid-template-columns: 1fr;
	padding: 1px 1px 0 1px;

	tr {
		margin-bottom: 1px;
		display: flex;
		background: #fff;

		td {
			background-color: white;
			line-height: 40px;
		}

		td:nth-child(1) {
			flex: 1;
			padding: 0 10px;
			font-weight: bold;
			color: #505050;
			background: #f7f7f7;
		}

		td:nth-child(2) {
			color: #606266;
			padding: 0 10px;
			flex: 3
		}
	}
}
</style>
