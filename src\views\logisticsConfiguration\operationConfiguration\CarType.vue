<template>
    <div class="app-container">
        <!--搜索-->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item label="车辆类型" prop="typeName">
                    <el-input v-model="queryParams.typeName" clearable placeholder="请输入车辆类型" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <el-button v-hasPermi="['transport:carType:add']" icon="el-icon-plus" size="mini" type="primary" @click="handleAdd(0, 0)">新增 </el-button>
                <right-toolbar v-model:showSearch="showSearch" table-i-d="CarType" @queryTable="getList"></right-toolbar>
            </div>
            <el-table v-loading="loading" :data="typeList" border row-key="typeCode">
                <el-table-column align="center" label="车辆类型" prop="typeName" show-overflow-tooltip width="150" />
                <el-table-column align="center" label="创建人" prop="createBy.name" />
                <el-table-column align="center" label="创建时间" prop="createDate" width="180" sortable>
                    <template #default="scope">
                        {{ formatDate(scope.row.createDate) }}
                    </template>
                </el-table-column>
                <el-table-column :formatter="statusFormat" align="center" label="车辆类型状态" prop="status"> </el-table-column>
                <el-table-column align="center" label="备注" prop="remark" show-overflow-tooltip width="200"> </el-table-column>
                <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="220">
                    <template #default="scope">
                        <el-button v-hasPermi="['transport:carType:add']" icon="el-icon-plus" link size="small" type="success" @click="handleAdd(scope.row.typeCode, scope.row.typeLevel)">新增 </el-button>
                        <el-button v-hasPermi="['transport:carType:edit']" icon="el-icon-edit" link size="small" type="warning" @click="handleUpdate(scope.row)">修改 </el-button>
                        <el-button v-hasPermi="['transport:carType:remove']" icon="el-icon-delete" link size="small" type="danger" @click="handleDelete(scope.row)">删除 </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 添加或修改车辆类型对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="500px">
            <el-form ref="form" :model="form" :rules="rules" label-width="150px">
                <el-form-item label="上级" prop="parentCode">
                    <el-tree-select v-model="form.parentCode" :data="typeOptions" node-key="value" placeholder="请选择上级车辆类型" style="width: 100%" value-key="value" />
                </el-form-item>
                <el-form-item label="车辆类型" prop="typeName">
                    <el-input v-model="form.typeName" maxlength="20" placeholder="请输入车辆类型" show-word-limit />
                </el-form-item>
                <el-form-item label="车辆类型级别" prop="typeLevel">
                    <el-input v-model="form.typeLevel" disabled placeholder="默认显示" />
                </el-form-item>
                <el-form-item label="车辆类型状态">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in statusOptions" :key="dict.value" :label="dict.value">{{ dict.name }} </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="60" placeholder="请输入备注" show-word-limit type="textarea" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import RightToolbar from '@/components/RightToolbar';
import operationConfiguration from '@/api/logisticsConfiguration/operationConfiguration.js';
import moment from 'moment';
import { handleTree } from '@/utils/index.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
export default {
    name: 'CarType',
    components: { SearchButton, RightToolbar },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            // 车辆类型表格数据
            typeList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                typeLevel: null,
                typeName: null,
                level: null,
                size: 1000
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                typeName: [{ required: true, message: '请输入车辆类型', trigger: 'blur' }]
            },
            typeOptions: [],
            // 状态
            statusOptions: []
        };
    },
    async created() {
        this.getList();
        this.statusOptions = await this.getDictList('sys_normal_disable');
        this.getTreeSelect();
    },
    methods: {
        /** 状态字典转换 */
        statusFormat(val) {
            return this.selectDictLabel(this.statusOptions, val.status);
        },
        // 标准时间格式化
        formatDate(cellValue) {
            return moment(cellValue).format('YYYY-MM-DD HH:mm:ss');
        },
        /** 查询车辆类型下拉树结构 */
        getTreeSelect() {
            operationConfiguration.listCarType({ size: 1000 }).then((response) => {
                this.typeOptions = [];
                if (response.code == 200) {
                    let data = response?.data?.records.map((item) => {
                        return { label: item.typeName, value: item.typeCode, parentId: item.parentCode };
                    });
                    data.push({ label: '主类型', typeCode: '0', value: '0', parentId: '' });
                    this.typeOptions = handleTree(data, 'value');
                }
            });
        },
        /** 查询车辆类型列表 */
        getList() {
            this.loading = true;
            operationConfiguration.listCarType(this.queryParams).then((response) => {
                if (response.code == 200) {
                    let data = response?.data?.records || [];
                    this.typeList = handleTree(data, 'typeCode', 'parentCode');
                }
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                typeName: null,
                parentCode: '0',
                typeLevel: null,
                status: '0',
                remark: null,
                Level: null
            };
            this.$refs['form'] ? this.$refs['form'].resetFields() : '';
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.handleQuery();
        },
        /** 新增按钮操作 */
        handleAdd(typeCode = '0', typeLevel = 0) {
            this.reset();
            this.form.typeLevel = parseInt(typeLevel) + 1;
            this.form.parentCode = typeCode + '';
            this.open = true;
            this.title = '添加车辆类型';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            operationConfiguration.queryCarTypeById({ id }).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = '修改车辆类型';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.id != null) {
                        operationConfiguration.saveCarType(this.form).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                                this.getTreeSelect();
                            }
                        });
                    } else {
                        operationConfiguration.saveCarType(this.form).then((response) => {
                            if (response.code == 200) {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                                this.getTreeSelect();
                            }
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            if (row.children && row.children.length > 0) {
                this.msgError('请先删除子级车辆类型');
                return false;
            }
            const ids = row.id || this.ids;
            const name = row.typeName;
            this.$confirm('是否确认删除车辆类型"' + name + '"的数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    return operationConfiguration.delCarType({ ids: ids });
                })
                .then(() => {
                    this.getList();
                    this.getTreeSelect();
                    this.msgSuccess('删除成功');
                })
                .catch(() => {});
        }
    }
};
</script>

<style lang="scss" scoped>
.Botm {
    .el-card__body {
        padding-bottom: 0px;
    }
}
::v-deep {
    .top-right-btn {
        .el-tooltip__trigger:nth-child(3) {
            display: none;
        }
    }
}
</style>
