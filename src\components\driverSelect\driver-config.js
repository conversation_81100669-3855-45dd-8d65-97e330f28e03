import driver from '@/api/logisticsConfiguration/operationConfiguration.js';
import request from '@/utils/request';

// 获取承运商所有可用司机

// 派单获取司机信息
export const LAN_DRIVER_LIST = (params) => request.get('/tms/driver/lanDriver/pcDriverSelect', params);

// 需要返回一个promise
async function loadDriver(params, type = 1) {
    let nodeData = [];
    let drivers = {};
    if (type == 2) {
        drivers = await LAN_DRIVER_LIST(params);
        if (drivers.code == '200' && drivers.data) {
            nodeData = drivers.data.records.map((item) => {
                return item;
            });
        }
    } else {
        drivers = await driver.getDriverList(params);
        if (drivers.code == '200' && drivers.data) {
            nodeData = drivers.data.map((item) => {
                return { driverCode: item.driverCode, driverName: item.driverName, driverPhone: item.driverPhone };
            });
        }
    }

    return nodeData;
}

//司机选择
export const DRIVER_SELECT_CONFIG = {
    tabName: '指定司机', // 选项卡名称
    type: 'flow_driver', //选项卡键值 传入的selected要和键值保持一致 eg: {dep: [], role: []}
    children: 'children', // 子节点标志
    // 生成每个节点的id 保证唯一
    nodeId: function (data) {
        return data.driverCode;
    },
    // // 生成节点的名称 可选值 string | function
    label: function (data, node) {
        return data.label || data.driverName;
    },
    getList(queryDriverParams, type = 1) {
        return loadDriver(queryDriverParams, type);
    },
    checkedHandle(data) {
        return data.map((item) => {
            return item.driverCode;
        });
    }
};

export const CONFIG_LIST = [DRIVER_SELECT_CONFIG];
