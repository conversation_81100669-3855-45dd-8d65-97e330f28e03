<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-17 15:38:33
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-09-04 16:20:47
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\views\assist\purchasingManagement\component\basicInformation.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div>
        <el-form :model="form" ref="queryRef" :inline="true"  :rules="rules" class="form_130">
            <div class="box">
                <el-form-item label="供应商" prop="supplier">
                    <el-select v-model="form.supplier" filterable placeholder="请选择供应商" style="width: 100%;"
                        @change="(value) => handleChange(value, 'supplier')" :disabled="modalType == 'detail'" clearable>
                        <el-option :label="item.enterpriseName" :value="item.id" v-for="item in supplierList"
                            :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="供应商代表" prop="supplierRepresentative">
                    <el-select v-model="form.supplierRepresentative" filterable
                        :placeholder="!form.supplier ? '请先选择供应商' : '请选择供应商代表'" style="width: 100%;"
                        :disabled="(modalType == 'detail') || (!form.supplier) || (supplierPeopleList.length == 1)"
                        clearable @change="(value) => handleChange(value, 'supplierRepresentative')">
                        <el-option :label="item.delegateName" :value="item.id" v-for="item in supplierPeopleList"
                            :key="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="结算方式" prop="settlementMethod">
                    <el-select v-model="form.settlementMethod" placeholder="请选择结算方式" style="width: 100%;"
                        :disabled="modalType == 'detail'" @change="(value) => handleChange(value, 'settlementMethod')"
                        clearable>
                        <el-option :label="item.name" :value="item.value" v-for="item in settlementMethod"
                            :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="付款期限" prop="termsPayment">
                    <el-date-picker v-model="form.termsPayment" type="date" placeholder="请选择付款期限"
                        :disabled="modalType == 'detail'" style="width: 100%;" :disabled-date="disabledDate" clearable />
                </el-form-item>
                <el-form-item label="物流方式" prop="logisticsMode">
                    <el-select v-model="form.logisticsMode" placeholder="请选择物流方式" style="width: 100%;"
                        :disabled="modalType == 'detail'" clearable>
                        <el-option :label="item.name" :value="item.value" v-for="item in logisticsMethodsList"
                            :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="库号" prop="warehouseNumber">
                    <el-select v-model="form.warehouseNumber" placeholder="请选择库号" style="width: 100%;"
                        :disabled="modalType == 'detail'" @change="(value) => handleChange(value, 'warehouseNumber')"
                        clearable>
                        <el-option :label="item.warehouseNumber" :value="item.id" v-for="item in warehouseNumberList"
                            :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="经手人" prop="handledBy">
                    <el-select v-model="form.handledBy" clearable :placeholder="form.warehouseNumber ? '请选择经手人' : '请先选择库号'"
                        style="width: 100%;" :disabled="modalType == 'detail' || !form.warehouseNumber">
                        <el-option :label="item.handledBy.name" :value="item.handledBy.id" v-for="item in handledByList"
                            :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="制单人" prop="preparedBy">
                    <el-input v-model="form.preparedBy" placeholder="请输入制单人" clearable maxlength="100" show-word-limit
                        style="width: 100%;" :disabled="true" />
                </el-form-item>

                <el-form-item label="自营扣率" prop="selfRate">
                    <el-select v-model="form.selfRate" placeholder="请选择自营扣率" style="width: 100%;" clearable
                        :disabled="modalType == 'detail'">
                        <el-option :label="item.name" :value="item.value" v-for="item in deductionRateList"
                            :key="item.value" />
                    </el-select>
                </el-form-item>


                <el-form-item label="折扣金额" prop="discountAmount">
                    <el-input-number v-model="form.discountAmount" placeholder="请输入折扣金额" :min="0" :precision="2" clearable
                        :disabled="modalType == 'detail'" style="width: 100%;height: 100%;" />
                </el-form-item>
                <!-- <el-form-item> -->
                <el-checkbox v-model="form.isGenerateContract" label="1" size="large"
                    :disabled="modalType == 'detail' || form.settlementMethod == '2'">是否生成合同</el-checkbox>
                <!-- </el-form-item> -->
            </div>
            <div class="box_2">
                <el-form-item label="备注" prop="remark" style="width: 98%;">
                    <el-input v-model="form.remark" placeholder="请输入备注" clearable type="textarea" maxlength="1000"
                        show-word-limit style="width: 100%;" :disabled="modalType == 'detail'" />
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script setup>
import {defineExpose, defineProps, getCurrentInstance, onMounted, reactive, ref, toRefs, watch} from 'vue'
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import purchasingManagement from '@/api/erp/purchasingManagement'
import tool from '@/utils/tool';

const { proxy } = getCurrentInstance();
const settlementMethod = ref([])
const logisticsMethodsList = ref([])
const deductionRateList = ref([])
const warehouseNumberList = ref([])
const handledByList = ref([])
const isRulesPass = ref(false)
const supplierList = ref([])
const supplierPeopleList = ref([])
const data = reactive({
    form: {
    },
    rules: {
        supplier: [{ required: true, message: "供应商不能为空", trigger: "change" }],
        supplierRepresentative: [{ required: true, message: "供应商代表不能为空", trigger: "change" }],
        settlementMethod: [{ required: true, message: "结算方式不能为空", trigger: "change" }],
        termsPayment: [{ required: true, message: "付款期限不能为空", trigger: "change" }],
        // logisticsMode: [{ required: true, message: "物流方式不能为空", trigger: "change" }],
        warehouseNumber: [{ required: true, message: "库号不能为空", trigger: "change" }],
        handledBy: [{ required: true, message: "经手人不能为空", trigger: "change" }],
        preparedBy: [{ required: true, message: "制单人不能为空", trigger: "blur" }],
        // remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
        // selfRate: [{ required: true, message: "自营扣率不能为空", trigger: "change" }],
    },
});
const props = defineProps({
    modalType: {
        type: String,
        default: ""
    },
    essentialInformation: {
        type: Object,
        default: () => { }
    }
})
const { form, rules } = toRefs(data);
const { modalType, essentialInformation } = toRefs(props);
const reset = () => {
    form.value = {}
}
const disabledDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7
}
watch(() => [modalType.value, essentialInformation.value], (newValue, oldValue) => {
    if (modalType.value == 'edit' || modalType.value == 'detail' || modalType.value == 'draft') {
        form.value = essentialInformation.value
        purchasingManagement.getSupplierProductionDelegate({ 'supplier.id': form.value.supplier,size:9999,current:1 }).then(res => {
            if (res.code == 200) {
                supplierPeopleList.value = res.data?.records
                form.value.certificateCard = supplierPeopleList.value.filter(v => v.id === form.value.supplierRepresentative)[0]?.certificateCard
            }
        })
        warehouseNumberManagement.getWarehouseNumberId({ wnid: form.value.warehouseNumber,size:9999,current:1 }).then(res => {
            if (res.code == 200) {
                handledByList.value = res.data

            }
        })
    }
}, { deep: true, immediate: true })
const rulesSubmit = () => {
  proxy.$refs["queryRef"].validate((valid, fields) => {
        if (valid) {
            isRulesPass.value = true
        } else {
            isRulesPass.value = false
          Object.keys(fields).forEach((key, i) => {
            const propName = fields[key][0].field
            if (i == 0) {
              proxy.$refs["queryRef"].scrollToField(propName)
            }
          })
        }
    })
}
const handleChange = (value, type) => {
    if (type == 'settlementMethod' && value == '2') {
        form.value.isGenerateContract = true
    }
    if (type == 'supplierRepresentative') {
        form.value.certificateCard = supplierPeopleList.value.filter(v => v.id === value)[0]?.certificateCard
    }
    if (type == 'supplier') {
        form.value.certificateCard = undefined
        purchasingManagement.getSupplierProductionDelegate({ 'supplier.id': value,size:9999,current:1 }).then(res => {
            if (res.code == 200) {
                supplierPeopleList.value = res.data?.records
                if (supplierPeopleList.value?.length === 1) {
                    form.value.supplierRepresentative = supplierPeopleList.value[0].id
                    form.value.certificateCard = supplierPeopleList.value[0]?.certificateCard
                }
            }
        })
    }
    if (type == 'warehouseNumber') {
        form.value.handledBy = undefined
        warehouseNumberManagement.getWarehouseNumberId({ wnid: form.value.warehouseNumber,size:9999,current:1 }).then(res => {
            if (res.code == 200) {
                handledByList.value = res.data

            }
        })
    }
}
function getHandleBy() {
    warehouseNumberManagement.getList({size:9999,current:1,status:'1',isStop:'0'}).then(res => {
        if (res.code == 200) {
            warehouseNumberList.value = res.data.records
        }
    })
}
async function dict() {
    settlementMethod.value = await proxy.getDictList('erp_settlement_method') // 结算方式
    logisticsMethodsList.value = await proxy.getDictList('erp_logistics_methods') // 物流方式
    deductionRateList.value = await proxy.getDictList('serp_elfoperated_deduction_rate') // 自营扣率
    if (!form.value.preparedBys) {
        form.value.preparedBys = tool.data.get("USER_INFO")?.id
    }
    if (!form.value.preparedBy) {
        form.value.preparedBy = tool.data.get("USER_INFO")?.name
    }
}
/**
 * @description: 获取供应商
 * @return {*}
 */
const getSupplier = () => {
    purchasingManagement.getSupplierProduction({ status: 3, customLabel: 2,size:9999,current:1 }).then(res => {
        if (res.code == 200) {
            supplierList.value = res.data?.records
        }
    })
}

onMounted(() => {
    dict()
    getHandleBy()
    getSupplier()
})

defineExpose({
    form,
    rulesSubmit,
    isRulesPass,
    reset
})
</script>

<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    justify-items: stretch;
    align-items: stretch;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    justify-items: stretch;
    align-items: stretch;
}

::v-deep .el-input.is-disabled .el-input__inner {
    color: #333 !important;
    -webkit-text-fill-color: #333 !important;
}
</style>
