<template>
  <div class="app-container">
    <!--  /搜索区域  -->
    <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
      <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" @submit.native.prevent>
        <el-form-item label="增值服务" prop="name">
          <el-input v-model="queryParams.name" clearable placeholder="请输入增值服务" @clear="handleQuery" @keyup.enter.native="handleQuery"></el-input>
        </el-form-item>
        <el-form-item class="last-form-item">
          <el-button icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" type="info" @click="resetQuery('queryForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- / 表格内容 -->
    <el-card :body-style="{ padding: '10px' }" shadow="never">
      <div style="margin-bottom: 10px">
        <el-button type="primary" @click="openValueAddedService('add')">新增</el-button>
        <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="valueAddedServiceChargeConfigurationTable" @queryTable="getList" />
      </div>
      <column-table v-loading="loading" :columns="columns" :data="orderList" :showIndex="true">
        <template slot="defaultFormula" slot-scope="{ row }">
          <span>{{ formulaFilter(row) }}</span>
        </template>
        <template #status="{ row }">
          <el-switch v-model="row.status" :before-change="() => changeStatus(row)" active-color="#13ce66" active-text="启用" active-value="0" inactive-text="禁用" inactive-value="1" inline-prompt />
          <!--          <el-button icon="el-icon-edit" plain  style="padding: 5px 10px" type="primary" @click="openValueAddedService('edit', row)">修改</el-button> -->
        </template>
      </column-table>
      <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" @pagination="getList" />
    </el-card>

    <!-- /新增增值服务 抽屉  -->
    <el-drawer v-model="newValueAddedServicesVisible" :title="newValueAddedServicesTitle" size="35vw" @close="hideNewValueAddedServices()">
      <div v-loading="newValueAddedServicesLoading" :element-loading-text="newValueAddedServicesLoadingText" style="background-color: #f2f2f2; padding: 10px">
        <el-card shadow="never">
          <el-form ref="valueAddedServicesForm" :model="valueAddedServicesForm" :rules="valueAddedServicesRules" class="valueAddedServicesForm" label-width="auto">
            <el-form-item label="增值服务名称" prop="name">
              <el-input v-model="valueAddedServicesForm.name" clearable placeholder="请输入增值服务名称"></el-input>
            </el-form-item>
            <el-form-item label="是否需要输入" prop="isNeedInput">
              <el-radio-group v-model="valueAddedServicesForm.isNeedInput" @change="clearCalculationFormula">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="valueAddedServicesForm.isNeedInput == 1" label="增值服务参数" prop="parameter">
              <el-input v-model="valueAddedServicesForm.parameter" clearable placeholder="请输入增值服务参数" @input="clearCalculationFormula"></el-input>
            </el-form-item>
            <el-form-item v-if="valueAddedServicesForm.isNeedInput == 1" label="参数英文名称" prop="englishName">
              <el-input v-model="valueAddedServicesForm.englishName" clearable placeholder="请输入参数英文名称"></el-input>
            </el-form-item>
            <el-form-item v-if="valueAddedServicesForm.isNeedInput == 1" label="输入单位" prop="unit">
              <el-select v-model="valueAddedServicesForm.unit" clearable placeholder="请选择输入单位">
                <el-option v-for="item in unitList" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <!--            <el-form-item label="默认计算公式">
              <div class="tag__box">
                <div class="tag__m">
                  <span v-for="(item, index) in valueAddedServicesForm.defaultFormulaFormat" :key="index">{{ item.text }}</span>
                  <span v-if="valueAddedServicesForm.defaultFormulaFormat.length > 0" class="btn__revoke" @click="revokeFormula">撤销</span>
                </div>
                <el-button type="text" @click="clearCalculationFormula()">清空</el-button>
              </div>
            </el-form-item>
            <el-form-item label="参数选择">
              <div style="display: flex">
                <el-button v-if="valueAddedServicesForm.parameter && valueAddedServicesForm.isNeedInput == 1" @click="setParameterSelection('self', valueAddedServicesForm.parameter)">{{ valueAddedServicesForm.parameter }}</el-button>
                <el-button @click="setParameterSelection('price', '单项价格')">单项价格</el-button>
              </div>
            </el-form-item>
            <el-form-item label="运算符号">
              <div style="display: flex">
                <el-button @click="setParameterSelection('*')">*</el-button>
                <el-button @click="setParameterSelection('+')">+</el-button>
              </div>
            </el-form-item>
            <el-form-item label="运算数字">
              <div class="box__numberButton">
                <el-button v-for="(item, index) in 10" :key="index" @click="setParameterSelection(index)">{{ index }}</el-button>
                <el-button @click="setParameterSelection('.')">.</el-button>
              </div>
            </el-form-item>-->
          </el-form>
        </el-card>
        <div slot="footer" class="dialog-footer" style="display: flex; justify-content: end; margin-top: 10px">
          <el-button type="info" @click="hideNewValueAddedServices()">取消</el-button>
          <el-button v-if="isAdd" type="primary" @click="submitNew()">确定</el-button>
          <el-button v-else type="primary" @click="submitModification()">修改</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import RightToolbar from '@/components/RightToolbar/index.vue';
import valueAddedServiceChargeConfiguration from '@/api/platformFeatures/valueAddedServiceChargeConfiguration';

export default {
  name: 'ValueAddedServiceChargeConfiguration',
  components: {
    RightToolbar,
    ColumnTable
  },
  data() {
    return {
      showSearch: true,
      queryParams: {
        current: 1,
        size: 10,
        name: null
      },
      columns: [
        { title: '增值服务名称', key: 'name', align: 'center', columnShow: true, minWidth: '200px' },
        { title: '增值服务参数', key: 'parameter', align: 'center', columnShow: true },
        { title: '参数英文名称', key: 'englishName', align: 'center', columnShow: true },
        // { title: '默认计算公式', key: 'defaultFormula', align: 'center', columnShow: true },
        { title: '状态', key: 'status', align: 'center', width: '120px', columnShow: true, hideFilter: true, fixed: 'right' }
      ],
      loading: false,
      orderList: [],
      total: 0,
      newValueAddedServicesVisible: false,
      newValueAddedServicesTitle: '新增增值服务',
      newValueAddedServicesLoading: false,
      newValueAddedServicesLoadingText: '新增中...',
      valueAddedServicesForm: {
        name: '',
        parameter: '',
        englishName: '',
        unit: '',
        isNeedInput: '0',
        defaultFormula: '',
        defaultFormulaFormat: []
      },
      valueAddedServicesRules: {
        name: [{ required: true, message: '请输入增值服务名称', trigger: 'blur' }],
        parameter: [{ required: true, message: '请输入增值服务参数', trigger: 'blur' }],
        englishName: [{ required: true, message: '请输入参数英文名称', trigger: 'blur' }],
        unit: [{ required: true, message: '请选择单位', trigger: 'blur' }]
      },
      unitList: [],
      isAdd: true
    };
  },
  created() {
    this.getDict();
    this.getList();
  },
  methods: {
    /**
     * 获取字典值
     * @returns {Promise<void>}
     */
    async getDict() {
      this.unitList = await this.getDictList('fourpl_added_service_unit');
    },
    clearCalculationFormula() {
      this.valueAddedServicesForm.defaultFormulaFormat = [];
    },
    formulaFilter(data) {
      const { defaultFormula } = data;
      if (defaultFormula) {
        const defaultFormulaFormat = [];
        // defaultFormula 根据 0-9 '.' '*' '+'  字母 拆分成数组
        // self1*price 拆分为['self', '1', '*', 'price']
        const defaultFormulaArr = defaultFormula.match(/([0-9]+)|(\.)|(\*)|(\+)|([a-zA-Z]+)/g);
        // const defaultFormulaArr = defaultFormula.split(/(\d+\.?\d*)/);
        defaultFormulaArr.forEach((item) => {
          if (item) {
            if (item.match(/(\d+\.?\d*)/)) {
              defaultFormulaFormat.push({ type: '', value: item, text: item });
            } else {
              if (item === 'price') {
                defaultFormulaFormat.push({ type: 'text', value: item, text: '单项价格' });
              } else if (item === 'self') {
                defaultFormulaFormat.push({ type: 'text', value: item, text: data.parameter || '' });
              } else {
                defaultFormulaFormat.push({ type: 'text', value: item, text: item });
              }
            }
          }
        });
        return defaultFormulaFormat.map((item) => item.text).join('');
      }
    },
    getList() {
      this.loading = true;
      valueAddedServiceChargeConfiguration
        .addedServiceList(this.queryParams)
        .then((res) => {
          if (res.code === 200 && res.data.records) {
            this.orderList = res.data.records || [];
            this.total = res.data.total || 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    // 关闭新增增值服务弹窗
    hideNewValueAddedServices() {
      this.newValueAddedServicesVisible = false;
      this.resetForm('valueAddedServicesForm');
      this.valueAddedServicesForm = {
        name: '',
        parameter: '',
        englishName: '',
        isNeedInput: '0',
        defaultFormula: '',
        defaultFormulaFormat: []
      };
    },
    // 打开新增增值服务弹窗
    openValueAddedService(type, row) {
      this.newValueAddedServicesVisible = true;
      if (type === 'add') {
        this.newValueAddedServicesTitle = '新增增值服务';
        this.isAdd = true;
      } else {
        const { id } = row;
        this.newValueAddedServicesTitle = '修改增值服务';
        this.newValueAddedServicesLoading = true;
        this.newValueAddedServicesLoadingText = '加载中...';
        this.isAdd = false;
        addedServiceDetailPlatformSide(id)
          .then((res) => {
            if (res.code === 200) {
              const { defaultFormula, isNeedInput } = res.data;
              if (defaultFormula) {
                const defaultFormulaFormat = [];
                // defaultFormula 根据 0-9 '.' '*' '+'  字母 拆分成数组
                // self1*price 拆分为['self', '1', '*', 'price']
                const defaultFormulaArr = defaultFormula.match(/([0-9]+)|(\.)|(\*)|(\+)|([a-zA-Z]+)/g);
                // const defaultFormulaArr = defaultFormula.split(/(\d+\.?\d*)/);
                defaultFormulaArr.forEach((item) => {
                  if (item) {
                    if (item.match(/(\d+\.?\d*)/)) {
                      defaultFormulaFormat.push({ type: '', value: item, text: item });
                    } else {
                      if (item === 'price') {
                        defaultFormulaFormat.push({ type: 'text', value: item, text: '单项价格' });
                      } else if (item === 'self') {
                        defaultFormulaFormat.push({ type: 'text', value: item, text: res.data.parameter || '' });
                      } else {
                        defaultFormulaFormat.push({ type: 'text', value: item, text: item });
                      }
                    }
                  }
                });
                res.data.defaultFormulaFormat = defaultFormulaFormat;
              }
              if (isNeedInput) {
                res.data.isNeedInput = '1';
              } else {
                res.data.isNeedInput = '0';
              }
              Object.assign(this.valueAddedServicesForm, res.data);
            }
          })
          .finally(() => {
            this.newValueAddedServicesLoading = false;
          });
      }
    },
    resetQuery(formName) {
      this.$refs[formName].resetFields();
      this.handleQuery();
    },
    setParameterSelection(type, val) {
      // type 等于 self 或者 price 拼接 val
      if (type === 'self' || type === 'price') {
        this.valueAddedServicesForm.defaultFormulaFormat.push({
          type: 'text',
          value: type,
          text: val
        });
      } else {
        this.valueAddedServicesForm.defaultFormulaFormat.push({
          type: '',
          value: type,
          text: type
        });
      }
    },
    submitModification() {
      this.$refs.valueAddedServicesForm.validate((valid) => {
        if (valid) {
          this.newValueAddedServicesLoading = true;
          this.newValueAddedServicesLoadingText = '新增中...';
          // 对公式进行处理
          const defaultFormulaFormat = this.valueAddedServicesForm.defaultFormulaFormat;
          let defaultFormula = '';
          defaultFormulaFormat.forEach((item) => {
            if (item.type === 'text') {
              defaultFormula += item.value;
            } else {
              defaultFormula += item.value;
            }
          });
          this.valueAddedServicesForm.defaultFormula = defaultFormula;
          updateAddedServicePlatformSide(this.valueAddedServicesForm)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({ title: '成功', message: '修改增值服务成功', type: 'success', duration: 2000 });
                this.hideNewValueAddedServices();
                this.getList();
              } else {
                this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
              }
            })
            .finally(() => {
              this.newValueAddedServicesLoading = false;
            });
        }
      });
    },
    // 确定新增增值服务
    submitNew() {
      this.$refs.valueAddedServicesForm.validate((valid) => {
        if (valid) {
          this.newValueAddedServicesLoading = true;
          this.newValueAddedServicesLoadingText = '新增中...';
          // 对公式进行处理
          const defaultFormulaFormat = this.valueAddedServicesForm.defaultFormulaFormat;
          let defaultFormula = '';
          defaultFormulaFormat.forEach((item) => {
            if (item.type === 'text') {
              defaultFormula += item.value;
            } else {
              defaultFormula += item.value;
            }
          });
          this.valueAddedServicesForm.defaultFormula = defaultFormula;
          valueAddedServiceChargeConfiguration
            .addAddedService(this.valueAddedServicesForm)
            .then((res) => {
              if (res.code === 200) {
                this.$notify({ title: '成功', message: '新增增值服务成功', type: 'success', duration: 2000 });
                this.newValueAddedServicesVisible = false;
                this.getList();
              } else {
                this.$notify({ title: '失败', message: res.msg, type: 'error', duration: 2000 });
              }
            })
            .finally(() => {
              this.newValueAddedServicesLoading = false;
            });
        }
      });
    },
    // 撤销操作
    revokeFormula() {
      this.valueAddedServicesForm.defaultFormulaFormat.pop();
    },
    // 修改增值服务 状态
    changeStatus(row) {
      const { id, status } = row;
      this.loading = true;
      const params = {
        id,
        status: status === '0' ? '1' : '0'
      };
      if (id && status) {
        valueAddedServiceChargeConfiguration
          .updateAddServiceStatus(params)
          .then((res) => {
            if (res.code === 200) {
              this.$message.success('修改成功');
              this.getList();
            } else {
              this.$message.error(res.msg);
            }
          })
          .finally(() => {
            this.loading = true;
          });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}
.dialog__deleteValueAddedService {
  :deep(.el-dialog__header) {
    padding-bottom: 20px;
  }

  :deep(.el-result) {
    padding: 0;
  }
}
.tag__box {
  display: flex;
  grid-gap: 10px;
}
.tag__m {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
  padding: 0 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 13px;
}
.box__numberButton {
  display: flex;
  flex-wrap: wrap;
  grid-gap: 10px;
  .el-button + .el-button {
    margin-left: 0;
  }
}
.btn__revoke {
  margin-left: auto;
  color: #5670fe;
  cursor: pointer;
}
</style>
