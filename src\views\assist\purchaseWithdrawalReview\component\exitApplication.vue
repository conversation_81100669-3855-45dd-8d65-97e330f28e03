<template>
    <div class="app-container" v-if="open">
        <!-- 添加或修改角色配置对话框 -->
        <el-dialog :title="title" v-model="open" width="85%" :before-close="close">
            <div v-loading="loading">
                <div class="step" v-if="modalType != 'detail'">
                    <el-steps :active="active" align-center>
                        <el-step title="选择入库单据" />
                        <el-step title="填写退货信息" />
                        <el-step title="提交申请" />
                    </el-steps>
                </div>
                <div class="step1 " v-show="active == 1">
                    <el-form :model="queryParams" ref="queryParamsRef" :inline="true" class="form_130"
                        :rules="queryParamsRules" v-if="modalType != 'detail'">
                        <div class="box">
                            <el-form-item label="供应商" prop="supplierId">
                                <el-select v-model="queryParams.supplierId" filterable placeholder="请选择供应商" class="form_225"
                                    clearable>
                                    <el-option :label="item.enterpriseName" :value="item.id" v-for="item in supplierList"
                                        :key="item.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="商品名称" prop="tradeName">
                                <el-input v-model="queryParams.tradeName" placeholder="请输入商品名称" clearable
                                    class="form_225" />
                            </el-form-item>
                            <el-form-item label="经手人" prop="creatorId">
                                <el-select v-model="queryParams.creatorId" filterable placeholder="请选择经手人" clearable
                                    class="form_225" :disabled="roleList.length == 1 && roleList[0].enName == 'ywjl'">
                                    <el-option v-for="item in optionList" :key="item.id" :label="item.name"
                                        :value="item.id" />
                                </el-select>
                            </el-form-item>

                            <el-form-item label="入库日期" prop="createDate">
                                <div class="box_date">
                                    <el-date-picker v-model="queryParams.createDate" type="daterange" range-separator="至"
                                        start-placeholder="开始日期" end-placeholder="结束日期" class="form_225" clearable />
                                </div>
                            </el-form-item>
                            <el-form-item label="单据编号" prop="orderCode">
                                <el-input v-model="queryParams.orderCode" placeholder="请输入单据编号" clearable
                                    class="form_225" />
                            </el-form-item>
                            <el-button type="primary" @click="step1_getList" class="step1_search_btn">查询</el-button>
                        </div>
                    </el-form>
                    <h4 class="titleH4" v-if="modalType == 'detail'">
                        申请编号:<span
                            style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;"> {{
                                detailData.applyNo ||
                                '--' }}</span>
                        申请日期: <span
                            style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;"> {{
                                detailData.applyDate ?
                                moment(detailData.applyDate).format('YYYY-MM-DD') : '--' }}</span>
                        申请人:<span
                            style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;"> {{
                                detailData.createBy &&
                                detailData.createBy.name || '--' }}</span>
                        审核状态: <span
                            style="color: #505050;font-weight: 500;margin-right:10px;margin-left:10px;font-size: 13px;">{{
                                formDict(reviewStatus,
                                    detailData.auditStatus) }}</span> </h4>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px" v-if="modalType == 'detail'">退货明细</h3>
                    <el-table :data="step1_list" border style="margin-top: 20px;" row-key="id"
                        ref="entrust_out_table_listRef" v-loading="loading"
                        @selection-change="handleSelectionChange_step1_list">
                        <el-table-column type="selection" min-width="55" align="center" :reserve-selection="true"
                            fixed="left" v-if="modalType != 'detail'" />
                        <el-table-column label="单据编号" prop="orderCode" :show-overflow-tooltip="true" align="center"
                            min-width="170" />
                        <el-table-column label="供应商" :prop="modalType == 'detail' ? 'supplier.enterpriseName' : 'suppier'"
                            :show-overflow-tooltip="true" align="center" min-width="170" />
                        <el-table-column label="经手人" prop="creator" :show-overflow-tooltip="true" align="center"
                            min-width="120" v-if="modalType !== 'detail'" />
                        <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="自编码" prop="commodity.commoditySelfCode" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="规格" prop="commodity.packageSpecification" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="生产厂家"
                            :prop="modalType == 'detail' ? 'manufactureName' : 'commodity.manufactureName'" align="center"
                            min-width="120" />
                        <el-table-column label="产地" prop="commodity.producingArea" align="center" min-width="120" />
                        <el-table-column label="剂型" prop="commodity.dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="基本单位" :prop="modalType == 'detail' ? 'commodityBasicUnit' : 'basicUnit'"
                            :show-overflow-tooltip="true" align="center" min-width="80" />


                        <el-table-column label="批号" :prop="modalType == 'detail' ? 'batchNumber' : 'intoNo'"
                            :show-overflow-tooltip="true" align="center" min-width="160" />
                        <el-table-column label="生产日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                            min-width="120"
                            :formatter="row => row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="有效期" prop="commodity.validityTime" :show-overflow-tooltip="true"
                            align="center" min-width="120"><template #default="scope">
                                <p>{{ moment(scope.row.produceDate).add(isNaN(Number(scope.row.commodity.validityTime)) ? 0
                                    : Number(scope.row.commodity.validityTime), 'months').format('YYYY-MM-DD')
                                }}</p>
                            </template>
                        </el-table-column>
                        <el-table-column label="成本单价" prop="unitPrice" :show-overflow-tooltip="true" align="center"
                            min-width="80" />
                        <el-table-column label="可退数量" prop="currentInventory" :show-overflow-tooltip="true" align="center"
                            min-width="80" v-if="modalType !== 'detail'" />
                        <el-table-column label="库存余量" prop="currentInventory" :show-overflow-tooltip="true" align="center"
                            min-width="80" v-if="modalType !== 'detail'" />
                        <el-table-column label="申退数量" prop="retreatQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="120" v-if="modalType == 'detail'" fixed="right" />
                        <el-table-column label="申退金额" prop="retreatAmount" :show-overflow-tooltip="true" align="center"
                            min-width="120" v-if="modalType == 'detail'" fixed="right">
                            <template #default="scope">
                                <p>{{ (scope.row.retreatQuantity *
                                    (isNaN(Number(scope.row.unitPrice || 0)) ? 0 : Number(scope.row.unitPrice ||
                                        0))).toFixed(2)
                                }}</p>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div style="display:flex;justify-content:end;" v-if="modalType != 'detail'">
                        <pagination v-show="step1_list_Total > 0" :total="step1_list_Total"
                            v-model:page="queryParams.current" v-model:limit="queryParams.size"
                            @pagination="step1_getList" />
                    </div>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;" v-if="modalType == 'detail'">
                        退货信息
                    </h3>
                    <table border="0" cellpadding="0" cellspacing="1" class="messTable" v-if="modalType == 'detail'">
                        <tr v-if="form.retreatAmount">
                            <th>合计申退金额</th>
                            <th>{{ form.retreatAmount.toFixed(2) }}</th>
                        </tr>
                        <tr>
                            <th>合计申退数量</th>
                            <th>{{ form.retreatQuantity }}</th>
                        </tr>
                        <tr>
                            <th>退货原因</th>
                            <th>{{ form.returnReason == '3' ? form.otherReasons : formDict(reasonForReturn,
                                form.returnReason)
                            }}</th>
                        </tr>
                        <tr>
                            <th>货物处理</th>
                            <th>{{ formDict(cargoHandling, form.cargoHandling) }}</th>
                        </tr>
                        <tr>
                            <th>款项处理</th>
                            <th>{{ formDict(paymentProcessing, form.payProcess) }}</th>
                        </tr>
                        <tr>
                            <th>物流方式</th>
                            <th>{{ formDict(logisticsMethods, form.logisticsMethods) }}</th>
                        </tr>
                        <tr>
                            <th>三方物流</th>
                            <th>{{ formDict(thirdPartyIogistics, form.thirdLogistics) }}</th>
                        </tr>
                        <tr>
                            <th>收货地址</th>
                            <th>{{ form.deliveryAddressCn }}</th>
                        </tr>
                        <tr>
                            <th>详细地址</th>
                            <th>{{ form.detailAddress }}</th>
                        </tr>
                        <tr>
                            <th>收货人姓名</th>
                            <th>{{ form.name }}</th>
                        </tr>
                        <tr>
                            <th>收货人电话</th>
                            <th>{{ form.phone }}</th>
                        </tr>
                        <tr style="background:#fff">
                            <th>收货人电话</th>
                            <th>{{ form.phone }}</th>
                        </tr>
                    </table>
                    <div class="remark" v-if="modalType == 'detail'">
                        <tr style="width: 100%;">
                            <th style="flex: 1;">备注</th>
                            <th style="flex: 12;text-align: left;">{{ form.remark }}</th>
                        </tr>
                    </div>
                    <div class="file" v-if="modalType == 'detail'">
                        <tr style="width: 100%;">
                            <th style="flex: 1;">附件</th>
                            <th
                                style="flex: 12;text-align: left;display: flex;flex-direction: column;align-items: flex-start;">
                                <p v-for="(item, index) in form.fileList" :key="index" @click="handlePreview(item)">{{
                                    item.fileName }}
                                </p>
                            </th>
                        </tr>
                    </div>
                    <h3 class="el-dialog__title" style="margin-bottom: 10px;margin-top: 20px;" v-if="modalType == 'detail'">
                        出库记录
                    </h3>
                    <el-table :data="outboundList" border style="margin-top: 20px;" v-if="modalType == 'detail'">
                        <el-table-column label="自编码" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="商品名称" prop="tradeName" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="批号" prop="intoNo" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="采退出库单编号" prop="packageSpecification" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="出库时间" prop="manufacture.enterpriseName" align="center" min-width="120" />
                        <el-table-column label="出库数量" prop="originPlace" align="center" min-width="120" />
                        <el-table-column label="出库状态" prop="dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                    </el-table>
                </div>
                <div class="step2" v-show="active == 2">

                    <el-table :data="choose_setp1" border>
                        <el-table-column label="商品名称" prop="commodity.tradeName" />
                        <el-table-column label="规格" prop="commodity.packageSpecification" />
                        <el-table-column label="生产厂家" prop="commodity.manufactureName" />
                        <el-table-column label="批号" prop="intoNo" />
                        <el-table-column label="可退数量" prop="currentInventory" />
                        <el-table-column label="申退数量" align="center" :show-overflow-tooltip="true" fixed="right">
                            <template #default="scope">
                                <div v-clickOutside="() => handleClickOutside(scope, 'isShowretreatQuantity')">
                                    <p v-if="!scope.row.isShowretreatQuantity"
                                        style="height: 30px;line-height:30px;color: red;font-size: 16px;"
                                        @click="handleInputEdit(scope, 'retreatQuantity')">
                                        {{ Tofixed_(scope.row.retreatQuantity, 0) || '请输入数量' }}</p>
                                    <el-input-number v-model="scope.row.retreatQuantity" placeholder="请输入数量"
                                        v-if="scope.row.isShowretreatQuantity" :min="1" :precision="0"
                                        :max="scope.row.currentInventory" style="width: 100%;height: 30px" clearable />
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" fixed="right" align="center">
                            <template #default="scope">
                                <el-button type="primary" text @click="detailGoods(scope.row)">
                                    查看详情
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-drawer v-model="detailFlag" title="商品详情" direction="rtl" size="30%">
                        <div style="padding: 0 20px">
                            <table border="0" cellpadding="0" cellspacing="1" class="detailTable">
                                <tr>
                                    <td>单据编号</td>
                                    <td>{{ shopDetail.orderCode || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>供应商</td>
                                    <td>{{ shopDetail.suppier || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>商品名称</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.tradeName || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>自编码</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.commoditySelfCode || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>规格</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.packageSpecification || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>生产厂家</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.manufactureName || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>产地</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.producingArea || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>剂型</td>
                                    <td>{{ shopDetail.commodity && shopDetail.commodity.dosageForm || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>基本单位</td>
                                    <td>{{ shopDetail.basicUnit || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>可退数量</td>
                                    <td>{{ shopDetail.currentInventory || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>成本单价</td>
                                    <td>{{ shopDetail.unitPrice || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>批号</td>
                                    <td>{{ shopDetail.intoNo || '--' }}</td>
                                </tr>
                                <tr>
                                    <td>生产日期</td>
                                    <td>{{ shopDetail.produceDate ? moment(shopDetail.produceDate).format('YYYY-MM-DD') :
                                        '--'
                                    }}</td>
                                </tr>
                                <tr>
                                    <td>有效期</td>
                                    <td> {{
                                        shopDetail.produceDate ?
                                        moment(shopDetail.produceDate).add(isNaN(Number(shopDetail.commodity.validityTime))
                                            ?
                                            0 : Number(shopDetail.commodity.validityTime), 'months').format('YYYY-MM-DD') : '--'
                                    }}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </el-drawer>
                    <el-form :model="form" ref="queryRef_setp2" :rules="rules" :inline="true" class="form_130"
                        style="margin-top: 30px;">
                        <div class="box">
                            <el-form-item label="退货原因" prop="returnReason">
                                <el-select v-model="form.returnReason" placeholder="请选择退货原因" class="form_225" clearable
                                    @change="form.otherReasons = undefined">
                                    <el-option :label="item.name" :value="item.value" v-for="item in reasonForReturn"
                                        :key="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="退货原因明细" prop="otherReasons" v-if="form.returnReason == '3'">
                                <el-input v-model="form.otherReasons" clearable placeholder="请输入退货原因明细" class="form_225">
                                </el-input>
                            </el-form-item>
                            <el-form-item label="货物处理" prop="cargoHandling">
                                <el-select v-model="form.cargoHandling" placeholder="请选择货物处理" class="form_225" clearable>
                                    <el-option :label="item.name" :value="item.value" v-for="item in cargoHandling"
                                        :key="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="款项处理" prop="payProcess">
                                <el-select v-model="form.payProcess" placeholder="请选择款项处理" class="form_225" clearable>
                                    <el-option :label="item.name" :value="item.value" v-for="item in paymentProcessing"
                                        :key="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="物流方式" prop="logisticsMethods">
                                <el-select v-model="form.logisticsMethods" placeholder="请选择物流方式" class="form_225" clearable>
                                    <el-option :label="item.name" :value="item.value" v-for="item in logisticsMethods"
                                        :key="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="三方物流" prop="thirdLogistics"
                                v-if="form.logisticsMethods === '2' || form.logisticsMethods === '3'">
                                <el-select v-model="form.thirdLogistics" placeholder="请选择三方物流" class="form_225" clearable>
                                    <el-option :label="item.name" :value="item.value" v-for="item in thirdPartyIogistics"
                                        :key="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="收货人姓名" prop="name">
                                <el-input v-model="form.name" placeholder="请输入收货人姓名" clearable class="form_225"
                                    maxlength="100" show-word-limit />
                            </el-form-item>
                            <el-form-item label="收货人电话" prop="phone">
                                <el-input v-model="form.phone" type="number" placeholder="请输入收货人电话" clearable
                                    class="form_225" clarable />
                            </el-form-item>
                            <el-form-item label="收货地址" prop="deliveryAddress">
                                <el-cascader v-model="form.deliveryAddress" ref="deliveryAddress" :options="areasThird"
                                    clearable filterable placeholder="请输收货地址" class="form_225" @change="addressChange" />
                            </el-form-item>
                        </div>
                        <div style="width: 100%;" class="box_2">
                            <el-form-item label="详细地址" prop="detailAddress" style="width: 100%;">
                                <el-input v-model="form.detailAddress" maxlength="200" placeholder="请输入详细地址" show-word-limit
                                    clearable style="width: 94%;" type="textarea" />
                            </el-form-item>
                            <el-form-item label="备注" prop="remark" style="width: 100%;">
                                <el-input v-model="form.remark" placeholder="请输入备注" clearable type="textarea"
                                    maxlength="100" show-word-limit style="width: 94%;" />
                            </el-form-item>
                        </div>
                        <el-form-item label="上传附件" prop="fileList">
                            <el-upload v-model:file-list="form.fileList" :on-success="(res) => handleUploadSuccess(res)"
                                :action="uploadUrl" :headers='headers' :data="{ zhType: '采购退回', fjType: '附件' }"
                                :on-preview="handlePreview" class="upload-demo" multiple>
                                <el-button type="primary">上传附件</el-button>
                            </el-upload>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="step2" v-show="active == 3">
                    <h3 style="margin: 10px 0">退货明細</h3>
                    <el-table :data="choose_setp1" border style="margin-top: 30px;">
                        <el-table-column label="单据编号" prop="orderCode" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="商品名称" prop="commodity.tradeName" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="自编码" prop="commodity.commodityCode" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="规格" prop="commodity.packageSpecification" :show-overflow-tooltip="true"
                            align="center" min-width="120" />
                        <el-table-column label="生产厂家" prop="commodity.manufactureName" align="center" min-width="120" />
                        <el-table-column label="产地" prop="commodity.producingArea" align="center" min-width="120" />
                        <el-table-column label="剂型" prop="commodity.dosageForm" :show-overflow-tooltip="true" align="center"
                            min-width="120" />
                        <el-table-column label="基本单位" prop="basicUnit" :show-overflow-tooltip="true" align="center"
                            min-width="80" />
                        <el-table-column label="可退数量" prop="currentInventory" :show-overflow-tooltip="true" align="center"
                            min-width="80" />

                        <el-table-column label="成本单价" prop="unitPrice" :show-overflow-tooltip="true" align="center"
                            min-width="80" />
                        <el-table-column label="批号" prop="intoNo" :show-overflow-tooltip="true" align="center"
                            min-width="160" />
                        <el-table-column label="生产日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                            min-width="140"
                            :formatter="row => row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'" />
                        <el-table-column label="有效期" prop="commodity.validityTime" :show-overflow-tooltip="true"
                            align="center" min-width="140">
                            <template #default="scope">
                                {{
                                    scope.row.produceDate ?
                                    moment(scope.row.produceDate).add(isNaN(Number(scope.row.commodity.validityTime)) ?
                                        0 : Number(scope.row.commodity.validityTime), 'months').format('YYYY-MM-DD') : '--'
                                }}
                            </template>

                        </el-table-column>
                        <el-table-column label="申退数量" prop="retreatQuantity" :show-overflow-tooltip="true" align="center"
                            min-width="100" fixed="right">
                            <template #default="scope">
                                <span style="color:red;font-size:16px">{{ scope.row.retreatQuantity }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="申退金额" prop="retreatAmount" :show-overflow-tooltip="true" align="center"
                            min-width="100" fixed="right"><template #default="scope">
                                <span style="color:red;font-size:16px">{{ (scope.row.retreatQuantity *
                                    (isNaN(Number(scope.row.unitPrice || 0)) ? 0 : Number(scope.row.unitPrice ||
                                        0))).toFixed(2)
                                }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <h3 style="margin: 10px 0">退货信息</h3>
                    <table border="0" cellpadding="0" cellspacing="1" class="messTable">
                        <tr>
                            <th>合计申退金额</th>
                            <th>{{ form.cost }}</th>
                        </tr>
                        <tr>
                            <th>合计申退数量</th>
                            <th>{{ form.num }}</th>
                        </tr>
                        <tr>
                            <th>退货原因</th>
                            <th>{{ form.returnReason == '3' ? form.otherReasons : formDict(reasonForReturn,
                                form.returnReason)
                            }}
                            </th>
                        </tr>
                        <tr>
                            <th>货物处理</th>
                            <th>{{ formDict(cargoHandling, form.cargoHandling) }}</th>
                        </tr>
                        <tr>
                            <th>款项处理</th>
                            <th>{{ formDict(paymentProcessing, form.payProcess) }}</th>
                        </tr>
                        <tr>
                            <th>物流方式</th>
                            <th>{{ formDict(logisticsMethods, form.logisticsMethods) }}</th>
                        </tr>
                        <tr>
                            <th>三方物流</th>
                            <th>{{ formDict(thirdPartyIogistics, form.thirdLogistics) }}</th>
                        </tr>
                        <tr>
                            <th>收货地址</th>
                            <th>{{ form.deliveryAddressCn }}</th>
                        </tr>
                        <tr>
                            <th>详细地址</th>
                            <th>{{ form.detailAddress }}</th>
                        </tr>
                        <tr>
                            <th>收货人姓名</th>
                            <th>{{ form.name }}</th>
                        </tr>
                        <tr>
                            <th>收货人电话</th>
                            <th>{{ form.phone }}</th>
                        </tr>
                        <tr>
                            <th style="background-color: #fff;"></th>
                            <th></th>
                        </tr>
                    </table>
                    <div class="remark">
                        <tr style="width: 100%;">
                            <th style="flex: 1;">备注</th>
                            <th style="flex: 12;text-align: left;">{{ form.remark }}</th>
                        </tr>
                    </div>
                    <div class="file">
                        <tr style="width: 100%;">
                            <th style="flex: 1;">附件</th>
                            <th
                                style="flex: 12;text-align: left;display: flex;flex-direction: column;align-items: flex-start;">
                                <p v-for="(item, index) in form.fileList" :key="index" @click="handlePreview(item)">{{
                                    item.name }}
                                </p>
                            </th>
                        </tr>
                    </div>
                    <p style="color: red;margin: 40px 2% 0 4%;text-align: left">
                        请检查以上信息是否填写准确，
                        若信息准确，请点击“确定”发起“采购退出申请，
                        若需修改，请点击“上一步”进行修改。</p>


                </div>
            </div>
            <template #footer>
                <div class="dialog-footer" v-if="!loading">
                    <el-button @click="next">{{ textBtn1 }}</el-button>
                    <el-button type="primary" @click="prev" v-if="modalType != 'detail'">{{ textBtn2 }}</el-button>
                </div>
            </template>
        </el-dialog>
        <viewImg v-if="uploadVisibleFile" :visible="uploadVisibleFile" :src="uploadViewImgUrlFile"
            :beforeClose="() => uploadVisibleFile = false" />
    </div>
</template>

<script setup>
import {defineExpose, defineProps, getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue'
import {ElMessageBox} from "element-plus";
import moment from 'moment'
import Table from "@/components/assist/massRange/Table.vue";
import inboundRecordService from '@/api/erp/procure/erpPurchaseWarehousingRecordService'
import warehouseNumberManagement from '@/api/erp/warehouseNumberManagement/warehouseNumberManagement'
import purchasingManagement from '@/api/erp/purchasingManagement'
import purchaseWithdrawalReview from '@/api/erp/purchaseWithdrawalReview'
import tool from '@/utils/tool';


const Tofixed_ = (value, num) => {
    if (value) {
        return parseFloat(value)?.toFixed(num)
    } else {
        return false
    }
}
const uploadUrl = process.env.VUE_APP_API_UPLOAD
const headers = {
    Authorization: 'Bearer ' + tool.cookie.get("TOKEN"),
    ContentType: 'multipart/form-data',
    clientType:'pc',
}
const { proxy } = getCurrentInstance();
const loading = ref(false);
const active = ref(1)
const textBtn1 = ref('取消')
const textBtn2 = ref('下一步')
const step1_list = ref([])
const step1_list_Total = ref(0)
const form = ref({
    fileList: []
})
const choose_setp1 = ref([])
const cargoHandling = ref([])
const paymentProcessing = ref([])
const logisticsMethods = ref([])
const thirdPartyIogistics = ref([])
const reasonForReturn = ref([])
const areasThird = ref([])
const deliveryAddress = ref(null)
const optionList = ref([])
const supplierList = ref([])
const uploadVisibleFile = ref(false)
const uploadViewImgUrlFile = ref('')
const reviewStatus = ref([])
const outboundList = ref([])
const roleList = ref({})
const entrust_out_table_listRef = ref(null)
const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
        createDate: [moment().subtract(30, "days"), moment()],
        creatorId: tool.data?.get("ROLE_LIST")?.length && tool.data?.get("ROLE_LIST")?.[0]?.enName == 'ywjl' ? tool.data?.get("USER_INFO")?.id : undefined
    },
    rules: {
        returnReason: [{ required: true, message: "退货原因不能为空", trigger: "change" }],
        cargoHandling: [{ required: true, message: "货物处理不能为空", trigger: "change" }],
        payProcess: [{ required: true, message: "款项处理不能为空", trigger: "change" }],
        logisticsMethods: [{ required: true, message: "物流方式不能为空", trigger: "change" }],
        thirdLogistics: [{ required: true, message: "三方物流不能为空", trigger: "change" }],
        name: [{ required: true, message: "收货人姓名不能为空", trigger: "blur" }],
        phone: [{ required: true, message: "收货人电话不能为空", trigger: "blur" }],
        deliveryAddress: [{ required: true, message: "收货地址不能为空", trigger: "blur" }],
        detailAddress: [{ required: true, message: "详细地址不能为空", trigger: "blur" },{ max: 200, message: '最多输入200个字符', trigger: 'blur' }],
        fileList: [{ required: true, message: "附件不能为空", trigger: "change" }],
    },
    queryParamsRules: {
        supplierId: [{ required: true, message: "供应商不能为空", trigger: "change" }],
        creatorId: [{ required: true, message: "经手人不能为空", trigger: "change" }]
    }
});

const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    beforClose: {
        type: Function,
        default: () => { }
    },
    title: {
        type: String,
        default: ''
    },
    modalType: {
        type: String,
        default: ""
    },
    getList: {
        type: Function,
        default: () => { }
    },
    detailData: {
        type: Object,
        default: () => { { } }
    }

})
const detailFlag = ref(false)
const shopDetail = ref({})
const { open, beforClose, title, modalType, getList, detailData } = toRefs(props)
const { queryParams, rules, queryParamsRules } = toRefs(data);
const handleUploadSuccess = (res) => {
    form.value.fileList = form.value.fileList.filter(v => !v.response)
    form.value.fileList.push(res.data)
}
const handlePreview = (res) => {
    if ((res.url || res.fileUrl).includes('.xlsx') || (res.url || res.fileUrl).includes('.xls')) {
        const a = document.createElement('a');
        a.style.display = 'none';
        a.download =res.name
        a.href = (res.url || res.fileUrl);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    } else {
        uploadVisibleFile.value = true
        uploadViewImgUrlFile.value = res.url || res.fileUrl
    }

}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}
const handleInputEdit = (scope, type) => {
    if (modalType.value == 'detail') return
    scope.row[`isShow${type}`] = true
}
const detailGoods = (row) => {
    detailFlag.value = true
    shopDetail.value = row
}
const handleClickOutside = (scope, key) => {
    scope.row[key] = false
}
// 获取采购入库记录
const step1_getList = async () => {
    proxy.$refs["queryParamsRef"].validate(async valid => {
        if (valid) {
            loading.value = true
            const params = { ...queryParams.value }
            params.beginInTime = params.createDate?.length ? moment(params.createDate[0]).format('YYYY-MM-DD') : undefined
            params.endInTime = params.createDate?.length ? moment(params.createDate[1]).format('YYYY-MM-DD') : undefined
            delete params.createDate
            try {
                const res = await inboundRecordService.list(params)
                if (res.code == 200) {
                    step1_list.value = res.data?.records
                    step1_list_Total.value = res.data.total
                    loading.value = false
                } else {
                    loading.value = false
                }
            } catch {
                loading.value = false
            }
        }
    })

}
const handleSelectionChange_step1_list = (key) => {
    choose_setp1.value = key
    console.log(choose_setp1.value);
}
const regxNum = () => {
    let index = 0
    for (let i = 0; i < choose_setp1.value.length; i++) {
        if (!choose_setp1.value[i]?.retreatQuantity) {
            proxy.msgError(`请输入商品名称为${choose_setp1.value[i]?.commodity?.tradeName}的申退数量！`)
            break
        } else {
            index++
        }
    }
    return index
}
const addressChange = () => {
    form.value.deliveryAddressCn = deliveryAddress.value.getCheckedNodes()[0].text
}
const calculateTotal = () => {
    choose_setp1.value.forEach(item => {
        form.value.num = (isNaN(Number(form.value.num)) ? 0 : Number(form.value.num)) + Number(item.retreatQuantity)
        form.value.cost = (Number((isNaN(Number(form.value.cost)) ? 0 : Number(form.value.cost))) + Number(((Number(item.retreatQuantity) * (isNaN(Number(item.unitPrice)) ? 0 : Number(item.unitPrice)))))).toFixed(2)
    })

}
const getDetail = () => {
    purchaseWithdrawalReview.getIdOrder({ id: detailData.value.id }).then(res => {
        if (res.code == 200) {
            loading.value = false
            step1_list.value = res.data?.purchaseRetreatForms
            form.value = res.data?.purchaseRetreatReason
            form.value = { ...form.value, retreatAmount: res.data?.purchaseRetreat?.retreatAmount, retreatQuantity: res.data?.purchaseRetreat?.retreatQuantity, fileList: res.data?.fileDtos }
        } else {
            proxy.msgError(res.msg)
        }
    })
}
const getOut = () => {
    purchaseWithdrawalReview.getOut({ 'purchaseRetreat.id': detailData.value.id }).then(res => {
        if (res.code == 200) {
            outboundList.value = res.data?.record
        }
    })
}
const next = () => {
    if (active.value == 1) {
        close()
        return
    }
    if (active.value == 2) {
        active.value = 1
        textBtn1.value = '取消'
        textBtn2.value = '下一步'
        entrust_out_table_listRef.value?.clearSelection()
        return
    }
    if (active.value == 3) {
        active.value = 2
        textBtn1.value = '上一步'
        textBtn2.value = '下一步'
        return
    }
}
const prev = () => {
    if (active.value == 1) {
        if (!choose_setp1.value.length) {
            proxy.msgError('请至少选择一条商品')
            return
        } else {
            active.value = 2
            textBtn1.value = '上一步'
            textBtn2.value = '下一步'
            return
        }
    }
    if (active.value == 2) {
        proxy.$refs["queryRef_setp2"].validate(valid => {
            if (valid) {
                if (regxNum() !== choose_setp1.value?.length) return
                calculateTotal()
                active.value = 3
                textBtn1.value = '上一步'
                textBtn2.value = '提交申请'
                return
            }
        })

    }
    if (active.value == 3) {
        loading.value = true
        textBtn1.value = '上一步'
        const purchaseRetreat = {
            supplier: {},
            handledBy: {}
        }
        const purchaseRetreatForms = JSON.parse(JSON.stringify(choose_setp1.value))
        const purchaseRetreatReason = JSON.parse(JSON.stringify(form.value))
        const fileDtos = []
        purchaseRetreatReason.fileList.forEach(item => {
            fileDtos.push({
                fileName: item.name,
                fileUrl: item.url
            })
        })
        purchaseRetreatReason.deliveryAddress = purchaseRetreatReason.deliveryAddress.toString()
        purchaseRetreatForms.forEach((item, index) => {
            item.supplier = {}
            item.purchaseWarehousingRecord = {}
            item.supplier.id = item.supplierId
            item.purchaseWarehousingRecord.id = item.id
            item.orderCode = item.orderCode
            item.commodityName = item.commodity.tradeName
            item.commoditySelfCode = item.commodity.commodityCode
            item.commoditySpecification = item.commodity.packageSpecification
            item.manufactureName = item.commodity.manufactureName
            item.commodityProducingArea = item.commodity.producingArea
            item.commodityDosageForm = item.commodity.dosageForm
            item.commodityBasicUnit = item.basicUnit
            item.unitPrice = item.unitPrice
            item.batchNumber = item.intoNo
            item.produceDate = item.produceDate
            item.commodityValidityTime = item.commodity.validityTime
            item.retreatQuantity = item.retreatQuantity
            delete item.id
        })
        delete purchaseRetreatReason.fileList
        purchaseRetreat.id = queryParams.value?.id
        purchaseRetreat.supplier.id = queryParams.value?.supplierId
        purchaseRetreat.handledBy.id = queryParams.value?.creatorId
        purchaseRetreat.retreatQuantity = form.value?.num
        purchaseRetreat.retreatAmount = form.value?.cost
        const params = {
            purchaseRetreat,
            purchaseRetreatForms,
            purchaseRetreatReason,
            fileDtos,
            formType: 'submit'
        }
        purchaseWithdrawalReview.save(params).then(res => {
            if (res.code == 200) {
                proxy.msgSuccess('操作成功')
                beforClose.value()
                form.value = {
                    fileList: []
                }
                queryParams.value = {
                    current: 1,
                    size: 10,
                    createDate: [moment().subtract(30, "days"), moment()]
                }
                choose_setp1.value = []
                step1_list.value = []
                step1_list_Total.value = 0
                loading.value = false
                active.value = 1
                getList.value()
            } else {
                loading.value = false
                proxy.msgError(res.msg)
            }
        }).catch(() => {
            loading.value = false
        }).finally(() => {
            loading.value = false
        })
        return
    }
}
const close = () => {
    if (modalType.value == 'detail') {
        beforClose.value()
    } else {
        ElMessageBox.confirm("页面未保存确定取消编辑吗？", '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
            .then(() => {
                beforClose.value()
                form.value = {
                    fileList: []
                }
                queryParams.value = {
                    current: 1,
                    size: 10,
                    createDate: [moment().subtract(30, "days"), moment()]
                }
                choose_setp1.value = []
                step1_list.value = []
                step1_list_Total.value = 0
                active.value = 1
            })
            .catch(() => {

            });
    }


}
const getUsers = () => {
    warehouseNumberManagement.getUser({ size: 9999, current: 1 }).then(res => {
        if (res.code == 200) {
            optionList.value = res.data?.records
        }

    })
}
/**
 * @description: 获取供应商
 * @return {*}
 */
const getSupplier = () => {
    purchasingManagement.getSupplierProduction({ status: 3, customLabel: 2, size: 9999, current: 1 }).then(res => {
        if (res.code == 200) {
            supplierList.value = res.data?.records
        }
    })
}

async function dict() {
    cargoHandling.value = await proxy.getDictList('cargo_handling')
    paymentProcessing.value = await proxy.getDictList('payment_processing')
    logisticsMethods.value = await proxy.getDictList('logistics_methods')
    thirdPartyIogistics.value = await proxy.getDictList('third_party_iogistics')
    reasonForReturn.value = await proxy.getDictList('reason_for_return')
    reviewStatus.value = await proxy.getDictList('erp_review_status')
}
onMounted(async () => {
    loading.value = true
    await dict()
    getUsers()
    await getSupplier()
    areasThird.value = proxy.getSysAreasThird
    if (modalType.value == 'detail') {
        getDetail()
        getOut()
    }
    loading.value = false
    roleList.value = tool.data.get("ROLE_LIST")
})

defineExpose({

})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.box_2 {
    width: 100%;
    display: grid;
    // grid-template-rows: 50% 50%;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    // grid-column-gap: 8px;
    // grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}

.col_title {
    color: #333;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    padding-left: 8px;

    &::after {
        content: "";
        display: inline-block;
        width: 3px;
        height: 20px;
        background-color: #2878ff;
        border-radius: 2px;
        position: absolute;
        top: 15px;
        left: 0;
    }
}

.rowStyle {
    .el-col {
        margin-top: 20px;
        font-size: 15px;

        .rowTitle {
            width: 120px;
            text-align: right;
            display: inline-block;
            font-size: 15px;
            font-weight: bolder;
            color: #000;
        }

        .rowMess {
            color: #4d4d4d;
            font-weight: 600;
        }

        .rowRed {
            color: red;
        }
    }
}

.total {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    margin: 0px 20px;

    p {
        margin-right: 50px;
        margin-top: 20px;

        & span:nth-of-type(1) {
            font-size: 15px;
            font-weight: bold;
            color: #333;
            display: inline-block;
            width: 130px;
            text-align: right;
        }

        .red {
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
    }
}

.box_date {
    width: 220px;
}

.step {
    margin-bottom: 30px;
}

.step1_search_btn {
    width: 60px;
    margin-left: 50px;
}

h3 {
    color: black;
}

.detailTable {
    width: 100%;
    background-color: #eaedf3;
    font-size: 14px;
    border-radius: 5px;

    tr {
        height: 40px;

        td {
            background-color: white;
        }

        td:nth-child(1) {
            padding: 0 10px;
            font-weight: bold;
            width: 20%;
            color: #505050;
            background: #f7f7f7;
        }

        td:nth-child(2) {
            width: 80%;
            color: #606266;
            padding: 0 10px;
        }
    }

}

.messTable {
    width: 100%;
    background-color: #eaedf3;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    padding: 1px 1px 0 1px;

    tr {
        margin-bottom: 1px;
        display: flex;

        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2
        }
    }
}

.remark,
.file {
    tr {
        margin-bottom: 1px;
        display: flex;

        // border: 1px solid #eaedf3;
        th {
            background-color: white;
            line-height: 40px;
        }

        th:nth-child(1) {
            flex: 1;
            padding: 0 10px;
            font-weight: bold;
            color: #505050;
            background: #f7f7f7;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        th:nth-child(2) {
            color: #606266;
            padding: 0 10px;
            flex: 2;
            display: flex;
            align-items: center;
        }
    }
}

.remark,
.file {
    border-bottom: 1px solid #eaedf3;
    border-left: 1px solid #eaedf3;
    border-right: 1px solid #eaedf3;
}

.file {
    th:nth-child(2) {
        line-height: 25px;
        color: #2878ff !important;
        cursor: pointer;
        font-weight: 400;
    }
}

.titleH4 {
    margin-bottom: 20px;
    color: #000;
    font-weight: bolder;
    font-size: 15px;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type="number"] {
    -moz-appearance: textfield;
    /* 此处写不写都可以 */
}
</style>
