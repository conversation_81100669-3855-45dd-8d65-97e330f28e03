<template>
    <div class="app-container">
        <el-card v-show="showSearch" class="box-card mb10 Botm" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryParams" class="seache-form" label-width="70px" @submit.native.prevent>
                <el-form-item label="订单号" prop="orderNo">
                    <el-input v-model="queryParams.orderNo" clearable placeholder="请输入订单号" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单号" prop="transOrderNo">
                    <el-input v-model="queryParams.transOrderNo" clearable placeholder="请输入运单号" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="发件公司" prop="sendCompany">
                    <el-input v-model="queryParams.sendCompany" clearable placeholder="请输入发件公司" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item label="收件公司" prop="receiverCompany">
                    <el-input v-model="queryParams.receiverCompany" clearable placeholder="请输入收件公司" @keyup.enter.native="handleQuery"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="请选择订单状态">
                        <el-option v-for="dict in statusDicts" :key="dict.value" :label="dict.name" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item class="date-screening" label="下单时间" prop="queryTime">
                    <el-date-picker v-model="queryParams.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" value-format="YYYY-MM-DD" @change="handleQuery"> </el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="getList" @resetQuery="resetQuery('queryParams')" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!-- 列表 -->
        <el-card shadow="never">
            <div class="mb10">
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" class="mb10" table-i-d="collectOrderList" @queryTable="getList"></right-toolbar>
            </div>
            <column-table key="collectOrderList" :columns="columns" :data="dataList" class="column-table" :max-height="600">
                <template #productClass="{ row }">
                    <span>{{ dictionaryFormatting(fourplProductClassDicts, row.productClass) }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ dictionaryFormatting(productTypeDicts, row.productType) }}</span>
                </template>
                <template #paymentMethod="{ row }">
                    <span>{{ dictionaryFormatting(fourplPaymentMethodOptions, row.paymentMethod) }}</span>
                </template>
                <template #temperatureType="{ row }">
                    <span>{{ row.temperatureType.describtion || '' }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="getOrderInfo(row)">订单详情</el-button>
                    <el-button icon="el-icon-plus" link size="small" type="success" @click="quicklyPlaceOrder(row.id)">快速下单</el-button>
                    <el-button icon="el-icon-delete" link size="small" type="warning" @click="collectOrder(row.id)">取消收藏</el-button>
                </template>
            </column-table>

            <pagination v-show="total > 0" v-model:limit="queryParams.size" v-model:page="queryParams.current" :total="total" class="mb0" @pagination="getList" />
        </el-card>
        <!--订单详情-->
        <el-drawer v-model="infoOpen" :title="title" direction="rtl" size="50%" @close="infoOpen = false">
            <div style="background-color: #f5f7fd; padding: 10px">
                <order-detail-trans v-if="infoOpen" :order-info="orderInfo" source="2"></order-detail-trans>
            </div>
        </el-drawer>
        <!--快速下单-->
        <el-dialog v-model="quicklyPlaceOrderShow" close-on-click-modal width="600px">
            <div style="text-align: center">
                <h4 class="mb-40 font-bold font-color-main">该收藏订单为模板快速生成订单，请输入快速下单订单数量，最大支持5条！</h4>
                <el-input-number v-model="quicklyPlaceOrderNum" :max="5" :min="1" style="width: 100%"></el-input-number>
            </div>
            <div style="display: flex; justify-content: center; margin-top: 35px">
                <el-button @click="quicklyPlaceOrderShow = false">取消</el-button>
                <el-button :disabled="quicklyPlaceOrderConfirmDisabled" type="primary" @click="quicklyPlaceOrderConfirm">确定 </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import SearchButton from '@/components/searchModule/SearchButton.vue';
import RightToolbar from '@/components/RightToolbar/index.vue';
import ColumnTable from '@/components/ColumnTable';
import OrderDetailTrans from '@/views/orderComponents/OrderDetailTrans';
import collect from '@/api/collectOrder/collect.js';
import moment from 'moment';
export default {
    name: '',
    components: {
        SearchButton,
        RightToolbar,
        OrderDetailTrans,
        ColumnTable
    },
    data() {
        return {
            isShowAll: false,
            infoOpen: false,
            queryParams: {
                current: 1,
                size: 10,
                collectFlag: 1,
                queryType: '0' // 货主查询
            },
            dataList: [
                {
                    reason: 56454165
                }
            ],
            showSearch: true,
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '160px', columnShow: true, fixed: 'left' },
                { title: '发件人', key: 'sendUser', minWidth: '120px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件公司', key: 'sendCompany', minWidth: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '发件人电话', key: 'sendUserPhone', width: '120px', align: 'center', columnShow: true },
                { title: '发件地址', key: 'sendComplateAddress', width: '280px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件人', key: 'receiverUser', minWidth: '120px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', minWidth: '200px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '收件人电话', key: 'receiverUserPhone', minWidth: '120px', align: 'center', columnShow: true },
                { title: '收件地址', key: 'receiverComplateAddress', minWidth: '160px', align: 'center', columnShow: true, showOverflowTooltip: true },
                { title: '产品分类', key: 'productClass', minWidth: '120px', align: 'center', columnShow: true },
                { title: '件数', key: 'goodsPackages', minWidth: '100px', align: 'center', columnShow: true },
                { title: '运输类型', key: 'productType', minWidth: '120px', align: 'center', columnShow: true },
                { title: '温层类型', key: 'temperatureType', minWidth: '140px', align: 'center', columnShow: true },
                { title: '付款方式', key: 'paymentMethod', minWidth: '120px', align: 'center', columnShow: true },
                { title: '操作', key: 'opt', align: 'center', width: '320px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            statusDicts: [], // 订单状态
            fourplProductClassDicts: [], // 产品分类
            productTypeDicts: [], // 运输类型4PL
            fourplPaymentMethodOptions: [], // 付款方式
            shortcuts: [
                {
                    text: '无',
                    value: (e) => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: (e) => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ], // 时间
            quicklyPlaceOrderShow: false, //  快速下单弹窗
            quicklyPlaceOrderNum: 1, // 快速下单数量
            quicklyPlaceOrderConfirmDisabled: false, //  快速下单确认按钮是否禁用
            orderId: null
        };
    },
    computed: {
        // 字典翻译
        dictionaryFormatting() {
            return (data, value) => {
                return this.selectDictLabel(data, value);
            };
        }
    },
    created() {
        this.getDict();
        // 默认设置当天
        let now = moment(new Date()).format('YYYY-MM-DD');
        this.queryParams.queryTime = [now, now];
        this.handleQuery();
    },
    mounted() {},
    methods: {
        /**
         * 获取增值服务
         */
        async getDict() {
            // 订单状态字典
            this.statusDicts = await this.getDictList('fourpl_order_status');
            /** 产品分类 */
            this.fourplProductClassDicts = await this.getDictList('fourpl_product_class');
            // 运输类型4PL
            this.productTypeDicts = await this.getDictList('fourpl_product_type');
            /** 付款方式 */
            this.fourplPaymentMethodOptions = await this.getDictList('fourpl_payment_method');
        },
        /**
         * 快速下单确认
         */
        quicklyPlaceOrderConfirm() {
            this.quicklyPlaceOrderConfirmDisabled = true;
            collect
                .fastCreateOrder({
                    id: this.orderId, //订单ID
                    goodsPackages: this.quicklyPlaceOrderNum // 单数
                })
                .then((response) => {
                    if (response.code == 200) {
                        this.quicklyPlaceOrderShow = false;
                        this.quicklyPlaceOrderConfirmDisabled = false;
                        this.msgSuccess('快速下单成功');
                    } else {
                        this.msgError('快速下单失败');
                    }
                });
        },
        /**
         * 快速下单
         * @param id
         */
        quicklyPlaceOrder(id) {
            this.orderId = id;
            this.quicklyPlaceOrderShow = true;
        },
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        getList() {
            this.loading = true;
            collect.getOrderList(this.queryParams).then((res) => {
                if (res.code == 200) {
                    this.loading = false;
                    this.dataList = res.data.records || [];
                    this.total = res.data.total || 0;
                }
            });
        },
        collectOrder(id) {
            this.$confirm('取消收藏该订单，是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    collect.signDetali({ id, collectFlag: '0' }).then((response) => {
                        if (response.code == 200) {
                            this.$message.success('取消收藏成功');
                            this.handleQuery();
                        }
                    });
                })
                .catch(() => {
                    this.$message.error('撤销取消收藏');
                });
        },
        /**
         * 筛选
         */
        handleQuery() {
            if (this.queryParams.queryTime != undefined && this.queryParams.queryTime.length != 0 && this.queryParams.queryTime[0] != 'Invalid Date') {
                this.queryParams.beginCreateDate = this.queryParams.queryTime[0] + ' 00:00:00';
                this.queryParams.endCreateDate = this.queryParams.queryTime[1] + ' 23:59:59';
            } else {
                this.queryParams.beginCreateDate = null;
                this.queryParams.endCreateDate = null;
            }
            this.getList();
        },
        // 重置
        resetQuery(formName) {
            this.queryParams = {
                current: 1,
                size: 10,
                collectFlag: 1,
                queryType: '0' // 货主查询
            };
            let now = moment(new Date()).format('YYYY-MM-DD');
            this.queryParams.queryTime = [now, now];
            this.handleQuery();
        },
        // 查看订单详情
        getOrderInfo(row) {
            this.infoOpen = true;
            this.orderInfo = { ...row, orderId: row.orderid };
            this.title = '订单详情';
        }
    }
};
</script>
<style lang="scss" scoped>
@media screen and (max-width: 1366px) {
    .seache-form .el-form-item:not(.last-form-item) {
        width: 260px;
    }
}
</style>
