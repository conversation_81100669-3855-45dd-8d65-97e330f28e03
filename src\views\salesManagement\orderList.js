export const columnsList = (businessType, overType, statusType) => {
	return [
		{type: "selection", fixed: 'left', minWidth: '60'},
		{label: '序号', type: 'sort', minWidth: '70', fixed: 'left'},
		{label: '单据编号', prop: 'docNum', minWidth: '180'},
		{label: '单据日期', prop: 'docDate', type: 'date'},
		{label: '业务类型', prop: 'businessType', type: 'status', searchKey: 'n14', filters: businessType},
		{label: '客户名称', prop: 'customer.enterpriseName'},
		{label: '备注', prop: 'remark'},
		{label: '制单人', prop: 'preparedBy.name'},
		{label: '审核人', prop: 'auditorBy.name'},
		{label: '审核日期', prop: 'auditorDate', type: "date"},
		{
			label: '订单状态',
			prop: 'status',
			type: 'status',
			filters: JSON.parse(localStorage.getItem('salesType')) || statusType
		}, //
		{
			label: '出库状态', prop: 'inventoryStatus', searchKey: 'n7', type: 'status', filters: [
				{
					name: "待出库",
					value: "0"
				},
				{
					name: "出库中",
					value: "1"
				},
				{
					name: "出库完成",
					value: "2"
				},
			]
		}, //
		{label: '单据金额', prop: 'docAmount'},
		{label: '单据数量', prop: 'docQuantity'},
		{label: '出库数量', prop: 'outQuantity'},
		{label: '收款期限', prop: 'collectionPeriod', type: "date"},
		{label: '结算方式', prop: 'settlementMethod', type: 'status', searchKey: 'n5', filters: overType},//
		{label: '库号', prop: 'warehouseNumber.warehouseNumber'},
		{label: '经手人', prop: 'handledBy.name'},
		{label: '操作', prop: 'operate', minWidth: "300px", type: 'operate', fixed: 'right'},
	]
}

export const dataList = () => {
	return {
		editStrs: {
			orderHeader: {
				status: 0,
			},
		},
		pageNum: 1,
		pageSize: 10,
		total: 0,
		clientType: {
			total: 0,
			pageSize: 5,
			pageNum: 1,
		},
		serial: {
			total: 0,
			pageSize: 5,
			pageNum: 1,
		},
		myFlag: false,
	}
}

export const addGoodsList = () => {
	return {
		value: "",
		total: 0,
		pageSize: 10,
		pageNum: 1,
		addList: null,
		tableData: [],
		shops: "",
		allGoods: [],
		delList: [],
	}
}

export const creatRules = (num) => {
	if (num == 1) {
		return {
			n1: [{required: true, message: "请输入客户", trigger: "blur"}],
			n2: [{required: true, message: "请输入客户代表", trigger: "blur"}],
			n3: [{required: true, message: "请输入结算方式", trigger: "blur"}],
			n4: [{required: true, message: "请输入收款期限", trigger: "blur"}],
			n5: [{required: true, message: "请输入发票类型", trigger: "blur"}],
			n6: [{required: true, message: "请输入业务类型", trigger: "blur"}],
			n7: [{required: true, message: "请输入物流方式", trigger: "blur"}],
			n8: [{required: true, message: "请输入三方物流", trigger: "blur"}],
			n9: [{required: true, message: "请输入收货地址", trigger: "blur"}],
			n10: [{required: true, message: "请输入详细地址", trigger: "blur"}],
			n11: [{required: true, message: "请输入收货人", trigger: "blur"}],
			n12: [{required: true, message: "请输入收货人电话", trigger: "blur"}],
			n13: [{required: true, message: "请输入库号", trigger: "blur"}],
			n14: [{required: true, message: "请输入经手人", trigger: "blur"}],
			n15: [{required: true, message: "请输入自营扣率", trigger: "blur"}],
			n16: [{required: true, message: "请输入制单人", trigger: "blur"}],
			n17: [{required: true, message: "请输入备注", trigger: "blur"}],
			n19: [{required: true, message: "请输入折扣金额", trigger: "blur"}],
			n21: [{required: true, message: "请输入合同模板", trigger: "blur"}],
		}
	} else if (num == 2) {
		return {
			n1: [{required: true, message: "请选择三方章选项", trigger: "blur"}],
			n2: [{required: true, message: "请选择货主章选项", trigger: "blur"}],
			n3: [{required: true, message: "请选择装货选项", trigger: "blur"}],
			n4: [{required: true, message: "请选择是否拼箱", trigger: "blur"}],
			n5: [{required: true, message: "请选择单据返回选项", trigger: "blur"}],
			n6: [{required: true, message: "请选择留存资料选项", trigger: "blur"}],
			n7: [{required: true, message: "请选择质检报告", trigger: "blur"}],
			n8: [{required: true, message: "请选择质量单据返回", trigger: "blur"}],
			n9: [{required: true, message: "请选择收集随货资料", trigger: "blur"}],
		}
	}
}

export const allFormInline = (num) => {
	if (num == 1) {
		return {
			n1: "",
			n2: "",
			n3: "",
			n4: "",
			n5: "",
			n6: "",
			n7: "",
			n8: "",
			n9: "",
			n10: "",
			n11: "",
			n12: "",
			n13: "",
			n14: "",
			n15: "",
			n16: "",
			n17: "111111",
			n18: "",
			n19: 0,
			n20: false,
			n21: "",
			n22: false,
			n23: "",
			n24: "",
			n25: "",
		}
	} else if (num == 2) {
		return {
			n1: "",
			n2: "",
			n3: "",
			n4: "",
			n5: [],
			n6: "",
			n7: [],
			n8: [],
			n9: [],
			n10: "",
		}
	}
}
