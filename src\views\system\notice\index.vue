<template>
    <div class="app-container">
        <el-card class="box-card" style="min-height: calc(100vh - 160px); margin:10px">
            <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">
                <el-form-item label="公告标题" prop="noticeTitle">
                    <el-input v-model="queryParams.noticeTitle" placeholder="请输入公告标题" clearable style="width: 240px" />
                </el-form-item>
                <el-form-item label="公告内容" prop="noticeContent">
                    <el-input v-model="queryParams.noticeContent" placeholder="请输入公告内容" clearable style="width: 240px" />
                </el-form-item>
                <el-form-item label="公告类型" prop="noticeType">
                    <el-select v-model="queryParams.noticeType" placeholder="请选择公告类型">
                        <el-option :label="item.name" :value="item.value" v-for="item in typeList" :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公告状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="请选择公告状态">
                        <el-option :label="item.name" :value="item.value" v-for="item in statusList" :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" icon="el-icon-plus" @click="() => handleAdd()">新增</el-button>
                </el-col>
            </el-row>

            <!-- 表格数据 -->
            <el-table v-loading="loading" :data="list" border style="margin-top: 30px;">
                <el-table-column label="序号" prop="sort" width="80" align="center">
                    <template #default="scope">
                        <span>{{ (queryParams.current - 1) * queryParams.size + scope.$index + 1 }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="公告标题" prop="noticeTitle" align="center" :show-overflow-tooltip="true"/>
                <el-table-column label="公告类型" align="center">
                    <template #default="scope">
                        <span>{{ formDict(typeList, scope.row.noticeType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="公告内容" prop="noticeContent" :show-overflow-tooltip="true" align="center" />
                <el-table-column label="公告状态" align="center">
                    <template #default="scope">
                        <span>{{ formDict(statusList, scope.row.status) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300px">
                    <template #default="scope">
                        <el-button link type="primary" icon="el-icon-edit" @click="handleAdd(scope.row)"
                            :disabled="scope.row.status == '2'">修改</el-button>
                        <el-button link type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
                        <el-button link type="success" icon="el-icon-folder-checked" @click="release(scope.row)"
                            :disabled="scope.row.status == '2'">发布</el-button>
                        <el-button link type="warning" icon="el-icon-folder-delete" @click="release(scope.row)"
                            :disabled="scope.row.status == '3' || scope.row.status == '1'">撤销</el-button>
                    </template>
                </el-table-column>

            </el-table>
            <div style="float: right;">
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                    v-model:limit="queryParams.size" @pagination="getList" />
            </div>
        </el-card>




        <!-- 添加或修改角色配置对话框 -->
        <el-dialog :title="title" v-model="open" width="600px" append-to-body>
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px"
                style="margin-top: 0px;padding-right: 20px;">
                <el-form-item label="公告标题" prop="noticeTitle">
                    <el-input v-model="form.noticeTitle" placeholder="请输入公告标题" />
                </el-form-item>
                <el-form-item label="公告类型" prop="noticeType">
                    <el-select v-model="form.noticeType" placeholder="请选择公告类型" style="width:100%">
                        <el-option :label="item.name" :value="item.value" v-for="item in typeList" :key="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公告内容" prop="noticeContent">
                    <el-input v-model="form.noticeContent" placeholder="请输入公告内容" type="textarea" />
                </el-form-item>
                <el-form-item label="公告状态" prop="status">
                    <el-select v-model="form.status" placeholder="请选择公告状态" style="width:100%">
                        <el-option :label="item.name" :value="item.value" v-for="item in statusList" :key="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="() => open = false">取 消</el-button>
					<el-button type="primary" @click="submitForm">确 定</el-button>
				</div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup >
import { reactive, ref, getCurrentInstance, toRefs } from 'vue'
const { proxy } = getCurrentInstance();
import sysNoticeService from '@/api/sys/sysNoticeService'
//  const { sys_normal_disable } = proxy.useDict("sys_normal_disable");
const list = ref([]);
const open = ref(false);
const loading = ref(false);
const total = ref(0);
const title = ref("");
const statusList = ref([])
const typeList = ref([])
const data = reactive({
    form: {},
    queryParams: {
        current: 1,
        size: 10,
    },
    rules: {
        noticeTitle: [{ required: true, message: "公告标题不能为空", trigger: "blur" }],
        noticeType: [{ required: true, message: "公告内容不能为空", trigger: "change" }],
        noticeContent: [{ required: true, message: "公告类型不能为空", trigger: "blur" }],
        status: [{ required: true, message: "公告状态不能为空", trigger: "change" }],
    },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询角色列表 */
function getList() {
    loading.value = true
    sysNoticeService.list(queryParams.value).then(res => {
        if (res.code == 200) {
            list.value = res.data.records
            total.value = res.data.total
            loading.value = false
        }
    })
}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}
/** 搜索按钮操作 */
function handleQuery() {
    getList();
}
const handleAdd = (row) => {
    row ? (title.value = '修改') : (title.value = '新增')
    if (row) {
        const { noticeTitle, noticeType, noticeContent, status, id } = row
        form.value = {
            noticeTitle,
            noticeType,
            noticeContent,
            status,
            id
        }
    } else {
        form.value = {}
    }
    open.value = true
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
function release(row) {
    sysNoticeService.release((row.status == '1' || row.status == '3') ? { id: row.id, status: '2' } :  { id: row.id, status: '3' }).then(res => {
        if (res.code == 200) {
            proxy.msgSuccess(`${row.status == '1' ? '发布' : '撤销'}成功`)
            getList()
        }
    })
}
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            sysNoticeService.save(form.value).then(res => {
                if (res.code == 200) {
                    proxy.msgSuccess(`${title.value}成功`)
                    open.value = false
                    getList()
                }
            })
        }
    })
}
/** 删除按钮操作 */
function handleDelete(row) {
    proxy.$confirm('是否确认删除改数据项?', '提示', {
        type: 'warning'
    }).then(() => {
        sysNoticeService.delete({ ids: row.id }).then(res => {
            if (res.code == 200) {
                getList();
                proxy.msgSuccess("删除成功");
            }
        })  // 删除数据的接口
    }).catch(() => { });
}

async function dict() {
    statusList.value = await proxy.getDictList('sys_notice_status')
    typeList.value = await proxy.getDictList('sys_notice_type')
}
dict()
getList();
</script>
