<template>
    <div v-loading="fullLoading" class="app-container" element-loading-text="导出中...">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" :label-width="isShowAll ? 'auto' : ''" @submit.native.prevent>
                <el-form-item label="下单时段" prop="queryTime" style="width: 310px">
                    <el-date-picker v-model="queryForm.queryTime" :shortcuts="shortcuts" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="daterange" unlink-panels value-format="YYYY-MM-DD" @change="handleChangesTheOrderType"></el-date-picker>
                </el-form-item>
                <el-form-item label="费用状态" prop="status" style="width: 245px">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择费用状态" @change="handleChangesTheOrderType">
                        <el-option v-for="item in orderFeeStatusList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="订单类型" prop="type">
                    <el-select v-model="queryForm.type" clearable disabled placeholder="请选择订单类型" style="width: 100%">
                        <el-option v-for="item in settlementManagementOrderTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="queryForm.type === '2'" v-show="isShowAll" label="货主公司" prop="companyId">
                    <el-select v-model="queryForm.companyId" :disabled="queryForm.receiverCompany !== '' && queryForm.receiverCompany !== null" clearable filterable placeholder="请选择货主公司" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in customerList" :key="item.companyId" :label="item.companyName" :value="item.companyId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="收件公司" prop="receiverCompany">
                    <el-select v-model="queryForm.receiverCompany" :disabled="queryForm.companyId !== '' && queryForm.companyId !== null" clearable filterable placeholder="请选择收件公司" style="width: 100%" @change="handleQuery">
                        <el-option v-for="(item, index) in receivedCompanyList" :key="index" :label="item" :value="item"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="付款方式" prop="settlementMethod" style="width: 245px">
                    <el-select v-model="queryForm.settlementMethod" clearable placeholder="请选择付款方式" @change="handleChangesTheOrderType">
                        <el-option v-for="item in settlementMethodList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all="isShowAll" @handleQuery="handleChangesTheOrderType" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>
        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <div class="d__flex__statisticsRows">
                <el-statistic :precision="2" :value="overview.receivedCostTotal" :value-style="{ color: '#5670FE' }" group-separator="," title="应收费用合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.accountTotal" :value-style="{ color: '#11caca' }" group-separator="," title="已对账合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.unAccountTotal" :value-style="{ color: '#fea700' }" group-separator="," title="未对账合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.receiptTotal" :value-style="{ color: '#3dc726' }" group-separator="," title="已收款合计"></el-statistic>
                <el-statistic :precision="2" :value="overview.unReceiptTotal" :value-style="{ color: '#f43b3c' }" group-separator="," title="未收款合计"></el-statistic>
            </div>
        </el-card>
        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10">
                <el-button icon="el-icon-download" type="warning" @click="handleExport">全部导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="orderCostQueryStatistics" @queryTable="getList" />
            </div>
            <column-table ref="orderCostQueryStatistics" v-loading="loading" :max-height="600" :columns="columns" :data="dataList" :show-summary="true" element-loading-text="加载中..." show-index>
                <template #type="{ row }">
                    <span>{{ formatDictionaryData('settlementManagementOrderTypeList', row.type) }}</span>
                </template>
                <template #settlementMethod="{ row }">
                    <span>{{ formatDictionaryData('settlementMethodList', row.settlementMethod) }}</span>
                </template>
                <template #status="{ row }">
                    <div v-html="formatStatus(row.status)"></div>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickFeeDetails(row)">费用明细</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :pageSizes="[10, 20, 30, 50, 100]" :total="total" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import { selectDictLabel } from '@/utils/dictLabel';
import RightToolbar from '@/components/RightToolbar/index.vue';
import orderCostQueryStatistics from '@/api/carrierEnd/orderCostQueryStatistics';
import { downloadNoData } from '@/utils';
import { setDatePickerShortcuts } from '@/utils/config-store';

export default {
    name: 'OrderCostQueryStatistics',
    components: {
        RightToolbar,
        SearchButton,
        ColumnTable
    },
    data() {
        return {
            fullLoading: false,
            // 搜索部分
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                queryTime: [],
                status: null,
                type: '2',
                companyId: null, // 承运商/货主编号
                receiverCompany: null, // 到付收货客户名称
                settlementMethod: null // 付款方式
            },
            orderFeeStatusList: [], // 费用状态
            settlementManagementOrderTypeList: [], // 订单类型
            customerList: [], // 货主/承运商列表
            receivedCompanyList: [], // 到付收货客户
            settlementMethodList: [], // 付款方式
            //统计部分
            overview: {
                receivedCostTotal: 0, // 应收费用合计
                accountTotal: 0, // 已对账合计
                unAccountTotal: 0, // 未对账合计
                receiptTotal: 0, // 已收款合计
                unReceiptTotal: 0 // 未收款合计
            },
            // 表格部分
            loading: false,
            dataList: [],
            total: 0,
            columns: [
                { title: '订单类型', key: 'type', align: 'center', width: '120px', columnShow: true },
                { title: '货主公司', key: 'companyName', align: 'center', width: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', minWidth: '200px', columnShow: true, showOverflowTooltip: true },
                { title: '付款方式', key: 'settlementMethod', align: 'center', width: '120px', columnShow: true },
                { title: '揽收费用', labelClassName: 'isShowSummary', key: 'collectCost', align: 'center', width: '120px', columnShow: true },
                { title: '干线费用', labelClassName: 'isShowSummary', key: 'transportCost', align: 'center', width: '120px', columnShow: true },
                { title: '配送费用', labelClassName: 'isShowSummary', key: 'deliveryCost', align: 'center', width: '120px', columnShow: true },
				{ title: '超区费用', key: 'exceedCountyCost', labelClassName: 'isShowSummary', align: 'center', width: '120px', columnShow: true },
                { title: '增值服务总费用', labelClassName: 'isShowSummary', key: 'addedServicesCost', align: 'center', width: '120px', columnShow: true },
                { title: '合同价合计', labelClassName: 'isShowSummary', key: 'totalContractPrice', align: 'center', width: '160px', columnShow: true },
                { title: '折扣金额', labelClassName: 'isShowSummary', key: 'discountCost', align: 'center', width: '120px', columnShow: true },
                { title: '异动费用', labelClassName: 'isShowSummary', key: 'abnormalCost', align: 'center', width: '120px', columnShow: true },
                { title: '应收费用合计', labelClassName: 'isShowSummary', key: 'totalReceivedCost', align: 'center', width: '120px', columnShow: true },
                { title: '费用状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right' },
                { title: '操作', key: 'opt', align: 'center', width: '100px', columnShow: true, hideFilter: true, fixed: 'right' }
            ],
            isShowAll: false,
            shortcuts: setDatePickerShortcuts(),
        };
    },
    computed: {
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || [];
                return selectDictLabel(dictionary, value) || value || '-';
            };
        },
        /**
         * 格式化费用单状态
         * @returns {(function(*): (string|string|string))|*}
         */
        formatStatus() {
            return (value) => {
                const statusText = this.selectDictLabel(this.orderFeeStatusList, value) || '-';
                if (value === '0') {
                    return `<span style="color: #B1B1B1">${statusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #F4AC00">${statusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #5670FE">${statusText}</span>`;
                } else if (value === '3') {
                    return `<span style="color: #1ACD7E">${statusText}</span>`;
                } else {
                    return statusText;
                }
            };
        }
    },
    created() {
        this.getDict();
        // 货主公司列表
        // this.handleChangesTheOrderType();
        // this.getList();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            this.orderFeeStatusList = await this.getDictList('4pl_order_expense_status');
            this.settlementManagementOrderTypeList = await this.getDictList('cost_settlement_management_order_type');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
        },
        /**
         * 订单费用明细跳转
         * */
        handleClickFeeDetails(data) {
            const params = { companyId: data.companyId, type: data.type, receiverCompany: data.receiverCompany };
            this.$router.push({ name: 'CustomerOrderExpenseManagement', params });
        },
        /** 导出按钮操作 */
        handleExport() {
            this.fullLoading = true;
            orderCostQueryStatistics
                .paymentExport({ filename: '客户订单费用查询统计.xls' }, '', '', 'blob')
                .then((res) => {
                    downloadNoData(res, 'application/vnd.ms-excel', '客户订单费用查询统计.xlsx');
                })
                .catch(() => {})
                .finally(() => {
                    this.fullLoading = false;
                });
        },
        /**
         * 数据查询接口
         * */
        getList() {
            this.loading = true;
            const { queryTime, ...params } = this.queryForm;
            orderCostQueryStatistics
                .orderCostStats(params)
                .then((res) => {
                    if (res.code === 200 && res.data.list.records) {
                        this.dataList = res.data.list.records || [];
                        this.total = res.data.list.total || 0;
                    }
                    if (res.code === 200 && res.data) {
                        const { receivedCostTotal, accountTotal, unAccountTotal, receiptTotal, unReceiptTotal } = res.data;
                        this.overview.receivedCostTotal = receivedCostTotal * 1 || 0;
                        this.overview.accountTotal = accountTotal * 1 || 0;
                        this.overview.unAccountTotal = unAccountTotal * 1 || 0;
                        this.overview.receiptTotal = receiptTotal * 1 || 0;
                        this.overview.unReceiptTotal = unReceiptTotal * 1 || 0;
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 搜索
         */
        handleQuery() {
            const { queryTime } = this.queryForm;
            if (queryTime && queryTime.length) {
                this.queryForm.startOrderDate = queryTime[0] + ' 00:00:00';
                this.queryForm.endOrderDate = queryTime[1] + ' 23:59:59';
            } else {
                this.queryForm.startOrderDate = null;
                this.queryForm.endOrderDate = null;
            }

            this.queryForm.current = 1;
            this.getList();
        },
        /**
         * 搜索重置
         * */
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleChangesTheOrderType();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 改变订单类型
         */
        async handleChangesTheOrderType() {
            this.handleQuery();
            this.customerList = [];
            this.receivedCompanyList = [];
            this.queryForm.companyId = null;
            this.queryForm.receiverCompany = null;

            const { queryTime, sendAddress, receiverAddress, ...params } = this.queryForm;

            try {
                const res1 = await orderCostQueryStatistics.customerNameList({ ...params });

                if (res1.code === 200 && res1.data) {
                    this.customerList = res1.data;
                }
            } catch (error) {
                console.error('Error fetching customer name list:', error);
            }

            try {
                const res2 = await orderCostQueryStatistics.receivingCustomerList({...params});

                if (res2.code === 200 && res2.data) {
                    this.receivedCompanyList = res2.data;
                }
            } catch (error) {
                console.error('Error fetching receiving customer list:', error);
            }
        }
    }
};
</script>

<style scoped>
.d__flex__statisticsRows {
    display: flex;
    justify-content: space-around;
}
.el-statistic {
    display: flex;
    align-items: baseline;
    margin-right: 20px;
    gap: 10px;
}
</style>
