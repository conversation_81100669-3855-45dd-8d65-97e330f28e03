<template>
    <div ref="chart" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';
export default {
    name: 'IndexOrderQty',
    props: {
        height: {
            type: String,
            default: '100%'
        },
        lineData: {
            type: [Object, Array],
            default: () => []
        },
        width: {
            type: String,
            default: '100%'
        }
    },
    data() {
        return {};
    },
    watch: {
        lineData: {
            handler() {
                this.initChart();
            },
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart();
        });
    },
    methods: {
        initChart() {
            const chart = echarts.init(this.$refs.chart);
            chart.setOption({
                title: {
                    show: this.lineData.seriesData.length === 0,
                    textStyle: {
                        color: '#ccc'
                    },
                    text: '暂无数据',
                    left: 'center',
                    top: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    textStyle: {
                        color: '#fff'
                    },
                    backgroundColor: 'rgba(54, 54, 54,0.7)'
                },
                grid: {
                    left: '2%',
                    right: '5%',
                    bottom: '3%',
                    top: '3%',
                    containLabel: true
                },
                xAxis: [
                    {
                        show: this.lineData.seriesData.length === 0 ? false : true,
                        type: 'category',
                        boundaryGap: false,
                        data: this.lineData.xAxisData
                    }
                ],
                yAxis: [
                    {
                        type: 'value'
                    }
                ],
                series: [
                    {
                        name: this.lineData.seriesName,
                        type: 'line',
                        stack: 'Total',
                        smooth: true,
                        lineStyle: {
                            color: '#2998ff',
                            width: 2
                        },
                        showSymbol: false,
                        areaStyle: {
                            opacity: 0.6,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(232, 244, 255, 1)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(232, 244, 255, 1)'
                                }
                            ])
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: this.lineData.seriesData
                    }
                ]
            });
        }
    }
};
</script>

<style scoped></style>
