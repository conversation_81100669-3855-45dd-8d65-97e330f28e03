import http from "@/utils/request"
export default {
  list: function (params) {
    return http.get('/task/alarm/config/list',params)
  },
  save: function (data) {
    return http.post('/task/alarm/config/save',data)
  },
  updateStatus: function (params) {
    return http.get('/task/alarm/config/updateStatus',params)
  },
  detail: function (id) {
    return http.get('/task/alarm/config/queryById',id)
  },
  getUser: function (id) {
    return http.get('/sys/user/list',id)
  },
  //删除规则
  deleteRuler: function (id) {
    return http.delete('/task/alarm/moniterRule/delete',id)
  },
}