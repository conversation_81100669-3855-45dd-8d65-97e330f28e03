<template>
  <div class="demo-collapse">
    <div v-if="formRow" style="height: 600px;overflow: auto;padding-right: 10px">
      <div v-if="formRow.split(',').indexOf('1') != -1">
        <span class="titleForm">基本信息</span>
        <el-form ref="creatform1" :disabled="data.title == '查看详情'" :model="formInline"
                 :rules="creatRules1"
                 class="demo-form-inline" label-position="right" label-width="125px" style="overflow-x:auto">
          <el-form-item label="通用名" prop="n1">
            <el-input v-model="formInline.n1" placeholder="请输入通用名" style="width:300px"
                      @blur="Pinyin()"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n1', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="商品名" prop="n2">
            <el-input v-model="formInline.n2" placeholder="请输入商品名" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n2', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item v-if="data.title == '查看详情'" label="拼音码" prop="n3">
            <el-input v-model="formInline.n3" :disabled="true" placeholder="系统生成字段"
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n3', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="自编码" prop="n4">
            <el-input v-model="formInline.n4" placeholder="系统生成字段" style="width:300px">
              <template v-slot:prepend>
                {{ formInline.n26 }}
              </template>
            </el-input>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n4', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="商品编码" prop="n17">
            <el-input v-model="formInline.n17" placeholder="请输入商品编码" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n17', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="基本单位" prop="n5">
            <el-select v-model="formInline.n5" placeholder="请选择基本单位" style="width:300px">
              <el-option v-for="(item, index) in data.basicType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="整件单位" prop="n6">
            <el-select v-model="formInline.n6" placeholder="请选择整件单位" style="width:300px">
              <el-option v-for="(item, index) in data.wholeType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="装箱规格" prop="n7">
            <el-input-number v-model="formInline.n7" :min="0" :precision="0" :step="1" :value-on-clear="0"
                             class="mx-4"
                             placeholder="请输入装箱规格" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n7', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="包装规格" prop="n10">
            <el-input v-model="formInline.n10" placeholder="请输入包装规格" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n10', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="生产厂家" prop="n12">
            <el-select v-model="formInline.n12" placeholder="请选择生产厂家" style="width:300px">
              <template #empty>
                <p style="
												text-align: center;
												color: #635f5e;
												margin: 15px 0;
											">
                  无数据
                </p>
                <p style="text-align: center">
                  <el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
                      data.venderDrug.value =
                        '';
                      venterSearch();
                    }
                      ">
                    返回
                  </el-button>
                </p>
              </template>
              <el-input v-model="data.venderDrug.value" placeholder="请输入企业名称搜索"
                        @keydown.enter="venterSearch"/>
              <el-option v-for="(item, index) in data.venderDrug
                  .records" :key="index" :label="item.enterpriseName" :value="item.id"/>
            </el-select>
          </el-form-item>
          <el-form-item label="生产地址" prop="n13">
            <el-select v-model="formInline.n13" :disabled="!formInline.n12"
                       :placeholder="formInline.n12 ? '请选择生产地址' : '请先选择生产厂家'"
                       style="width:300px">
              <el-option v-for="(item, index) in data.siteType" :key="index" :label="item.licenseAddress"
                         :value="item.licenseAddress"/>
            </el-select>
          </el-form-item>
          <el-form-item label="经营范围">
            <el-tree-select v-model="formInline.n14" :data="data.treesType"
                            :props="{ value: 'id', label: 'massName' }"
                            :render-after-expand="false" node-key="id" placeholder="请选择经营范围"
                            show-checkbox
                            style="width:300px" value-key="id"/>
          </el-form-item>
          <el-form-item label="有效期(月)" prop="n8">
            <el-input-number v-model="formInline.n8" :min="0" :precision="0" :step="1" :value-on-clear="0"
                             class="mx-4"
                             placeholder="请输入有效期" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n8', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="上市许可持有人">
            <el-input v-model="formInline.n11" placeholder="请输入上市许可持有人"
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n11', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="产地">
            <el-input v-model="formInline.n15" placeholder="请输入产地" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n15', false, 1)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="formInline.n16" placeholder="请输入备注" style="width:300px"/>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="formRow.split(',').indexOf('2') != -1">
        <span class="titleForm">质量信息</span>
        <el-form ref="creatform2" :disabled="data.title == '查看详情'" :model="formInline2"
                 :rules="creatRules2" class="demo-form-inline" label-position="right" label-width="125px"
                 style="overflow-x:auto">
          <el-form-item label="GSP属性" prop="n1">
            <el-select v-model="formInline2.n1" :disabled="true" filterable placeholder="请选择GSP属性"
                       style="width:300px">
              <el-option v-for="(item, index) in data.GSPType" :key="index" :label="item.valueName"
                         :value="item.valueName"/>
            </el-select>
          </el-form-item>
          <el-form-item label="类别" prop="n15">
            <el-select v-model="formInline2.n15" placeholder="请选择类别" style="width:300px"
                       @change="changeGsp">
              <el-option v-for="(item, index) in data.manageType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="商品分类" prop="n16">
            <el-select v-model="formInline2.n16" placeholder="请选择商品分类" style="width:300px">
              <el-option v-for="(item, index) in data.goodsType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="阳光采购码">
            <el-input v-model="formInline2.n18" placeholder="请输入阳光采购码" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n18', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="质管分类" prop="n17">
            <el-select v-model="formInline2.n17" placeholder="请选择质管分类" style="width:300px">
              <el-option v-for="(item, index) in data.conType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>

          <el-form-item label="质量标准" prop="n6">
            <el-input v-model="formInline2.n6" placeholder="请输入质量标准" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n6', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="养护类型" prop="n7">
            <el-select v-model="formInline2.n7" placeholder="请选择养护类型" style="width:300px">
              <el-option v-for="(
											item, index
										) in data.maintainType" :key="index" :label="item.name" :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="注册证号" prop="n19">
            <el-input v-model="formInline2.n19" placeholder="请输入注册证号" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n19', false, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="注册证发证日期" prop="n20">
            <el-date-picker v-model="formInline2.n20" placeholder="请选择注册证发证日期" size="default"
                            style="width:300px" type="date" value-format="YYYY-MM-DD"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n20', true, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="注册证有效期" prop="n21">
            <el-date-picker v-model="formInline2.n21" placeholder="请选择注册证有效期" size="default"
                            style="width:300px" type="date" value-format="YYYY-MM-DD"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n21', true, 2)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="贮藏温区" prop="n8">
            <el-select v-model="formInline2.n8" placeholder="请选择贮藏温区" style="width:300px">
              <el-option v-for="(item, index) in data.storeType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="贮藏温区范围">
            <el-select v-model="formInline2.n9" placeholder="请选择贮藏温区范围"
                       style="width:300px">
              <el-option v-for="(item, index) in data.warmType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="运输温区">
            <el-select v-model="formInline2.n10" placeholder="请选择运输温区" style="width:300px">
              <el-option v-for="(item, index) in data.storeType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="运输温度范围">
            <el-select v-model="formInline2.n11" placeholder="请选择运输温度范围"
                       style="width:300px">
              <el-option v-for="(item, index) in data.warmType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label=" " label-width="50px">
            <el-checkbox v-model="formInline2.n22" disabled label="唯一标识码"/>
            <el-checkbox v-model="formInline2.n12" label="首营品种"/>
            <el-checkbox v-model="formInline2.n13" label="特药控制"/>
          </el-form-item>
          <el-form-item label=" " label-width="50px">
            <el-checkbox v-model="formInline2.n14" label="特殊温区"/>
            <el-checkbox v-model="formInline2.n23" label="电子监控"/>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="formRow.split(',').indexOf('3') != -1">
        <span class="titleForm">经营信息</span>
        <el-form ref="creatform3" :default-expand-all="true" :disabled="data.title == '查看详情'"
                 :model="formInline3" :rules="creatRules3" class="demo-form-inline" label-position="right"
                 label-width="125px"
                 style="overflow-x:auto">
          <el-form-item label="税率(%)" prop="n1">
            <el-input-number v-model="formInline3.n1" :min="0" :precision="2" :step="1" :value-on-clear="0"
                             class="mx-4"
                             placeholder="请输入税率" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n1', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="税务分类编码" prop="n2">
            <el-input v-model="formInline3.n2" maxlength="100" placeholder="请输入税务分类编码"
                      show-word-limit
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n2', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="疗程数量">
            <el-input v-model="formInline3.n3" maxlength="100" placeholder="请输入疗程数量" show-word-limit
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n3', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="器械分类">
            <el-select v-model="formInline3.n4" placeholder="请选择器械分类" style="width:300px">
              <el-option v-for="(item, index) in data.typecos" :key="index" :label="item.valueName"
                         :value="item.valueName"/>
            </el-select>
          </el-form-item>
          <el-form-item label="使用天数(天)">
            <el-input-number v-model="formInline3.n5" :min="0" :precision="0" :step="1" :value-on-clear="0"
                             class="mx-4"
                             placeholder="请输入使用天数" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n5', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="医保分类">
            <el-select v-model="formInline3.n6" placeholder="请选择医保分类" style="width:300px">
              <el-option v-for="(item, index) in data.healthType" :key="index" :label="item.name"
                         :value="item.name"/>
            </el-select>
          </el-form-item>
          <el-form-item label="用法用量">
            <el-input v-model="formInline3.n7" maxlength="100" placeholder="请输入用法用量" show-word-limit
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n7', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="禁忌">
            <el-input v-model="formInline3.n8" placeholder="请输入禁忌" style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n8', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="主要成分">
            <el-input v-model="formInline3.n9" maxlength="100" placeholder="请输入主要成分" show-word-limit
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n9', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="特殊存储要求">
            <el-select v-model="formInline3.n10" placeholder="请选择特殊存储要求"
                       style="width:300px">
              <el-option v-for="(item, index) in data.saveType" :key="index" :label="item.valueName"
                         :value="item.valueName"/>
            </el-select>
          </el-form-item>
          <el-form-item label="功能主治">
            <el-input v-model="formInline3.n11" maxlength="100" placeholder="请输入功能主治" show-word-limit
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n11', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="适应症状">
            <el-input v-model="formInline3.n12" maxlength="100" placeholder="请输入适应症状" show-word-limit
                      style="width:300px"/>
            <el-tooltip class="box-item" content="框选识别" effect="dark" placement="top">
              <el-icon v-if="formRow" style="font-size: 20px;margin-left: 7px"
                       @click="iconBtn('n12', false, 3)">
                <Aim/>
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item label=" " label-width="50px">
            <el-checkbox v-model="formInline3.n13" label="基本药物"/>
            <el-checkbox v-model="formInline3.n14" label="注册商标"/>
            <el-checkbox v-model="formInline3.n15" label="会员积分"/>
          </el-form-item>
          <el-form-item label=" " label-width="50px">
            <el-checkbox v-model="formInline3.n16" label="价格保护"/>
            <el-checkbox v-model="formInline3.n17" label="贵细药品"/>
            <el-checkbox v-model="formInline3.n18" label="网络销售"/>
          </el-form-item>
          <el-form-item label=" " label-width="50px">
            <el-checkbox v-model="formInline3.n19" label="独占货位"/>
            <el-checkbox v-model="formInline3.n20" label="是否易碎"/>
          </el-form-item>
        </el-form>
      </div>
      <el-empty
          v-if="formRow.split(',').indexOf('4') != -1 &&  formRow.split(',').length==1"
          description="暂无对应表单"/>
    </div>
    <div v-if="!formRow">
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item name="1">
          <template #title>
            <span class="col_title">基本信息</span>
          </template>
          <el-form ref="creatform1" :disabled="data.title == '查看详情'" :inline="true" :model="formInline"
                   :rules="creatRules1"
                   class="demo-form-inline" label-position="right" label-width="125px"
                   style="overflow-x:auto">
            <div class="formBox">
              <el-form-item label="通用名" prop="n1">
                <el-input v-model="formInline.n1" placeholder="请输入通用名"
                          style="width: 100%;min-width:100px"
                          @blur="Pinyin()"/>
              </el-form-item>
              <el-form-item label="商品名" prop="n2">
                <el-input v-model="formInline.n2" placeholder="请输入商品名"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item v-if="data.title == '查看详情'" label="拼音码" prop="n3">
                <el-input v-model="formInline.n3" :disabled="true" placeholder="系统生成字段"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="自编码" prop="n4">
                <el-input v-model="formInline.n4" placeholder="系统生成字段"
                          style="width: 100%;min-width:100px">
                  <template v-slot:prepend>
                    {{ formInline.n26 }}
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="商品编码" prop="n17">
                <el-input v-model="formInline.n17" placeholder="请输入商品编码"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="基本单位" prop="n5">
                <el-select v-model="formInline.n5" placeholder="请选择基本单位"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.basicType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="整件单位" prop="n6">
                <el-select v-model="formInline.n6" placeholder="请选择整件单位"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.wholeType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="装箱规格" prop="n7">
                <el-input-number v-model="formInline.n7" :min="0" :precision="0" :step="1"
                                 :value-on-clear="0"
                                 class="mx-4"
                                 placeholder="请输入装箱规格"
                                 style="width: 100%;min-width:100px;height:100%"/>
              </el-form-item>
              <el-form-item label="包装规格" prop="n10">
                <el-input v-model="formInline.n10" placeholder="请输入包装规格"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="生产厂家" prop="n12">
                <el-select v-model="formInline.n12" placeholder="请选择生产厂家"
                           style="width: 100%;min-width:100px">
                  <template #empty>
                    <p style="
												text-align: center;
												color: #635f5e;
												margin: 15px 0;
											">
                      无数据
                    </p>
                    <p style="text-align: center">
                      <el-button size="small" style="margin: 0px 0 15px 0" type="primary" @click="() => {
                      data.venderDrug.value =
                        '';
                      venterSearch();
                    }
                      ">
                        返回
                      </el-button>
                    </p>
                  </template>
                  <el-input v-model="data.venderDrug.value" placeholder="请输入企业名称搜索"
                            @keydown.enter="venterSearch"/>
                  <el-option v-for="(item, index) in data.venderDrug
                  .records" :key="index" :label="item.enterpriseName" :value="item.id"/>
                </el-select>
              </el-form-item>
              <el-form-item label="生产地址" prop="n13">
                <el-select v-model="formInline.n13" :disabled="!formInline.n12"
                           :placeholder="formInline.n12 ? '请选择生产地址' : '请先选择生产厂家'"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.siteType" :key="index"
                             :label="item.licenseAddress"
                             :value="item.licenseAddress"/>
                </el-select>
              </el-form-item>
              <el-form-item label="经营范围">
                <el-tree-select v-model="formInline.n14" :data="data.treesType"
                                :props="{ value: 'id', label: 'massName' }"
                                :render-after-expand="false" node-key="id" placeholder="请选择经营范围"
                                show-checkbox
                                style="width: 100%;min-width:100px" value-key="id"/>
              </el-form-item>
              <el-form-item label="有效期(月)" prop="n8">
                <el-input-number v-model="formInline.n8" :min="0" :precision="0" :step="1"
                                 :value-on-clear="0"
                                 class="mx-4"
                                 placeholder="请输入有效期"
                                 style="width: 100%;min-width:100px;height:100%"/>
              </el-form-item>
              <el-form-item label="上市许可持有人">
                <el-input v-model="formInline.n11" placeholder="请输入上市许可持有人"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>

              <el-form-item label="产地">
                <el-input v-model="formInline.n15" placeholder="请输入产地"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="备注">
                <el-input v-model="formInline.n16" placeholder="请输入备注"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
            </div>
          </el-form>
        </el-collapse-item>
        <el-collapse-item name="2">
          <template #title>
            <span class="col_title">质量信息</span>
          </template>
          <el-form ref="creatform2" :disabled="data.title == '查看详情'" :inline="true" :model="formInline2"
                   :rules="creatRules2" class="demo-form-inline" label-position="right" label-width="125px"
                   style="overflow-x:auto">
            <div class="formBox">
              <el-form-item label="GSP属性" prop="n1">
                <el-select v-model="formInline2.n1" :disabled="true" filterable
                           placeholder="请选择GSP属性"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.GSPType" :key="index"
                             :label="item.valueName"
                             :value="item.valueName"/>
                </el-select>
              </el-form-item>
              <el-form-item label="类别" prop="n15">
                <el-select v-model="formInline2.n15" placeholder="请选择类别"
                           style="width: 100%;min-width:100px"
                           @change="changeGsp">
                  <el-option v-for="(item, index) in data.manageType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="商品分类" prop="n16">
                <el-select v-model="formInline2.n16" placeholder="请选择商品分类"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.goodsType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="阳光采购码">
                <el-input v-model="formInline2.n18" placeholder="请输入阳光采购码"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="质管分类" prop="n17">
                <el-select v-model="formInline2.n17" placeholder="请选择质管分类"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.conType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>

              <el-form-item label="质量标准" prop="n6">
                <el-input v-model="formInline2.n6" placeholder="请输入质量标准"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="养护类型" prop="n7">
                <el-select v-model="formInline2.n7" placeholder="请选择养护类型"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(
											item, index
										) in data.maintainType" :key="index" :label="item.name" :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="注册证号" prop="n19">
                <el-input v-model="formInline2.n19" placeholder="请输入注册证号"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="注册证发证日期" prop="n20">
                <el-date-picker v-model="formInline2.n20" placeholder="请选择注册证发证日期"
                                size="default"
                                style="width: 100%;min-width:100px" type="date"
                                value-format="YYYY-MM-DD"/>
              </el-form-item>
              <el-form-item label="注册证有效期" prop="n21">
                <el-date-picker v-model="formInline2.n21" placeholder="请选择注册证有效期"
                                size="default"
                                style="width: 100%;min-width:100px" type="date"
                                value-format="YYYY-MM-DD"/>
              </el-form-item>
              <el-form-item label="贮藏温区" prop="n8">
                <el-select v-model="formInline2.n8" placeholder="请选择贮藏温区"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.storeType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="贮藏温区范围">
                <el-select v-model="formInline2.n9" placeholder="请选择贮藏温区范围"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.warmType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="运输温区">
                <el-select v-model="formInline2.n10" placeholder="请选择运输温区"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.storeType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="运输温度范围">
                <el-select v-model="formInline2.n11" placeholder="请选择运输温度范围"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.warmType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>

            </div>
            <el-form-item label=" " label-width="50px">
              <el-checkbox v-model="formInline2.n22" disabled label="唯一标识码"/>
              <el-checkbox v-model="formInline2.n12" label="首营品种"/>
              <el-checkbox v-model="formInline2.n13" label="特药控制"/>
              <el-checkbox v-model="formInline2.n14" label="特殊温区"/>
              <el-checkbox v-model="formInline2.n23" label="电子监控"/>
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item name="3">
          <template #title>
            <span class="col_title">经营信息</span>
          </template>
          <el-form ref="creatform3" :default-expand-all="true" :disabled="data.title == '查看详情'"
                   :inline="true"
                   :model="formInline3" :rules="creatRules3" class="demo-form-inline" label-position="right"
                   label-width="125px"
                   style="overflow-x:auto">
            <div class="formBox">
              <el-form-item label="税率(%)" prop="n1">
                <el-input-number v-model="formInline3.n1" :min="0" :precision="2" :step="1"
                                 :value-on-clear="0"
                                 class="mx-4"
                                 placeholder="请输入税率"
                                 style="width: 100%;min-width:100px;height:100%"/>
              </el-form-item>
              <el-form-item label="税务分类编码" prop="n2">
                <el-input v-model="formInline3.n2" maxlength="100" placeholder="请输入税务分类编码"
                          show-word-limit
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="疗程数量">
                <el-input v-model="formInline3.n3" maxlength="100" placeholder="请输入疗程数量"
                          show-word-limit
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="器械分类">
                <el-select v-model="formInline3.n4" placeholder="请选择器械分类"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.typecos" :key="index"
                             :label="item.valueName"
                             :value="item.valueName"/>
                </el-select>
              </el-form-item>
              <el-form-item label="使用天数(天)">
                <el-input-number v-model="formInline3.n5" :min="0" :precision="0" :step="1"
                                 :value-on-clear="0"
                                 class="mx-4"
                                 placeholder="请输入使用天数"
                                 style="width: 100%;min-width:100px;height:100%"/>
              </el-form-item>
              <el-form-item label="医保分类">
                <el-select v-model="formInline3.n6" placeholder="请选择医保分类"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.healthType" :key="index" :label="item.name"
                             :value="item.name"/>
                </el-select>
              </el-form-item>
              <el-form-item label="用法用量">
                <el-input v-model="formInline3.n7" maxlength="100" placeholder="请输入用法用量"
                          show-word-limit
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="禁忌">
                <el-input v-model="formInline3.n8" placeholder="请输入禁忌"
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="主要成分">
                <el-input v-model="formInline3.n9" maxlength="100" placeholder="请输入主要成分"
                          show-word-limit
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="特殊存储要求">
                <el-select v-model="formInline3.n10" placeholder="请选择特殊存储要求"
                           style="width: 100%;min-width:100px">
                  <el-option v-for="(item, index) in data.saveType" :key="index"
                             :label="item.valueName"
                             :value="item.valueName"/>
                </el-select>
              </el-form-item>
              <el-form-item label="功能主治">
                <el-input v-model="formInline3.n11" maxlength="100" placeholder="请输入功能主治"
                          show-word-limit
                          style="width: 100%;min-width:100px"/>
              </el-form-item>
              <el-form-item label="适应症状">
                <el-input v-model="formInline3.n12" maxlength="100" placeholder="请输入适应症状"
                          show-word-limit
                          style="width: 100%;min-width:100px"/>
              </el-form-item>

            </div>
            <el-form-item label=" " label-width="50px">
              <el-checkbox v-model="formInline3.n13" label="基本药物"/>
              <el-checkbox v-model="formInline3.n14" label="注册商标"/>
              <el-checkbox v-model="formInline3.n15" label="会员积分"/>
              <el-checkbox v-model="formInline3.n16" label="价格保护"/>
              <el-checkbox v-model="formInline3.n17" label="贵细药品"/>
              <el-checkbox v-model="formInline3.n18" label="网络销售"/>
              <el-checkbox v-model="formInline3.n19" label="独占货位"/>
              <el-checkbox v-model="formInline3.n20" label="是否易碎"/>
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item name="4">
          <template #title>
            <span class="col_title">附件上传</span>
          </template>
          <div style="margin-bottom: 15px">
            <!--          <el-button v-if="data.title != '查看详情'" type="primary" @click="newAddFile()">新增-->
            <!--          </el-button>-->
            <!--          <el-button v-if="data.title != '查看详情'" type="danger" @click="delFile()">删除-->
            <!--          </el-button>-->
			  <el-select v-if="!ocrFlag" v-model="smallSelect" placeholder="请选择器械小类"
						 style="margin-right: 10px;width: 200px">
				  <el-option v-for="(item, index) in smallList" :key="index" :label="item.name"
							 :value="item.value"/>
			  </el-select>
			  <el-button v-if="data.title != '查看详情'&&!ocrFlag" :disabled="!smallSelect" type="primary"
						 @click="getFileList()">获取附件列表
			  </el-button>
          </div>
          <el-table v-loading="fileLoading" :cell-style="{ textAlign: 'center' }" :data="formInline4.table"
                    :header-cell-style="{ 'text-align': 'center' }" border style="width: 100%;min-width:100px"
                    @selection-change="handleSelectionChange">
            <el-table-column label="序号" prop="n1" width="50">
              <template #default="scope">
                {{ scope.row.n1 }}
              </template>
            </el-table-column>
            <el-table-column label="类型" prop="n2" width="150">
              <template #default="scope">
                {{ echo1(scope.row.n2) }}
              </template>
            </el-table-column>
            <el-table-column label="文件类型" prop="n3" width="250">
              <template #default="scope">
                {{ echo2(scope.row.n3) }}
              </template>
            </el-table-column>
            <el-table-column label="文件名称" min-width="400" prop="n4">
              <template #default="scope">
                <div v-if="scope.row.n4.file.length > 0">
                  <div v-for="(item, index) in scope.row.n4.file" :key="index"
                       style="display: flex;justify-content: center">
                    <el-tooltip :content="item.name" class="box-item" effect="dark" placement="top">
                      <p style="text-overflow: ellipsis;overflow: hidden;word-break: break-all;line-height: 30px;white-space: nowrap;font-size:12px;color: #2a76f8;cursor: pointer;"
                         @click="checkFile(item)">
                        {{ item.name }}</p>
                    </el-tooltip>
                    <p v-if="item.url&&!ocrFlag"
                       style="display: flex;align-items:center;margin-left: 10px "
                       @click="delFileS(scope.$index, index)">
                      <el-icon style="color: red;">
                        <Delete/>
                      </el-icon>
                    </p>
                  </div>
                </div>
                <p v-else>请上传</p>
              </template>
            </el-table-column>
            <el-table-column v-if="!ocrFlag" label="文件上传" prop="n4" width="130">
              <template #default="scope">
                <el-upload :auto-upload="true" :before-upload="(file) =>
                        beforeFile(file, scope)
                        " :data="{
                          zhType: scope.row.n4.zhType,
                          fjType: scope.row.n4.fjType,
                          smallClass: scope.row.n4.smallClass
                        }" :disabled="title == '查看详情' || (scope.row.n4.file.length > 0 && scope.row.n6 == 0)"
                           :headers="headers"
                           :on-error="() => errorFile(scope)" :on-success="(response) =>
                  successFile(response, scope)
                  " :show-file-list="false" :action="process.env.VUE_APP_API_UPLOAD" class="upload-demo" multiple style="display: inline">
                  <template #trigger>
                    <el-button
                        :disabled="title == '查看详情' || (scope.row.n4.file.length > 0 && scope.row.n6 == 0)"
                        :icon='UploadFilled' type="primary">上传
                    </el-button>
                  </template>
                </el-upload>
              </template>
            </el-table-column>
            <el-table-column label="是否必传" prop="n5" width="100">
              <template #default="scope">
                {{ scope.row.n5 == 0 ? '否' : '是' }}
              </template>
            </el-table-column>
            <el-table-column label="是否多张" prop="n6" width="100">
              <template #default="scope">
                {{ scope.row.n6 == 0 ? '否' : '是' }}
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="400" prop="n7">
              <template #default="scope">
                <p v-show="!scope.row.n7.flag" style="width: 100%;min-width:100px" @click="
                formInline4.table[
                  scope.$index
                ].n7.flag = true
                ">
                  {{ scope.row.n7.str }}&emsp;
                </p>

                <el-input v-show="scope.row.n7.flag" v-model="formInline4.table[scope.$index].n7
                .str
                " :disabled="title == '查看详情'" placeholder="请输入备注" @blur="
    formInline4.table[
      scope.$index
    ].n7.flag = false
    "/>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import {onBeforeMount, onMounted, reactive, ref, toRefs, watchEffect} from 'vue';
import tool from "@/utils/tool";
import {ElLoading, ElMessage} from "element-plus";
import {drugApi} from "@/api/model/commodity/drug";
import {Aim, Delete, UploadFilled} from '@element-plus/icons-vue'
import {applianceApi} from "@/api/erp/IntelligentInput/postureApi";
// import { useStore } from 'vuex';

//console.log('1-开始创建组件-setup')
/**
 * 数据部分
 */
const headers = {
  Authorization: "Bearer " + tool.cookie.get("TOKEN"),
  ContentType: "multipart/form-data",
  clientType:'pc',
};
const upload = ref()
const emit = defineEmits(['checkFile', 'iconBtn', 'changeGsp', 'delFileS', 'successFile', 'errorFile', 'Pinyin', 'venterSearch', 'newAddFile', 'delFile'])
const props = defineProps({
  title: {
    default: ""
  },
  formInline: {
    default: {}
  },
  formInline2: {
    default: {}
  },
  formInline3: {
    default: {}
  },
  formInline4: {
    default: {}
  },
  data: {
    default: {}
  },
  creatform1: {
    default: {}
  },
  creatform2: {
    default: {}
  },
  creatform3: {
    default: {}
  },
  formRow: {
    default: null
  },
  ocrFlag: {
    default: false
  },
  smallTypeForm: {
    default: null
  }
})
const smallSelect = ref()
const smallList = ref()
const fileLoading = ref(false); //
const fileSmallType = ref()
const fileType = ref()
const {
  title,
  formInline,
  formInline2,
  formInline3,
  formInline4,
  data,
  smallTypeForm,
  ocrFlag,
  formRow
} = toRefs(props)
const creatform1 = ref(); //验证表单/流程新增
const creatform2 = ref(); //验证表单/流程新增
const creatform3 = ref(); //验证表单/流程新增


const creatRules1 = reactive({
  n1: [{required: true, message: "请输入通用名", trigger: "blur"}],
  n2: [{required: true, message: "请输入商品名", trigger: "blur"}],
  n3: [{required: true, message: "请输入拼音码", trigger: "blur"}],
  n4: [{required: true, message: "请输入自编码", trigger: "blur"}],
  n5: [{required: true, message: "请选择基本单位", trigger: "blur"}],
  n6: [{required: true, message: "请选择整件单位", trigger: "blur"}],
  n7: [{required: true, message: "请输入装箱规格", trigger: "blur"}],
  n8: [{required: true, message: "请输入有效期", trigger: "blur"}],
  n9: [{required: true, message: "请选择器械剂型", trigger: "blur"}],
  n10: [{required: true, message: "请输入包装规格", trigger: "blur"}],
  n12: [{required: true, message: "请选择生产厂家", trigger: "blur"}],
  n13: [{required: true, message: "请选择生产地址", trigger: "blur"}],
  n14: [{required: true, message: "请输入经营范围", trigger: "blur"}],
  n17: [{required: true, message: "请输入商品编码", trigger: "blur"}],
});
const creatRules2 = reactive({
  n1: [{required: true, message: "请选择GSP属性", trigger: "blur"}],
  n2: [{required: true, message: "请选择批准文号", trigger: "blur"}],
  n3: [{required: true, message: "请输入批准文号有效期", trigger: "blur"}],
  n4: [{required: true, message: "请选择处方类型", trigger: "blur"}],
  n5: [{required: true, message: "请输入注册批件号", trigger: "blur"}],
  n6: [{required: true, message: "请输入质量标准", trigger: "blur"}],
  n7: [{required: true, message: "请选择养护类型", trigger: "blur"}],
  n8: [{required: true, message: "请选择贮藏温区", trigger: "blur"}],
  n15: [{required: true, message: "请选择类别", trigger: "blur"}],
  n16: [{required: true, message: "请选择商品分类", trigger: "blur"}],
  n17: [{required: true, message: "请选择质管分类", trigger: "blur"}],
  n19: [{required: true, message: "请输入注册证号", trigger: "blur"}],
  n20: [
    {
      required: true,
      message: "请选择注册证发证日期",
      trigger: "blur",
    },
  ],
  n21: [{required: true, message: "请选择注册证有效期", trigger: "blur"}],
  n24: [{required: true, message: "请输入生产许可证号", trigger: "blur"}],
  n25: [
    {required: true, message: "请选择生产许可证有效期", trigger: "blur"},
  ],
});
const activeNames = ref(["1"]);
const creatRules3 = reactive({
  n1: [{required: true, message: "请输入税率", trigger: "blur"}],
  n2: [{required: true, message: "请输入税务分类编码", trigger: "blur"}],
  n5: [{required: true, message: "请输入使用天数", trigger: "blur"}],
});
const changeGsp = () => {
  emit('changeGsp')
}
const errorFile = (scope) => {
  emit('errorFile', scope)
}
const delFileS = (ind1, ind2) => {
  emit('delFileS', ind1, ind2)
}
const successFile = (response, scope) => {
  emit('successFile', response, scope)
}
const iconBtn = (ind, timeFlag, type) => {
  emit('iconBtn', {
    ind: ind,
    time: timeFlag,
    type: type
  })
}
const Pinyin = () => {
  emit('Pinyin')
};
const checkFile = (item) => {
  emit('checkFile', item)
}
const beforeFile = (file, scope) => {
  if (file.size > 5242880) {
    ElMessage.error("文件不能大于5M");
    return false;
  } else if (scope.row.n4.file.length > 0 && scope.row.n6 == 0) {
    ElMessage.error("不可多张上传");
    return false;
  }
  ElLoading.service();
  formInline4.value.table[scope.$index].n4.zhType = '器械';
  formInline4.value.table[scope.$index].n4.fjType = echo2(formInline4.value.table[scope.$index].n3);
  formInline4.value.table[scope.$index].n4.smallClass = echo1(formInline4.value.table[scope.$index].n2);
}
const echo1 = (value) => {
  return fileSmallType.value.find(item => item.value == value)?.name
}
const echo2 = (value) => {
  return fileType.value.find(item => item.value == value)?.name
}
const getFileList = async () => {
  fileLoading.value = true
  drugApi.getFileList({
	  bigType: 3,
	  size: 1000,
	  smallType: ocrFlag.value ? smallTypeForm.value : smallSelect.value,
    isFile: 1
  }).then(res => {
    if (res.code == 200) {
      formInline4.value.table = []
      res.data.records.forEach((item, index) => {
        formInline4.value.table.push({
          n1: index + 1,
          n2: item.smallType,
          n3: item.categoryName,
          n4: {
            zhType: "",
            fjType: "",
            smallClass: "",
            file: []
          },
          n5: item.isUpload,
          n6: item.isMultiPage,
          n7: {
            str: item.remark,
            flag: false,
          }
        })
      })
    }
    fileLoading.value = false
  })
}
const venterSearch = () => {
  emit('venterSearch')
}
const newAddFile = () => {
  emit('newAddFile')
}
const delFile = () => {
  emit('delFile')
}
const handleSelectionChange = (selection) => {
  formInline4.value.delAll = selection;
};
const handleChange = (val) => {
  console.log(val);
};
onBeforeMount(async () => {
  //console.log('2.组件挂载页面之前执行----onBeforeMount')
  const res1 = await drugApi.getSys({
    type: 'drug_type',
    size: 1000
  })
  const res2 = await drugApi.getSys({
    type: 'directory_file_name',
    size: 1000
  })

  fileSmallType.value = res1.data
  fileType.value = res2.data
})
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
  if (ocrFlag.value) {
    getFileList()
  } else {
	  applianceApi.getSmall({
		  type: 'instrument_type',
		  size: 1000
	  }).then(res => {
		  if (res.code == 200) {
			  smallList.value = res.data
		  }
	  })
  }
})
watchEffect(() => {
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({
  creatform1,
  creatform2,
  creatform3,
  activeNames
})

</script>
<style lang='scss' scoped>
.formBox {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

::v-deep .el-checkbox__inner {
  width: 20px !important;
  height: 20px !important;
}

::v-deep .el-checkbox__inner::after {
  width: 7px !important;
  height: 13px !important;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.dialog-footer button:first-child {
  margin-right: 10px;
}

.el-checkbox-group {
  margin-right: 10px;
}

.el-select-dropdown__list {
  .el-input {
    width: 90%;
    margin-left: 5%;
    margin-top: 10px;
    margin-bottom: 15px;
  }

  .el-pagination {
    margin-right: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.cell {
  width: 100%;
  height: 100%;
}

.zhe {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.6);

  .imgDiv {
    max-width: 70%;
    max-height: 70%;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    img {
      width: 100%;
      min-width: 200px;
      height: 100%;
      color: #fff;
    }

    span {
      position: absolute;
      font-size: 25px;
      border-radius: 50%;
      height: 30px;
      width: 30px;
      line-height: 34px;
      text-align: center;
      color: #fff;
      right: -30px;
      top: -5px;
    }
  }
}

.col_title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 8px;

  &::after {
    content: "";
    display: inline-block;
    width: 3px;
    height: 20px;
    background-color: #2878ff;
    border-radius: 2px;
    position: absolute;
    top: 15px;
    left: 0;
  }
}

.el-dialog {
  position: relative;
}

.stateTitle {
  position: absolute;
  font-size: 15px;
  top: 21px;
  right: 53px;
}

.titleForm {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
}
</style>
