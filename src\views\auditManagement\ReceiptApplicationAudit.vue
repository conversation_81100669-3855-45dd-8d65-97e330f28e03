<template>
    <div>
        <el-dialog v-model="visible" :title="auditTitle" top="5vh" width="90%" @close="closeVisible">
            <div v-loading="viewLoading">
                <el-descriptions>
                    <el-descriptions-item label="申请编号：" v-if="taskInfo?.claimApply?.applyNo">{{ taskInfo.claimApply.applyNo }}</el-descriptions-item>
                    <el-descriptions-item label="申请人：" v-if="taskInfo?.claimApply?.applyUser">{{ taskInfo.claimApply.applyUser }}</el-descriptions-item>
                    <el-descriptions-item label="申请时间：" v-if="taskInfo?.claimApply?.claimDate">{{ taskInfo.claimApply.claimDate }}</el-descriptions-item>
                </el-descriptions>
                <!--到款信息-->
                <div style="padding-bottom: 10px">
                    <el-descriptions border class="mt-10 mb-5" size="default" title="到款信息"></el-descriptions>
                    <!--表格数据-->
                    <column-table key="incomingViewTable" ref="incomingViewTable" :columns="incomingColumns" :data="taskInfo.claimArriveList" rowKey="id" show-index>
                        <template #status="{ row }">
                            <span>{{ row.status ? selectDictLabel(statusOptions, row.status) : '--' }}</span>
                        </template>
                        <template #arriveDate="{ row }">
                            <span>{{ row.arriveDate ? formatDate(row.arriveDate) : '--' }}</span>
                        </template>
                        <template #arriveSource="{ row }">
                            <span>{{ selectDictLabel(arriveSourceOptions, row.arriveSource) }}</span>
                        </template>
                        <template #accountAmount="{ row }">
                            <span>{{ row.accountAmount ? row.accountAmount : '--' }}</span>
                        </template>
                    </column-table>
                    <div class="text-main-500 font-bold" v-if="taskInfo?.claimApply?.claimAmount">合计认款金额：{{ taskInfo.claimApply.claimAmount }}</div>
                </div>
                <!--支付信息-->
                <div style="padding-bottom: 10px">
                    <el-descriptions border class="mt-10 mb-10" size="default" title="支付信息"></el-descriptions>
                    <!--表格数据-->
                    <column-table key="paymentViewTable" ref="paymentViewTable" :columns="paymentColumns" :data="taskInfo.claimPayApplyList" rowKey="id" show-index>
                        <template #businessType="{ row }">
                            <span :style="setBusinessTypeColor(row.businessType)">{{ selectDictLabel(businessTypeList, row.businessType) }}</span>
                        </template>
                        <template #status="{ row }">
                            <span :style="setStatusColor(row.status)">{{ selectDictLabel(statusList, row.status) }}</span>
                        </template>
                        <template #receiveCompany="{ row }">
                            <span>{{ row.receiveCompany ? selectDictLabel(receiverCompanyOptions, row.receiveCompany) : '--' }}</span>
                        </template>
                    </column-table>
                    <div class="text-main-500 font-bold" v-if="taskInfo?.claimApply?.claimAmount">合计支付金额：{{ taskInfo.claimApply.claimAmount }}</div>
                </div>

                <!--提交表单-->
                <el-form ref="baseForm" class="mt10" @submit.native.prevent v-if="taskInfo?.claimApply?.remark">
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="taskInfo.claimApply.remark" :disabled="true" class="w-full" clearable maxlength="100" placeholder="请输入备注" show-word-limit type="textarea" />
                    </el-form-item>
                </el-form>
                <!--审批记录-->
                <el-card v-show="taskInfo?.approveList?.length > 0" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
                    <el-descriptions border class="mt-10 mb-10" size="small" title="审批记录"></el-descriptions>
                    <audit-and-flow-records ref="AuditAndFlowRecords"></audit-and-flow-records>
                </el-card>
                <!--审批表单-->
                <el-form ref="receiptApplicationAuditForm" :model="receiptApplicationAuditForm" label-width="auto" v-if="auditInfo.auditStatus == '0'  && type === 'examine'">
                    <el-form-item :rules="{ required: true, message: '请选择审批意见', trigger: 'change' }" label="审批意见" prop="status">
                        <el-radio-group v-model="receiptApplicationAuditForm.status">
                            <el-radio label="1">通过</el-radio>
                            <el-radio label="2">驳回</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="receiptApplicationAuditForm?.status === '2'" :rules="{ required: true, message: '请输入驳回原因', trigger: 'blur' }" label="驳回原因" prop="auditIdea">
                        <el-input v-model="receiptApplicationAuditForm.auditIdea" :rows="4" maxlength="500" placeholder="请输入驳回原因" show-word-limit type="textarea"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer v-if="auditInfo.auditStatus == '0'">
                <div v-loading="receiptApplicationAuditLoading" style="text-align: center">
                    <el-button @click="closeVisible">取 消</el-button>
                    <el-button type="primary" @click="submitForm()">提 交</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import AuditAndFlowRecords from '@/views/auditManagement/AuditAndFlowRecords';
import { tasks } from '@/api/auditManagement/approvalTask';
import receiptOfPayment from '@/api/carrierEnd/receiveThePayment/receiptOfPayment';
import moment from 'moment';
export default {
    name: 'ReceiptApplicationAudit',
    components: { AuditAndFlowRecords, ColumnTable },
    model: {
        prop: 'receiptApplicationAuditVisible',
        event: 'update:receiptApplicationAuditVisible'
    },
    props: {
        auditInfo: {},
        auditTitle: {
            type: String,
            default: undefined
        },
        receiptApplicationAuditVisible: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'details'
        }
    },
    data() {
        return {
            receiptApplicationAuditForm: {
                status: undefined,
                auditIdea: undefined
            }, // 审批表单
            receiptApplicationAuditLoading: false, // 审批loading
            visible: this.receiptApplicationAuditVisible, // 审批弹窗
            incomingColumns: [
                { title: '到款日期', key: 'arriveDate', align: 'center', width: '110px', columnShow: true },
                { title: '到款单位/人', key: 'arriveUnit', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '到款金额', key: 'arriveAmount', align: 'center', width: '100px', columnShow: true },
                { title: '到款渠道', key: 'arriveSource', align: 'center', minWidth: '160px', columnShow: true, showOverflowTooltip: true },
                { title: '入账金额', key: 'accountAmount', align: 'center', width: '100px', columnShow: true },
                { title: '认款状态', key: 'status', align: 'center', width: '80px', columnShow: true },
                { title: '认款日期', key: 'claimDate', align: 'center', width: '110px', columnShow: true },
                { title: '认款金额', key: 'claimAmount', align: 'center', width: '100px', columnShow: true },
                { title: '待认款金额', key: 'stayAmount', align: 'center', width: '100px', columnShow: true },
                { title: '本次认款金额', key: 'currentClaimAmount', align: 'center', width: '100px', columnShow: true }
            ], // 到款记录列
            statusOptions: [], // 认款状态
            paymentColumns: [
                { title: '汇款时间', key: 'remitTime', align: 'center', width: '160px', columnShow: true },
                { title: '付款类型', key: 'businessType', align: 'center', minWidth: '100px', columnShow: true },
                { title: '付款公司', key: 'ownerName', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '收款公司', key: 'receiveCompany', align: 'center', minWidth: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '汇款金额（元）', key: 'remitAmount', align: 'center', minWidth: '120px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '结算公司', key: 'settleCompany', align: 'center', width: '250px', columnShow: true, showOverflowTooltip: true },
                { title: '审批状态', key: 'status', align: 'center', fixed: 'right', minWidth: '160px', columnShow: true }
            ], // 支付信息列
            businessTypeList: [], // 付款类型
            receiverCompanyOptions: [], // 收款公司
            statusList: [], // 审批状态
            arriveSourceOptions: [], // 到款渠道
            taskInfo: {}, // 任务信息
            viewLoading: false // 查看loading
        };
    },
    computed: {
        /**
         * 设置业务类型颜色
         */
        setBusinessTypeColor() {
            return (businessType) => {
                return (
                    {
                        '1': { color: '#f0ad4e' },
                        '2': {}
                    }[businessType] || { color: '#999' }
                );
            };
        },
        /**
         * 设置审批状态颜色
         */
        setStatusColor() {
            return (status) => {
                return (
                    {
                        '0': { color: '#f0ad4e' },
                        '1': { color: '#5cb85c' },
                        '2': { color: '#d9534f' },
                        '3': { color: '#999' }
                    }[status] || { color: '#999' }
                );
            };
        },
        /**
         * 格式化日期
         * @returns {function(*=): *}
         */
        formatDate() {
            return function (date) {
                return moment(date).format('YYYY-MM-DD');
            };
        }
    },
    async created() {
        await this.getDict();
        this.$nextTick(() => {
            this.getDetail();
            // if (this.taskInfo.approveList.length > 0) {
            // 	console.log(this.$refs.AuditAndFlowRecords);
            //     this.$refs.AuditAndFlowRecords.timeFns(this.taskInfo.approveList);
            // }
        });
    },
    methods: {
        /**
         * 获取详情
         */
        getDetail() {
            this.viewLoading = true;
            receiptOfPayment
                .queryById({ id: this.auditInfo.sourceId })
                .then((res) => {
                    if (res.code == 200) {
                        this.taskInfo = res.data;
                    }
                })
                .finally(() => {
                    this.viewLoading = false;
                });
        },
        /**
         * 获取字典数据
         */
        async getDict() {
            // 付款类型
            this.businessTypeList = await this.getDictList('payment_type');
            // 审批状态
            this.statusList = await this.getDictList('payment_approval_type');
            // 认款状态
            this.statusOptions = await this.getDictList('fourpl_subscription_status');
            // 收款公司
            this.receiverCompanyOptions = await this.getDictList('signing_company');
            // 到款渠道
            this.arriveSourceOptions = await this.getDictList('fourpl_arrive_source');
        },
        /**
         * 关闭抽屉
         */
        closeVisible() {
            this.visible = false;
            this.$emit('update:receiptApplicationAuditVisible', false);
        },
        /**
         * 提交表单
         */
        submitForm() {
            this.$refs.receiptApplicationAuditForm.validate(async (valid) => {
                if (valid) {
                    this.receiptApplicationAuditLoading = true;
                    const params = {
                        taskId: this.auditInfo.id,
                        auditIdea: this.receiptApplicationAuditForm.auditIdea,
                        status: this.receiptApplicationAuditForm.status
                    };
                    tasks
                        .auditSubmit(params)
                        .then((res) => {
                            if (res.code == 200) {
                                this.$message.success('操作成功');
                                this.$emit('success');
                                this.closeVisible();
                            }
                        })
                        .finally(() => {
                            this.receiptApplicationAuditLoading = false;
                        });
                }
            });
        }
    }
};
</script>

<style scoped>
.el-descriptions__header {
    margin-bottom: 0px;
}
.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    padding-bottom: 0px;
}
</style>
