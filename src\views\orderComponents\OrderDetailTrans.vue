<template>
    <div>
        <div class="p-16 orderDetails" style="background-color: #f5f7fd">
            <el-radio-group v-model="type" style="margin-bottom: 15px" @change="tabClick">
                <el-radio-button label="1">详情</el-radio-button>
                <el-radio-button label="2">温度记录</el-radio-button>
                <el-radio-button v-if="source == '1'" label="3">异动记录</el-radio-button>
                <el-radio-button label="4">轨迹</el-radio-button>
                <el-radio-button label="5">运输记录</el-radio-button>
            </el-radio-group>
            <order-details v-if="type == 1" :order-info="orderInfo" :source="source"></order-details>
            <temperature-recording v-if="type == 2" :orderInfo="orderInfo"></temperature-recording>
            <OrderTransactionDetails v-if="type == 3" :orderId="orderInfo.orderId"></OrderTransactionDetails>
            <div v-if="type == 4">
                <el-card class="mb16 box__locus" shadow="never">
                    <el-timeline :reverse="reverse">
                        <el-timeline-item v-for="(activity, i) in activities" :key="i" :timestamp="timeFormatting(activity.createDate)" :type="i == 0 ? 'success' : ''">
                            <div>
                                {{ activity.content }}
                                <el-tag v-if="activity.remark" effect="plain" round size="small" @click="toggleVisibility(i)">
                                    查看三方物流轨迹
                                    <el-icon> <ArrowUp v-if="activity.isVisible" /> <ArrowDown v-else /> </el-icon>
                                </el-tag>
                                <transition name="fade">
                                    <div v-if="activity.isVisible" class="three_party">
                                        <el-timeline :reverse="true">
                                            <el-timeline-item v-for="(record, recordIndex) in parseRemark(activity.remark)" :key="recordIndex" :timestamp="timeFormatting(record.time)" :type="recordIndex == 0 ? 'success' : ''">
                                                <div>
                                                    {{ record.remark }}
                                                </div>
                                            </el-timeline-item>
                                        </el-timeline>
                                    </div>
                                </transition>
                                <el-button v-if="activity.status == 'T22'" link type="primary" @click="toDetail(activity)">修改记录</el-button>
                            </div>
                        </el-timeline-item>
                    </el-timeline>
                    <el-empty v-if="activities && activities.length == 0" description="不存在运输轨迹"></el-empty>
                </el-card>
            </div>
            <div v-if="type == 5">
                <transportation-records :parent-data="parentData" class="mb10" />
            </div>
        </div>
        <!--    修改记录-->
        <el-drawer v-model="amendantRecordShow" direction="rtl" size="50%" title="修改记录">
            <order-modification-details v-if="amendantRecordShow" :transRecordId="transRecordId" @callbackMethod="amendantRecordShow = false"></order-modification-details>
        </el-drawer>
    </div>
</template>

<script>
import OrderDetails from '@/views/orderComponents/OrderDetails';
import TemperatureRecording from '@/views/orderComponents/TemperatureRecording.vue';
import OrderTransactionDetails from '@/views/orderComponents/OrderTransactionDetails';
import orderManagement from '@/api/logisticsManagement/orderManagement.js'; // 订单管理
import OrderModificationDetails from '@/views/orderComponents/OrderModificationDetails';
import TransportationRecords from '@/views/logisticsManagement/orderManagement/TransportationRecords.vue';
import moment from 'moment';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';

export default {
    name: 'OrderDetailTrans',
    components: { TransportationRecords, OrderModificationDetails, OrderDetails, OrderTransactionDetails, TemperatureRecording, ArrowDown, ArrowUp },
    props: {
        handleReceiptRemove: {
            type: Function,
            default: () => {}
        },
        handleSignRemove: {
            type: Function,
            default: () => {}
        },
        orderInfo: {
            required: true,
            type: Object
        },
        parentData: {
            type: Object,
            default: () => ({})
        },
        receiptUploadChange: {
            type: Function,
            default: () => {}
        },
        signUploadChange: {
            type: Function,
            default: () => {}
        },
        source: {
            type: String,
            default: '1'
        },
        timeFormatting: {
            type: Function,
            default: (val) => {
                // 检查val是否为undefined、null或空字符串
                if (!val) {
                    return ''; // 如果val不存在，返回空字符串
                }
                // 使用moment格式化时间
                return moment(val).format('YYYY-MM-DD HH:mm:ss') || '';
            }
        }
    },
    data() {
        return {
            type: 1,
            activities: [], // 订单轨迹
            addServiceList: [], //新增订单增值服务
            // 排序方向
            reverse: true,
            // 异动类型
            fourplOrderChangeTypeOptions: [],
            dialogImageUrl: '',
            dialogVisible: false,
            options: {
                zIndex: 1
            },
            amendantRecordShow: false,
            transRecordId: null,
            auditParam: {},
            signPicList: [],
            receiptPicList: []
        };
    },
    async created() {
        this.type = 1;
        // 异动类型4PL
        this.fourplOrderChangeTypeOptions = await this.getDictList('fourpl_order_change_type');
    },
    /**
     *
     */
    methods: {
        /**
         * 切换显示隐藏
         */
        toggleVisibility(index) {
            // 切换指定 activity 的 isVisible 状态
            this.activities[index].isVisible = !this.activities[index].isVisible;
        },
        addServiceListCheckBox() {
            let formServices = [];
            this.addServiceList.forEach((val) => {
                let obj = {};
                obj.type = val.dictValue;
                obj.content = val.defaultContent || '是';
                formServices.push(obj);
            });
            this.form.addedServices = formServices;
        },
        // 标准时间格式化
        formatDate(cellValue) {
            return formatDate(cellValue);
        },
        // 格式化付款方式
        formatThePaymentMethod(val) {
            return this.selectDictLabel(this.fourplPaymentMethodOptions, val);
        },
        // 格式化揽收方式
        formatTheWayTheGoodsAreCollected(val) {
            return this.selectDictLabel(this.collectionMethod, val);
        },
        /** 4PL字典转换 */
        fourplFormat(data, val) {
            return this.selectDictLabel(data, val);
        },
        // 多选字典翻译
        getDictVals(data, vals) {
            var actions = [];
            if (Array.isArray(vals)) {
                vals.forEach((item) => {
                    let index = data.findIndex((t) => t.value == '' + item);
                    if (index > -1) {
                        actions.push(data[index].name);
                    }
                });
            } else {
                let index = data.findIndex((t) => t.value == '' + vals);
                if (index > -1) {
                    actions.push(data[index].name);
                }
            }
            return actions.join(',');
        },
        submitForm() {},
        // 点击tabs
        tabClick(label) {
            switch (label) {
                case '1':
                    // this.getTransDetail();
                    break;
                case '2':
                    break;
                case '3':
                    break;
                case '4':
                    // 获取订单轨迹
                    this.trajectoryViewClick();
                    break;
                case '5':
                    break;
                default:
                    break;
            }
        },
        /**
         * 查看详情
         * @param row
         */
        toDetail(row) {
            this.transRecordId = row.id;
            this.amendantRecordShow = true;
        },
        // 获取订单轨迹
        trajectoryViewClick() {
            const { transOrderNo } = this.orderInfo;
            if (!transOrderNo) {
                this.msgError('此订单没有运输轨迹');
                return false;
            }
            orderManagement.getTransRecord({ transOrderNo }).then((response) => {
                if (response.code == 200 && response.data.records) {
                    this.activities = response.data.records;
                }
            });
        },
        // 查看温度记录打印数据
        viewDevicePrintData(row) {
            this.printRecord.params = JSON.parse(row.printParams);
            this.printRecord.data = JSON.parse(row.printData);
            this.printRecordReturnDataViewShow = true;
        },
        /**
         * 解析 JSON 字符串为对象
         * @param remark
         * @returns {any|*[]}
         */
        parseRemark(remark) {
            // 解析 JSON 字符串为对象
            try {
                return JSON.parse(remark);
            } catch (e) {
                console.error('Invalid JSON format in remark:', remark);
                return [];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    thead th {
        border-right: none !important;
    }

    .orderDetails {
        .el-card__body {
            padding-bottom: 15px;
        }
    }
}

.mb16 {
    margin-bottom: 16px;
}

.p-16 {
    padding: 16px;
}

.orderDetails {
    .heading {
        color: #999999;
    }

    .heading-value {
        color: #707070;
    }

    .el-card__body {
        padding-bottom: 15px;
    }

    .box__flex__2 {
        display: flex;
        align-items: center;
        gap: 50px;
        font-size: 14px;
        > div {
            :nth-child(1) {
                color: #999999;
            }

            :nth-child(2) {
                color: #666666;
            }
        }
    }
}

.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}

.box-orderInformation {
    // display: grid;
    // grid-template-columns: 1fr 1fr;
    // gap: 16px;
    .d-box-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        font-size: 14px;

        .d-box-column {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            .d-box-info {
                display: flex;
                justify-content: space-between;

                :nth-child(1) {
                    flex-shrink: 0;
                    flex-grow: 0;
                    text-align: left;
                    margin-right: 10px;
                    color: #999999;
                    width: 90px;
                }

                :nth-child(2) {
                    color: #333333;
                }
            }
        }

        .d-box-column:first-child {
            border-right: 1px solid #e6ebf5;
            padding-right: 10px;
        }
        .d-box-column:last-child {
            padding-left: 10px;
        }
    }
}

.box {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-column-gap: 8px;
    grid-row-gap: 8px;
    justify-items: stretch;
    align-items: stretch;
}
.box__two__columns {
    //display: grid;
    //grid-template-columns: 1fr 1fr;
    //gap: 16px;
    //margin-top: 16px;
    .d-box-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        font-size: 14px;

        .d-box-column {
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex: 1;

            > .d-box-info {
                display: flex;
                justify-content: space-between;

                > :nth-child(1) {
                    flex-shrink: 0;
                    flex-grow: 0;
                    text-align: left;
                    margin-right: 10px;
                    color: #999999;
                }

                a:nth-child(2) {
                    color: #333333;
                }
            }
        }

        .d-box-column:first-child {
            border-right: 1px solid #e6ebf5;
            padding-right: 10px;
        }
        .d-box-column:last-child {
            padding-left: 10px;
        }
    }
}
.item-line {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    .item-label {
        color: #aaaaaa;
        width: 80px;
        text-align: right;
        margin-right: 15px;
        flex-shrink: 0;
        flex-wrap: 0;
    }
    .item-content {
        color: #333333;
        .img-list {
            display: flex;
            .img {
                width: 128px;
                height: 128px;
                border: 1px dashed #d9d9d9;
                margin-top: 0;
                margin-left: 10px;
                img {
                    width: 108px;
                    height: 108px;
                    margin: 10px;
                }
            }
        }
    }
}
.box__locus .el-timeline {
    padding-top: 20px;
}
.cost__card {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    ::v-deep .el-card__header {
        background: #21292508;
    }
}
.cost__card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.card__header__num {
    display: flex;
    align-items: baseline;
    gap: 2px;
    color: #5670fe;
}
.card__header__num__label {
    font-size: 12px;
}
.card__header__num__value {
    font-size: 18px;
}
.cost__box {
    display: flex;
    gap: 20px;
    .cost__item {
        display: flex;
        flex-direction: column;
        gap: 5px;
        .cost__item__label {
            font-size: 12px;
            color: #999999;
        }
    }
}
.cost__card__estimate {
    display: flex;
    gap: 15px;
    font-size: 18px;
    align-items: baseline;
    .cost__card__estimate__main {
        display: flex;
        align-items: baseline;
        gap: 2px;
        color: #ff2a2a;
        .cost__card__estimate__main__label {
            font-size: 18px;
        }
        .cost__card__estimate__main__value {
            font-size: 22px;
        }
    }
}
::v-deep {
    .three_party .el-timeline-item__node--normal {
        left: 0;
    }
    .three_party .el-timeline-item {
        padding-bottom: 10px;
    }
    // 最后一个 .el-timeline-item padding-bottom: 0;
    .three_party .el-timeline-item:last-child {
        padding-bottom: 0;
    }
    .three_party .el-timeline {
        padding-top: 0;
    }
}

.three_party {
    border: 1px solid #2a76f8;
    padding: 10px 10px 5px 10px;
    margin-top: 5px;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease;
}
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
    opacity: 0;
}
</style>
