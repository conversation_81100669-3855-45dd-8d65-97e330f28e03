<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" ref="searchCard" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" @submit.prevent>
                <el-form-item label="订单号" prop="orderNo" style="width: 250px">
                    <el-input v-model="queryForm.orderNo" clearable placeholder="请输入订单号" @clear="handleChangesTheOrderType" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="运单号" prop="transOrderNo" style="width: 250px">
                    <el-input v-model="queryForm.transOrderNo" clearable placeholder="请输入运单号" @clear="handleChangesTheOrderType" @keyup.enter="handleChangesTheOrderType"></el-input>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="承运商名称" prop="carrierName" style="width: 250px">
                    <el-select v-model="queryForm.carrierId" clearable filterable placeholder="请选择承运商名称" @change="handleChangesTheOrderType">
                        <el-option v-for="item in carrierList" :key="item.carrierId" :label="item.carrierName" :value="item.carrierId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="运输类型" prop="transType" style="width: 250px">
                    <el-select v-model="queryForm.transType" clearable placeholder="请选择产品类型" @change="handleChangesTheOrderType">
                        <el-option v-for="item in productTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="产品分类" prop="productType">
                    <el-select v-model="queryForm.productType" clearable placeholder="请选择产品分类" @change="handleChangesTheOrderType">
                        <el-option v-for="item in goodsTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="温区类型" prop="temperatureType">
                    <el-select v-model="queryForm.temperatureType" clearable placeholder="请选择温区类型" @change="handleChangesTheOrderType">
                        <el-option v-for="item in temperatureTypeDicts" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="isShowAll" label="对账状态" prop="status">
                    <el-select v-model="queryForm.status" clearable placeholder="请选择对账状态" @change="handleChangesTheOrderType">
                        <el-option v-for="item in expenseOrderStatus" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="账单月度" prop="billDate" style="width: 250px">
                    <el-date-picker v-model="queryForm.billDate" class="w-full" placeholder="选择账单月度" type="month" value-format="YYYY-MM" @change="handleChangesTheOrderType"></el-date-picker>
                </el-form-item>
                <search-button :is-show-all="isShowAll" :loading="loading" @handleQuery="handleChangesTheOrderType" @resetQuery="resetQuery" @showAllClick="showAllClick" />
            </el-form>
        </el-card>

        <!--  /统计行  -->
        <el-card :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <div class="d__flex__statisticsRows">
                <el-statistic :precision="2" :value="overview.totalAmount" :value-style="{ color: '#5670FE' }" group-separator="," title="总账单金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.checkedAmount" :value-style="{ color: '#1ACD7E' }" group-separator="," title="已核对账单金额"></el-statistic>
                <el-statistic :precision="2" :value="overview.toBeCheckedAmount" :value-style="{ color: '#F4AC00' }" group-separator="," title="待核对账单金额"></el-statistic>
            </div>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button :disabled="multiple" type="primary" @click="handleClickBulkChecks">批量核对</el-button>
                <el-button :disabled="orderList.length === 0" type="primary" @click="exportAll">全部导出</el-button>
                <right-toolbar v-model:columns="columns" v-model:show-search="showSearch" table-i-d="reconciliationDocumentOwnerTable" @queryTable="getList" />
            </div>
            <column-table v-loading="loading" :columns="columns" :data="orderList" :maxHeight="tableHeight" :showCheckBox="true" @selection-change="handleSelectionChange">
                <template #orderDate="{ row }">
                    <span>{{ row.orderDate }}</span>
                </template>
                <template #temperatureType="{ row }">
                    <span>{{ formatDictionaryData('temperatureTypeDicts', row.temperatureType) }}</span>
                </template>
                <template #settlementMethod="{ row }">
                    <span>{{ formatDictionaryData('settlementMethodList', row.settlementMethod) }}</span>
                </template>
                <template #type="{ row }">
                    <span>{{ formatDictionaryData('settlementManagementOrderTypeList', row.type) }}</span>
                </template>
                <template #transType="{ row }">
                    <span>{{ formatDictionaryData('productTypeList', row.transType) }}</span>
                </template>
                <template #productType="{ row }">
                    <span>{{ formatDictionaryData('goodsTypeList', row.productType) }}</span>
                </template>
                <template #orderStatus="{ row }">
                    <span>{{ formatDictionaryData('orderStatusList', row.orderStatus) }}</span>
                </template>
                <template #status="{ row }">
                    <div v-html="formatStatus(row.status)"></div>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickFeeDetails(row)">费用详情</el-button>
                </template>
            </column-table>
            <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" @pagination="getList" />
        </el-card>

        <!--  /订单费用详情  -->
        <order-fee-details-with-details v-if="orderCostVisible" v-model="orderCostVisible" :detail-data="detailData" :fee-breakdown-data="feeBreakdownData" />

        <!--  /订单明细  -->
        <el-drawer v-model="orderDetailsVisible" size="900px" title="订单明细" @close="hideOrderDetails">
            <div v-loading="orderDetailsLoading" :element-loading-text="orderDetailsLoadingText" class="mb16" style="background-color: #f2f2f2"></div>
        </el-drawer>
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import accountStatement from '@/api/shipperEnd/accountStatement';
import RightToolbar from '@/components/RightToolbar/index.vue';
import {selectDictLabel} from '@/utils/dictLabel';
import customerStatement from '@/api/carrierEnd/customerStatement';
import OrderFeeDetailsWithDetails from '@/views/carrierFunction/OrderFeeDetailsWithDetails.vue';
import moment from 'moment/moment';
import { downloadNoData } from '@/utils';

export default {
    name: 'AccountStatement',
    components: {
        OrderFeeDetailsWithDetails,
        RightToolbar,
        SearchButton,
        ColumnTable
    },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                orderNo: undefined,
                transOrderNo: undefined,
                carrierName: undefined,
                orderTypes: undefined,
                pricingTypes: undefined,
                goodsTypes: undefined,
                temperatureTypes: undefined,
                orderStatus: undefined,
                billDate: undefined
            },
            carrierList: [],
            expenseOrderStatus: [],
            total: 0,
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            loading: false,
            columns: [
                { title: '订单号', key: 'orderNo', align: 'center', minWidth: '140px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '运单号', key: 'transOrderNo', align: 'center', minWidth: '140px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '下单时间', key: 'orderDate', align: 'center', width: '150px', columnShow: true, fixed: 'left', showOverflowTooltip: true },
                { title: '收件公司', key: 'receiverCompany', align: 'center', minWidth: '130px', columnShow: true, showOverflowTooltip: true },
                { title: '月度', key: 'billDate', align: 'center', width: '120px', columnShow: true, showOverflowTooltip: true },
                { title: '发件地址', key: 'sendAddress', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '目的地址', key: 'receiverAddress', align: 'center', minWidth: '180px', columnShow: true, showOverflowTooltip: true },
                { title: '件数', key: 'goodsPackages', align: 'center', width: '80px', columnShow: true, showOverflowTooltip: true },
                { title: '温区类型', key: 'temperatureType', align: 'center', width: '80px', columnShow: true, showOverflowTooltip: true },
                { title: '付款方式', key: 'settlementMethod', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '揽收费用', key: 'collectCost', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '配送费用', key: 'deliveryCost', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '增值服务总费用', key: 'addedServicesCost', align: 'center', width: '110px', columnShow: true, showOverflowTooltip: true },
                { title: '超区费用', key: 'exceedCountyCost', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '合同价金额', key: 'totalContractPrice', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '折扣金额', key: 'discountCost', align: 'center', width: '100px', columnShow: true, showOverflowTooltip: true },
                { title: '应付金额', key: 'totalReceivableCost', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '订单状态', key: 'orderStatus', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '对账状态', key: 'status', align: 'center', width: '100px', columnShow: true, fixed: 'right', showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '140px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            orderList: [],
            orderCostVisible: false,
            orderDetailsVisible: false,
            orderDetailsLoading: false,
            orderDetailsLoadingText: '加载中...',
            detailData: {},
            feeBreakdownData: [],
            expenseAccountList: [],
            productTypeList: [],
            settlementMethodList: [],
            isShowAll: false,
            overview: {
                totalAmount: 0,
                checkedAmount: 0,
                toBeCheckedAmount: 0
            },
            settlementManagementOrderTypeList: [],
            shortcuts: [
                {
                    text: '无',
                    value: () => {
                        return [null, null];
                    }
                },
                {
                    text: '当天',
                    value: () => {
                        let now = moment(new Date()).format('YYYY-MM-DD');
                        return [now, now];
                    }
                },
                {
                    text: '7天',
                    value: () => {
                        let start = moment(new Date()).subtract(7, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                },
                {
                    text: '30天',
                    value: () => {
                        let start = moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD');
                        let end = moment(new Date()).format('YYYY-MM-DD');
                        return [start, end];
                    }
                }
            ],
            orderStatusList: [],
            tableHeight: 600
        };
    },
    computed: {
        // 格式化对账状态
        formatStatus() {
            return (value) => {
                const statusText = selectDictLabel(this.expenseOrderStatus, value);
                if (value === '0') {
                    return `<span style="color: #B1B1B1">${statusText}</span>`;
                } else if (value === '1') {
                    return `<span style="color: #F4AC00">${statusText}</span>`;
                } else if (value === '2') {
                    return `<span style="color: #5670FE">${statusText}</span>`;
                } else if (value === '3') {
                    return `<span style="color: #1ACD7E">${statusText}</span>`;
                } else {
                    return statusText;
                }
            };
        },
        /**
         * 格式化字典数据
         * @returns {function(*, *): string}
         */
        formatDictionaryData() {
            return (dictionaryName, value) => {
                const dictionary = this[dictionaryName] || {};
                return selectDictLabel(dictionary, value) || value || '-';
            };
        }
    },
    created() {
        this.getDict();
        this.queryForm.billDate = moment(new Date()).subtract(1, 'months').format('YYYY-MM');
        this.handleChangesTheOrderType();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            this.expenseOrderStatus = await this.getDictList('expense_order_status');
            this.valuationTypeList = await this.getDictList('fourpl_product_type');
            this.goodsTypeList = await this.getDictList('fourpl_product_class');
            this.temperatureTypeDicts = await this.getDictList('fourpl_temperature_type');
            this.expenseAccountList = await this.getDictList('cost_order_type');
            this.productTypeList = await this.getDictList('fourpl_product_type');
            this.settlementMethodList = await this.getDictList('fourpl_payment_method');
            this.settlementManagementOrderTypeList = await this.getDictList('cost_settlement_management_order_type');
            this.orderStatusList = await this.getDictList('fourpl_order_status');
        },
        getList() {
            this.loading = true;
			// eslint-disable-next-line no-unused-vars
            const { sendAddress, receiverAddress, ...params } = this.queryForm;

            // 列表数据使用完整参数
            const listPromise = accountStatement.statementListOwner(params);

            // 统计数据去除分页参数
			// eslint-disable-next-line no-unused-vars
            const { current, size, ...costParams } = params;
            const costPromise = accountStatement.statementListOwnerCost(costParams);

            Promise.all([listPromise, costPromise])
                .then(([listRes, costRes]) => {
                    if (listRes.code === 200) {
                        this.tableHeight = window.innerHeight - this.$refs.searchCard.$el.offsetHeight - 300;
                        this.orderList = listRes.data.records || [];
                        this.total = listRes.data.total || 0;
                    }

                    if (costRes.code === 200) {
                        const { checkedAmount, toBeCheckedAmount, totalAmount } = costRes.data;
                        this.overview.checkedAmount = checkedAmount * 1 || 0.0;
                        this.overview.toBeCheckedAmount = toBeCheckedAmount * 1 || 0.0;
                        this.overview.totalAmount = totalAmount * 1 || 0.0;
                    }
                })
                .catch(() => {
                    // 错误处理
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 批量核对
        handleClickBulkChecks() {
            this.$confirm('是否确认核对？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    // 遍历 this.selectData id 赋值为 idList
                    let idList = [];
                    this.selectData.forEach((item) => {
                        idList.push(item.id);
                    });
                    const loading = this.$loading({
                        lock: true,
                        text: '核对中...',
                        spinner: 'el-icon-loading'
                    });
                    accountStatement
                        .batchCheck(idList)
                        .then((res) => {
                            if (res.code === 200) {
                                if (res.data.successNum) {
                                    this.$message({
                                        type: 'success',
                                        message: res.data.successNum
                                    });
                                }
                                if (res.data.errorNum) {
                                    this.$message({
                                        type: 'error',
                                        message: res.data.errorNum,
                                        offset: 80
                                    });
                                }
                            }
                            this.getList();
                        })
                        .catch(() => {})
                        .finally(() => {
                            loading.close();
                        });
                })
                .catch(() => {});
        },
        // 查看费用详情
        handleClickFeeDetails(data) {
            const { id } = data;
            customerStatement.statementDetail({ id }).then((res) => {
                if (res.code === 200) {
                    this.detailData = res.data;
                    this.orderCostVisible = true;
                    const { statementAddedServiceList, statementCostList } = res.data;
                    let addedServicesFilter = [];
                    if (statementAddedServiceList) {
                        // statementAddedServiceList addedServicesContractPrice 变为 costContractPrice, addedServicesCost 变为 costData,addedServicesName 变为 costType 赋值给 statementAddedServiceList
                        // 使用 for of 循环遍历 statementAddedServiceList
                        for (const item of statementAddedServiceList) {
                            item.costContractPrice = item.addedServicesContractPrice;
                            item.costData = item.addedServicesCost;
                            item.costType = item.addedServicesName;
                        }
                        // 去除 statementAddedServiceList 中 costData 和 costContractPrice 都为 '0' 的数据 其中一个为 '0' 的数据不去除
                        // 赋值给 addedServicesFilter
                        addedServicesFilter = statementAddedServiceList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
                    }
                    let costListFilter = [];
                    if (statementCostList) {
                        // 筛选 statementCostList 中 costData 和 costContractPrice 都不等于0的数据
                        costListFilter = statementCostList.filter((item) => item.costData != 0 || item.costContractPrice != 0);
                        costListFilter.sort((a, b) => {
                            return a.costType - b.costType;
                        });
                    }
                    // statementAddedServiceList 为 null 解构赋值给 this.feeBreakdownData []
                    this.feeBreakdownData = [...(addedServicesFilter || []), ...(costListFilter || [])];
                }
            });
        },
        handleQuery() {
            this.queryForm.current = 1;
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            // 遍历 selection中的 id status 赋值给 this.selectData
            this.selectData = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        hideOrderDetails() {
            this.orderDetailsVisible = false;
        },
        resetQuery(formName) {
            this.$refs[formName].resetFields();
            this.handleChangesTheOrderType();
        },
        /**
         * 展开折叠
         */
        showAllClick() {
            this.isShowAll = !this.isShowAll;
        },
        /**
         * 全部导出
         */
        exportAll() {
			// eslint-disable-next-line no-unused-vars
            const { sendAddress, receiverAddress, ...params } = this.queryForm;
            accountStatement
                .exportStatementOwner({ filename: '对账单.xls', ...params }, '', '', 'blob')
                .then((res) => {
                    downloadNoData(res, 'application/vnd.ms-excel', '对账单.xlsx');
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         * 改变订单类型
         */
        handleChangesTheOrderType() {
            this.loading = true;
            this.handleQuery();
            // eslint-disable-next-line no-unused-vars
            const { sendAddress, receiverAddress, ...params } = this.queryForm;
            accountStatement
                .getCarrierList({ ...params })
                .then((res) => {
                    if (res.code === 200 && res.data) {
                        this.carrierList = res.data || [];
                    } else {
                        this.carrierList = [];
                        this.queryForm.carrierId = null;
                    }
                })
                .catch(() => {
                    this.carrierList = [];
                    this.queryForm.carrierId = null;
                })
                .finally(() => {
                    this.getList();
                    this.loading = false;
                });
        }
    }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
    margin-bottom: 20px;
}
:deep(.el-dialog__header) {
    padding-bottom: 20px;
}
.form-mb0 .el-form-item {
    margin-bottom: 4px;
    margin-top: 4px;
}

.box-search {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.box-period {
    height: calc(100vh - 154px);
    :deep(.el-scrollbar) {
        height: 100%;
    }

    :deep(.el-scrollbar__wrap) {
        width: 102%;
    }
}
.orderCostDialog {
    :deep(.el-dialog__body) {
        padding: 0;
    }
}
.titleLayout {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .verticalBar {
        display: inline-block;
        background-color: #5670fe;
        width: 3px;
        height: 1em;
        margin-right: 8px;
    }

    .title {
        color: #5670fe;
    }
}
.d__flex__statisticsRows {
    display: flex;
    justify-content: space-around;
}
.el-statistic {
    display: flex;
    align-items: baseline;
    margin-right: 20px;
    gap: 10px;
}
</style>
