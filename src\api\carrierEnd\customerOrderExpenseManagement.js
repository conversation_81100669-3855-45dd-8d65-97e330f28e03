import request from '@/utils/request';

export default {
    // 列表
    listOrderCost: function (params) {
        return request.get('/cost/order/costDetailed/list', params);
    },
    // 费用调整前置数据查询
    getOrderCostAdjustment: function (params) {
        return request.post('/cost/order/costDetailed/queryByIdList', params);
    },
    // 订单费用调整
    orderExpenseAdjustment: function (params) {
        return request.post('/cost/order/costDetailed/costAdjust', params);
    },
    // 费用计算
    calculateContractCost: function (params) {
        return request.post('/cost/formula/evelCost', params);
    },
    // 费用异动
    saveOrderCostChange: function (params) {
        return request.post('/cost/order/costDetailed/costChange', params);
    },
    // 费用详情
    getOrderCostDetail: function (params) {
        return request.get('/cost/order/costDetailed/getDetailById', params);
    },
    // 生成对账单
    createStatement: function (params) {
        return request.post('/cost/order/costDetailed/generateStatement', params);
    },
    // 导出
    exportOrderCost: function (params) {
        return request.get('/cost/order/costDetailed/export', params);
    },
    // 导出记录
    exportRecords: function (params) {
        return request.get('/cost/order/costExportRecord/list', params);
    },
    // 下载导出文件
    downloadExportRecords: function (params, config, resDetail, responseType) {
        return request.get('/file/preview', params, config, resDetail, responseType);
    },
    // 费用统计
    costStatistics: function (params) {
        return request.get('/cost/order/costDetailed/costStats', params);
    },
    // 根据查询条件全部生成对账单
    createStatementAll: function (params) {
        return request.get('/cost/order/costDetailed/all/gen/statement', params);
    }
};
