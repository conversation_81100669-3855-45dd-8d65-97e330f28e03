<!--
 * @Author: 赵克强 <EMAIL>
 * @Date: 2023-05-18 10:06:52
 * @LastEditors: 赵克强 <EMAIL>
 * @LastEditTime: 2023-08-21 11:35:30
 * @FilePath: \zhixing-heyue-erp-front-end-pc\src\views\assist\purchasingManagement\component\fineSingle.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="app-container">
        <!-- <el-row :gutter="10" class="mb8" style="display: flex;justify-content: end;"> -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" @click="() => handleAdd()" :disabled="modalType == 'detail'">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" @click="() => handleDelete()" :disabled="modalType == 'detail' || !chooseList.length">删除</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="loading" :data="list" border style="margin-top: 30px;" size="small"
            @selection-change="handleChangeSelection">
            <el-table-column type="selection" min-width="55" align="center" />
            <el-table-column min-width="80" align="center" label="序号" fixed="left">
                <template #default="scope">
                    {{ scope.$index + 1 }}
                </template>
            </el-table-column>
            <el-table-column label="商品名称" align="center" prop="tradeName" :show-overflow-tooltip="true" min-width="140"
                fixed="left" />
            <el-table-column label="数量" align="center" :show-overflow-tooltip="true" min-width="140" fixed="right">
                <template #default="scope">
                    <div v-clickOutside="() => handleClickOutside(scope, 'isShowquantity')">
                        <p v-show="!scope.row.isShowquantity"
                            style="height: 42px;line-height:42px;color: red;font-size: 16px;"
                            @click="handleInputEdit(scope, 'quantity')">
                            {{ Tofixed_(scope.row.quantity, 0) || '请输入数量' }}</p>
                        <el-input-number v-model="scope.row.quantity" placeholder="数量" v-show="scope.row.isShowquantity"
                            :min="0" :max="10000" :precision="0" style="width: 100%;height: 30px" clearable />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="单价" align="center" :show-overflow-tooltip="true" min-width="140" fixed="right">
                <template #default="scope">
                    <div v-clickOutside="() => handleClickOutside(scope, 'isShowunitPrice')">
                        <p v-show="!scope.row.isShowunitPrice"
                            style="height: 42px;line-height:42px;color: red;font-size: 16px;"
                            @click="handleInputEdit(scope, 'unitPrice')">
                            {{ Tofixed_(scope.row.unitPrice, 2) || '请输入单价' }}</p>
                        <el-input-number v-model="scope.row.unitPrice" placeholder="单价"
                            v-show="scope.row.isShowunitPrice" :min="0" :precision="2" style="width: 100%;height: 30px;" clearable />
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="细单合计金额" prop="amountMoney" :show-overflow-tooltip="true" align="center" min-width="100" fixed="right">
                <template #default="scope">
                    <span  style="height: 42px;line-height:42px;color: red;font-size: 16px;">{{ (Number(scope.row.quantity || 0) * Number(scope.row.unitPrice || 0)).toFixed(2) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="到货数量" prop="arrivalQuantity" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="合格收货数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="入库数量" prop="receivingQuantity" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="商品编码" prop="commodityCode" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="商品自编码" prop="commoditySelfCode" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="类型" prop="commodityType"  :formatter="(row) => formDict(productType, row.commodityType)" :show-overflow-tooltip="true" align="center" min-width="140" />
            <el-table-column label="生产厂家" prop="manufacture.enterpriseName" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="产地" prop="originPlace" :show-overflow-tooltip="true" align="center" min-width="140" />
            <el-table-column label="规格" prop="packageSpecification" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="税率" prop="taxRate" :show-overflow-tooltip="true" align="center" min-width="140" />
            <el-table-column label="件装量" prop="ratio" :show-overflow-tooltip="true" align="center" min-width="140" />
                
            <el-table-column label="件数" prop="pieceNumber" :show-overflow-tooltip="true" align="center" min-width="140" >
                <template #default="scope">
                    <span>{{String(Math.floor(((scope.row.quantity ? Number(scope.row.quantity || 0) : 0) / (scope.row.ratio ? Number(scope.row.ratio || 0) : 1))))  + (scope.row.completeUnit|| '箱') + (((scope.row.quantity ? Number(scope.row.quantity || 0) : 0) % (scope.row.ratio ? Number(scope.row.ratio || 0) : 1)) ? (((scope.row.quantity ? Number(scope.row.quantity || 0) : 0) % (scope.row.ratio ? Number(scope.row.ratio || 0) : 1))  + (scope.row.basicUnit)) : '')  }}</span>
                </template>
            </el-table-column>
            <el-table-column label="基本单位" prop="basicUnit" :show-overflow-tooltip="true" align="center" min-width="140" />
            <el-table-column label="整件单位" prop="completeUnit" :show-overflow-tooltip="true" align="center" min-width="140" />
            <el-table-column label="剂型" prop="dosageForm" :show-overflow-tooltip="true" align="center" min-width="140" />
            <el-table-column label="生产日期" prop="produceDate" :show-overflow-tooltip="true" align="center"
                min-width="140" :formatter="row=>row.produceDate ? moment(row.produceDate).format('YYYY-MM-DD') : '--'"/>
            <!-- TODO -->
            <el-table-column label="过期日期" prop="noticeContent" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="有效期" prop="validityTime" :show-overflow-tooltip="true" align="center" min-width="140" />
            <el-table-column label="贮藏温区" prop="storageTemperature" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="库存余量" prop="availableInventory" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="上次购价" prop="lastDifferencePrice" :show-overflow-tooltip="true" align="center"
                min-width="140" />
            <el-table-column label="上次供应商" prop="lastSupplier" :show-overflow-tooltip="true" align="center"
                min-width="140" />
        </el-table>
        <shopModal :visible="visible" :beforeClose="beforeClose" :chooseListAfter="list" ref="shopValue" v-if="visible"
            :form="form" />
    </div>
</template>
 
<script setup >
import { reactive, ref, getCurrentInstance, toRefs, defineProps, watch, defineExpose, onMounted } from 'vue'
import shopModal from './shopModal.vue';
import { ClickOutside as vClickOutside } from 'element-plus'
import purchasingManagement from '@/api/erp/purchasingManagement'
import moment from 'moment'
const { proxy } = getCurrentInstance();
const list = ref([]);
const loading = ref(false);
const reviewStatus = ref([])
const settlementMethod = ref([])
const visible = ref(false)
const chooseList = ref([])
const shopValue = ref(null)
const num = ref(0)
const totalCost = ref(0.00)
const discountTotalCost = ref(0.00)
const timer = ref(null)
const productType = ref([])
const data = reactive({
    rules: {},
});
const listRegIndex = ref(0)
const props = defineProps({
    modalType: {
        type: String,
        default: ""
    },
    form: {
        type: Object,
        default: () => { { } }
    },
    detailedOrderInformation: {
        type: Object,
        default: () => { { } }
    }
})
const { rules } = toRefs(data);
const { modalType, form, detailedOrderInformation } = toRefs(props);
const listReg = () => {
    listRegIndex.value = 0
   for(var [index,item] of list.value?.entries()){
    if(!item?.quantity || !item?.unitPrice){
         proxy.msgError(`请输入序号为${index+1}的数量或单价！`)
        break 
    }
    if(item?.quantity && item?.unitPrice){
        listRegIndex.value++
        console.log(listRegIndex.value);
    }
   }
}
const reset = () => {
    list.value = []
    num.value = []
    totalCost.value = []
    discountTotalCost.value = []
}
const Tofixed_ = (value, num) => {
    if (value) {
        return parseFloat(value)?.toFixed(num)
    } else {
        return false
    }
}
const formDict = (data, val) => {
    return proxy.selectDictLabel(data, val)
}
watch(() => visible.value, (newValue, oldValue) => {
    if (!visible.value && shopValue.value.type) {
        if(list.value?.length>=1000){
            return proxy.msgError('单次最多1000件商品！')
        }
        list.value = [...list.value,...shopValue.value.chooseList]
    }
})
watch(() => [modalType.value, detailedOrderInformation.value], (newValue, oldValue) => {
    if (modalType.value == 'edit' || modalType.value == 'detail' || modalType.value == 'draft') {
        if (detailedOrderInformation.value) {
            list.value = detailedOrderInformation.value
        }

    }
}, { deep: true, immediate: true })
watch(() => [list.value, form.value], (newValue, oldValue) => {
    if (timer.value) {
        clearTimeout(timer.value)
    }
    timer.value = setTimeout(() => {
        num.value = 0
        totalCost.value = 0
        discountTotalCost.value = 0
        list.value.forEach(value => {
            num.value += isNaN(Number(value.quantity || 0)) ? 0 : Number(value.quantity || 0)
            totalCost.value += (isNaN(Number(value.quantity || 0)) ? 0 : Number(value.quantity || 0)) * (isNaN(Number(value.unitPrice || 0)) ? 0 : Number(value.unitPrice || 0))
            discountTotalCost.value += (isNaN(Number(value.quantity || 0)) ? 0 : Number(value.quantity || 0)) * (isNaN(Number(value.unitPrice || 0)) ? 0 : Number(value.unitPrice || 0))
        })
        discountTotalCost.value = discountTotalCost.value - (isNaN(Number(form.value?.discountAmount || 0)) ? 0 : Number(form.value?.discountAmount || 0))
        if (totalCost.value && discountTotalCost.value && discountTotalCost.value < 0) {
            proxy.msgError('折扣金额不能大于总金额！')
        }
    }, 500)
}, { deep: true, immediate: true })
const handleClickOutside = (scope, key) => {
    scope.row[key] = false
}
const handleInputEdit = (scope, type) => {
    if (modalType.value == 'detail') return
    scope.row[`isShow${type}`] = true
}

/**
 * @description: 选中的数据
 * @return {*}
 */
const handleChangeSelection = key => {
    chooseList.value = key
}
/**
 * @description: 关闭商品搜索弹窗
 * @return {*}
 */
const beforeClose = () => {
    visible.value = false
}
/**
 * @description: 搜索
 * @return {*}
 */
const handleAdd = () => {
    if (!form.value?.supplier) {
        return proxy.msgError('请先选择供应商！')
    }else {
        visible.value = true
    }

}
/**
 * @description: 删除
 * @return {*}
 */


const handleDelete = () => {
    proxy.$confirm('是否确认删除选中数据项?', '提示', {
        type: 'warning',
        confirmButtonText: "确定",
				cancelButtonText: "取消",
    }).then(() => {
        loading.value = true
        const ids = []
        chooseList.value.forEach(item => {
            if (!item.isAdd) {
                ids.push(item.id)
            }
        })
        if (ids?.length) {
            purchasingManagement.deleteDetail({ ids: ids?.toString() }).then(res => {
                if (res.code == 200) {
                    loading.value = false
                    proxy.msgSuccess('删除成功')
                    list.value = list.value.filter((item) => !(chooseList.value.some((ele) => ele.id === item.id)));
                } else {
                    loading.value = false
                    proxy.msgError(res.msg)
                }
            }).finally(() => {
                loading.value = false
            })
        } else {
            list.value = list.value.filter((item) => !(chooseList.value.some((ele) => ele.id === item.id)));
            proxy.msgSuccess('删除成功')
            loading.value = false
        }


    }).catch(()=>{
        
    })
}
/**
 * @description: 字典查询
 * @return {*}
 */
async function dict() {
    reviewStatus.value = await proxy.getDictList('erp_review_status')
    settlementMethod.value = await proxy.getDictList('erp_settlement_method')
    productType.value = await proxy.getDictList('product_type') 
}
onMounted(() => {
    dict()
})
defineExpose({
    num,
    totalCost,
    discountTotalCost,
    list,
    reset,
    listReg,
    listRegIndex
})
</script>
<style lang="scss" scoped>
.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    ::v-deep .el-form-item {
        width: 22%;
    }
}

::v-deep .labelStyle {
    .el-form-item__label {
        margin-left: 10px;
    }
}
::v-deep .el-checkbox__inner {
    width: 20px !important;
    height: 20px !important;
}
::v-deep .el-checkbox.el-checkbox--small .el-checkbox__inner::after {
    width: 7px;
    height: 13px;
}
</style>