import request from '@/utils/request'
export default {
    // 获取计费参数设置列表
    billingParameterList: function (params) {
        return request.get('/cost/billing/parameters/list', params);
    },
    // 新增计费参数设置
    addBillingParameter: function (params) {
        return request.post('/cost/billing/parameters/save', params);
    },
    // 修改状态
    updateBillingParamStatus: function (params) {
        return request.get('/cost/billing/parameters/updateStatus', params);
    },
    // 查询可用的计费参数列表（不分页）
    billingParameterListByFormulaCopy: function (params) {
        return request.get('/cost/billing/parameters/usable/list', params);
    }
}
