<template>
    <div class="app-container">
        <!--  /搜索区域  -->
        <el-card v-show="showSearch" :body-style="{ padding: '10px' }" class="mb10" shadow="never">
            <el-form ref="queryForm" :inline="true" :model="queryForm" class="seache-form" label-width="auto" @submit.native.prevent>
                <el-form-item label="承运商名称" prop="carrierId">
                    <el-select v-model="queryForm.carrierId" clearable filterable placeholder="请选择承运商" style="width: 100%" @change="handleQuery">
                        <el-option v-for="item in signAdvancePaymentContract" :key="item.carrierId" :label="item.carrierName" :value="item.carrierId"></el-option>
                    </el-select>
                </el-form-item>
                <search-button :is-show-all-switch="false" @handleQuery="handleQuery" @resetQuery="resetQuery" />
            </el-form>
        </el-card>

        <!-- / 表格内容 -->
        <el-card :body-style="{ padding: '10px' }" shadow="never">
            <div class="mb10" style="display: flex; justify-content: space-between">
                <div>
                    <el-button v-if="dataList && dataList.length" type="primary" @click="handleClickExportAll">全部导出</el-button>
                    <el-button :disabled="single" type="primary" @click="onClickApplyPrepayment">预付款充值申请</el-button>
                </div>
                <right-toolbar v-model:columns="columns" v-model:showSearch="showSearch" table-i-d="CustomerPrepaymentBalance" @queryTable="getList" />
            </div>
            <column-table ref="CustomerPrepaymentBalance" v-loading="loading" :columns="columns" :data="dataList" :max-height="600" :show-index="true" :showCheckBox="true" element-loading-text="加载中..." @selection-change="handleSelectionChange">
                <template #status="{ row }">
                    <span>{{ row.status === '0' ? '正常' : '不正常' }}</span>
                </template>
                <template #createDate="{ row }">
                    <span>{{ formatDate(row.createDate) }}</span>
                </template>
                <template #latestCostTime="{ row }">
                    <span>{{ formatDate(row.latestCostTime) }}</span>
                </template>
                <template #opt="{ row }">
                    <el-button icon="el-icon-info-filled" link size="small" type="primary" @click="handleClickTransactionDetails(row)">交易明细</el-button>
                </template>
            </column-table>
            <div class="box-flex-right">
                <pagination v-show="total > 0" v-model:limit="queryForm.size" v-model:page="queryForm.current" :total="total" class="mb0" @pagination="getList" />
            </div>
        </el-card>
        <!-- 预付款充值申请 -->
        <el-drawer v-model="applyPrepaymentVisible" size="600px" title="预付款充值申请" @close="onCloseApplyPrepayment">
            <div class="p-10" style="background-color: #f2f2f2">
                <el-card shadow="never">
                    <el-form ref="applyPrepaymentForm" :model="applyPrepaymentForm" :rules="applyPrepaymentFormRules" label-width="auto">
                        <el-form-item label="付款公司" prop="companyName">
                            <el-input v-model="applyPrepaymentForm.companyName" class="w-full" clearable disabled />
                        </el-form-item>
                        <el-form-item label="预付款账号" prop="advanceAccount">
                            <span>{{ applyPrepaymentForm.advanceAccount }}</span>
                        </el-form-item>
                        <el-form-item label="收款公司" prop="receiveCompany">
                            <el-select v-model="applyPrepaymentForm.receiveCompany" class="w-full" clearable filterable placeholder="请选择收款公司">
                                <el-option v-for="dict in companyList" :key="dict.code" :label="dict.name" :value="dict.code" />
                            </el-select>
                        </el-form-item>
                        <div class="grid" style="grid-template-columns: minmax(0, 0.8fr) minmax(0, 1fr); grid-gap: 10px">
                            <el-form-item label="汇款金额" prop="remitAmount">
                                <el-input-number v-model="applyPrepaymentForm.remitAmount" :max="9999999" :min="0" :step="1" class="number__unit__element w-full" controls-position="right"></el-input-number>
                            </el-form-item>
                            <el-form-item label="汇款时间" prop="remitTime">
                                <el-date-picker v-model="applyPrepaymentForm.remitTime" class="w-full" placeholder="请选择汇款时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
                            </el-form-item>
                        </div>
                        <el-form-item label="付款凭证" prop="remitFile">
                            <el-upload
                                ref="uploadFile"
                                :action="uploadFileUrl"
                                :class="{ 'avatar__uploader__vertical__display': hideUploadCompanyLogo, 'avatar__uploader__vertical': !hideUploadCompanyLogo }"
                                :headers="headers"
                                :limit="2"
                                :on-preview="handlePreview"
                                :on-remove="fileUploadRemove"
                                :on-success="fileUploadSuccess"
                                accept="image/*"
                                list-type="picture-card"
                            >
                                <el-icon>
                                    <Plus />
                                </el-icon>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="applyPrepaymentForm.remark" class="w-full" clearable maxlength="200" placeholder="请输入备注" show-word-limit type="textarea" />
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
            <template #footer>
                <div class="flex justify-end">
                    <el-button @click="onCloseApplyPrepayment">取消</el-button>
                    <el-button type="primary" @click="onSubmitApplyPrepayment">提交</el-button>
                </div>
            </template>
        </el-drawer>

        <!-- /图片预览 -->
        <el-image-viewer v-if="dialogVisible" :initial-index="0" :url-list="dialogImageUrl" :z-index="9999" @close="imgClose" />
    </div>
</template>

<script>
import ColumnTable from '@/components/ColumnTable/index.vue';
import RightToolbar from '@/components/RightToolbar';
import OrderFeeDetailsWithDetails from '@/views/logisticsConfiguration/prepaymentFromTheShipper/CustomerPrepaymentBalanceDetail.vue';
import prepaymentFromTheShipper from '@/api/logisticsConfiguration/prepaymentFromTheShipper.js';
import orderManagement from '@/api/logisticsManagement/orderManagement.js';
import SearchButton from '@/components/searchModule/SearchButton.vue';
import tool from '@/utils/tool';
import { Close, Delete, Plus } from '@element-plus/icons-vue';
import moment from 'moment';
import paymentDoc from '@/api/shipperEnd/paymentDoc';
import customerPrepaymentBalance from '@/api/shipperEnd/customerPrepaymentBalance';

export default {
    name: 'CustomerPrepaymentBalance',
    components: { SearchButton, OrderFeeDetailsWithDetails, ColumnTable, RightToolbar, Plus, Close, Delete },
    data() {
        return {
            showSearch: true,
            queryForm: {
                current: 1,
                size: 10,
                carrierId: null
            },
            columns: [
                { title: '预付款账号', key: 'advanceCode', align: 'center', minWidth: '180px', columnShow: true, fixed: 'left' },
                { title: '承运商', key: 'carrierName', align: 'center', minWidth: '210px', columnShow: true, showOverflowTooltip: true },
                { title: '预付金额(元)', key: 'advanceAmt', align: 'center', minWidth: '140px', columnShow: true },
                { title: '返利金额(元)', key: 'rebateAmt', align: 'center', minWidth: '140px', columnShow: true },
                { title: '使用金额(元)', key: 'useAmt', align: 'center', minWidth: '140px', columnShow: true },
                { title: '当前余额(元)', key: 'balance', align: 'center', minWidth: '140px', columnShow: true },
                { title: '预支额度(元)', key: 'advanceLimit', align: 'center', minWidth: '140px', columnShow: true },
                { title: '创建日期', key: 'createDate', align: 'center', minWidth: '150px', columnShow: true },
                { title: '最新结算时间', key: 'latestCostTime', align: 'center', minWidth: '150px', columnShow: true },
                { title: '状态', key: 'status', align: 'center', minWidth: '120px', columnShow: true },
                { title: '备注', key: 'remark', align: 'center', minWidth: '210px', columnShow: true, showOverflowTooltip: true },
                { title: '操作', key: 'opt', align: 'center', width: '110px', columnShow: true, hideFilter: true, fixed: 'right', showOverflowTooltip: true }
            ],
            loading: false,
            dataList: [],
            total: 0,
            signAdvancePaymentContract: [],
            rechargeMethodList: [],
            transactionTypeList: [],
            applyPrepaymentVisible: false,
            // 预付款充值申请表单
            applyPrepaymentForm: {
                companyName: undefined,
                receiveCompany: undefined,
                remitAmount: undefined,
                remitTime: [],
                remitFile: [],
                isInvoice: '0',
                invoiceId: null
            },
            applyPrepaymentFormRules: {
                companyName: [{ required: true, message: '请选择付款公司', trigger: 'change' }],
                receiveCompany: [{ required: true, message: '请选择收款公司', trigger: 'change' }],
                remitAmount: [{ required: true, message: '请输入汇款金额', trigger: 'change' }],
                remitTime: [{ required: true, message: '请选择汇款时间', trigger: 'change' }],
                remitFile: [{ required: true, message: '请上传付款凭证', trigger: 'change' }]
            },
            headers: {
                Authorization: 'Bearer ' + tool.cookie.get('TOKEN'),
                ContentType: 'multipart/form-data',
                clientType: 'pc'
            },
            // 上传的图片服务器地址
            uploadFileUrl: process.env.VUE_APP_API_UPLOAD,
            dialogImageUrl: [],
            dialogVisible: false,
            companyList: [],
            // 选中的数据
            selectData: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,

            //websocket
            websocket: null,
            websocketUrl: 'ws://localhost:3000/ws/message/',
            //承运商id
            carrierId: null,
            //当前登录用户id
            userId: null
        };
    },
    computed: {
        hideUploadCompanyLogo() {
            return this.applyPrepaymentForm.remitFile.length >= 2;
        }
    },
    async created() {
        await this.getDict();
        // 交易类型 字典
        let rechargeMethodList = await this.getDictList('fourpl_payment_type');
        this.rechargeMethodList = rechargeMethodList.filter((item) => item.value !== '4' && item.value !== '5');
        // 交易类型 字典
        let transactionTypeList = await this.getDictList('fourpl_deal_type');
        this.transactionTypeList = transactionTypeList.filter((item) => item.value != '2' && item.value != '3');
        this.applyPrepaymentForm.remitTime = moment().format('YYYY-MM-DD HH:mm:ss');
        this.getPaymentList();
        this.getList();
    },
    methods: {
        /**
         * 获取字典数据
         */
        async getDict() {
            this.companyList = await this.getDictList('signing_company');
        },
        getList() {
            this.loading = true;
            prepaymentFromTheShipper
                .prepaymentList(this.queryForm)
                .then((res) => {
                    if (res.code === 200 && res.data.records) {
                        this.dataList = res.data.records || [];
                        this.total = res.data.total || 0;
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        /**
         *  查询预付款列表
         */
        getPaymentList() {
            orderManagement.carrierSelectList().then((res) => {
                if (res.code === 200 && res.data) {
                    this.signAdvancePaymentContract = res.data || [];
                } else {
                    this.signAdvancePaymentContract = [];
                    this.queryForm.carrierId = null;
                }
            });
        },
        /**
         * 全部导出
         */
        handleClickExportAll() {
            prepaymentFromTheShipper.advancePaymentExport({ filename: '预付款列表.xls', carrierId: this.queryForm.carrierId }, '', '', 'blob').then((res) => {
                const debug = res;
                if (debug) {
                    var elink = document.createElement('a');
                    elink.download = '预付款列表.xlsx';
                    elink.style.display = 'none';
                    var blob = new Blob([debug], { type: 'application/vnd.ms-excel' });
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    this.$message.error('导出异常请联系管理员');
                }
            });
        },
        /**
         * 打开交易明细 drawer
         */
        handleClickTransactionDetails(data) {
            const { id } = data;
            const params = { advanceId: id };
            this.$router.push({ name: 'CustomerPrepaymentBalanceDetailShipperEnd', query: params });
        },
        handleQuery() {
            this.queryForm.current = 1;
            this.getList();
        },
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.handleQuery();
        },
        /**
         * 打开预付款充值申请
         */
        async onClickApplyPrepayment() {
            // 当选择数据只有一个时，将选择的 advanceCode 赋值给预付款申请表单的 advanceAccount 字段
            if (this.selectData.length === 1) {
                this.applyPrepaymentForm.advanceAccount = this.selectData[0].advanceCode;
            }
            const Organization = this.$TOOL.data.get('Organization'); // 获取组织信息
            // 根据组织ID和承运商ID获取公司信息
            const res = await customerPrepaymentBalance.getCompanyInfo(Organization[0].id, this.selectData[0].carrierId);
            if (res.code === 200 && res?.data?.signCompany) {
                // 请求成功时，设置承运商信息
                this.applyPrepaymentForm.receiveCompany = res.data.signCompany;
            } else {
                this.$message.error('请您联系承运商完善合作关系后再操作！');
                return;
            }
            // 获取当前登录用户信息
            const userInfo = this.$TOOL.data.get('USER_INFO');
            // 如果能获取到用户信息，则设置用户信息到表单，否则显示错误信息
            if (userInfo) {
                this.userId = userInfo.id;
                this.applyPrepaymentForm.companyName = userInfo.name;
                this.applyPrepaymentForm.companyId = userInfo.id;
            } else {
                this.$message.error('无法获取当前登录用户信息，请重新登录或联系管理员');
            }
            this.applyPrepaymentForm.remitTime = moment().format('YYYY-MM-DD HH:mm:ss');
            this.applyPrepaymentVisible = true; // 显示预付款申请模态框
        },
        /**
         * 关闭预付款充值申请
         */
        onCloseApplyPrepayment() {
            this.applyPrepaymentVisible = false;
            this.$refs.applyPrepaymentForm.resetFields();
            this.$refs.uploadFile.clearFiles();
        },
        imgClose() {
            this.dialogVisible = false;
        },
        /**
         * 预览图片
         */
        handlePreview(file) {
            // 如果是已上传的文件，使用 response 中的 fileUrl
            if (file.response && file.response.data && file.response.data.fileUrl) {
                this.dialogImageUrl = [file.response.data.fileUrl];
            }
            // 如果是本地文件，使用 url
            else if (file.url) {
                this.dialogImageUrl = [file.url];
            }
            // 如果都没有，尝试使用 raw 创建临时 URL
            else if (file.raw) {
                this.dialogImageUrl = [URL.createObjectURL(file.raw)];
            }
            this.dialogVisible = true;
        },
        /**
         * 多选框选中数据
         * @param selection
         */
        handleSelectionChange(selection) {
            // 遍历 selection中的 id status 赋值给 this.selectData
            this.selectData = selection;
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /**
         * 上传成功
         */
        fileUploadSuccess(res) {
            this.applyPrepaymentForm.remitFile.push({
                fileUrl: res.data.fileUrl,
                fileName: res.data.fileName
            });
        },
        /**
         * 删除
         */
        fileUploadRemove(val) {
            this.applyPrepaymentForm.remitFile = this.applyPrepaymentForm.remitFile.filter((item) => item.fileUrl !== val.response.data.fileUrl);
        },
        /**
         * 提交预付款充值申请
         */
        onSubmitApplyPrepayment() {
            this.$refs.applyPrepaymentForm.validate(async (valid) => {
                if (valid) {
                    const applyWay = '1'; // 申请方式 1-货主发起 2-承运商发起
                    const businessType = '1'; //1-预存款充值 2-付款单支付 3-收款单收款
                    const carrierId = this.selectData[0].carrierId;
                    const payMethod = '1';
                    this.carrierId = carrierId;
                    // 将 this.applyPrepaymentForm.remitFile json字符串化
                    this.applyPrepaymentForm.remitFile = JSON.stringify(this.applyPrepaymentForm.remitFile);

                    const res = await paymentDoc.applyPayment({ applyWay, businessType, payMethod, carrierId, ...this.applyPrepaymentForm });
                    if (res.code === 200) {
                        this.$message.success('提交成功');
                        this.onCloseApplyPrepayment();
                        this.getList();

                        //发送消息给审批人
                        // 发布事件
                        //this.initWebSocket();
                    }
                }
            });
        },

        initWebSocket() {
            //初始化weosocket
            const wsuri = this.websocketUrl + this.carrierId;
            debugger;
            this.websocket = new WebSocket(wsuri);
            this.websocket.onopen = this.websocketonopen;
            this.websocket.onerror = this.websocketonerror;
            this.websocket.onmessage = this.websocketonmessage;
            this.websocket.onclose = this.websocketclose;
        },
        websocketonopen() {
            //连接建立之后执行send方法发送数据
            //  传递参数  不需要传参就不传
            var jsonStr = {
                money: this.applyPrepaymentForm.remitAmount,
                timer: this.applyPrepaymentForm.remitTime,
                company: this.applyPrepaymentForm.companyName,
                carrierId: this.carrierId,
                businessType: '1'
            };
            this.websocketsend(JSON.stringify(jsonStr));
        },
        websocketonerror() {
            //连接建立失败重连
            this.initWebSocket();
        },
        websocketonmessage(e) {
            //数据接收
            this.recevieData = e.data;
            this.openMessage();
        },
        websocketsend(Data) {
            //数据发送
            this.websocket.send(Data);
        },
        websocketclose(e) {
            //关闭
        },

        openMessage() {
            this.$notify({
                title: '成功',
                message: '您有新的待审批通知',
                type: 'success'
            });
        },
        /**
         * 标准时间格式化
         * @param cellValue
         * @returns {*}
         */
        formatDate(cellValue) {
            return moment(cellValue).format('YYYY-MM-DD HH:mm:ss');
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep {
    .el-drawer__header {
        margin-bottom: 20px;
    }
    .el-input.is-disabled .el-input__inner {
        color: #333 !important;
        -webkit-text-fill-color: #333 !important;
    }
}
.number__unit__element {
    position: relative;
    ::v-deep .el-input__inner {
        text-align: left;
    }
    &::after {
        content: '元';
        position: absolute;
        right: 40px;
        top: 47%;
        transform: translateY(-50%);
    }
}
.avatar__uploader__vertical__display {
    ::v-deep {
        .el__upload__tip {
            display: none;
        }

        .el-upload--picture-card {
            display: none;
        }
    }
}
.avatar__uploader__vertical {
    display: flex;

    ::v-deep {
        .el__upload__tip {
            color: #ff2a2a;
            margin-left: 11px;
            font-size: 12px;
            align-self: center;
        }
    }
}
</style>
